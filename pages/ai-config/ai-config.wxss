/* pages/ai-config/ai-config.wxss */
.ai-config-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  padding: 20rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  /* backdrop-filter: blur(10px); */
  /* 小程序不支持 */
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-inverse);
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 配置状态概览 */
.config-overview {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  /* backdrop-filter: blur(10px); */
  /* 小程序不支持 */
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-inverse);
  margin-bottom: 20rpx;
}

.provider-status {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
}

.status-info {
  flex: 1;
}

.provider-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-inverse);
  margin-bottom: 4rpx;
}

.provider-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.status-indicator {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-indicator.configured {
  background: rgba(76, 175, 80, 0.3);
  color: var(--success);
}

.status-indicator.not-configured {
  background: rgba(244, 67, 54, 0.3);
  color: var(--error);
}

/* 服务提供商配置 */
.providers-config {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-inverse);
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.provider-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  /* backdrop-filter: blur(10px); */
  /* 小程序不支持 */
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.provider-info {
  flex: 1;
}

.provider-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-inverse);
  margin-bottom: 8rpx;
}

.provider-subtitle {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.provider-actions {
  margin-left: 20rpx;
}

.config-btn {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

.provider-details {
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 12rpx;
}

.detail-label {
  width: 140rpx;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  flex: 1;
  font-size: 26rpx;
  color: var(--text-inverse);
}

.masked-key {
  font-family: monospace;
  background: rgba(0, 0, 0, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

/* 推荐配置 */
.recommendation {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  /* backdrop-filter: blur(10px); */
  /* 小程序不支持 */
}

.recommendation-header {
  margin-bottom: 20rpx;
}

.recommendation-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-inverse);
}

.recommendation-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}

.recommendation-steps {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.step-item {
  display: flex;
  align-items: center;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  color: var(--text-inverse);
  margin-right: 16rpx;
}

.step-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 底部操作 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--radius-xl);
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.danger {
  background: rgba(244, 67, 54, 0.3);
  color: var(--error);
  border: 2rpx solid rgba(244, 67, 54, 0.5);
}

/* 配置弹窗 */
.config-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  font-size: 40rpx;
  color: #666666;
}

.modal-body {
  flex: 1;
  padding: 30rpx 40rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 100rpx 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
}

.toggle-btn {
  position: absolute;
  right: 10rpx;
  top: 50rpx;
  width: 80rpx;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-tips {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.tips-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn.primary {
  background: #667eea;
  color: var(--text-inverse);
}

.modal-btn.secondary {
  background: #f5f5f5;
  color: #666666;
}

.modal-btn[disabled] {
  opacity: 0.6;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.loading-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #333333;
}