-- 创建AI盘点记录表
CREATE TABLE IF NOT EXISTS ai_inventory_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  batch_number VARCHAR(50) NOT NULL COMMENT '批次编号',
  original_count INT DEFAULT 0 COMMENT 'AI识别的原始数量',
  final_count INT NOT NULL COMMENT '最终确认的数量',
  confidence DECIMAL(5,2) DEFAULT 0.00 COMMENT '识别置信度(0-100)',
  ai_model VARCHAR(50) DEFAULT 'GPT-4V' COMMENT 'AI模型名称',
  image_path VARCHAR(500) DEFAULT '' COMMENT '图片路径',
  notes TEXT COMMENT '备注信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_batch_number (batch_number),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI盘点记录表';

-- 插入一些示例数据
INSERT INTO ai_inventory_records (
  user_id, batch_number, original_count, final_count, 
  confidence, ai_model, notes, created_at
) VALUES 
(1, 'AI202501010900', 45, 47, 92.5, 'GPT-4V', '早晨盘点，鹅群活跃', '2025-01-01 09:00:00'),
(1, 'AI202501011400', 52, 52, 95.8, 'GPT-4V', '下午盘点，光线充足', '2025-01-01 14:00:00'),
(1, 'AI202501021000', 48, 49, 88.3, 'Claude Vision', '上午盘点，部分遮挡', '2025-01-02 10:00:00'),
(1, 'AI202501021600', 51, 50, 94.2, 'GPT-4V', '傍晚盘点，鹅群聚集', '2025-01-02 16:00:00'),
(1, 'AI202501030800', 46, 46, 96.7, 'YOLO v8', '清晨盘点，识别精准', '2025-01-03 08:00:00');
