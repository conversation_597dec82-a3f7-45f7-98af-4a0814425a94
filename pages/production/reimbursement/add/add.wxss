/* pages/production/reimbursement/add/add.wxss */
.add-reimbursement-container {
  padding: 20rpx;
  background-color: #F5F6F8;
  min-height: 100vh;
}

.form-item {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #E5E5E5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #F8F9FA;
}

.form-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #E5E5E5;
  border-radius: 8rpx;
  background: #F8F9FA;
  font-size: 28rpx;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #E5E5E5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #F8F9FA;
}

.upload-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4d4f;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #D9D9D9;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #FAFAFA;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.submit-section {
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border-radius: var(--radius-xl);
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.submit-btn[disabled] {
  background: #D9D9D9;
  color: #999;
}