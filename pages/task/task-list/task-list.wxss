/* pages/task/task-list/task-list.wxss */
.task-list-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 筛选栏 */
.filter-section {
  background-color: #ffffff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  width: 100%;
}

.filter-tabs {
  display: flex;
  gap: 16rpx;
  white-space: nowrap;
}

.filter-tab {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666666;
  background-color: #f8f9fa;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.filter-tab.active {
  background-color: #0066cc;
  color: #ffffff;
}

/* 搜索栏 */
.search-section {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  margin-left: 12rpx;
}

.clear-btn image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666666;
}

.sort-btn image {
  width: 24rpx;
  height: 24rpx;
}

/* 任务列表 */
.task-list {
  padding: 20rpx;
}

.task-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 6rpx solid #e0e0e0;
  transition: all 0.2s ease;
}

.task-item.high {
  border-left-color: #ff4d4f;
}

.task-item.medium {
  border-left-color: #faad14;
}

.task-item.low {
  border-left-color: #52c41a;
}

.task-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.task-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.task-status.pending {
  background-color: #fff7e6;
  color: #faad14;
}

.task-status.processing {
  background-color: #e6f7ff;
  color: #1890ff;
}

.task-status.completed {
  background-color: #e8f5e8;
  color: #52c41a;
}

.task-status.overdue {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.task-priority {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.task-priority.high {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.task-priority.medium {
  background-color: #fff7e6;
  color: #faad14;
}

.task-priority.low {
  background-color: #e8f5e8;
  color: #52c41a;
}

.task-content {
  margin-bottom: 16rpx;
}

.task-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.task-desc {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.meta-icon {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 22rpx;
  color: #999999;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.action-btn.primary {
  background-color: #0066cc;
  color: #ffffff;
}

.action-btn.success {
  background-color: #52c41a;
  color: #ffffff;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666666;
}

.action-btn.warning {
  background-color: #faad14;
  color: #ffffff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

.add-task-btn {
  background-color: #0066cc;
  color: #ffffff;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border: none;
}

.add-task-btn::after {
  border: none;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40rpx;
  font-size: 24rpx;
  color: #666666;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #0066cc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.3);
  z-index: 100;
}

.fab image {
  width: 48rpx;
  height: 48rpx;
}

/* 排序弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.sort-modal {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  width: 100%;
  max-height: 60vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.close-btn image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.sort-options {
  display: flex;
  flex-direction: column;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333333;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active {
  color: #0066cc;
}

.sort-option image {
  width: 32rpx;
  height: 32rpx;
}
