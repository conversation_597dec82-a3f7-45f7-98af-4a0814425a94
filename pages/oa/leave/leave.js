// pages/oa/leave/leave.js

/**
 * 请假申请模块主页面
 * 功能：请假申请管理、查看请假记录
 */

Page({
  data: {
    // 页面状态
    loading: false,
    
    // 快捷操作
    quickActions: [
      {
        id: 'apply-leave',
        title: '申请请假',
        description: '提交新的请假申请',
        iconText: '➕',
        url: '/pages/oa/leave/apply/apply',
        color: '#0066CC'
      },
      {
        id: 'my-leaves',
        title: '我的请假',
        description: '查看我的请假记录',
        iconText: '📝',
        url: '/pages/oa/leave/list/list?type=my',
        color: '#00A86B'
      }
    ],
    
    // 统计数据
    statistics: {
      pending: 2,      // 待审批
      approved: 8,     // 已批准
      rejected: 1,     // 已拒绝
      total: 11        // 总数
    },
    
    // 最近请假记录
    recentLeaves: [
      {
        id: 'leave_001',
        type: '事假',
        reason: '家庭事务处理',
        startDate: '2024-01-15',
        endDate: '2024-01-16',
        days: 2,
        status: 'pending',
        statusText: '待审批',
        statusColor: '#FF9500',
        submitTime: '2024-01-10 09:30'
      },
      {
        id: 'leave_002',
        type: '病假',
        reason: '感冒发烧需要休息',
        startDate: '2024-01-08',
        endDate: '2024-01-09',
        days: 2,
        status: 'approved',
        statusText: '已批准',
        statusColor: '#00A86B',
        submitTime: '2024-01-05 14:20'
      },
      {
        id: 'leave_003',
        type: '年假',
        reason: '年度休假',
        startDate: '2024-12-25',
        endDate: '2024-12-31',
        days: 7,
        status: 'approved',
        statusText: '已批准',
        statusColor: '#00A86B',
        submitTime: '2023-12-20 16:45'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refreshData();
  },

  /**
   * 加载数据
   */
  loadData: function() {
    this.setData({ loading: true });
    
    // 模拟数据加载
    setTimeout(() => {
      this.setData({ 
        loading: false
      });
    }, 500);
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    this.loadData();
  },

  /**
   * 快捷操作点击
   */
  onQuickActionTap: function(e) {
    const { url } = e.currentTarget.dataset;
    
    if (url) {
      wx.navigateTo({
        url: url,
        fail: () => {
          wx.showToast({
            title: '页面暂未开放',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 统计卡片点击
   */
  onStatTap: function(e) {
    const { type } = e.currentTarget.dataset;
    let url = '/pages/oa/leave/list/list';
    
    switch(type) {
      case 'pending':
        url += '?status=pending';
        break;
      case 'approved':
        url += '?status=approved';
        break;
      case 'rejected':
        url += '?status=rejected';
        break;
      default:
        url += '?status=all';
        break;
    }
    
    wx.navigateTo({
      url: url,
      fail: () => {
        wx.showToast({
          title: '页面暂未开放',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 请假记录点击
   */
  onLeaveItemTap: function(e) {
    const { id } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/oa/leave/detail/detail?id=${id}`,
      fail: () => {
        wx.showToast({
          title: '页面暂未开放',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看全部请假记录
   */
  onViewAllLeaves: function() {
    wx.navigateTo({
      url: '/pages/oa/leave/list/list',
      fail: () => {
        wx.showToast({
          title: '页面暂未开放',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 分享
   */
  onShareAppMessage: function () {
    return {
      title: '请假申请管理',
      path: '/pages/oa/leave/leave'
    };
  }
});