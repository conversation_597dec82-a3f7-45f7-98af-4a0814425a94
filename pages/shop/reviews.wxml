<!-- pages/shop/reviews.wxml -->
<view class="reviews-container">
  <!-- 总体评价 -->
  <view class="rating-overview">
    <view class="rating-score-large">{{goods.rating || 4.8}}</view>
    <view class="rating-details">
      <view class="rating-stars-large">
        <text class="star {{index < Math.floor(goods.rating || 4.8) ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
      </view>
      <view class="rating-count">基于 {{goods.reviewCount || 156}} 条评价</view>
    </view>
  </view>

  <!-- 评分分布 -->
  <view class="rating-distribution">
    <view class="distribution-item" wx:for="{{ratingStats}}" wx:key="star">
      <view class="star-label">{{item.star}}星</view>
      <view class="distribution-bar">
        <view class="bar-fill" style="width: {{item.percentage}}%"></view>
      </view>
      <view class="star-count">{{item.count}}</view>
    </view>
  </view>

  <!-- 筛选和排序 -->
  <view class="filter-section">
    <view class="filter-tabs">
      <view 
        class="filter-tab {{currentFilter === item.key ? 'active' : ''}}"
        wx:for="{{filterOptions}}"
        wx:key="key"
        bindtap="onFilterChange"
        data-filter="{{item.key}}"
      >
        {{item.label}}
      </view>
    </view>
  </view>

  <!-- 评价列表 -->
  <view class="reviews-list">
    <view class="review-item" wx:for="{{reviews}}" wx:key="id" wx:for-item="review">
      <view class="review-header">
        <image class="reviewer-avatar" src="{{review.avatar || '/images/default_avatar.png'}}"></image>
        <view class="reviewer-info">
          <view class="reviewer-name">{{review.username}}</view>
          <view class="review-date">{{review.date}}</view>
        </view>
        <view class="review-rating">
          <text class="star {{index < review.rating ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
      </view>
      
      <view class="review-content">{{review.content}}</view>
      
      <view class="review-images" wx:if="{{review.images && review.images.length > 0}}">
        <image 
          class="review-image" 
          wx:for="{{review.images}}" 
          wx:for-item="imgUrl"
          wx:key="*this"
          src="{{imgUrl}}" 
          mode="aspectFill"
          bindtap="onPreviewImage"
          data-url="{{imgUrl}}"
          data-urls="{{review.images}}"
        ></image>
      </view>

      <!-- SKU信息 -->
      <view class="review-sku" wx:if="{{review.sku}}">
        <text class="sku-info">规格：{{review.sku}}</text>
      </view>

      <!-- 有用评价 -->
      <view class="review-actions">
        <view class="helpful-action" bindtap="onHelpfulTap" data-id="{{review.id}}">
          <text class="helpful-icon">👍</text>
          <text class="helpful-text">有用 ({{review.helpful || 0}})</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}" bindtap="onLoadMore">
    <text class="load-more-text">{{loadingMore ? '加载中...' : '加载更多'}}</text>
  </view>

  <!-- 无更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && reviews.length > 0}}">
    <text class="no-more-text">没有更多评价了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-reviews" wx:if="{{reviews.length === 0 && !loading}}">
    <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无评价</text>
  </view>
</view>