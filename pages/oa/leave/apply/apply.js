// pages/oa/leave/apply/apply.js

/**
 * 请假申请页面
 * 功能：填写和提交请假申请
 */

Page({
  data: {
    // 表单数据
    formData: {
      type: '',         // 请假类型
      reason: '',       // 请假原因
      startDate: '',    // 开始日期
      endDate: '',      // 结束日期
      startTime: '09:00', // 开始时间
      endTime: '18:00',   // 结束时间
      days: 0,          // 请假天数
      attachment: []    // 附件
    },
    
    // 请假类型选项
    leaveTypes: [
      { value: 'sick', label: '病假', color: '#FF9500' },
      { value: 'personal', label: '事假', color: '#0066CC' },
      { value: 'annual', label: '年假', color: '#00A86B' },
      { value: 'marriage', label: '婚假', color: '#FF69B4' },
      { value: 'maternity', label: '产假', color: '#9370DB' },
      { value: 'paternity', label: '陪产假', color: '#4169E1' },
      { value: 'compassionate', label: '丧假', color: '#696969' },
      { value: 'other', label: '其他', color: '#808080' }
    ],
    

    
    // 页面状态
    loading: false,
    submitting: false,
    
    // 计算属性
    selectedTypeLabel: '',      // 当前选中的请假类型标签
    
    // 用户信息
    userInfo: {
      name: '张三',
      department: '技术部',
      position: '高级工程师',
      phone: '138****8888'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initFormData();
  },

  /**
   * 初始化表单数据
   */
  initFormData: function() {
    const userInfo = getApp().globalData.userInfo || this.data.userInfo;
    
    this.setData({
      userInfo: userInfo
    });
  },

  /**
   * 请假类型选择
   */
  onLeaveTypeChange: function(e) {
    const typeIndex = e.detail.value;
    const selectedType = this.data.leaveTypes[typeIndex];
    
    this.setData({
      'formData.type': selectedType.value,
      'selectedTypeLabel': selectedType.label
    });
    
    this.calculateDays();
  },

  /**
   * 输入框变化
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 日期选择
   */
  onDateChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    this.calculateDays();
  },

  /**
   * 时间选择
   */
  onTimeChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    this.calculateDays();
  },



  /**
   * 计算请假天数
   */
  calculateDays: function() {
    const { startDate, endDate, startTime, endTime } = this.data.formData;
    
    if (!startDate || !endDate) {
      return;
    }
    
    const start = new Date(startDate + ' ' + startTime);
    const end = new Date(endDate + ' ' + endTime);
    
    if (end <= start) {
      this.setData({
        'formData.days': 0
      });
      return;
    }
    
    // 计算天数（简化计算，按工作日计算）
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    this.setData({
      'formData.days': diffDays
    });
  },

  /**
   * 选择附件
   */
  onChooseAttachment: function() {
    wx.chooseImage({
      count: 3,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        const currentAttachments = this.data.formData.attachment || [];
        
        this.setData({
          'formData.attachment': [...currentAttachments, ...tempFilePaths]
        });
      },
      fail: (error) => {
        wx.showToast({
          title: '选择失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 删除附件
   */
  onDeleteAttachment: function(e) {
    const { index } = e.currentTarget.dataset;
    const attachments = this.data.formData.attachment.filter((_, i) => i !== index);
    
    this.setData({
      'formData.attachment': attachments
    });
  },

  /**
   * 预览附件
   */
  onPreviewAttachment: function(e) {
    const { url } = e.currentTarget.dataset;
    const urls = this.data.formData.attachment;
    
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  /**
   * 表单验证
   */
  validateForm: function() {
    const { type, reason, startDate, endDate, days } = this.data.formData;
    
    if (!type) {
      wx.showToast({
        title: '请选择请假类型',
        icon: 'none'
      });
      return false;
    }
    
    if (!reason.trim()) {
      wx.showToast({
        title: '请填写请假原因',
        icon: 'none'
      });
      return false;
    }
    
    if (!startDate || !endDate) {
      wx.showToast({
        title: '请选择请假日期',
        icon: 'none'
      });
      return false;
    }
    
    if (days <= 0) {
      wx.showToast({
        title: '请假时间无效',
        icon: 'none'
      });
      return false;
    }
    

    
    return true;
  },

  /**
   * 提交申请
   */
  onSubmit: function() {
    if (!this.validateForm()) {
      return;
    }
    
    wx.showModal({
      title: '确认提交',
      content: '确定要提交这份请假申请吗？',
      success: (res) => {
        if (res.confirm) {
          this.submitApplication();
        }
      }
    });
  },

  /**
   * 提交申请到服务器
   */
  submitApplication: function() {
    this.setData({ submitting: true });
    
    const submitData = {
      ...this.data.formData,
      applicant: this.data.userInfo.name,
      department: this.data.userInfo.department,
      position: this.data.userInfo.position,
      submitTime: new Date().toISOString(),
      status: 'pending'
    };
    
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('提交请假申请', submitData); } catch(_) {}
    
    // 模拟API调用
    setTimeout(() => {
      this.setData({ submitting: false });
      
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        // 返回上一页并刷新
        wx.navigateBack({
          delta: 1
        });
      }, 1500);
      
    }, 2000);
  },

  /**
   * 重置表单
   */
  onReset: function() {
    wx.showModal({
      title: '确认重置',
      content: '确定要清空所有填写内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              type: '',
              reason: '',
              startDate: '',
              endDate: '',
              startTime: '09:00',
              endTime: '18:00',
              days: 0,
              attachment: []
            }
          });
        }
      }
    });
  }
});