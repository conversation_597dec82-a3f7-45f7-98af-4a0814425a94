// pages/health/material-detail/material-detail.js
Page({
  data: {
    loading: false,
    saving: false,
    material: {
      recordDate: '',
      category: 'feed', // 默认选择饲料
      name: '',
      spec: '',
      stock: '',
      supplier: '',
      notes: '',
      // 饲料特有字段
      feedTypeIndex: undefined,
      unitPrice: '',
      // 药品特有字段
      medicineTypeIndex: undefined,
      manufacturer: '',
      expiryDate: '',
      batchNumber: '',
      // 其他物料特有字段
      otherTypeIndex: undefined
    },

    // 饲料类型选项
    feedTypeOptions: [
      { label: '雏鹅全价饲料', value: 'chick_feed' },
      { label: '生长期饲料', value: 'grow_feed' },
      { label: '产蛋期饲料', value: 'laying_feed' },
      { label: '种鹅饲料', value: 'breeding_feed' },
      { label: '育肥饲料', value: 'fattening_feed' },
      { label: '其他饲料', value: 'other_feed' }
    ],

    // 药品类型选项
    medicineTypeOptions: [
      { label: '抗生素类', value: 'antibiotic' },
      { label: '疫苗类', value: 'vaccine' },
      { label: '消毒剂', value: 'disinfectant' },
      { label: '维生素类', value: 'vitamin' },
      { label: '驱虫药', value: 'deworming' },
      { label: '中草药', value: 'herbal' },
      { label: '其他药品', value: 'other_medicine' }
    ],

    // 其他物料类型选项
    otherTypeOptions: [
      { label: '清洁用品', value: 'cleaning' },
      { label: '养殖设备', value: 'equipment' },
      { label: '包装材料', value: 'packaging' },
      { label: '工具用品', value: 'tools' },
      { label: '办公用品', value: 'office' },
      { label: '其他物料', value: 'other' }
    ]
  },

  onLoad: function(options) {
    // 设置当前日期作为默认值
    const now = new Date();
    const today = now.toISOString().substr(0, 10);

    this.setData({
      'material.recordDate': today
    });

    // 如果是编辑模式，加载现有数据
    if (options.id) {
      this.loadMaterialData(options.id);
    }
  },

  // 日期变化
  onDateChange: function(e) {
    this.setData({
      'material.recordDate': e.detail.value
    });
  },



  // 日期字段变化（用于有效期等）
  onDateFieldChange: function(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      [`material.${field}`]: e.detail.value
    });
  },

  // 物料类型切换
  onCategoryChange: function(e) {
    const category = e.currentTarget.dataset.category;
    
    // 清空所有特定字段的数据
    const updatedMaterial = {
      ...this.data.material,
      category: category,
      // 清空类型特有字段
      feedTypeIndex: undefined,
      unitPrice: '',
      medicineTypeIndex: undefined,
      manufacturer: '',
      expiryDate: '',
      batchNumber: '',
      otherTypeIndex: undefined
    };

    this.setData({
      material: updatedMaterial
    });
  },

  // 输入框变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`material.${field}`]: value
    });
  },

  // 选择器变化
  onPickerChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = parseInt(e.detail.value);
    
    this.setData({
      [`material.${field}`]: value
    });
  },

  // 加载物料数据（编辑模式）
  loadMaterialData: function(id) {
    this.setData({ loading: true });
    
    // 模拟加载数据
    setTimeout(() => {
      // 这里应该从服务器或本地存储获取数据
      this.setData({
        loading: false
      });
    }, 500);
  },

  // 取消操作
  onCancel: function() {
    wx.navigateBack();
  },

  // 保存物料记录
  onSave: function() {
    const material = this.data.material;
    
    // 表单验证
    if (!this.validateForm(material)) {
      return;
    }

    this.setData({ saving: true });

    // 模拟保存操作
    setTimeout(() => {
      this.saveMaterialRecord(material);
    }, 1000);
  },

  // 表单验证
  validateForm: function(material) {
    // 基础必填字段验证
    if (!material.recordDate) {
      wx.showToast({ title: '请选择记录日期', icon: 'none' });
      return false;
    }
    


    if (!material.name || material.name.trim() === '') {
      wx.showToast({ title: '请输入物料名称', icon: 'none' });
      return false;
    }

    if (!material.spec || material.spec.trim() === '') {
      wx.showToast({ title: '请输入规格信息', icon: 'none' });
      return false;
    }

    if (!material.stock || material.stock.trim() === '') {
      wx.showToast({ title: '请输入库存数量', icon: 'none' });
      return false;
    }

    // 验证库存数量是否为有效数字
    const stockNum = parseInt(material.stock);
    if (isNaN(stockNum) || stockNum < 0) {
      wx.showToast({ title: '库存数量必须为有效数字', icon: 'none' });
      return false;
    }

    return true;
  },

  // 获取物料状态
  getMaterialStatus: function(stock) {
    const stockNum = parseInt(stock);
    if (stockNum <= 0) {
      return 'danger';
    } else if (stockNum < 10) {
      return 'warning';
    } else {
      return 'normal';
    }
  },

  // 保存物料记录
  saveMaterialRecord: function(material) {
    try {
      // 生成新的物料记录
      const newMaterial = {
        id: Date.now(),
        name: material.name.trim(),
        category: material.category,
        spec: material.spec.trim(),
        stock: parseInt(material.stock) || 0,
        supplier: material.supplier ? material.supplier.trim() : '',
        notes: material.notes ? material.notes.trim() : '',
        recordDate: material.recordDate,
        status: this.getMaterialStatus(material.stock),
        createTime: new Date().toISOString()
      };

      // 根据类别添加特有字段
      if (material.category === 'feed') {
        const feedTypeOptions = this.data.feedTypeOptions;
        newMaterial.feedType = material.feedTypeIndex !== undefined && feedTypeOptions[material.feedTypeIndex] 
          ? feedTypeOptions[material.feedTypeIndex].value 
          : '';
        newMaterial.unitPrice = material.unitPrice ? parseFloat(material.unitPrice) : 0;
      } else if (material.category === 'medicine') {
        const medicineTypeOptions = this.data.medicineTypeOptions;
        newMaterial.medicineType = material.medicineTypeIndex !== undefined && medicineTypeOptions[material.medicineTypeIndex] 
          ? medicineTypeOptions[material.medicineTypeIndex].value 
          : '';
        newMaterial.manufacturer = material.manufacturer ? material.manufacturer.trim() : '';
        newMaterial.expiryDate = material.expiryDate || '';
        newMaterial.batchNumber = material.batchNumber ? material.batchNumber.trim() : '';
      } else if (material.category === 'other') {
        const otherTypeOptions = this.data.otherTypeOptions;
        newMaterial.otherType = material.otherTypeIndex !== undefined && otherTypeOptions[material.otherTypeIndex] 
          ? otherTypeOptions[material.otherTypeIndex].value 
          : '';
      }

      // 保存到本地存储
      const existingMaterials = wx.getStorageSync('materialList') || [];
      existingMaterials.push(newMaterial);
      wx.setStorageSync('materialList', existingMaterials);

      this.setData({ saving: false });

      wx.showToast({
        title: '物料记录保存成功',
        icon: 'success',
        duration: 2000
      });

      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);

      try { 
        const logger = require('../../../utils/logger.js'); 
        logger.info && logger.info('[MaterialDetail] Material record saved successfully', newMaterial); 
      } catch(_) {}

    } catch (error) {
      this.setData({ saving: false });

      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });

      try { 
        const logger = require('../../../utils/logger.js'); 
        logger.error && logger.error('[MaterialDetail] Failed to save material record', error); 
      } catch(_) {}
    }
  }
});
