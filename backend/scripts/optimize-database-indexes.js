#!/usr/bin/env node
// backend/scripts/optimize-database-indexes.js
// 数据库索引优化脚本

const sequelize = require("../config/database");

class DatabaseIndexOptimizer {
  constructor() {
    this.queryInterface = sequelize.getQueryInterface();
  }

  /**
   * 执行数据库索引优化
   */
  async optimize() {
    console.log("🚀 开始数据库索引优化...");

    try {
      // 1. 分析现有索引
      await this.analyzeExistingIndexes();

      // 2. 添加复合索引
      await this.addCompositeIndexes();

      // 3. 添加单列索引
      await this.addSingleColumnIndexes();

      // 4. 优化外键索引
      await this.optimizeForeignKeyIndexes();

      // 5. 验证索引效果
      await this.validateIndexes();

      console.log("✅ 数据库索引优化完成！");
    } catch (error) {
      console.error("❌ 索引优化失败:", error);
      throw error;
    }
  }

  /**
   * 分析现有索引
   */
  async analyzeExistingIndexes() {
    console.log("📊 分析现有索引...");

    const tables = [
      "users",
      "health_records",
      "production_records",
      "inventory_records",
      "announcements",
    ];

    for (const table of tables) {
      try {
        const indexes = await this.queryInterface.showIndex(table);
        console.log(
          `表 ${table} 现有索引:`,
          indexes.map((idx) => ({
            name: idx.name,
            fields: idx.fields.map((f) => f.attribute),
            unique: idx.unique,
          })),
        );
      } catch (error) {
        console.log(`表 ${table} 不存在或无法访问`);
      }
    }
  }

  /**
   * 添加复合索引
   */
  async addCompositeIndexes() {
    console.log("🔗 添加复合索引...");

    const compositeIndexes = [
      // 健康记录表复合索引
      {
        table: "health_records",
        name: "idx_health_user_status",
        fields: ["user_id", "status"],
        description: "用户ID和状态复合索引，优化按用户和状态查询",
      },
      {
        table: "health_records",
        name: "idx_health_user_date",
        fields: ["user_id", "created_at"],
        description: "用户ID和创建时间复合索引，优化按用户和时间查询",
      },
      {
        table: "health_records",
        name: "idx_health_goose_status",
        fields: ["goose_id", "status"],
        description: "鹅群ID和状态复合索引，优化按鹅群和状态查询",
      },

      // 生产记录表复合索引
      {
        table: "production_records",
        name: "idx_production_user_date",
        fields: ["user_id", "recorded_date"],
        description: "用户ID和记录日期复合索引，优化按用户和日期查询",
      },
      {
        table: "production_records",
        name: "idx_production_user_created",
        fields: ["user_id", "created_at"],
        description: "用户ID和创建时间复合索引，优化按用户和创建时间查询",
      },
      {
        table: "production_records",
        name: "idx_production_date_type",
        fields: ["recorded_date", "type"],
        description: "记录日期和类型复合索引，优化按日期和类型查询",
      },

      // 库存记录表复合索引
      {
        table: "inventory_records",
        name: "idx_inventory_user_category",
        fields: ["user_id", "age_category"],
        description: "用户ID和年龄分类复合索引",
      },
      {
        table: "inventory_records",
        name: "idx_inventory_location_health",
        fields: ["area_location", "health_status"],
        description: "区域位置和健康状态复合索引",
      },

      // 公告表复合索引
      {
        table: "announcements",
        name: "idx_announcement_type_priority",
        fields: ["type", "priority"],
        description: "类型和优先级复合索引",
      },
      {
        table: "announcements",
        name: "idx_announcement_status_date",
        fields: ["status", "publish_time"],
        description: "状态和发布时间复合索引",
      },

      // 用户表复合索引
      {
        table: "users",
        name: "idx_users_role_created",
        fields: ["role", "createdAt"],
        description: "角色和创建时间复合索引",
      },
    ];

    for (const index of compositeIndexes) {
      try {
        await this.queryInterface.addIndex(index.table, {
          fields: index.fields,
          name: index.name,
          type: "BTREE",
        });
        console.log(`✓ 添加复合索引: ${index.name} (${index.description})`);
      } catch (error) {
        if (error.message.includes("Duplicate key name")) {
          console.log(`⚠️ 索引 ${index.name} 已存在`);
        } else {
          console.error(`❌ 添加索引 ${index.name} 失败:`, error.message);
        }
      }
    }
  }

  /**
   * 添加单列索引
   */
  async addSingleColumnIndexes() {
    console.log("📍 添加单列索引...");

    const singleIndexes = [
      // 健康记录表单列索引
      {
        table: "health_records",
        field: "goose_id",
        name: "idx_health_goose_id",
        description: "鹅群ID索引",
      },
      {
        table: "health_records",
        field: "status",
        name: "idx_health_status",
        description: "健康状态索引",
      },

      // 生产记录表单列索引
      {
        table: "production_records",
        field: "recorded_date",
        name: "idx_production_recorded_date",
        description: "记录日期索引",
      },
      {
        table: "production_records",
        field: "type",
        name: "idx_production_type",
        description: "记录类型索引",
      },

      // 库存记录表单列索引
      {
        table: "inventory_records",
        field: "breed_name",
        name: "idx_inventory_breed_name",
        description: "品种名称索引",
      },
      {
        table: "inventory_records",
        field: "health_status",
        name: "idx_inventory_health_status",
        description: "健康状态索引",
      },

      // 公告表单列索引
      {
        table: "announcements",
        field: "type",
        name: "idx_announcement_type",
        description: "公告类型索引",
      },
      {
        table: "announcements",
        field: "priority",
        name: "idx_announcement_priority",
        description: "优先级索引",
      },
      {
        table: "announcements",
        field: "publish_time",
        name: "idx_announcement_publish_time",
        description: "发布时间索引",
      },

      // 用户表单列索引
      {
        table: "users",
        field: "role",
        name: "idx_users_role",
        description: "用户角色索引",
      },
      {
        table: "users",
        field: "email",
        name: "idx_users_email",
        description: "邮箱索引",
      },
    ];

    for (const index of singleIndexes) {
      try {
        await this.queryInterface.addIndex(index.table, {
          fields: [index.field],
          name: index.name,
          type: "BTREE",
        });
        console.log(`✓ 添加单列索引: ${index.name} (${index.description})`);
      } catch (error) {
        if (error.message.includes("Duplicate key name")) {
          console.log(`⚠️ 索引 ${index.name} 已存在`);
        } else {
          console.error(`❌ 添加索引 ${index.name} 失败:`, error.message);
        }
      }
    }
  }

  /**
   * 优化外键索引
   */
  async optimizeForeignKeyIndexes() {
    console.log("🔗 优化外键索引...");

    const foreignKeyIndexes = [
      {
        table: "health_records",
        field: "user_id",
        name: "idx_health_user_id_fk",
        description: "健康记录用户外键索引",
      },
      {
        table: "production_records",
        field: "user_id",
        name: "idx_production_user_id_fk",
        description: "生产记录用户外键索引",
      },
      {
        table: "inventory_records",
        field: "user_id",
        name: "idx_inventory_user_id_fk",
        description: "库存记录用户外键索引",
      },
    ];

    for (const index of foreignKeyIndexes) {
      try {
        await this.queryInterface.addIndex(index.table, {
          fields: [index.field],
          name: index.name,
          type: "BTREE",
        });
        console.log(`✓ 添加外键索引: ${index.name} (${index.description})`);
      } catch (error) {
        if (error.message.includes("Duplicate key name")) {
          console.log(`⚠️ 外键索引 ${index.name} 已存在`);
        } else {
          console.error(`❌ 添加外键索引 ${index.name} 失败:`, error.message);
        }
      }
    }
  }

  /**
   * 验证索引效果
   */
  async validateIndexes() {
    console.log("🔍 验证索引效果...");

    const testQueries = [
      {
        description: "按用户ID和状态查询健康记录",
        query: `EXPLAIN SELECT * FROM health_records WHERE user_id = 1 AND status = 'healthy' ORDER BY created_at DESC LIMIT 10`,
      },
      {
        description: "按用户ID和日期查询生产记录",
        query: `EXPLAIN SELECT * FROM production_records WHERE user_id = 1 AND recorded_date >= '2024-01-01' ORDER BY recorded_date DESC`,
      },
      {
        description: "按鹅群ID查询健康记录",
        query: `EXPLAIN SELECT * FROM health_records WHERE goose_id = 'GOOSE001' ORDER BY created_at DESC`,
      },
    ];

    for (const test of testQueries) {
      try {
        const [results] = await sequelize.query(test.query);
        console.log(`\n📋 ${test.description}:`);
        console.log("执行计划:", results);
      } catch (error) {
        console.log(`⚠️ 查询测试失败: ${test.description}`);
      }
    }
  }

  /**
   * 生成索引使用报告
   */
  async generateIndexReport() {
    console.log("📊 生成索引使用报告...");

    try {
      // 查询索引使用统计
      const [indexStats] = await sequelize.query(`
        SELECT 
          TABLE_NAME,
          INDEX_NAME,
          CARDINALITY,
          INDEX_TYPE,
          COMMENT
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = DATABASE()
        ORDER BY TABLE_NAME, INDEX_NAME
      `);

      console.log("\n📈 索引统计报告:");
      console.table(indexStats);

      return indexStats;
    } catch (error) {
      console.error("生成索引报告失败:", error);
      return [];
    }
  }

  /**
   * 清理无用索引
   */
  async cleanupUnusedIndexes() {
    console.log("🧹 清理无用索引...");

    // 这里可以添加清理无用索引的逻辑
    // 需要谨慎操作，建议先分析索引使用情况
    console.log("索引清理功能需要手动分析后执行");
  }
}

// 执行索引优化
async function main() {
  const optimizer = new DatabaseIndexOptimizer();

  try {
    await sequelize.authenticate();
    console.log("数据库连接成功");

    await optimizer.optimize();
    await optimizer.generateIndexReport();
  } catch (error) {
    console.error("索引优化失败:", error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = DatabaseIndexOptimizer;
