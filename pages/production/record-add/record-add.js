// pages/production/record-add/record-add.js
const { API, UI, BUSINESS } = require('../../../constants/index.js');
const request = require('../../../utils/request.js');

Page({
  data: {
    // 标签页管理
    activeTab: 0,
    tabs: [
      { id: 0, name: '入栏记录', type: 'entry' },
      { id: 1, name: '称重记录', type: 'weight' },
      { id: 2, name: '出栏记录', type: 'sale' }
    ],
    recordType: 'entry', // entry, weight, sale
    
    // 通用字段
    batch: '',
    date: '',
    count: '',
    weight: '',
    notes: '',
    
    // 入栏记录特有字段
    source: '',
    breed: '',
    age: '',
    cost: '',
    supplier: '',
    
    // 出栏记录特有字段
    price: '',
    buyer: '',
    totalIncome: '',
    
    // 选项数据
    breedOptions: [
      { label: '太湖鹅', value: 'taihu' },
      { label: '四川白鹅', value: 'sichuan' },
      { label: '皖西白鹅', value: 'wanxi' },
      { label: '扬州鹅', value: 'yangzhou' },
      { label: '其他品种', value: 'other' }
    ],
    batchOptions: [], // 批次选择选项
    activeBatches: [], // 活跃批次数据
    
    // 批次相关信息
    batchLabel: '',
    selectedBatchInfo: null,
    maxCount: 0, // 批次可操作的最大数量
    entryDate: '', // 入栏日期
    
    loading: false
  },

  onLoad: function (options) {
    // 获取记录类型，默认为入栏记录
    const recordType = options.type || 'entry';
    
    // 生成默认日期
    const now = new Date();
    const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    
    this.setData({
      recordType: recordType,
      date: today
    });
    
    // 根据记录类型初始化数据
    this.initializeByRecordType(recordType);
  },
  
  // 根据记录类型初始化数据
  initializeByRecordType: function(recordType) {
    if (recordType === 'entry') {
      // 入栏记录：生成新批次号
      this.generateNewBatchNumber();
    } else {
      // 称重和出栏记录：加载已有批次选项
      this.loadActiveBatches();
    }
  },
  
  // 生成新的批次号（仅用于入栏记录）
  generateNewBatchNumber: function() {
    const now = new Date();
    const baseNumber = `QY-${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
    
    // 检查当前日期是否已有批次，如果有则加序号
    this.checkBatchNumberUniqueness(baseNumber, 1);
  },
  
  // 检查批次号唯一性
  checkBatchNumberUniqueness: function(baseNumber, sequence) {
    const batchNumber = sequence === 1 ? baseNumber : `${baseNumber}-${sequence}`;
    
    // 这里应该调用API检查批次号是否已存在
    // 暂时使用模拟逻辑
    const existingBatches = wx.getStorageSync('existing_batches') || [];
    
    if (existingBatches.includes(batchNumber)) {
      // 如果已存在，递增序号
      this.checkBatchNumberUniqueness(baseNumber, sequence + 1);
    } else {
      // 如果不存在，使用此批次号
      this.setData({
        batch: batchNumber
      });
    }
  },
  
  // 加载活跃批次（用于称重和出栏记录）
  loadActiveBatches: function() {
    // 模拟从API获取活跃批次数据
    const activeBatches = [
      { 
        batchNumber: 'QY-20250810', 
        breed: '太湖鹅', 
        currentCount: 500,
        entryDate: '2025-08-10',
        avgWeight: '2.5kg'
      },
      { 
        batchNumber: 'QY-20250812', 
        breed: '四川白鹅', 
        currentCount: 300,
        entryDate: '2025-08-12',
        avgWeight: '2.2kg'
      },
      { 
        batchNumber: 'QY-20250815', 
        breed: '皖西白鹅', 
        currentCount: 450,
        entryDate: '2025-08-15',
        avgWeight: '2.8kg'
      }
    ];
    
    // 格式化为选择器选项
    const batchOptions = activeBatches.map(batch => ({
      value: batch.batchNumber,
      label: `${batch.batchNumber} (${batch.breed}, ${batch.currentCount}只)`,
      ...batch
    }));
    
    this.setData({
      batchOptions: batchOptions,
      activeBatches: activeBatches
    });
  },

  // 标签页切换
  onTabChange: function(e) {
    const tabId = e.currentTarget.dataset.tab;
    const selectedTab = this.data.tabs.find(tab => tab.id === tabId);
    
    this.setData({
      activeTab: tabId,
      recordType: selectedTab.type
    });
    
    // 清空表单数据，保留通用字段
    this.resetFormData();
    
    // 根据新的记录类型重新初始化数据
    this.initializeByRecordType(selectedTab.type);
  },

  // 重置表单数据
  resetFormData: function() {
    // 保留批次号和日期，清空其他字段
    const { batch, date } = this.data;
    this.setData({
      count: '',
      weight: '',
      notes: '',
      source: '',
      breed: '',
      age: '',
      cost: '',
      supplier: '',
      price: '',
      buyer: '',
      totalIncome: '',
    });
  },

  // 表单输入处理
  onBatchInput: function(e) {
    this.setData({ batch: e.detail.value });
  },

  onDateChange: function(e) {
    this.setData({ date: e.detail.value });
  },

  onCountInput: function(e) {
    const inputCount = parseInt(e.detail.value) || 0;
    const maxCount = this.data.maxCount || 0;
    
    // 验证数量不能超过最大可操作数量（称重和出栏记录）
    if (this.data.recordType !== 'entry' && maxCount > 0 && inputCount > maxCount) {
      wx.showToast({
        title: `数量不能超过${maxCount}只`,
        icon: 'none'
      });
      
      this.setData({ 
        count: maxCount.toString()
      });
      this.calculateTotalIncome();
      return;
    }
    
    this.setData({ count: inputCount });
    this.calculateTotalIncome();
  },

  onWeightInput: function(e) {
    const weight = parseFloat(e.detail.value) || 0;
    this.setData({ weight: weight });
  },

  onSourceInput: function(e) {
    this.setData({ source: e.detail.value });
  },

  onBreedChange: function(e) {
    const index = e.detail.value;
    const selectedBreed = this.data.breedOptions[index];
    this.setData({ 
      breed: selectedBreed ? selectedBreed.value : '',
      breedLabel: selectedBreed ? selectedBreed.label : ''
    });
  },
  
  // 批次选择（用于称重和出栏记录）
  onBatchChange: function(e) {
    const index = e.detail.value;
    const selectedBatch = this.data.batchOptions[index];
    
    if (selectedBatch) {
      this.setData({ 
        batch: selectedBatch.value,
        batchLabel: selectedBatch.label,
        selectedBatchInfo: selectedBatch
      });
      
      // 根据选择的批次更新相关信息
      this.updateBatchRelatedInfo(selectedBatch);
    }
  },
  
  // 根据选择的批次更新相关信息
  updateBatchRelatedInfo: function(batchInfo) {
    if (this.data.recordType === 'weight') {
      // 称重记录：显示批次基本信息
      this.setData({
        maxCount: batchInfo.currentCount,
        breed: batchInfo.breed,
        entryDate: batchInfo.entryDate
      });
    } else if (this.data.recordType === 'sale') {
      // 出栏记录：显示可出栏信息
      this.setData({
        maxCount: batchInfo.currentCount,
        breed: batchInfo.breed,
        entryDate: batchInfo.entryDate,
        avgWeight: batchInfo.avgWeight
      });
      
      // 重新计算预计收入
      this.calculateTotalIncome();
    }
  },

  onAgeInput: function(e) {
    this.setData({ age: e.detail.value });
  },

  onCostInput: function(e) {
    this.setData({ cost: e.detail.value });
  },

  onSupplierInput: function(e) {
    this.setData({ supplier: e.detail.value });
  },

  onPriceInput: function(e) {
    const price = parseFloat(e.detail.value) || 0;
    this.setData({ price: price });
    this.calculateTotalIncome();
  },

  onBuyerInput: function(e) {
    this.setData({ buyer: e.detail.value });
  },

  onNotesInput: function(e) {
    this.setData({ notes: e.detail.value });
  },

  // 计算总收入
  calculateTotalIncome: function() {
    const { count, price, weight } = this.data;
    if (count && price) {
      const totalIncome = count * price * (weight || 1);
      this.setData({ totalIncome: totalIncome.toFixed(2) });
    }
  },

  // 验证表单
  validateForm: function() {
    const { recordType, batch, date, count } = this.data;
    
    if (!batch.trim()) {
      wx.showToast({ title: '请填写批次号', icon: 'none' });
      return false;
    }
    
    if (!date) {
      wx.showToast({ title: '请选择日期', icon: 'none' });
      return false;
    }
    
    if (!count || count <= 0) {
      wx.showToast({ title: '请填写数量', icon: 'none' });
      return false;
    }

    // 入栏记录特殊验证
    if (recordType === 'entry') {
      const { source, cost } = this.data;
      if (!source.trim()) {
        wx.showToast({ title: '请填写来源', icon: 'none' });
        return false;
      }
      if (!cost || parseFloat(cost) <= 0) {
        wx.showToast({ title: '请填写成本', icon: 'none' });
        return false;
      }
    }

    // 出栏记录特殊验证
    if (recordType === 'sale') {
      const { price, buyer } = this.data;
      if (!price || parseFloat(price) <= 0) {
        wx.showToast({ title: '请填写单价', icon: 'none' });
        return false;
      }
      if (!buyer.trim()) {
        wx.showToast({ title: '请填写买方', icon: 'none' });
        return false;
      }
    }

    return true;
  },

  // 保存记录
  onSave: function() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ loading: true });

    const { recordType } = this.data;
    let recordData = this.buildRecordData();

    // 模拟保存到后端
    setTimeout(() => {
      this.setData({ loading: false });
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 返回上一页并刷新数据
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  // 构建记录数据
  buildRecordData: function() {
    const { recordType, batch, date, count, weight, notes } = this.data;
    
    let recordData = {
      type: recordType,
      batch,
      date,
      count: parseInt(count),
      weight: parseFloat(weight) || 0,
      notes,
      createTime: new Date().toISOString()
    };

    // 根据记录类型添加特殊字段
    switch (recordType) {
      case 'entry':
        const { source, breed, age, cost, supplier } = this.data;
        recordData.details = {
          source,
          breed,
          age: parseInt(age) || 0,
          cost: parseFloat(cost) || 0,
          supplier
        };
        break;
        
      case 'sale':
        const { price, buyer, totalIncome } = this.data;
        recordData.details = {
          price: parseFloat(price) || 0,
          buyer,
          totalIncome: parseFloat(totalIncome) || 0
        };
        break;
        
      case 'weight':
        recordData.details = {
          averageWeight: parseFloat(weight) || 0,
          weightDate: date
        };
        break;
    }

    return recordData;
  },

  // 取消操作
  onCancel: function() {
    wx.navigateBack();
  }
});
