// backend/saas-admin/routes/cross-tenant.routes.js
const express = require("express");
const router = express.Router();
const crossTenantController = require("../controllers/cross-tenant.controller");
const { verifyToken } = require("../../middleware/auth.middleware");
const {
  authenticateAdmin,
  requireCrossTenantAccess,
  requirePlatformMonitoring,
} = require("../../middleware/rbac.middleware");

/**
 * 跨租户数据路由
 * 所有路由都需要平台管理员权限
 */

// 应用认证和权限中间件
router.use(verifyToken); // 验证JWT Token
router.use(authenticateAdmin); // 验证平台管理员身份
router.use(requireCrossTenantAccess); // 验证跨租户数据访问权限

/**
 * @route   GET /cross-tenant-data
 * @desc    显示跨租户数据监控页面
 * @access  Platform Admin
 */
router.get("/cross-tenant-data", crossTenantController.showCrossTenantDataPage);

/**
 * @route   GET /api/cross-tenant-overview
 * @desc    获取跨租户概览数据
 * @access  Platform Admin
 */
router.get(
  "/api/cross-tenant-overview",
  crossTenantController.getCrossTenantOverview,
);

/**
 * @route   GET /api/tenants-data
 * @desc    获取所有租户详细数据列表
 * @query   status - 租户状态筛选
 * @query   subscriptionPlan - 订阅计划筛选
 * @query   timeRange - 时间范围(天数)
 * @access  Platform Admin
 */
router.get("/api/tenants-data", crossTenantController.getTenantsData);

/**
 * @route   GET /api/tenant/:tenantCode/production
 * @desc    获取特定租户的生产数据
 * @param   tenantCode - 租户代码
 * @query   timeRange - 时间范围(天数)
 * @access  Platform Admin
 */
router.get(
  "/api/tenant/:tenantCode/production",
  crossTenantController.getTenantProductionData,
);

/**
 * @route   GET /api/tenant-comparison
 * @desc    获取租户对比数据
 * @query   tenantCodes - 要对比的租户代码数组
 * @query   metric - 对比指标(production/revenue/health/growth)
 * @access  Platform Admin
 */
router.get("/api/tenant-comparison", crossTenantController.getTenantComparison);

/**
 * @route   GET /api/export-cross-tenant-data
 * @desc    导出跨租户数据报表
 * @access  Platform Admin
 */
router.get(
  "/api/export-cross-tenant-data",
  crossTenantController.exportCrossTenantData,
);

module.exports = router;
