/**
 * 前端RBAC权限映射与校验
 * - 将前端角色代码(admin/manager/finance/user)映射到后端权限点集合
 * - 提供 hasPermission / hasAny / hasAll API 供页面与组件使用
 */

// 与后端保持一致的权限点定义（子集，前端用于UI权限栅格）
const PERMISSIONS = {
  PLATFORM: {
    TENANT_MANAGE: 'platform:tenant:manage',
    ANALYTICS: 'platform:analytics:view'
  },
  OA: {
    ACCESS: 'oa:access',
    FINANCE_VIEW: 'oa:finance:view',
    FINANCE_MANAGE: 'oa:finance:manage',
    PURCHASE_VIEW: 'oa:purchase:view',
    PURCHASE_APPROVE: 'oa:purchase:approve',
    REIMBURSEMENT_VIEW: 'oa:reimbursement:view',
    REIMBURSEMENT_APPROVE: 'oa:reimbursement:approve',
    APPROVAL_PROCESS: 'oa:approval:process'
  },
  PRODUCTION: {
    VIEW: 'production:view',
    MANAGE: 'production:manage',
    RECORDS_EDIT: 'production:records:edit',
    INVENTORY_MANAGE: 'production:inventory:manage'
  },
  SHOP: {
    VIEW: 'shop:view',
    MANAGE: 'shop:manage',
    ORDER_PROCESS: 'shop:order:process',
    PRODUCT_MANAGE: 'shop:product:manage'
  },
  USER: {
    VIEW: 'user:view',
    MANAGE: 'user:manage'
  }
};

// 将前端角色代码映射到后端权限组（最小可用集合）
const ROLE_TO_PERMISSIONS = {
  admin: [
    ...Object.values(PERMISSIONS.OA),
    ...Object.values(PERMISSIONS.PRODUCTION),
    ...Object.values(PERMISSIONS.SHOP),
    ...Object.values(PERMISSIONS.USER),
    PERMISSIONS.PLATFORM.TENANT_MANAGE,
    PERMISSIONS.PLATFORM.ANALYTICS
  ],
  manager: [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.PURCHASE_APPROVE,
    PERMISSIONS.OA.REIMBURSEMENT_APPROVE,
    PERMISSIONS.OA.APPROVAL_PROCESS,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.PRODUCTION.MANAGE,
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.USER.VIEW
  ],
  finance: [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.FINANCE_MANAGE,
    PERMISSIONS.OA.REIMBURSEMENT_APPROVE
  ],
  user: [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.PURCHASE_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.SHOP.VIEW
  ]
};

function getUserPermissions(userInfo) {
  const roleCode = userInfo?.roleCode || 'user';
  return ROLE_TO_PERMISSIONS[roleCode] || [];
}

function hasPermission(userInfo, permission) {
  const permissions = getUserPermissions(userInfo);
  return permissions.includes(permission);
}

function hasAll(userInfo, requiredPermissions) {
  const permissions = getUserPermissions(userInfo);
  return requiredPermissions.every(p => permissions.includes(p));
}

function hasAny(userInfo, requiredPermissions) {
  const permissions = getUserPermissions(userInfo);
  return requiredPermissions.some(p => permissions.includes(p));
}

module.exports = {
  PERMISSIONS,
  ROLE_TO_PERMISSIONS,
  getUserPermissions,
  hasPermission,
  hasAll,
  hasAny
};


