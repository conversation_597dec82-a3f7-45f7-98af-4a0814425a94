/**
 * 完整的数据库迁移管理系统
 * 支持版本控制、回滚、备份等功能
 */

const mysql = require("mysql2/promise");
const fs = require("fs").promises;
const path = require("path");

class MigrationManager {
  constructor() {
    this.config = {
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "zhihuiyange",
      password: process.env.DB_PASSWORD || "zhihuiyange123",
      database: process.env.DB_NAME || "zhihuiyange_local",
      charset: "utf8mb4",
    };

    this.migrationsDir = path.join(__dirname, "../migrations");
    this.backupsDir = path.join(__dirname, "../backups");
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(this.config);
      console.log("✅ 数据库连接成功");
    } catch (error) {
      console.error("❌ 数据库连接失败:", error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log("✅ 数据库连接已关闭");
    }
  }

  async ensureBackupsDir() {
    try {
      await fs.access(this.backupsDir);
    } catch {
      await fs.mkdir(this.backupsDir, { recursive: true });
      console.log("✅ 备份目录已创建");
    }
  }

  async createMigrationsTable() {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        migration_name VARCHAR(255) NOT NULL UNIQUE,
        version VARCHAR(50) NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        rollback_sql TEXT,
        checksum VARCHAR(64),
        INDEX idx_migration_name (migration_name),
        INDEX idx_version (version),
        INDEX idx_executed_at (executed_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await this.connection.execute(createTableSQL);
    console.log("✅ 迁移记录表已创建/更新");
  }

  async getMigrationFiles() {
    const files = await fs.readdir(this.migrationsDir);
    return files
      .filter((file) => file.endsWith(".sql"))
      .sort()
      .map((file) => ({
        filename: file,
        name: file.replace(".sql", ""),
        path: path.join(this.migrationsDir, file),
      }));
  }

  async getExecutedMigrations() {
    const [rows] = await this.connection.execute(
      "SELECT migration_name, version, executed_at FROM migrations ORDER BY executed_at",
    );
    return rows;
  }

  async calculateChecksum(filePath) {
    const crypto = require("crypto");
    const content = await fs.readFile(filePath, "utf8");
    return crypto.createHash("md5").update(content).digest("hex");
  }

  async createBackup(tableNames = null) {
    await this.ensureBackupsDir();

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const backupFile = path.join(this.backupsDir, `backup-${timestamp}.sql`);

    console.log("🔄 创建数据备份...");

    // 获取所有表名
    if (!tableNames) {
      const [tables] = await this.connection.execute(
        `SHOW TABLES FROM \`${this.config.database}\``,
      );
      tableNames = tables.map((row) => Object.values(row)[0]);
    }

    let backupSQL = `-- Database backup created at ${new Date().toISOString()}\n`;
    backupSQL += `-- Database: ${this.config.database}\n\n`;

    for (const tableName of tableNames) {
      // 获取表结构
      const [createTable] = await this.connection.execute(
        `SHOW CREATE TABLE \`${tableName}\``,
      );
      backupSQL += `-- Table: ${tableName}\n`;
      backupSQL += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      backupSQL += createTable[0]["Create Table"] + ";\n\n";

      // 获取表数据
      const [rows] = await this.connection.execute(
        `SELECT * FROM \`${tableName}\``,
      );
      if (rows.length > 0) {
        backupSQL += `-- Data for table: ${tableName}\n`;
        backupSQL += `LOCK TABLES \`${tableName}\` WRITE;\n`;
        backupSQL += `INSERT INTO \`${tableName}\` VALUES\n`;

        const valueStrings = rows.map((row) => {
          const values = Object.values(row).map((value) => {
            if (value === null) return "NULL";
            if (typeof value === "string")
              return `'${value.replace(/'/g, "\\'")}'`;
            if (value instanceof Date)
              return `'${value.toISOString().slice(0, 19).replace("T", " ")}'`;
            return value;
          });
          return `(${values.join(", ")})`;
        });

        backupSQL += valueStrings.join(",\n") + ";\n";
        backupSQL += `UNLOCK TABLES;\n\n`;
      }
    }

    await fs.writeFile(backupFile, backupSQL);
    console.log(`✅ 备份已创建: ${backupFile}`);
    return backupFile;
  }

  async executeMigration(migrationFile) {
    try {
      const sqlContent = await fs.readFile(migrationFile.path, "utf8");
      const checksum = await this.calculateChecksum(migrationFile.path);

      // 分割SQL语句
      const statements = sqlContent
        .split(";")
        .map((stmt) => stmt.trim())
        .filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"));

      console.log(`🔄 执行迁移: ${migrationFile.filename}`);

      // 开始事务
      await this.connection.execute("START TRANSACTION");

      try {
        for (const statement of statements) {
          if (statement.trim()) {
            await this.connection.execute(statement);
          }
        }

        // 记录迁移
        await this.connection.execute(
          "INSERT INTO migrations (migration_name, version, checksum) VALUES (?, ?, ?)",
          [
            migrationFile.name,
            this.extractVersion(migrationFile.filename),
            checksum,
          ],
        );

        await this.connection.execute("COMMIT");
        console.log(`✅ 迁移 ${migrationFile.filename} 执行成功`);
      } catch (error) {
        await this.connection.execute("ROLLBACK");
        throw error;
      }
    } catch (error) {
      console.error(
        `❌ 迁移 ${migrationFile.filename} 执行失败:`,
        error.message,
      );
      throw error;
    }
  }

  extractVersion(filename) {
    const match = filename.match(/^(\d+)/);
    return match ? match[1] : "000";
  }

  async isMigrationExecuted(migrationName) {
    const [rows] = await this.connection.execute(
      "SELECT id FROM migrations WHERE migration_name = ?",
      [migrationName],
    );
    return rows.length > 0;
  }

  async runPendingMigrations() {
    console.log("🚀 开始数据库迁移...\n");

    const migrationFiles = await this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();

    console.log(`📁 发现 ${migrationFiles.length} 个迁移文件`);
    console.log(`📊 已执行 ${executedMigrations.length} 个迁移`);

    let pendingCount = 0;

    for (const migrationFile of migrationFiles) {
      if (await this.isMigrationExecuted(migrationFile.name)) {
        console.log(`⏭️  跳过已执行的迁移: ${migrationFile.filename}`);
        continue;
      }

      // 创建备份（仅第一个待执行的迁移时）
      if (pendingCount === 0) {
        await this.createBackup();
      }

      await this.executeMigration(migrationFile);
      pendingCount++;
    }

    if (pendingCount === 0) {
      console.log("✅ 所有迁移都已是最新状态");
    } else {
      console.log(`\n🎉 成功执行了 ${pendingCount} 个新迁移！`);
    }
  }

  async getMigrationStatus() {
    const migrationFiles = await this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();

    console.log("\n📋 迁移状态报告");
    console.log("=".repeat(50));

    console.log("\n✅ 已执行的迁移:");
    executedMigrations.forEach((migration) => {
      console.log(
        `  ${migration.version.padStart(3, "0")} - ${migration.migration_name} (${migration.executed_at})`,
      );
    });

    console.log("\n⏳ 待执行的迁移:");
    const pendingMigrations = migrationFiles.filter(
      (file) =>
        !executedMigrations.some(
          (executed) => executed.migration_name === file.name,
        ),
    );

    if (pendingMigrations.length === 0) {
      console.log("  无待执行迁移");
    } else {
      pendingMigrations.forEach((migration) => {
        console.log(
          `  ${this.extractVersion(migration.filename).padStart(3, "0")} - ${migration.name}`,
        );
      });
    }

    console.log("\n📊 统计信息:");
    console.log(`  总迁移文件: ${migrationFiles.length}`);
    console.log(`  已执行: ${executedMigrations.length}`);
    console.log(`  待执行: ${pendingMigrations.length}`);
  }

  async validateDatabase() {
    console.log("\n🔍 验证数据库完整性...");

    try {
      // 检查关键表是否存在
      const criticalTables = [
        "users",
        "flocks",
        "unified_inventory",
        "migrations",
      ];

      for (const tableName of criticalTables) {
        const [tables] = await this.connection.execute(
          "SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = ?",
          [this.config.database, tableName],
        );

        if (tables.length === 0) {
          console.log(`❌ 关键表 ${tableName} 不存在`);
          return false;
        } else {
          console.log(`✅ 表 ${tableName} 存在`);
        }
      }

      // 检查约束
      const [constraints] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM information_schema.table_constraints WHERE table_schema = ? AND constraint_type = 'CHECK'",
        [this.config.database],
      );

      console.log(`✅ 数据库约束: ${constraints[0].count} 个CHECK约束`);

      // 检查数据完整性
      const [userCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM users",
      );
      console.log(`✅ 用户数据: ${userCount[0].count} 个用户`);

      console.log("🎉 数据库验证通过");
      return true;
    } catch (error) {
      console.error("❌ 数据库验证失败:", error.message);
      return false;
    }
  }
}

// 主执行函数
async function main() {
  const manager = new MigrationManager();

  const command = process.argv[2] || "migrate";

  try {
    await manager.connect();
    await manager.createMigrationsTable();

    switch (command) {
      case "migrate":
        await manager.runPendingMigrations();
        await manager.validateDatabase();
        break;

      case "status":
        await manager.getMigrationStatus();
        break;

      case "backup":
        await manager.createBackup();
        break;

      case "validate":
        await manager.validateDatabase();
        break;

      default:
        console.log("使用方法:");
        console.log("  node migration-manager.js migrate   - 执行待处理的迁移");
        console.log("  node migration-manager.js status    - 查看迁移状态");
        console.log("  node migration-manager.js backup    - 创建数据备份");
        console.log("  node migration-manager.js validate  - 验证数据库完整性");
        process.exit(1);
    }
  } catch (error) {
    console.error("❌ 操作失败:", error.message);
    process.exit(1);
  } finally {
    await manager.disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = MigrationManager;
