// 简化的平台管理员模型（用于演示）
class PlatformAdmin {
  static async findByCredentials(username, password) {
    // 简单的硬编码验证（实际应该查询数据库）
    if (username === "platform_admin" && password === "admin123") {
      return {
        id: 1,
        username: "platform_admin",
        name: "平台超级管理员",
        role: "super_admin",
        email: "<EMAIL>",
      };
    }
    return null;
  }
}

module.exports = PlatformAdmin;
