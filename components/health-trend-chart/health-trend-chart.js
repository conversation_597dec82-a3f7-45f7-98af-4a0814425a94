// components/health-trend-chart/health-trend-chart.js
Component({
  properties: {
    chartData: {
      type: Array,
      value: [],
      observer: 'onDataChange'
    },
    width: {
      type: Number,
      value: 0
    },
    height: {
      type: Number,
      value: 400
    }
  },

  data: {
    showTooltip: false,
    tooltipX: 0,
    tooltipY: 0,
    tooltipData: {}
  },

  lifetimes: {
    attached() {
      this.initChart();
    },
    
    async ready() {
      // 获取Canvas 2D上下文
      await this.initCanvas();
      
      // 延迟绘制，确保组件完全加载
      setTimeout(() => {
        this.drawChart();
      }, 100);
    }
  },

  methods: {
    // 初始化Canvas 2D
    async initCanvas() {
      try {
        // 获取Canvas 2D上下文
        const query = this.createSelectorQuery();
        const canvas = await new Promise((resolve) => {
          query.select('#healthTrendChart')
            .fields({ node: true, size: true })
            .exec((res) => {
              resolve(res[0]);
            });
        });

        if (!canvas || !canvas.node) {
          try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('Canvas元素未找到，延迟重试'); } catch(_) {}
          setTimeout(() => {
            this.initCanvas();
          }, 200);
          return;
        }

        const canvasNode = canvas.node;
        const ctx = canvasNode.getContext('2d');

        // 设置画布尺寸
        const dpr = wx.getWindowInfo().pixelRatio;
        canvasNode.width = canvas.width * dpr;
        canvasNode.height = canvas.height * dpr;
        ctx.scale(dpr, dpr);

        this.ctx = ctx;
        this.canvasNode = canvasNode;
        this.canvasWidth = canvas.width;
        this.canvasHeight = canvas.height;
      } catch (error) {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('初始化Canvas失败', error); } catch(_) {}
      }
    },

    initChart() {
      // 获取组件尺寸
      const query = this.createSelectorQuery();
      query.select('.chart-canvas').boundingClientRect((rect) => {
        if (rect) {
          this.canvasWidth = rect.width;
          this.canvasHeight = rect.height;
          this.drawChart();
        }
      }).exec();
    },

    onDataChange() {
      // 数据变化时重新绘制
      if (this.ctx) {
        this.drawChart();
      }
    },

    drawChart() {
      if (!this.ctx || !this.data.chartData || this.data.chartData.length === 0) {
        return;
      }

      const { chartData } = this.data;
      const ctx = this.ctx;
      
      // 设置画布尺寸
      const canvasWidth = this.canvasWidth || 300;
      const canvasHeight = this.canvasHeight || 400;
      
      // 设置边距
      const padding = {
        top: 40,
        right: 40,
        bottom: 60,
        left: 60
      };
      
      const chartWidth = canvasWidth - padding.left - padding.right;
      const chartHeight = canvasHeight - padding.top - padding.bottom;

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 计算数据范围
      const maxValue = Math.max(...chartData.map(item => 
        Math.max(item.healthy, item.sick, item.death)
      ));
      const minValue = 0;

      // 绘制背景网格
      this.drawGrid(ctx, padding, chartWidth, chartHeight, maxValue);

      // 绘制坐标轴
      this.drawAxes(ctx, padding, chartWidth, chartHeight, chartData, maxValue);

      // 绘制数据线
      this.drawLines(ctx, padding, chartWidth, chartHeight, chartData, maxValue, minValue);

      // 提交绘制
      // Canvas 2D不需要调用draw()方法
    },

    drawGrid(ctx, padding, width, height, maxValue) {
          ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;

      // 绘制水平网格线
      const ySteps = 5;
      for (let i = 0; i <= ySteps; i++) {
        const y = padding.top + (height / ySteps) * i;
        ctx.beginPath();
        ctx.moveTo(padding.left, y);
        ctx.lineTo(padding.left + width, y);
        ctx.stroke();
      }

      // 绘制垂直网格线
      const xSteps = this.data.chartData.length - 1;
      for (let i = 0; i <= xSteps; i++) {
        const x = padding.left + (width / xSteps) * i;
        ctx.beginPath();
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, padding.top + height);
        ctx.stroke();
      }
    },

    drawAxes(ctx, padding, width, height, data, maxValue) {
      ctx.fillStyle = '#666';
      ctx.font = '12px sans-serif';

      // Y轴标签
      const ySteps = 5;
      for (let i = 0; i <= ySteps; i++) {
        const value = Math.round((maxValue / ySteps) * (ySteps - i));
        const y = padding.top + (height / ySteps) * i;
        ctx.fillText(value.toString(), padding.left - 30, y + 4);
      }

      // X轴标签
      data.forEach((item, index) => {
        const x = padding.left + (width / (data.length - 1)) * index;
        ctx.save();
        ctx.translate(x, padding.top + height + 20);
        ctx.rotate(-Math.PI / 6); // 旋转-30度
        ctx.fillText(item.date, 0, 0);
        ctx.restore();
      });
    },

    drawLines(ctx, padding, width, height, data, maxValue, minValue) {
      const colors = {
        healthy: '#52C41A',
        sick: '#FAAD14',
        death: '#FF4D4F'
      };

      ['healthy', 'sick', 'death'].forEach(type => {
              ctx.strokeStyle = colors[type];
      ctx.lineWidth = 3;
        ctx.beginPath();

        data.forEach((item, index) => {
          const x = padding.left + (width / (data.length - 1)) * index;
          const y = padding.top + height - ((item[type] - minValue) / (maxValue - minValue)) * height;

          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });

        ctx.stroke();

        // 绘制数据点
        ctx.fillStyle = colors[type];
        data.forEach((item, index) => {
          const x = padding.left + (width / (data.length - 1)) * index;
          const y = padding.top + height - ((item[type] - minValue) / (maxValue - minValue)) * height;
          
          // 绘制方形数据点（遵循微信小程序Canvas规范）
          const pointSize = 8; // 4*2，保持视觉大小一致
          ctx.fillRect(x - pointSize/2, y - pointSize/2, pointSize, pointSize);
        });
      });
    },

    // 触摸事件处理
    onTouchStart(e) {
      this.handleTouch(e);
    },

    onTouchMove(e) {
      this.handleTouch(e);
    },

    onTouchEnd() {
      this.setData({
        showTooltip: false
      });
    },

    handleTouch(e) {
      if (!this.data.chartData || this.data.chartData.length === 0) return;

      const touch = e.touches[0];
      const { chartData } = this.data;
      
      // 计算触摸点对应的数据索引
      const canvasWidth = this.canvasWidth || 300;
      const padding = { left: 60, right: 40 };
      const chartWidth = canvasWidth - padding.left - padding.right;
      
      const touchX = touch.x - padding.left;
      const dataIndex = Math.round((touchX / chartWidth) * (chartData.length - 1));
      
      if (dataIndex >= 0 && dataIndex < chartData.length) {
        const data = chartData[dataIndex];
        this.setData({
          showTooltip: true,
          tooltipX: touch.x,
          tooltipY: touch.y - 100,
          tooltipData: data
        });
      }
    }
  }
});
