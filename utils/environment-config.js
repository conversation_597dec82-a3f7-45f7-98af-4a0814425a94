/**
 * 环境配置管理器
 * 统一管理开发、测试、生产环境的配置
 */

const ENVIRONMENT_CONFIG = {
  development: {
    apiBaseUrl: 'http://localhost:3001',
    uploadUrl: 'http://localhost:3001',
    websocketUrl: 'ws://localhost:3001',
    debug: true,
    enableMock: true
  },
  testing: {
    apiBaseUrl: 'https://test-api.zhihuiyange.com',
    uploadUrl: 'https://test-upload.zhihuiyange.com',
    websocketUrl: 'wss://test-ws.zhihuiyange.com',
    debug: true,
    enableMock: false
  },
  staging: {
    apiBaseUrl: 'https://staging-api.zhihuiyange.com',
    uploadUrl: 'https://staging-upload.zhihuiyange.com',
    websocketUrl: 'wss://staging-ws.zhihuiyange.com',
    debug: false,
    enableMock: false
  },
  production: {
    apiBaseUrl: 'https://api.zhihuiyange.com',
    uploadUrl: 'https://upload.zhihuiyange.com',
    websocketUrl: 'wss://ws.zhihuiyange.com',
    debug: false,
    enableMock: false
  }
};

class EnvironmentConfigManager {
  constructor() {
    this.currentEnvironment = this.detectEnvironment();
    this.config = ENVIRONMENT_CONFIG[this.currentEnvironment];
  }

  /**
   * 检测当前环境
   */
  detectEnvironment() {
    // 优先从全局配置获取
    const app = getApp();
    if (app && app.globalData && app.globalData.environment) {
      return app.globalData.environment;
    }

    // 从本地存储获取
    try {
      const savedEnv = wx.getStorageSync('current_environment');
      if (savedEnv && ENVIRONMENT_CONFIG[savedEnv]) {
        return savedEnv;
      }
    } catch (e) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('获取环境配置失败', e); } catch(_) {}
    }

    // 默认开发环境
    return 'development';
  }

  /**
   * 获取API基础URL
   */
  getApiBaseUrl() {
    return this.config.apiBaseUrl;
  }

  /**
   * 获取上传URL
   */
  getUploadUrl() {
    return this.config.uploadUrl;
  }

  /**
   * 获取WebSocket URL
   */
  getWebsocketUrl() {
    return this.config.websocketUrl;
  }

  /**
   * 获取请求配置
   */
  getRequestConfig() {
    return {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '1.0.0',
        'X-Platform': 'wechat-miniprogram'
      },
      enableDebug: this.config.debug,
      enableMock: this.config.enableMock
    };
  }

  /**
   * 是否为开发环境
   */
  isDevelopment() {
    return this.currentEnvironment === 'development';
  }

  /**
   * 是否为生产环境
   */
  isProduction() {
    return this.currentEnvironment === 'production';
  }

  /**
   * 获取当前环境名称
   */
  getEnvironment() {
    return this.currentEnvironment;
  }

  /**
   * 获取环境显示名称
   */
  getEnvironmentName() {
    const names = {
      development: '开发环境',
      testing: '测试环境',
      staging: '预发布环境',
      production: '生产环境'
    };
    return names[this.currentEnvironment] || '未知环境';
  }

  /**
   * 获取API端点URL（兼容旧版本）
   */
  getApiEndpointUrl(type = 'tenant') {
    return this.getApiBaseUrl() + '/api/v1';
  }

  /**
   * 切换环境
   */
  switchEnvironment(env) {
    if (ENVIRONMENT_CONFIG[env]) {
      this.currentEnvironment = env;
      this.config = ENVIRONMENT_CONFIG[env];
      
      // 保存到本地存储
      try {
        wx.setStorageSync('current_environment', env);
      } catch (e) {
        try { const logger = require('./logger.js'); logger.warn && logger.warn('保存环境配置失败', e); } catch(_) {}
      }

      // 通知应用重新初始化
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.environment = env;
      }
    }
  }

  /**
   * 生成微信域名配置指南（可选方法）
   */
  generateWechatDomainConfigGuide() {
    return `微信小程序域名配置:\nrequest合法域名: ${this.getApiBaseUrl()}\nuploadFile合法域名: ${this.getUploadUrl()}`;
  }
}

// 创建全局实例
const environmentConfig = new EnvironmentConfigManager();

module.exports = {
  environmentConfig,
  EnvironmentConfigManager,
  ENVIRONMENT_CONFIG
};