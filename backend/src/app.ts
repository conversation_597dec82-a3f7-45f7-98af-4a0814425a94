import { createServer } from "express-zod-api";
import { zodConfig } from "./config/zod-config";
import { routing } from "./config/routing";

// 启动服务器
const startServer = async () => {
  try {
    console.log("🚀 启动智慧养鹅SaaS平台 (TypeScript + Zod API)...");

    const { app, logger } = await createServer(zodConfig, routing);

    const port = process.env.PORT || 3001;

    app.listen(port, () => {
      logger.info(`🌟 服务器启动成功！`);
      logger.info(`📍 端口: ${port}`);
      logger.info(`🌍 环境: ${process.env.NODE_ENV || "development"}`);
      logger.info(`📚 API文档: http://localhost:${port}/docs`);
      logger.info(`🔍 健康检查: http://localhost:${port}/health`);
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      logger.info(`🛑 收到 ${signal} 信号，开始优雅关闭...`);
      process.exit(0);
    };

    process.on("SIGINT", () => gracefulShutdown("SIGINT"));
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
  } catch (error) {
    console.error("❌ 服务器启动失败:", error);
    process.exit(1);
  }
};

// 处理未捕获的异常
process.on("uncaughtException", (error) => {
  console.error("🚨 未捕获的异常:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("🚨 未处理的Promise拒绝:", reason, "at:", promise);
  process.exit(1);
});

// 启动应用
startServer();
