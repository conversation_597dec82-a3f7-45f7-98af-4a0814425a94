/* pages/more/more.wxss */
.more-container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 用户信息卡片 */
.user-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  border: 3rpx solid #e0e0e0;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.user-role {
  display: block;
  font-size: 22rpx;
  color: #0066cc;
  background-color: #e6f7ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  width: fit-content;
  margin-bottom: 8rpx;
}

.farm-name {
  font-size: 24rpx;
  color: #666666;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #0066cc;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #666666;
}

/* 功能分类 */
.function-sections {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

/* 网格布局功能 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.function-item:active {
  background-color: #e8f4fd;
  transform: scale(0.95);
}

.function-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.function-name {
  font-size: 22rpx;
  color: #333333;
  text-align: center;
  line-height: 1.2;
}

/* 列表布局功能 */
.function-list {
  display: flex;
  flex-direction: column;
}

.function-item-list {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.function-item-list:last-child {
  border-bottom: none;
}

.function-item-list:active {
  background-color: #f8f8f8;
}

.function-icon-small {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.function-name-list {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-top: 20rpx;
}

.app-version {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.copyright {
  font-size: 20rpx;
  color: #999999;
}

/* 响应式布局 */
@media (max-width: 400px) {
  .function-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 320px) {
  .function-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .user-card {
    flex-direction: column;
    text-align: center;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 16rpx;
  }
  
  .user-stats {
    flex-direction: row;
    justify-content: center;
    margin-top: 16rpx;
  }
}
