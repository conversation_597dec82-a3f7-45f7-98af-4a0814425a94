<!-- pages/oa/finance/overview/overview.wxml -->
<view class="finance-overview-container">
  <!-- 快捷操作 -->
  <view class="quick-actions-section">
    <view class="section-title">
      <text>快捷操作</text>
    </view>
    <view class="quick-actions-grid">
      <view class="quick-action-item" 
            bindtap="onOverviewCardTap" 
            data-type="overview"
            style="background: linear-gradient(135deg, #0066CC15 0%, #0066CC05 100%); border-left: 4rpx solid #0066CC;">
        <view class="action-icon">
                      <image src="/images/icons/grid.png" mode="aspectFit"></image>
        </view>
        <view class="action-content">
          <text class="action-title">财务概览</text>
          <text class="action-description">查看整体财务状况</text>
        </view>
        <view class="action-arrow">
          <text>></text>
        </view>
      </view>
      
      <view class="quick-action-item" 
            bindtap="onOverviewCardTap" 
            data-type="reports"
            style="background: linear-gradient(135deg, #00A86B15 0%, #00A86B05 100%); border-left: 4rpx solid #00A86B;">
        <view class="action-icon">
                      <image src="/images/icons/list.png" mode="aspectFit"></image>
        </view>
        <view class="action-content">
          <text class="action-title">财务报表</text>
          <text class="action-description">生成各类财务报表</text>
        </view>
        <view class="action-arrow">
          <text>></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 财务概览 -->
  <view class="overview-section">
    <view class="section-title">
      <text>财务概览</text>
      <text class="export-btn" bindtap="onExportReport">导出报表</text>
    </view>
    
    <view class="overview-grid">
      <view class="overview-card revenue" bindtap="onOverviewCardTap" data-type="revenue">
        <view class="card-header">
          <text class="card-label">总收入</text>
          <view class="card-trend positive">↑ {{overview.revenueGrowth}}%</view>
        </view>
        <text class="card-amount">￥{{overview.totalRevenue}}</text>
      </view>
      
      <view class="overview-card expense" bindtap="onOverviewCardTap" data-type="expense">
        <view class="card-header">
          <text class="card-label">总支出</text>
          <view class="card-trend negative">↓ {{Math.abs(overview.expenseGrowth)}}%</view>
        </view>
        <text class="card-amount">￥{{overview.totalExpense}}</text>
      </view>
      
      <view class="overview-card profit" bindtap="onOverviewCardTap" data-type="profit">
        <view class="card-header">
          <text class="card-label">净利润</text>
          <view class="card-trend positive">↑ {{overview.profitGrowth}}%</view>
        </view>
        <text class="card-amount">￥{{overview.netProfit}}</text>
      </view>
      
      <view class="overview-card pending" bindtap="onOverviewCardTap" data-type="pending">
        <view class="card-header">
          <text class="card-label">待付款</text>
          <view class="card-trend warning">待处理</view>
        </view>
        <text class="card-amount">￥{{overview.pendingPayments}}</text>
      </view>
    </view>
  </view>

  <!-- 收支趋势图表 -->
  <view class="trend-section">
    <view class="section-title">
      <text>收支趋势</text>
    </view>
    <view class="chart-container">
      <!-- 这里可以集成图表组件，暂时用模拟数据展示 -->
      <view class="chart-placeholder">
        <text class="chart-text">收支趋势图表</text>
        <text class="chart-desc">集成图表组件显示收支趋势</text>
      </view>
    </view>
  </view>

  <!-- 支出分类 -->
  <view class="expense-categories-section">
    <view class="section-title">
      <text>支出分类</text>
    </view>
    <view class="categories-list">
      <block wx:for="{{expenseCategories}}" wx:key="name">
        <view class="category-item" bindtap="onExpenseCategoryTap" data-category="{{item}}">
          <view class="category-indicator" style="background-color: {{item.color}};"></view>
          <view class="category-content">
            <view class="category-header">
              <text class="category-name">{{item.name}}</text>
              <text class="category-amount">￥{{item.amount}}</text>
            </view>
            <view class="category-progress">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{item.percentage}}%; background-color: {{item.color}};"></view>
              </view>
              <text class="category-percentage">{{item.percentage}}%</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 财务提醒 -->
  <view wx:if="{{alerts.length > 0}}" class="alerts-section">
    <view class="section-title">
      <text>财务提醒</text>
    </view>
    <view class="alerts-list">
      <block wx:for="{{alerts}}" wx:key="id">
        <view class="alert-item {{item.type}}" bindtap="onAlertTap" data-alert="{{item}}">
          <view class="alert-icon">
            <image src="/images/icons/{{item.type}}.png" mode="aspectFit"></image>
          </view>
          <view class="alert-content">
            <text class="alert-title">{{item.title}}</text>
            <text class="alert-message">{{item.message}}</text>
          </view>
          <view class="alert-arrow">
            <image src="/images/icons/arrow_right.png" mode="aspectFit"></image>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 最近交易 -->
  <view class="transactions-section">
    <view class="section-header">
      <text class="section-title">最近记录</text>
      <text class="view-all-btn" bindtap="onViewAllTransactions">查看全部</text>
    </view>
    
    <view class="transactions-list">
      <block wx:for="{{recentTransactions}}" wx:key="id">
        <view class="transaction-item" bindtap="onTransactionTap" data-transaction="{{item}}">
          <view class="transaction-icon {{item.type}}">
            <image src="/images/icons/{{item.type}}.png" mode="aspectFit"></image>
          </view>
          <view class="transaction-content">
            <text class="transaction-description">{{item.description}}</text>
            <view class="transaction-meta">
              <text class="transaction-category">{{item.category}}</text>
              <text class="transaction-date">{{item.date}}</text>
            </view>
          </view>
          <view class="transaction-amount {{item.type}}">
            <text>{{item.type === 'income' ? '+' : '-'}}￥{{item.amount}}</text>
          </view>
          <view class="transaction-status {{item.status}}">
            <text>{{item.status === 'completed' ? '已完成' : '待处理'}}</text>
          </view>
        </view>
      </block>
    </view>
    
    <view wx:if="{{recentTransactions.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无交易记录</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

</view>