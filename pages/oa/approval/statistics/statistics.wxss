/* pages/oa/approval/statistics/statistics.wxss */

.page-container {
  min-height: 100vh;
  background: #F2F2F7;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.time-filter {
  flex-shrink: 0;
}

.filter-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.filter-text {
  font-size: 26rpx;
  color: white;
}

.filter-arrow {
  font-size: 20rpx;
  color: white;
  opacity: 0.8;
}

/* 通用区块样式 */
.stats-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.08);
}

.overview-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-detail-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.view-detail-btn:active {
  background: rgba(0, 122, 255, 0.2);
  transform: scale(0.95);
}

.detail-text {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
}

.detail-arrow {
  font-size: 20rpx;
  color: #007aff;
}

/* 统计概览 */
.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.overview-card {
  padding: 24rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  position: relative;
  overflow: hidden;
}

.overview-card.primary {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: white;
}

.overview-card.warning {
  background: linear-gradient(135deg, #ff9500 0%, #ffad33 100%);
  color: white;
}

.overview-card.success {
  background: linear-gradient(135deg, #34c759 0%, #5dd378 100%);
  color: white;
}

.overview-card.danger {
  background: linear-gradient(135deg, #ff3b30 0%, #ff6259 100%);
  color: white;
}

.card-icon {
  font-size: 40rpx;
  opacity: 0.9;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.card-number {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}

.card-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 类型统计 */
.type-stats-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.type-stat-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-width: 180rpx;
}

.type-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.type-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.type-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.type-count {
  font-size: 24rpx;
  color: #666;
}

.type-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.8s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  min-width: 60rpx;
  text-align: right;
}



/* 效率统计 */
.efficiency-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.efficiency-item {
  background: #f8faff;
  border: 2rpx solid rgba(0, 122, 255, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  text-align: center;
}

.efficiency-label {
  font-size: 24rpx;
  color: #666;
}

.efficiency-value {
  font-size: 32rpx;
  font-weight: bold;
}

.efficiency-value.primary {
  color: #007aff;
}

.efficiency-value.success {
  color: #34c759;
}

.efficiency-value.info {
  color: #5ac8fa;
}

.efficiency-value.warning {
  color: #ff9500;
}

.efficiency-value.danger {
  color: #ff3b30;
}

/* 我的统计 */
.my-stats-container {
  display: flex;
  flex-direction: column;
}

.my-stats-card {
  background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
  border: 2rpx solid #007aff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.stats-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: rgba(0, 122, 255, 0.2);
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #007aff;
}

.stat-number.success {
  color: #34c759;
}

.stat-number.danger {
  color: #ff3b30;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stats-footer {
  display: flex;
  justify-content: space-around;
  padding-top: 24rpx;
  border-top: 2rpx solid rgba(0, 122, 255, 0.1);
}

.footer-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.footer-label {
  font-size: 22rpx;
  color: #666;
}

.footer-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
}



/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.refresh-indicator {
  position: fixed;
  top: 88rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 122, 255, 0.9);
  color: white;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  z-index: 999;
}

.refresh-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.refresh-text {
  font-size: 24rpx;
  color: white;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .overview-grid {
    grid-template-columns: 1fr;
  }

  .efficiency-grid {
    grid-template-columns: 1fr;
  }

  .stats-row {
    flex-direction: column;
    gap: 20rpx;
  }

  .stat-divider {
    width: 100%;
    height: 2rpx;
  }


}