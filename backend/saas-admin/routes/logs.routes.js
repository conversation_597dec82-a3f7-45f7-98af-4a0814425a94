const express = require("express");
const router = express.Router();
const fs = require("fs").promises;
const path = require("path");

// 系统日志管理页面
router.get("/", async (req, res) => {
  try {
    // 模拟系统日志数据
    const logs = [
      {
        id: 1,
        level: "error",
        message: "租户数据库连接失败",
        timestamp: new Date(Date.now() - 300000).toISOString(),
        source: "database",
        tenantId: "tenant_001",
        details: "Connection timeout after 30000ms",
      },
      {
        id: 2,
        level: "warning",
        message: "AI服务响应时间过长",
        timestamp: new Date(Date.now() - 600000).toISOString(),
        source: "ai-service",
        tenantId: "tenant_003",
        details: "Response time: 15.6s, threshold: 10.0s",
      },
      {
        id: 3,
        level: "info",
        message: "新租户注册成功",
        timestamp: new Date(Date.now() - 900000).toISOString(),
        source: "auth",
        tenantId: "tenant_005",
        details: "租户：测试养殖场 注册成功",
      },
      {
        id: 4,
        level: "info",
        message: "系统备份完成",
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        source: "system",
        tenantId: null,
        details: "备份大小: 2.3GB, 耗时: 45分钟",
      },
      {
        id: 5,
        level: "error",
        message: "API调用频率超限",
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        source: "api",
        tenantId: "tenant_002",
        details: "Rate limit exceeded: 1000 calls/hour",
      },
      {
        id: 6,
        level: "warning",
        message: "存储空间使用率过高",
        timestamp: new Date(Date.now() - 10800000).toISOString(),
        source: "storage",
        tenantId: "tenant_001",
        details: "使用率: 87%, 剩余空间: 650MB",
      },
    ];

    // 日志统计
    const logStats = {
      total: logs.length,
      errors: logs.filter((log) => log.level === "error").length,
      warnings: logs.filter((log) => log.level === "warning").length,
      info: logs.filter((log) => log.level === "info").length,
      last24Hours: logs.filter((log) => {
        const logTime = new Date(log.timestamp);
        const now = new Date();
        return now - logTime <= 24 * 60 * 60 * 1000;
      }).length,
    };

    // 按来源分组统计
    const sourceStats = logs.reduce((acc, log) => {
      acc[log.source] = (acc[log.source] || 0) + 1;
      return acc;
    }, {});

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("logs/index", {
        title: "系统日志",
        logs: logs,
        logStats: logStats,
        sourceStats: sourceStats,
      });
    } else {
      // 如果是API请求，返回JSON
      res.json({
        success: true,
        message: "获取系统日志成功",
        data: {
          logs: logs,
          stats: logStats,
          sourceStats: sourceStats,
        },
      });
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取系统日志失败:", error); } catch(_) {}

    if (req.accepts("html")) {
      return res.render("error", {
        title: "错误",
        error: "获取系统日志失败",
        message: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "获取系统日志失败",
        error: error.message,
      });
    }
  }
});

// 日志详情
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // 模拟获取单个日志详情
    const log = {
      id: parseInt(id),
      level: "error",
      message: "租户数据库连接失败",
      timestamp: new Date(Date.now() - 300000).toISOString(),
      source: "database",
      tenantId: "tenant_001",
      details: "Connection timeout after 30000ms",
      stackTrace: `Error: Connection timeout
        at Database.connect (/app/models/database.js:45)
        at TenantService.initialize (/app/services/tenant.js:23)
        at /app/controllers/tenant.controller.js:67
        at processTicksAndRejections (internal/process/task_queues.js:95)`,
      context: {
        requestId: "req_1234567890",
        userId: "admin_001",
        ip: "*************",
        userAgent:
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        path: "/saas-admin/tenants/123",
        method: "GET",
      },
    };

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("logs/detail", {
        title: `日志详情 #${log.id}`,
        log: log,
      });
    } else {
      // 如果是API请求，返回JSON
      res.json({
        success: true,
        message: "获取日志详情成功",
        data: log,
      });
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取日志详情失败:", error); } catch(_) {}

    if (req.accepts("html")) {
      return res.render("error", {
        title: "错误",
        error: "获取日志详情失败",
        message: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "获取日志详情失败",
        error: error.message,
      });
    }
  }
});

// 清理日志
router.delete("/cleanup", async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 模拟清理指定天数前的日志
    const cleanupResult = {
      deletedCount: 156,
      sizeFreed: "23.4MB",
      beforeDate: new Date(
        Date.now() - days * 24 * 60 * 60 * 1000,
      ).toISOString(),
    };

    res.json({
      success: true,
      message: `成功清理${days}天前的日志`,
      data: cleanupResult,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "清理日志失败",
      error: error.message,
    });
  }
});

// 导出日志
router.get("/export/:format", async (req, res) => {
  try {
    const { format } = req.params;
    const { startDate, endDate, level } = req.query;

    if (format === "csv") {
      // 模拟CSV导出
      const csvContent = `时间,级别,来源,租户ID,消息,详情
2024-01-20T10:30:00.000Z,error,database,tenant_001,租户数据库连接失败,Connection timeout after 30000ms
2024-01-20T10:25:00.000Z,warning,ai-service,tenant_003,AI服务响应时间过长,Response time: 15.6s
2024-01-20T10:15:00.000Z,info,auth,tenant_005,新租户注册成功,租户：测试养殖场 注册成功`;

      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="system-logs.csv"',
      );
      res.send(csvContent);
    } else if (format === "json") {
      // 模拟JSON导出
      const logs = [
        {
          timestamp: "2024-01-20T10:30:00.000Z",
          level: "error",
          source: "database",
          tenantId: "tenant_001",
          message: "租户数据库连接失败",
          details: "Connection timeout after 30000ms",
        },
      ];

      res.setHeader("Content-Type", "application/json");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="system-logs.json"',
      );
      res.json({ logs, exportTime: new Date().toISOString() });
    } else {
      res.status(400).json({
        success: false,
        message: "不支持的导出格式，支持: csv, json",
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "导出日志失败",
      error: error.message,
    });
  }
});

module.exports = router;
