/**app.wxss - 智慧养鹅小程序全局样式 V2.0**/

/* 导入设计系统 */
@import "styles/design-system.wxss";

/* 导入高级组件库 */
@import "styles/advanced-components.wxss";

/* 导入微动画系统 */
@import "styles/micro-animations.wxss";

/* 导入响应式增强 */
@import "styles/responsive-enhancements.wxss";

/* 导入深色模式支持 */
@import "styles/dark-mode.wxss";

/* 导入无障碍增强 */
@import "styles/accessibility.wxss";

/* 导入图标系统 */
@import "styles/icon-system.wxss";

/* 导入性能优化系统 */
@import "styles/performance-optimization.wxss";

/* 导入高级交互系统 */
@import "styles/advanced-interactions.wxss";

/* 引入iconfont样式 */
@import "/styles/iconfont.wxss";

/* ==================== 全局时间选择器修复 V2.0 ==================== */
/* 全局级别的时间选择器样式修复 - 解决过大色块问题 */
.filter-option {
  padding: 8rpx 16rpx !important;
  margin: 0 4rpx !important;
  border-radius: 12rpx !important;
  background: var(--bg-disabled) !important;
  color: var(--text-tertiary) !important;
  font-size: 24rpx !important;
  font-weight: 400 !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  text-align: center !important;
  line-height: 1.2 !important;
  border: none !important;
  box-sizing: border-box !important;
  display: inline-block !important;
  min-width: auto !important;
  max-width: none !important;
}

.filter-option.active {
  background: var(--primary) !important;
  color: var(--text-inverse) !important;
  font-weight: 500 !important;
  border: none !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 102, 204, 0.2) !important;
}

/* 全局重置和基础样式 */
page {
  background-color: var(--bg-secondary);
  font-family: var(--font-family);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  font-size: var(--text-base);
  scroll-behavior: smooth;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局盒模型 - 小程序中通过page和view元素实现 */
page,
view,
text,
button,
input,
image {
  box-sizing: border-box;
}

/* 全局容器 */
.app-container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 页面容器 */
.page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
}

/* 页面标题样式 */
.page-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  padding: var(--space-xl) 0;
  position: relative;
  margin-bottom: var(--space-lg);
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-xs);
}

/* 通用卡片样式增强 */
.common-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.common-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

.common-card-header {
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
  margin-bottom: var(--space-lg);
}

.common-card-body {
  flex: 1;
}

.common-card-footer {
  padding-top: var(--space-lg);
  border-top: 1rpx solid var(--border-light);
  margin-top: var(--space-lg);
}

/* 通用按钮样式增强 */
.primary-button {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  font-weight: 500;
  font-family: var(--font-family);
  box-shadow: var(--shadow-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 64rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.primary-button:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

.secondary-button {
  background-color: var(--primary-subtle);
  color: var(--primary);
  border: 1rpx solid var(--primary);
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  font-weight: 500;
  font-family: var(--font-family);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 64rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.secondary-button:active {
  background-color: var(--primary);
  color: var(--text-inverse);
  transform: scale(0.98);
}

/* 通用输入框样式增强 */
.enhanced-input {
  padding: var(--space-lg) var(--space-xl);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.enhanced-input::placeholder {
  color: var(--text-tertiary);
}

.enhanced-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 6rpx var(--primary-subtle);
}

/* 状态指示器 */
.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 3rpx;
  display: inline-block;
  margin-right: var(--space-sm);
}

/* 全局强制所有颜色指示器为方形 - v2加强版 */
.legend-color,
.value-color,
.current-color,
.point-dot,
.color-indicator {
  border-radius: 4rpx !important;
  -webkit-border-radius: 4rpx !important;
  -moz-border-radius: 4rpx !important;
  width: 20rpx !important;
  height: 20rpx !important;
  box-sizing: border-box !important;
}

/* 特别针对trend-chart组件的强制覆盖 */
.trend-chart-container .legend-color,
.chart-legend .legend-color,
.legend-list .legend-color,
.legend-item .legend-color {
  border-radius: 4rpx !important;
  width: 20rpx !important;
  height: 20rpx !important;
}

.status-success {
  background-color: var(--success);
}

.status-warning {
  background-color: var(--warning);
}

.status-error {
  background-color: var(--error);
}

.status-info {
  background-color: var(--info);
}

/* 状态徽章 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  line-height: 1;
}

.status-badge-success {
  background-color: var(--success-bg);
  color: var(--success);
}

.status-badge-warning {
  background-color: var(--warning-bg);
  color: var(--warning);
}

.status-badge-error {
  background-color: var(--error-bg);
  color: var(--error);
}

.status-badge-info {
  background-color: var(--info-bg);
  color: var(--info);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-mask);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid var(--text-inverse);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

/* 通用列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  background-color: var(--bg-primary);
  border-bottom: 1rpx solid var(--border-light);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: var(--bg-secondary);
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  line-height: var(--leading-snug);
}

.list-item-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
}

.list-item-extra {
  margin-left: var(--space-lg);
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.list-item-arrow {
  margin-left: var(--space-lg);
  width: 16rpx;
  height: 16rpx;
  opacity: 0.5;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl) var(--space-xl);
  text-align: center;
}

.empty-state-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.4;
}

.empty-state-title {
  font-size: var(--text-lg);
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
}

.empty-state-description {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: var(--border-light);
  margin: var(--space-lg) 0;
}

.divider-thick {
  height: 16rpx;
  background-color: var(--bg-secondary);
  margin: var(--space-xl) calc(-1 * var(--space-lg));
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

/* 固定底部按钮 */
.fixed-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-lg);
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: var(--space-lg);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}