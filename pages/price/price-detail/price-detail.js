// pages/price/price-detail/price-detail.js
const logger = require('../../../utils/logger.js');

Page({
  data: {
    activeType: 'gosling', // 当前选中的类型：gosling(鹅苗) 或 adult(成鹅)
    priceList: [], // 当前类型的价格列表
    selectedBreed: '', // 选中的品种
    selectedIndex: -1, // 选中品种的索引
    timeRange: '7d',
    stats: {
      maxPrice: 0,
      minPrice: 0,
      avgPrice: 0,
      volatility: 0
    },
    analysis: {
      trend: '',
      forecast: '',
      suggestion: ''
    },
    priceData: []
  },

  onLoad: function (options) {
    const { type } = options;
    this.setData({
      activeType: type || 'gosling'
    });
    
    this.loadPriceData();
  },

  // 加载价格数据
  loadPriceData() {
    wx.showLoading({
      title: '加载中...'
    });

    // 模拟API调用
    setTimeout(() => {
      const priceList = this.getMockPriceList();
      this.setData({
        priceList: priceList
      });
      
      wx.hideLoading();
    }, 500);
  },

  // 获取模拟价格列表数据
  getMockPriceList() {
    const { activeType } = this.data;
    
    if (activeType === 'gosling') {
      // 鹅苗价格数据 - 4个品种
      return [
        {
          breed: '白鹅苗',
          description: '优质白鹅品种',
          price: 12.5,
          change: 2.3
        },
        {
          breed: '灰鹅苗',
          description: '抗病能力强',
          price: 11.8,
          change: -1.2
        },
        {
          breed: '狮头鹅苗',
          description: '大型鹅种',
          price: 15.0,
          change: 3.1
        },
        {
          breed: '四川白鹅苗',
          description: '生长快速',
          price: 13.2,
          change: 0.8
        }
      ];
    } else {
      // 成鹅价格数据 - 4个品种
      return [
        {
          breed: '白鹅',
          description: '活重8-10斤',
          price: 18.5,
          change: 1.8
        },
        {
          breed: '灰鹅',
          description: '活重7-9斤',
          price: 17.2,
          change: -2.1
        },
        {
          breed: '狮头鹅',
          description: '活重12-15斤',
          price: 22.0,
          change: 4.2
        },
        {
          breed: '四川白鹅',
          description: '活重9-11斤',
          price: 19.8,
          change: 2.5
        }
      ];
    }
  },

  // 类型切换
  onTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      activeType: type,
      selectedBreed: '', // 清空选中品种
      selectedIndex: -1
    });
    this.loadPriceData();
  },

  // 品种选择
  onBreedSelect(e) {
    const { breed, index } = e.currentTarget.dataset;
    const selectedIndex = parseInt(index);
    
    this.setData({
      selectedBreed: breed,
      selectedIndex: selectedIndex
    });
    
    // 加载选中品种的详细数据
    this.loadBreedDetail(breed);
  },

  // 加载品种详细数据
  loadBreedDetail(breed) {
    wx.showLoading({
      title: '加载中...'
    });

    // 模拟API调用
    setTimeout(() => {
      const mockData = this.getMockBreedDetail(breed);
      this.setData({
        ...mockData
      });
      
      this.drawPriceChart();
      wx.hideLoading();
    }, 500);
  },

  // 获取品种详细数据
  getMockBreedDetail(breed) {
    // 生成模拟价格数据
    const priceData = this.generatePriceData(breed);
    const currentPrice = priceData[priceData.length - 1].price;
    const previousPrice = priceData[priceData.length - 2].price;
    const priceChange = ((currentPrice - previousPrice) / previousPrice * 100).toFixed(1);

    // 计算统计数据
    const prices = priceData.map(item => item.price);
    const maxPrice = Math.max(...prices).toFixed(1);
    const minPrice = Math.min(...prices).toFixed(1);
    const avgPrice = (prices.reduce((a, b) => a + b, 0) / prices.length).toFixed(1);
    const volatility = (((maxPrice - minPrice) / avgPrice) * 100).toFixed(1);

    return {
      priceData: priceData,
      stats: {
        maxPrice,
        minPrice,
        avgPrice,
        volatility
      },
      analysis: {
        trend: priceChange > 0 ? '近期价格呈上涨趋势，市场需求旺盛' : '近期价格有所下跌，建议关注市场动态',
        forecast: '预计未来一周价格将保持稳定，波动幅度在±2%以内',
        suggestion: this.data.activeType === 'gosling' ? '建议适量采购，关注季节性需求变化' : '当前价格适中，可考虑适时出栏'
      }
    };
  },

  // 生成价格数据
  generatePriceData(breed) {
    const data = [];
    const { activeType } = this.data;
    
    // 根据品种和类型确定基础价格
    let basePrice = 12;
    if (activeType === 'gosling') {
      switch (breed) {
        case '白鹅苗': basePrice = 12.5; break;
        case '灰鹅苗': basePrice = 11.8; break;
        case '狮头鹅苗': basePrice = 15.0; break;
        case '四川白鹅苗': basePrice = 13.2; break;
        default: basePrice = 12; break;
      }
    } else {
      switch (breed) {
        case '白鹅': basePrice = 18.5; break;
        case '灰鹅': basePrice = 17.2; break;
        case '狮头鹅': basePrice = 22.0; break;
        case '四川白鹅': basePrice = 19.8; break;
        default: basePrice = 18; break;
      }
    }
    
    let currentPrice = basePrice;
    
    for (let i = 0; i < 30; i++) {
      const change = (Math.random() - 0.5) * 2; // -1 到 1 的随机变化
      currentPrice += change;
      currentPrice = Math.max(currentPrice, basePrice * 0.8); // 最低不低于基础价格的80%
      currentPrice = Math.min(currentPrice, basePrice * 1.3); // 最高不超过基础价格的130%
      
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      
      data.push({
        date: this.formatDate(date),
        price: currentPrice
      });
    }
    
    return data;
  },

  // 绘制价格图表 - 使用Canvas 2D接口
  async drawPriceChart() {
    const { priceData } = this.data;
    if (!priceData || priceData.length === 0) return;

    try {
      // 获取Canvas 2D上下文
      const query = this.createSelectorQuery();
      const canvas = await new Promise((resolve) => {
        query.select('#priceChart')
          .fields({ node: true, size: true })
          .exec((res) => {
            resolve(res[0]);
          });
      });

      if (!canvas || !canvas.node) return;

      const canvasNode = canvas.node;
      const ctx = canvasNode.getContext('2d');

      // 设置画布尺寸
      const dpr = wx.getWindowInfo().pixelRatio;
      canvasNode.width = canvas.width * dpr;
      canvasNode.height = canvas.height * dpr;
      ctx.scale(dpr, dpr);

      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const padding = 40;

      this.drawChartWithSize(ctx, canvasWidth, canvasHeight, padding, canvasNode);
    } catch (error) {
      logger.error('绘制图表失败:', error);
    }
  },

  // 使用指定尺寸绘制图表
  drawChartWithSize(ctx, canvasWidth, canvasHeight, padding, canvasNode) {
    const { priceData } = this.data;

    // 数据范围
    const prices = priceData.map(item => item.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice || 1;

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // 绘制网格线
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    for (let i = 0; i <= 4; i++) {
      const y = padding + (canvasHeight - 2 * padding) * i / 4;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(canvasWidth - padding, y);
      ctx.stroke();
    }

    // 绘制价格线
    ctx.strokeStyle = '#0066cc';
    ctx.lineWidth = 2;
    ctx.beginPath();

    priceData.forEach((item, index) => {
      const x = padding + (canvasWidth - 2 * padding) * index / (priceData.length - 1);
      const y = canvasHeight - padding - (item.price - minPrice) / priceRange * (canvasHeight - 2 * padding);

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();
    // Canvas 2D不需要调用draw()方法
  },

  // 切换时间范围
  onTimeRangeChange(e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      timeRange: range
    });
    
    // 重新加载选中品种的数据
    if (this.data.selectedBreed) {
      this.loadBreedDetail(this.data.selectedBreed);
    }
  },

  // 订阅价格提醒
  onSubscribeTap() {
    wx.showToast({
      title: '订阅成功',
      icon: 'success'
    });
  },

  // 分享价格信息
  onShareTap() {
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  // 格式化日期
  formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}-${day}`;
  },

  // 分享功能
  onShareAppMessage() {
    const { selectedBreed, activeType, priceList, selectedIndex } = this.data;
    if (selectedBreed && selectedIndex >= 0 && priceList[selectedIndex]) {
      const currentPrice = priceList[selectedIndex].price;
      return {
        title: `${selectedBreed}最新价格：¥${currentPrice}`,
        path: `/pages/price/price-detail/price-detail?type=${activeType}`
      };
    }
    return {
      title: `${activeType === 'gosling' ? '鹅苗' : '成鹅'}价格行情`,
      path: `/pages/price/price-detail/price-detail?type=${activeType}`
    };
  }
});
