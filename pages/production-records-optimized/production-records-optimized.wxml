<!-- 优化后的生产记录页面 -->
<view class="production-records-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="header-title">生产记录</view>
      <view class="header-subtitle">智能管理，精准追踪</view>
    </view>
    <view class="header-actions">
      <view class="add-record-btn" bindtap="onShowAddMenu">
        <text class="add-icon">+</text>
      </view>
    </view>
  </view>

  <!-- 筛选标签栏 -->
  <view class="filter-tabs">
    <scroll-view class="tabs-scroll" scroll-x>
      <view class="tabs-container">
        <view 
          wx:for="{{filterTabs}}" 
          wx:key="value" 
          class="filter-tab {{selectedFilter === item.value ? 'active' : ''}}"
          data-value="{{item.value}}"
          bindtap="onFilterChange"
        >
          <text class="tab-icon">{{item.icon}}</text>
          <text class="tab-text">{{item.label}}</text>
          <view wx:if="{{item.count > 0}}" class="tab-badge">{{item.count}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-cards">
    <view class="stats-card">
      <view class="stats-number">{{todayRecords}}</view>
      <view class="stats-label">今日记录</view>
    </view>
    <view class="stats-card">
      <view class="stats-number">{{totalRecords}}</view>
      <view class="stats-label">总记录数</view>
    </view>
    <view class="stats-card">
      <view class="stats-number">{{activeBatches}}</view>
      <view class="stats-label">活跃批次</view>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="records-container">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{filteredRecords.length === 0}}" class="empty-container">
      <view class="empty-icon">📝</view>
      <view class="empty-title">暂无记录</view>
      <view class="empty-subtitle">点击右上角添加第一条生产记录</view>
      <view class="empty-action" bindtap="onShowAddMenu">
        <text class="action-text">立即添加</text>
      </view>
    </view>

    <!-- 记录列表 -->
    <scroll-view 
      wx:else
      class="records-list" 
      scroll-y 
      refresher-enabled="{{true}}"
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onRefresh"
      bindscrolltolower="onLoadMore"
    >
      <!-- 日期分组 -->
      <view wx:for="{{groupedRecords}}" wx:key="date" class="date-group">
        <view class="date-header">
          <view class="date-line"></view>
          <view class="date-text">{{item.dateText}}</view>
          <view class="date-line"></view>
        </view>
        
        <!-- 该日期下的记录 -->
        <view class="date-records">
          <production-record-item
            wx:for="{{item.records}}"
            wx:key="id"
            wx:for-item="record"
            record="{{record}}"
            show-finance-link="{{true}}"
            show-status="{{true}}"
            bind:itemTap="onRecordTap"
          />
        </view>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{hasMore}}" class="load-more">
        <view class="load-more-spinner"></view>
        <text class="load-more-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view wx:elif="{{filteredRecords.length > 0}}" class="no-more">
        <text class="no-more-text">没有更多记录了</text>
      </view>
    </scroll-view>
  </view>

  <!-- 添加记录菜单 -->
  <view wx:if="{{showAddMenu}}" class="add-menu-overlay" bindtap="onHideAddMenu">
    <view class="add-menu" catchtap="">
      <view class="menu-header">
        <text class="menu-title">添加记录</text>
        <view class="menu-close" bindtap="onHideAddMenu">×</view>
      </view>
      <view class="menu-options">
        <view class="menu-option" data-type="entry" bindtap="onAddRecord">
          <view class="option-icon entry">📥</view>
          <view class="option-content">
            <text class="option-title">入栏记录</text>
            <text class="option-desc">记录新批次鹅只入栏</text>
          </view>
        </view>
        <view class="menu-option" data-type="weight" bindtap="onAddRecord">
          <view class="option-icon weight">⚖️</view>
          <view class="option-content">
            <text class="option-title">称重记录</text>
            <text class="option-desc">记录鹅只体重变化</text>
          </view>
        </view>
        <view class="menu-option" data-type="sale" bindtap="onAddRecord">
          <view class="option-icon sale">📤</view>
          <view class="option-content">
            <text class="option-title">出栏记录</text>
            <text class="option-desc">记录鹅只销售出栏</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动操作按钮 -->
  <view class="fab" bindtap="onShowAddMenu">
    <text class="fab-icon">+</text>
  </view>
</view>
