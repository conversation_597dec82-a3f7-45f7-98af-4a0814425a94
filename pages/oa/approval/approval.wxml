<!-- pages/oa/approval/approval.wxml -->
<view class="page-container">
  
  <!-- 顶部统计卡片 -->
  <view class="stats-container">
    <view class="stats-grid">
      <view class="stat-card primary" bindtap="onStatTap" data-type="pending">
        <view class="stat-icon">⏳</view>
        <view class="stat-info">
          <text class="stat-number">{{approvalStats.pending}}</text>
          <text class="stat-label">待审批</text>
        </view>
        <view wx:if="{{approvalStats.urgent > 0}}" class="urgent-badge">{{approvalStats.urgent}}</view>
      </view>
      
      <view class="stat-card warning" bindtap="onStatTap" data-type="urgent">
        <view class="stat-icon">🚨</view>
        <view class="stat-info">
          <text class="stat-number">{{approvalStats.urgent}}</text>
          <text class="stat-label">紧急</text>
        </view>
      </view>
      
      <view class="stat-card success" bindtap="onStatTap" data-type="completed">
        <view class="stat-icon">✅</view>
        <view class="stat-info">
          <text class="stat-number">{{approvalStats.completed}}</text>
          <text class="stat-label">已完成</text>
        </view>
      </view>
      
      <view class="stat-card info" bindtap="onStatTap" data-type="monthly">
        <view class="stat-icon">📅</view>
        <view class="stat-info">
          <text class="stat-number">{{approvalStats.monthly}}</text>
          <text class="stat-label">本月</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能入口 -->
  <view class="function-container">
    <view class="function-title">审批功能</view>
    <view class="function-grid">
      <view class="function-item" bindtap="onFunctionTap" data-type="pending">
        <view class="function-icon">
          <text class="icon-text">📝</text>
        </view>
        <text class="function-text">我的审批</text>
        <view wx:if="{{approvalStats.pending > 0}}" class="function-badge">{{approvalStats.pending}}</view>
      </view>
      
      <view class="function-item" bindtap="onFunctionTap" data-type="history">
        <view class="function-icon">
          <text class="icon-text">📋</text>
        </view>
        <text class="function-text">审批历史</text>
      </view>
      
      <view class="function-item" bindtap="onFunctionTap" data-type="statistics">
        <view class="function-icon">
          <text class="icon-text">📊</text>
        </view>
        <text class="function-text">审批统计</text>
      </view>
    </view>
  </view>

  <!-- 最近审批动态 -->
  <view class="recent-container" wx:if="{{recentApprovals.length > 0}}">
    <view class="recent-header">
      <text class="recent-title">最近审批</text>
      <text class="recent-more" bindtap="onViewAllApprovals">查看全部</text>
    </view>
    
    <view class="recent-list">
      <view 
        wx:for="{{recentApprovals}}" 
        wx:key="id" 
        class="recent-item"
        bindtap="onApprovalItemTap" 
        data-id="{{item.id}}" 
        data-type="{{item.type}}"
        data-business-id="{{item.business_id}}"
      >
        <view class="recent-item-left">
          <view class="approval-status {{item.status}}">
            <text class="status-text">{{item.statusText}}</text>
          </view>
          <view class="approval-info">
            <text class="approval-title">{{item.title}}</text>
            <text class="approval-meta">{{item.applicant}} · {{item.timeAgo}}</text>
          </view>
        </view>
        
        <view class="recent-item-right">
          <view wx:if="{{item.isUrgent}}" class="urgent-tag">急</view>
          <text class="arrow">〉</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{isEmpty}}" class="empty-container">
    <view class="empty-content">
      <text class="empty-icon">📋</text>
      <text class="empty-title">暂无审批事项</text>
      <text class="empty-desc">您当前没有需要处理的审批</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

</view>