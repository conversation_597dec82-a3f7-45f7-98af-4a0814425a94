// pages/oa/reimbursement/detail/detail.js

const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info');
const { isLoggedIn, getCurrentUser } = require('../../../../utils/auth-helper');
const { formatDate } = require('../../../../utils/format');
const request = require('../../../../utils/request.js');
let logger; try { logger = require('../../../../utils/logger.js'); } catch(_) { logger = console; }
const { API } = require('../../../../constants/index.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,

    // 报销申请详情
    reimbursement: null,

    // 页面状态
    loading: true,
    submitting: false,

    // 审批操作
    showApprovalModal: false,
    approvalAction: '', // approve/reject
    approvalRemarks: '',

    // 状态配置
    statusConfig: {
      draft: { label: '草稿', color: '#8E8E93', bgColor: '#F2F2F7' },
      submitted: { label: '待审批', color: '#FF9500', bgColor: '#FFF4E6' },
      approved: { label: '已批准', color: '#00A86B', bgColor: '#E8F5E8' },
      rejected: { label: '已拒绝', color: '#FF3B30', bgColor: '#FFE8E8' },
      paid: { label: '已付款', color: '#007AFF', bgColor: '#E3F2FD' },
      cancelled: { label: '已取消', color: '#8E8E93', bgColor: '#F2F2F7' }
    },

    // 类别配置
    categoryConfig: {
      travel: '差旅费',
      office: '办公用品',
      meal: '餐费',
      transport: '交通费',
      communication: '通讯费',
      training: '培训费',
      entertainment: '招待费',
      other: '其他'
    }
  },

  onLoad(options) {
    if (!options.id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.initPage(options.id);
  },

  onShow() {
    // 每次显示时刷新数据
    if (this.data.reimbursement) {
      this.loadReimbursementDetail(this.data.reimbursement.id);
    }
  },

  onPullDownRefresh() {
    if (this.data.reimbursement) {
      this.loadReimbursementDetail(this.data.reimbursement.id).finally(() => {
        wx.stopPullDownRefresh();
      });
    }
  },

  /**
   * 初始化页面
   */
  initPage(id) {
    // 使用auth-helper进行可靠的登录检查

    if (!isLoggedIn()) {

      wx.showModal({

        title: '需要登录',

        content: '使用此功能需要先登录系统',

        confirmText: '去登录',

        cancelText: '取消',

        success: (res) => {

          if (res.confirm) {

            wx.redirectTo({ url: '/pages/login/login' });

          } else {

            wx.navigateBack();

          }

        }

      });

      return;

    }


    // 获取用户信息并同步数据

    const authUser = getCurrentUser();

    let userInfo = getCurrentUserInfo();


    if (!userInfo && authUser) {

      const app = getApp();

      app.globalData.userInfo = authUser;

      wx.setStorageSync('user_info', authUser);

      userInfo = getCurrentUserInfo();

    }

    

    const permissions = getUserPermissions();


    if (!userInfo) {

      wx.showModal({

        title: '数据错误',

        content: '无法获取用户信息，请重新登录',

        confirmText: '重新登录',

        showCancel: false,

        success: () => {

          wx.redirectTo({ url: '/pages/login/login' });

        }

      });

      return;

    }

    this.setData({
      userInfo,
      permissions
    });

    this.loadReimbursementDetail(id);
  },

  /**
   * 加载报销申请详情
   */
  async loadReimbursementDetail(id) {
    this.setData({ loading: true });

    try {
      const res = await request.get(`${API.ENDPOINTS.OA.REIMBURSEMENT.DETAIL(id)}`);
      const reimbursement = (res && res.success !== false) ? (res.data || res) : null;
      if (!reimbursement) throw new Error(res?.message || '加载失败');

      this.setData({ reimbursement });
      wx.setNavigationBarTitle({ title: reimbursement.title || '报销详情' });
      
    } catch (error) {
      try { logger.error && logger.error('加载报销申请详情失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 创建模拟报销详情数据
   */
  createMockReimbursementDetail(id) {
    const currentUser = wx.getStorageSync('user_info') || { 
      id: 'user_001', 
      name: '王五', 
      employeeId: 'EMP003', 
      department: '行政部', 
      position: '经理', 
      phone: '13900139000' 
    };
    
    return {
      id: id,
      title: '差旅费报销',
      type: 'travel',
      status: 'approved',
      amount: 2580.50,
      user_id: currentUser.id,
      applicant: currentUser.name,
      department: currentUser.department,
      position: currentUser.position,
      phone: currentUser.phone,
      submit_time: new Date(Date.now() - 86400000).toISOString(), // 1天前
      approve_time: new Date().toISOString(),
      description: '出差北京参加行业会议产生的差旅费用报销申请',
      items: [
        {
          category: 'transport',
          description: '往返机票',
          amount: 1200.00,
          date: new Date(Date.now() - *********).toISOString() // 2天前
        },
        {
          category: 'accommodation',
          description: '酒店住宿费',
          amount: 980.50,
          date: new Date(Date.now() - 86400000).toISOString() // 1天前
        },
        {
          category: 'meal',
          description: '餐费补贴',
          amount: 400.00,
          date: new Date(Date.now() - 86400000).toISOString() // 1天前
        }
      ],
      attachments: [
        {
          id: 'att_001',
          name: '机票行程单.pdf',
          size: '1.2MB',
          url: '/temp/files/ticket.pdf'
        },
        {
          id: 'att_002', 
          name: '酒店发票.jpg',
          size: '800KB',
          url: '/temp/files/hotel_receipt.jpg'
        }
      ],
      approval_flow: [
        {
          id: 'flow_001',
          approver: '张经理',
          action: 'approved',
          comment: '出差合理，符合公司标准，同意报销。',
          time: new Date().toISOString()
        }
      ]
    };
  },

  /**
   * 获取状态配置
   */
  getStatusConfig(status) {
    if (!status) {
      return {
        label: '未知状态',
        color: '#8E8E93',
        bgColor: '#F2F2F7'
      };
    }
    return this.data.statusConfig[status] || this.data.statusConfig.draft || {
      label: '草稿',
      color: '#8E8E93',
      bgColor: '#F2F2F7'
    };
  },

  /**
   * 获取类别标签
   */
  getCategoryLabel(category) {
    return this.data.categoryConfig[category] || category;
  },

  /**
   * 检查是否可以编辑
   */
  canEdit() {
    const { reimbursement, userInfo } = this.data;
    if (!reimbursement || !userInfo) return false;

    return reimbursement.user_id === userInfo.id &&
           (reimbursement.status === 'draft' || reimbursement.status === 'rejected');
  },

  /**
   * 检查是否可以删除
   */
  canDelete() {
    const { reimbursement, userInfo } = this.data;
    if (!reimbursement || !userInfo) return false;

    return reimbursement.user_id === userInfo.id &&
           (reimbursement.status === 'draft' || reimbursement.status === 'rejected');
  },

  /**
   * 检查是否可以审批
   */
  canApprove() {
    const { reimbursement, userInfo, permissions } = this.data;
    if (!reimbursement || !userInfo || !permissions.canApprove) return false;

    return reimbursement.status === 'submitted' &&
           reimbursement.user_id !== userInfo.id;
  },

  /**
   * 编辑报销申请
   */
  onEdit() {
    if (!this.canEdit()) return;

    wx.navigateTo({
      url: `/pages/oa/reimbursement/apply/apply?id=${this.data.reimbursement.id}`
    });
  },

  /**
   * 删除报销申请
   */
  onDelete() {
    if (!this.canDelete()) return;

    const { reimbursement } = this.data;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除报销申请"${reimbursement.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteReimbursement();
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async deleteReimbursement() {
    try {
      const res = await request.del(API.ENDPOINTS.OA.REIMBURSEMENT.DELETE(this.data.reimbursement.id));
      if (res && res.success !== false) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(res?.message || '删除失败');
      }
    } catch (error) {
      try { logger.error && logger.error('删除报销申请失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示审批模态框
   */
  showApprovalModal(action) {
    if (!this.canApprove()) return;

    this.setData({
      showApprovalModal: true,
      approvalAction: action,
      approvalRemarks: ''
    });
  },

  /**
   * 隐藏审批模态框
   */
  hideApprovalModal() {
    this.setData({
      showApprovalModal: false,
      approvalAction: '',
      approvalRemarks: ''
    });
  },

  /**
   * 审批意见输入
   */
  onRemarksInput(e) {
    this.setData({
      approvalRemarks: e.detail.value
    });
  },

  /**
   * 确认审批
   */
  async confirmApproval() {
    const { approvalAction, approvalRemarks, reimbursement } = this.data;

    if (!approvalRemarks.trim()) {
      wx.showToast({
        title: '请输入审批意见',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      const res = await request.post(API.ENDPOINTS.OA.REIMBURSEMENT.APPROVE(reimbursement.id), {
        action: approvalAction,
        remarks: approvalRemarks
      });
      if (res && res.success !== false) {
        wx.showToast({
          title: approvalAction === 'approve' ? '审批通过' : '审批拒绝',
          icon: 'success'
        });

        this.hideApprovalModal();

        // 重新加载数据
        setTimeout(() => {
          this.loadReimbursementDetail(reimbursement.id);
        }, 1000);
      } else {
        throw new Error(res?.message || '审批失败');
      }
    } catch (error) {
      try { logger.error && logger.error('审批失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '审批失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 审批流程操作
   */
  onApprovalAction(e) {
    const { action } = e.detail;
    this.showApprovalModal(action);
  },

  /**
   * 查看费用明细
   */
  onViewExpenseItem(e) {
    const { index } = e.currentTarget.dataset;
    const item = this.data.reimbursement.items[index];

    // 可以在这里实现费用明细的详细查看功能
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('查看费用明细', item); } catch(_) {}
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  }
});