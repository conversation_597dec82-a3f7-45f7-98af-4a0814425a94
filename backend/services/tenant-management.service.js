/**
 * SAAS平台租户管理服务
 * SAAS Platform Tenant Management Service
 * 
 * 提供完整的租户生命周期管理，包括创建、配置、监控、计费等功能
 */

const { Op } = require('sequelize');
const saasModels = require('../saas-admin/models/saas-platform.model');
const TenantDatabaseManager = require('../models/tenant-database.model');
const { UnifiedModelFactory } = require('../models/unified-models');

class TenantManagementService {
  constructor() {
    this.tenantDbManager = new TenantDatabaseManager();
    this.subscriptionLimits = this.initializeSubscriptionLimits();
    this.defaultFeatures = this.initializeDefaultFeatures();
  }

  // ================================
  // 租户创建和初始化
  // ================================

  /**
   * 创建新租户
   * @param {Object} tenantData 租户基础信息
   * @param {Object} adminUserData 管理员用户信息
   * @returns {Object} 创建结果
   */
  async createTenant(tenantData, adminUserData) {
    const transaction = await saasModels.sequelize.transaction();
    
    try {
      console.log('🏗️ 开始创建租户:', tenantData.tenantCode);

      // 1. 验证租户代码唯一性
      await this.validateTenantCode(tenantData.tenantCode);

      // 2. 创建租户记录
      const tenant = await this.createTenantRecord(tenantData, transaction);

      // 3. 创建租户数据库
      await this.createTenantDatabase(tenant.tenantCode);

      // 4. 初始化租户数据库结构
      await this.initializeTenantDatabase(tenant);

      // 5. 创建默认管理员用户
      await this.createTenantAdminUser(tenant, adminUserData);

      // 6. 设置默认配置
      await this.setupDefaultTenantConfiguration(tenant, transaction);

      // 7. 初始化订阅和计费
      await this.initializeTenantSubscription(tenant, transaction);

      // 8. 创建初始数据
      await this.createInitialTenantData(tenant);

      // 9. 记录创建日志
      await this.logTenantCreation(tenant, transaction);

      await transaction.commit();

      console.log('✅ 租户创建成功:', tenant.tenantCode);
      
      return {
        success: true,
        tenant: this.formatTenantInfo(tenant),
        credentials: {
          tenantCode: tenant.tenantCode,
          adminEmail: adminUserData.email,
          temporaryPassword: adminUserData.temporaryPassword
        }
      };

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 创建租户失败:', error);
      
      // 清理可能创建的资源
      await this.cleanupFailedTenantCreation(tenantData.tenantCode);
      
      throw error;
    }
  }

  /**
   * 验证租户代码唯一性
   */
  async validateTenantCode(tenantCode) {
    const existingTenant = await saasModels.Tenant.findOne({
      where: { tenantCode: tenantCode.toUpperCase() }
    });

    if (existingTenant) {
      throw new Error(`租户代码 ${tenantCode} 已存在`);
    }

    // 验证代码格式
    if (!/^[A-Z0-9_]{3,20}$/.test(tenantCode.toUpperCase())) {
      throw new Error('租户代码格式不正确，只能包含大写字母、数字和下划线，长度3-20字符');
    }
  }

  /**
   * 创建租户记录
   */
  async createTenantRecord(tenantData, transaction) {
    const subscriptionPlan = tenantData.subscriptionPlan || 'trial';
    const limits = this.subscriptionLimits[subscriptionPlan];
    
    return await saasModels.Tenant.create({
      tenantCode: tenantData.tenantCode.toUpperCase(),
      companyName: tenantData.companyName,
      contactName: tenantData.contactName,
      contactPhone: tenantData.contactPhone,
      contactEmail: tenantData.contactEmail,
      address: tenantData.address,
      businessLicense: tenantData.businessLicense,
      industry: tenantData.industry,
      scale: tenantData.scale || 'small',
      
      // 订阅信息
      subscriptionPlan,
      subscriptionStartDate: new Date(),
      subscriptionEndDate: this.calculateSubscriptionEndDate(subscriptionPlan),
      
      // 限制配置
      maxUsers: limits.maxUsers,
      maxFlocks: limits.maxFlocks,
      storageLimit: limits.storageLimit,
      apiCallsLimit: limits.apiCallsLimit,
      
      // 功能配置
      features: JSON.stringify(this.defaultFeatures[subscriptionPlan]),
      
      // 基础设置
      settings: JSON.stringify(this.getDefaultTenantSettings()),
      
      // 财务信息
      monthlyFee: limits.monthlyFee,
      
      // 初始状态
      status: 'pending'
    }, { transaction });
  }

  /**
   * 创建租户数据库
   */
  async createTenantDatabase(tenantCode) {
    const dbName = `smart_goose_${tenantCode.toLowerCase()}`;
    
    try {
      console.log(`📊 创建租户数据库: ${dbName}`);
      
      // 创建数据库连接
      const connection = await this.tenantDbManager.createTenantConnection({
        tenantCode,
        dbName
      });

      console.log(`✅ 租户数据库创建成功: ${dbName}`);
      return connection;
    } catch (error) {
      console.error(`❌ 创建租户数据库失败: ${dbName}`, error);
      throw new Error(`创建租户数据库失败: ${error.message}`);
    }
  }

  /**
   * 初始化租户数据库结构
   */
  async initializeTenantDatabase(tenant) {
    try {
      console.log(`🏗️ 初始化租户数据库结构: ${tenant.tenantCode}`);
      
      // 获取租户数据库连接
      const tenantConnection = this.tenantDbManager.getTenantConnection(tenant.tenantCode);
      
      if (!tenantConnection) {
        throw new Error('无法获取租户数据库连接');
      }

      // 创建统一模型工厂
      const modelFactory = new UnifiedModelFactory(tenantConnection);
      
      // 初始化所有模型
      await modelFactory.initializeModels();
      
      // 同步数据库结构
      await tenantConnection.sync({ force: false });
      
      console.log(`✅ 租户数据库结构初始化完成: ${tenant.tenantCode}`);
    } catch (error) {
      console.error(`❌ 初始化租户数据库结构失败: ${tenant.tenantCode}`, error);
      throw error;
    }
  }

  /**
   * 创建租户管理员用户
   */
  async createTenantAdminUser(tenant, adminUserData) {
    try {
      console.log(`👤 创建租户管理员用户: ${tenant.tenantCode}`);
      
      const tenantConnection = this.tenantDbManager.getTenantConnection(tenant.tenantCode);
      const modelFactory = new UnifiedModelFactory(tenantConnection);
      await modelFactory.initializeModels();
      
      const User = modelFactory.getModel('User');
      
      // 生成临时密码
      const temporaryPassword = this.generateTemporaryPassword();
      const hashedPassword = await this.hashPassword(temporaryPassword);
      
      const adminUser = await User.create({
        username: adminUserData.username || 'admin',
        password: hashedPassword,
        name: adminUserData.name || tenant.contactName,
        email: adminUserData.email || tenant.contactEmail,
        phone: adminUserData.phone || tenant.contactPhone,
        role: 'owner',
        status: 'active',
        farm_name: tenant.companyName
      });

      // 保存临时密码供返回
      adminUserData.temporaryPassword = temporaryPassword;
      
      console.log(`✅ 租户管理员用户创建成功: ${adminUser.username}`);
      return adminUser;
    } catch (error) {
      console.error(`❌ 创建租户管理员用户失败: ${tenant.tenantCode}`, error);
      throw error;
    }
  }

  // ================================
  // 租户配置和管理
  // ================================

  /**
   * 更新租户订阅计划
   */
  async updateTenantSubscription(tenantId, newPlan, duration = 12) {
    const transaction = await saasModels.sequelize.transaction();
    
    try {
      const tenant = await saasModels.Tenant.findByPk(tenantId);
      if (!tenant) {
        throw new Error('租户不存在');
      }

      const oldPlan = tenant.subscriptionPlan;
      const newLimits = this.subscriptionLimits[newPlan];
      
      if (!newLimits) {
        throw new Error('无效的订阅计划');
      }

      // 更新租户订阅信息
      await tenant.update({
        subscriptionPlan: newPlan,
        subscriptionStartDate: new Date(),
        subscriptionEndDate: this.calculateSubscriptionEndDate(newPlan, duration),
        maxUsers: newLimits.maxUsers,
        maxFlocks: newLimits.maxFlocks,
        storageLimit: newLimits.storageLimit,
        apiCallsLimit: newLimits.apiCallsLimit,
        monthlyFee: newLimits.monthlyFee,
        features: JSON.stringify(this.defaultFeatures[newPlan])
      }, { transaction });

      // 记录订阅变更
      await saasModels.SubscriptionChange.create({
        tenantId,
        fromPlan: oldPlan,
        toPlan: newPlan,
        changeDate: new Date(),
        duration,
        priceChange: newLimits.monthlyFee - this.subscriptionLimits[oldPlan].monthlyFee
      }, { transaction });

      // 记录操作日志
      await this.logTenantOperation(tenantId, 'SUBSCRIPTION_UPDATED', null, {
        oldPlan,
        newPlan,
        duration
      }, transaction);

      await transaction.commit();

      return {
        success: true,
        tenant: this.formatTenantInfo(tenant),
        subscriptionChange: {
          from: oldPlan,
          to: newPlan,
          effectiveDate: new Date(),
          endDate: tenant.subscriptionEndDate
        }
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 暂停租户服务
   */
  async suspendTenant(tenantId, reason, suspendUntil) {
    const transaction = await saasModels.sequelize.transaction();
    
    try {
      const tenant = await saasModels.Tenant.findByPk(tenantId);
      if (!tenant) {
        throw new Error('租户不存在');
      }

      if (tenant.status === 'suspended') {
        throw new Error('租户已处于暂停状态');
      }

      const originalStatus = tenant.status;
      
      await tenant.update({
        status: 'suspended',
        settings: JSON.stringify({
          ...JSON.parse(tenant.settings || '{}'),
          suspension: {
            reason,
            suspendedAt: new Date(),
            originalStatus,
            suspendUntil
          }
        })
      }, { transaction });

      // 记录暂停操作
      await this.logTenantOperation(tenantId, 'SUSPENDED', null, {
        reason,
        suspendUntil,
        originalStatus
      }, transaction);

      await transaction.commit();

      return {
        success: true,
        message: '租户已暂停',
        suspendedUntil: suspendUntil
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 恢复租户服务
   */
  async resumeTenant(tenantId) {
    const transaction = await saasModels.sequelize.transaction();
    
    try {
      const tenant = await saasModels.Tenant.findByPk(tenantId);
      if (!tenant) {
        throw new Error('租户不存在');
      }

      if (tenant.status !== 'suspended') {
        throw new Error('租户未处于暂停状态');
      }

      const settings = JSON.parse(tenant.settings || '{}');
      const originalStatus = settings.suspension?.originalStatus || 'active';

      await tenant.update({
        status: originalStatus,
        lastActiveAt: new Date(),
        settings: JSON.stringify({
          ...settings,
          resumption: {
            resumedAt: new Date(),
            resumedFrom: 'suspended'
          }
        })
      }, { transaction });

      // 记录恢复操作
      await this.logTenantOperation(tenantId, 'RESUMED', null, {
        resumedAt: new Date(),
        previousStatus: 'suspended'
      }, transaction);

      await transaction.commit();

      return {
        success: true,
        message: '租户服务已恢复',
        status: originalStatus
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  // ================================
  // 租户监控和分析
  // ================================

  /**
   * 获取租户使用统计
   */
  async getTenantUsageStats(tenantId, timeRange = '30d') {
    try {
      const tenant = await saasModels.Tenant.findByPk(tenantId);
      if (!tenant) {
        throw new Error('租户不存在');
      }

      const timeFilter = this.getTimeFilter(timeRange);
      
      // 获取使用统计数据
      const usageStats = await saasModels.TenantUsageStats.findAll({
        where: {
          tenantId,
          statDate: { [Op.gte]: timeFilter }
        },
        order: [['statDate', 'DESC']]
      });

      // 获取实时资源使用情况
      const currentUsage = await this.getCurrentTenantUsage(tenant);

      // 计算使用趋势
      const trends = this.calculateUsageTrends(usageStats);

      return {
        tenant: this.formatTenantInfo(tenant),
        currentUsage,
        usageHistory: usageStats,
        trends,
        limits: {
          maxUsers: tenant.maxUsers,
          maxFlocks: tenant.maxFlocks,
          storageLimit: tenant.storageLimit,
          apiCallsLimit: tenant.apiCallsLimit
        }
      };

    } catch (error) {
      console.error('获取租户使用统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前租户资源使用情况
   */
  async getCurrentTenantUsage(tenant) {
    try {
      const tenantConnection = this.tenantDbManager.getTenantConnection(tenant.tenantCode);
      
      if (!tenantConnection) {
        return {
          users: 0,
          flocks: 0,
          storageUsed: 0,
          apiCallsToday: 0
        };
      }

      // 查询用户数量
      const [userCount] = await tenantConnection.query(
        'SELECT COUNT(*) as count FROM users WHERE status = "active"'
      );

      // 查询鹅群数量
      const [flockCount] = await tenantConnection.query(
        'SELECT COUNT(*) as count FROM flocks WHERE status = "active"'
      );

      // 计算存储使用量（简化实现）
      const storageUsed = await this.calculateTenantStorageUsage(tenant.tenantCode);

      // 获取今日API调用数
      const today = new Date().toISOString().split('T')[0];
      const todayStats = await saasModels.TenantUsageStats.findOne({
        where: {
          tenantId: tenant.id,
          statDate: today
        }
      });

      return {
        users: userCount[0]?.count || 0,
        flocks: flockCount[0]?.count || 0,
        storageUsed: storageUsed,
        apiCallsToday: todayStats?.apiCalls || 0
      };

    } catch (error) {
      console.error('获取当前租户使用情况失败:', error);
      return {
        users: 0,
        flocks: 0,
        storageUsed: 0,
        apiCallsToday: 0
      };
    }
  }

  /**
   * 计算租户健康评分
   */
  async calculateTenantHealthScore(tenant) {
    try {
      let score = 100;
      const factors = [];

      // 1. 活跃度评分 (30%)
      const lastActiveDays = tenant.lastActiveAt 
        ? Math.floor((Date.now() - new Date(tenant.lastActiveAt).getTime()) / (1000 * 60 * 60 * 24))
        : 999;
      
      if (lastActiveDays <= 1) {
        factors.push({ name: '活跃度', score: 30, weight: 30 });
      } else if (lastActiveDays <= 7) {
        factors.push({ name: '活跃度', score: 20, weight: 30 });
        score -= 10;
      } else if (lastActiveDays <= 30) {
        factors.push({ name: '活跃度', score: 10, weight: 30 });
        score -= 20;
      } else {
        factors.push({ name: '活跃度', score: 0, weight: 30 });
        score -= 30;
      }

      // 2. 订阅状态评分 (25%)
      const subscriptionDaysLeft = tenant.subscriptionEndDate
        ? Math.floor((new Date(tenant.subscriptionEndDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24))
        : 0;

      if (subscriptionDaysLeft > 30) {
        factors.push({ name: '订阅状态', score: 25, weight: 25 });
      } else if (subscriptionDaysLeft > 7) {
        factors.push({ name: '订阅状态', score: 15, weight: 25 });
        score -= 10;
      } else if (subscriptionDaysLeft > 0) {
        factors.push({ name: '订阅状态', score: 5, weight: 25 });
        score -= 20;
      } else {
        factors.push({ name: '订阅状态', score: 0, weight: 25 });
        score -= 25;
      }

      // 3. 资源使用率评分 (25%)
      const currentUsage = await this.getCurrentTenantUsage(tenant);
      const usageRate = {
        users: tenant.maxUsers > 0 ? (currentUsage.users / tenant.maxUsers) : 0,
        flocks: tenant.maxFlocks > 0 ? (currentUsage.flocks / tenant.maxFlocks) : 0,
        storage: tenant.storageLimit > 0 ? (currentUsage.storageUsed / tenant.storageLimit) : 0
      };

      const avgUsageRate = (usageRate.users + usageRate.flocks + usageRate.storage) / 3;
      
      if (avgUsageRate >= 0.3 && avgUsageRate <= 0.8) {
        factors.push({ name: '资源使用', score: 25, weight: 25 });
      } else if (avgUsageRate < 0.3) {
        factors.push({ name: '资源使用', score: 15, weight: 25 });
        score -= 10;
      } else {
        factors.push({ name: '资源使用', score: 10, weight: 25 });
        score -= 15;
      }

      // 4. 系统状态评分 (20%)
      if (tenant.status === 'active') {
        factors.push({ name: '系统状态', score: 20, weight: 20 });
      } else if (tenant.status === 'trial') {
        factors.push({ name: '系统状态', score: 15, weight: 20 });
        score -= 5;
      } else {
        factors.push({ name: '系统状态', score: 0, weight: 20 });
        score -= 20;
      }

      return {
        totalScore: Math.max(0, Math.min(100, score)),
        factors,
        level: this.getHealthLevel(score),
        recommendations: this.getHealthRecommendations(factors, currentUsage, tenant)
      };

    } catch (error) {
      console.error('计算租户健康评分失败:', error);
      return {
        totalScore: 0,
        factors: [],
        level: 'unknown',
        recommendations: ['无法获取健康评分数据']
      };
    }
  }

  // ================================
  // 辅助方法
  // ================================

  /**
   * 初始化订阅计划限制
   */
  initializeSubscriptionLimits() {
    return {
      trial: {
        maxUsers: 3,
        maxFlocks: 5,
        storageLimit: 100 * 1024 * 1024, // 100MB
        apiCallsLimit: 1000,
        monthlyFee: 0
      },
      basic: {
        maxUsers: 10,
        maxFlocks: 20,
        storageLimit: 500 * 1024 * 1024, // 500MB
        apiCallsLimit: 5000,
        monthlyFee: 299
      },
      standard: {
        maxUsers: 50,
        maxFlocks: 100,
        storageLimit: 2 * 1024 * 1024 * 1024, // 2GB
        apiCallsLimit: 20000,
        monthlyFee: 899
      },
      premium: {
        maxUsers: 200,
        maxFlocks: 500,
        storageLimit: 10 * 1024 * 1024 * 1024, // 10GB
        apiCallsLimit: 100000,
        monthlyFee: 2899
      },
      enterprise: {
        maxUsers: -1, // 无限制
        maxFlocks: -1,
        storageLimit: -1,
        apiCallsLimit: -1,
        monthlyFee: 9999
      }
    };
  }

  /**
   * 初始化默认功能配置
   */
  initializeDefaultFeatures() {
    return {
      trial: [
        'basic_features',
        'limited_support'
      ],
      basic: [
        'basic_features',
        'reports',
        'email_support'
      ],
      standard: [
        'basic_features',
        'reports',
        'ai_diagnosis',
        'api_access',
        'email_support'
      ],
      premium: [
        'basic_features',
        'reports',
        'ai_diagnosis',
        'api_access',
        'advanced_analytics',
        'priority_support',
        'data_export'
      ],
      enterprise: [
        'all_features',
        'custom_branding',
        'dedicated_support',
        'sla_guarantee',
        'advanced_security',
        'custom_integrations'
      ]
    };
  }

  /**
   * 获取默认租户设置
   */
  getDefaultTenantSettings() {
    return {
      timezone: 'Asia/Shanghai',
      locale: 'zh-CN',
      currency: 'CNY',
      dateFormat: 'YYYY-MM-DD',
      notifications: {
        email: true,
        sms: false,
        system: true
      },
      security: {
        passwordPolicy: 'standard',
        sessionTimeout: 3600,
        ipRestriction: false
      },
      features: {
        allowGuestAccess: false,
        enableAuditLog: true,
        enableApiAccess: true
      }
    };
  }

  /**
   * 计算订阅结束日期
   */
  calculateSubscriptionEndDate(plan, duration = 12) {
    const endDate = new Date();
    
    if (plan === 'trial') {
      endDate.setDate(endDate.getDate() + 30); // 试用期30天
    } else {
      endDate.setMonth(endDate.getMonth() + duration);
    }
    
    return endDate;
  }

  /**
   * 生成临时密码
   */
  generateTemporaryPassword() {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * 密码哈希
   */
  async hashPassword(password) {
    const bcrypt = require('bcrypt');
    return await bcrypt.hash(password, 10);
  }

  /**
   * 格式化租户信息
   */
  formatTenantInfo(tenant) {
    const tenantData = tenant.toJSON();
    
    return {
      ...tenantData,
      settings: JSON.parse(tenantData.settings || '{}'),
      features: JSON.parse(tenantData.features || '[]'),
      subscriptionStatus: this.getSubscriptionStatus(tenant),
      daysLeft: this.getSubscriptionDaysLeft(tenant),
      usagePercentage: this.calculateUsagePercentage(tenant)
    };
  }

  /**
   * 获取订阅状态
   */
  getSubscriptionStatus(tenant) {
    if (!tenant.subscriptionEndDate) {
      return 'unknown';
    }

    const now = new Date();
    const endDate = new Date(tenant.subscriptionEndDate);
    const daysLeft = Math.floor((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (daysLeft < 0) {
      return 'expired';
    } else if (daysLeft <= 7) {
      return 'expiring';
    } else if (tenant.subscriptionPlan === 'trial') {
      return 'trial';
    } else {
      return 'active';
    }
  }

  /**
   * 获取健康等级
   */
  getHealthLevel(score) {
    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (score >= 40) return 'fair';
    if (score >= 20) return 'poor';
    return 'critical';
  }

  /**
   * 记录租户操作日志
   */
  async logTenantOperation(tenantId, operation, operatorId, details, transaction) {
    await saasModels.TenantOperationLog.create({
      tenantId,
      operation,
      operatorId,
      details: JSON.stringify(details),
      timestamp: new Date()
    }, { transaction });
  }

  /**
   * 清理失败的租户创建
   */
  async cleanupFailedTenantCreation(tenantCode) {
    try {
      console.log(`🧹 清理失败的租户创建: ${tenantCode}`);
      
      // 清理可能创建的数据库连接
      if (this.tenantDbManager.getTenantConnection(tenantCode)) {
        await this.tenantDbManager.closeTenantConnection(tenantCode);
      }
      
      // 这里可以添加更多清理逻辑，如删除数据库、清理文件等
      
    } catch (error) {
      console.error('清理失败的租户创建时出错:', error);
    }
  }

  /**
   * 获取时间过滤器
   */
  getTimeFilter(timeRange) {
    const now = new Date();
    const filters = {
      '1d': new Date(now.getTime() - 24 * 60 * 60 * 1000),
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      '90d': new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    };
    
    return filters[timeRange] || filters['30d'];
  }
}

module.exports = TenantManagementService;