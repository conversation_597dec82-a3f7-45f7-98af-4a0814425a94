/*!
 * iconfont 图标库 V2.0
 * 与统一图标系统集成的图标字体
 * 说明：这是iconfont的模板文件，需要从https://www.iconfont.cn/下载实际的图标文件来替换
 */

@font-face {
  font-family: "iconfont";
  src: url('/styles/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 32rpx; /* 默认图标大小，可通过icon组件的size属性覆盖 */
  font-style: normal;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* 继承父级颜色 */
  color: inherit;
  
  /* 防止用户选中 */
  -webkit-user-select: none;
  user-select: none;
}

/* 图标类名占位符，实际使用时需要根据从iconfont下载的文件进行替换 */
.icon-health:before {
  content: "\e601";
}

.icon-production:before {
  content: "\e602";
}

.icon-shop:before {
  content: "\e603";
}

.icon-profile:before {
  content: "\e604";
}

.icon-notification:before {
  content: "\e605";
}

.icon-task:before {
  content: "\e606";
}

.icon-announcement:before {
  content: "\e607";
}

.icon-more:before {
  content: "\e608";
}

.icon-search:before {
  content: "\e609";
}

.icon-settings:before {
  content: "\e610";
}