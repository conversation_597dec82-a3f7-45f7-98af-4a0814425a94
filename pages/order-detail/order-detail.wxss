/* pages/order-detail/order-detail.wxss - 重新设计的订单详情页面样式 */
@import '/styles/design-system.wxss';

.order-detail-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  background: var(--bg-primary);
  margin: var(--space-lg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: var(--space-xl);
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 详情内容 */
.detail-content {
  height: calc(100vh - 120rpx);
  padding: var(--space-lg);
}

/* 通用区域样式 */
.status-section,
.logistics-section,
.address-section,
.goods-section,
.order-info-section,
.price-section {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1rpx solid var(--border-subtle);
  background: var(--bg-subtle);
}

.section-icon {
  font-size: var(--text-lg);
}

.section-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* 订单状态 */
.status-section {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
  padding: var(--space-2xl) var(--space-xl);
  background: linear-gradient(135deg, var(--primary-subtle) 0%, var(--bg-primary) 100%);
}

.status-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.status-pending {
  background: var(--warning-subtle);
  border: 2rpx solid var(--warning-light);
}

.status-paid {
  background: var(--info-subtle);
  border: 2rpx solid var(--info-light);
}

.status-shipped {
  background: var(--success-subtle);
  border: 2rpx solid var(--success-light);
}

.status-completed {
  background: var(--success-subtle);
  border: 2rpx solid var(--success-light);
}

.status-cancelled {
  background: var(--error-subtle);
  border: 2rpx solid var(--error-light);
}

.icon-text {
  font-size: 40rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-sm);
}

.status-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* 物流信息 */
.logistics-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
}

.logistics-company {
  flex: 1;
}

.company-name {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-xs);
}

.tracking-number {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.logistics-status {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.status-text {
  font-size: var(--text-sm);
  color: var(--success);
  font-weight: var(--font-medium);
}

.arrow-icon {
  font-size: var(--text-base);
  color: var(--text-tertiary);
}

/* 收货地址 */
.address-info {
  padding: var(--space-xl);
}

.address-contact {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.contact-name {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.contact-phone {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.address-detail {
  font-size: var(--text-base);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
}

/* 商品信息 */
.goods-list {
  padding: 0 var(--space-xl) var(--space-xl);
}

.goods-item {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-lg) 0;
  border-bottom: 1rpx solid var(--border-subtle);
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  flex-shrink: 0;
  border: 1rpx solid var(--border-light);
}

.goods-info {
  flex: 1;
  min-width: 0;
}

.goods-name {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-sm);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-specs {
  margin-bottom: var(--space-sm);
}

.specs-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.goods-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
}

.goods-tag {
  background: var(--bg-subtle);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  border: 1rpx solid var(--border-light);
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--primary);
}

.goods-qty {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 订单信息 */
.order-details {
  padding: 0 var(--space-xl) var(--space-xl);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) 0;
  border-bottom: 1rpx solid var(--border-subtle);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.detail-value {
  font-size: var(--text-base);
  color: var(--text-primary);
  text-align: right;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.order-number {
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-size: var(--text-sm);
}

.copy-btn {
  background: var(--primary-subtle);
  color: var(--primary);
  font-size: var(--text-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  border: 1rpx solid var(--primary-light);
  transition: var(--transition-all);
}

.copy-btn:active {
  background: var(--primary-light);
  transform: scale(0.98);
}

/* 费用明细 */
.price-details {
  padding: 0 var(--space-xl) var(--space-xl);
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) 0;
  border-bottom: 1rpx solid var(--border-subtle);
}

.price-item:last-child {
  border-bottom: none;
}

.price-item.total {
  padding-top: var(--space-xl);
  border-top: 2rpx solid var(--border-light);
  margin-top: var(--space-lg);
}

.price-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.price-item.total .price-label {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.price-value {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: var(--font-semibold);
}

.price-value.discount {
  color: var(--success);
}

.price-item.total .price-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--primary);
}

/* 底部占位 */
.bottom-placeholder {
  height: var(--space-4xl);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  padding: var(--space-lg) var(--space-xl) calc(var(--space-lg) + env(safe-area-inset-bottom));
  border-top: 1rpx solid var(--border-light);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.contact-service {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm);
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
}

.contact-service:active {
  background: var(--bg-secondary);
}

.contact-icon {
  font-size: var(--text-xl);
}

.contact-text {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.action-buttons {
  flex: 1;
  display: flex;
  gap: var(--space-lg);
}

.action-btn {
  flex: 1;
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  border: none;
  transition: var(--transition-all);
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-light);
}

.action-btn.secondary:active {
  background: var(--bg-tertiary);
  transform: scale(0.98);
}

.action-btn.primary {
  background: var(--primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.action-btn.primary:active {
  background: var(--primary-dark);
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

/* 发票模态框 */
.invoice-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  width: 600rpx;
  max-width: 90vw;
  box-shadow: var(--shadow-xl);
  position: relative;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl) var(--space-2xl);
  border-bottom: 1rpx solid var(--border-light);
}

.modal-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.modal-close {
  font-size: var(--text-2xl);
  color: var(--text-tertiary);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-all);
}

.modal-close:active {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.modal-body {
  padding: var(--space-lg) 0;
}

.invoice-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl) var(--space-2xl);
  transition: var(--transition-all);
}

.invoice-option:active {
  background: var(--bg-secondary);
}

.option-text {
  font-size: var(--text-base);
  color: var(--text-primary);
}

.option-check {
  font-size: var(--text-lg);
  color: var(--primary);
  font-weight: var(--font-bold);
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .detail-content {
    padding: var(--space-sm);
  }

  .status-section {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .status-icon {
    width: 80rpx;
    height: 80rpx;
  }

  .goods-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .goods-image {
    width: 100%;
    height: 200rpx;
  }

  .bottom-actions {
    flex-direction: column;
    gap: var(--space-sm);
  }

  .action-buttons {
    width: 100%;
  }
}

/* 可访问性支持 */
@media (prefers-reduced-motion: reduce) {

  .loading-spinner,
  .status-icon,
  .action-btn,
  .modal-content,
  .invoice-option {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {

  .status-section,
  .logistics-section,
  .address-section,
  .goods-section,
  .order-info-section,
  .price-section {
    border-width: 2rpx;
  }

  .action-btn {
    border-width: 2rpx;
  }
}