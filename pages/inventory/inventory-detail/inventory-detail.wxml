<!-- pages/inventory/inventory-detail/inventory-detail.wxml -->
<view class="inventory-container">
  <!-- 总览卡片 -->
  <view class="overview-section">
    <view class="overview-header">
      <text class="overview-title">库存总览</text>
      <text class="update-time">更新时间: {{updateTime}}</text>
    </view>
    <view class="overview-stats">
      <view class="stat-item">
        <text class="stat-value">{{totalCount}}</text>
        <text class="stat-label">总存栏</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{healthyCount}}</text>
        <text class="stat-label">健康</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{sickCount}}</text>
        <text class="stat-label">患病</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{quarantineCount}}</text>
        <text class="stat-label">隔离</text>
      </view>
    </view>
  </view>

  <!-- 分类统计 -->
  <view class="category-section">
    <view class="section-header">
      <text class="section-title">分类统计</text>
      <view class="filter-tabs">
        <view class="tab-item {{activeTab === 'age' ? 'active' : ''}}" data-tab="age" bindtap="onTabChange">按年龄</view>
        <view class="tab-item {{activeTab === 'breed' ? 'active' : ''}}" data-tab="breed" bindtap="onTabChange">按品种</view>
        <view class="tab-item {{activeTab === 'area' ? 'active' : ''}}" data-tab="area" bindtap="onTabChange">按区域</view>
      </view>
    </view>
    
    <!-- 按年龄分类 -->
    <view wx:if="{{activeTab === 'age'}}" class="category-list">
      <block wx:for="{{ageCategories}}" wx:key="name">
        <view class="category-item" bindtap="onCategoryTap" data-type="age" data-value="{{item.name}}">
          <view class="category-info">
            <text class="category-name">{{item.name}}</text>
            <text class="category-desc">{{item.description}}</text>
          </view>
          <view class="category-stats">
            <text class="category-count">{{item.count}}只</text>
            <text class="category-percent">{{item.percent}}%</text>
          </view>
          <view class="category-chart">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.percent}}%"></view>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 按品种分类 -->
    <view wx:elif="{{activeTab === 'breed'}}" class="category-list">
      <block wx:for="{{breedCategories}}" wx:key="name">
        <view class="category-item" bindtap="onCategoryTap" data-type="breed" data-value="{{item.name}}">
          <view class="category-info">
            <text class="category-name">{{item.name}}</text>
            <text class="category-desc">{{item.description}}</text>
          </view>
          <view class="category-stats">
            <text class="category-count">{{item.count}}只</text>
            <text class="category-percent">{{item.percent}}%</text>
          </view>
          <view class="category-chart">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.percent}}%"></view>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 按区域分类 -->
    <view wx:elif="{{activeTab === 'area'}}" class="category-list">
      <block wx:for="{{areaCategories}}" wx:key="name">
        <view class="category-item" bindtap="onCategoryTap" data-type="area" data-value="{{item.name}}">
          <view class="category-info">
            <text class="category-name">{{item.name}}</text>
            <text class="category-desc">{{item.description}}</text>
          </view>
          <view class="category-stats">
            <text class="category-count">{{item.count}}只</text>
            <text class="category-percent">{{item.percent}}%</text>
          </view>
          <view class="category-chart">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.percent}}%"></view>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 健康状态分析 -->
  <view class="health-section">
    <view class="section-title">健康状态分析</view>
    <view class="health-chart">
              <canvas type="2d" id="healthChart" class="chart-canvas"></canvas>
    </view>
    <view class="health-legend">
      <view class="legend-item">
        <view class="legend-color healthy"></view>
        <text class="legend-text">健康 ({{healthyPercent}}%)</text>
      </view>
      <view class="legend-item">
        <view class="legend-color sick"></view>
        <text class="legend-text">患病 ({{sickPercent}}%)</text>
      </view>
      <view class="legend-item">
        <view class="legend-color quarantine"></view>
        <text class="legend-text">隔离 ({{quarantinePercent}}%)</text>
      </view>
    </view>
  </view>

  <!-- 预警信息 -->
  <view class="alert-section" wx:if="{{alerts.length > 0}}">
    <view class="section-title">预警信息</view>
    <view class="alert-list">
      <block wx:for="{{alerts}}" wx:key="id">
        <view class="alert-item {{item.level}}">
          <view class="alert-icon">
            <image src="/images/icons/{{item.level}}.png" mode="aspectFit"></image>
          </view>
          <view class="alert-content">
            <text class="alert-title">{{item.title}}</text>
            <text class="alert-desc">{{item.description}}</text>
            <text class="alert-time">{{item.time}}</text>
          </view>
          <view class="alert-action" bindtap="onAlertTap" data-id="{{item.id}}">
            <text>处理</text>
          </view>
        </view>
      </block>
    </view>
  </view>


</view>
