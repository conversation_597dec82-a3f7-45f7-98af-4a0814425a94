// pages/oa/approval/statistics/statistics.js

const { getCurrentUser } = require('../../../../utils/auth-helper.js');
const { formatDate } = require('../../../../utils/format.js');

Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 用户信息
    currentUser: null,
    
    // 统计概览数据
    overview: {
      totalApprovals: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      monthly: 0,
      avgDuration: '0天'
    },
    
    // 按类型统计
    typeStats: [],
    
    // 按月份统计
    monthlyStats: [],
    
    // 效率统计
    efficiencyStats: {
      avgApprovalTime: '2.3天',
      quickestApproval: '0.5天',
      longestApproval: '5.2天',
      approvalRate: '95.2%'
    },
    
    // 我的统计（如果是审批人）
    myStats: {
      processed: 0,
      approved: 0, 
      rejected: 0,
      avgTime: '1.8天'
    },
    
    // 图表数据
    chartData: {
      monthly: [],
      types: [],
      trends: []
    },
    
    // 筛选条件
    filters: {
      timeRange: 'thisMonth', // thisMonth, lastMonth, thisQuarter, thisYear
      timeRangeIndex: 0, // picker选中的索引
      department: 'all',
      status: 'all'
    },
    
    // 时间范围选项
    timeRangeOptions: [
      { value: 'thisMonth', label: '本月' },
      { value: 'lastMonth', label: '上月' },
      { value: 'thisQuarter', label: '本季度' },
      { value: 'thisYear', label: '本年度' }
    ]
  },

  onLoad: function(options) {
    this.initPage();
  },

  onShow: function() {
    this.refreshData();
  },

  onPullDownRefresh: function() {
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('开始下拉刷新统计数据'); } catch(_) {}
    wx.vibrateShort();
    this.loadStatisticsData(true);
  },

  onReachBottom: function() {
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('到达页面底部'); } catch(_) {}
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    const currentUser = getCurrentUser() || { 
      id: 'user_001', 
      name: '管理员', 
      role: 'admin',
      department: '总经办'
    };
    
    this.setData({ currentUser });
    this.loadStatisticsData();
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    this.loadStatisticsData();
  },

  /**
   * 加载统计数据
   */
  loadStatisticsData: function(isRefresh = false) {
    this.setData({
      loading: !isRefresh,
      refreshing: isRefresh
    });

    // 模拟数据加载
    setTimeout(() => {
      const mockData = this.generateMockStatistics();
      
      this.setData({
        ...mockData,
        loading: false,
        refreshing: false
      });

      if (isRefresh) {
        wx.stopPullDownRefresh();
        wx.showToast({
          title: '刷新完成',
          icon: 'success',
          duration: 1500
        });
      }

      try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('统计数据加载完成', mockData); } catch(_) {}
    }, 1000);
  },

  /**
   * 生成模拟统计数据
   */
  generateMockStatistics: function() {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    
    return {
      overview: {
        totalApprovals: 328,
        pending: 8,
        approved: 268,
        rejected: 52,
        monthly: 45,
        avgDuration: '2.3天'
      },
      
      typeStats: [
        { type: 'leave', label: '请假申请', count: 156, percentage: 47.6, color: '#007AFF' },
        { type: 'reimbursement', label: '报销申请', count: 89, percentage: 27.1, color: '#34C759' },
        { type: 'purchase', label: '采购申请', count: 52, percentage: 15.9, color: '#FF9500' },
        { type: 'overtime', label: '加班申请', count: 31, percentage: 9.5, color: '#AF52DE' }
      ],
      
      monthlyStats: this.generateMonthlyStats(6), // 最近6个月
      
      efficiencyStats: {
        avgApprovalTime: '2.3天',
        quickestApproval: '0.5天',
        longestApproval: '5.2天',
        approvalRate: '95.2%',
        onTimeRate: '92.8%'
      },
      
      myStats: {
        processed: 156,
        approved: 142,
        rejected: 14,
        avgTime: '1.8天',
        efficiency: '91.0%'
      },
      
      chartData: {
        monthly: this.generateMonthlyChartData(),
        types: this.generateTypeChartData(),
        trends: this.generateTrendData()
      }
    };
  },

  /**
   * 生成月度统计数据
   */
  generateMonthlyStats: function(months) {
    const stats = [];
    const currentDate = new Date();
    
    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = (date.getMonth() + 1) + '月';
      
      stats.push({
        month: monthName,
        date: formatDate(date, 'YYYY-MM'),
        total: Math.floor(Math.random() * 50) + 20,
        approved: Math.floor(Math.random() * 40) + 15,
        rejected: Math.floor(Math.random() * 10) + 2,
        pending: Math.floor(Math.random() * 5) + 1
      });
    }
    
    return stats;
  },

  /**
   * 生成月度图表数据
   */
  generateMonthlyChartData: function() {
    return this.generateMonthlyStats(6).map(item => ({
      month: item.month,
      value: item.total
    }));
  },

  /**
   * 生成类型图表数据
   */
  generateTypeChartData: function() {
    return [
      { name: '请假', value: 156, color: '#007AFF' },
      { name: '报销', value: 89, color: '#34C759' },
      { name: '采购', value: 52, color: '#FF9500' },
      { name: '加班', value: 31, color: '#AF52DE' }
    ];
  },

  /**
   * 生成趋势数据
   */
  generateTrendData: function() {
    const trends = [];
    const labels = ['1月', '2月', '3月', '4月', '5月', '6月'];
    
    labels.forEach(month => {
      trends.push({
        month,
        approved: Math.floor(Math.random() * 40) + 20,
        rejected: Math.floor(Math.random() * 8) + 2
      });
    });
    
    return trends;
  },

  /**
   * 时间范围改变
   */
  onTimeRangeChange: function(e) {
    const index = parseInt(e.detail.value);
    const timeRange = this.data.timeRangeOptions[index].value;
    
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('时间范围改变', { timeRange, index }); } catch(_) {}
    
    this.setData({
      'filters.timeRange': timeRange,
      'filters.timeRangeIndex': index
    });
    
    // 重新加载数据
    this.loadStatisticsData();
    
    wx.showToast({
      title: '数据更新中',
      icon: 'loading',
      duration: 1000
    });
  },

  /**
   * 查看详细统计
   */
  onViewDetail: function(e) {
    const { type } = e.currentTarget.dataset;
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('查看详细统计', type); } catch(_) {}
    
    wx.vibrateShort();
    
    switch (type) {
      case 'type':
        this.showTypeDetail();
        break;
      case 'efficiency':
        this.showEfficiencyDetail();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 显示类型详情
   */
  showTypeDetail: function() {
    const typeStats = this.data.typeStats;
    const content = typeStats.map(item => 
      `${item.label}: ${item.count}件 (${item.percentage}%)`
    ).join('\n');
    
    wx.showModal({
      title: '按类型统计详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },



  /**
   * 显示效率详情
   */
  showEfficiencyDetail: function() {
    const { efficiencyStats } = this.data;
    const content = `平均审批时间: ${efficiencyStats.avgApprovalTime}\n最快审批: ${efficiencyStats.quickestApproval}\n最慢审批: ${efficiencyStats.longestApproval}\n通过率: ${efficiencyStats.approvalRate}\n按时完成率: ${efficiencyStats.onTimeRate}`;
    
    wx.showModal({
      title: '审批效率详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },


});