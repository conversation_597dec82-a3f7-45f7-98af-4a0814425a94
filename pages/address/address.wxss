/* pages/address/address.wxss - 现代化收货地址页面样式 */
@import '/styles/design-system.wxss';

.address-page {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 地址容器 */
.address-container {
  flex: 1;
  padding: 16rpx;
}

/* 地址列表 */
.address-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 地址卡片 */
.address-card {
  position: relative;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.address-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.address-card.is-default {
  border-color: #007AFF;
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);
}

/* 默认标识 */
.default-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  z-index: 10;
}

.badge-text {
  font-size: 20rpx;
}

/* 地址标签 */
.address-tag {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: #F2F2F7;
  color: #8E8E93;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  z-index: 10;
}

.tag-text {
  font-size: 20rpx;
  font-weight: 500;
}

/* 地址主要内容 */
.address-main {
  margin-top: 40rpx;
  margin-bottom: 20rpx;
}

/* 收件人信息 */
.recipient-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.recipient-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.recipient-phone {
  font-size: 28rpx;
  color: #8E8E93;
  background: #F2F2F7;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-family: 'SF Mono', monospace;
}

/* 地址信息 */
.address-section {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 16rpx;
}

.address-text {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.region {
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
}

.detail {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
  line-height: 1.4;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  gap: 12rpx;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #F2F2F7;
}

.action-btn {
  flex: 1;
  text-align: center;
  padding: 12rpx;
  border-radius: 8rpx;
  background: #F8F9FA;
  transition: all 0.2s ease;
}

.action-hover {
  background: #E5E5EA;
  transform: scale(0.98);
}

.action-text {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
}

.edit-action .action-text {
  color: #007AFF;
}

.star-action .action-text {
  color: #FF9500;
}

.star-action.is-active {
  background: #FFF3E0;
}

.star-action.is-active .action-text {
  color: #FF9500;
  font-weight: 600;
}

.delete-action .action-text {
  color: #FF3B30;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  min-height: 60vh;
}

.empty-icon {
  font-size: 120rpx;
  opacity: 0.3;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #C7C7CC;
  line-height: 1.4;
}

/* 底部安全区域 */
.bottom-safe-area {
  height: calc(120rpx + env(safe-area-inset-bottom));
}

/* 添加地址按钮区域 */
.add-address-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #F2F2F7;
  z-index: 100;
}

.add-btn {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: white;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.25);
  transition: all 0.3s ease;
}

.add-btn-hover {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.2);
}

.add-icon {
  font-size: 32rpx;
  font-weight: 300;
}

.add-text {
  font-size: 32rpx;
  font-weight: 600;
}

/* 删除确认弹窗 */
.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.delete-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90vw;
  overflow: hidden;
}

.modal-header {
  padding: 32rpx 32rpx 16rpx;
  text-align: center;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-body {
  padding: 0 32rpx 32rpx;
  text-align: center;
}

.modal-message {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.address-preview {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 16rpx;
  text-align: left;
}

.preview-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  display: block;
  margin-bottom: 8rpx;
}

.preview-address {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  padding: 32rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.cancel-btn {
  color: #8E8E93;
  border-right: 1rpx solid #F2F2F7;
}

.cancel-btn:active {
  background: #F8F9FA;
}

.confirm-btn {
  color: #FF3B30;
  font-weight: 600;
}

.confirm-btn:active {
  background: #FFF5F5;
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .address-container {
    padding: 12rpx;
  }

  .address-card {
    padding: 20rpx;
  }

  .recipient-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .action-section {
    flex-direction: column;
    gap: 8rpx;
  }

  .action-btn {
    padding: 16rpx;
  }
}

/* 可访问性支持 */
@media (prefers-reduced-motion: reduce) {

  .address-card,
  .action-btn,
  .add-btn,
  .delete-modal {
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {
  .address-card {
    border-width: 3rpx;
  }

  .action-btn {
    border: 2rpx solid #E5E5EA;
  }
}