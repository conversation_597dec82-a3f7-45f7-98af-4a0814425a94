/* 智慧养鹅小程序 - 高级组件库 V1.0 */
/* 基于现有设计系统的扩展组件 */

/* ==================== 高级卡片组件 ==================== */

/* 数据统计卡片 */
.stats-card {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  position: relative;
  overflow: hidden;
  transition: var(--transition-all);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
}

.stats-card:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-lg);
}

.stats-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-lg);
}

.stats-card-icon {
  width: 56rpx;
  height: 56rpx;
  padding: var(--space-sm);
  background: linear-gradient(135deg, var(--primary-subtle) 0%, var(--secondary-subtle) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-card-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-black);
  color: var(--primary);
  line-height: var(--leading-none);
  margin-bottom: var(--space-xs);
}

.stats-card-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-sm);
}

.stats-card-trend {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

.stats-card-trend.up {
  color: var(--success);
}

.stats-card-trend.down {
  color: var(--error);
}

.stats-card-trend-icon {
  width: 20rpx;
  height: 20rpx;
}

/* 功能卡片 */
.feature-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  text-align: center;
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-subtle) 0%, var(--secondary-subtle) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-lg);
}

.feature-card:active::before {
  opacity: 1;
}

.feature-card-content {
  position: relative;
  z-index: 1;
}

.feature-card-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto var(--space-lg);
  padding: var(--space-lg);
  background: linear-gradient(135deg, var(--primary-subtle) 0%, var(--secondary-subtle) 100%);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-tight);
}

.feature-card-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* ==================== 高级按钮组件 ==================== */

/* 渐变按钮 */
.btn-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  box-shadow: var(--shadow-primary);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.6s ease;
}

.btn-gradient:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-md);
}

.btn-gradient:active::before {
  left: 100%;
}

/* 玻璃态按钮 */
.btn-glass {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: var(--transition-all);
}

.btn-glass:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 霓虹按钮 */
.btn-neon {
  background: transparent;
  border: 2rpx solid var(--primary);
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--primary);
  position: relative;
  transition: var(--transition-all);
  overflow: hidden;
}

.btn-neon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
  z-index: -1;
}

.btn-neon:active {
  color: var(--text-inverse);
  box-shadow: 0 0 20rpx var(--primary);
}

.btn-neon:active::before {
  transform: scaleX(1);
}

/* 浮动操作按钮 */
.fab {
  width: 96rpx;
  height: 96rpx;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-light) 100%);
  box-shadow: var(--shadow-lg);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: var(--space-3xl);
  right: var(--space-2xl);
  z-index: 1000;
  transition: var(--transition-all);
}

.fab:active {
  transform: scale(0.9);
  box-shadow: var(--shadow-md);
}

.fab-icon {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* ==================== 高级输入框组件 ==================== */

/* 浮动标签输入框 */
.input-float-label {
  position: relative;
  margin-bottom: var(--space-xl);
}

.input-float-label input {
  width: 100%;
  padding: var(--space-lg) var(--space-lg) var(--space-lg) var(--space-lg);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: var(--transition-all);
}

.input-float-label input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 4rpx var(--primary-subtle);
}

.input-float-label label {
  position: absolute;
  left: var(--space-lg);
  top: var(--space-lg);
  font-size: var(--text-base);
  color: var(--text-tertiary);
  pointer-events: none;
  transition: var(--transition-all);
  background-color: var(--bg-primary);
  padding: 0 var(--space-xs);
}

.input-float-label input:focus + label,
.input-float-label input.has-value + label {
  top: -12rpx;
  font-size: var(--text-sm);
  color: var(--primary);
  font-weight: var(--font-medium);
}

/* 图标输入框 */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon input {
  flex: 1;
  padding: var(--space-lg) var(--space-lg) var(--space-lg) 80rpx;
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: var(--transition-all);
}

.input-with-icon input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 4rpx var(--primary-subtle);
}

.input-icon {
  position: absolute;
  left: var(--space-lg);
  width: 40rpx;
  height: 40rpx;
  z-index: 1;
  opacity: 0.6;
}

/* ==================== 加载和状态组件 ==================== */

/* 骨架屏 */
.skeleton {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--border-light) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 24rpx;
  margin-bottom: var(--space-sm);
}

.skeleton-text:last-child {
  width: 60%;
  margin-bottom: 0;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-full);
}

.skeleton-card {
  height: 200rpx;
  margin-bottom: var(--space-lg);
}

/* 脉冲加载 */
.pulse-loader {
  display: inline-flex;
  gap: var(--space-xs);
  align-items: center;
}

.pulse-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: var(--radius-full);
  background: var(--primary);
  animation: pulse 1.4s infinite ease-in-out both;
}

.pulse-dot:nth-child(1) { animation-delay: -0.32s; }
.pulse-dot:nth-child(2) { animation-delay: -0.16s; }
.pulse-dot:nth-child(3) { animation-delay: 0; }

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ==================== 模态框和弹窗组件 ==================== */

/* 模态框背景 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  animation: modal-fade-in 0.3s ease forwards;
}

@keyframes modal-fade-in {
  to { opacity: 1; }
}

/* 模态框内容 */
.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin: var(--space-lg);
  max-width: 600rpx;
  width: 100%;
  box-shadow: var(--shadow-lg);
  transform: scale(0.9) translateY(50rpx);
  animation: modal-slide-up 0.3s ease forwards;
}

@keyframes modal-slide-up {
  to {
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xl);
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: var(--radius-full);
  background: var(--bg-secondary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  transition: var(--transition-all);
}

.modal-close:active {
  background: var(--border-light);
  transform: scale(0.9);
}

/* 提示框 */
.toast {
  position: fixed;
  top: var(--space-3xl);
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  z-index: 1001;
  opacity: 0;
  animation: toast-show 3s ease forwards;
}

@keyframes toast-show {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
  10%, 90% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
}

.toast.success {
  background: var(--success);
  color: var(--text-inverse);
}

.toast.error {
  background: var(--error);
  color: var(--text-inverse);
}

.toast.warning {
  background: var(--warning);
  color: var(--text-inverse);
}

/* ==================== 特殊效果组件 ==================== */

/* 发光效果 */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-effect:active::before {
  opacity: 0.7;
  animation: glow-pulse 1s ease infinite alternate;
}

@keyframes glow-pulse {
  0% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 3D翻转效果 */
.flip-card {
  perspective: 1000rpx;
  width: 100%;
  height: 200rpx;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flip-card:active .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
}

.flip-card-front {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
}

.flip-card-back {
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-light) 100%);
  color: var(--text-inverse);
  transform: rotateY(180deg);
}