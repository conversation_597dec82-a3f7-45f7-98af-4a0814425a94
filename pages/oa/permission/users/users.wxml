<!-- pages/oa/permission/users/users.wxml -->

<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">用户权限管理</text>
    <text class="page-subtitle">管理用户角色分配和权限设置</text>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <oa-data-card
      title="总用户数"
      value="{{statistics.total}}"
      icon="👤"
      iconColor="#007AFF"
      size="small"
    />
    <oa-data-card
      title="启用中"
      value="{{statistics.active}}"
      icon="正常"
      iconColor="#00A86B"
      size="small"
    />
    <oa-data-card
      title="已停用"
      value="{{statistics.inactive}}"
      icon="停用"
      iconColor="#8E8E93"
      size="small"
    />
    <oa-data-card
      title="已分配角色"
      value="{{statistics.hasRoles}}"
      icon="角色"
      iconColor="#FF9500"
      size="small"
    />
  </view>

  <!-- 搜索和筛选 -->
  <view class="search-section">
    <view class="search-bar">
      <input 
        class="search-input"
        placeholder="搜索用户..."
        value="{{filters.keyword}}"
        bindinput="onSearchInput"
      />
      <button class="filter-btn" bindtap="toggleFilters">
        筛选 {{showFilters ? '▲' : '▼'}}
      </button>
    </view>
    
    <!-- 筛选面板 -->
    <view wx:if="{{showFilters}}" class="filter-panel">
      <view class="filter-row">
        <text class="filter-label">部门</text>
        <picker 
          mode="selector" 
          range="{{departmentOptions}}" 
          range-key="label"
          value="{{departmentIndex}}"
          bindchange="onDepartmentFilter"
        >
          <view class="filter-picker">
            {{selectedDepartmentLabel}}
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="filter-row">
        <text class="filter-label">角色</text>
        <picker 
          mode="selector" 
          range="{{roleOptions}}" 
          range-key="label"
          value="{{roleIndex}}"
          bindchange="onRoleFilter"
        >
          <view class="filter-picker">
            {{selectedRoleLabel}}
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="filter-row">
        <text class="filter-label">状态</text>
        <picker 
          mode="selector" 
          range="{{statusOptions}}" 
          range-key="label"
          value="{{statusIndex}}"
          bindchange="onStatusFilter"
        >
          <view class="filter-picker">
            {{selectedStatusLabel}}
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="filter-actions">
        <button class="clear-btn" bindtap="clearFilters">清除筛选</button>
      </view>
    </view>
  </view>

  <!-- 用户列表 -->
  <view class="list-section">
    <!-- 加载状态 -->
    <view wx:if="{{refreshing}}" class="loading-state">
      <text>加载中...</text>
    </view>

    <!-- 空数据状态 -->
    <view wx:elif="{{users.length === 0 && !loading}}" class="empty-state">
      <text class="empty-icon">👤</text>
      <text class="empty-text">暂无用户</text>
    </view>

    <!-- 用户列表 -->
    <view wx:else class="user-list">
      <view 
        wx:for="{{users}}" 
        wx:key="id"
        class="user-item"
      >
        <view class="item-header">
          <view class="user-avatar">
            <image wx:if="{{item.avatar}}" src="{{item.avatar}}" class="avatar-img" />
            <text wx:else class="avatar-text">{{item.username.charAt(0).toUpperCase()}}</text>
          </view>
          <view class="user-info">
            <text class="user-name">{{item.username}}</text>
            <text class="user-email">{{item.email}}</text>
          </view>
          <view class="user-status">
            <oa-status-tag
              text="{{item.statusLabel}}"
              color="{{item.statusColor}}"
              size="small"
            />
          </view>
        </view>
        
        <view class="item-content">
          <view class="info-row">
            <text class="info-label">部门：</text>
            <text class="info-value">{{item.department_name || '未分配'}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">角色：</text>
            <text class="info-value">{{item.rolesText}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">创建时间：</text>
            <text class="info-value">{{item.createdTimeAgo}}</text>
          </view>
        </view>
        
        <view class="item-actions">
          <button 
            class="action-btn assign"
            data-user="{{item}}"
            bindtap="onAssignRoles"
          >
            分配角色
          </button>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view wx:if="{{loading}}" class="loading-more">
        <text>加载更多...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view wx:if="{{!pagination.hasMore && users.length > 0}}" class="no-more">
        <text>没有更多数据了</text>
      </view>
    </view>
  </view>

  <!-- 角色分配模态框 -->
  <view wx:if="{{showRoleModal}}" class="modal-overlay" bindtap="hideRoleModal">
    <view class="role-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">分配角色 - {{currentUser.username}}</text>
        <button class="modal-close" bindtap="hideRoleModal">×</button>
      </view>
      
      <view class="modal-content">
        <view class="role-list">
          <view 
            wx:for="{{allRoles}}" 
            wx:key="id"
            class="role-item"
            data-role-id="{{item.id}}"
            bindtap="onRoleToggle"
          >
            <checkbox checked="{{userRoles.includes(item.id)}}" />
            <view class="role-info">
              <text class="role-name">{{item.name}}</text>
              <text class="role-desc">{{item.description}}</text>
              <view class="role-tags">
                <oa-status-tag
                  text="{{item.level}}级"
                  type="info"
                  size="mini"
                />
                <oa-status-tag
                  text="{{item.data_scope === 'all' ? '全部数据' : item.data_scope === 'department' ? '部门数据' : '个人数据'}}"
                  type="secondary"
                  size="mini"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="hideRoleModal">取消</button>
        <button 
          class="modal-btn confirm"
          loading="{{submitting}}"
          bindtap="saveUserRoles"
        >
          保存
        </button>
      </view>
    </view>
  </view>
</view>
