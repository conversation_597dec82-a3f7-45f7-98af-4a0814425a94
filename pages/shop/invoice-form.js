// pages/shop/invoice-form.js
Page({
  data: {
    type: 'personal', // personal 或 company
    formData: {
      personal: {
        email: ''
      },
      company: {
        title: '',
        taxNumber: '',
        address: '',
        phone: '',
        bank: '',
        account: '',
        email: ''
      }
    }
  },

  onLoad(options) {
    const type = options.type || 'personal';
    this.setData({
      type: type
    });
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: type === 'personal' ? '个人发票信息' : '企业发票信息'
    });
  },

  // 输入框内容变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const { type } = this.data;
    
    this.setData({
      [`formData.${type}.${field}`]: value
    });
  },

  // 表单验证
  validateForm() {
    const { type, formData } = this.data;
    const data = formData[type];
    
    if (type === 'personal') {
      if (!data.email) {
        wx.showToast({
          title: '请输入邮箱地址',
          icon: 'none'
        });
        return false;
      }
      
      // 简单的邮箱格式验证
      const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailReg.test(data.email)) {
        wx.showToast({
          title: '请输入正确的邮箱格式',
          icon: 'none'
        });
        return false;
      }
    } else if (type === 'company') {
      if (!data.title) {
        wx.showToast({
          title: '请输入公司名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!data.taxNumber) {
        wx.showToast({
          title: '请输入税号',
          icon: 'none'
        });
        return false;
      }
      
      if (!data.email) {
        wx.showToast({
          title: '请输入邮箱地址',
          icon: 'none'
        });
        return false;
      }
      
      // 简单的邮箱格式验证
      const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailReg.test(data.email)) {
        wx.showToast({
          title: '请输入正确的邮箱格式',
          icon: 'none'
        });
        return false;
      }
    }
    
    return true;
  },

  // 保存发票信息
  onSave() {
    if (!this.validateForm()) {
      return;
    }
    
    const { type, formData } = this.data;
    
    // 保存到本地存储
    wx.setStorageSync('invoiceInfo', {
      type: type,
      data: formData[type]
    });
    
    wx.showToast({
      title: '发票信息已保存',
      icon: 'success'
    });
    
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
});