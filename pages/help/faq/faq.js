// pages/help/faq/faq.js
Page({
  data: {
    // 搜索关键词
    searchKeyword: '',
    selectedTag: '',
    activeCategory: 'all',
    
    // 分类
    categories: [
      { key: 'all', name: '全部' },
      { key: 'account', name: '账户' },
      { key: 'health', name: '健康' },
      { key: 'production', name: '生产' },
      { key: 'system', name: '系统' },
      { key: 'technical', name: '技术' }
    ],
    
    // 热门标签
    hotTags: [
      '登录问题', 'AI诊断', '数据同步', '环境监控', 
      '健康记录', '设备连接', '数据导出', '密码重置'
    ],
    
    // FAQ数据
    allQuestions: [
      {
        id: 1,
        question: '如何重置登录密码？',
        answer: '您可以通过以下方式重置密码：\n1. 在登录页面点击"忘记密码"\n2. 输入注册时的手机号码\n3. 获取验证码并设置新密码\n4. 使用新密码登录\n\n如果手机号码已更换，请联系客服处理。',
        category: 'account',
        categoryName: '账户问题',
        helpful: 45,
        unhelpful: 2,
        views: 580,
        tags: ['登录问题', '密码重置'],
        images: [],
        relatedLinks: [
          { id: 101, title: '如何注册账户' },
          { id: 103, title: '如何修改个人信息' }
        ],
        expanded: false,
        userRating: null
      },
      {
        id: 2,
        question: 'AI诊断的准确率如何？',
        answer: '我们的AI诊断系统具有以下特点：\n\n准确率：基于大量养鹅数据训练，准确率达到95%以上\n识别范围：可识别常见的20多种鹅类疾病\n响应速度：通常在3-5秒内给出诊断结果\n\n重要提醒：AI诊断仅供参考，建议结合专业兽医意见进行最终诊断和治疗。',
        category: 'health',
        categoryName: '健康管理',
        helpful: 38,
        unhelpful: 5,
        views: 425,
        tags: ['AI诊断', '健康管理'],
        images: ['/images/help/ai-diagnosis-demo.png'],
        relatedLinks: [
          { id: 202, title: 'AI诊断使用说明' },
          { id: 203, title: '健康报告解读' }
        ],
        expanded: false,
        userRating: null
      },
      {
        id: 3,
        question: '数据同步失败怎么办？',
        answer: '数据同步失败的常见原因及解决方法：\n\n1. 网络问题\n   - 检查网络连接是否正常\n   - 尝试切换网络或重启路由器\n\n2. 设备问题\n   - 重启应用程序\n   - 清除应用缓存\n   - 更新到最新版本\n\n3. 服务器问题\n   - 稍后重试\n   - 联系技术支持\n\n如果问题持续存在，请提供错误截图联系客服。',
        category: 'technical',
        categoryName: '技术问题',
        helpful: 32,
        unhelpful: 1,
        views: 289,
        tags: ['数据同步', '技术问题'],
        images: [],
        relatedLinks: [
          { id: 501, title: '数据同步失败怎么办' },
          { id: 502, title: '登录异常处理' }
        ],
        expanded: false,
        userRating: null
      },
      {
        id: 4,
        question: '环境监控数据异常如何处理？',
        answer: '环境数据异常处理步骤：\n\n1. 确认异常类型\n   - 温度异常：检查温控设备\n   - 湿度异常：检查通风系统\n   - 光照异常：检查照明设备\n\n2. 设备检查\n   - 传感器是否正常工作\n   - 设备连接是否稳定\n   - 设备是否需要校准\n\n3. 环境调节\n   - 及时调整环境参数\n   - 记录处理措施\n   - 观察鹅群反应\n\n系统会在检测到异常时自动发送预警通知。',
        category: 'production',
        categoryName: '生产管理',
        helpful: 28,
        unhelpful: 0,
        views: 345,
        tags: ['环境监控', '生产管理'],
        images: ['/images/help/environment-monitoring.png'],
        relatedLinks: [
          { id: 301, title: '环境监控数据说明' }
        ],
        expanded: false,
        userRating: null
      },
      {
        id: 5,
        question: '如何导出生产数据？',
        answer: '生产数据导出操作指南：\n\n1. 进入数据管理模块\n2. 选择要导出的数据类型\n3. 设置时间范围\n4. 选择导出格式（Excel/CSV）\n5. 点击"导出"按钮\n6. 等待生成完成后下载\n\n支持导出的数据类型：\n- 健康记录\n- 环境数据\n- 生产记录\n- 财务数据\n\n导出的文件会保存在手机的下载文件夹中。',
        category: 'system',
        categoryName: '系统功能',
        helpful: 25,
        unhelpful: 2,
        views: 198,
        tags: ['数据导出', '系统功能'],
        images: [],
        relatedLinks: [
          { id: 304, title: '生产报表导出' }
        ],
        expanded: false,
        userRating: null
      }
    ],
    
    filteredQuestions: [],
    totalQuestions: 0,
    
    // 统计数据
    faqStats: {
      totalQuestions: 45,
      totalViews: 8520,
      resolvedRate: 96,
      avgResponseTime: 2
    }
  },

  onLoad: function (options) {
    // 初始化数据
    this.setData({
      filteredQuestions: this.data.allQuestions,
      totalQuestions: this.data.allQuestions.length
    });
    
    // 检查用户评价状态
    this.checkUserRatings();
  },

  onShow: function () {
    // 页面显示
  },

  // 检查用户评价状态
  checkUserRatings: function() {
    const ratings = wx.getStorageSync('faqRatings') || {};
    const allQuestions = this.data.allQuestions.map(item => ({
      ...item,
      userRating: ratings[item.id] || null
    }));
    
    this.setData({
      allQuestions: allQuestions,
      filteredQuestions: allQuestions
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    this.filterQuestions();
  },

  // 执行搜索
  onSearch: function() {
    this.filterQuestions();
  },

  // 清除搜索
  onClearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    this.filterQuestions();
  },

  // 选择标签
  onSelectTag: function(e) {
    const { tag } = e.currentTarget.dataset;
    const selectedTag = this.data.selectedTag === tag ? '' : tag;
    
    this.setData({
      selectedTag: selectedTag,
      searchKeyword: selectedTag
    });
    
    this.filterQuestions();
  },

  // 选择分类
  onSelectCategory: function(e) {
    const { category } = e.currentTarget.dataset;
    this.setData({
      activeCategory: category
    });
    
    this.filterQuestions();
  },

  // 筛选问题
  filterQuestions: function() {
    const { searchKeyword, activeCategory, allQuestions } = this.data;
    
    let filtered = [...allQuestions];
    
    // 分类筛选
    if (activeCategory !== 'all') {
      filtered = filtered.filter(item => item.category === activeCategory);
    }
    
    // 关键词筛选
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(item => 
        item.question.toLowerCase().includes(keyword) ||
        item.answer.toLowerCase().includes(keyword) ||
        item.tags.some(tag => tag.toLowerCase().includes(keyword))
      );
    }
    
    this.setData({
      filteredQuestions: filtered
    });
  },

  // 获取分类名称
  getCategoryName: function(categoryKey) {
    const category = this.data.categories.find(item => item.key === categoryKey);
    return category ? category.name : '全部';
  },

  // 展开/折叠FAQ
  onToggleFAQ: function(e) {
    const { index } = e.currentTarget.dataset;
    const filteredQuestions = this.data.filteredQuestions;
    
    filteredQuestions[index].expanded = !filteredQuestions[index].expanded;
    
    // 增加浏览量
    if (filteredQuestions[index].expanded) {
      filteredQuestions[index].views += 1;
    }
    
    this.setData({
      filteredQuestions: filteredQuestions
    });
  },

  // 预览图片
  onPreviewImage: function(e) {
    const { src } = e.currentTarget.dataset;
    
    wx.previewImage({
      current: src,
      urls: [src]
    });
  },

  // 查看相关文章
  onViewArticle: function(e) {
    const { article } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/help/article/article?id=${article.id}&title=${encodeURIComponent(article.title)}`,
      fail: () => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    });
  },

  // 标记有帮助/没帮助
  onMarkHelpful: function(e) {
    const { id, helpful } = e.currentTarget.dataset;
    const isHelpful = helpful === 'true';
    
    // 找到对应的问题
    const filteredQuestions = this.data.filteredQuestions;
    const questionIndex = filteredQuestions.findIndex(item => item.id == id);
    
    if (questionIndex === -1) return;
    
    const question = filteredQuestions[questionIndex];
    
    // 检查是否已经评价过
    if (question.userRating !== null) {
      wx.showToast({
        title: '您已经评价过了',
        icon: 'none'
      });
      return;
    }
    
    // 更新评价数据
    if (isHelpful) {
      question.helpful += 1;
    } else {
      question.unhelpful += 1;
    }
    question.userRating = isHelpful;
    
    this.setData({
      filteredQuestions: filteredQuestions
    });
    
    // 保存评价状态
    let ratings = wx.getStorageSync('faqRatings') || {};
    ratings[id] = isHelpful;
    wx.setStorageSync('faqRatings', ratings);
    
    // 显示反馈
    wx.showToast({
      title: isHelpful ? '感谢您的反馈' : '我们会持续改进',
      icon: 'success'
    });
  },

  // 提问题
  onAskQuestion: function() {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback',
      fail: () => {
        wx.showModal({
          title: '提交问题',
          content: '请通过意见反馈功能提交您的问题，我们会尽快回复。',
          showCancel: false
        });
      }
    });
  },

  // 联系客服
  onContactService: function() {
    wx.showModal({
      title: '联系客服',
      content: '客服热线：************\n工作时间：周一至周五 9:00-18:00\n\n是否拨打客服电话？',
      confirmText: '拨打',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '智慧养鹅 - 常见问题',
      desc: '快速找到您遇到问题的解决方案',
      path: '/pages/help/faq/faq'
    };
  }
});