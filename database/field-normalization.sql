-- 智慧养鹅SAAS平台 - 数据库字段命名规范化脚本
-- 将所有表的字段统一为下划线命名规范
-- 执行前请务必备份数据库

-- ========================================
-- 主要表结构规范化
-- ========================================

-- 1. 用户表字段规范化
ALTER TABLE users 
  CHANGE userId user_id INT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  CHANGE farmName farm_name VARCHAR(100) COMMENT '养殖场名称',
  CHANGE roleCode role_code VARCHAR(20) DEFAULT 'employee' COMMENT '角色代码',
  CHANGE isActive is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  CHANGE createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  CHANGE updatedAt updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 2. 鹅群表字段规范化  
ALTER TABLE flocks
  CHANGE flockId flock_id INT NOT NULL AUTO_INCREMENT COMMENT '鹅群ID',
  CHANGE userId user_id INT NOT NULL COMMENT '用户ID',
  CHANGE flockName flock_name VARCHAR(100) NOT NULL COMMENT '鹅群名称',
  CHANGE totalCount total_count INT NOT NULL DEFAULT 0 COMMENT '总数量',
  CHANGE healthyCount healthy_count INT NOT NULL DEFAULT 0 COMMENT '健康数量',
  CHANGE sickCount sick_count INT NOT NULL DEFAULT 0 COMMENT '患病数量',
  CHANGE deathCount death_count INT NOT NULL DEFAULT 0 COMMENT '死亡数量',
  CHANGE lastCheckDate last_check_date DATE COMMENT '最后检查日期',
  CHANGE createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  CHANGE updatedAt updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 3. 健康记录表字段规范化
ALTER TABLE health_records
  CHANGE recordId record_id INT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  CHANGE userId user_id INT NOT NULL COMMENT '用户ID',
  CHANGE flockId flock_id INT COMMENT '鹅群ID',
  CHANGE gooseId goose_id VARCHAR(50) COMMENT '鹅只ID',
  CHANGE healthStatus health_status ENUM('healthy', 'sick', 'dead', 'recovered') NOT NULL COMMENT '健康状态',
  CHANGE checkDate check_date DATE NOT NULL COMMENT '检查日期',
  CHANGE symptomsDescription symptoms_description TEXT COMMENT '症状描述',
  CHANGE treatmentPlan treatment_plan TEXT COMMENT '治疗方案',
  CHANGE medicineUsed medicine_used VARCHAR(200) COMMENT '使用药物',
  CHANGE isResolved is_resolved BOOLEAN DEFAULT FALSE COMMENT '是否已解决',
  CHANGE createdBy created_by INT COMMENT '创建人ID',
  CHANGE createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  CHANGE updatedAt updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 4. 生产记录表字段规范化
ALTER TABLE production_records
  CHANGE recordId record_id INT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  CHANGE userId user_id INT NOT NULL COMMENT '用户ID',
  CHANGE flockId flock_id INT COMMENT '鹅群ID',
  CHANGE recordType record_type ENUM('入栏', '出栏', '称重', '生长') NOT NULL COMMENT '记录类型',
  CHANGE recordDate record_date DATE NOT NULL COMMENT '记录日期',
  CHANGE batchNumber batch_number VARCHAR(50) COMMENT '批次号',
  CHANGE quantity quantity INT COMMENT '数量',
  CHANGE averageWeight average_weight DECIMAL(8,2) COMMENT '平均体重(kg)',
  CHANGE totalCost total_cost DECIMAL(10,2) COMMENT '总成本',
  CHANGE unitPrice unit_price DECIMAL(8,2) COMMENT '单价',
  CHANGE notes notes TEXT COMMENT '备注',
  CHANGE createdBy created_by INT COMMENT '创建人ID',
  CHANGE createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  CHANGE updatedAt updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 5. 统一库存表字段规范化（推荐使用）
ALTER TABLE unified_inventory
  CHANGE inventoryId inventory_id INT NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  CHANGE userId user_id INT NOT NULL COMMENT '用户ID',
  CHANGE tenantId tenant_id VARCHAR(50) COMMENT '租户ID',
  CHANGE materialName material_name VARCHAR(100) NOT NULL COMMENT '物料名称',
  CHANGE category category VARCHAR(50) NOT NULL COMMENT '分类',
  CHANGE currentStock current_stock DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '当前库存',
  CHANGE unit unit VARCHAR(20) NOT NULL DEFAULT '个' COMMENT '单位',
  CHANGE unitPrice unit_price DECIMAL(8,2) COMMENT '单价',
  CHANGE totalValue total_value DECIMAL(10,2) COMMENT '总价值',
  CHANGE minStock min_stock DECIMAL(10,2) DEFAULT 0 COMMENT '最小库存',
  CHANGE maxStock max_stock DECIMAL(10,2) DEFAULT 999999 COMMENT '最大库存',
  CHANGE supplier supplier VARCHAR(100) COMMENT '供应商',
  CHANGE storageLocation storage_location VARCHAR(100) COMMENT '存储位置',
  CHANGE expiryDate expiry_date DATE COMMENT '过期日期',
  CHANGE batchNumber batch_number VARCHAR(50) COMMENT '批次号',
  CHANGE status status ENUM('active', 'inactive', 'expired') DEFAULT 'active' COMMENT '状态',
  CHANGE lastUpdatedBy last_updated_by INT COMMENT '最后更新人',
  CHANGE createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  CHANGE updatedAt updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 6. OA审批记录表字段规范化
ALTER TABLE oa_approvals
  CHANGE approvalId approval_id INT NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  CHANGE tenantId tenant_id VARCHAR(50) NOT NULL COMMENT '租户ID',
  CHANGE businessId business_id INT NOT NULL COMMENT '业务ID',
  CHANGE businessType business_type ENUM('leave', 'purchase', 'reimbursement', 'other') NOT NULL COMMENT '业务类型',
  CHANGE applicantId applicant_id INT NOT NULL COMMENT '申请人ID',
  CHANGE approverId approver_id INT COMMENT '审批人ID',
  CHANGE approvalLevel approval_level INT DEFAULT 1 COMMENT '审批层级',
  CHANGE status status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending' COMMENT '状态',
  CHANGE submittedAt submitted_at TIMESTAMP COMMENT '提交时间',
  CHANGE processedAt processed_at TIMESTAMP COMMENT '处理时间',
  CHANGE approvalComments approval_comments TEXT COMMENT '审批意见',
  CHANGE attachments attachments JSON COMMENT '附件信息',
  CHANGE priority priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
  CHANGE createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  CHANGE updatedAt updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 7. 系统日志表字段规范化
ALTER TABLE system_logs
  CHANGE logId log_id INT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  CHANGE userId user_id INT COMMENT '用户ID',
  CHANGE tenantId tenant_id VARCHAR(50) COMMENT '租户ID',
  CHANGE actionType action_type VARCHAR(50) NOT NULL COMMENT '操作类型',
  CHANGE resourceType resource_type VARCHAR(50) COMMENT '资源类型',
  CHANGE resourceId resource_id VARCHAR(50) COMMENT '资源ID',
  CHANGE actionDescription action_description TEXT COMMENT '操作描述',
  CHANGE ipAddress ip_address VARCHAR(45) COMMENT 'IP地址',
  CHANGE userAgent user_agent TEXT COMMENT '用户代理',
  CHANGE requestData request_data JSON COMMENT '请求数据',
  CHANGE responseData response_data JSON COMMENT '响应数据',
  CHANGE executionTime execution_time INT COMMENT '执行时间(ms)',
  CHANGE logLevel log_level ENUM('debug', 'info', 'warn', 'error') DEFAULT 'info' COMMENT '日志级别',
  CHANGE createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- ========================================
-- 索引优化
-- ========================================

-- 用户表索引
CREATE INDEX idx_users_role_code ON users(role_code);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_is_active ON users(is_active);

-- 鹅群表索引
CREATE INDEX idx_flocks_user_id ON flocks(user_id);
CREATE INDEX idx_flocks_created_at ON flocks(created_at);

-- 健康记录表索引
CREATE INDEX idx_health_records_user_id ON health_records(user_id);
CREATE INDEX idx_health_records_flock_id ON health_records(flock_id);
CREATE INDEX idx_health_records_check_date ON health_records(check_date);
CREATE INDEX idx_health_records_health_status ON health_records(health_status);

-- 生产记录表索引
CREATE INDEX idx_production_records_user_id ON production_records(user_id);
CREATE INDEX idx_production_records_flock_id ON production_records(flock_id);
CREATE INDEX idx_production_records_record_date ON production_records(record_date);
CREATE INDEX idx_production_records_record_type ON production_records(record_type);
CREATE INDEX idx_production_records_batch_number ON production_records(batch_number);

-- 统一库存表索引
CREATE INDEX idx_unified_inventory_user_id ON unified_inventory(user_id);
CREATE INDEX idx_unified_inventory_tenant_id ON unified_inventory(tenant_id);
CREATE INDEX idx_unified_inventory_category ON unified_inventory(category);
CREATE INDEX idx_unified_inventory_status ON unified_inventory(status);
CREATE INDEX idx_unified_inventory_expiry_date ON unified_inventory(expiry_date);

-- OA审批记录索引
CREATE INDEX idx_oa_approvals_tenant_id ON oa_approvals(tenant_id);
CREATE INDEX idx_oa_approvals_business_type ON oa_approvals(business_type);
CREATE INDEX idx_oa_approvals_applicant_id ON oa_approvals(applicant_id);
CREATE INDEX idx_oa_approvals_approver_id ON oa_approvals(approver_id);
CREATE INDEX idx_oa_approvals_status ON oa_approvals(status);
CREATE INDEX idx_oa_approvals_submitted_at ON oa_approvals(submitted_at);

-- 系统日志表索引
CREATE INDEX idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_system_logs_tenant_id ON system_logs(tenant_id);
CREATE INDEX idx_system_logs_action_type ON system_logs(action_type);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX idx_system_logs_log_level ON system_logs(log_level);

-- ========================================
-- 外键约束
-- ========================================

-- 鹅群表外键
ALTER TABLE flocks ADD CONSTRAINT fk_flocks_user_id 
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

-- 健康记录表外键
ALTER TABLE health_records ADD CONSTRAINT fk_health_records_user_id 
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;
ALTER TABLE health_records ADD CONSTRAINT fk_health_records_flock_id 
  FOREIGN KEY (flock_id) REFERENCES flocks(flock_id) ON DELETE SET NULL;

-- 生产记录表外键
ALTER TABLE production_records ADD CONSTRAINT fk_production_records_user_id 
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;
ALTER TABLE production_records ADD CONSTRAINT fk_production_records_flock_id 
  FOREIGN KEY (flock_id) REFERENCES flocks(flock_id) ON DELETE SET NULL;

-- 统一库存表外键
ALTER TABLE unified_inventory ADD CONSTRAINT fk_unified_inventory_user_id 
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

-- ========================================
-- 数据完整性检查
-- ========================================

-- 检查字段规范化是否成功
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'smart_goose_platform' 
  AND TABLE_NAME IN ('users', 'flocks', 'health_records', 'production_records', 'unified_inventory', 'oa_approvals', 'system_logs')
ORDER BY TABLE_NAME, ORDINAL_POSITION;

-- 验证索引创建
SHOW INDEX FROM users;
SHOW INDEX FROM flocks;
SHOW INDEX FROM health_records;
SHOW INDEX FROM production_records;
SHOW INDEX FROM unified_inventory;
SHOW INDEX FROM oa_approvals;
SHOW INDEX FROM system_logs;

-- ========================================
-- 备注
-- ========================================
-- 1. 执行此脚本前请备份数据库
-- 2. 在测试环境先验证脚本正确性
-- 3. 更新后端模型文件中的field映射
-- 4. 更新API接口的字段引用
-- 5. 测试所有相关功能正常运行