/* components/lazy-list/lazy-list.wxss */

.lazy-list {
  width: 100%;
  position: relative;
}

.virtual-container,
.normal-container {
  width: 100%;
  position: relative;
}

.list-item {
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.virtual-item {
  position: relative;
  overflow: hidden;
}

.virtual-placeholder {
  width: 100%;
  pointer-events: none;
}

/* 加载状态 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
  background-color: transparent;
}

/* 无更多数据提示 */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
  background-color: transparent;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
}

/* 空状态 */
.empty-container {
  width: 100%;
  min-height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 性能优化 */
.list-item {
  /* 小程序中使用transform来优化渲染性能 */
  transform: translateZ(0); /* 开启硬件加速 */
  backface-visibility: hidden;
}

.virtual-item {
  will-change: transform; /* 提示浏览器优化 */
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .list-item {
    padding: 0 20rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .no-more-text {
    background-color: #333;
    color: #ccc;
  }
}