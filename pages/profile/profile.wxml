<!-- pages/profile/profile.wxml -->
<view class="profile-container">
  <!-- 用户信息 -->
  <view class="user-info-banner">
    <view class="user-info-content" bindtap="onEditProfile">
      <view class="user-section">
        <view class="avatar">
          <image src="{{userInfo.avatar}}" mode="aspectFill" lazy-load="{{true}}"></image>
        </view>
        <view class="user-details">
          <view class="user-name-line">
            <text class="username">{{userInfo.name}}</text>
            <view class="user-role">{{userInfo.role}}</view>
          </view>
          <view class="farm-name-line">
            <text class="farm-name">{{userInfo.farmName}}</text>
          </view>
        </view>
      </view>
      <image class="edit-icon" src="/images/icons/edit.png" mode="aspectFit" lazy-load="{{true}}"></image>
    </view>
  </view>
  
  <!-- 订单快捷入口 -->
  <c-card padding="md" margin="md">
    <c-section-header 
      title="我的订单" 
      showMore="{{true}}"
      bind:moretap="onViewAllOrders">
    </c-section-header>
    <view class="order-status-grid">
      <view class="order-status-item" bindtap="onOrderStatus" data-status="pending">
        <image class="status-icon" src="/images/icons/clock.svg" mode="aspectFit" lazy-load="{{true}}"></image>
        <text class="status-text">待付款</text>
        <text class="status-count" wx:if="{{orderCounts.pending > 0}}">{{orderCounts.pending}}</text>
      </view>
      <view class="order-status-item" bindtap="onOrderStatus" data-status="paid">
        <image class="status-icon" src="/images/icons/package.svg" mode="aspectFit" lazy-load="{{true}}"></image>
        <text class="status-text">待发货</text>
        <text class="status-count" wx:if="{{orderCounts.paid > 0}}">{{orderCounts.paid}}</text>
      </view>
      <view class="order-status-item" bindtap="onOrderStatus" data-status="shipped">
        <image class="status-icon" src="/images/icons/truck.svg" mode="aspectFit" lazy-load="{{true}}"></image>
        <text class="status-text">待收货</text>
        <text class="status-count" wx:if="{{orderCounts.shipped > 0}}">{{orderCounts.shipped}}</text>
      </view>
      <view class="order-status-item" bindtap="onOrderStatus" data-status="completed">
        <image class="status-icon" src="/images/icons/check-circle.svg" mode="aspectFit" lazy-load="{{true}}"></image>
        <text class="status-text">已完成</text>
        <text class="status-count" wx:if="{{orderCounts.completed > 0}}">{{orderCounts.completed}}</text>
      </view>
    </view>
  </c-card>

  <!-- OA办公模块 -->
  <c-card padding="md" margin="md">
    <c-section-header title="办公管理"></c-section-header>
    <view class="oa-grid">
      <block wx:for="{{oaModules}}" wx:key="id">
        <view class="oa-item" bindtap="onOATap" data-id="{{item.id}}" data-url="{{item.url}}">
          <view class="oa-header">
            <text class="oa-title">{{item.title}}</text>
            <view wx:if="{{item.badge && item.badge > 0}}" class="oa-badge">{{item.badge}}</view>
          </view>
          <text class="oa-description">{{item.description}}</text>
        </view>
      </block>
    </view>
  </c-card>
  
  <!-- 功能服务 -->
  <c-card padding="md" margin="md">
    <c-section-header title="功能服务"></c-section-header>
    <view class="service-grid">
      <block wx:for="{{menuItems}}" wx:key="id">
        <view 
          class="service-item"
          bindtap="onMenuTap"
          data-url="{{item.url}}"
          data-id="{{item.id}}"
          data-requiredrole="{{item.requiredRole ? item.requiredRole.join(',') : ''}}"
        >
          <view class="service-header">
            <text class="service-title">{{item.title}}</text>
            <view wx:if="{{item.badge && item.badge > 0}}" class="service-badge">{{item.badge}}</view>
          </view>
          <text class="service-description">{{item.subtitle}}</text>
        </view>
      </block>
    </view>
  </c-card>
  
  <!-- 退出登录按钮 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>
</view>