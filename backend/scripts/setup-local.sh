#!/bin/bash

# 智慧养鹅本地环境设置脚本
echo "开始设置智慧养鹅本地开发环境..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null
then
    echo "错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null
then
    echo "错误: 未找到npm，请先安装npm"
    exit 1
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 安装后端依赖
echo "安装后端依赖..."
cd /Volumes/DATA/千问/智慧养鹅/backend
npm install

# 检查MySQL是否安装
if ! command -v mysql &> /dev/null
then
    echo "警告: 未找到MySQL客户端，如果使用本地MySQL请先安装"
else
    echo "MySQL版本: $(mysql --version)"
fi

# 创建数据库（如果使用本地MySQL）
echo "请确保已启动MySQL服务并创建数据库..."
echo "可以使用以下SQL语句创建数据库（请根据需要修改密码）:"
echo "CREATE DATABASE IF NOT EXISTS zhihuiyange_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo "CREATE USER IF NOT EXISTS 'root'@'localhost' IDENTIFIED BY 'root123';"
echo "GRANT ALL PRIVILEGES ON zhihuiyange_local.* TO 'root'@'localhost';"
echo "FLUSH PRIVILEGES;"

# 初始化数据库表
echo "初始化数据库表结构..."
# 这里应该运行数据库迁移脚本，暂时留空

# 启动后端服务
echo "本地环境设置完成！"
echo "请执行以下命令启动后端服务:"
echo "cd /Volumes/DATA/千问/智慧养鹅/backend"
echo "export NODE_ENV=local && node app.js"

echo "或者使用npm脚本:"
echo "npm run dev"