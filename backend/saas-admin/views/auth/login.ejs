<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            min-height: 600px;
            display: flex;
        }

        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-brand {
            position: relative;
            z-index: 2;
        }

        .brand-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 24px;
        }

        .brand-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 1rem;
            opacity: 0.9;
        }

        .feature-item i {
            margin-right: 12px;
            font-size: 1.2rem;
        }

        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-form-container {
            max-width: 400px;
            margin: 0 auto;
            width: 100%;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .login-description {
            color: #718096;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #9ca3af;
            z-index: 3;
        }

        .input-group .form-control {
            padding-left: 50px;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 16px 20px;
            margin-bottom: 24px;
        }

        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
        }

        .login-footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }

        .login-footer p {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        .demo-credentials {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .demo-credentials h6 {
            color: #0369a1;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .demo-credentials p {
            color: #0369a1;
            font-size: 0.85rem;
            margin: 4px 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                margin: 20px;
                min-height: auto;
            }

            .login-left {
                padding: 40px 30px;
                text-align: center;
            }

            .login-right {
                padding: 40px 30px;
            }

            .brand-title {
                font-size: 2rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }

        /* 加载动画 */
        .loading {
            display: none;
        }

        .loading .spinner-border {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        .btn-login.loading .btn-text {
            display: none;
        }

        .btn-login.loading .loading {
            display: inline-flex;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 左侧品牌区域 -->
        <div class="login-left">
            <div class="login-brand">
                <div class="brand-logo">
                    <i class="bi bi-building"></i>
                </div>
                <h1 class="brand-title">智慧养鹅<br>SaaS平台</h1>
                <p class="brand-subtitle">
                    企业级多租户养殖管理平台<br>
                    为养殖企业提供专业的数字化解决方案
                </p>
                <ul class="feature-list">
                    <li class="feature-item">
                        <i class="bi bi-shield-check"></i>
                        多租户架构，数据安全隔离
                    </li>
                    <li class="feature-item">
                        <i class="bi bi-graph-up-arrow"></i>
                        实时数据分析与智能决策
                    </li>
                    <li class="feature-item">
                        <i class="bi bi-cloud"></i>
                        云端部署，随时随地访问
                    </li>
                    <li class="feature-item">
                        <i class="bi bi-headset"></i>
                        7×24小时专业技术支持
                    </li>
                </ul>
            </div>
        </div>

        <!-- 右侧登录表单 -->
        <div class="login-right">
            <div class="login-form-container">
                <div class="login-header">
                    <h2 class="login-title">平台管理登录</h2>
                    <p class="login-description">登录SaaS平台管理中心</p>
                </div>

                <!-- 演示账户信息 -->
                <div class="demo-credentials">
                    <h6><i class="bi bi-info-circle"></i> 演示账户</h6>
                    <p><strong>用户名:</strong> platform_admin</p>
                    <p><strong>密码:</strong> admin123</p>
                </div>

                <!-- 错误提示 -->
                <% if (typeof error !== 'undefined' && error) { %>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <% if (error === 'invalid_credentials') { %>
                        用户名或密码错误，请重试
                    <% } else if (error === 'system_error') { %>
                        系统错误，请稍后重试
                    <% } else { %>
                        登录失败，请检查输入信息
                    <% } %>
                </div>
                <% } %>

                <!-- 登录表单 -->
                <form method="POST" action="/saas-admin/login" id="loginForm">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <i class="bi bi-person"></i>
                            用户名
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-person"></i>
                            </span>
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   name="username" 
                                   placeholder="请输入用户名"
                                   value="platform_admin"
                                   required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="bi bi-lock"></i>
                            密码
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock"></i>
                            </span>
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   placeholder="请输入密码"
                                   value="admin123"
                                   required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="btn-text">
                            <i class="bi bi-box-arrow-in-right"></i>
                            登录平台
                        </span>
                        <span class="loading">
                            <span class="spinner-border" role="status"></span>
                            登录中...
                        </span>
                    </button>
                </form>

                <div class="login-footer">
                    <p>
                        <i class="bi bi-shield-lock"></i>
                        您的数据受到企业级安全保护
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;
        });

        // 自动填充演示账户（开发环境）
        document.addEventListener('DOMContentLoaded', function() {
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            
            // 如果是演示环境，自动填充
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                usernameInput.value = 'platform_admin';
                passwordInput.value = 'admin123';
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter 快速登录
            if (e.ctrlKey && e.key === 'Enter') {
                document.getElementById('loginForm').submit();
            }
        });

        // 输入框焦点效果
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
