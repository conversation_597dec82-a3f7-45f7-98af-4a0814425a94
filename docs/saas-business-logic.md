# 智慧养鹅SAAS平台 - 业务逻辑与权限体系

## 🏗️ SAAS架构设计

### 多租户架构
智慧养鹅SAAS平台采用完全隔离的多租户架构，确保企业级数据安全和性能：

```
┌─────────────────────────────────────────────────────────────────┐
│                      SAAS平台管理层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐    │
│  │   租户管理       │  │   订阅计费       │  │   平台监控       │    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
                              │
                ┌─────────────┼─────────────┐
                │             │             │
        ┌───────▼─────┐ ┌─────▼─────┐ ┌─────▼─────┐
        │  租户A      │ │  租户B    │ │  租户C    │
        │ DB_TENANT_A │ │ DB_TENANT_B│ │ DB_TENANT_C│
        └─────────────┘ └───────────┘ └───────────┘
              │               │             │
    ┌─────────▼─────────┐ ┌───▼────┐ ┌─────▼─────┐
    │ 微信小程序      │ │ Web管理 │ │ 移动端应用 │
    │ + Web管理后台   │ │ 后台    │ │          │
    └─────────────────┘ └────────┘ └───────────┘
```

### 数据隔离策略

1. **数据库级隔离**
   - 每个租户拥有独立数据库: `smart_goose_${tenantCode}`
   - 完全的数据隔离，避免数据泄露风险
   - 支持租户自定义备份策略

2. **动态连接管理**
   - 租户数据库连接池管理
   - 自动重连和故障恢复
   - 性能监控和资源限制

## 👥 角色权限体系

### 平台级角色（SAAS管理）

#### 1. 超级管理员 (Super Admin)
- **职责**: 平台整体运营管理
- **权限**:
  - 租户生命周期管理
  - 系统配置和监控
  - 订阅计划管理
  - 财务数据查看
  - 所有租户数据访问（紧急情况）

#### 2. 平台管理员 (Platform Admin)
- **职责**: 日常运营管理
- **权限**:
  - 租户管理（创建、暂停、激活）
  - 订阅计划分配
  - 使用统计查看
  - 客户支持

#### 3. 平台运维 (Platform Operator)
- **职责**: 技术运维和监控
- **权限**:
  - 系统监控和告警
  - 性能数据查看
  - 技术支持
  - 日志查看

#### 4. 客服支持 (Support)
- **职责**: 客户服务和支持
- **权限**:
  - 租户基本信息查看
  - 使用统计查看
  - 创建支持工单

### 租户级角色（企业内部）

#### 1. 企业所有者 (Owner)
- **数量限制**: 每租户1个
- **职责**: 企业账户最高权限
- **权限**:
  - 所有业务功能完整权限
  - 用户管理和角色分配
  - 订阅管理和付费
  - 企业配置和设置
  - 数据导出和备份

#### 2. 企业管理员 (Admin)
- **数量限制**: 根据订阅计划
- **职责**: 日常管理和配置
- **权限**:
  - 业务数据管理（鹅群、健康、生产）
  - 用户管理（除Owner外）
  - 报表生成和导出
  - AI功能使用
  - 系统设置

#### 3. 部门经理 (Manager)
- **数量限制**: 根据订阅计划
- **职责**: 部门业务管理
- **权限**:
  - 分管业务数据管理
  - 部门用户查看
  - 数据录入和修改
  - 基础报表查看
  - AI诊断功能

#### 4. 普通用户 (User)
- **数量限制**: 根据订阅计划
- **职责**: 日常操作员
- **权限**:
  - 数据录入和查看
  - 基础功能使用
  - 个人数据管理

## 📊 订阅计划体系

### 计划层级

| 计划 | 用户数 | 鹅群数 | 存储空间 | API调用/月 | 特色功能 |
|------|--------|--------|----------|------------|----------|
| **试用版** | 3 | 5 | 1GB | 1,000 | 基础管理 |
| **基础版** | 10 | 20 | 5GB | 10,000 | 数据分析 |
| **标准版** | 50 | 100 | 20GB | 50,000 | API访问、高级分析 |
| **高级版** | 200 | 500 | 100GB | 200,000 | 自定义报表、优先支持 |
| **企业版** | 1000 | 2000 | 1TB | 1,000,000 | 全功能、专属支持 |

### 功能权限矩阵

| 功能模块 | 试用版 | 基础版 | 标准版 | 高级版 | 企业版 |
|----------|--------|--------|--------|--------|--------|
| 鹅群管理 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 健康管理 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 生产管理 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 财务管理 | ❌ | ✅ | ✅ | ✅ | ✅ |
| AI诊断 | ❌ | ❌ | ✅ | ✅ | ✅ |
| 数据导出 | ❌ | ✅ | ✅ | ✅ | ✅ |
| API访问 | ❌ | ❌ | ✅ | ✅ | ✅ |
| 自定义报表 | ❌ | ❌ | ❌ | ✅ | ✅ |
| 多端同步 | ❌ | ❌ | ✅ | ✅ | ✅ |
| 优先支持 | ❌ | ❌ | ❌ | ✅ | ✅ |
| 定制开发 | ❌ | ❌ | ❌ | ❌ | ✅ |

## 🔄 业务流程设计

### 租户生命周期管理

#### 1. 租户注册流程
```mermaid
graph TD
    A[企业申请注册] --> B{资质审核}
    B -->|通过| C[创建租户账户]
    B -->|不通过| D[拒绝并通知原因]
    C --> E[分配租户代码]
    E --> F[创建数据库]
    F --> G[初始化数据]
    G --> H[发送激活邮件]
    H --> I[账户激活]
    I --> J[开始试用]
```

#### 2. 订阅管理流程
```mermaid
graph TD
    A[试用期结束] --> B{选择订阅计划}
    B --> C[付费确认]
    C --> D[订阅激活]
    D --> E[资源配额调整]
    E --> F[功能权限更新]
    F --> G[通知租户用户]
    
    H[订阅续费] --> I{付费状态}
    I -->|成功| J[延长订阅期]
    I -->|失败| K[进入宽限期]
    K --> L{宽限期内付费}
    L -->|是| J
    L -->|否| M[账户暂停]
```

#### 3. 数据迁移流程
```mermaid
graph TD
    A[企业申请迁移] --> B[数据备份]
    B --> C[新环境准备]
    C --> D[数据同步]
    D --> E[业务验证]
    E --> F{验证通过}
    F -->|是| G[切换生产环境]
    F -->|否| H[回滚并修复]
    G --> I[旧环境清理]
```

### 用户管理流程

#### 1. 用户邀请机制
```javascript
// 用户邀请流程
const inviteUser = async (tenantId, inviterUserId, inviteData) => {
  // 1. 验证邀请者权限
  const inviter = await getUserById(inviterUserId);
  if (!hasPermission(inviter, PERMISSIONS.USER_CREATE)) {
    throw new Error('无权限邀请用户');
  }
  
  // 2. 检查租户用户配额
  const tenant = await getTenantById(tenantId);
  const currentUserCount = await getUserCountByTenant(tenantId);
  if (currentUserCount >= tenant.maxUsers) {
    throw new Error('用户数已达上限');
  }
  
  // 3. 生成邀请码并发送邮件
  const inviteCode = generateInviteCode();
  await sendInviteEmail(inviteData.email, inviteCode);
  
  // 4. 记录邀请记录
  await createInviteRecord({
    tenantId,
    inviterUserId,
    email: inviteData.email,
    role: inviteData.role,
    inviteCode,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天有效期
  });
};
```

#### 2. 角色权限检查
```javascript
// 权限检查中间件
const requirePermission = (permission) => {
  return async (req, res, next) => {
    const user = req.user;
    const tenant = req.tenant;
    
    // 1. 检查用户角色权限
    if (!hasPermission(user, permission)) {
      return res.status(403).json({
        success: false,
        message: '权限不足',
        code: 'PERMISSION_DENIED'
      });
    }
    
    // 2. 检查租户功能权限
    if (!hasTenantFeature(tenant, permission)) {
      return res.status(403).json({
        success: false,
        message: '当前订阅计划不支持此功能',
        code: 'FEATURE_NOT_AVAILABLE'
      });
    }
    
    next();
  };
};
```

### 数据安全与隔离

#### 1. 数据访问控制
```javascript
// 数据行级安全
const applyRowLevelSecurity = (query, user, tenant) => {
  // 根据用户角色和数据所有权添加过滤条件
  if (user.role === 'user') {
    // 普通用户只能访问自己创建的数据
    query.where.userId = user.id;
  } else if (user.role === 'manager') {
    // 经理可以访问部门数据
    query.where.departmentId = user.departmentId;
  }
  // admin和owner可以访问租户内所有数据
  
  // 确保数据属于当前租户
  query.where.tenantId = tenant.id;
  
  return query;
};
```

#### 2. API访问限制
```javascript
// API调用限制
const apiRateLimit = (req, res, next) => {
  const tenant = req.tenant;
  const plan = tenant.subscriptionPlan;
  
  // 根据订阅计划设置不同限制
  const limits = {
    trial: { requests: 100, window: '1h' },
    basic: { requests: 1000, window: '1h' },
    standard: { requests: 5000, window: '1h' },
    premium: { requests: 20000, window: '1h' },
    enterprise: { requests: -1, window: '1h' } // 无限制
  };
  
  const limit = limits[plan] || limits.trial;
  
  // 实现限流逻辑
  checkRateLimit(tenant.id, limit).then(allowed => {
    if (!allowed) {
      return res.status(429).json({
        success: false,
        message: 'API调用频率超限',
        code: 'RATE_LIMIT_EXCEEDED'
      });
    }
    next();
  });
};
```

## 📈 运营监控体系

### 关键指标监控

#### 1. 租户健康度
```javascript
const calculateTenantHealth = (tenant, usage) => {
  const metrics = {
    userActivity: usage.activeUsers / tenant.maxUsers,
    featureUsage: usage.featuresUsed / tenant.availableFeatures,
    apiUsage: usage.apiCalls / tenant.apiLimit,
    storageUsage: usage.storageUsed / tenant.storageLimit
  };
  
  // 综合评分
  const healthScore = Object.values(metrics).reduce((sum, val) => sum + val, 0) / 4;
  
  return {
    score: healthScore,
    level: healthScore > 0.8 ? 'excellent' : 
           healthScore > 0.6 ? 'good' : 
           healthScore > 0.4 ? 'fair' : 'poor',
    recommendations: generateRecommendations(metrics)
  };
};
```

#### 2. 收入预测模型
```javascript
const predictRevenue = (tenant, historicalData) => {
  const monthlyGrowth = calculateGrowthRate(historicalData);
  const churnRisk = calculateChurnRisk(tenant);
  const upsellPotential = calculateUpsellPotential(tenant);
  
  return {
    nextMonth: tenant.monthlyFee * (1 + monthlyGrowth),
    nextQuarter: tenant.monthlyFee * 3 * (1 + monthlyGrowth) * (1 - churnRisk),
    upsellOpportunity: upsellPotential * getNextPlanPrice(tenant.subscriptionPlan)
  };
};
```

### 自动化运营

#### 1. 智能提醒系统
```javascript
const automatedAlerts = {
  // 订阅即将到期提醒
  subscriptionExpiry: (tenant) => {
    const daysUntilExpiry = getDaysUntil(tenant.subscriptionEndDate);
    if (daysUntilExpiry <= 30) {
      sendRenewalReminder(tenant);
    }
  },
  
  // 使用量告警
  usageAlert: (tenant, usage) => {
    if (usage.storageUsed / tenant.storageLimit > 0.8) {
      sendStorageWarning(tenant);
    }
    if (usage.apiCalls / tenant.apiLimit > 0.9) {
      sendApiLimitWarning(tenant);
    }
  },
  
  // 升级建议
  upgradeRecommendation: (tenant, usage) => {
    if (shouldRecommendUpgrade(tenant, usage)) {
      sendUpgradeRecommendation(tenant);
    }
  }
};
```

## 🔐 安全与合规

### 数据保护策略

1. **数据加密**
   - 传输加密：TLS 1.3
   - 存储加密：AES-256
   - 密钥管理：AWS KMS/HashiCorp Vault

2. **访问控制**
   - 多因素认证（MFA）
   - IP白名单
   - 会话管理
   - 审计日志

3. **备份策略**
   - 自动日备份
   - 异地容灾
   - 数据恢复测试
   - 合规性报告

### 合规要求

1. **数据隐私**
   - GDPR合规
   - 数据最小化原则
   - 用户同意管理
   - 数据删除权

2. **行业标准**
   - ISO 27001
   - SOC 2 Type II
   - 农业数据保护标准

---

## 总结

智慧养鹅SAAS平台通过完善的多租户架构、细粒度权限控制、灵活的订阅体系和智能运营监控，为农业数字化转型提供了企业级的解决方案。平台设计充分考虑了数据安全、业务扩展和用户体验，能够支撑大规模商业化运营。