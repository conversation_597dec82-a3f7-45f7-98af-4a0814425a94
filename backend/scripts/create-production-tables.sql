-- 创建生产记录V2表 (生长记录、称重记录、出栏记录)
CREATE TABLE IF NOT EXISTS `production_records_v2` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` enum('growth','weight','sale') NOT NULL COMMENT '记录类型: growth-生长记录, weight-称重记录, sale-出栏记录',
  `batch` varchar(50) NOT NULL COMMENT '批次号',
  `date` date NOT NULL COMMENT '记录日期',
  `average_weight` decimal(8,2) DEFAULT NULL COMMENT '平均体重(kg)',
  `feed_ratio` varchar(20) DEFAULT NULL COMMENT '料肉比',
  `weight_count` int(11) DEFAULT NULL COMMENT '称重数量(只)',
  `sale_count` int(11) DEFAULT NULL COMMENT '出栏数量(只)',
  `notes` text DEFAULT NULL COMMENT '备注',
  `status` enum('draft','submitted','approved','rejected') DEFAULT 'submitted' COMMENT '状态: draft-草稿, submitted-已提交, approved-已审批, rejected-已拒绝',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_batch` (`batch`),
  KEY `idx_date` (`date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产记录V2表';

-- 创建物料管理表
CREATE TABLE IF NOT EXISTS `materials` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '创建用户ID',
  `name` varchar(100) NOT NULL COMMENT '物料名称',
  `category` enum('feed','medicine','other') NOT NULL COMMENT '物料类别: feed-饲料, medicine-药品, other-其他',
  `spec` varchar(50) NOT NULL COMMENT '规格',
  `stock` int(11) NOT NULL DEFAULT 0 COMMENT '库存数量',
  `unit` varchar(20) NOT NULL DEFAULT '个' COMMENT '单位',
  `min_stock` int(11) DEFAULT NULL COMMENT '最小库存预警值',
  `max_stock` int(11) DEFAULT NULL COMMENT '最大库存值',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `supplier` varchar(100) DEFAULT NULL COMMENT '供应商',
  `supplier_contact` varchar(50) DEFAULT NULL COMMENT '供应商联系方式',
  `purchase_date` date DEFAULT NULL COMMENT '采购日期',
  `expiry_date` date DEFAULT NULL COMMENT '过期日期',
  `location` varchar(100) DEFAULT NULL COMMENT '存放位置',
  `description` text DEFAULT NULL COMMENT '描述',
  `status` enum('normal','warning','danger','expired') DEFAULT 'normal' COMMENT '状态: normal-正常, warning-预警, danger-不足, expired-过期',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_name` (`name`),
  KEY `idx_expiry_date` (`expiry_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料管理表';

-- 插入示例数据 - 生产记录V2
INSERT INTO `production_records_v2` (`user_id`, `type`, `batch`, `date`, `average_weight`, `feed_ratio`, `weight_count`, `sale_count`, `notes`, `status`) VALUES
(1, 'growth', 'GE2023061501', '2023-06-15', 2.50, '2.8:1', NULL, NULL, '生长情况良好', 'submitted'),
(1, 'weight', 'GE2023060801', '2023-06-14', 2.30, NULL, 500, NULL, '称重记录', 'submitted'),
(1, 'sale', 'GE2023060501', '2023-06-05', 3.20, NULL, NULL, 200, '出栏记录', 'submitted'),
(1, 'growth', 'GE2023050801', '2023-06-10', 2.10, '3.0:1', NULL, NULL, '生长记录', 'submitted'),
(1, 'weight', 'GE2023050501', '2023-06-08', 2.00, NULL, 480, NULL, '称重记录', 'submitted');

-- 插入示例数据 - 物料管理
INSERT INTO `materials` (`user_id`, `name`, `category`, `spec`, `stock`, `unit`, `min_stock`, `max_stock`, `unit_price`, `supplier`, `supplier_contact`, `purchase_date`, `expiry_date`, `location`, `description`, `status`) VALUES
(1, '雏鹅专用饲料', 'feed', '25kg/袋', 120, '袋', 20, 200, 85.00, '优质饲料公司', '13800138000', '2023-06-01', '2024-06-01', 'A区仓库', '适合0-4周龄雏鹅', 'normal'),
(1, '育肥期饲料', 'feed', '25kg/袋', 45, '袋', 30, 150, 78.00, '优质饲料公司', '13800138000', '2023-05-15', '2024-05-15', 'A区仓库', '适合4-8周龄鹅', 'warning'),
(1, '成鹅饲料', 'feed', '25kg/袋', 80, '袋', 25, 180, 72.00, '优质饲料公司', '13800138000', '2023-05-20', '2024-05-20', 'A区仓库', '适合8周龄以上成鹅', 'normal'),
(1, '抗生素', 'medicine', '100ml/瓶', 15, '瓶', 20, 50, 25.00, '兽药公司', '13900139000', '2023-04-10', '2024-04-10', 'B区药品柜', '治疗细菌感染', 'danger'),
(1, '维生素', 'medicine', '500g/瓶', 25, '瓶', 15, 40, 18.00, '兽药公司', '13900139000', '2023-05-01', '2024-05-01', 'B区药品柜', '补充维生素', 'warning'),
(1, '消毒液', 'other', '5L/桶', 15, '桶', 10, 30, 35.00, '清洁用品公司', '13700137000', '2023-05-10', '2025-05-10', 'C区清洁用品', '环境消毒', 'normal'),
(1, '饮水器', 'other', '自动饮水器', 8, '个', 5, 20, 120.00, '设备公司', '13600136000', '2023-03-15', NULL, 'D区设备间', '自动供水设备', 'normal');

-- 创建物料库存变动记录表（可选，用于追踪库存变化）
CREATE TABLE IF NOT EXISTS `material_inventory_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `material_id` int(11) NOT NULL COMMENT '物料ID',
  `user_id` int(11) NOT NULL COMMENT '操作用户ID',
  `type` enum('in','out','adjust') NOT NULL COMMENT '变动类型: in-入库, out-出库, adjust-调整',
  `quantity` int(11) NOT NULL COMMENT '变动数量',
  `before_stock` int(11) NOT NULL COMMENT '变动前库存',
  `after_stock` int(11) NOT NULL COMMENT '变动后库存',
  `reason` varchar(200) DEFAULT NULL COMMENT '变动原因',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料库存变动记录表';
