<!-- 帮助分类详情页 -->
<view class="category-container">
  <!-- 分类头部 -->
  <view class="category-header">
    <view class="header-content">
      <text class="category-icon">{{categoryInfo.icon}}</text>
      <view class="header-text">
        <text class="category-title">{{categoryInfo.title}}</text>
        <text class="category-desc">{{categoryInfo.description}}</text>
      </view>
    </view>
    <view class="category-stats">
      <view class="stat-item">
        <text class="stat-number">{{categoryInfo.articles.length}}</text>
        <text class="stat-label">篇文章</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{categoryInfo.totalViews}}</text>
        <text class="stat-label">次浏览</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-container">
      <text class="search-icon">🔍</text>
      <input class="search-input" 
             placeholder="搜索{{categoryInfo.title}}相关内容..." 
             value="{{searchKeyword}}" 
             bindinput="onSearchInput"
             bindconfirm="onSearch" />
      <text class="clear-btn" wx:if="{{searchKeyword}}" bindtap="onClearSearch">×</text>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="onSortBy" data-type="latest">
      <text class="action-icon">🕒</text>
      <text class="action-text">最新发布</text>
    </view>
    <view class="action-item" bindtap="onSortBy" data-type="popular">
      <text class="action-icon">🔥</text>
      <text class="action-text">最受欢迎</text>
    </view>
    <view class="action-item" bindtap="onSortBy" data-type="helpful">
      <text class="action-icon">👍</text>
      <text class="action-text">最有帮助</text>
    </view>
  </view>

  <!-- 文章列表 -->
  <view class="articles-section">
    <view class="section-header">
      <text class="section-title">相关文章</text>
      <text class="sort-indicator">{{sortText}}</text>
    </view>
    
    <view class="articles-list">
      <view class="article-item" 
            wx:for="{{filteredArticles}}" 
            wx:key="id"
            bindtap="onViewArticle" 
            data-article="{{item}}">
        <view class="article-content">
          <text class="article-title">{{item.title}}</text>
          <text class="article-summary">{{item.summary}}</text>
          <view class="article-meta">
            <text class="meta-item">
              <text class="meta-icon">👁️</text>
              <text class="meta-text">{{item.views}}次浏览</text>
            </text>
            <text class="meta-item">
              <text class="meta-icon">👍</text>
              <text class="meta-text">{{item.likes}}赞</text>
            </text>
            <text class="meta-item">
              <text class="meta-icon">🕒</text>
              <text class="meta-text">{{item.updateTime}}</text>
            </text>
          </view>
        </view>
        <view class="article-actions">
          <text class="difficulty-tag difficulty-{{item.difficulty}}">{{item.difficultyText}}</text>
          <text class="article-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredArticles.length === 0}}">
      <text class="empty-icon">📖</text>
      <text class="empty-text">暂无相关文章</text>
      <text class="empty-tips">试试其他搜索关键词</text>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedCategories.length > 0}}">
    <view class="section-header">
      <text class="section-title">相关分类</text>
    </view>
    <view class="related-grid">
      <view class="related-item" 
            wx:for="{{relatedCategories}}" 
            wx:key="id"
            bindtap="onViewCategory" 
            data-category="{{item}}">
        <text class="related-icon">{{item.icon}}</text>
        <text class="related-title">{{item.title}}</text>
        <text class="related-count">{{item.articles.length}}篇</text>
      </view>
    </view>
  </view>

  <!-- 常见问题 -->
  <view class="faq-section" wx:if="{{categoryFAQ.length > 0}}">
    <view class="section-header">
      <text class="section-title">常见问题</text>
    </view>
    <view class="faq-list">
      <view class="faq-item" 
            wx:for="{{categoryFAQ}}" 
            wx:key="id">
        <view class="faq-question" bindtap="onToggleFAQ" data-index="{{index}}">
          <text class="question-text">{{item.question}}</text>
          <text class="arrow-icon {{item.expanded ? 'expanded' : ''}}">{{item.expanded ? '▲' : '▼'}}</text>
        </view>
        <view wx:if="{{item.expanded}}" class="faq-answer">
          <text class="answer-text">{{item.answer}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 反馈区域 -->
  <view class="feedback-section">
    <view class="feedback-card">
      <text class="feedback-title">没找到您要的内容？</text>
      <text class="feedback-desc">告诉我们您的问题，我们会尽快为您解答</text>
      <view class="feedback-actions">
        <button class="feedback-btn" bindtap="onFeedback">
          <text class="btn-icon">💬</text>
          <text class="btn-text">提交反馈</text>
        </button>
        <button class="contact-btn" bindtap="onContact">
          <text class="btn-icon">📞</text>
          <text class="btn-text">联系客服</text>
        </button>
      </view>
    </view>
  </view>
</view>