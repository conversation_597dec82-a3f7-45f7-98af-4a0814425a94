/**
 * 统一库存管理测试脚本
 * 验证UnifiedInventory模型和控制器是否正常工作
 */

const UnifiedInventory = require("../models/unified-inventory.model");
const { Op } = require("sequelize");

async function testUnifiedInventory() {
  console.log("🧪 开始测试统一库存管理系统...\n");

  try {
    // 测试1: 创建测试库存项目
    console.log("📝 测试 1: 创建库存项目");
    const testItem = await UnifiedInventory.create({
      userId: 1,
      name: "测试鹅饲料",
      category: "feed",
      specification: "25kg/袋",
      unit: "袋",
      currentStock: 100,
      minStock: 20,
      maxStock: 500,
      unitPrice: 45.5,
      supplier: "优质饲料供应商",
      location: "仓库A区",
      status: "active",
      description: "优质鹅专用饲料，营养均衡",
      createdBy: 1,
      updatedBy: 1,
    });
    console.log("✅ 创建成功:", testItem.name);

    // 测试2: 查询库存项目
    console.log("\n🔍 测试 2: 查询库存项目");
    const allItems = await UnifiedInventory.findAll({
      where: { userId: 1 },
      limit: 5,
    });
    console.log("✅ 查询成功，找到项目数:", allItems.length);

    // 测试3: 按类别筛选
    console.log("\n📊 测试 3: 按类别筛选");
    const feedItems = await UnifiedInventory.findAll({
      where: {
        userId: 1,
        category: "feed",
      },
    });
    console.log("✅ 饲料类项目数:", feedItems.length);

    // 测试4: 搜索功能
    console.log("\n🔎 测试 4: 搜索功能");
    const searchResults = await UnifiedInventory.findAll({
      where: {
        userId: 1,
        [Op.or]: [
          { name: { [Op.like]: "%饲料%" } },
          { description: { [Op.like]: "%饲料%" } },
        ],
      },
    });
    console.log("✅ 搜索结果数:", searchResults.length);

    // 测试5: 更新库存项目
    console.log("\n📝 测试 5: 更新库存项目");
    const [updatedCount] = await UnifiedInventory.update(
      {
        currentStock: 80,
        updatedBy: 1,
      },
      {
        where: { id: testItem.id },
      },
    );
    console.log("✅ 更新成功，影响行数:", updatedCount);

    // 测试6: 库存统计
    console.log("\n📈 测试 6: 库存统计");
    const stats = await UnifiedInventory.findAll({
      where: { userId: 1 },
      attributes: ["category", "currentStock", "status"],
    });

    const totalStock = stats.reduce(
      (sum, item) => sum + (parseFloat(item.currentStock) || 0),
      0,
    );
    const activeItems = stats.filter((item) => item.status === "active").length;
    const categories = [...new Set(stats.map((item) => item.category))].length;

    console.log("✅ 统计结果:");
    console.log("  - 总库存量:", totalStock);
    console.log("  - 活跃项目数:", activeItems);
    console.log("  - 类别数:", categories);

    // 测试7: 低库存预警
    console.log("\n⚠️  测试 7: 低库存预警");
    const lowStockItems = await UnifiedInventory.findAll({
      where: {
        userId: 1,
        [Op.and]: [
          { currentStock: { [Op.lte]: { [Op.col]: "min_stock" } } },
          { status: "active" },
        ],
      },
    });
    console.log("✅ 低库存项目数:", lowStockItems.length);

    // 清理测试数据
    console.log("\n🧹 清理测试数据");
    await UnifiedInventory.destroy({
      where: {
        name: "测试鹅饲料",
        userId: 1,
      },
    });
    console.log("✅ 测试数据已清理");

    console.log("\n🎉 统一库存管理系统测试完成！所有功能正常工作。");
  } catch (error) {
    console.error("❌ 测试失败:", error.message);
    console.error("详细错误:", error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testUnifiedInventory()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error("测试过程中发生错误:", error);
      process.exit(1);
    });
}

module.exports = { testUnifiedInventory };
