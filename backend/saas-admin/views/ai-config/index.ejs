<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-content {
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .config-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .usage-progress {
            height: 8px;
            border-radius: 4px;
        }

        .provider-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-right: 15px;
        }

        .openai-bg { background: linear-gradient(135deg, #10a37f, #1a7f64); }
        .baidu-bg { background: linear-gradient(135deg, #4285f4, #3367d6); }
        .ali-bg { background: linear-gradient(135deg, #ff6900, #e55a00); }
    </style>
</head>
<body>
    <%- include('../partials/navbar') %>

    <div class="container-fluid">
        <div class="row">
            <%- include('../partials/sidebar') %>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="main-content">
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h2">
                                <i class="bi bi-robot me-2"></i>AI配置管理
                            </h1>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addConfigModal">
                                <i class="bi bi-plus"></i> 添加AI配置
                            </button>
                        </div>
                    </div>
                    
                    <!-- AI配置统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h4 class="text-primary mb-2"><%= aiStats.totalProviders %></h4>
                                <p class="text-muted mb-0">总配置数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h4 class="text-success mb-2"><%= aiStats.activeProviders %></h4>
                                <p class="text-muted mb-0">活跃配置</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h4 class="text-info mb-2"><%= aiStats.totalDailyCalls.toLocaleString() %></h4>
                                <p class="text-muted mb-0">今日调用</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h4 class="text-warning mb-2"><%= aiStats.totalMonthlyCalls.toLocaleString() %></h4>
                                <p class="text-muted mb-0">本月调用</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI配置列表 -->
                    <% if (typeof aiConfigs !== 'undefined' && aiConfigs.length > 0) { %>
                        <% aiConfigs.forEach(function(config) { %>
                            <div class="config-card">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <div class="d-flex align-items-center">
                                            <div class="provider-icon <%= config.provider === 'OpenAI' ? 'openai-bg' : config.provider === '百度文心' ? 'baidu-bg' : 'ali-bg' %>">
                                                <i class="bi bi-robot"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h5 class="mb-1">
                                                    <%= config.provider %>
                                                    <span class="status-badge <%= config.status === 'active' ? 'bg-success' : 'bg-secondary' %>">
                                                        <%= config.status === 'active' ? '活跃' : '停用' %>
                                                    </span>
                                                </h5>
                                                <p class="text-muted mb-2">模型: <%= config.model %> | API密钥: <%= config.apiKey %></p>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <small class="text-muted">今日调用: <strong><%= config.usage.dailyCalls.toLocaleString() %></strong></small>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <small class="text-muted">本月调用: <strong><%= config.usage.monthlyCalls.toLocaleString() %></strong></small>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <small class="text-muted">使用率: <strong><%= Math.round((config.usage.monthlyCalls / config.usage.limit) * 100) %>%</strong></small>
                                                    </div>
                                                </div>
                                                <div class="progress usage-progress mt-2">
                                                    <div class="progress-bar <%= Math.round((config.usage.monthlyCalls / config.usage.limit) * 100) > 80 ? 'bg-danger' : Math.round((config.usage.monthlyCalls / config.usage.limit) * 100) > 60 ? 'bg-warning' : 'bg-success' %>" 
                                                         style="width: <%= Math.round((config.usage.monthlyCalls / config.usage.limit) * 100) %>%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <small class="text-muted d-block mb-2">
                                            创建时间: <%= new Date(config.createdAt).toLocaleDateString('zh-CN') %>
                                        </small>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/saas-admin/ai-config/<%= config.id %>" class="btn btn-outline-primary">查看</a>
                                            <button type="button" class="btn btn-outline-secondary" onclick="testConnection(<%= config.id %>)">测试</button>
                                            <button type="button" class="btn btn-outline-warning" onclick="editConfig(<%= config.id %>)">编辑</button>
                                            <button type="button" class="btn btn-outline-danger" onclick="deleteConfig(<%= config.id %>)">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <% }) %>
                    <% } else { %>
                        <div class="config-card text-center py-5">
                            <i class="bi bi-robot display-1 text-muted"></i>
                            <h4 class="mt-3 text-muted">暂无AI配置</h4>
                            <p class="text-muted">点击上方"添加AI配置"按钮来创建AI配置</p>
                        </div>
                    <% } %>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加AI配置模态框 -->
    <div class="modal fade" id="addConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加AI配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addConfigForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">AI提供商</label>
                                <select class="form-select" name="provider" required>
                                    <option value="">选择提供商</option>
                                    <option value="OpenAI">OpenAI</option>
                                    <option value="百度文心">百度文心</option>
                                    <option value="阿里通义千问">阿里通义千问</option>
                                    <option value="腾讯混元">腾讯混元</option>
                                    <option value="讯飞星火">讯飞星火</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">模型名称</label>
                                <input type="text" class="form-control" name="model" placeholder="如: gpt-4, ernie-bot-4.0" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">API密钥</label>
                            <input type="password" class="form-control" name="apiKey" placeholder="输入API密钥" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">API基础URL</label>
                            <input type="url" class="form-control" name="baseUrl" placeholder="如: https://api.openai.com/v1">
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">最大Token数</label>
                                <input type="number" class="form-control" name="maxTokens" value="4000" min="1" max="32000">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">温度 (Temperature)</label>
                                <input type="number" class="form-control" name="temperature" value="0.7" min="0" max="2" step="0.1">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveConfig()">保存配置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试连接
        function testConnection(configId) {
            // 显示加载状态
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
            button.disabled = true;
            
            fetch(`/saas-admin/ai-config/${configId}/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('连接测试成功！响应时间: ' + data.data.responseTime + 'ms');
                } else {
                    alert('连接测试失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('测试失败: ' + error.message);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
        
        // 编辑配置
        function editConfig(configId) {
            window.location.href = `/saas-admin/ai-config/${configId}`;
        }
        
        // 删除配置
        function deleteConfig(configId) {
            if (confirm('确定要删除这个AI配置吗？')) {
                fetch(`/saas-admin/ai-config/${configId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除失败: ' + error.message);
                });
            }
        }
        
        // 保存配置
        function saveConfig() {
            const form = document.getElementById('addConfigForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            fetch('/saas-admin/ai-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('保存失败: ' + error.message);
            });
        }
    </script>
</body>
</html>