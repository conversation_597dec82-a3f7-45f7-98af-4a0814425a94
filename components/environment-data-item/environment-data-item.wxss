/* components/environment-data-item/environment-data-item.wxss */

.data-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.data-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.data-item:hover::before,
.data-item:active::before {
  opacity: 1;
}

.data-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border-color: var(--primary-light);
}

.data-item:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.item-icon {
  width: 56rpx;
  height: 56rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  padding: 8rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.data-item:hover .item-icon {
  background: var(--primary-bg);
  transform: scale(1.1);
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.item-value {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 6rpx;
  line-height: 1.1;
  letter-spacing: -0.5rpx;
}

.item-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
  line-height: 1.2;
  font-weight: 500;
}

.item-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
  align-self: flex-start;
  line-height: 1.2;
}

.item-status.normal {
  background-color: var(--info-bg);
  color: var(--primary);
}

.item-status.good {
  background-color: #F6FFED;
  color: #52C41A;
}

.item-status.suitable {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.item-status.warning {
  background-color: #FFF2E6;
  color: #FA8C16;
}

.item-status.danger {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
  opacity: 0.6;
}

/* 小屏设备优化 - 但保持水平布局 */
@media (max-width: 480px) {
  .data-item {
    padding: 20rpx 16rpx;
  }

  .item-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
  }

  .item-value {
    font-size: 30rpx;
  }

  .item-label {
    font-size: 22rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .data-item {
    background: #1a1a1a;
    border-color: #333;
  }

  .item-value {
    color: #fff;
  }

  .item-label {
    color: #999;
  }

  .item-icon {
    background: #2a2a2a;
  }
}