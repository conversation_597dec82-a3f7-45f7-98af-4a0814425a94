// pages/oa/leave/list/list.js

/**
 * 请假记录列表页面
 * 功能：查看请假记录列表、筛选、搜索
 */

Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    loadingMore: false,
    
    // 筛选条件
    filters: {
      status: 'all',    // all, pending, approved, rejected
      type: 'all',      // all, sick, personal, annual, etc.
      timeRange: 'all'  // all, thisMonth, lastMonth, thisYear
    },
    
    // 筛选选项
    statusOptions: [
      { value: 'all', label: '全部状态', color: '#666' },
      { value: 'pending', label: '待审批', color: '#FF9500' },
      { value: 'approved', label: '已批准', color: '#00A86B' },
      { value: 'rejected', label: '已拒绝', color: '#FF3B30' }
    ],
    
    typeOptions: [
      { value: 'all', label: '全部类型' },
      { value: 'sick', label: '病假' },
      { value: 'personal', label: '事假' },
      { value: 'annual', label: '年假' },
      { value: 'marriage', label: '婚假' },
      { value: 'maternity', label: '产假' },
      { value: 'other', label: '其他' }
    ],
    
    timeRangeOptions: [
      { value: 'all', label: '全部时间' },
      { value: 'thisMonth', label: '本月' },
      { value: 'lastMonth', label: '上月' },
      { value: 'thisYear', label: '今年' }
    ],
    
    // 搜索关键词
    searchKeyword: '',
    
    // 计算属性 - 当前选中的筛选器标签
    selectedStatusLabel: '全部状态',
    selectedTypeLabel: '全部类型',
    selectedTimeRangeLabel: '全部时间',
    
    // 请假记录列表
    leaveList: [],
    
    // 分页参数
    page: 1,
    pageSize: 10,
    hasMore: true,
    total: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 从参数中获取筛选条件
    if (options.status) {
      this.setData({
        'filters.status': options.status
      });
    }
    
    if (options.type) {
      this.setData({
        'filters.type': options.type
      });
    }
    
    this.loadLeaveList(true);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 如果从详情页返回，刷新列表
    if (this.data.leaveList.length > 0) {
      this.refreshList();
    }
  },

  /**
   * 加载请假记录列表
   */
  loadLeaveList: function(reset = false) {
    if (this.data.loading) return;
    
    this.setData({ 
      loading: reset ? true : false,
      loadingMore: !reset
    });
    
    if (reset) {
      this.setData({
        page: 1,
        hasMore: true,
        leaveList: []
      });
    }
    
    // 模拟API调用
    setTimeout(() => {
      const mockData = this.generateMockData();
      const currentList = reset ? [] : this.data.leaveList;
      
      this.setData({
        leaveList: [...currentList, ...mockData.list],
        total: mockData.total,
        hasMore: mockData.hasMore,
        loading: false,
        loadingMore: false,
        refreshing: false,
        page: this.data.page + 1
      });
    }, 800);
  },

  /**
   * 生成模拟数据
   */
  generateMockData: function() {
    const { page, filters, searchKeyword } = this.data;
    
    // 完整的模拟数据
    const allData = [
      {
        id: 'leave_001',
        type: 'sick',
        typeLabel: '病假',
        typeColor: '#FF9500',
        reason: '感冒发烧需要休息',
        startDate: '2024-01-15',
        endDate: '2024-01-16',
        days: 2,
        status: 'pending',
        statusText: '待审批',
        statusColor: '#FF9500',
        applicant: '张三',
        department: '技术部',
        submitTime: '2024-01-10 09:30',
        approver: '',
        approveTime: ''
      },
      {
        id: 'leave_002',
        type: 'personal',
        typeLabel: '事假',
        typeColor: '#0066CC',
        reason: '家庭事务处理',
        startDate: '2024-01-08',
        endDate: '2024-01-09',
        days: 2,
        status: 'approved',
        statusText: '已批准',
        statusColor: '#00A86B',
        applicant: '张三',
        department: '技术部',
        submitTime: '2024-01-05 14:20',
        approver: '李经理',
        approveTime: '2024-01-06 10:15'
      },
      {
        id: 'leave_003',
        type: 'annual',
        typeLabel: '年假',
        typeColor: '#00A86B',
        reason: '年度休假',
        startDate: '2023-12-25',
        endDate: '2023-12-31',
        days: 7,
        status: 'approved',
        statusText: '已批准',
        statusColor: '#00A86B',
        applicant: '张三',
        department: '技术部',
        submitTime: '2023-12-20 16:45',
        approver: '李经理',
        approveTime: '2023-12-21 09:30'
      },
      {
        id: 'leave_004',
        type: 'sick',
        typeLabel: '病假',
        typeColor: '#FF9500',
        reason: '急性肠胃炎',
        startDate: '2023-11-20',
        endDate: '2023-11-21',
        days: 2,
        status: 'rejected',
        statusText: '已拒绝',
        statusColor: '#FF3B30',
        applicant: '张三',
        department: '技术部',
        submitTime: '2023-11-18 08:45',
        approver: '李经理',
        approveTime: '2023-11-19 14:20',
        rejectReason: '请假时间与重要项目冲突'
      }
    ];
    
    // 根据筛选条件过滤数据
    let filteredData = allData.filter(item => {
      // 状态筛选
      if (filters.status !== 'all' && item.status !== filters.status) {
        return false;
      }
      
      // 类型筛选
      if (filters.type !== 'all' && item.type !== filters.type) {
        return false;
      }
      
      // 搜索关键词
      if (searchKeyword && !item.reason.includes(searchKeyword)) {
        return false;
      }
      
      return true;
    });
    
    // 分页处理
    const startIndex = (page - 1) * this.data.pageSize;
    const endIndex = startIndex + this.data.pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    return {
      list: pageData,
      total: filteredData.length,
      hasMore: endIndex < filteredData.length
    };
  },

  /**
   * 筛选器变化
   */
  onFilterChange: function(e) {
    const { type } = e.currentTarget.dataset;
    const value = e.detail.value;
    let selectedOption;
    
    switch(type) {
      case 'status':
        selectedOption = this.data.statusOptions[value];
        this.setData({
          'filters.status': selectedOption.value,
          'selectedStatusLabel': selectedOption.label
        });
        break;
      case 'type':
        selectedOption = this.data.typeOptions[value];
        this.setData({
          'filters.type': selectedOption.value,
          'selectedTypeLabel': selectedOption.label
        });
        break;
      case 'timeRange':
        selectedOption = this.data.timeRangeOptions[value];
        this.setData({
          'filters.timeRange': selectedOption.value,
          'selectedTimeRangeLabel': selectedOption.label
        });
        break;
    }
    
    this.loadLeaveList(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索提交
   */
  onSearchSubmit: function() {
    this.loadLeaveList(true);
  },

  /**
   * 清除搜索
   */
  onClearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    this.loadLeaveList(true);
  },

  /**
   * 请假记录点击
   */
  onLeaveItemTap: function(e) {
    const { id } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/oa/leave/detail/detail?id=${id}`,
      fail: () => {
        wx.showToast({
          title: '页面暂未开放',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 刷新列表
   */
  refreshList: function() {
    this.setData({ refreshing: true });
    this.loadLeaveList(true);
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.refreshList();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadLeaveList();
    }
  },

  /**
   * 申请新请假
   */
  onApplyLeave: function() {
    wx.navigateTo({
      url: '/pages/oa/leave/apply/apply'
    });
  },

  /**
   * 分享
   */
  onShareAppMessage: function () {
    return {
      title: '请假记录',
      path: '/pages/oa/leave/list/list'
    };
  }
});