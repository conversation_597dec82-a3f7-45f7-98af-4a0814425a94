// backend/controllers/health-v2.controller.js
// 使用统一错误处理的健康管理控制器

const HealthRecord = require("../models/health-record.model");
const { createError, asyncHandler } = require("../utils/error-handler");

/**
 * 获取健康记录列表
 * GET /api/v2/health/records
 */
exports.getRecords = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;

  // 验证用户信息
  if (!req.user || !req.user.id) {
    throw createError.tokenInvalid("用户信息无效");
  }

  // 验证分页参数
  if (page < 1 || limit < 1 || limit > 100) {
    throw createError.validationFailed("分页参数无效");
  }

  const { count, rows } = await HealthRecord.findAndCountAll({
    where: { userId: req.user.id },
    limit: limit,
    offset: offset,
    order: [["created_at", "DESC"]],
  });

  // 转换字段名为统一格式
  const records = rows.map((record) => ({
    id: record.id,
    userId: record.userId,
    gooseId: record.gooseId,
    healthStatus: record.status,
    symptoms: record.symptoms,
    diagnosis: record.diagnosis,
    treatment: record.treatment,
    createdAt: record.created_at,
    updatedAt: record.updated_at,
  }));

  res.json({
    success: true,
    message: "获取健康记录成功",
    data: {
      records: records,
      pagination: {
        page: page,
        limit: limit,
        total: count,
        totalPages: Math.ceil(count / limit),
      },
    },
  });
});

/**
 * 获取健康记录详情
 * GET /api/v2/health/records/:id
 */
exports.getRecordById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 验证ID参数
  if (!id || isNaN(parseInt(id))) {
    throw createError.validationFailed("无效的记录ID");
  }

  // 验证用户信息
  if (!req.user || !req.user.id) {
    throw createError.tokenInvalid("用户信息无效");
  }

  const record = await HealthRecord.findOne({
    where: {
      id: parseInt(id),
      userId: req.user.id,
    },
  });

  if (!record) {
    throw createError.recordNotFound("健康记录");
  }

  res.json({
    success: true,
    message: "获取健康记录详情成功",
    data: {
      id: record.id,
      userId: record.userId,
      gooseId: record.gooseId,
      healthStatus: record.status,
      symptoms: record.symptoms,
      diagnosis: record.diagnosis,
      treatment: record.treatment,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    },
  });
});

/**
 * 创建健康记录
 * POST /api/v2/health/records
 */
exports.createRecord = asyncHandler(async (req, res) => {
  const { gooseId, healthStatus, symptoms, diagnosis, treatment } = req.body;

  // 验证用户信息
  if (!req.user || !req.user.id) {
    throw createError.tokenInvalid("用户信息无效");
  }

  // 验证必填字段（Joi验证已经处理，这里是双重保险）
  if (!gooseId) {
    throw createError.requiredField("鹅群编号");
  }

  if (!healthStatus) {
    throw createError.requiredField("健康状态");
  }

  // 检查是否已存在相同的记录
  const existingRecord = await HealthRecord.findOne({
    where: {
      userId: req.user.id,
      gooseId: gooseId,
      status: healthStatus,
    },
  });

  if (existingRecord) {
    throw createError.resourceAlreadyExists("相同的健康记录");
  }

  const record = await HealthRecord.create({
    userId: req.user.id,
    gooseId: gooseId,
    status: healthStatus,
    symptoms: symptoms,
    diagnosis: diagnosis,
    treatment: treatment,
  });

  res.status(201).json({
    success: true,
    message: "健康记录创建成功",
    data: {
      id: record.id,
      userId: record.userId,
      gooseId: record.gooseId,
      healthStatus: record.status,
      symptoms: record.symptoms,
      diagnosis: record.diagnosis,
      treatment: record.treatment,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    },
  });
});

/**
 * 更新健康记录
 * PUT /api/v2/health/records/:id
 */
exports.updateRecord = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { gooseId, healthStatus, symptoms, diagnosis, treatment } = req.body;

  // 验证ID参数
  if (!id || isNaN(parseInt(id))) {
    throw createError.validationFailed("无效的记录ID");
  }

  // 验证用户信息
  if (!req.user || !req.user.id) {
    throw createError.tokenInvalid("用户信息无效");
  }

  const record = await HealthRecord.findOne({
    where: {
      id: parseInt(id),
      userId: req.user.id,
    },
  });

  if (!record) {
    throw createError.recordNotFound("健康记录");
  }

  // 更新记录
  await record.update({
    gooseId: gooseId || record.gooseId,
    status: healthStatus || record.status,
    symptoms: symptoms !== undefined ? symptoms : record.symptoms,
    diagnosis: diagnosis !== undefined ? diagnosis : record.diagnosis,
    treatment: treatment !== undefined ? treatment : record.treatment,
  });

  res.json({
    success: true,
    message: "健康记录更新成功",
    data: {
      id: record.id,
      userId: record.userId,
      gooseId: record.gooseId,
      healthStatus: record.status,
      symptoms: record.symptoms,
      diagnosis: record.diagnosis,
      treatment: record.treatment,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    },
  });
});

/**
 * 删除健康记录
 * DELETE /api/v2/health/records/:id
 */
exports.deleteRecord = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 验证ID参数
  if (!id || isNaN(parseInt(id))) {
    throw createError.validationFailed("无效的记录ID");
  }

  // 验证用户信息
  if (!req.user || !req.user.id) {
    throw createError.tokenInvalid("用户信息无效");
  }

  const record = await HealthRecord.findOne({
    where: {
      id: parseInt(id),
      userId: req.user.id,
    },
  });

  if (!record) {
    throw createError.recordNotFound("健康记录");
  }

  await record.destroy();

  res.json({
    success: true,
    message: "健康记录删除成功",
  });
});

/**
 * 批量删除健康记录
 * DELETE /api/v2/health/records/batch
 */
exports.batchDeleteRecords = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  // 验证用户信息
  if (!req.user || !req.user.id) {
    throw createError.tokenInvalid("用户信息无效");
  }

  // 验证IDs参数
  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    throw createError.validationFailed("请提供要删除的记录ID列表");
  }

  // 限制批量删除数量
  if (ids.length > 100) {
    throw createError.validationFailed("一次最多只能删除100条记录");
  }

  // 验证所有ID都是有效的数字
  const validIds = ids
    .filter((id) => !isNaN(parseInt(id)))
    .map((id) => parseInt(id));
  if (validIds.length !== ids.length) {
    throw createError.validationFailed("包含无效的记录ID");
  }

  // 查找用户拥有的记录
  const records = await HealthRecord.findAll({
    where: {
      id: validIds,
      userId: req.user.id,
    },
  });

  if (records.length === 0) {
    throw createError.recordNotFound("要删除的健康记录");
  }

  // 执行批量删除
  const deletedCount = await HealthRecord.destroy({
    where: {
      id: records.map((record) => record.id),
      userId: req.user.id,
    },
  });

  res.json({
    success: true,
    message: `成功删除${deletedCount}条健康记录`,
    data: {
      deletedCount: deletedCount,
      requestedCount: ids.length,
    },
  });
});
