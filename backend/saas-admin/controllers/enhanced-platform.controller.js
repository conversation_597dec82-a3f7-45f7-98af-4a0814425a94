/**
 * 增强型SAAS平台管理控制器
 * Enhanced SAAS Platform Management Controller
 * 
 * 提供完整的租户生命周期管理、监控分析、订阅管理等功能
 */

const { Op } = require('sequelize');
const saasModels = require('../models/saas-platform.model');
const { generateErrorResponse, generateSuccessResponse } = require('../../utils/response-helper');
const { requirePermissions, PERMISSIONS } = require('../../middleware/unified-permission.middleware');
const TenantDatabaseManager = require('../../models/tenant-database.model');

class EnhancedPlatformController {
  
  // ================================
  // 租户生命周期管理
  // ================================

  /**
   * 获取租户列表（增强版）
   * 支持高级筛选、分页、排序
   */
  async getTenants(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        subscriptionPlan,
        scale,
        industry,
        search,
        sortBy = 'createdAt',
        sortOrder = 'DESC',
        startDate,
        endDate,
        activeOnly = false
      } = req.query;

      // 构建查询条件
      const whereClause = {};
      
      if (status) {
        whereClause.status = status;
      }
      
      if (subscriptionPlan) {
        whereClause.subscriptionPlan = subscriptionPlan;
      }
      
      if (scale) {
        whereClause.scale = scale;
      }
      
      if (industry) {
        whereClause.industry = { [Op.like]: `%${industry}%` };
      }
      
      if (search) {
        whereClause[Op.or] = [
          { companyName: { [Op.like]: `%${search}%` } },
          { contactName: { [Op.like]: `%${search}%` } },
          { contactEmail: { [Op.like]: `%${search}%` } },
          { tenantCode: { [Op.like]: `%${search}%` } }
        ];
      }
      
      if (startDate && endDate) {
        whereClause.createdAt = {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        };
      }
      
      if (activeOnly) {
        whereClause.status = { [Op.in]: ['active', 'trial'] };
        whereClause.subscriptionEndDate = { [Op.gte]: new Date() };
      }

      // 执行查询
      const { count, rows: tenants } = await saasModels.Tenant.findAndCountAll({
        where: whereClause,
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        order: [[sortBy, sortOrder]],
        include: [
          {
            model: saasModels.TenantUsageStats,
            as: 'usageStats',
            required: false,
            where: {
              statDate: {
                [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
              }
            }
          }
        ]
      });

      // 计算统计信息
      const statistics = await this.calculateTenantStatistics(whereClause);

      res.json(generateSuccessResponse({
        tenants: tenants.map(tenant => this.formatTenantInfo(tenant)),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / limit)
        },
        statistics
      }));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('获取租户列表失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('获取租户列表失败', error));
    }
  }

  /**
   * 创建租户（增强版）
   * 包含完整的初始化流程
   */
  async createTenant(req, res) {
    const transaction = await saasModels.sequelize.transaction();
    
    try {
      const {
        tenantCode,
        companyName,
        contactName,
        contactPhone,
        contactEmail,
        address,
        businessLicense,
        industry,
        scale,
        subscriptionPlan = 'trial',
        customDomain,
        initialSettings = {}
      } = req.body;

      // 验证租户代码唯一性
      const existingTenant = await saasModels.Tenant.findOne({
        where: { tenantCode: tenantCode.toUpperCase() }
      });

      if (existingTenant) {
        return res.status(400).json(generateErrorResponse('租户代码已存在'));
      }

      // 创建租户记录
      const tenant = await saasModels.Tenant.create({
        tenantCode: tenantCode.toUpperCase(),
        companyName,
        contactName,
        contactPhone,
        contactEmail,
        address,
        businessLicense,
        industry,
        scale,
        subscriptionPlan,
        subscriptionStartDate: new Date(),
        subscriptionEndDate: this.calculateSubscriptionEndDate(subscriptionPlan),
        maxUsers: this.getSubscriptionLimits(subscriptionPlan).maxUsers,
        maxFlocks: this.getSubscriptionLimits(subscriptionPlan).maxFlocks,
        storageLimit: this.getSubscriptionLimits(subscriptionPlan).storageLimit,
        apiCallsLimit: this.getSubscriptionLimits(subscriptionPlan).apiCallsLimit,
        customDomain,
        features: JSON.stringify(this.getSubscriptionFeatures(subscriptionPlan)),
        settings: JSON.stringify({
          ...this.getDefaultTenantSettings(),
          ...initialSettings
        }),
        status: 'pending'
      }, { transaction });

      // 创建租户数据库
      await this.createTenantDatabase(tenant.tenantCode);

      // 初始化租户数据
      await this.initializeTenantData(tenant, transaction);

      // 创建默认管理员用户
      await this.createDefaultAdminUser(tenant, req.body.adminUser, transaction);

      // 记录创建操作
      await this.logTenantOperation(tenant.id, 'CREATED', req.user.id, {
        creator: req.user.username,
        initialPlan: subscriptionPlan
      }, transaction);

      await transaction.commit();

      // 激活租户
      await this.activateTenant(tenant.id);

      res.status(201).json(generateSuccessResponse({
        tenant: this.formatTenantInfo(tenant),
        message: '租户创建成功'
      }));

    } catch (error) {
      await transaction.rollback();
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('创建租户失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('创建租户失败', error));
    }
  }

  /**
   * 租户详情（增强版）
   * 包含使用统计、性能指标等
   */
  async getTenantDetail(req, res) {
    try {
      const { id } = req.params;

      const tenant = await saasModels.Tenant.findByPk(id, {
        include: [
          {
            model: saasModels.TenantUsageStats,
            as: 'usageStats',
            limit: 30,
            order: [['statDate', 'DESC']]
          },
          {
            model: saasModels.TenantOperationLog,
            as: 'operationLogs',
            limit: 10,
            order: [['createdAt', 'DESC']]
          }
        ]
      });

      if (!tenant) {
        return res.status(404).json(generateErrorResponse('租户不存在'));
      }

      // 获取实时统计数据
      const realtimeStats = await this.getTenantRealtimeStats(tenant.tenantCode);
      
      // 获取健康度评分
      const healthScore = await this.calculateTenantHealthScore(tenant);
      
      // 获取订阅信息
      const subscriptionInfo = await this.getSubscriptionInfo(tenant);

      res.json(generateSuccessResponse({
        tenant: this.formatTenantInfo(tenant),
        realtimeStats,
        healthScore,
        subscriptionInfo,
        usageHistory: tenant.usageStats,
        recentOperations: tenant.operationLogs
      }));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('获取租户详情失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('获取租户详情失败', error));
    }
  }

  /**
   * 更新租户信息（增强版）
   */
  async updateTenant(req, res) {
    const transaction = await saasModels.sequelize.transaction();
    
    try {
      const { id } = req.params;
      const updateData = req.body;

      const tenant = await saasModels.Tenant.findByPk(id);
      if (!tenant) {
        return res.status(404).json(generateErrorResponse('租户不存在'));
      }

      // 记录变更前的状态
      const originalData = tenant.toJSON();

      // 更新租户信息
      await tenant.update(updateData, { transaction });

      // 记录变更操作
      await this.logTenantOperation(id, 'UPDATED', req.user.id, {
        changes: this.getChangedFields(originalData, updateData),
        updatedBy: req.user.username
      }, transaction);

      // 如果订阅计划发生变更，更新相关限制
      if (updateData.subscriptionPlan && updateData.subscriptionPlan !== originalData.subscriptionPlan) {
        await this.updateSubscriptionLimits(tenant, updateData.subscriptionPlan, transaction);
      }

      await transaction.commit();

      res.json(generateSuccessResponse({
        tenant: this.formatTenantInfo(tenant),
        message: '租户信息更新成功'
      }));

    } catch (error) {
      await transaction.rollback();
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('更新租户失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('更新租户失败', error));
    }
  }

  /**
   * 暂停租户
   */
  async suspendTenant(req, res) {
    try {
      const { id } = req.params;
      const { reason, suspendUntil } = req.body;

      const tenant = await saasModels.Tenant.findByPk(id);
      if (!tenant) {
        return res.status(404).json(generateErrorResponse('租户不存在'));
      }

      if (tenant.status === 'suspended') {
        return res.status(400).json(generateErrorResponse('租户已处于暂停状态'));
      }

      await tenant.update({
        status: 'suspended',
        settings: JSON.stringify({
          ...JSON.parse(tenant.settings || '{}'),
          suspension: {
            reason,
            suspendedAt: new Date(),
            suspendedBy: req.user.id,
            suspendUntil
          }
        })
      });

      // 记录暂停操作
      await this.logTenantOperation(id, 'SUSPENDED', req.user.id, {
        reason,
        suspendedBy: req.user.username,
        suspendUntil
      });

      res.json(generateSuccessResponse({
        message: '租户已暂停'
      }));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('暂停租户失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('暂停租户失败', error));
    }
  }

  /**
   * 激活租户
   */
  async activateTenant(req, res) {
    try {
      const { id } = req.params;

      const tenant = await saasModels.Tenant.findByPk(id);
      if (!tenant) {
        return res.status(404).json(generateErrorResponse('租户不存在'));
      }

      await tenant.update({
        status: 'active',
        lastActiveAt: new Date(),
        settings: JSON.stringify({
          ...JSON.parse(tenant.settings || '{}'),
          activation: {
            activatedAt: new Date(),
            activatedBy: req.user.id
          }
        })
      });

      // 记录激活操作
      await this.logTenantOperation(id, 'ACTIVATED', req.user.id, {
        activatedBy: req.user.username
      });

      res.json(generateSuccessResponse({
        message: '租户已激活'
      }));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('激活租户失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('激活租户失败', error));
    }
  }

  // ================================
  // 平台监控和分析
  // ================================

  /**
   * 平台总览仪表板
   */
  async getPlatformDashboard(req, res) {
    try {
      const { timeRange = '30d' } = req.query;
      const timeFilter = this.getTimeFilter(timeRange);

      // 基础统计
      const basicStats = await this.getPlatformBasicStats(timeFilter);
      
      // 增长趋势
      const growthTrends = await this.getPlatformGrowthTrends(timeFilter);
      
      // 收入统计
      const revenueStats = await this.getPlatformRevenueStats(timeFilter);
      
      // 活跃度分析
      const activityStats = await this.getPlatformActivityStats(timeFilter);
      
      // 订阅分析
      const subscriptionStats = await this.getSubscriptionAnalytics(timeFilter);
      
      // 系统健康度
      const systemHealth = await this.getSystemHealthMetrics();

      res.json(generateSuccessResponse({
        basicStats,
        growthTrends,
        revenueStats,
        activityStats,
        subscriptionStats,
        systemHealth,
        lastUpdated: new Date()
      }));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('获取平台仪表板失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('获取平台仪表板失败', error));
    }
  }

  /**
   * 租户使用统计分析
   */
  async getTenantUsageAnalytics(req, res) {
    try {
      const { 
        timeRange = '30d',
        metric = 'all',
        groupBy = 'day',
        tenantIds
      } = req.query;

      const timeFilter = this.getTimeFilter(timeRange);
      const tenantFilter = tenantIds ? { id: { [Op.in]: tenantIds.split(',') } } : {};

      const analytics = await this.calculateTenantUsageAnalytics(
        timeFilter,
        metric,
        groupBy,
        tenantFilter
      );

      res.json(generateSuccessResponse(analytics));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('获取租户使用分析失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('获取租户使用分析失败', error));
    }
  }

  /**
   * 系统性能监控
   */
  async getSystemMetrics(req, res) {
    try {
      const { timeRange = '1h' } = req.query;

      const metrics = {
        // 系统资源使用率
        systemResources: await this.getSystemResourceMetrics(timeRange),
        
        // API性能指标
        apiPerformance: await this.getApiPerformanceMetrics(timeRange),
        
        // 数据库性能
        databasePerformance: await this.getDatabasePerformanceMetrics(timeRange),
        
        // 错误率统计
        errorRates: await this.getErrorRateMetrics(timeRange),
        
        // 活跃连接数
        activeConnections: await this.getActiveConnectionMetrics(),
        
        // 缓存命中率
        cacheMetrics: await this.getCacheMetrics(timeRange)
      };

      res.json(generateSuccessResponse(metrics));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('获取系统指标失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('获取系统指标失败', error));
    }
  }

  // ================================
  // 订阅和计费管理
  // ================================

  /**
   * 订阅计划管理
   */
  async getSubscriptionPlans(req, res) {
    try {
      const plans = await saasModels.SubscriptionPlan.findAll({
        order: [['price', 'ASC']]
      });

      res.json(generateSuccessResponse(plans));

    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('获取订阅计划失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('获取订阅计划失败', error));
    }
  }

  /**
   * 更新租户订阅
   */
  async updateTenantSubscription(req, res) {
    const transaction = await saasModels.sequelize.transaction();
    
    try {
      const { tenantId } = req.params;
      const { subscriptionPlan, duration = 12 } = req.body;

      const tenant = await saasModels.Tenant.findByPk(tenantId);
      if (!tenant) {
        return res.status(404).json(generateErrorResponse('租户不存在'));
      }

      // 计算新的订阅周期
      const startDate = new Date();
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + duration);

      // 更新租户订阅信息
      await tenant.update({
        subscriptionPlan,
        subscriptionStartDate: startDate,
        subscriptionEndDate: endDate,
        ...this.getSubscriptionLimits(subscriptionPlan)
      }, { transaction });

      // 创建订阅变更记录
      await saasModels.SubscriptionChange.create({
        tenantId,
        fromPlan: tenant.subscriptionPlan,
        toPlan: subscriptionPlan,
        changeDate: startDate,
        changedBy: req.user.id,
        reason: req.body.reason || 'Manual update'
      }, { transaction });

      await transaction.commit();

      res.json(generateSuccessResponse({
        message: '订阅更新成功',
        subscription: {
          plan: subscriptionPlan,
          startDate,
          endDate,
          duration
        }
      }));

    } catch (error) {
      await transaction.rollback();
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('更新订阅失败:', error); } catch(_) {}

      res.status(500).json(generateErrorResponse('更新订阅失败', error));
    }
  }

  // ================================
  // 辅助方法
  // ================================

  /**
   * 格式化租户信息
   */
  formatTenantInfo(tenant) {
    const tenantData = tenant.toJSON();
    
    return {
      ...tenantData,
      settings: JSON.parse(tenantData.settings || '{}'),
      features: JSON.parse(tenantData.features || '[]'),
      subscriptionStatus: this.getSubscriptionStatus(tenant),
      healthScore: this.calculateTenantHealthScore(tenant),
      usageSummary: this.getTenantUsageSummary(tenant)
    };
  }

  /**
   * 计算租户统计信息
   */
  async calculateTenantStatistics(whereClause) {
    const [
      totalCount,
      activeCount,
      trialCount,
      suspendedCount,
      revenueSum
    ] = await Promise.all([
      saasModels.Tenant.count({ where: whereClause }),
      saasModels.Tenant.count({ where: { ...whereClause, status: 'active' } }),
      saasModels.Tenant.count({ where: { ...whereClause, subscriptionPlan: 'trial' } }),
      saasModels.Tenant.count({ where: { ...whereClause, status: 'suspended' } }),
      saasModels.Tenant.sum('totalRevenue', { where: whereClause })
    ]);

    return {
      total: totalCount,
      active: activeCount,
      trial: trialCount,
      suspended: suspendedCount,
      totalRevenue: revenueSum || 0,
      conversionRate: trialCount > 0 ? ((activeCount / trialCount) * 100).toFixed(2) : 0
    };
  }

  /**
   * 获取订阅计划限制
   */
  getSubscriptionLimits(plan) {
    const limits = {
      trial: { maxUsers: 3, maxFlocks: 5, storageLimit: 1024 * 1024 * 100, apiCallsLimit: 1000 },
      basic: { maxUsers: 10, maxFlocks: 20, storageLimit: 1024 * 1024 * 500, apiCallsLimit: 5000 },
      standard: { maxUsers: 50, maxFlocks: 100, storageLimit: 1024 * 1024 * 1024, apiCallsLimit: 20000 },
      premium: { maxUsers: 200, maxFlocks: 500, storageLimit: 1024 * 1024 * 1024 * 5, apiCallsLimit: 100000 },
      enterprise: { maxUsers: -1, maxFlocks: -1, storageLimit: -1, apiCallsLimit: -1 }
    };
    
    return limits[plan] || limits.trial;
  }

  /**
   * 获取订阅计划功能
   */
  getSubscriptionFeatures(plan) {
    const features = {
      trial: ['basic_features'],
      basic: ['basic_features', 'reports'],
      standard: ['basic_features', 'reports', 'ai_diagnosis', 'api_access'],
      premium: ['basic_features', 'reports', 'ai_diagnosis', 'api_access', 'advanced_analytics', 'priority_support'],
      enterprise: ['all_features', 'custom_branding', 'dedicated_support', 'sla_guarantee']
    };
    
    return features[plan] || features.trial;
  }

  /**
   * 记录租户操作日志
   */
  async logTenantOperation(tenantId, operation, operatorId, details, transaction) {
    await saasModels.TenantOperationLog.create({
      tenantId,
      operation,
      operatorId,
      details: JSON.stringify(details),
      timestamp: new Date()
    }, { transaction });
  }

  /**
   * 创建租户数据库
   */
  async createTenantDatabase(tenantCode) {
    const tenantDbManager = new TenantDatabaseManager();
    
    // 这里实现创建租户数据库的逻辑
    // 包括创建数据库、初始化表结构等
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.info(`创建租户数据库: smart_goose_${tenantCode.toLowerCase()}`); } catch(_) {}

  }

  /**
   * 获取时间过滤器
   */
  getTimeFilter(timeRange) {
    const now = new Date();
    const filters = {
      '1h': new Date(now.getTime() - 60 * 60 * 1000),
      '24h': new Date(now.getTime() - 24 * 60 * 60 * 1000),
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      '90d': new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    };
    
    return filters[timeRange] || filters['30d'];
  }
}

module.exports = new EnhancedPlatformController();