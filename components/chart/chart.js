// components/chart/chart.js
const { throttle, debounce } = require('../../utils/performance.js');

/**
 * 图表组件
 * 支持折线图、柱状图、饼图等多种图表类型
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图表类型
    type: {
      type: String,
      value: 'line' // line, bar, pie, area
    },
    // 图表数据
    data: {
      type: Array,
      value: []
    },
    // 图表配置
    options: {
      type: Object,
      value: {}
    },
    // 图表宽度
    width: {
      type: Number,
      value: 350
    },
    // 图表高度
    height: {
      type: Number,
      value: 250
    },
    // 是否显示动画
    animation: {
      type: Boolean,
      value: true
    },
    // 是否可交互
    interactive: {
      type: Boolean,
      value: true
    },
    // 主题色
    color: {
      type: String,
      value: '#0066CC'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    canvasId: '',
    chartInstance: null,
    // 默认配置
    defaultOptions: {
      grid: { top: 20, right: 30, bottom: 60, left: 60 },
      xAxis: { type: 'category' },
      yAxis: { type: 'value' },
      series: []
    },
    // 预设主题
    themes: {
      default: ['#0066CC', '#52C41A', '#FA8C16', '#FF4D4F', '#722ED1', '#13C2C2'],
      blue: ['#0066CC', '#1890FF', '#69C0FF', '#BAE7FF', '#E6F7FF'],
      green: ['#52C41A', '#73D13D', '#95DE64', '#B7EB8F', '#D9F7BE'],
      warm: ['#FA8C16', '#FF9C6E', '#FFBB96', '#FFD6CC', '#FFF2E8']
    },
    // 图表状态
    loading: false,
    error: null
  },

  /**
   * 数据监听器
   */
  observers: {
    'data, type, options': function(data, type, options) {
      if (data && data.length > 0) {
        this.renderChart();
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initChart();
    },

    detached() {
      this.destroyChart();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化图表
     */
    initChart() {
      const canvasId = `chart_${Math.random().toString(36).substr(2, 9)}`;
      this.setData({ canvasId });
      
      try { const logger = require('../../utils/logger.js'); logger.debug && logger.debug('[Chart] 图表初始化', canvasId); } catch(_) {}
      
      // 延迟渲染，确保DOM已创建
      setTimeout(() => {
        if (this.properties.data && this.properties.data.length > 0) {
          this.renderChart();
        }
      }, 100);
    },

    /**
     * 渲染图表
     */
    renderChart: throttle(function() {
      if (!this.data.canvasId) return;
      
      this.setData({ loading: true, error: null });
      
      try {
        const { type, data, options, width, height, color } = this.properties;
        
        // 获取canvas上下文
        this.createSelectorQuery()
          .select(`#${this.data.canvasId}`)
          .fields({ node: true, size: true })
          .exec((res) => {
            if (!res[0]) {
              try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Chart] Canvas未找到'); } catch(_) {}
              this.setData({ error: 'Canvas初始化失败', loading: false });
              return;
            }

            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            
            // 设置canvas尺寸
            const { getDeviceInfo } = require('../../utils/system-info-helper');
    const dpr = getDeviceInfo().pixelRatio;
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            ctx.scale(dpr, dpr);

            // 清除之前的内容
            ctx.clearRect(0, 0, width, height);

            // 根据图表类型渲染
            switch (type) {
              case 'line':
                this.renderLineChart(ctx, data, options, width, height, color);
                break;
              case 'bar':
                this.renderBarChart(ctx, data, options, width, height, color);
                break;
              case 'pie':
                this.renderPieChart(ctx, data, options, width, height, color);
                break;
              case 'area':
                this.renderAreaChart(ctx, data, options, width, height, color);
                break;
              default:
                try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[Chart] 不支持的图表类型', type); } catch(_) {}
            }

            this.setData({ loading: false });
            this.triggerEvent('rendered', { type, data });
          });
      } catch (error) {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Chart] 渲染失败', error); } catch(_) {}
        this.setData({ error: '图表渲染失败', loading: false });
      }
    }, 300),

    /**
     * 渲染折线图
     */
    renderLineChart(ctx, data, options, width, height, color) {
      const padding = 60;
      const chartWidth = width - padding * 2;
      const chartHeight = height - padding * 2;
      
      if (!data || data.length === 0) return;

      // 计算数据范围
      const values = data.map(item => item.value || item.y || 0);
      const minValue = Math.min(...values);
      const maxValue = Math.max(...values);
      const valueRange = maxValue - minValue || 1;

      // 设置样式
      ctx.strokeStyle = color;
      ctx.fillStyle = color;
      ctx.lineWidth = 2;
      ctx.font = '12px system-ui';

      // 绘制坐标轴
      this.drawAxis(ctx, padding, chartWidth, chartHeight, minValue, maxValue);

      // 绘制折线
      ctx.beginPath();
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth;
        const y = padding + chartHeight - ((item.value - minValue) / valueRange) * chartHeight;
        
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      ctx.stroke();

      // 绘制数据点
      ctx.fillStyle = color;
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth;
        const y = padding + chartHeight - ((item.value - minValue) / valueRange) * chartHeight;
        
        // 绘制方形数据点（遵循微信小程序Canvas规范）
        const pointSize = 8; // 4*2，保持视觉大小一致
        ctx.fillRect(x - pointSize/2, y - pointSize/2, pointSize, pointSize);
      });

      // 绘制X轴标签
      ctx.fillStyle = '#666';
      ctx.textAlign = 'center';
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth;
        const y = height - padding + 20;
        ctx.fillText(item.label || item.x || index, x, y);
      });
    },

    /**
     * 渲染柱状图
     */
    renderBarChart(ctx, data, options, width, height, color) {
      const padding = 60;
      const chartWidth = width - padding * 2;
      const chartHeight = height - padding * 2;
      
      if (!data || data.length === 0) return;

      const values = data.map(item => item.value || item.y || 0);
      const maxValue = Math.max(...values);
      const barWidth = chartWidth / data.length * 0.6;
      const barSpacing = chartWidth / data.length * 0.4;

      // 绘制坐标轴
      this.drawAxis(ctx, padding, chartWidth, chartHeight, 0, maxValue);

      // 绘制柱状图
      ctx.fillStyle = color;
      data.forEach((item, index) => {
        const barHeight = (item.value / maxValue) * chartHeight;
        const x = padding + index * (barWidth + barSpacing) + barSpacing / 2;
        const y = padding + chartHeight - barHeight;
        
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // 绘制标签
        ctx.fillStyle = '#666';
        ctx.textAlign = 'center';
        ctx.fillText(item.label || item.x || index, x + barWidth / 2, height - padding + 20);
        ctx.fillStyle = color;
      });
    },

    /**
     * 渲染饼图
     */
    renderPieChart(ctx, data, options, width, height, color) {
      const centerX = width / 2;
      const centerY = height / 2;
      const radius = Math.min(width, height) / 2 - 40;
      
      if (!data || data.length === 0) return;

      const total = data.reduce((sum, item) => sum + (item.value || 0), 0);
      let currentAngle = -Math.PI / 2; // 从顶部开始

      const colors = this.data.themes.default;

      data.forEach((item, index) => {
        const sliceAngle = (item.value / total) * 2 * Math.PI;
        const sliceColor = colors[index % colors.length];
        
        // 绘制扇形
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fillStyle = sliceColor;
        ctx.fill();
        
        // 绘制标签
        const labelAngle = currentAngle + sliceAngle / 2;
        const labelX = centerX + Math.cos(labelAngle) * (radius + 20);
        const labelY = centerY + Math.sin(labelAngle) * (radius + 20);
        
        ctx.fillStyle = '#333';
        ctx.textAlign = 'center';
        ctx.fillText(item.label || '', labelX, labelY);
        
        currentAngle += sliceAngle;
      });
    },

    /**
     * 渲染面积图
     */
    renderAreaChart(ctx, data, options, width, height, color) {
      // 先绘制折线图
      this.renderLineChart(ctx, data, options, width, height, color);
      
      // 添加面积填充
      const padding = 60;
      const chartWidth = width - padding * 2;
      const chartHeight = height - padding * 2;
      
      const values = data.map(item => item.value || item.y || 0);
      const minValue = Math.min(...values);
      const maxValue = Math.max(...values);
      const valueRange = maxValue - minValue || 1;

      // 创建渐变填充
      const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight);
      gradient.addColorStop(0, color + '40'); // 40% 透明度
      gradient.addColorStop(1, color + '10'); // 10% 透明度
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      
      // 绘制面积路径
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth;
        const y = padding + chartHeight - ((item.value - minValue) / valueRange) * chartHeight;
        
        if (index === 0) {
          ctx.moveTo(x, padding + chartHeight); // 从底部开始
          ctx.lineTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      // 闭合路径到底部
      const lastX = padding + chartWidth;
      ctx.lineTo(lastX, padding + chartHeight);
      ctx.closePath();
      ctx.fill();
    },

    /**
     * 绘制坐标轴
     */
    drawAxis(ctx, padding, chartWidth, chartHeight, minValue, maxValue) {
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      
      // X轴
      ctx.beginPath();
      ctx.moveTo(padding, padding + chartHeight);
      ctx.lineTo(padding + chartWidth, padding + chartHeight);
      ctx.stroke();
      
      // Y轴
      ctx.beginPath();
      ctx.moveTo(padding, padding);
      ctx.lineTo(padding, padding + chartHeight);
      ctx.stroke();
      
      // Y轴标签
      ctx.fillStyle = '#666';
      ctx.textAlign = 'right';
      const steps = 5;
      for (let i = 0; i <= steps; i++) {
        const value = minValue + (maxValue - minValue) * (i / steps);
        const y = padding + chartHeight - (i / steps) * chartHeight;
        ctx.fillText(Math.round(value).toString(), padding - 10, y + 4);
      }
    },

    /**
     * 销毁图表
     */
    destroyChart() {
      if (this.data.chartInstance) {
        // 清理图表实例
        this.data.chartInstance = null;
      }
      try { const logger = require('../../utils/logger.js'); logger.debug && logger.debug('[Chart] 图表已销毁'); } catch(_) {}
    },

    /**
     * 更新图表数据
     */
    updateData(newData) {
      this.setData({
        data: newData
      });
    },

    /**
     * 图表点击事件
     */
    onChartTap(e) {
      if (!this.properties.interactive) return;
      
      // 计算点击位置对应的数据项
      const { x, y } = e.detail;
      // 这里可以添加点击位置计算逻辑
      
      this.triggerEvent('tap', {
        x, y,
        data: this.properties.data
      });
    }
  }
});