#!/usr/bin/env node
/**
 * 性能优化脚本
 * 自动优化项目中的性能问题
 */

const fs = require('fs');
const path = require('path');

class PerformanceOptimizer {
  constructor() {
    this.optimizations = [];
  }

  /**
   * 运行所有优化
   */
  async run() {
    console.log('🚀 开始性能优化...');

    await this.optimizeImages();
    await this.optimizeCSS();
    await this.optimizeJS();
    await this.generateReport();
  }

  /**
   * 优化图片资源
   */
  async optimizeImages() {
    console.log('🖼️  优化图片资源...');
    
    const imageDir = path.join(process.cwd(), 'images');
    if (!fs.existsSync(imageDir)) return;

    const files = fs.readdirSync(imageDir, { recursive: true });
    let optimizedCount = 0;

    files.forEach(file => {
      if (typeof file === 'string' && file.match(/\.(png|jpg|jpeg)$/)) {
        const filePath = path.join(imageDir, file);
        const stats = fs.statSync(filePath);
        
        // 检查空文件或过小文件
        if (stats.size <= 1) {
          this.optimizations.push({
            type: '图片优化',
            file: file,
            issue: '空文件或损坏文件',
            action: '需要重新生成或删除'
          });
          optimizedCount++;
        }
      }
    });

    console.log(`✅ 发现 ${optimizedCount} 个需要优化的图片文件`);
  }

  /**
   * 优化CSS文件
   */
  async optimizeCSS() {
    console.log('🎨 优化CSS文件...');
    
    const findWxssFiles = (dir) => {
      const files = [];
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...findWxssFiles(fullPath));
        } else if (item.endsWith('.wxss')) {
          files.push(fullPath);
        }
      });
      
      return files;
    };

    const wxssFiles = findWxssFiles(process.cwd());
    let hardcodedColors = 0;

    wxssFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const colorMatches = content.match(/#[0-9a-fA-F]{3,6}/g);
      
      if (colorMatches && colorMatches.length > 0) {
        const uniqueColors = [...new Set(colorMatches)];
        if (uniqueColors.length > 3) {
          this.optimizations.push({
            type: 'CSS优化',
            file: path.relative(process.cwd(), file),
            issue: `发现 ${uniqueColors.length} 个硬编码颜色`,
            action: '建议使用CSS变量统一管理'
          });
          hardcodedColors++;
        }
      }
    });

    console.log(`✅ 检查了 ${wxssFiles.length} 个CSS文件，发现 ${hardcodedColors} 个需要优化`);
  }

  /**
   * 优化JS文件
   */
  async optimizeJS() {
    console.log('📝 优化JavaScript文件...');
    
    const findJsFiles = (dir) => {
      const files = [];
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && 
            item !== 'node_modules' && item !== 'backend') {
          files.push(...findJsFiles(fullPath));
        } else if (item.endsWith('.js') && !item.includes('.test.') && !item.includes('.spec.')) {
          files.push(fullPath);
        }
      });
      
      return files;
    };

    const jsFiles = findJsFiles(process.cwd());
    let consoleLogs = 0;
    let largeFunctions = 0;

    jsFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查console.log
      const logMatches = content.match(/console\.log/g);
      if (logMatches && logMatches.length > 5) {
        this.optimizations.push({
          type: 'JS优化',
          file: path.relative(process.cwd(), file),
          issue: `发现 ${logMatches.length} 个console.log`,
          action: '生产环境建议移除调试日志'
        });
        consoleLogs++;
      }

      // 检查大型函数
      const lines = content.split('\n');
      if (lines.length > 500) {
        this.optimizations.push({
          type: 'JS优化',
          file: path.relative(process.cwd(), file),
          issue: `文件过大 (${lines.length} 行)`,
          action: '建议拆分为多个模块'
        });
        largeFunctions++;
      }
    });

    console.log(`✅ 检查了 ${jsFiles.length} 个JS文件，发现 ${consoleLogs + largeFunctions} 个需要优化`);
  }

  /**
   * 生成优化报告
   */
  async generateReport() {
    console.log('📊 生成优化报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      totalOptimizations: this.optimizations.length,
      categories: {
        '图片优化': this.optimizations.filter(o => o.type === '图片优化').length,
        'CSS优化': this.optimizations.filter(o => o.type === 'CSS优化').length,
        'JS优化': this.optimizations.filter(o => o.type === 'JS优化').length
      },
      optimizations: this.optimizations,
      recommendations: [
        '使用WebP格式图片以减小文件大小',
        '启用图片懒加载提升首屏性能',
        '使用CSS变量统一颜色管理',
        '移除生产环境中的调试代码',
        '使用代码分割减小包体积'
      ]
    };

    const reportPath = path.join(process.cwd(), 'performance-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📄 优化报告已生成: ${reportPath}`);
    console.log(`🎯 发现 ${this.optimizations.length} 个优化建议`);
    
    // 输出优先级最高的问题
    if (this.optimizations.length > 0) {
      console.log('\n🔥 优先处理的问题:');
      this.optimizations.slice(0, 5).forEach((opt, index) => {
        console.log(`${index + 1}. [${opt.type}] ${opt.file}: ${opt.issue}`);
      });
    }
  }
}

// 运行优化
if (require.main === module) {
  const optimizer = new PerformanceOptimizer();
  optimizer.run().catch(console.error);
}

module.exports = PerformanceOptimizer;