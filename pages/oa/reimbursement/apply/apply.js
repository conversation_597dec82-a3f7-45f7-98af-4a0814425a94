// pages/oa/reimbursement/apply/apply.js
const { getCurrentUserInfo } = require('../../../../utils/user-info.js');
const { isLoggedIn, getCurrentUser } = require('../../../../utils/auth-helper');

Page({
  data: {
    // 表单数据
    formData: {
      title: '',
      description: '',
      category: 'office',
      amount: '',
      expenseDate: '',
      businessPurpose: '',
      paymentMethod: 'bank_transfer'
    },
    
    // 表单配置
    categories: [
      { value: 'travel', label: '差旅费' },
      { value: 'office', label: '办公用品' },
      { value: 'meal', label: '餐费' },
      { value: 'transport', label: '交通费' },
      { value: 'communication', label: '通讯费' },
      { value: 'training', label: '培训费' },
      { value: 'entertainment', label: '招待费' },
      { value: 'other', label: '其他' }
    ],

    paymentMethods: [
      { value: 'bank_transfer', label: '银行转账' },
      { value: 'cash', label: '现金' },
      { value: 'alipay', label: '支付宝' },
      { value: 'wechat', label: '微信' }
    ],

    // 当前选中的索引
    categoryIndex: 1, // 默认为'office'
    paymentMethodIndex: 0, // 默认为'bank_transfer'

    // 显示用的标签
    selectedCategoryLabel: '办公用品',
    selectedPaymentMethodLabel: '银行转账',

    // 页面状态
    loading: false,
    submitting: false,
    savingDraft: false,
    
    // 错误信息
    errors: {}
  },

  onLoad: function (options) {
    this.checkUserPermissions();
  },

  // 检查用户权限
  checkUserPermissions: function() {
    if (!isLoggedIn()) {
      wx.showModal({
        title: '提示',
        content: '请先登录后使用此功能',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    const userInfo = getCurrentUser() || getCurrentUserInfo();
    this.setData({ userInfo });
  },

  // 输入框变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    if (field === 'category') {
      const categoryIndex = value;
      const selectedCategoryLabel = categoryIndex >= 0 ? this.data.categories[categoryIndex].label : '请选择类别';
      this.setData({
        [`formData.${field}`]: categoryIndex >= 0 ? this.data.categories[categoryIndex].value : '',
        categoryIndex,
        selectedCategoryLabel
      });
    } else if (field === 'paymentMethod') {
      const paymentMethodIndex = value;
      const selectedPaymentMethod = this.data.paymentMethods[paymentMethodIndex];
      this.setData({
        [`formData.${field}`]: selectedPaymentMethod.value,
        paymentMethodIndex,
        selectedPaymentMethodLabel: selectedPaymentMethod.label
      });
    } else {
      this.setData({
        [`formData.${field}`]: value
      });
    }
    
    // 清除错误信息
    if (this.data.errors[field]) {
      this.setData({
        [`errors.${field}`]: ''
      });
    }
  },

  // 日期选择
  onDateChange: function(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      [`formData.${field}`]: e.detail.value
    });
  },

  // 表单验证
  validateForm: function() {
    const { formData } = this.data;
    const errors = {};

    if (!formData.title.trim()) {
      errors.title = '请输入报销标题';
    }

    if (!formData.category) {
      errors.category = '请选择报销类别';
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      errors.amount = '请输入有效的报销金额';
    }

    if (!formData.expenseDate) {
      errors.expenseDate = '请选择费用发生日期';
    }

    if (!formData.businessPurpose.trim()) {
      errors.businessPurpose = '请说明业务目的';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  // 保存草稿
  onSaveDraft: function() {
    this.setData({ savingDraft: true });

    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('保存草稿', this.data.formData); } catch(_) {}

    setTimeout(() => {
      this.setData({ savingDraft: false });
      wx.showToast({
        title: '草稿已保存',
        icon: 'success'
      });
    }, 1000);
  },

  // 提交申请
  onSubmit: function() {
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    // 构造提交数据
    const submitData = {
      ...this.data.formData,
      applicant: this.data.userInfo?.name || '当前用户',
      submitTime: new Date().toISOString(),
      status: 'pending'
    };

    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('提交报销申请', submitData); } catch(_) {}

    // 模拟API调用
    setTimeout(() => {
      this.setData({ submitting: false });
      
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  }
});