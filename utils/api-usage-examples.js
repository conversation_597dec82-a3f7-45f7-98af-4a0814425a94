/**
 * 📚 API使用示例
 * 展示如何使用新的环境配置管理器和统一API客户端
 */

// 引入统一API客户端
const { apiClient, get, post, put, delete: del } = require('./unified-api-client.js');
const { environmentConfig } = require('./environment-config.js');

/**
 * 🏠 首页数据获取示例
 */
const HomePageExample = {
  async loadHomeData() {
    try {
      // 方法1: 使用便捷函数
      const homeData = await get('/home/<USER>');
      
      // 方法2: 使用API客户端实例
      const announcements = await apiClient.get('/home/<USER>');
      
      // 方法3: 使用完整URL
      const weather = await get(environmentConfig.getApiEndpointUrl('/home/<USER>'));
      
      return {
        homeData,
        announcements,
        weather
      };
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('首页数据加载失败', error); } catch(_) {}
      throw error;
    }
  }
};

/**
 * 🦢 鹅群管理示例
 */
const FlockManagementExample = {
  // 获取鹅群列表
  async getFlockList(params = {}) {
    try {
      const response = await get('/flocks', {
        data: params,
        timeout: 8000 // 自定义超时时间
      });
      
      return response.data || response;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('获取鹅群列表失败', error); } catch(_) {}
      
      // 根据错误类型显示不同提示
      if (error.type === 'NETWORK_ERROR') {
        wx.showToast({
          title: '网络连接失败，请检查网络',
          icon: 'none'
        });
      } else if (error.type === 'TIMEOUT_ERROR') {
        wx.showToast({
          title: '请求超时，请稍后重试',
          icon: 'none'
        });
      }
      
      throw error;
    }
  },

  // 创建新鹅群
  async createFlock(flockData) {
    try {
      const response = await post('/flocks', flockData);
      
      wx.showToast({
        title: '鹅群创建成功',
        icon: 'success'
      });
      
      return response;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('创建鹅群失败', error); } catch(_) {}
      
      wx.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      });
      
      throw error;
    }
  },

  // 更新鹅群信息
  async updateFlock(flockId, updateData) {
    try {
      const response = await put(`/flocks/${flockId}`, updateData);
      
      wx.showToast({
        title: '更新成功',
        icon: 'success'
      });
      
      return response;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('更新鹅群失败', error); } catch(_) {}
      throw error;
    }
  },

  // 删除鹅群
  async deleteFlock(flockId) {
    try {
      await del(`/flocks/${flockId}`);
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('删除鹅群失败', error); } catch(_) {}
      throw error;
    }
  }
};

/**
 * 🤖 AI服务示例
 */
const AIServiceExample = {
  // AI健康诊断
  async diagnoseHealth(symptoms) {
    try {
      const response = await post('/ai/health', {
        symptoms,
        timestamp: new Date().toISOString()
      });
      
      return response.diagnosis;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('AI诊断失败', error); } catch(_) {}
      throw error;
    }
  },

  // 图片识别
  async recognizeImage(imagePath) {
    try {
      // 使用上传功能
      const response = await apiClient.upload('/ai/image', imagePath, {
        name: 'image',
        formData: {
          type: 'recognition'
        }
      });
      
      return response.result;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('图片识别失败', error); } catch(_) {}
      throw error;
    }
  }
};

/**
 * 👤 用户认证示例
 */
const AuthExample = {
  // 用户登录
  async login(credentials) {
    try {
      const response = await post('/auth/login', credentials);
      
      // 保存认证信息
      wx.setStorageSync('token', response.token);
      wx.setStorageSync('userInfo', response.user);
      
      // 更新全局数据
      const app = getApp();
      app.globalData.userInfo = response.user;
      app.globalData.token = response.token;
      
      return response;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('登录失败', error); } catch(_) {}
      
      if (error.type === 'BUSINESS_ERROR') {
        wx.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        });
      }
      
      throw error;
    }
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      const response = await get('/auth/userinfo');
      return response.user || response;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('获取用户信息失败', error); } catch(_) {}
      throw error;
    }
  }
};

/**
 * 🏪 商城示例
 */
const ShopExample = {
  // 获取商品列表
  async getProducts(params = {}) {
    try {
      const response = await get('/shop/products', {
        data: {
          page: 1,
          limit: 20,
          ...params
        }
      });
      
      return response.products || response.data;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('获取商品列表失败', error); } catch(_) {}
      throw error;
    }
  },

  // 添加到购物车
  async addToCart(productId, quantity = 1) {
    try {
      const response = await post('/shop/cart', {
        productId,
        quantity
      });
      
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });
      
      return response;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('添加购物车失败', error); } catch(_) {}
      throw error;
    }
  }
};

/**
 * 📊 统计信息示例
 */
const StatsExample = {
  // 获取API使用统计
  getAPIStats() {
    const stats = apiClient.getStats();
    try { const logger = require('./logger.js'); logger.debug && logger.debug('API客户端统计', stats); } catch(_) {}
    return stats;
  },

  // 获取环境信息
  getEnvironmentInfo() {
    const envInfo = {
      environment: environmentConfig.getEnvironment(),
      environmentName: environmentConfig.getEnvironmentName(),
      apiBaseUrl: environmentConfig.getApiBaseUrl(),
      isDevelopment: environmentConfig.isDevelopment(),
      isProduction: environmentConfig.isProduction(),
      features: environmentConfig.getFeatures()
    };
    
    try { const logger = require('./logger.js'); logger.debug && logger.debug('环境信息', envInfo); } catch(_) {}
    return envInfo;
  }
};

/**
 * 🔧 页面使用示例
 */
const PageUsageExample = {
  // 在页面的onLoad中使用
  onLoad() {
    // 输出环境信息
    try { const logger = require('./logger.js'); logger.debug && logger.debug('当前环境', environmentConfig.getEnvironmentName()); } catch(_) {}
    
    // 加载页面数据
    this.loadPageData();
  },

  async loadPageData() {
    try {
      // 显示加载提示
      wx.showLoading({
        title: '加载中...'
      });

      // 并发请求多个API
      const [homeData, flocks, userInfo] = await Promise.all([
        HomePageExample.loadHomeData(),
        FlockManagementExample.getFlockList(),
        AuthExample.getUserInfo()
      ]);

      // 更新页面数据
      this.setData({
        homeData,
        flocks,
        userInfo,
        loaded: true
      });

    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('页面数据加载失败', error); } catch(_) {}
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 处理用户操作
  async handleUserAction() {
    try {
      // 检查环境
      if (environmentConfig.isDevelopment()) {
        try { const logger = require('./logger.js'); logger.debug && logger.debug('开发环境特殊处理'); } catch(_) {}
      }

      // 执行API调用
      const result = await FlockManagementExample.createFlock({
        name: '新鹅群',
        count: 100
      });

      // 处理结果
      try { const logger = require('./logger.js'); logger.debug && logger.debug('操作成功', result); } catch(_) {}

    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('操作失败', error); } catch(_) {}
    }
  }
};

/**
 * 🛠️ 工具函数示例
 */
const UtilityExample = {
  // 环境相关工具
  getCurrentEnvironment() {
    return {
      env: environmentConfig.getEnvironment(),
      name: environmentConfig.getEnvironmentName(),
      isDev: environmentConfig.isDevelopment(),
      isProd: environmentConfig.isProduction()
    };
  },

  // API相关工具
  buildApiUrl(path) {
    return environmentConfig.getApiEndpointUrl(path);
  },

  // 请求配置工具
  getRequestConfig() {
    return environmentConfig.getRequestConfig();
  },

  // 域名白名单工具
  getDomainWhitelist() {
    return environmentConfig.getDomainWhitelist();
  }
};

// 导出所有示例
module.exports = {
  HomePageExample,
  FlockManagementExample,
  AIServiceExample,
  AuthExample,
  ShopExample,
  StatsExample,
  PageUsageExample,
  UtilityExample
};