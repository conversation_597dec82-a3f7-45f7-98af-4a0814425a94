<!-- pages/oa/oa.wxml -->
<view class="oa-container">
  
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">OA办公系统</text>
    <text class="page-subtitle">智能办公，高效管理</text>
  </view>

  <!-- 统计概览 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-card approvals" bindtap="onStatCardTap" data-type="approvals">
        <view class="stat-icon">
          <image src="/images/icons/check-circle.png" mode="aspectFit"></image>
        </view>
        <view class="stat-content">
          <text class="stat-number">{{oaStats.pendingApprovals}}</text>
          <text class="stat-label">待审批</text>
        </view>
      </view>
      
      <view class="stat-card tasks" bindtap="onStatCardTap" data-type="tasks">
        <view class="stat-icon">
          <image src="/images/icons/list.png" mode="aspectFit"></image>
        </view>
        <view class="stat-content">
          <text class="stat-number">{{oaStats.todayTasks}}</text>
          <text class="stat-label">今日任务</text>
        </view>
      </view>
      
      <view class="stat-card expense" bindtap="onStatCardTap" data-type="expense">
        <view class="stat-icon">
          <image src="/images/icons/package.png" mode="aspectFit"></image>
        </view>
        <view class="stat-content">
          <text class="stat-number">{{oaStats.thisMonthExpense}}</text>
          <text class="stat-label">本月支出</text>
        </view>
      </view>
      
      <view class="stat-card notices" bindtap="onStatCardTap" data-type="notices">
        <view class="stat-icon">
          <image src="/images/icons/star.png" mode="aspectFit"></image>
        </view>
        <view class="stat-content">
          <text class="stat-number">{{oaStats.unreadNotices}}</text>
          <text class="stat-label">未读通知</text>
        </view>
        <view wx:if="{{oaStats.unreadNotices > 0}}" class="stat-badge">{{oaStats.unreadNotices}}</view>
      </view>
    </view>
  </view>

  <!-- 主要功能模块 -->
  <view class="modules-section">
    <view class="section-title">
      <text>功能模块</text>
    </view>
    <view class="modules-grid">
      <block wx:for="{{mainModules}}" wx:key="id">
        <view class="module-item" 
              bindtap="onModuleTap" 
              data-url="{{item.url}}"
              style="border-left: 4rpx solid {{item.color}};">
          <view class="module-icon">
            <image src="{{item.icon}}" mode="aspectFit"></image>
            <view wx:if="{{item.badge}}" class="module-badge">{{item.badge}}</view>
          </view>
          <view class="module-content">
            <text class="module-title">{{item.title}}</text>
            <text class="module-description">{{item.description}}</text>
          </view>
          <view class="module-arrow">
            <image src="/images/icons/eye.png" mode="aspectFit"></image>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-section">
    <view class="section-title">
      <text>快捷操作</text>
    </view>
    <view class="quick-actions-grid">
      <block wx:for="{{quickActions}}" wx:key="id">
        <view class="quick-action-item" 
              bindtap="onQuickActionTap" 
              data-url="{{item.url}}"
              style="background: linear-gradient(135deg, {{item.color}}15 0%, {{item.color}}05 100%);">
          <view class="action-icon" style="background-color: {{item.color}}10;">
            <image src="{{item.icon}}" mode="aspectFit"></image>
          </view>
          <text class="action-title">{{item.title}}</text>
        </view>
      </block>
    </view>
  </view>

  <!-- 最近动态 -->
  <view class="activities-section">
    <view class="section-header">
      <text class="section-title">最近动态</text>
      <text class="view-all-btn" bindtap="onViewAllActivities">查看全部</text>
    </view>
    
    <view class="activities-list">
      <block wx:for="{{recentActivities}}" wx:key="id">
        <view class="activity-item" bindtap="onActivityTap" data-activity="{{item}}">
          <view class="activity-icon {{item.type}}">
            <image src="/images/icons/clock.png" mode="aspectFit"></image>
          </view>
          <view class="activity-content">
            <text class="activity-title">{{item.title}}</text>
            <text class="activity-description">{{item.description}}</text>
            <text class="activity-time">{{item.time}}</text>
          </view>
          <view class="activity-status {{item.status}}">
            <text>{{item.status === 'approved' ? '已通过' : item.status === 'pending' ? '待处理' : '已提交'}}</text>
          </view>
        </view>
      </block>
    </view>
    
    <view wx:if="{{recentActivities.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/icons/search.png" mode="aspectFit"></image>
      <text class="empty-text">暂无最近动态</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

</view>