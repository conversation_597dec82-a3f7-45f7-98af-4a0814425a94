@import '/styles/oa-common.wxss';
/* pages/oa/finance/overview/overview.wxss */

.finance-overview-container {
  padding: 20rpx 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header-actions {
  z-index: 2;
}

.time-range-picker {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.time-range-picker image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

/* 快捷操作 */
.quick-actions-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.export-btn {
  font-size: 26rpx;
  color: #0066CC;
  background: rgba(0, 102, 204, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.quick-action-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.quick-action-item:active {
  transform: scale(0.98);
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.action-icon image {
  width: 100%;
  height: 100%;
}

.action-content {
  flex: 1;
}

.action-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.action-description {
  font-size: 24rpx;
  color: #666;
}

.action-arrow {
  color: #999;
  font-size: 24rpx;
}

/* 财务概览 */
.overview-section {
  margin-bottom: 30rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.overview-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.overview-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.overview-card.revenue {
  border-left: 4rpx solid #00A86B;
}

.overview-card.expense {
  border-left: 4rpx solid #FF6B35;
}

.overview-card.profit {
  border-left: 4rpx solid #0066CC;
}

.overview-card.pending {
  border-left: 4rpx solid #FFA500;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.card-label {
  font-size: 24rpx;
  color: #666;
}

.card-trend {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.card-trend.positive {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.card-trend.negative {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
}

.card-trend.warning {
  background: rgba(255, 165, 0, 0.1);
  color: #FFA500;
}

.card-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 趋势图表 */
.trend-section {
  margin-bottom: 30rpx;
}

.chart-container {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-placeholder {
  height: 300rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.chart-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.chart-desc {
  font-size: 24rpx;
  color: #666;
}

/* 支出分类 */
.expense-categories-section {
  margin-bottom: 30rpx;
}

.categories-list {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.category-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:active {
  background-color: #f8f9fa;
}

.category-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.category-content {
  flex: 1;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.category-name {
  font-size: 28rpx;
  color: #333;
}

.category-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.category-progress {
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-right: 12rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.category-percentage {
  font-size: 24rpx;
  color: #666;
  min-width: 60rpx;
  text-align: right;
}

/* 财务提醒 */
.alerts-section {
  margin-bottom: 30rpx;
}

.alerts-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.alert-item {
  padding: 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-item:active {
  background-color: #f8f9fa;
}

.alert-item.warning {
  border-left: 4rpx solid #FFA500;
}

.alert-item.info {
  border-left: 4rpx solid #0066CC;
}

.alert-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-icon image {
  width: 28rpx;
  height: 28rpx;
}

.alert-content {
  flex: 1;
}

.alert-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.alert-message {
  font-size: 24rpx;
  color: #666;
}

.alert-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.alert-arrow image {
  width: 100%;
  height: 100%;
}

/* 交易记录 */
.transactions-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.view-all-btn {
  font-size: 26rpx;
  color: #0066CC;
}

.transactions-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.transaction-item {
  padding: 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item:active {
  background-color: #f8f9fa;
}

.transaction-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transaction-icon.income {
  background: rgba(46, 204, 113, 0.1);
}

.transaction-icon.expense {
  background: rgba(255, 107, 53, 0.1);
}

.transaction-icon image {
  width: 28rpx;
  height: 28rpx;
}

.transaction-content {
  flex: 1;
}

.transaction-description {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.transaction-meta {
  display: flex;
  gap: 20rpx;
}

.transaction-category,
.transaction-date {
  font-size: 22rpx;
  color: #999;
}

.transaction-amount {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
  min-width: 120rpx;
  text-align: right;
}

.transaction-amount.income {
  color: #2ecc71;
}

.transaction-amount.expense {
  color: #FF6B35;
}

.transaction-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.transaction-status.completed {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.transaction-status.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #f39c12;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  min-width: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}