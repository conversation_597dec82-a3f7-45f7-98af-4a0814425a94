#!/usr/bin/env node
// 数据库初始化脚本

const mysql = require("mysql2/promise");
const fs = require("fs").promises;
const path = require("path");

class DatabaseSetup {
  constructor() {
    this.rootConfig = {
      host: "localhost",
      port: 3306,
      user: "root",
      password: "", // 如果root有密码，请在这里设置
      charset: "utf8mb4",
    };

    this.dbConfig = {
      host: "localhost",
      port: 3306,
      user: "zhihuiyange",
      password: "zhihuiyange123",
      database: "zhihuiyange_local",
      charset: "utf8mb4",
    };
  }

  async createDatabase() {
    console.log("🗄️ 创建数据库...");

    let connection;
    try {
      // 尝试连接MySQL
      connection = await mysql.createConnection(this.rootConfig);
      console.log("✅ 连接到MySQL服务器成功");

      // 创建数据库
      await connection.execute(
        `CREATE DATABASE IF NOT EXISTS zhihuiyange_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`,
      );
      console.log("✅ 数据库 zhihuiyange_local 创建成功");

      // 创建用户（如果不存在）
      try {
        await connection.execute(
          `CREATE USER IF NOT EXISTS 'zhihuiyange'@'localhost' IDENTIFIED BY 'zhihuiyange123'`,
        );
        console.log("✅ 用户 zhihuiyange 创建成功");
      } catch (error) {
        if (error.code !== "ER_CANNOT_USER") {
          console.log("⚠️ 用户可能已存在:", error.message);
        }
      }

      // 授权
      await connection.execute(
        `GRANT ALL PRIVILEGES ON zhihuiyange_local.* TO 'zhihuiyange'@'localhost'`,
      );
      await connection.execute(`FLUSH PRIVILEGES`);
      console.log("✅ 用户权限设置成功");
    } catch (error) {
      console.error("❌ 数据库创建失败:", error.message);

      if (error.code === "ER_ACCESS_DENIED_ERROR") {
        console.log("💡 请确保MySQL root用户可以访问，或者手动创建数据库：");
        console.log("   mysql -u root -p");
        console.log(
          "   CREATE DATABASE zhihuiyange_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;",
        );
        console.log(
          '   CREATE USER "zhihuiyange"@"localhost" IDENTIFIED BY "zhihuiyange123";',
        );
        console.log(
          '   GRANT ALL PRIVILEGES ON zhihuiyange_local.* TO "zhihuiyange"@"localhost";',
        );
        console.log("   FLUSH PRIVILEGES;");
      }

      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async testConnection() {
    console.log("🔗 测试数据库连接...");

    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      console.log("✅ 数据库连接测试成功");

      // 测试查询
      const [rows] = await connection.execute("SELECT 1 as test");
      console.log("✅ 数据库查询测试成功");

      return true;
    } catch (error) {
      console.error("❌ 数据库连接测试失败:", error.message);
      return false;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async createTables() {
    console.log("📋 创建数据表...");

    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);

      // 创建用户表
      const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          phone VARCHAR(20),
          role ENUM('admin', 'manager', 'user') DEFAULT 'user',
          farm_name VARCHAR(100),
          createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      await connection.execute(createUsersTable);
      console.log("✅ 用户表创建成功");

      // 创建健康记录表
      const createHealthRecordsTable = `
        CREATE TABLE IF NOT EXISTS health_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          goose_id VARCHAR(50),
          symptoms TEXT,
          diagnosis TEXT,
          treatment TEXT,
          status ENUM('healthy', 'sick', 'recovering', 'dead') DEFAULT 'healthy',
          record_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      await connection.execute(createHealthRecordsTable);
      console.log("✅ 健康记录表创建成功");

      // 创建生产记录表
      const createProductionRecordsTable = `
        CREATE TABLE IF NOT EXISTS production_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          egg_count INT DEFAULT 0,
          feed_consumption DECIMAL(10,2) DEFAULT 0,
          temperature DECIMAL(5,2),
          humidity DECIMAL(5,2),
          notes TEXT,
          recorded_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      await connection.execute(createProductionRecordsTable);
      console.log("✅ 生产记录表创建成功");

      // 创建库存记录表
      const createInventoryRecordsTable = `
        CREATE TABLE IF NOT EXISTS inventory_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          type ENUM('goose', 'egg', 'feed') NOT NULL,
          quantity INT NOT NULL,
          unit VARCHAR(20) DEFAULT '只',
          record_date DATE,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      await connection.execute(createInventoryRecordsTable);
      console.log("✅ 库存记录表创建成功");
    } catch (error) {
      console.error("❌ 创建数据表失败:", error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async insertTestData() {
    console.log("📝 插入测试数据...");

    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);

      // 插入测试用户（适配现有表结构）
      const insertUsers = `
        INSERT IGNORE INTO users (username, password, email, role) VALUES
        ('admin', '$2a$10$rOzJqQjQjQjQjQjQjQjQjOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '<EMAIL>', 'admin'),
        ('manager', '$2a$10$rOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '<EMAIL>', 'manager'),
        ('demo', '$2a$10$rOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '<EMAIL>', 'user');
      `;

      await connection.execute(insertUsers);
      console.log("✅ 测试用户数据插入成功");

      // 插入测试健康记录
      const insertHealthRecords = `
        INSERT IGNORE INTO health_records (user_id, goose_id, symptoms, diagnosis, treatment, status, record_date) VALUES
        (3, 'G001', '精神不振，食欲下降', '感冒', '保温，给予维生素C', 'recovering', '2023-12-01'),
        (3, 'G002', '正常', '健康检查', '无需治疗', 'healthy', '2023-12-01'),
        (3, 'G003', '腹泻', '肠胃炎', '给予益生菌，调整饲料', 'recovering', '2023-12-02');
      `;

      await connection.execute(insertHealthRecords);
      console.log("✅ 测试健康记录数据插入成功");

      // 插入测试生产记录
      const insertProductionRecords = `
        INSERT IGNORE INTO production_records (user_id, egg_count, feed_consumption, temperature, humidity, notes, recorded_date) VALUES
        (3, 120, 45.5, 25.5, 65.0, '天气晴朗，鹅群状态良好', '2023-12-01'),
        (3, 115, 46.0, 24.8, 68.0, '有轻微降温，增加了保温措施', '2023-12-02'),
        (3, 125, 44.8, 26.2, 62.0, '产蛋量有所提升', '2023-12-03');
      `;

      await connection.execute(insertProductionRecords);
      console.log("✅ 测试生产记录数据插入成功");

      // 插入测试库存记录
      const insertInventoryRecords = `
        INSERT IGNORE INTO inventory_records (user_id, type, quantity, unit, record_date, notes) VALUES
        (3, 'goose', 500, '只', '2023-12-01', '成年鹅存栏'),
        (3, 'egg', 1200, '个', '2023-12-01', '鹅蛋库存'),
        (3, 'feed', 2000, 'kg', '2023-12-01', '饲料库存');
      `;

      await connection.execute(insertInventoryRecords);
      console.log("✅ 测试库存记录数据插入成功");
    } catch (error) {
      console.error("❌ 插入测试数据失败:", error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async setup() {
    console.log("🚀 开始数据库初始化...\n");

    try {
      // 1. 创建数据库和用户
      await this.createDatabase();

      // 2. 测试连接
      const connected = await this.testConnection();
      if (!connected) {
        throw new Error("数据库连接失败");
      }

      // 3. 创建表
      await this.createTables();

      // 4. 插入测试数据
      await this.insertTestData();

      console.log("\n🎉 数据库初始化完成！");
      console.log("📊 数据库信息:");
      console.log(`   数据库名: ${this.dbConfig.database}`);
      console.log(`   用户名: ${this.dbConfig.user}`);
      console.log(`   主机: ${this.dbConfig.host}:${this.dbConfig.port}`);
    } catch (error) {
      console.error("\n❌ 数据库初始化失败:", error.message);
      process.exit(1);
    }
  }
}

// 运行初始化
if (require.main === module) {
  const setup = new DatabaseSetup();
  setup.setup().catch(console.error);
}

module.exports = DatabaseSetup;
