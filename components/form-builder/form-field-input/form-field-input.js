// components/form-builder/form-field-input/form-field-input.js
Component({
  properties: {
    field: {
      type: Object,
      value: {}
    }
  },

  methods: {
    onInput(e) {
      this.triggerEvent('change', {
        field: this.properties.field,
        value: e.detail.value
      });
    },

    onBlur(e) {
      this.triggerEvent('blur', {
        field: this.properties.field,
        value: e.detail.value
      });
    }
  }
});