/**
 * 统一权限管理工具 - 小程序前端
 * Unified Permission Management Utils for Mini Program
 * 
 * 与后端权限系统保持一致，提供前端权限验证和界面控制
 */

// 权限常量定义 - 与后端保持完全一致
const PERMISSIONS = {
  // ====================
  // 平台级权限 (SAAS管理)
  // ====================
  
  // 超级管理员权限
  PLATFORM_SUPER_ADMIN: "platform:super_admin",
  PLATFORM_ADMIN: "platform:admin",
  PLATFORM_OPERATOR: "platform:operator",
  PLATFORM_SUPPORT: "platform:support",
  
  // 跨租户权限
  CROSS_TENANT_READ: "platform:cross_tenant_read",
  CROSS_TENANT_WRITE: "platform:cross_tenant_write",
  
  // 租户管理权限
  TENANT_CREATE: "platform:tenant:create",
  TENANT_UPDATE: "platform:tenant:update",
  TENANT_DELETE: "platform:tenant:delete",
  TENANT_VIEW: "platform:tenant:view",
  TENANT_SUSPEND: "platform:tenant:suspend",
  TENANT_ACTIVATE: "platform:tenant:activate",
  
  // 订阅管理权限
  SUBSCRIPTION_MANAGE: "platform:subscription:manage",
  SUBSCRIPTION_VIEW: "platform:subscription:view",
  
  // 平台分析权限
  PLATFORM_ANALYTICS: "platform:analytics:view",
  PLATFORM_REPORTS: "platform:reports:view",
  
  // 系统监控权限
  SYSTEM_MONITOR: "platform:system:monitor",
  SYSTEM_CONFIG: "platform:system:config",
  
  // ====================
  // 租户级权限 (企业内部)
  // ====================
  
  // 基础资源权限
  FLOCK_VIEW: "tenant:flock:view",
  FLOCK_CREATE: "tenant:flock:create",
  FLOCK_UPDATE: "tenant:flock:update",
  FLOCK_DELETE: "tenant:flock:delete",
  
  // 健康管理权限
  HEALTH_VIEW: "tenant:health:view",
  HEALTH_CREATE: "tenant:health:create",
  HEALTH_UPDATE: "tenant:health:update",
  HEALTH_DELETE: "tenant:health:delete",
  
  // 生产管理权限
  PRODUCTION_VIEW: "tenant:production:view",
  PRODUCTION_CREATE: "tenant:production:create",
  PRODUCTION_UPDATE: "tenant:production:update",
  PRODUCTION_DELETE: "tenant:production:delete",
  
  // 库存管理权限
  INVENTORY_VIEW: "tenant:inventory:view",
  INVENTORY_CREATE: "tenant:inventory:create",
  INVENTORY_UPDATE: "tenant:inventory:update",
  INVENTORY_DELETE: "tenant:inventory:delete",
  
  // 用户管理权限
  USER_VIEW: "tenant:user:view",
  USER_CREATE: "tenant:user:create",
  USER_UPDATE: "tenant:user:update",
  USER_DELETE: "tenant:user:delete",
  
  // 商城管理权限
  SHOP_VIEW: "tenant:shop:view",
  SHOP_CREATE: "tenant:shop:create",
  SHOP_UPDATE: "tenant:shop:update",
  SHOP_DELETE: "tenant:shop:delete",
  
  // 财务管理权限
  FINANCE_VIEW: "tenant:finance:view",
  FINANCE_CREATE: "tenant:finance:create",
  FINANCE_UPDATE: "tenant:finance:update",
  FINANCE_DELETE: "tenant:finance:delete",
  FINANCE_APPROVE: "tenant:finance:approve",
  FINANCE_EXPORT: "tenant:finance:export",
  
  // OA办公权限
  OA_ACCESS: "tenant:oa:access",
  OA_ADMIN: "tenant:oa:admin",
  TASK_VIEW: "tenant:task:view",
  TASK_CREATE: "tenant:task:create",
  TASK_UPDATE: "tenant:task:update",
  TASK_DELETE: "tenant:task:delete",
  TASK_ASSIGN: "tenant:task:assign",
  
  // 审批权限
  APPROVAL_VIEW: "tenant:approval:view",
  APPROVAL_PROCESS: "tenant:approval:process",
  APPROVAL_DELEGATE: "tenant:approval:delegate",
  APPROVAL_WORKFLOW_MANAGE: "tenant:approval:workflow_manage",
  
  // AI服务权限
  AI_DIAGNOSIS: "tenant:ai:diagnosis",
  AI_CHAT: "tenant:ai:chat",
  AI_CONFIG: "tenant:ai:config",
  AI_STATS: "tenant:ai:stats",
  
  // 数据权限
  DATA_EXPORT: "tenant:data:export",
  DATA_IMPORT: "tenant:data:import",
  DATA_BACKUP: "tenant:data:backup",
  
  // 租户内管理权限
  TENANT_MANAGEMENT: "tenant:management",
  STAFF_MANAGE: "tenant:staff:manage",
  ROLE_MANAGE: "tenant:role:manage",
  SETTINGS_MANAGE: "tenant:settings:manage",
  REPORTS_VIEW: "tenant:reports:view",
};

// 角色权限映射 - 与后端保持一致
const ROLE_PERMISSIONS = {
  // 平台级角色
  platform_super_admin: Object.values(PERMISSIONS),
  platform_admin: [
    PERMISSIONS.PLATFORM_ADMIN,
    PERMISSIONS.CROSS_TENANT_READ,
    PERMISSIONS.TENANT_CREATE,
    PERMISSIONS.TENANT_UPDATE,
    PERMISSIONS.TENANT_VIEW,
    PERMISSIONS.TENANT_SUSPEND,
    PERMISSIONS.TENANT_ACTIVATE,
    PERMISSIONS.SUBSCRIPTION_MANAGE,
    PERMISSIONS.SUBSCRIPTION_VIEW,
    PERMISSIONS.PLATFORM_ANALYTICS,
    PERMISSIONS.PLATFORM_REPORTS,
    PERMISSIONS.SYSTEM_MONITOR,
  ],
  platform_operator: [
    PERMISSIONS.PLATFORM_OPERATOR,
    PERMISSIONS.CROSS_TENANT_READ,
    PERMISSIONS.TENANT_VIEW,
    PERMISSIONS.PLATFORM_ANALYTICS,
    PERMISSIONS.SYSTEM_MONITOR,
    PERMISSIONS.SYSTEM_CONFIG,
  ],
  platform_support: [
    PERMISSIONS.PLATFORM_SUPPORT,
    PERMISSIONS.TENANT_VIEW,
    PERMISSIONS.SUBSCRIPTION_VIEW,
  ],
  
  // 租户级角色 - 完整权限集合
  owner: [
    // 所有租户级权限
    PERMISSIONS.FLOCK_VIEW, PERMISSIONS.FLOCK_CREATE, PERMISSIONS.FLOCK_UPDATE, PERMISSIONS.FLOCK_DELETE,
    PERMISSIONS.HEALTH_VIEW, PERMISSIONS.HEALTH_CREATE, PERMISSIONS.HEALTH_UPDATE, PERMISSIONS.HEALTH_DELETE,
    PERMISSIONS.PRODUCTION_VIEW, PERMISSIONS.PRODUCTION_CREATE, PERMISSIONS.PRODUCTION_UPDATE, PERMISSIONS.PRODUCTION_DELETE,
    PERMISSIONS.INVENTORY_VIEW, PERMISSIONS.INVENTORY_CREATE, PERMISSIONS.INVENTORY_UPDATE, PERMISSIONS.INVENTORY_DELETE,
    PERMISSIONS.USER_VIEW, PERMISSIONS.USER_CREATE, PERMISSIONS.USER_UPDATE, PERMISSIONS.USER_DELETE,
    PERMISSIONS.SHOP_VIEW, PERMISSIONS.SHOP_CREATE, PERMISSIONS.SHOP_UPDATE, PERMISSIONS.SHOP_DELETE,
    PERMISSIONS.FINANCE_VIEW, PERMISSIONS.FINANCE_CREATE, PERMISSIONS.FINANCE_UPDATE, PERMISSIONS.FINANCE_DELETE, PERMISSIONS.FINANCE_APPROVE, PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.OA_ACCESS, PERMISSIONS.OA_ADMIN, PERMISSIONS.TASK_VIEW, PERMISSIONS.TASK_CREATE, PERMISSIONS.TASK_UPDATE, PERMISSIONS.TASK_DELETE, PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.APPROVAL_VIEW, PERMISSIONS.APPROVAL_PROCESS, PERMISSIONS.APPROVAL_DELEGATE, PERMISSIONS.APPROVAL_WORKFLOW_MANAGE,
    PERMISSIONS.AI_DIAGNOSIS, PERMISSIONS.AI_CHAT, PERMISSIONS.AI_CONFIG, PERMISSIONS.AI_STATS,
    PERMISSIONS.DATA_EXPORT, PERMISSIONS.DATA_IMPORT, PERMISSIONS.DATA_BACKUP,
    PERMISSIONS.TENANT_MANAGEMENT, PERMISSIONS.STAFF_MANAGE, PERMISSIONS.ROLE_MANAGE, PERMISSIONS.SETTINGS_MANAGE, PERMISSIONS.REPORTS_VIEW,
  ],
  
  admin: [
    // 大部分业务权限（除了用户删除等敏感操作）
    PERMISSIONS.FLOCK_VIEW, PERMISSIONS.FLOCK_CREATE, PERMISSIONS.FLOCK_UPDATE, PERMISSIONS.FLOCK_DELETE,
    PERMISSIONS.HEALTH_VIEW, PERMISSIONS.HEALTH_CREATE, PERMISSIONS.HEALTH_UPDATE, PERMISSIONS.HEALTH_DELETE,
    PERMISSIONS.PRODUCTION_VIEW, PERMISSIONS.PRODUCTION_CREATE, PERMISSIONS.PRODUCTION_UPDATE, PERMISSIONS.PRODUCTION_DELETE,
    PERMISSIONS.INVENTORY_VIEW, PERMISSIONS.INVENTORY_CREATE, PERMISSIONS.INVENTORY_UPDATE, PERMISSIONS.INVENTORY_DELETE,
    PERMISSIONS.USER_VIEW, PERMISSIONS.USER_CREATE, PERMISSIONS.USER_UPDATE,
    PERMISSIONS.SHOP_VIEW, PERMISSIONS.SHOP_CREATE, PERMISSIONS.SHOP_UPDATE, PERMISSIONS.SHOP_DELETE,
    PERMISSIONS.FINANCE_VIEW, PERMISSIONS.FINANCE_CREATE, PERMISSIONS.FINANCE_UPDATE, PERMISSIONS.FINANCE_APPROVE, PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.OA_ACCESS, PERMISSIONS.OA_ADMIN, PERMISSIONS.TASK_VIEW, PERMISSIONS.TASK_CREATE, PERMISSIONS.TASK_UPDATE, PERMISSIONS.TASK_DELETE, PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.APPROVAL_VIEW, PERMISSIONS.APPROVAL_PROCESS, PERMISSIONS.APPROVAL_DELEGATE, PERMISSIONS.APPROVAL_WORKFLOW_MANAGE,
    PERMISSIONS.AI_DIAGNOSIS, PERMISSIONS.AI_CHAT, PERMISSIONS.AI_STATS,
    PERMISSIONS.DATA_EXPORT, PERMISSIONS.DATA_IMPORT,
    PERMISSIONS.TENANT_MANAGEMENT, PERMISSIONS.STAFF_MANAGE, PERMISSIONS.REPORTS_VIEW, PERMISSIONS.SETTINGS_MANAGE,
  ],
  
  manager: [
    // 部门级权限
    PERMISSIONS.FLOCK_VIEW, PERMISSIONS.FLOCK_CREATE, PERMISSIONS.FLOCK_UPDATE,
    PERMISSIONS.HEALTH_VIEW, PERMISSIONS.HEALTH_CREATE, PERMISSIONS.HEALTH_UPDATE,
    PERMISSIONS.PRODUCTION_VIEW, PERMISSIONS.PRODUCTION_CREATE, PERMISSIONS.PRODUCTION_UPDATE,
    PERMISSIONS.INVENTORY_VIEW, PERMISSIONS.INVENTORY_CREATE, PERMISSIONS.INVENTORY_UPDATE,
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.SHOP_VIEW,
    PERMISSIONS.FINANCE_VIEW, PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.OA_ACCESS, PERMISSIONS.TASK_VIEW, PERMISSIONS.TASK_CREATE, PERMISSIONS.TASK_UPDATE, PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.APPROVAL_VIEW, PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.AI_DIAGNOSIS, PERMISSIONS.AI_CHAT,
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.REPORTS_VIEW,
  ],
  
  user: [
    // 基础操作权限
    PERMISSIONS.FLOCK_VIEW,
    PERMISSIONS.HEALTH_VIEW, PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.PRODUCTION_VIEW, PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.SHOP_VIEW,
    PERMISSIONS.OA_ACCESS, PERMISSIONS.TASK_VIEW, PERMISSIONS.TASK_CREATE,
    PERMISSIONS.AI_DIAGNOSIS, PERMISSIONS.AI_CHAT,
  ],
  
  finance: [
    PERMISSIONS.FINANCE_VIEW, PERMISSIONS.FINANCE_CREATE, PERMISSIONS.FINANCE_UPDATE, PERMISSIONS.FINANCE_APPROVE, PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.OA_ACCESS, PERMISSIONS.APPROVAL_VIEW, PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.REPORTS_VIEW,
  ],
  
  hr: [
    PERMISSIONS.USER_VIEW, PERMISSIONS.USER_CREATE, PERMISSIONS.USER_UPDATE,
    PERMISSIONS.STAFF_MANAGE,
    PERMISSIONS.OA_ACCESS, PERMISSIONS.TASK_VIEW, PERMISSIONS.TASK_CREATE, PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.APPROVAL_VIEW, PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.REPORTS_VIEW,
  ],
};

/**
 * 统一权限管理器
 */
class UnifiedPermissionManager {
  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  static getCurrentUser() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (e) {
      try { const logger = require('./logger.js'); logger.error && logger.error('获取用户信息失败', e); } catch(_) {}
      return null;
    }
  }

  /**
   * 获取用户的所有权限
   * @param {Object} user 用户对象，可选，默认获取当前用户
   * @returns {Array} 权限列表
   */
  static getUserPermissions(user = null) {
    const currentUser = user || this.getCurrentUser();
    if (!currentUser || !currentUser.role) {
      return [];
    }

    // 从角色获取基础权限
    const rolePermissions = ROLE_PERMISSIONS[currentUser.role] || [];
    
    // 如果用户有自定义权限，合并进来
    const customPermissions = currentUser.permissions || [];
    
    // 合并去重
    return [...new Set([...rolePermissions, ...customPermissions])];
  }

  /**
   * 检查用户是否拥有指定权限
   * @param {string|Array} requiredPermissions 需要的权限
   * @param {Object} options 选项
   * @param {boolean} options.requireAll 是否需要所有权限，默认true
   * @param {Object} options.user 指定用户，默认当前用户
   * @returns {boolean} 是否有权限
   */
  static hasPermission(requiredPermissions, options = {}) {
    const { requireAll = true, user = null } = options;
    const currentUser = user || this.getCurrentUser();
    
    if (!currentUser) {
      return false;
    }

    const userPermissions = this.getUserPermissions(currentUser);
    
    // 转换为数组
    const permissions = Array.isArray(requiredPermissions) 
      ? requiredPermissions 
      : [requiredPermissions];

    if (requireAll) {
      // 需要拥有所有权限
      return permissions.every(permission => userPermissions.includes(permission));
    } else {
      // 需要拥有任意一个权限
      return permissions.some(permission => userPermissions.includes(permission));
    }
  }

  /**
   * 检查用户是否是平台管理员
   * @param {Object} user 用户对象，可选
   * @returns {boolean} 是否是平台管理员
   */
  static isPlatformAdmin(user = null) {
    const currentUser = user || this.getCurrentUser();
    return currentUser && currentUser.role && currentUser.role.startsWith('platform_');
  }

  /**
   * 检查用户是否是租户管理员
   * @param {Object} user 用户对象，可选
   * @returns {boolean} 是否是租户管理员
   */
  static isTenantAdmin(user = null) {
    const currentUser = user || this.getCurrentUser();
    return currentUser && currentUser.role && ['owner', 'admin'].includes(currentUser.role);
  }

  /**
   * 检查用户是否可以访问指定角色的功能
   * @param {string} requiredRole 需要的角色
   * @param {Object} user 用户对象，可选
   * @returns {boolean} 是否有角色权限
   */
  static hasRole(requiredRole, user = null) {
    const currentUser = user || this.getCurrentUser();
    if (!currentUser || !currentUser.role) {
      return false;
    }

    // 角色等级映射
    const roleHierarchy = {
      'platform_super_admin': 1000,
      'platform_admin': 900,
      'platform_operator': 800,
      'platform_support': 700,
      'owner': 600,
      'admin': 500,
      'manager': 400,
      'finance': 300,
      'hr': 300,
      'user': 100,
    };

    const userLevel = roleHierarchy[currentUser.role] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  }

  /**
   * 获取用户角色的显示名称
   * @param {string} role 角色代码
   * @returns {string} 角色显示名称
   */
  static getRoleDisplayName(role) {
    const roleNames = {
      'platform_super_admin': '平台超级管理员',
      'platform_admin': '平台管理员',
      'platform_operator': '平台运维',
      'platform_support': '平台客服',
      'owner': '企业所有者',
      'admin': '企业管理员',
      'manager': '部门经理',
      'finance': '财务人员',
      'hr': 'HR人员',
      'user': '普通员工',
    };

    return roleNames[role] || '未知角色';
  }

  /**
   * 检查功能是否对当前用户可见
   * @param {string} feature 功能标识
   * @param {Object} user 用户对象，可选
   * @returns {boolean} 是否可见
   */
  static isFeatureVisible(feature, user = null) {
    // 功能与权限的映射关系
    const featurePermissions = {
      // 基础功能
      'flock_management': [PERMISSIONS.FLOCK_VIEW],
      'health_management': [PERMISSIONS.HEALTH_VIEW],
      'production_management': [PERMISSIONS.PRODUCTION_VIEW],
      'inventory_management': [PERMISSIONS.INVENTORY_VIEW],
      'shop_management': [PERMISSIONS.SHOP_VIEW],
      
      // 管理功能
      'user_management': [PERMISSIONS.USER_VIEW],
      'finance_management': [PERMISSIONS.FINANCE_VIEW],
      'oa_management': [PERMISSIONS.OA_ACCESS],
      'ai_services': [PERMISSIONS.AI_DIAGNOSIS, PERMISSIONS.AI_CHAT],
      
      // 高级功能
      'data_export': [PERMISSIONS.DATA_EXPORT],
      'system_settings': [PERMISSIONS.SETTINGS_MANAGE],
      'reports': [PERMISSIONS.REPORTS_VIEW],
      
      // 平台功能
      'tenant_management': [PERMISSIONS.TENANT_VIEW],
      'platform_analytics': [PERMISSIONS.PLATFORM_ANALYTICS],
    };

    const requiredPermissions = featurePermissions[feature];
    if (!requiredPermissions) {
      return true; // 未定义的功能默认可见
    }

    return this.hasPermission(requiredPermissions, { requireAll: false, user });
  }

  /**
   * 获取用户可访问的菜单项
   * @param {Array} menuItems 完整菜单列表
   * @param {Object} user 用户对象，可选
   * @returns {Array} 过滤后的菜单列表
   */
  static getAccessibleMenuItems(menuItems, user = null) {
    return menuItems.filter(item => {
      // 如果菜单项定义了权限要求，检查权限
      if (item.requiredPermissions) {
        return this.hasPermission(item.requiredPermissions, { requireAll: false, user });
      }
      
      // 如果菜单项定义了功能标识，检查功能可见性
      if (item.feature) {
        return this.isFeatureVisible(item.feature, user);
      }
      
      // 如果菜单项定义了角色要求，检查角色
      if (item.requiredRole) {
        return this.hasRole(item.requiredRole, user);
      }
      
      // 默认显示
      return true;
    });
  }

  /**
   * 验证操作权限并显示提示
   * @param {string|Array} requiredPermissions 需要的权限
   * @param {Object} options 选项
   * @returns {boolean} 是否有权限
   */
  static validateActionPermission(requiredPermissions, options = {}) {
    const hasPermission = this.hasPermission(requiredPermissions, options);
    
    if (!hasPermission) {
      const { showToast = true, message = '权限不足，无法执行此操作' } = options;
      
      if (showToast) {
        wx.showToast({
          title: message,
          icon: 'none',
          duration: 2000
        });
      }
    }
    
    return hasPermission;
  }
}

module.exports = {
  PERMISSIONS,
  ROLE_PERMISSIONS,
  UnifiedPermissionManager,
};