-- 智慧养鹅管理系统数据库初始化脚本
-- Smart Goose Management System Database Initialization

-- 创建数据库
CREATE DATABASE IF NOT EXISTS smart_goose CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE smart_goose;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    farmName VARCHAR(200) COMMENT '养殖场名称',
    role ENUM('admin', 'manager', 'user') DEFAULT 'user' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    avatar VARCHAR(500) COMMENT '头像URL',
    lastLoginAt DATETIME COMMENT '最后登录时间',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='用户表';

-- 鹅群表
CREATE TABLE IF NOT EXISTS flocks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL COMMENT '用户ID',
    name VARCHAR(100) NOT NULL COMMENT '鹅群名称',
    batchNumber VARCHAR(50) NOT NULL UNIQUE COMMENT '批次号',
    breed VARCHAR(50) NOT NULL COMMENT '品种',
    totalCount INT NOT NULL DEFAULT 0 COMMENT '总数量',
    currentCount INT NOT NULL DEFAULT 0 COMMENT '当前数量',
    maleCount INT NOT NULL DEFAULT 0 COMMENT '雄性数量',
    femaleCount INT NOT NULL DEFAULT 0 COMMENT '雌性数量',
    ageGroup ENUM('young', 'adult', 'breeding', 'retired') DEFAULT 'young' COMMENT '年龄组',
    status ENUM('active', 'inactive', 'sold', 'deceased') DEFAULT 'active' COMMENT '状态',
    establishedDate DATE NOT NULL COMMENT '建群日期',
    location VARCHAR(200) COMMENT '饲养位置',
    description TEXT COMMENT '描述备注',
    lastHealthCheckDate DATE COMMENT '最后健康检查日期',
    healthStatus ENUM('excellent', 'good', 'fair', 'poor') DEFAULT 'good' COMMENT '健康状态',
    avgDailyEggProduction DECIMAL(5,2) DEFAULT 0 COMMENT '平均日产蛋量',
    avgEggWeight DECIMAL(5,2) DEFAULT 0 COMMENT '平均蛋重(克)',
    avgDailyFeedConsumption DECIMAL(8,2) DEFAULT 0 COMMENT '平均日饲料消耗量(克)',
    feedConversionRatio DECIMAL(5,2) DEFAULT 0 COMMENT '料蛋比',
    avgTemperature DECIMAL(4,1) COMMENT '平均温度(°C)',
    avgHumidity DECIMAL(4,1) COMMENT '平均湿度(%)',
    totalCost DECIMAL(10,2) DEFAULT 0 COMMENT '总成本',
    totalRevenue DECIMAL(10,2) DEFAULT 0 COMMENT '总收入',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_batch_number (batchNumber),
    INDEX idx_breed (breed),
    INDEX idx_status (status),
    INDEX idx_age_group (ageGroup),
    INDEX idx_established_date (establishedDate)
) ENGINE=InnoDB COMMENT='鹅群表';

-- 生产记录表
CREATE TABLE IF NOT EXISTS production_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL COMMENT '用户ID',
    flockId INT NOT NULL COMMENT '鹅群ID',
    recordedDate DATE NOT NULL COMMENT '记录日期',
    eggCount INT NOT NULL DEFAULT 0 COMMENT '产蛋数量',
    eggWeight DECIMAL(6,2) COMMENT '蛋重(克)',
    feedConsumption DECIMAL(8,2) DEFAULT 0 COMMENT '饲料消耗(克)',
    temperature DECIMAL(4,1) COMMENT '温度(°C)',
    humidity DECIMAL(4,1) COMMENT '湿度(%)',
    mortalityCount INT DEFAULT 0 COMMENT '死亡数量',
    healthStatus ENUM('normal', 'warning', 'critical') DEFAULT 'normal' COMMENT '健康状态',
    notes TEXT COMMENT '备注',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (flockId) REFERENCES flocks(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_flock_id (flockId),
    INDEX idx_recorded_date (recordedDate),
    INDEX idx_health_status (healthStatus)
) ENGINE=InnoDB COMMENT='生产记录表';

-- 健康记录表
CREATE TABLE IF NOT EXISTS health_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL COMMENT '用户ID',
    flockId INT NOT NULL COMMENT '鹅群ID',
    checkDate DATE NOT NULL COMMENT '检查日期',
    checkType ENUM('routine', 'vaccination', 'treatment', 'emergency') DEFAULT 'routine' COMMENT '检查类型',
    healthStatus ENUM('healthy', 'warning', 'sick', 'critical') DEFAULT 'healthy' COMMENT '健康状态',
    affectedCount INT DEFAULT 0 COMMENT '受影响数量',
    symptoms TEXT COMMENT '症状',
    diagnosis TEXT COMMENT '诊断',
    treatment TEXT COMMENT '治疗方案',
    medication VARCHAR(200) COMMENT '用药',
    dosage VARCHAR(100) COMMENT '剂量',
    veterinarian VARCHAR(100) COMMENT '兽医师',
    followUpDate DATE COMMENT '随访日期',
    notes TEXT COMMENT '备注',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (flockId) REFERENCES flocks(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_flock_id (flockId),
    INDEX idx_check_date (checkDate),
    INDEX idx_check_type (checkType),
    INDEX idx_health_status (healthStatus)
) ENGINE=InnoDB COMMENT='健康记录表';

-- 饲料记录表
CREATE TABLE IF NOT EXISTS feed_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL COMMENT '用户ID',
    feedType VARCHAR(100) NOT NULL COMMENT '饲料类型',
    quantity DECIMAL(10,2) NOT NULL COMMENT '数量(kg)',
    unitPrice DECIMAL(8,2) COMMENT '单价',
    totalCost DECIMAL(10,2) COMMENT '总成本',
    supplier VARCHAR(200) COMMENT '供应商',
    purchaseDate DATE COMMENT '采购日期',
    expiryDate DATE COMMENT '过期日期',
    notes TEXT COMMENT '备注',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_feed_type (feedType),
    INDEX idx_purchase_date (purchaseDate)
) ENGINE=InnoDB COMMENT='饲料记录表';

-- 财务记录表
CREATE TABLE IF NOT EXISTS financial_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL COMMENT '用户ID',
    type ENUM('income', 'expense') NOT NULL COMMENT '类型',
    category VARCHAR(100) NOT NULL COMMENT '分类',
    amount DECIMAL(10,2) NOT NULL COMMENT '金额',
    description TEXT COMMENT '描述',
    recordDate DATE NOT NULL COMMENT '记录日期',
    notes TEXT COMMENT '备注',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_record_date (recordDate)
) ENGINE=InnoDB COMMENT='财务记录表';

-- 插入默认管理员用户
INSERT INTO users (username, email, password, name, role, status) VALUES 
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', '系统管理员', 'admin', 'active');
-- 密码是: admin123

-- 插入示例鹅群数据
INSERT INTO flocks (userId, name, batchNumber, breed, totalCount, currentCount, maleCount, femaleCount, ageGroup, status, establishedDate, location, description) VALUES
(1, '白鹅群A', 'FLOCK-2024001', 'white_goose', 100, 98, 20, 78, 'adult', 'active', '2024-01-15', '1号养殖区', '优质白鹅群，产蛋性能良好'),
(1, '灰鹅群B', 'FLOCK-2024002', 'grey_goose', 80, 79, 15, 64, 'breeding', 'active', '2023-12-01', '2号养殖区', '种鹅群，用于繁殖'),
(1, '幼鹅群C', 'FLOCK-2024003', 'white_goose', 150, 148, 30, 118, 'young', 'active', '2024-01-20', '3号养殖区', '新孵化的幼鹅群');

-- 插入示例生产记录
INSERT INTO production_records (userId, flockId, recordedDate, eggCount, eggWeight, feedConsumption, temperature, humidity, mortalityCount, healthStatus, notes) VALUES
(1, 1, '2024-01-30', 45, 65.5, 8500, 18.5, 65.0, 0, 'normal', '产蛋正常'),
(1, 1, '2024-01-29', 42, 64.8, 8200, 19.0, 62.0, 0, 'normal', '天气良好'),
(1, 2, '2024-01-30', 35, 68.2, 6800, 18.5, 65.0, 0, 'normal', '种鹅产蛋稳定'),
(1, 2, '2024-01-29', 38, 67.5, 7000, 19.0, 62.0, 0, 'normal', '健康状况良好');

-- 插入示例健康记录
INSERT INTO health_records (userId, flockId, checkDate, checkType, healthStatus, affectedCount, symptoms, diagnosis, treatment, veterinarian, notes) VALUES
(1, 1, '2024-01-28', 'routine', 'healthy', 0, '无异常症状', '健康状况良好', '无需治疗', '张兽医', '定期健康检查'),
(1, 2, '2024-01-25', 'vaccination', 'healthy', 79, '疫苗接种反应轻微', '疫苗接种成功', '观察3天', '李兽医', '禽流感疫苗接种'),
(1, 3, '2024-01-22', 'routine', 'warning', 2, '轻微腹泻', '消化不良', '调整饲料配比', '张兽医', '幼鹅适应期正常反应');

-- 插入示例饲料记录
INSERT INTO feed_records (userId, feedType, quantity, unitPrice, totalCost, supplier, purchaseDate, expiryDate, notes) VALUES
(1, '成鹅配合饲料', 1000.00, 3.50, 3500.00, '绿源饲料公司', '2024-01-15', '2024-07-15', '高蛋白配方'),
(1, '幼鹅开食料', 500.00, 4.20, 2100.00, '绿源饲料公司', '2024-01-20', '2024-07-20', '营养全面，易消化'),
(1, '种鹅繁殖料', 800.00, 3.80, 3040.00, '康牧饲料厂', '2024-01-10', '2024-07-10', '提高繁殖性能');

-- 插入示例财务记录
INSERT INTO financial_records (userId, type, category, amount, description, recordDate, notes) VALUES
(1, 'expense', '饲料采购', 3500.00, '采购成鹅配合饲料1000kg', '2024-01-15', '绿源饲料公司'),
(1, 'expense', '疫苗费用', 800.00, '禽流感疫苗接种费用', '2024-01-25', '预防疫苗'),
(1, 'income', '鹅蛋销售', 2800.00, '销售鹅蛋350kg', '2024-01-30', '本地农贸市场'),
(1, 'expense', '兽医费用', 300.00, '健康检查和治疗费用', '2024-01-28', '张兽医诊疗费');

-- 创建视图：用户鹅群统计
CREATE OR REPLACE VIEW user_flock_stats AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(f.id) as total_flocks,
    SUM(f.totalCount) as total_geese,
    SUM(f.currentCount) as current_geese,
    AVG(f.avgDailyEggProduction) as avg_production
FROM users u
LEFT JOIN flocks f ON u.id = f.userId
GROUP BY u.id, u.username;

-- 创建视图：生产统计
CREATE OR REPLACE VIEW production_stats AS
SELECT 
    DATE(recordedDate) as date,
    SUM(eggCount) as daily_eggs,
    SUM(feedConsumption) as daily_feed,
    AVG(temperature) as avg_temperature,
    AVG(humidity) as avg_humidity
FROM production_records
GROUP BY DATE(recordedDate)
ORDER BY date DESC;

COMMIT;
