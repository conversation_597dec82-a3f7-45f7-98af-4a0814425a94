// pages/task/task-list/task-list.js
Page({
  data: {
    activeFilter: 'all',
    searchKeyword: '',
    sortBy: 'deadline',
    showSortModal: false,
    loading: false,
    tasks: [],
    filteredTasks: [],
    taskCounts: {
      all: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      overdue: 0
    }
  },

  onLoad: function (options) {
    this.loadTasks();
  },

  onPullDownRefresh: function () {
    this.loadTasks(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    // 加载更多任务
    this.loadMoreTasks();
  },

  // 加载任务列表
  loadTasks(callback) {
    this.setData({ loading: true });

    // 模拟API调用
    setTimeout(() => {
      const mockTasks = this.getMockTasks();
      this.setData({
        tasks: mockTasks,
        loading: false
      });
      
      this.filterTasks();
      this.calculateTaskCounts();
      callback && callback();
    }, 1000);
  },

  // 获取模拟任务数据
  getMockTasks() {
    return [
      {
        id: 1,
        title: '鹅舍清洁消毒',
        description: 'A区鹅舍需要进行全面清洁和消毒工作',
        status: 'pending',
        statusText: '待处理',
        priority: 'high',
        priorityText: '高',
        deadline: '今天 18:00',
        assignee: '张三',
        category: '日常维护',
        createTime: '2024-01-15 09:00'
      },
      {
        id: 2,
        title: '疫苗接种',
        description: 'B区雏鹅第二次疫苗接种',
        status: 'processing',
        statusText: '进行中',
        priority: 'high',
        priorityText: '高',
        deadline: '明天 10:00',
        assignee: '李四',
        category: '健康管理',
        createTime: '2024-01-14 14:30'
      },
      {
        id: 3,
        title: '饲料采购',
        description: '采购下周所需的优质鹅饲料',
        status: 'completed',
        statusText: '已完成',
        priority: 'medium',
        priorityText: '中',
        deadline: '昨天 16:00',
        assignee: '王五',
        category: '物料管理',
        createTime: '2024-01-13 11:20'
      },
      {
        id: 4,
        title: '环境监测',
        description: '检查各区域温湿度和空气质量',
        status: 'overdue',
        statusText: '已逾期',
        priority: 'medium',
        priorityText: '中',
        deadline: '昨天 12:00',
        assignee: '赵六',
        category: '环境监控',
        createTime: '2024-01-12 08:45'
      },
      {
        id: 5,
        title: '设备维护',
        description: '检查和维护自动喂食设备',
        status: 'pending',
        statusText: '待处理',
        priority: 'low',
        priorityText: '低',
        deadline: '后天 14:00',
        assignee: '孙七',
        category: '设备维护',
        createTime: '2024-01-11 16:15'
      }
    ];
  },

  // 筛选任务
  filterTasks() {
    let filtered = [...this.data.tasks];
    
    // 按状态筛选
    if (this.data.activeFilter !== 'all') {
      filtered = filtered.filter(task => task.status === this.data.activeFilter);
    }
    
    // 按关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(task => 
        task.title.toLowerCase().includes(keyword) ||
        task.description.toLowerCase().includes(keyword) ||
        task.category.toLowerCase().includes(keyword)
      );
    }
    
    // 排序
    this.sortTasks(filtered);
    
    this.setData({
      filteredTasks: filtered
    });
  },

  // 排序任务
  sortTasks(tasks) {
    const { sortBy } = this.data;
    
    tasks.sort((a, b) => {
      switch (sortBy) {
        case 'deadline':
          return new Date(a.deadline) - new Date(b.deadline);
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case 'createTime':
          return new Date(b.createTime) - new Date(a.createTime);
        case 'status':
          const statusOrder = { pending: 1, processing: 2, overdue: 3, completed: 4 };
          return statusOrder[a.status] - statusOrder[b.status];
        default:
          return 0;
      }
    });
  },

  // 计算任务数量
  calculateTaskCounts() {
    const { tasks } = this.data;
    const counts = {
      all: tasks.length,
      pending: tasks.filter(t => t.status === 'pending').length,
      processing: tasks.filter(t => t.status === 'processing').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      overdue: tasks.filter(t => t.status === 'overdue').length
    };
    
    this.setData({ taskCounts: counts });
  },

  // 切换筛选条件
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter
    });
    this.filterTasks();
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.filterTasks();
  },

  // 执行搜索
  onSearch() {
    this.filterTasks();
  },

  // 清除搜索
  onClearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.filterTasks();
  },

  // 显示排序弹窗
  onSortTap() {
    this.setData({
      showSortModal: true
    });
  },

  // 关闭排序弹窗
  onCloseSortModal() {
    this.setData({
      showSortModal: false
    });
  },

  // 选择排序方式
  onSortSelect(e) {
    const sortBy = e.currentTarget.dataset.sort;
    this.setData({
      sortBy: sortBy,
      showSortModal: false
    });
    this.filterTasks();
  },

  // 点击任务项
  onTaskTap(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/task/task-detail/task-detail?id=${taskId}`
    });
  },

  // 开始任务
  onStartTask(e) {
    const taskId = e.currentTarget.dataset.id;
    this.updateTaskStatus(taskId, 'processing', '任务已开始');
  },

  // 完成任务
  onCompleteTask(e) {
    const taskId = e.currentTarget.dataset.id;
    this.updateTaskStatus(taskId, 'completed', '任务已完成');
  },

  // 查看任务
  onViewTask(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/task/task-detail/task-detail?id=${taskId}`
    });
  },

  // 重启任务
  onRestartTask(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认重启',
      content: '确定要重启这个任务吗？',
      success: (res) => {
        if (res.confirm) {
          this.updateTaskStatus(taskId, 'pending', '任务已重启');
        }
      }
    });
  },

  // 更新任务状态
  updateTaskStatus(taskId, newStatus, message) {
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        const statusMap = {
          pending: '待处理',
          processing: '进行中',
          completed: '已完成',
          overdue: '已逾期'
        };
        return {
          ...task,
          status: newStatus,
          statusText: statusMap[newStatus]
        };
      }
      return task;
    });

    this.setData({ tasks });
    this.filterTasks();
    this.calculateTaskCounts();

    wx.showToast({
      title: message,
      icon: 'success'
    });
  },

  // 添加任务
  onAddTask() {
    wx.navigateTo({
      url: '/pages/task/task-add/task-add'
    });
  },

  // 加载更多任务
  loadMoreTasks() {
    // 模拟加载更多
    wx.showToast({
      title: '没有更多任务了',
      icon: 'none'
    });
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  }
});
