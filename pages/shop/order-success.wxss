/* pages/shop/order-success.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f6f8;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 50rpx 30rpx;
  box-sizing: border-box;
}

.success-content {
  text-align: center;
}

.success-icon {
  width: 200rpx;
  height: 200rpx;
  margin: 50rpx auto;
}

.success-title {
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 50rpx;
}

.order-info {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666666;
}

.value {
  font-size: 28rpx;
  color: #333333;
}

.price {
  color: #ff5722;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 30rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-xl);
  font-size: 30rpx;
}

.home-btn {
  background-color: #ffffff;
  color: #333333;
  border: 1rpx solid #dddddd;
}

.order-btn {
  background-color: #ff5722;
  color: #ffffff;
}