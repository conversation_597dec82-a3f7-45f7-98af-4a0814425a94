// pages/oa/notification/notification.js

const { getCurrentUserInfo, getUserPermissions } = require('../../../utils/user-info');
const { isLoggedIn, getCurrentUser } = require('../../../utils/auth-helper');
const { formatDate } = require('../../../utils/format');
const { PERMISSIONS, PermissionMixin } = require('../../../utils/oa-permissions');
const request = require('../../../utils/request.js');
const { API } = require('../../../constants/index.js');
let logger; try { logger = require('../../../utils/logger.js'); } catch(_) { logger = console; }

Page({
  // 混入权限检查功能
  ...PermissionMixin,
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,
    
    // 通知列表
    notifications: [],
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      hasMore: true
    },
    
    // 筛选状态
    filters: {
      type: 'all',
      read: 'all'
    },
    
    // 通知类型选项
    typeOptions: [
      { value: 'all', label: '全部类型' },
      { value: 'system', label: '系统通知' },
      { value: 'approval', label: '审批通知' },
      { value: 'task', label: '任务通知' },
      { value: 'announcement', label: '公告通知' }
    ],
    
    readOptions: [
      { value: 'all', label: '全部状态' },
      { value: 'unread', label: '未读' },
      { value: 'read', label: '已读' }
    ],
    
    // 选择器索引
    typeIndex: 0,
    readIndex: 0,
    
    // 显示标签
    selectedTypeLabel: '全部类型',
    selectedReadLabel: '全部状态',
    
    // 页面状态
    loading: false,
    refreshing: false,
    showFilters: false,
    
    // 统计数据
    statistics: {
      total: 0,
      unread: 0,
      today: 0
    }
  },

  onLoad(options) {
    this.initPage();
  },

  onShow() {
    this.loadNotifications(true);
  },

  onPullDownRefresh() {
    this.loadNotifications(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadNotifications(false);
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 使用auth-helper进行可靠的登录检查

    if (!isLoggedIn()) {

      wx.showModal({

        title: '需要登录',

        content: '使用此功能需要先登录系统',

        confirmText: '去登录',

        cancelText: '取消',

        success: (res) => {

          if (res.confirm) {

            wx.redirectTo({ url: '/pages/login/login' });

          } else {

            wx.navigateBack();

          }

        }

      });

      return;

    }


    // 获取用户信息并同步数据

    const authUser = getCurrentUser();

    let userInfo = getCurrentUserInfo();


    if (!userInfo && authUser) {

      const app = getApp();

      app.globalData.userInfo = authUser;

      wx.setStorageSync('user_info', authUser);

      userInfo = getCurrentUserInfo();

    }

    

    const permissions = getUserPermissions();


    if (!userInfo) {

      wx.showModal({

        title: '数据错误',

        content: '无法获取用户信息，请重新登录',

        confirmText: '重新登录',

        showCancel: false,

        success: () => {

          wx.redirectTo({ url: '/pages/login/login' });

        }

      });

      return;

    }

    this.setData({
      userInfo,
      permissions
    });

    this.loadNotifications(true);
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查通知查看权限
    if (!this.hasPermission(PERMISSIONS.VIEW_OA)) {
      wx.showModal({
        title: '权限不足',
        content: '您没有查看通知的权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限验证通过，加载数据
    this.loadNotifications(true);
  },

  /**
   * 加载通知列表
   */
  async loadNotifications(refresh = false) {
    if (this.data.loading) return;

    if (refresh) {
      this.setData({
        refreshing: true,
        'pagination.page': 1,
        'pagination.hasMore': true
      });
    } else {
      this.setData({ loading: true });
    }

    try {
      const { pagination, filters } = this.data;
      const params = {
        page: refresh ? 1 : pagination.page,
        limit: pagination.limit,
        ...filters
      };

      const res = await request.get(API.ENDPOINTS.OA.NOTIFICATIONS.LIST, params);
      if (res && res.success !== false) {
        const payload = res.data || res;
        const list = payload.list || [];
        const newPagination = payload.pagination || { page: params.page, total: list.length, pages: list.length === pagination.limit ? params.page + 1 : params.page };
        const statistics = payload.statistics || this.data.statistics;

        let notifications = [];
        if (refresh) {
          notifications = list;
        } else {
          notifications = [...this.data.notifications, ...list];
        }

        // 格式化通知数据
        const formattedNotifications = notifications.map(notification => ({
          ...notification,
          typeLabel: this.getTypeLabel(notification.type),
          createdTimeAgo: this.formatTimeAgo(notification.created_at),
          isToday: this.isToday(notification.created_at)
        }));

        this.setData({
          notifications: formattedNotifications,
          'pagination.page': newPagination.page,
          'pagination.total': newPagination.total,
          'pagination.hasMore': newPagination.page < newPagination.pages,
          statistics
        });

        // 更新选择器索引
        this.updateFilterIndexes();
      } else {
        throw new Error(res?.message || '加载失败');
      }
    } catch (error) {
      try { logger.error && logger.error('加载通知列表失败', error); } catch(_) {}
      
      // 使用模拟数据
      this.loadMockData(refresh);
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 加载模拟数据
   */
  loadMockData(refresh) {
    const mockNotifications = [
      {
        id: 'notif_001',
        type: 'approval',
        title: '您的报销申请已通过',
        content: '您提交的差旅费报销申请已通过审批，预计3个工作日内到账。',
        read: false,
        created_at: new Date().toISOString(),
        sender: '财务部'
      },
      {
        id: 'notif_002',
        type: 'task',
        title: '新的待办任务',
        content: '您有一个新的采购审批任务需要处理，请及时查看。',
        read: false,
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        sender: '系统'
      },
      {
        id: 'notif_003',
        type: 'announcement',
        title: '系统维护通知',
        content: '系统将于本周六晚上进行维护升级，维护期间可能无法正常使用。',
        read: true,
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        sender: '管理员'
      }
    ];

    const formattedNotifications = mockNotifications.map(notification => ({
      ...notification,
      typeLabel: this.getTypeLabel(notification.type),
      createdTimeAgo: this.formatTimeAgo(notification.created_at),
      isToday: this.isToday(notification.created_at)
    }));

    this.setData({
      notifications: refresh ? formattedNotifications : [...this.data.notifications, ...formattedNotifications],
      'pagination.total': mockNotifications.length,
      'pagination.hasMore': false,
      statistics: {
        total: mockNotifications.length,
        unread: mockNotifications.filter(n => !n.read).length,
        today: mockNotifications.filter(n => this.isToday(n.created_at)).length
      }
    });
  },

  /**
   * 获取类型标签
   */
  getTypeLabel(type) {
    const typeMap = {
      system: '系统',
      approval: '审批',
      task: '任务',
      announcement: '公告'
    };
    return typeMap[type] || '其他';
  },

  /**
   * 格式化时间
   */
  formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return minutes <= 1 ? '刚刚' : `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return formatDate(date, 'MM-DD');
    }
  },

  /**
   * 检查是否为今天
   */
  isToday(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    return date.toDateString() === today.toDateString();
  },

  /**
   * 更新筛选器索引
   */
  updateFilterIndexes() {
    const { filters, typeOptions, readOptions } = this.data;

    const typeIndex = typeOptions.findIndex(item => item.value === filters.type);
    const readIndex = readOptions.findIndex(item => item.value === filters.read);

    this.setData({
      typeIndex: typeIndex >= 0 ? typeIndex : 0,
      readIndex: readIndex >= 0 ? readIndex : 0,
      selectedTypeLabel: typeOptions[typeIndex >= 0 ? typeIndex : 0].label,
      selectedReadLabel: readOptions[readIndex >= 0 ? readIndex : 0].label
    });
  },

  /**
   * 筛选器变化
   */
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    let newFilter = {};
    let label = '';

    if (field === 'type') {
      const selectedType = this.data.typeOptions[value];
      newFilter = { type: selectedType.value };
      label = selectedType.label;
      this.setData({
        typeIndex: value,
        selectedTypeLabel: label
      });
    } else if (field === 'read') {
      const selectedRead = this.data.readOptions[value];
      newFilter = { read: selectedRead.value };
      label = selectedRead.label;
      this.setData({
        readIndex: value,
        selectedReadLabel: label
      });
    }

    this.setData({
      [`filters.${field}`]: newFilter[field]
    });

    this.loadNotifications(true);
  },

  /**
   * 切换筛选器显示
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 点击通知项
   */
  onNotificationTap(e) {
    const { notification } = e.currentTarget.dataset;
    
    // 标记为已读
    if (!notification.read) {
      this.markAsRead(notification.id);
    }

    // 处理不同类型的通知跳转
    this.handleNotificationAction(notification);
  },

  /**
   * 标记为已读
   */
  async markAsRead(notificationId) {
    try {
      await request.put(API.ENDPOINTS.OA.NOTIFICATIONS.READ(notificationId), {});

      // 更新本地状态
      const notifications = this.data.notifications.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      );

      this.setData({
        notifications,
        'statistics.unread': Math.max(0, this.data.statistics.unread - 1)
      });

    } catch (error) {
      try { logger.error && logger.error('标记已读失败', error); } catch(_) {}
    }
  },

  /**
   * 处理通知操作
   */
  handleNotificationAction(notification) {
    switch (notification.type) {
      case 'approval':
        wx.navigateTo({
          url: '/pages/oa/approval/pending/pending'
        });
        break;
      case 'task':
        wx.navigateTo({
          url: '/pages/oa/approval/pending/pending'
        });
        break;
      case 'system':
      case 'announcement':
      default:
        // 显示详情
        this.showNotificationDetail(notification);
        break;
    }
  },

  /**
   * 显示通知详情
   */
  showNotificationDetail(notification) {
    wx.showModal({
      title: notification.title,
      content: notification.content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 全部标记已读
   */
  async markAllAsRead() {
    try {
      await request.put(API.ENDPOINTS.OA.NOTIFICATIONS.READ_ALL, {});

      // 更新本地状态
      const notifications = this.data.notifications.map(n => ({ ...n, read: true }));

      this.setData({
        notifications,
        'statistics.unread': 0
      });

      wx.showToast({
        title: '已全部标记为已读',
        icon: 'success'
      });

    } catch (error) {
      try { logger.error && logger.error('全部标记已读失败', error); } catch(_) {}
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  }
});