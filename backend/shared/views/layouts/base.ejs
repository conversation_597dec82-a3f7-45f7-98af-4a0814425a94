<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title || '智慧养鹅管理平台' %> - 智慧养鹅<%= platformType === 'saas' ? 'SaaS平台' : '管理系统' %></title>
    
    <!-- 基础样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定义公共样式 -->
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }
        
        /* 通用容器样式 */
        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .content-wrapper {
            flex: 1;
            display: flex;
        }
        
        .main-content {
            flex: 1;
            padding: 0;
            background-color: #f5f7fb;
        }
        
        /* 通用卡片样式 */
        .modern-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border: 1px solid rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .modern-card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .modern-card-header {
            padding: 20px 24px 15px;
            border-bottom: 1px solid #eee;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px 12px 0 0;
        }
        
        .modern-card-body {
            padding: 24px;
        }
        
        /* 通用按钮样式 */
        .btn-modern {
            border-radius: 8px;
            font-weight: 500;
            padding: 12px 24px;
            transition: all 0.3s ease;
            border: none;
            font-size: 14px;
        }
        
        .btn-modern:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .btn-modern.btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 100%);
            color: white;
        }
        
        .btn-modern.btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
            color: white;
        }
        
        /* 通用表格样式 */
        .modern-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .modern-table table {
            margin: 0;
        }
        
        .modern-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            font-weight: 600;
            padding: 15px 20px;
            border: none;
            color: var(--dark-color);
        }
        
        .modern-table td {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            vertical-align: middle;
        }
        
        .modern-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        /* 通用状态徽章 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-badge.active {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .status-badge.inactive {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .status-badge.pending {
            background-color: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .modern-card-header,
            .modern-card-body {
                padding: 16px;
            }
            
            .btn-modern {
                padding: 10px 20px;
                font-size: 13px;
            }
            
            .modern-table th,
            .modern-table td {
                padding: 12px 16px;
            }
        }
        
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
    
    <!-- 页面特定样式 -->
    <% if (locals.customStyles) { %>
        <style><%= customStyles %></style>
    <% } %>
    
    <!-- 外部CSS文件 -->
    <% if (locals.additionalCSS) { %>
        <% additionalCSS.forEach(function(css) { %>
            <link rel="stylesheet" href="<%= css %>">
        <% }); %>
    <% } %>
</head>
<body class="<%= locals.bodyClass || '' %>">
    <div class="main-container">
        <!-- 导航栏 -->
        <% if (!locals.hideNavbar) { %>
            <%- include('../partials/navbar', { 
                platformType: platformType, 
                currentUser: locals.currentUser,
                notifications: locals.notifications 
            }) %>
        <% } %>
        
        <div class="content-wrapper">
            <!-- 侧边栏 -->
            <% if (!locals.hideSidebar) { %>
                <%- include('../partials/sidebar', { 
                    platformType: platformType, 
                    currentPage: locals.currentPage,
                    userRole: locals.userRole 
                }) %>
            <% } %>
            
            <!-- 主内容区 -->
            <main class="main-content <%= locals.hideSidebar ? 'full-width' : '' %>">
                <!-- 页面头部 -->
                <% if (locals.pageHeader !== false) { %>
                    <%- include('../partials/page-header', {
                        title: title,
                        subtitle: locals.subtitle,
                        icon: locals.icon,
                        breadcrumb: locals.breadcrumb,
                        actions: locals.actions
                    }) %>
                <% } %>
                
                <!-- 页面内容 -->
                <div class="container-fluid <%= locals.containerClass || 'px-4' %>">
                    <!-- 全局消息提示 -->
                    <% if (locals.messages) { %>
                        <%- include('../partials/messages', { messages: messages }) %>
                    <% } %>
                    
                    <!-- 页面具体内容 -->
                    <%- body %>
                </div>
            </main>
        </div>
        
        <!-- 页脚 -->
        <% if (!locals.hideFooter) { %>
            <%- include('../partials/footer', { platformType: platformType }) %>
        <% } %>
    </div>
    
    <!-- 全局模态框容器 -->
    <div id="global-modal-container"></div>
    
    <!-- 基础JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 公共JavaScript工具 -->
    <script>
        // 全局配置
        window.AppConfig = {
            platformType: '<%= platformType %>',
            apiBaseUrl: '<%= locals.apiBaseUrl || '/api' %>',
            currentUser: <%- JSON.stringify(locals.currentUser || {}) %>,
            csrfToken: '<%= locals.csrfToken || '' %>'
        };
        
        // 全局工具函数
        window.AppUtils = {
            // 显示成功消息
            showSuccess: function(message) {
                this.showToast(message, 'success');
            },
            
            // 显示错误消息
            showError: function(message) {
                this.showToast(message, 'danger');
            },
            
            // 显示Toast消息
            showToast: function(message, type = 'info') {
                const toastHtml = `
                    <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;
                
                let toastContainer = document.getElementById('toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.id = 'toast-container';
                    toastContainer.className = 'position-fixed top-0 end-0 p-3';
                    toastContainer.style.zIndex = '9999';
                    document.body.appendChild(toastContainer);
                }
                
                const toastElement = document.createElement('div');
                toastElement.innerHTML = toastHtml;
                toastContainer.appendChild(toastElement.firstElementChild);
                
                const toast = new bootstrap.Toast(toastElement.firstElementChild);
                toast.show();
                
                // 自动清理
                setTimeout(() => {
                    if (toastElement.firstElementChild) {
                        toastElement.firstElementChild.remove();
                    }
                }, 5000);
            },
            
            // 确认对话框
            confirm: function(message, callback) {
                if (confirm(message)) {
                    callback();
                }
            },
            
            // AJAX请求工具
            ajax: function(options) {
                const defaults = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': window.AppConfig.csrfToken
                    }
                };
                
                const config = Object.assign({}, defaults, options);
                
                if (config.data && config.method !== 'GET') {
                    config.body = JSON.stringify(config.data);
                }
                
                return fetch(config.url, config)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .catch(error => {
                        this.showError('请求失败: ' + error.message);
                        throw error;
                    });
            }
        };
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 添加淡入动画
            document.body.classList.add('fade-in');
            
            // 自动隐藏alert消息
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>
    
    <!-- 页面特定JavaScript -->
    <% if (locals.customScripts) { %>
        <script><%= customScripts %></script>
    <% } %>
    
    <!-- 外部JavaScript文件 -->
    <% if (locals.additionalJS) { %>
        <% additionalJS.forEach(function(js) { %>
            <script src="<%= js %>"></script>
        <% }); %>
    <% } %>
</body>
</html>