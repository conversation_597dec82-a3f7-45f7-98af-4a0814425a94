const express = require("express");
const router = express.Router();

// 知识库文章列表
router.get("/", async (req, res) => {
  try {
    // 模拟数据 - 实际应该从数据库获取
    const articles = [
      {
        id: 1,
        title: "鹅的常见疾病及防治方法",
        category: "疾病防治",
        summary: "介绍鹅的常见疾病症状和防治措施",
        author: "张兽医",
        viewCount: 1250,
        status: "published",
        publishDate: "2024-01-01T10:00:00Z",
        createdAt: "2024-01-01T10:00:00Z",
      },
      {
        id: 2,
        title: "鹅的饲养管理要点",
        category: "饲养管理",
        summary: "详细介绍鹅的饲养管理技术",
        author: "李专家",
        viewCount: 980,
        status: "published",
        publishDate: "2024-01-05T10:00:00Z",
        createdAt: "2024-01-05T10:00:00Z",
      },
    ];

    res.json({
      success: true,
      message: "获取知识库文章列表成功",
      data: articles,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取知识库文章列表失败",
      error: error.message,
    });
  }
});

// 知识库文章详情
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    // 模拟数据 - 实际应该从数据库获取
    const article = {
      id: parseInt(id),
      title: "鹅的常见疾病及防治方法",
      category: "疾病防治",
      summary: "介绍鹅的常见疾病症状和防治措施",
      content: "文章详细内容...",
      author: "张兽医",
      viewCount: 1250,
      status: "published",
      publishDate: "2024-01-01T10:00:00Z",
      createdAt: "2024-01-01T10:00:00Z",
      updatedAt: "2024-01-01T10:00:00Z",
    };

    res.json({
      success: true,
      message: "获取知识库文章详情成功",
      data: article,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取知识库文章详情失败",
      error: error.message,
    });
  }
});

// 创建知识库文章
router.post("/", async (req, res) => {
  try {
    const { title, category, summary, content, author, status } = req.body;

    // 模拟创建文章 - 实际应该保存到数据库
    const newArticle = {
      id: Math.floor(Math.random() * 1000) + 3,
      title,
      category,
      summary,
      content,
      author,
      viewCount: 0,
      status,
      publishDate: status === "published" ? new Date().toISOString() : null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "创建知识库文章成功",
      data: newArticle,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "创建知识库文章失败",
      error: error.message,
    });
  }
});

// 更新知识库文章
router.put("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { title, category, summary, content, author, status } = req.body;

    // 模拟更新文章 - 实际应该更新数据库记录
    const updatedArticle = {
      id: parseInt(id),
      title,
      category,
      summary,
      content,
      author,
      viewCount: 1250,
      status,
      publishDate: status === "published" ? "2024-01-01T10:00:00Z" : null,
      createdAt: "2024-01-01T10:00:00Z",
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "更新知识库文章成功",
      data: updatedArticle,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "更新知识库文章失败",
      error: error.message,
    });
  }
});

// 删除知识库文章
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    // 模拟删除文章 - 实际应该从数据库删除

    res.json({
      success: true,
      message: "删除知识库文章成功",
      data: { id: parseInt(id) },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "删除知识库文章失败",
      error: error.message,
    });
  }
});

module.exports = router;
