// 生产统计分析控制器
// 提供生产趋势、效率分析、成本分析等功能

const { Op } = require("sequelize");
const ProductionRecord = require("../models/production-record.model");
const UnifiedInventory = require("../models/unified-inventory.model");

// 获取生产趋势数据
exports.getProductionTrends = async (req, res) => {
  try {
    const { period = 'month', type = 'all', startDate, endDate } = req.query;
    const userId = req.user.id;

    // 设置日期范围
    let dateFilter = {};
    const now = new Date();
    
    if (startDate && endDate) {
      dateFilter = {
        recordedDate: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      };
    } else {
      // 根据period设置默认时间范围
      let startTime;
      if (period === 'week') {
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      } else if (period === 'month') {
        startTime = new Date(now.getFullYear(), now.getMonth(), 1);
      } else if (period === 'quarter') {
        const quarter = Math.floor(now.getMonth() / 3);
        startTime = new Date(now.getFullYear(), quarter * 3, 1);
      } else {
        startTime = new Date(now.getFullYear(), 0, 1); // 年度
      }
      
      dateFilter = {
        recordedDate: {
          [Op.gte]: startTime,
          [Op.lte]: now
        }
      };
    }

    // 查询生产记录
    const records = await ProductionRecord.findAll({
      where: {
        userId: userId,
        ...dateFilter
      },
      order: [['recordedDate', 'ASC']],
      attributes: ['recordedDate', 'eggCount', 'feedConsumption', 'temperature', 'humidity']
    });

    // 按时间聚合数据
    const trendsData = aggregateProductionData(records, period, type);

    res.json({
      success: true,
      data: {
        period: period,
        type: type,
        trends: trendsData,
        summary: {
          totalRecords: records.length,
          dateRange: { start: startTime, end: now }
        }
      }
    });

  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取生产趋势失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取生产趋势失败",
      error: process.env.NODE_ENV === "development" ? error.message : {}
    });
  }
};

// 获取生产效率分析
exports.getProductionEfficiency = async (req, res) => {
  try {
    const userId = req.user.id;
    const { timeRange = 30 } = req.query; // 默认分析最近30天

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeRange);

    // 查询生产记录
    const records = await ProductionRecord.findAll({
      where: {
        userId: userId,
        recordedDate: {
          [Op.gte]: startDate
        }
      },
      order: [['recordedDate', 'ASC']]
    });

    if (records.length === 0) {
      return res.json({
        success: true,
        data: {
          message: "暂无生产数据",
          efficiency: null
        }
      });
    }

    // 计算效率指标
    const totalEggs = records.reduce((sum, record) => sum + (record.eggCount || 0), 0);
    const totalFeed = records.reduce((sum, record) => sum + (record.feedConsumption || 0), 0);
    const avgEggsPerDay = totalEggs / records.length;
    const feedConversionRatio = totalFeed > 0 ? (totalEggs / totalFeed * 1000).toFixed(2) : 0; // 每公斤饲料产蛋数

    // 趋势分析
    const weeklyData = [];
    for (let i = 0; i < Math.min(4, Math.floor(records.length / 7)); i++) {
      const weekRecords = records.slice(i * 7, (i + 1) * 7);
      const weekEggs = weekRecords.reduce((sum, r) => sum + (r.eggCount || 0), 0);
      weeklyData.push({
        week: i + 1,
        totalEggs: weekEggs,
        avgDaily: (weekEggs / 7).toFixed(1)
      });
    }

    const efficiency = {
      totalProduction: totalEggs,
      avgDailyProduction: avgEggsPerDay.toFixed(1),
      feedConversionRatio: feedConversionRatio,
      productionEfficiency: calculateEfficiencyScore(records),
      weeklyTrends: weeklyData,
      recommendations: generateEfficiencyRecommendations(avgEggsPerDay, feedConversionRatio)
    };

    res.json({
      success: true,
      data: {
        timeRange: timeRange,
        efficiency: efficiency
      }
    });

  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取生产效率分析失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取生产效率分析失败",
      error: process.env.NODE_ENV === "development" ? error.message : {}
    });
  }
};

// 获取成本分析
exports.getCostAnalysis = async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = 'month' } = req.query;

    // 设置时间范围
    const now = new Date();
    let startDate;
    
    if (period === 'week') {
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    } else if (period === 'month') {
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    } else {
      startDate = new Date(now.getFullYear(), 0, 1);
    }

    // 获取库存成本数据
    const inventoryItems = await UnifiedInventory.findAll({
      where: {
        userId: userId,
        updatedAt: {
          [Op.gte]: startDate
        }
      },
      attributes: ['name', 'category', 'currentStock', 'unitPrice', 'totalValue']
    });

    // 获取生产记录
    const productionRecords = await ProductionRecord.findAll({
      where: {
        userId: userId,
        recordedDate: {
          [Op.gte]: startDate
        }
      },
      attributes: ['recordedDate', 'eggCount', 'feedConsumption']
    });

    // 计算成本分析
    const feedCosts = inventoryItems
      .filter(item => item.category === 'feed')
      .reduce((sum, item) => sum + (item.totalValue || 0), 0);

    const medicineCosts = inventoryItems
      .filter(item => item.category === 'medicine')
      .reduce((sum, item) => sum + (item.totalValue || 0), 0);

    const equipmentCosts = inventoryItems
      .filter(item => item.category === 'equipment')
      .reduce((sum, item) => sum + (item.totalValue || 0), 0);

    const totalEggs = productionRecords.reduce((sum, record) => sum + (record.eggCount || 0), 0);
    const totalCosts = feedCosts + medicineCosts + equipmentCosts;
    const costPerEgg = totalEggs > 0 ? (totalCosts / totalEggs).toFixed(2) : 0;

    const costAnalysis = {
      period: period,
      totalCosts: totalCosts.toFixed(2),
      costBreakdown: {
        feed: feedCosts.toFixed(2),
        medicine: medicineCosts.toFixed(2),
        equipment: equipmentCosts.toFixed(2)
      },
      production: {
        totalEggs: totalEggs,
        costPerEgg: costPerEgg
      },
      profitability: calculateProfitability(totalCosts, totalEggs)
    };

    res.json({
      success: true,
      data: costAnalysis
    });

  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取成本分析失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取成本分析失败",
      error: process.env.NODE_ENV === "development" ? error.message : {}
    });
  }
};

// 批量创建生产记录
exports.batchCreateRecords = async (req, res) => {
  try {
    const { records } = req.body;
    const userId = req.user.id;

    if (!records || !Array.isArray(records) || records.length === 0) {
      return res.status(400).json({
        success: false,
        message: "记录数据不能为空"
      });
    }

    // 为每条记录添加用户ID
    const recordsWithUserId = records.map(record => ({
      ...record,
      userId: userId
    }));

    // 批量创建
    const createdRecords = await ProductionRecord.bulkCreate(recordsWithUserId, {
      validate: true,
      returning: true
    });

    res.status(201).json({
      success: true,
      message: `成功创建 ${createdRecords.length} 条生产记录`,
      data: {
        created: createdRecords.length,
        records: createdRecords
      }
    });

  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("批量创建生产记录失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "批量创建失败",
      error: process.env.NODE_ENV === "development" ? error.message : {}
    });
  }
};

// 辅助函数：聚合生产数据
function aggregateProductionData(records, period, type) {
  const data = {};
  
  records.forEach(record => {
    let key;
    const date = new Date(record.recordedDate);
    
    if (period === 'week') {
      key = `${date.getFullYear()}-W${Math.ceil(date.getDate()/7)}`;
    } else if (period === 'month') {
      key = `${date.getFullYear()}-${(date.getMonth()+1).toString().padStart(2, '0')}`;
    } else {
      key = date.toISOString().split('T')[0];
    }
    
    if (!data[key]) {
      data[key] = { date: key, eggCount: 0, feedConsumption: 0, records: 0 };
    }
    
    data[key].eggCount += record.eggCount || 0;
    data[key].feedConsumption += record.feedConsumption || 0;
    data[key].records += 1;
  });
  
  return Object.values(data).sort((a, b) => a.date.localeCompare(b.date));
}

// 辅助函数：计算效率评分
function calculateEfficiencyScore(records) {
  if (records.length === 0) return 0;
  
  const avgEggs = records.reduce((sum, r) => sum + (r.eggCount || 0), 0) / records.length;
  const targetEggs = 25; // 假设每天目标产蛋25个
  
  return Math.min(100, Math.round((avgEggs / targetEggs) * 100));
}

// 辅助函数：生成效率建议
function generateEfficiencyRecommendations(avgDaily, feedRatio) {
  const recommendations = [];
  
  if (avgDaily < 20) {
    recommendations.push("产蛋率偏低，建议检查饲料营养配比");
  }
  if (feedRatio < 15) {
    recommendations.push("饲料转化率较低，建议优化饲料配方");
  }
  if (avgDaily > 30) {
    recommendations.push("产蛋表现优秀，继续保持现有管理方式");
  }
  
  return recommendations;
}

// 辅助函数：计算盈利能力
function calculateProfitability(totalCosts, totalEggs) {
  const eggPrice = 0.8; // 假设每个蛋售价0.8元
  const revenue = totalEggs * eggPrice;
  const profit = revenue - totalCosts;
  const profitMargin = revenue > 0 ? ((profit / revenue) * 100).toFixed(1) : 0;
  
  return {
    revenue: revenue.toFixed(2),
    profit: profit.toFixed(2),
    profitMargin: profitMargin + '%'
  };
}