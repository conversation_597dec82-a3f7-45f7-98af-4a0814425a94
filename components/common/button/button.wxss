/* 通用按钮组件样式 */

/* 统一使用全局设计系统变量 */
:host {
  /* 继承全局设计系统变量，无需重复定义 */
}

/* 基础按钮样式 - 统一设计系统 */
.c-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  border: 1rpx solid transparent;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  font-family: var(--font-family);
  line-height: var(--leading-none);
  text-align: center;
  white-space: nowrap;
  user-select: none;
  touch-action: manipulation;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  gap: var(--space-sm);
  
  /* 重置小程序button默认样式 */
  background: none;
  margin: 0;
  padding: 0;
  outline: none;
}

.c-button::after {
  border: none;
}

/* 按钮内容容器 */
.c-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* ===== 按钮类型样式 ===== */

/* 默认按钮 */
.c-button--default {
  background-color: #FFFFFF;
  border-color: var(--c-button-border-color);
  color: var(--c-button-text-secondary);
}

.c-button--default:not(.c-button--disabled):active {
  background-color: #F9FAFB;
  border-color: #D1D5DB;
}

/* 主要按钮 */
.c-button--primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-color: var(--primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.c-button--primary:not(.c-button--disabled):active {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

/* 成功按钮 */
.c-button--success {
  background-color: var(--success);
  border-color: var(--success);
  color: var(--text-inverse);
}

.c-button--success:not(.c-button--disabled):active {
  background-color: var(--success-light);
  transform: scale(0.98);
}

/* 警告按钮 */
.c-button--warning {
  background-color: var(--c-button-warning-color);
  border-color: var(--c-button-warning-color);
  color: #333333;
}

.c-button--warning:not(.c-button--disabled):active {
  background-color: #E0A800;
  border-color: #E0A800;
}

/* 错误/危险按钮 */
.c-button--error {
  background-color: var(--c-button-error-color);
  border-color: var(--c-button-error-color);
  color: var(--c-button-text-primary);
}

.c-button--error:not(.c-button--disabled):active {
  background-color: #C82333;
  border-color: #C82333;
}

/* 信息按钮 */
.c-button--info {
  background-color: var(--c-button-info-color);
  border-color: var(--c-button-info-color);
  color: var(--c-button-text-primary);
}

.c-button--info:not(.c-button--disabled):active {
  background-color: #138496;
  border-color: #138496;
}

/* 文本按钮 */
.c-button--text {
  background-color: transparent;
  border-color: transparent;
  color: var(--c-button-primary-color);
  box-shadow: none;
}

.c-button--text:not(.c-button--disabled):active {
  background-color: rgba(46, 139, 87, 0.1);
}

/* 链接按钮 */
.c-button--link {
  background-color: transparent;
  border-color: transparent;
  color: var(--c-button-primary-color);
  text-decoration: underline;
  box-shadow: none;
}

.c-button--link:not(.c-button--disabled):active {
  color: #246A42;
}

/* ===== 按钮尺寸样式 ===== */

/* 小尺寸 */
.c-button--small {
  height: 60rpx;
  padding: 0 24rpx;
  font-size: 24rpx;
}

.c-button--small .c-button__icon {
  width: 32rpx;
  height: 32rpx;
}

/* 中等尺寸（默认） */
.c-button--medium {
  height: 80rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
}

.c-button--medium .c-button__icon {
  width: 40rpx;
  height: 40rpx;
}

/* 大尺寸 */
.c-button--large {
  height: 100rpx;
  padding: 0 40rpx;
  font-size: 32rpx;
}

.c-button--large .c-button__icon {
  width: 48rpx;
  height: 48rpx;
}

/* ===== 按钮状态样式 ===== */

/* 禁用状态 */
.c-button--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 加载状态 */
.c-button--loading {
  pointer-events: none;
}

/* 块级按钮 */
.c-button--block {
  display: flex;
  width: 100%;
}

/* 圆角按钮 */
.c-button--round {
  border-radius: var(--c-button-border-radius-round);
}

/* 朴素按钮 */
.c-button--plain.c-button--primary {
  background-color: transparent;
  color: var(--c-button-primary-color);
}

.c-button--plain.c-button--success {
  background-color: transparent;
  color: var(--c-button-success-color);
}

.c-button--plain.c-button--warning {
  background-color: transparent;
  color: var(--c-button-warning-color);
}

.c-button--plain.c-button--error {
  background-color: transparent;
  color: var(--c-button-error-color);
}

.c-button--plain.c-button--info {
  background-color: transparent;
  color: var(--c-button-info-color);
}

/* ===== 按钮内容样式 ===== */

/* 按钮文本 */
.c-button__text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 按钮图标 */
.c-button__icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.c-button__icon--left {
  margin-right: 12rpx;
}

.c-button__icon--right {
  margin-left: 12rpx;
}

.c-button__icon-img {
  width: 100%;
  height: 100%;
}

/* 加载图标 */
.c-button__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.c-button__loading-icon {
  width: 100%;
  height: 100%;
  animation: c-button-spin 1s linear infinite;
}

/* 自定义内容插槽 */
.c-button__slot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 动画 */
@keyframes c-button-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ===== 响应式设计 ===== */

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  :host {
    --c-button-default-color: #9CA3AF;
    --c-button-text-secondary: #F9FAFB;
    --c-button-border-color: #374151;
  }
  
  .c-button--default {
    background-color: #1F2937;
    color: var(--c-button-text-secondary);
  }
  
  .c-button--default:not(.c-button--disabled):active {
    background-color: #374151;
  }
}