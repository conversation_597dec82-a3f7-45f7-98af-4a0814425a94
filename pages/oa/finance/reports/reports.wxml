<!--pages/oa/finance/reports/reports.wxml-->
<view class="oa-container">
  <!-- 页面头部 -->
  <view class="oa-page-header">
    <view class="oa-header-content">
      <text class="oa-page-title">财务报表</text>
      <text class="oa-page-subtitle">数据分析与导出</text>
    </view>
  </view>

  <!-- 报表类型选择 -->
  <view class="report-types-section">
    <view class="section-title">报表类型</view>
    <scroll-view class="report-types-scroll" scroll-x="true">
      <view class="report-types-list">
        <view wx:for="{{reportTypes}}" wx:key="id" 
              class="report-type-item {{selectedReportType === item.id ? 'active' : ''}}"
              bindtap="onReportTypeChange" data-type="{{item.id}}">
          <view class="type-icon">报表</view>
          <text class="type-name">{{item.name}}</text>
          <text class="type-desc">{{item.description}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 时间范围选择 -->
  <view class="time-range-section">
    <view class="section-title">时间范围</view>
    <view class="time-range-picker">
      <view class="selected-range" bindtap="toggleTimeRangePicker">
        <text>{{selectedTimeRangeName}}</text>
        <text class="arrow {{showTimeRangePicker ? 'up' : 'down'}}">▼</text>
      </view>
      
      <!-- 时间范围选项 -->
      <view class="range-options {{showTimeRangePicker ? 'show' : ''}}">
        <view wx:for="{{timeRanges}}" wx:key="id"
              class="range-option {{selectedTimeRange === item.id ? 'active' : ''}}"
              bindtap="onTimeRangeChange" data-range="{{item.id}}">
          {{item.name}}
        </view>
      </view>
    </view>
  </view>

  <!-- 自定义日期选择弹窗 -->
  <view class="custom-date-modal {{showCustomDatePicker ? 'show' : ''}}" wx:if="{{showCustomDatePicker}}">
    <view class="modal-mask" bindtap="cancelCustomDate"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择日期范围</text>
        <text class="close-btn" bindtap="cancelCustomDate">×</text>
      </view>
      <view class="date-inputs">
        <view class="date-input-item">
          <text class="label">开始日期</text>
          <picker mode="date" value="{{customStartDate}}" bindchange="onStartDateChange">
            <view class="picker-input">
              <text>{{customStartDate || '请选择'}}</text>
            </view>
          </picker>
        </view>
        <view class="date-input-item">
          <text class="label">结束日期</text>
          <picker mode="date" value="{{customEndDate}}" bindchange="onEndDateChange">
            <view class="picker-input">
              <text>{{customEndDate || '请选择'}}</text>
            </view>
          </picker>
        </view>
      </view>
      <view class="modal-actions">
        <button class="cancel-btn" bindtap="cancelCustomDate">取消</button>
        <button class="confirm-btn" bindtap="confirmCustomDate">确定</button>
      </view>
    </view>
  </view>

  <!-- 报表摘要 -->
  <view class="summary-section" wx:if="{{reportData}}">
    <view class="section-title">数据摘要</view>
    <view class="summary-cards">
      <view class="summary-card income">
        <view class="card-value">{{formatCurrency(summary.totalIncome)}}</view>
        <text class="card-label">总收入</text>
      </view>
      <view class="summary-card expense">
        <view class="card-value">{{formatCurrency(summary.totalExpense)}}</view>
        <text class="card-label">总支出</text>
      </view>
      <view class="summary-card profit">
        <view class="card-value">{{formatCurrency(summary.netProfit)}}</view>
        <text class="card-label">净利润</text>
      </view>
      <view class="summary-card count">
        <view class="card-value">{{summary.recordCount}}</view>
        <text class="card-label">记录数</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="actions-section">
    <button class="action-btn primary {{generating ? 'loading' : ''}}" 
            bindtap="generateReport" disabled="{{generating}}">
      {{generating ? '生成中...' : '生成报表'}}
    </button>
    
    <!-- 导出选项 -->
    <view class="export-actions" wx:if="{{reportData && permissions.canExportData}}">
      <text class="export-label">导出为：</text>
      <view class="export-buttons">
        <button wx:for="{{exportFormats}}" wx:key="id"
                class="export-btn" 
                bindtap="exportReport" 
                data-format="{{item.id}}">
          {{item.name}}
        </button>
      </view>
    </view>
  </view>

  <!-- 报表数据展示 -->
  <view class="report-data-section" wx:if="{{reportData && reportData.length > 0}}">
    <view class="section-title">报表数据</view>
    
    <!-- 收支明细表 -->
    <view class="data-table" wx:if="{{selectedReportType === 'income_expense'}}">
      <view class="table-header">
        <text class="col-date">日期</text>
        <text class="col-type">类型</text>
        <text class="col-amount">金额</text>
        <text class="col-desc">描述</text>
      </view>
      <view class="table-body">
        <view wx:for="{{reportData}}" wx:key="id" 
              class="table-row {{item.type}}"
              bindtap="onReportItemTap" data-item="{{item}}">
          <text class="col-date">{{formatDate(item.date, 'MM-DD')}}</text>
          <text class="col-type">{{item.type === 'income' ? '收入' : '支出'}}</text>
          <text class="col-amount {{item.type}}">{{formatCurrency(item.amount)}}</text>
          <text class="col-desc">{{item.description || '-'}}</text>
        </view>
      </view>
    </view>

    <!-- 月度汇总表 -->
    <view class="summary-table" wx:if="{{selectedReportType === 'monthly_summary'}}">
      <view class="table-header">
        <text class="col-month">月份</text>
        <text class="col-income">收入</text>
        <text class="col-expense">支出</text>
        <text class="col-profit">净利润</text>
      </view>
      <view class="table-body">
        <view wx:for="{{reportData}}" wx:key="month" class="table-row">
          <text class="col-month">{{item.month}}</text>
          <text class="col-income">{{formatCurrency(item.totalIncome)}}</text>
          <text class="col-expense">{{formatCurrency(item.totalExpense)}}</text>
          <text class="col-profit {{item.netProfit >= 0 ? 'positive' : 'negative'}}">
            {{formatCurrency(item.netProfit)}}
          </text>
        </view>
      </view>
    </view>

    <!-- 分类分析表 -->
    <view class="category-table" wx:if="{{selectedReportType === 'category_analysis'}}">
      <view class="table-header">
        <text class="col-category">分类</text>
        <text class="col-amount">金额</text>
        <text class="col-percent">占比</text>
        <text class="col-count">笔数</text>
      </view>
      <view class="table-body">
        <view wx:for="{{reportData}}" wx:key="category" class="table-row">
          <text class="col-category">{{item.category}}</text>
          <text class="col-amount">{{formatCurrency(item.amount)}}</text>
          <text class="col-percent">{{formatPercent(item.percentage)}}</text>
          <text class="col-count">{{item.count}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && (!reportData || reportData.length === 0)}}">
          <view class="empty-icon">财务报表</view>
    <text class="empty-title">暂无报表数据</text>
    <text class="empty-desc">请选择时间范围后生成报表</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载数据...</text>
    </view>
  </view>
</view>