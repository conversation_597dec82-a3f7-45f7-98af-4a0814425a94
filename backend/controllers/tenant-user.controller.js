/**
 * 租户用户控制器 - 多租户用户管理
 * Tenant User Controller - Multi-tenant user management
 */

/**
 * 获取用户列表
 */
exports.getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, role, status, search } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = "WHERE 1=1";
    const replacements = [];

    if (role) {
      whereClause += " AND role = ?";
      replacements.push(role);
    }

    if (status) {
      whereClause += " AND status = ?";
      replacements.push(status);
    }

    if (search) {
      whereClause += " AND (nickname LIKE ? OR phone LIKE ?)";
      replacements.push(`%${search}%`, `%${search}%`);
    }

    // 获取总数
    const [countResult] = await req.tenantDb.query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      { replacements },
    );
    const total = countResult[0].total;

    // 获取分页数据
    const [users] = await req.tenantDb.query(
      `SELECT id, nickname, avatar, gender, city, province, role, status, lastLoginAt, createdAt
       FROM users 
       ${whereClause} 
       ORDER BY createdAt DESC 
       LIMIT ? OFFSET ?`,
      {
        replacements: [...replacements, parseInt(limit), parseInt(offset)],
      },
    );

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取用户列表失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取用户列表失败",
    });
  }
};

/**
 * 获取用户详情
 */
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const [users] = await req.tenantDb.query(
      "SELECT id, nickname, avatar, gender, city, province, country, role, status, lastLoginAt, createdAt FROM users WHERE id = ?",
      { replacements: [id] },
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: "用户不存在",
      });
    }

    res.json({
      success: true,
      data: users[0],
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取用户详情失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取用户详情失败",
    });
  }
};

/**
 * 更新用户信息
 */
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updateFields = req.body;

    // 检查用户是否存在
    const [existingUser] = await req.tenantDb.query(
      "SELECT * FROM users WHERE id = ?",
      { replacements: [id] },
    );

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: "用户不存在",
      });
    }

    // 构建更新语句
    const allowedFields = ["nickname", "avatar", "phone", "role", "status"];
    const updatePairs = [];
    const values = [];

    for (const [key, value] of Object.entries(updateFields)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updatePairs.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (updatePairs.length === 0) {
      return res.status(400).json({
        success: false,
        message: "没有有效的更新字段",
      });
    }

    updatePairs.push("updatedAt = NOW()");
    values.push(id);

    await req.tenantDb.query(
      `UPDATE users SET ${updatePairs.join(", ")} WHERE id = ?`,
      { replacements: values },
    );

    res.json({
      success: true,
      message: "用户信息更新成功",
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新用户信息失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "更新用户信息失败",
    });
  }
};
