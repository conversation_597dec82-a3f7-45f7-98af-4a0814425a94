// components/goose-price/goose-price.js
Component({
  properties: {
    // 默认显示的标签页
    defaultTab: {
      type: String,
      value: 'gosling'
    }
  },

  data: {
    activeTab: 'gosling',
    updateTime: '',
    goslingPrices: [],
    adultPrices: [],
    loading: true
  },

  lifetimes: {
    attached() {
      this.setData({
        activeTab: this.properties.defaultTab
      });
      this.loadPriceData();
    }
  },

  methods: {
    // 切换标签页
    onTabChange(e) {
      const tab = e.currentTarget.dataset.tab;
      // 确保tab是有效的值
      if (tab !== undefined && tab !== null && tab !== '') {
        this.setData({
          activeTab: tab
        });
      } else {
        try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[GoosePrice] Invalid tab value', tab); } catch(_) {}
        return;
      }
    },

    // 加载价格数据
    loadPriceData() {
      this.setData({
        loading: true
      });

      // 模拟API调用
      setTimeout(() => {
        const priceData = this.getMockPriceData();
        this.setData({
          ...priceData,
          loading: false,
          updateTime: this.formatTime(new Date())
        });
      }, 100);
    },

    // 获取模拟价格数据
    getMockPriceData() {
      // 鹅苗价格数据
      const goslingPrices = [
        {
          breed: '白鹅苗',
          description: '优质白鹅品种',
          price: 12.5,
          change: 2.3
        },
        {
          breed: '灰鹅苗',
          description: '抗病能力强',
          price: 11.8,
          change: -1.2
        },
        {
          breed: '狮头鹅苗',
          description: '大型鹅种',
          price: 15.0,
          change: 3.1
        },
        {
          breed: '四川白鹅苗',
          description: '生长快速',
          price: 13.2,
          change: 0.8
        },
        {
          breed: '皖西白鹅苗',
          description: '肉质鲜美',
          price: 14.5,
          change: -0.5
        }
      ];

      // 成鹅价格数据
      const adultPrices = [
        {
          breed: '白鹅',
          description: '活重8-10斤',
          price: 18.5,
          change: 1.8
        },
        {
          breed: '灰鹅',
          description: '活重7-9斤',
          price: 17.2,
          change: -2.1
        },
        {
          breed: '狮头鹅',
          description: '活重12-15斤',
          price: 22.0,
          change: 4.2
        },
        {
          breed: '四川白鹅',
          description: '活重9-11斤',
          price: 19.8,
          change: 2.5
        },
        {
          breed: '皖西白鹅',
          description: '活重8-10斤',
          price: 20.5,
          change: -1.3
        }
      ];

      return {
        goslingPrices: goslingPrices.slice(0, 3),
        adultPrices: adultPrices.slice(0, 3)
      };
    },

    // 刷新价格数据
    refreshPrices() {
      wx.showToast({
        title: '刷新中...',
        icon: 'loading',
        duration: 1000
      });

      this.loadPriceData();
    },



    // 格式化时间
    formatTime(date) {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${month}月${day}日 ${hours}:${minutes}`;
    },

    // 点击价格容器跳转到详情页面
    onPriceContainerTap() {
      wx.navigateTo({
        url: `/pages/price/price-detail/price-detail?type=${this.data.activeTab}`
      });
    }
  }
});
