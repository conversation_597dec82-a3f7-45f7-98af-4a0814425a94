// backend/models/permission.model.js
// 权限模型

const { DataTypes } = require("sequelize");
const { sequelize } = require("../config/database");

// 权限模型
const Permission = sequelize.define(
  "Permission",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: "权限名称",
    },
    code: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: "权限代码",
    },
    resource: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: "资源类型",
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: "操作类型",
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "权限描述",
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: "权限分类",
    },
    level: {
      type: DataTypes.ENUM("SYSTEM", "MODULE", "FEATURE", "DATA"),
      allowNull: false,
      defaultValue: "FEATURE",
      comment: "权限级别",
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: "is_active",
      comment: "是否启用",
    },
  },
  {
    tableName: "permissions",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        fields: ["resource", "action"],
      },
      {
        fields: ["category"],
      },
      {
        fields: ["level"],
      },
      {
        fields: ["is_active"],
      },
    ],
  },
);

// 角色模型
const Role = sequelize.define(
  "Role",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: "角色名称",
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: "角色代码",
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "角色描述",
    },
    level: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: "角色级别，数字越大权限越高",
    },
    isSystem: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: "is_system",
      comment: "是否为系统角色",
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: "is_active",
      comment: "是否启用",
    },
  },
  {
    tableName: "roles",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        fields: ["level"],
      },
      {
        fields: ["is_system"],
      },
      {
        fields: ["is_active"],
      },
    ],
  },
);

// 角色权限关联模型
const RolePermission = sequelize.define(
  "RolePermission",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    roleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "role_id",
      comment: "角色ID",
    },
    permissionId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "permission_id",
      comment: "权限ID",
    },
    grantedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "granted_by",
      comment: "授权人ID",
    },
    grantedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: "granted_at",
      comment: "授权时间",
    },
  },
  {
    tableName: "role_permissions",
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ["role_id", "permission_id"],
      },
      {
        fields: ["granted_by"],
      },
    ],
  },
);

// 用户角色关联模型
const UserRole = sequelize.define(
  "UserRole",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "user_id",
      comment: "用户ID",
    },
    roleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "role_id",
      comment: "角色ID",
    },
    assignedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "assigned_by",
      comment: "分配人ID",
    },
    assignedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: "assigned_at",
      comment: "分配时间",
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "expires_at",
      comment: "过期时间",
    },
  },
  {
    tableName: "user_roles",
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ["user_id", "role_id"],
      },
      {
        fields: ["assigned_by"],
      },
      {
        fields: ["expires_at"],
      },
    ],
  },
);

// 资源权限模型（用于数据级权限控制）
const ResourcePermission = sequelize.define(
  "ResourcePermission",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "user_id",
      comment: "用户ID",
    },
    resourceType: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: "resource_type",
      comment: "资源类型",
    },
    resourceId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: "resource_id",
      comment: "资源ID",
    },
    permissions: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: "权限列表",
    },
    grantedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "granted_by",
      comment: "授权人ID",
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "expires_at",
      comment: "过期时间",
    },
  },
  {
    tableName: "resource_permissions",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        fields: ["user_id", "resource_type", "resource_id"],
      },
      {
        fields: ["resource_type"],
      },
      {
        fields: ["expires_at"],
      },
    ],
  },
);

// 定义关联关系
Role.belongsToMany(Permission, {
  through: RolePermission,
  foreignKey: "role_id",
  otherKey: "permission_id",
  as: "permissions",
});

Permission.belongsToMany(Role, {
  through: RolePermission,
  foreignKey: "permission_id",
  otherKey: "role_id",
  as: "roles",
});

// 权限常量定义
const PERMISSIONS = {
  // 系统管理权限
  SYSTEM_ADMIN: "system:admin",
  SYSTEM_CONFIG: "system:config",
  SYSTEM_MONITOR: "system:monitor",

  // 用户管理权限
  USER_CREATE: "user:create",
  USER_READ: "user:read",
  USER_UPDATE: "user:update",
  USER_DELETE: "user:delete",
  USER_MANAGE_ROLES: "user:manage_roles",

  // 健康记录权限
  HEALTH_CREATE: "health:create",
  HEALTH_READ: "health:read",
  HEALTH_READ_ALL: "health:read_all",
  HEALTH_UPDATE: "health:update",
  HEALTH_UPDATE_ALL: "health:update_all",
  HEALTH_DELETE: "health:delete",
  HEALTH_DELETE_ALL: "health:delete_all",
  HEALTH_EXPORT: "health:export",

  // 生产记录权限
  PRODUCTION_CREATE: "production:create",
  PRODUCTION_READ: "production:read",
  PRODUCTION_READ_ALL: "production:read_all",
  PRODUCTION_UPDATE: "production:update",
  PRODUCTION_UPDATE_ALL: "production:update_all",
  PRODUCTION_DELETE: "production:delete",
  PRODUCTION_DELETE_ALL: "production:delete_all",
  PRODUCTION_EXPORT: "production:export",

  // 库存管理权限
  INVENTORY_CREATE: "inventory:create",
  INVENTORY_READ: "inventory:read",
  INVENTORY_UPDATE: "inventory:update",
  INVENTORY_DELETE: "inventory:delete",
  INVENTORY_MANAGE: "inventory:manage",

  // 公告管理权限
  ANNOUNCEMENT_CREATE: "announcement:create",
  ANNOUNCEMENT_READ: "announcement:read",
  ANNOUNCEMENT_UPDATE: "announcement:update",
  ANNOUNCEMENT_DELETE: "announcement:delete",
  ANNOUNCEMENT_PUBLISH: "announcement:publish",

  // AI功能权限
  AI_DIAGNOSIS: "ai:diagnosis",
  AI_CONFIG: "ai:config",
  AI_MANAGE: "ai:manage",

  // 报表权限
  REPORT_VIEW: "report:view",
  REPORT_EXPORT: "report:export",
  REPORT_MANAGE: "report:manage",

  // 批量操作权限
  BATCH_OPERATION: "batch:operation",
  BATCH_DELETE: "batch:delete",
  BATCH_UPDATE: "batch:update",
  BATCH_EXPORT: "batch:export",
};

// 角色常量定义
const ROLES = {
  SUPER_ADMIN: "super_admin",
  ADMIN: "admin",
  MANAGER: "manager",
  USER: "user",
  VIEWER: "viewer",
};

// 静态方法：初始化默认权限和角色
Permission.initializeDefaults = async function () {
  const defaultPermissions = [
    // 系统权限
    {
      name: "系统管理",
      code: PERMISSIONS.SYSTEM_ADMIN,
      resource: "system",
      action: "admin",
      category: "system",
      level: "SYSTEM",
    },
    {
      name: "系统配置",
      code: PERMISSIONS.SYSTEM_CONFIG,
      resource: "system",
      action: "config",
      category: "system",
      level: "SYSTEM",
    },
    {
      name: "系统监控",
      code: PERMISSIONS.SYSTEM_MONITOR,
      resource: "system",
      action: "monitor",
      category: "system",
      level: "SYSTEM",
    },

    // 用户权限
    {
      name: "创建用户",
      code: PERMISSIONS.USER_CREATE,
      resource: "user",
      action: "create",
      category: "user",
      level: "MODULE",
    },
    {
      name: "查看用户",
      code: PERMISSIONS.USER_READ,
      resource: "user",
      action: "read",
      category: "user",
      level: "MODULE",
    },
    {
      name: "更新用户",
      code: PERMISSIONS.USER_UPDATE,
      resource: "user",
      action: "update",
      category: "user",
      level: "MODULE",
    },
    {
      name: "删除用户",
      code: PERMISSIONS.USER_DELETE,
      resource: "user",
      action: "delete",
      category: "user",
      level: "MODULE",
    },
    {
      name: "管理用户角色",
      code: PERMISSIONS.USER_MANAGE_ROLES,
      resource: "user",
      action: "manage_roles",
      category: "user",
      level: "MODULE",
    },

    // 健康记录权限
    {
      name: "创建健康记录",
      code: PERMISSIONS.HEALTH_CREATE,
      resource: "health",
      action: "create",
      category: "health",
      level: "FEATURE",
    },
    {
      name: "查看健康记录",
      code: PERMISSIONS.HEALTH_READ,
      resource: "health",
      action: "read",
      category: "health",
      level: "FEATURE",
    },
    {
      name: "查看所有健康记录",
      code: PERMISSIONS.HEALTH_READ_ALL,
      resource: "health",
      action: "read_all",
      category: "health",
      level: "DATA",
    },
    {
      name: "更新健康记录",
      code: PERMISSIONS.HEALTH_UPDATE,
      resource: "health",
      action: "update",
      category: "health",
      level: "FEATURE",
    },
    {
      name: "更新所有健康记录",
      code: PERMISSIONS.HEALTH_UPDATE_ALL,
      resource: "health",
      action: "update_all",
      category: "health",
      level: "DATA",
    },
    {
      name: "删除健康记录",
      code: PERMISSIONS.HEALTH_DELETE,
      resource: "health",
      action: "delete",
      category: "health",
      level: "FEATURE",
    },
    {
      name: "删除所有健康记录",
      code: PERMISSIONS.HEALTH_DELETE_ALL,
      resource: "health",
      action: "delete_all",
      category: "health",
      level: "DATA",
    },
    {
      name: "导出健康记录",
      code: PERMISSIONS.HEALTH_EXPORT,
      resource: "health",
      action: "export",
      category: "health",
      level: "FEATURE",
    },
  ];

  for (const perm of defaultPermissions) {
    await this.findOrCreate({
      where: { code: perm.code },
      defaults: perm,
    });
  }
};

Role.initializeDefaults = async function () {
  const defaultRoles = [
    {
      name: "超级管理员",
      code: ROLES.SUPER_ADMIN,
      description: "系统超级管理员，拥有所有权限",
      level: 100,
      isSystem: true,
    },
    {
      name: "管理员",
      code: ROLES.ADMIN,
      description: "系统管理员，拥有大部分管理权限",
      level: 80,
      isSystem: true,
    },
    {
      name: "经理",
      code: ROLES.MANAGER,
      description: "部门经理，拥有部门管理权限",
      level: 60,
      isSystem: false,
    },
    {
      name: "用户",
      code: ROLES.USER,
      description: "普通用户，拥有基本操作权限",
      level: 40,
      isSystem: false,
    },
    {
      name: "访客",
      code: ROLES.VIEWER,
      description: "只读用户，只能查看数据",
      level: 20,
      isSystem: false,
    },
  ];

  for (const role of defaultRoles) {
    await this.findOrCreate({
      where: { code: role.code },
      defaults: role,
    });
  }
};

module.exports = {
  Permission,
  Role,
  RolePermission,
  UserRole,
  ResourcePermission,
  PERMISSIONS,
  ROLES,
};
