/* 生产记录管理表单 - 重新设计版本 */

/* 全局变量 */
page {
  --primary-color: #1677ff;
  --primary-hover: #4096ff;
  --success-color: #52c41a;
  --warning-color: #fa8c16;
  --error-color: #ff4d4f;
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-disabled: #bfbfbf;
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  --bg-container: #ffffff;
  --bg-layout: #f5f5f5;
  --shadow-1: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-2: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-3: 0 6px 16px rgba(0, 0, 0, 0.10);
  --radius-sm: 6rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
}

/* 主容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f0f9ff 0%, var(--bg-layout) 200rpx);
  display: flex;
  flex-direction: column;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  padding: var(--spacing-lg) var(--spacing-md);
  gap: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid var(--border-light);
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) var(--spacing-md);
  border-radius: var(--radius-lg);
  background: transparent;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tab-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 4rpx;
  background: var(--primary-color);
  border-radius: 2rpx;
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-item.active::before {
  width: 60%;
}

.tab-item.active {
  background: rgba(22, 119, 255, 0.05);
}

.tab-text {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.tab-item.active .tab-text {
  color: var(--primary-color);
  font-weight: 600;
}

/* 表单滚动区域 */
.form-scroll {
  flex: 1;
  padding: 0 var(--spacing-md);
}

.form-wrapper {
  padding: var(--spacing-lg) 0;
}

/* 卡片样式 */
.card {
  background: var(--bg-container);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-1);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-2);
}

.card-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: transparent;
  border-bottom: 1rpx solid var(--border-light);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

.card-content {
  padding: var(--spacing-xl);
}

/* 输入组样式 */
.input-group {
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-label {
  flex-shrink: 0;
  width: 160rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  text-align: right;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  letter-spacing: 0.3rpx;
}

.required {
  color: var(--error-color);
  font-size: 28rpx;
  font-weight: 600;
}

.input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  height: 88rpx;
  min-height: 88rpx;
}

/* 确保picker作为input-wrapper时样式一致 */
picker.input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  height: 88rpx;
  min-height: 88rpx;
}

.form-input {
  flex: 1;
  height: 88rpx;
  min-height: 88rpx;
  max-height: 88rpx;
  padding: 0 var(--spacing-lg);
  background: #fafafa;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.form-input:focus {
  background: var(--bg-container);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(22, 119, 255, 0.1);
  transform: translateY(-1rpx);
}

.form-input.picker:active {
  background: var(--bg-container);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(22, 119, 255, 0.1);
  transform: translateY(-1rpx);
}

.form-input.picker {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  height: 88rpx;
  min-height: 88rpx;
  max-height: 88rpx;
  background: #fafafa;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  line-height: 1.5;
}

.form-input .value {
  flex: 1;
  color: var(--text-primary);
  font-size: 28rpx;
  line-height: 1.5;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.form-input .placeholder {
  flex: 1;
  color: var(--text-disabled);
  font-size: 28rpx;
  line-height: 1.5;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
  flex-shrink: 0;
  opacity: 0.6;
  transition: transform 0.3s ease;
  margin-left: var(--spacing-sm);
}

.form-input:active .picker-arrow {
  transform: rotate(180deg);
}

.input-unit {
  position: absolute;
  right: var(--spacing-lg);
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
  background: var(--bg-container);
  padding: 0 var(--spacing-xs);
}

/* 收入显示 */
.income-display {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 0 var(--spacing-lg);
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
  border: 2rpx solid #b7eb8f;
  border-radius: var(--radius-md);
  box-shadow: inset 0 1rpx 3rpx rgba(82, 196, 26, 0.1);
}

.currency {
  font-size: 28rpx;
  color: var(--success-color);
  font-weight: 600;
}

.amount {
  font-size: 36rpx;
  color: var(--success-color);
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

/* 文本域样式 */
.textarea-wrapper {
  position: relative;
  flex: 1;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: var(--spacing-lg);
  background: #fafafa;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  resize: none;
}

.form-textarea:focus {
  background: var(--bg-container);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(22, 119, 255, 0.1);
}

/* 提示信息 */
.info-tip {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #fff7e6 0%, #fef0e6 100%);
  border: 2rpx solid #ffec8b;
  border-radius: var(--radius-md);
  box-shadow: inset 0 1rpx 3rpx rgba(250, 140, 22, 0.1);
}

.tip-icon {
  font-size: 32rpx;
  line-height: 1;
}

.tip-text {
  font-size: 26rpx;
  color: #ad6800;
  line-height: 1.5;
  font-weight: 500;
}

/* 批次信息显示 */
.batch-info {
  flex: 1;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2rpx solid #87ceeb;
  border-radius: var(--radius-md);
  box-shadow: inset 0 1rpx 3rpx rgba(14, 165, 233, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 120rpx;
}

.info-value {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: 600;
}

/* 底部间距 */
.bottom-space {
  height: 120rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-lg);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 20%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  gap: var(--spacing-md);
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.action-btn:active::before {
  width: 300rpx;
  height: 300rpx;
}

.action-btn.cancel {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  color: var(--text-secondary);
  border: 2rpx solid var(--border-color);
}

.action-btn.cancel:active {
  background: linear-gradient(135deg, #e8e8e8 0%, #d9d9d9 100%);
  transform: scale(0.98);
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: #ffffff;
  box-shadow: var(--shadow-2);
}

.action-btn.primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-1);
}

.action-btn.primary[loading] {
  opacity: 0.8;
  pointer-events: none;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
}

.action-btn.cancel .btn-icon {
  opacity: 0.6;
}

.action-btn.primary .btn-icon {
  filter: brightness(0) invert(1);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    font-size: 26rpx;
  }

  .tab-nav {
    padding: var(--spacing-md) var(--spacing-sm);
    gap: var(--spacing-xs);
  }

  .tab-item {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .tab-text {
    font-size: 26rpx;
  }

  .card-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .card-title {
    font-size: 28rpx;
  }

  .card-content {
    padding: var(--spacing-lg);
  }

  .input-label {
    width: 140rpx;
  }

  .label-text {
    font-size: 26rpx;
  }

  .form-input {
    height: 80rpx;
    font-size: 26rpx;
  }

  .form-textarea {
    min-height: 140rpx;
    font-size: 26rpx;
  }

  .action-btn {
    height: 80rpx;
    font-size: 28rpx;
  }
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:nth-child(1) {
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.card:nth-child(2) {
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.card:nth-child(3) {
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.card:nth-child(4) {
  animation-delay: 0.4s;
  animation-fill-mode: both;
}