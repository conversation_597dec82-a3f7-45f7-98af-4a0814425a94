<!-- components/list-item/list-item.wxml -->
<view 
  class="list-item {{customClass}} {{clickable ? 'clickable' : ''}} {{border ? 'border' : ''}}"
  bindtap="onItemTap"
>
  <!-- 左侧图标 -->
  <view wx:if="{{icon}}" class="item-icon">
    <image class="item-icon-image" src="{{icon}}" mode="aspectFit"></image>
  </view>
  
  <!-- 内容区域 -->
  <view class="item-content">
    <view class="item-main">
      <text class="item-title">{{title}}</text>
      <text wx:if="{{subtitle}}" class="item-subtitle">{{subtitle}}</text>
    </view>
    
    <!-- 右侧区域 -->
    <view class="item-right">
      <!-- 徽章 -->
      <view 
        wx:if="{{badge}}" 
        class="item-badge"
        style="background-color: {{badgeColors[badgeType] || badgeColors.default}}; color: {{badgeType === 'default' ? '#666' : '#fff'}};"
      >
        <text>{{badge}}</text>
      </view>
      
      <!-- 额外文本 -->
      <text wx:if="{{extra}}" class="item-extra">{{extra}}</text>
      
      <!-- 右箭头 -->
      <image 
        wx:if="{{arrow}}" 
        class="item-arrow" 
        src="/images/icons/arrow_right.png" 
        mode="aspectFit"
      ></image>
    </view>
  </view>
</view>