/* pages/home/<USER>/

/* 首页容器 */
.container {
  min-height: 100vh;
  background: #f2f2f7;
  padding: 0;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 用户信息横幅 */
.user-info-banner {
  background: linear-gradient(135deg, #0066CC 0%, #00CC99 100%);
  border-radius: 32rpx;
  padding: 32rpx;
  color: #ffffff;
  margin: 32rpx 32rpx 0 32rpx;
  position: relative;
  overflow: hidden;
}

.user-info-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.2);
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 8rpx;
  display: block;
}

.farm-name {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
  display: block;
}

.weather-info {
  flex-shrink: 0;
  margin-left: 24rpx;
}

/* 公告栏 */
.announcement-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
}

.announcement-list {
  /* 公告列表容器 */
}

.announcement-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-content {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
}

.announcement-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.announcement-time {
  flex-shrink: 0;
}

.announcement-time text {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
}

/* 任务部分 */
.task-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.more-text {
  font-size: 28rpx;
  color: #666666;
}

/* 任务列表 */
.task-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.task-item:last-child {
  border-bottom: none;
}

/* 已完成任务样式 */
.task-item.completed {
  opacity: 0.6;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  margin: 8rpx 0;
}

.task-content {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: block;
}

/* 已完成任务标题划掉效果 */
.task-item.completed .task-title {
  text-decoration: line-through;
  color: #999999;
  font-weight: normal;
}

.task-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  display: block;
}

/* 已完成任务描述样式 */
.task-item.completed .task-desc {
  text-decoration: line-through;
  color: #cccccc;
}

/* 完成时间显示 */
.completed-time {
  font-size: 24rpx;
  color: #28a745;
  margin-top: 8rpx;
  display: block;
  font-weight: normal;
}

.task-time {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.task-time text {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
}

/* 完成标记图标 */
.completed-icon {
  font-size: 32rpx;
  color: #28a745;
  font-weight: bold;
  margin-top: 8rpx;
  background: #e8f5e8;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
}

/* 知识库部分 */
.knowledge-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
}

.knowledge-list {
  /* 知识库列表容器 */
}

.knowledge-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.knowledge-item:last-child {
  border-bottom: none;
}

.knowledge-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  display: block;
}

.knowledge-summary {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 12rpx;
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.knowledge-time {
  font-size: 24rpx;
  color: #999999;
  display: block;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}