<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅计划管理 - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #212529);
            color: white;
            height: 100vh;
            position: fixed;
            overflow-y: auto;
        }
        
        .main-content {
            margin-left: 250px;
        }
        
        .card {
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: none;
            margin-bottom: 24px;
            transition: transform 0.2s ease;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.7);
            transition: all 0.3s;
            border-radius: 8px;
            margin: 2px 0;
        }
        
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .plan-card {
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .plan-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }
        
        .plan-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .plan-header.premium {
            background: linear-gradient(135deg, #fd7e14, #e83e8c);
        }
        
        .plan-header.enterprise {
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
        }
        
        .plan-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .plan-price {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .plan-price-unit {
            font-size: 1rem;
            opacity: 0.8;
        }
        
        .plan-popular {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .plan-features {
            padding: 30px;
        }
        
        .feature-item {
            padding: 12px 0;
            border-bottom: 1px solid #f8f9fa;
            display: flex;
            align-items: center;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            color: var(--primary-color);
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        .feature-text {
            flex: 1;
        }
        
        .feature-limit {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .plan-stats {
            background: #f8f9fa;
            padding: 20px;
            margin-top: 20px;
            border-radius: 8px;
        }
        
        .stat-item {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .pricing-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        
        .pricing-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            padding: 15px;
        }
        
        .comparison-table td {
            padding: 15px;
            vertical-align: middle;
        }
        
        .check-icon {
            color: var(--primary-color);
            font-size: 1.2rem;
        }
        
        .cross-icon {
            color: #dc3545;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-0">
                <div class="p-4">
                    <h4 class="text-white">
                        <i class="bi bi-egg-fill me-2"></i>智慧养鹅
                    </h4>
                    <p class="text-muted small">SaaS管理平台</p>
                </div>
                
                <ul class="nav flex-column px-2">
                    <li class="nav-item">
                        <a class="nav-link" href="/saas-admin/dashboard">
                            <i class="bi bi-speedometer2 me-2"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/saas-admin/tenants">
                            <i class="bi bi-building me-2"></i>租户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/saas-admin/users">
                            <i class="bi bi-people me-2"></i>用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/saas-admin/plans">
                            <i class="bi bi-card-checklist me-2"></i>订阅计划
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/saas-admin/monitoring">
                            <i class="bi bi-activity me-2"></i>系统监控
                        </a>
                    </li>
                </ul>
                
                <div class="px-3 py-4 mt-auto">
                    <div class="d-flex align-items-center">
                        <div class="avatar me-3">A</div>
                        <div>
                            <div class="text-white">管理员</div>
                            <div class="text-muted small">超级管理员</div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="/saas-admin/logout" class="btn btn-outline-light w-100">
                            <i class="bi bi-box-arrow-right me-1"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 顶部导航 -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0">订阅计划管理</h5>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPlanModal">
                                <i class="bi bi-plus-circle me-1"></i>新增计划
                            </button>
                        </div>
                    </div>
                </nav>
                
                <!-- 计划概览 -->
                <div class="container-fluid mt-4">
                    <!-- 统计卡片 -->
                    <div class="row g-4 mb-4">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="stat-value">5</div>
                                    <div class="stat-label">可用计划</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="stat-value">125</div>
                                    <div class="stat-label">订阅用户</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="stat-value">¥32,580</div>
                                    <div class="stat-label">月度收入</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="stat-value">89%</div>
                                    <div class="stat-label">续费率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 计划卡片 -->
                    <div class="row g-4 mb-5">
                        <!-- 免费版 -->
                        <div class="col-lg-4 col-md-6">
                            <div class="card plan-card">
                                <div class="plan-header">
                                    <div class="plan-title">免费版</div>
                                    <div class="plan-price">
                                        ¥0
                                        <span class="plan-price-unit">/月</span>
                                    </div>
                                    <div>适合个人用户试用</div>
                                </div>
                                <div class="plan-features">
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>用户数量</div>
                                            <div class="feature-limit">最多 5 个用户</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>鹅群管理</div>
                                            <div class="feature-limit">最多 3 个鹅群</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>基础功能</div>
                                            <div class="feature-limit">健康、生产记录</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-x-circle cross-icon"></i>
                                        <div class="feature-text">
                                            <div>AI诊断</div>
                                            <div class="feature-limit">不支持</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-x-circle cross-icon"></i>
                                        <div class="feature-text">
                                            <div>商城模块</div>
                                            <div class="feature-limit">不支持</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="plan-stats">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-value">23</div>
                                                <div class="stat-label">当前用户</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-value">¥0</div>
                                                <div class="stat-label">月收入</div>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-outline-primary w-100 mt-3">
                                        <i class="bi bi-gear me-1"></i>管理计划
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 标准版 -->
                        <div class="col-lg-4 col-md-6">
                            <div class="card plan-card">
                                <div class="plan-header">
                                    <div class="plan-popular">热门</div>
                                    <div class="plan-title">标准版</div>
                                    <div class="plan-price">
                                        ¥199
                                        <span class="plan-price-unit">/月</span>
                                    </div>
                                    <div>适合中小型养鹅场</div>
                                </div>
                                <div class="plan-features">
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>用户数量</div>
                                            <div class="feature-limit">最多 50 个用户</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>鹅群管理</div>
                                            <div class="feature-limit">最多 20 个鹅群</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>完整功能</div>
                                            <div class="feature-limit">所有基础功能</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>AI诊断</div>
                                            <div class="feature-limit">100次/月</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-x-circle cross-icon"></i>
                                        <div class="feature-text">
                                            <div>商城模块</div>
                                            <div class="feature-limit">不支持</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="plan-stats">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-value">56</div>
                                                <div class="stat-label">当前用户</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-value">¥11,144</div>
                                                <div class="stat-label">月收入</div>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-primary w-100 mt-3">
                                        <i class="bi bi-gear me-1"></i>管理计划
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 高级版 -->
                        <div class="col-lg-4 col-md-6">
                            <div class="card plan-card">
                                <div class="plan-header premium">
                                    <div class="plan-title">高级版</div>
                                    <div class="plan-price">
                                        ¥399
                                        <span class="plan-price-unit">/月</span>
                                    </div>
                                    <div>适合大型养鹅场</div>
                                </div>
                                <div class="plan-features">
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>用户数量</div>
                                            <div class="feature-limit">最多 200 个用户</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>鹅群管理</div>
                                            <div class="feature-limit">最多 100 个鹅群</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>完整功能</div>
                                            <div class="feature-limit">所有功能模块</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>AI诊断</div>
                                            <div class="feature-limit">无限次使用</div>
                                        </div>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle feature-icon"></i>
                                        <div class="feature-text">
                                            <div>商城模块</div>
                                            <div class="feature-limit">完整商城功能</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="plan-stats">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-value">41</div>
                                                <div class="stat-label">当前用户</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-value">¥16,359</div>
                                                <div class="stat-label">月收入</div>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-warning w-100 mt-3">
                                        <i class="bi bi-gear me-1"></i>管理计划
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 功能对比表 -->
                    <div class="pricing-table">
                        <div class="pricing-header">
                            <h5 class="mb-0">功能对比表</h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table comparison-table mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 40%;">功能特性</th>
                                        <th class="text-center">免费版</th>
                                        <th class="text-center">标准版</th>
                                        <th class="text-center">高级版</th>
                                        <th class="text-center">企业版</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>基础功能</strong></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                    </tr>
                                    <tr>
                                        <td>用户数量限制</td>
                                        <td class="text-center">5</td>
                                        <td class="text-center">50</td>
                                        <td class="text-center">200</td>
                                        <td class="text-center">无限制</td>
                                    </tr>
                                    <tr>
                                        <td>鹅群数量限制</td>
                                        <td class="text-center">3</td>
                                        <td class="text-center">20</td>
                                        <td class="text-center">100</td>
                                        <td class="text-center">无限制</td>
                                    </tr>
                                    <tr>
                                        <td>健康管理模块</td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                    </tr>
                                    <tr>
                                        <td>生产管理模块</td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                    </tr>
                                    <tr>
                                        <td>AI诊断功能</td>
                                        <td class="text-center"><i class="bi bi-x-circle cross-icon"></i></td>
                                        <td class="text-center">100次/月</td>
                                        <td class="text-center">无限制</td>
                                        <td class="text-center">无限制</td>
                                    </tr>
                                    <tr>
                                        <td>商城模块</td>
                                        <td class="text-center"><i class="bi bi-x-circle cross-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-x-circle cross-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                    </tr>
                                    <tr>
                                        <td>数据导出</td>
                                        <td class="text-center"><i class="bi bi-x-circle cross-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                        <td class="text-center"><i class="bi bi-check-circle check-icon"></i></td>
                                    </tr>
                                    <tr>
                                        <td>API访问</td>
                                        <td class="text-center"><i class="bi bi-x-circle cross-icon"></i></td>
                                        <td class="text-center">基础API</td>
                                        <td class="text-center">完整API</td>
                                        <td class="text-center">完整API + 私有部署</td>
                                    </tr>
                                    <tr>
                                        <td>技术支持</td>
                                        <td class="text-center">社区支持</td>
                                        <td class="text-center">邮件支持</td>
                                        <td class="text-center">电话支持</td>
                                        <td class="text-center">专属客服</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增计划模态框 -->
    <div class="modal fade" id="addPlanModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新增订阅计划</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addPlanForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="planName" class="form-label">计划名称 *</label>
                                    <input type="text" class="form-control" id="planName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="planCode" class="form-label">计划代码 *</label>
                                    <input type="text" class="form-control" id="planCode" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="monthlyPrice" class="form-label">月费价格 (¥)</label>
                                    <input type="number" class="form-control" id="monthlyPrice" min="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="yearlyPrice" class="form-label">年费价格 (¥)</label>
                                    <input type="number" class="form-control" id="yearlyPrice" min="0" step="0.01">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxUsers" class="form-label">最大用户数</label>
                                    <input type="number" class="form-control" id="maxUsers" min="1">
                                    <div class="form-text">设置为 -1 表示无限制</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxFlocks" class="form-label">最大鹅群数</label>
                                    <input type="number" class="form-control" id="maxFlocks" min="1">
                                    <div class="form-text">设置为 -1 表示无限制</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">计划描述</label>
                            <textarea class="form-control" id="description" rows="3"></textarea>
                        </div>
                        
                        <h6 class="mb-3">功能权限</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="healthModule" checked>
                                    <label class="form-check-label" for="healthModule">健康管理模块</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="productionModule" checked>
                                    <label class="form-check-label" for="productionModule">生产管理模块</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="aiDiagnosis">
                                    <label class="form-check-label" for="aiDiagnosis">AI诊断功能</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="shopModule">
                                    <label class="form-check-label" for="shopModule">商城模块</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="dataExport">
                                    <label class="form-check-label" for="dataExport">数据导出</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="apiAccess">
                                    <label class="form-check-label" for="apiAccess">API访问</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="savePlan()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function savePlan() {
            const form = document.getElementById('addPlanForm');
            const formData = new FormData(form);
            
            // 验证表单
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            // 收集功能权限
            const features = {
                healthModule: document.getElementById('healthModule').checked,
                productionModule: document.getElementById('productionModule').checked,
                aiDiagnosis: document.getElementById('aiDiagnosis').checked,
                shopModule: document.getElementById('shopModule').checked,
                dataExport: document.getElementById('dataExport').checked,
                apiAccess: document.getElementById('apiAccess').checked
            };
            
            const planData = {
                name: document.getElementById('planName').value,
                code: document.getElementById('planCode').value,
                monthlyPrice: document.getElementById('monthlyPrice').value,
                yearlyPrice: document.getElementById('yearlyPrice').value,
                maxUsers: document.getElementById('maxUsers').value,
                maxFlocks: document.getElementById('maxFlocks').value,
                description: document.getElementById('description').value,
                features: features
            };
            
            console.log('保存计划:', planData);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addPlanModal'));
            modal.hide();
            
            // 刷新页面
            location.reload();
        }
    </script>
</body>
</html>