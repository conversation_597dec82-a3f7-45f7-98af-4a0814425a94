### V2 接口迁移映射清单（小程序端）

说明：以下基于 `constants/api.constants.js`，标注页面/模块使用的端点与版本，便于逐步下线 V1。

- 健康管理（Health）
  - 列表/详情/CRUD：V2 `ENDPOINTS.HEALTH.RECORDS | RECORD_DETAIL | CREATE_RECORD | UPDATE_RECORD | DELETE_RECORD`
  - 统计：V2 `ENDPOINTS.HEALTH.STATS`
  - 知识库/AI诊断/报告：V1 兼容 `ENDPOINTS.HEALTH.KNOWLEDGE | AI_DIAGNOSIS | REPORT`（待迁移）

- 生产管理（Production）
  - 记录列表/CRUD/趋势/统计：V2 `ENDPOINTS.PRODUCTION.*`
  - 环境/财务/报销/采购/AI子模块：V1 兼容 `ENDPOINTS.PRODUCTION.ENVIRONMENT | FINANCE | REIMBURSEMENT_REQUESTS | PURCHASE_REQUESTS | AI_*`（待迁移）

- 库存（Inventory）
  - 全量使用 V2 `ENDPOINTS.INVENTORY.*`

- OA（办公自动化）
  - 报销：V1 `ENDPOINTS.OA.REIMBURSEMENT.*`（待迁移）
  - 采购：V1 `ENDPOINTS.OA.PURCHASE.REQUESTS | REQUEST_DETAIL | CANCEL`（待迁移）
  - 审批：V1 `ENDPOINTS.OA.APPROVALS.PENDING | HISTORY | APPROVE | REJECT`（待迁移）
  - 财务：V1 `ENDPOINTS.OA.FINANCE.OVERVIEW | REPORTS | EXPORT`（待迁移）
  - 权限：V1 `ENDPOINTS.OA.PERMISSIONS.USERS | ROLES`（待迁移）

- 认证（Auth）
  - 当前为 V1 `ENDPOINTS.AUTH.*`（可计划切换至 V2 认证中心）

- 首页/商城/个人中心/系统设置
  - 现为 V1 `ENDPOINTS.HOME.* | SHOP.* | PROFILE.* | SYSTEM.*`（按业务优先级迁移）

建议迁移顺序：Inventory → Health/Production → OA（审批、报销、采购、财务）→ 认证 → 其他（首页/商城/系统）。

备注：迁移过程中前端始终通过统一 `utils/request.js` 调用，保持响应结构兼容与错误处理一致；端点切换只需在常量层调整。


