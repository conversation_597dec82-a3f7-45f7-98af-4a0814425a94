const express = require("express");
const router = express.Router();

/**
 * 健康检查路由
 */

// 基础健康检查
router.get("/", (req, res) => {
  res.json({
    status: "ok",
    message: "智慧养鹅SAAS平台运行正常",
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || "2.9.2",
    environment: process.env.NODE_ENV || "development",
  });
});

// 详细健康检查
router.get("/detailed", async (req, res) => {
  try {
    const health = {
      status: "ok",
      timestamp: new Date().toISOString(),
      services: {
        database: "ok",
        redis: "not_configured",
        cache: "ok",
      },
      memory: process.memoryUsage(),
      uptime: process.uptime(),
    };

    res.json(health);
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "服务检查失败",
      error: error.message,
    });
  }
});

module.exports = router;
