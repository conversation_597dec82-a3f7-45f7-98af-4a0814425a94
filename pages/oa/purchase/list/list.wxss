/* pages/oa/purchase/list/list.wxss */
.container {
  min-height: 100vh;
  background: #f5f7fa;
}

/* ==================== 搜索栏 ==================== */
.search-header {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 0 20rpx;
}

.search-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.filter-toggle {
  background: #0066CC;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.filter-toggle.active {
  background: #0052A3;
}

/* ==================== 筛选面板 ==================== */
.filter-panel {
  background: #ffffff;
  margin: 0 20rpx 16rpx 20rpx;
  border-radius: 16rpx;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.filter-panel.show {
  padding: 24rpx;
  max-height: 300rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-item:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}

.filter-selector {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.arrow {
  font-size: 20rpx;
  color: #999;
}

/* ==================== 状态标签 ==================== */
.status-bar {
  background: #ffffff;
  padding: 16rpx 0;
  margin-bottom: 16rpx;
}

.status-scroll {
  white-space: nowrap;
}

.status-tabs {
  display: flex;
  padding: 0 20rpx;
  gap: 12rpx;
}

.status-tab {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.status-tab.active {
  background: #0066CC;
  color: white;
  border-color: #0066CC;
}

.count {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10rpx;
  padding: 2rpx 8rpx;
  font-size: 22rpx;
  font-weight: 600;
  min-width: 32rpx;
  text-align: center;
}

.status-tab.active .count {
  background: rgba(255, 255, 255, 0.2);
}

/* ==================== 列表容器 ==================== */
.list-container {
  padding: 0 20rpx 100rpx 20rpx;
}

/* ==================== 采购卡片 ==================== */
.purchase-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
}

.purchase-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 卡片头部 */
.card-header {
  padding: 24rpx 24rpx 16rpx 24rpx;
  border-bottom: 1rpx solid #f0f2f5;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.title {
  flex: 1;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-right: 16rpx;
}

.status-tag {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  color: white;
  font-size: 22rpx;
  font-weight: 600;
  white-space: nowrap;
}

.meta-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  font-size: 24rpx;
  color: #0066CC;
  font-weight: 600;
}

.time {
  font-size: 24rpx;
  color: #999;
}

/* 卡片内容 */
.card-content {
  padding: 16rpx 24rpx 24rpx 24rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  width: 80rpx;
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
}

.value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.category {
  flex: 1;
  font-size: 26rpx;
  color: #0066CC;
  background: #f0f7ff;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.items {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.more {
  color: #0066CC;
  font-weight: 600;
}

.amount-row {
  padding-top: 12rpx;
  border-top: 1rpx solid #f0f2f5;
  margin-top: 8rpx;
}

.amount {
  flex: 1;
  font-size: 28rpx;
  font-weight: 700;
  color: #0066CC;
  text-align: right;
}

/* ==================== 空状态 ==================== */
.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.3;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.create-btn {
  background: linear-gradient(135deg, #0066CC 0%, #0052A3 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(0, 102, 204, 0.3);
  transition: all 0.3s ease;
}

.create-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.4);
}

/* ==================== 加载状态 ==================== */
.loading-state {
  padding: 80rpx 0;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f2f5;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  padding: 40rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #ccc;
}

/* ==================== 悬浮按钮 ==================== */
.fab-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}

.fab-btn {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0066CC 0%, #0052A3 100%);
  color: white;
  border: none;
  font-size: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 102, 204, 0.4);
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.5);
}

/* ==================== 动画 ==================== */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* ==================== 页面进入动画 ==================== */
.purchase-card {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 480px) {
  .search-header {
    padding: 16rpx;
  }

  .filter-panel.show {
    padding: 20rpx;
  }

  .purchase-card {
    margin-bottom: 16rpx;
  }

  .card-header,
  .card-content {
    padding: 20rpx;
  }
}