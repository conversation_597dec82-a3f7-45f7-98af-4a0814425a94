// backend/models/product.model.js
// 商城商品数据模型

const { DataTypes } = require("sequelize");
const { sequelize } = require("../config/database");

const Product = sequelize.define(
  "Product",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "user_id",
      comment: "用户ID",
    },
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "flock_id",
      comment: "鹅群ID",
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: "商品名称",
    },
    description: {
      type: DataTypes.TEXT,
      comment: "商品描述",
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: "商品价格",
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: "商品分类",
    },
    image: {
      type: DataTypes.STRING(500),
      comment: "商品图片URL",
    },
    stock: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "库存数量",
    },
    status: {
      type: DataTypes.ENUM("active", "inactive", "out_of_stock"),
      defaultValue: "active",
      comment: "商品状态",
    },
    tags: {
      type: DataTypes.JSON,
      comment: "商品标签",
    },
    specifications: {
      type: DataTypes.JSON,
      comment: "商品规格参数",
    },
    salesCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "sales_count",
      comment: "销售数量",
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "created_by",
      comment: "创建者ID",
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      field: "updated_by",
      comment: "更新者ID",
    },
  },
  {
    tableName: "products",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true, // 软删除
    indexes: [
      {
        fields: ["category"],
      },
      {
        fields: ["status"],
      },
      {
        fields: ["name"],
      },
      {
        fields: ["created_by"],
      },
    ],
  },
);

module.exports = Product;
