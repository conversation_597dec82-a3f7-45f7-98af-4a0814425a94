/* pages/production/environment/environment.wxss */
@import "/styles/design-system.wxss";

.container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: 120rpx;
}

/* 区块 */
.section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.refresh-button image {
  width: 40rpx;
  height: 40rpx;
}

/* 当前环境数据 */
.current-data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.data-item:active {
  background-color: #f8f9fa;
  border-color: #0066cc;
  transform: scale(0.98);
}

.item-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.item-content {
  flex: 1;
}

.item-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4rpx;
}

.item-value.warning {
  color: #ff9900;
}

.item-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.item-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.item-status.normal {
  background-color: #f6ffed;
  color: #52c41a;
}

.item-status.good {
  background-color: #e6f7ff;
  color: #1890ff;
}

.item-status.suitable {
  background-color: #fff7e6;
  color: #fa8c16;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 12rpx;
  opacity: 0.6;
}



.update-time {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
}

/* 时间范围选择 */
.time-range-selector {
  display: flex;
  gap: 20rpx;
}

.range-item {
  font-size: 24rpx;
  color: #666666;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
}

.range-item.active {
  background-color: #0066cc;
  color: #ffffff;
}

/* 图表容器 */
.chart-container {
  padding: 20rpx 0;
}

.chart-placeholder {
  text-align: center;
  padding: 50rpx 0;
}

.chart-placeholder image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.chart-placeholder text {
  font-size: 28rpx;
  color: #999999;
}

/* 趋势图表 */
.trend-chart-container {
  padding: 20rpx 0;
}

.trend-chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  text-align: center;
}

.trend-chart {
  height: 300rpx;
  position: relative;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.mock-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-line {
  position: absolute;
  bottom: 50%;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: #0066cc;
  opacity: 0.3;
}

.chart-data {
  position: relative;
  width: 100%;
  height: 100%;
}

.data-point {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.point-dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #0066cc;
  border-radius: 3rpx;
  margin-bottom: 8rpx;
}

.point-value {
  font-size: 20rpx;
  color: #666666;
  white-space: nowrap;
}

/* 报警信息 */
.more {
  font-size: 26rpx;
  color: #0066cc;
}

.alerts-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.alert-item:last-child {
  margin-bottom: 0;
}

.alert-item.warning {
  background-color: #fff3e6;
  border-left: 8rpx solid #ff9900;
}

.alert-item.info {
  background-color: #e6f4ff;
  border-left: 8rpx solid #0066cc;
}

.alert-content {
  flex: 1;
}

.alert-message {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.alert-time {
  font-size: 24rpx;
  color: #999999;
}

.alert-status {
  font-size: 24rpx;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  margin-left: 20rpx;
}

.alert-status.pending {
  background-color: #fff3e6;
  color: #ff9900;
}

.alert-status.resolved {
  background-color: #e6f9f8;
  color: #00cc99;
}

.no-alerts {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999999;
}

/* 图表容器样式 */
.chart-container {
  position: relative;
  margin-top: 20rpx;
}

.close-chart-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.2s ease;
}

.close-chart-btn:hover {
  background-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}

.close-chart-btn:active {
  background-color: rgba(0, 0, 0, 0.3);
  transform: scale(0.95);
}

.close-icon {
  font-size: 32rpx;
  color: #666666;
  font-weight: bold;
  line-height: 1;
}