// pages/oa/oa.js

/**
 * OA办公自动化系统主页面
 * 提供财务、采购、报销、审批、工作流等模块的统一入口
 */

const { oaPermissionManager, PERMISSIONS, PermissionMixin } = require('../../utils/oa-permissions');
const request = require('../../utils/request.js');
const { API } = require('../../constants/index.js');
let logger; try { logger = require('../../utils/logger.js'); } catch(_) { logger = console; }

Page({
  // 混入权限检查功能
  ...PermissionMixin,
  data: {
    loading: false,
    
    // OA模块统计
    oaStats: {
      pendingApprovals: 8,      // 待审批数量
      todayTasks: 12,          // 今日任务
      thisMonthExpense: 25000, // 本月支出
      unreadNotices: 3         // 未读通知
    },
    
    // 主要功能模块
    mainModules: [
      {
        id: 'finance',
        title: '财务管理',
        description: '财务数据查看与管理',
        icon: '/images/icons/grid.png',
        url: '/pages/oa/finance/finance',
        color: '#0066CC',
        badge: ''
      },
      {
        id: 'purchase',
        title: '采购管理',
        description: '采购申请与管理',
        icon: '/images/icons/cart.png',
        url: '/pages/oa/purchase/purchase',
        color: '#00A86B',
        badge: '5'
      },
      {
        id: 'reimbursement',
        title: '报销管理',
        description: '费用报销申请与审核',
        icon: '/images/icons/package.png',
        url: '/pages/oa/reimbursement/reimbursement',
        color: '#FF6B35',
        badge: '2'
      },
      {
        id: 'approval',
        title: '审批管理',
        description: '审批流程与历史',
        icon: '/images/icons/check-circle.png',
        url: '/pages/oa/approval/approval',
        color: '#9B59B6',
        badge: '8'
      }
    ],
    
    // 快捷操作
    quickActions: [
      {
        id: 'new-purchase',
        title: '新建采购申请',
        icon: '/images/icons/plus.png',
        url: '/pages/oa/purchase/apply/apply',
        color: '#00A86B'
      },
      {
        id: 'new-reimbursement',
        title: '新建报销申请',
        icon: '/images/icons/plus.png',
        url: '/pages/oa/reimbursement/apply/apply',
        color: '#FF6B35'
      },
      {
        id: 'pending-approvals',
        title: '待审批事项',
        icon: '/images/icons/clock.png',
        url: '/pages/oa/approval/pending/pending',
        color: '#9B59B6'
      },

      {
        id: 'notifications',
        title: '系统通知',
        icon: '/images/icons/star.png',
        url: '/pages/oa/notification/notification',
        color: '#FF9500'
      },
      {
        id: 'user-permissions',
        title: '权限管理',
        icon: '/images/icons/check-circle.png',
        url: '/pages/oa/permission/users/users',
        color: '#8E44AD'
      }
    ],
    
    // 最近动态
    recentActivities: [
      {
        id: 'activity_001',
        type: 'approval',
        title: '采购申请已审批',
        description: '办公用品采购申请已通过审批',
        time: '10分钟前',
        status: 'approved'
      },
      {
        id: 'activity_002',
        type: 'reimbursement',
        title: '报销申请待审核',
        description: '差旅费报销申请等待财务审核',
        time: '1小时前',
        status: 'pending'
      },
      {
        id: 'activity_003',
        type: 'purchase',
        title: '新采购申请',
        description: '饲料采购申请已提交',
        time: '2小时前',
        status: 'submitted'
      }
    ]
  },

  onLoad: function (options) {
    this.loadData();
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查基础OA访问权限
    if (!this.hasPermission(PERMISSIONS.VIEW_OA)) {
      wx.showModal({
        title: '权限不足',
        content: '您没有访问OA系统的权限，请联系管理员',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    // 根据权限过滤快捷操作
    this.filterActionsByPermissions();
  },

  /**
   * 根据权限过滤快捷操作
   */
  filterActionsByPermissions() {
    const { quickActions } = this.data;
    const filteredActions = quickActions.filter(action => {
      switch (action.id) {
        case 'new-purchase':
          return this.hasPermission(PERMISSIONS.CREATE_PURCHASE);
        case 'new-reimbursement':
          return this.hasPermission(PERMISSIONS.CREATE_REIMBURSEMENT);
        case 'pending-approvals':
          return this.hasPermission(PERMISSIONS.APPROVE_PURCHASE) || 
                 this.hasPermission(PERMISSIONS.APPROVE_REIMBURSEMENT) ||
                 this.hasPermission(PERMISSIONS.APPROVE_LEAVE);

        case 'notifications':
          return true; // 所有用户都可以查看通知
        case 'user-permissions':
          return this.hasPermission(PERMISSIONS.MANAGE_USERS);
        default:
          return true;
      }
    });

    this.setData({ quickActions: filteredActions });
  },

  onShow: function () {
    this.refreshData();
  },

  onPullDownRefresh: function () {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },



  // 加载数据
  loadData: function() {
    this.setData({ loading: true });
    
    Promise.all([
      this.loadOAStats(),
      this.loadRecentActivities()
    ]).then(() => {
      this.setData({ loading: false });
    }).catch(error => {
      try { logger.error && logger.error('加载OA数据失败', error); } catch(_) {}
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  // 刷新数据
  refreshData: function() {
    this.loadData();
  },

  // 加载OA统计数据
  loadOAStats: function() {
    return request.get(API.ENDPOINTS.OA.DASHBOARD.STATISTICS)
      .then(res => {
        const data = res && res.success !== false ? (res.data || res) : null;
        if (!data) throw new Error(res?.message || '获取统计数据失败');
        this.setData({ oaStats: data });
        return data;
      });
  },

  // 加载最近活动
  loadRecentActivities: function() {
    return request.get(API.ENDPOINTS.OA.DASHBOARD.RECENT_ACTIVITIES)
      .then(res => {
        const data = res && res.success !== false ? (res.data || res) : [];
        this.setData({ recentActivities: Array.isArray(data) ? data : (data.list || []) });
        return data;
      });
  },

  // 点击模块
  onModuleTap: function(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });

      wx.navigateTo({
        url: url,
        fail: (error) => {
          try { logger.warn && logger.warn('模块导航失败', { error, url }); } catch(_) {}
          wx.showToast({
            title: '功能即将上线',
            icon: 'none'
          });
        }
      });
    }
  },

  // 点击快捷操作
  onQuickActionTap: function(e) {
    const { url, id } = e.currentTarget.dataset;
    
    // 触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 根据操作类型进行不同处理
    if (id === 'notifications') {
      // 清除通知徽章
      this.clearNotificationBadge();
    }

    if (url) {
      wx.navigateTo({
        url: url,
        fail: (error) => {
          try { logger.warn && logger.warn('快捷操作导航失败', { error, url }); } catch(_) {}
          wx.showToast({
            title: '功能即将上线',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 清除通知徽章
   */
  clearNotificationBadge() {
    const oaStats = { ...this.data.oaStats };
    oaStats.unreadNotices = 0;
    this.setData({ oaStats });
  },

  // 点击统计卡片
  onStatCardTap: function(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';
    
    switch(type) {
      case 'approvals':
        url = '/pages/oa/approval/pending/pending';
        break;
      case 'tasks':
        // 工作流程已移除，跳转到待办事项
        url = '/pages/oa/approval/pending/pending';
        break;
      case 'expense':
        url = '/pages/oa/finance/overview/overview';
        break;
      case 'notices':
        url = '/pages/announcement/announcement-list/announcement-list';
        break;
    }
    
    if (url) {
      wx.navigateTo({ url });
    }
  },

  // 点击活动项
  onActivityTap: function(e) {
    const activity = e.currentTarget.dataset.activity;
    let url = '';
    
    switch(activity.type) {
      case 'approval':
        url = `/pages/oa/approval/approval`;
        break;
      case 'reimbursement':
        url = `/pages/oa/reimbursement/reimbursement`;
        break;
      case 'purchase':
        url = `/pages/oa/purchase/purchase`;
        break;
    }
    
    if (url) {
      wx.navigateTo({ url });
    }
  },

  // 查看全部活动
  onViewAllActivities: function() {
    wx.navigateTo({
      url: '/pages/oa/activities/list'
    });
  }
});