<!-- 优化后的生产记录项组件 -->
<view class="production-record-item" bindtap="onItemTap">
  <!-- 记录头部 - 优化布局 -->
  <view class="record-header">
    <view class="header-left">
      <!-- 记录类型徽章 -->
      <view class="type-badge type-{{record.type}}">
        <text class="type-icon">
          {{record.type === 'entry' ? '📥' : record.type === 'weight' ? '⚖️' : '📤'}}
        </text>
        <text class="type-text">
          {{record.type === 'entry' ? '入栏' : record.type === 'weight' ? '称重' : '出栏'}}
        </text>
      </view>

      <!-- 批次信息 -->
      <view class="batch-info">
        <text class="batch-prefix">批次</text>
        <text class="batch-number">{{record.batch}}</text>
      </view>
    </view>

    <view class="header-right">
      <view class="header-right-content">
        <!-- 状态指示器 - 移至日期上方 -->
        <view wx:if="{{record.status}}" class="status-indicator status-{{record.status}}">
          <view class="status-dot"></view>
          <text class="status-text">
            {{record.status === 'pending' ? '待审核' :
              record.status === 'approved' ? '已审核' : '已完成'}}
          </text>
        </view>

        <!-- 日期信息 -->
        <view class="date-info">
          <text class="date-text">{{formatDate(record.date)}}</text>
          <text class="date-weekday">{{getWeekday(record.date)}}</text>
        </view>
      </view>

      <!-- 箭头指示器 -->
      <view class="arrow-indicator">
        <text class="arrow">›</text>
      </view>
    </view>
  </view>

  <!-- 记录内容 -->
  <view class="record-content">
    <!-- 入栏记录内容 -->
    <view wx:if="{{record.type === 'entry'}}" class="entry-content">
      <view class="content-row">
        <view class="content-item">
          <text class="item-label">数量</text>
          <text class="item-value">{{record.details.count}}只</text>
        </view>
        <view class="content-item">
          <text class="item-label">来源</text>
          <text class="item-value">{{record.details.source}}</text>
        </view>
      </view>
      <view class="content-row">
        <view class="content-item">
          <text class="item-label">成本</text>
          <text class="item-value cost">¥{{record.details.cost}}</text>
        </view>
        <view wx:if="{{record.financeLinked}}" class="finance-badge">
          <text class="finance-icon">🔗</text>
          <text class="finance-text">已关联财务</text>
        </view>
      </view>
    </view>

    <!-- 出栏记录内容 -->
    <view wx:elif="{{record.type === 'sale'}}" class="sale-content">
      <view class="content-row">
        <view class="content-item">
          <text class="item-label">数量</text>
          <text class="item-value">{{record.details.count}}只</text>
        </view>
        <view class="content-item">
          <text class="item-label">重量</text>
          <text class="item-value">{{record.details.weight}}kg</text>
        </view>
      </view>
      <view class="content-row">
        <view class="content-item">
          <text class="item-label">单价</text>
          <text class="item-value">¥{{record.details.price}}</text>
        </view>
        <view class="content-item">
          <text class="item-label">总收入</text>
          <text class="item-value income">¥{{record.details.totalIncome}}</text>
        </view>
      </view>
      <view wx:if="{{record.financeLinked}}" class="finance-badge">
        <text class="finance-icon">🔗</text>
        <text class="finance-text">已关联财务</text>
      </view>
    </view>

    <!-- 称重记录内容 -->
    <view wx:elif="{{record.type === 'weight'}}" class="weight-content">
      <view class="content-row">
        <view class="content-item">
          <text class="item-label">数量</text>
          <text class="item-value">{{record.details.count}}只</text>
        </view>
        <view class="content-item">
          <text class="item-label">平均体重</text>
          <text class="item-value">{{record.details.weight}}kg</text>
        </view>
      </view>
    </view>
  </view>


</view>
