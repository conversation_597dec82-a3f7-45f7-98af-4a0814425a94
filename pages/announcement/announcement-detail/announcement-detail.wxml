<!-- pages/announcement/announcement-detail/announcement-detail.wxml -->
<view class="detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 公告详情 -->
  <view wx:else class="announcement-detail">
    <!-- 公告头部 -->
    <view class="detail-header">
      <view class="title-section">
        <text class="announcement-title">{{announcement.title}}</text>
        <view class="announcement-meta">
          <text class="publish-time">{{announcement.publishTime}}</text>
          <view class="important-badge" wx:if="{{announcement.isImportant}}">
            <text>重要</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 公告内容 -->
    <view class="detail-content">
      <rich-text nodes="{{announcement.content}}"></rich-text>
    </view>

    <!-- 附件列表 -->
    <view wx:if="{{announcement.attachments && announcement.attachments.length > 0}}" class="attachments">
      <view class="section-title">
        <text>相关附件</text>
      </view>
      <view class="attachment-list">
        <block wx:for="{{announcement.attachments}}" wx:key="id">
          <view class="attachment-item" bindtap="onDownloadAttachment" data-url="{{item.url}}" data-name="{{item.name}}">
            <image class="attachment-icon" src="/images/icon_attachment.png" mode="aspectFit"></image>
            <text class="attachment-name">{{item.name}}</text>
            <image class="download-icon" src="/images/icon_download.png" mode="aspectFit"></image>
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:if="{{error}}" class="error-state">
    <image class="error-icon" src="/images/icon_error.png" mode="aspectFit"></image>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="loadAnnouncementDetail">重试</button>
  </view>
</view>
