// 智慧养鹅小程序 - 性能配置管理工具 V1.0
// 提供智能的性能优化配置和策略管理

/**
 * 性能配置管理器
 */
class PerformanceConfigManager {
  constructor() {
    this.config = {
      // 性能模式：high | balanced | battery
      performanceMode: 'balanced',
      
      // 设备信息
      deviceInfo: null,
      
      // 性能策略
      strategies: {
        animation: 'auto',      // auto | enhanced | reduced | disabled
        imageQuality: 'auto',   // auto | high | medium | low
        caching: 'auto',        // auto | aggressive | normal | minimal
        networking: 'auto',     // auto | optimized | standard
        rendering: 'auto'       // auto | optimized | standard
      },
      
      // 阈值配置
      thresholds: {
        lowEndDevice: 2000,     // 低端设备判断阈值(内存MB)
        slowNetwork: 500,       // 慢网络判断阈值(ms)
        batteryLow: 20,         // 低电量阈值(%)
        memoryPressure: 80      // 内存压力阈值(%)
      }
    };
    
    this.callbacks = [];
    this.init();
  }

  /**
   * 初始化配置管理器
   */
  async init() {
    // 获取设备信息
    await this.detectDeviceCapabilities();
    
    // 检测网络状态
    await this.detectNetworkConditions();
    
    // 加载用户偏好
    this.loadUserPreferences();
    
    // 应用性能策略
    this.applyPerformanceStrategies();
    
    // 监听系统变化
    this.setupSystemMonitoring();
  }

  /**
   * 检测设备性能能力
   */
  async detectDeviceCapabilities() {
    try {
      const { getCompleteSystemInfo } = require('./system-info-helper');
      const systemInfo = getCompleteSystemInfo();
      
      this.config.deviceInfo = {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        pixelRatio: systemInfo.pixelRatio,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        windowWidth: systemInfo.windowWidth,
        windowHeight: systemInfo.windowHeight,
        brand: systemInfo.brand
      };

      // 设备性能等级评估
      this.config.deviceInfo.performanceLevel = this.evaluateDevicePerformance(systemInfo);
      
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[PerformanceConfig] 设备信息', this.config.deviceInfo); } catch(_) {}
      
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[PerformanceConfig] 设备信息获取失败', error); } catch(_) {}
      // 使用默认配置
      this.config.deviceInfo = {
        performanceLevel: 'medium'
      };
    }
  }

  /**
   * 评估设备性能等级
   */
  evaluateDevicePerformance(systemInfo) {
    let score = 0;
    
    // 基于品牌和型号的性能评估
    const highEndBrands = ['iPhone', 'iPad', 'Huawei', 'Xiaomi', 'OPPO', 'vivo'];
    if (highEndBrands.some(brand => systemInfo.brand?.includes(brand))) {
      score += 30;
    }

    // 基于像素密度的评估
    if (systemInfo.pixelRatio >= 3) {
      score += 20;
    } else if (systemInfo.pixelRatio >= 2) {
      score += 10;
    }

    // 基于屏幕尺寸的评估
    const screenArea = systemInfo.screenWidth * systemInfo.screenHeight;
    if (screenArea > 2000000) { // 大屏设备
      score += 20;
    } else if (screenArea > 1000000) {
      score += 10;
    }

    // 基于系统版本的评估
    const systemVersion = parseFloat(systemInfo.system.match(/[\d.]+/)?.[0] || '0');
    if (systemInfo.platform === 'ios' && systemVersion >= 14) {
      score += 20;
    } else if (systemInfo.platform === 'android' && systemVersion >= 10) {
      score += 15;
    }

    // 性能等级判断
    if (score >= 70) {
      return 'high';
    } else if (score >= 40) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * 检测网络条件
   */
  async detectNetworkConditions() {
    try {
      const networkType = await this.getNetworkType();
      
      this.config.networkInfo = {
        type: networkType,
        quality: this.evaluateNetworkQuality(networkType)
      };

      try { const logger = require('./logger.js'); logger.debug && logger.debug('[PerformanceConfig] 网络信息', this.config.networkInfo); } catch(_) {}
      
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[PerformanceConfig] 网络信息获取失败', error); } catch(_) {}
      this.config.networkInfo = {
        type: 'unknown',
        quality: 'medium'
      };
    }
  }

  /**
   * 获取网络类型
   */
  getNetworkType() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve(res.networkType);
        },
        fail: () => {
          resolve('unknown');
        }
      });
    });
  }

  /**
   * 评估网络质量
   */
  evaluateNetworkQuality(networkType) {
    const qualityMap = {
      'wifi': 'high',
      '5g': 'high',
      '4g': 'medium',
      '3g': 'low',
      '2g': 'low',
      'none': 'none',
      'unknown': 'medium'
    };
    
    return qualityMap[networkType] || 'medium';
  }

  /**
   * 加载用户偏好设置
   */
  loadUserPreferences() {
    try {
      const savedPreferences = wx.getStorageSync('performance_preferences');
      if (savedPreferences) {
        this.config = { ...this.config, ...savedPreferences };
      }

      // 检查系统偏好设置
      this.detectSystemPreferences();
      
    } catch (error) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[PerformanceConfig] 用户偏好加载失败', error); } catch(_) {}
    }
  }

  /**
   * 检测系统偏好设置
   */
  detectSystemPreferences() {
    // 检测减少动效偏好
    try {
      // 小程序中无法直接检测 prefers-reduced-motion
      // 可以通过用户设置来模拟
      const reduceMotion = wx.getStorageSync('reduce_motion_preference');
      if (reduceMotion) {
        this.config.strategies.animation = 'reduced';
      }
    } catch (error) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[PerformanceConfig] 系统偏好检测失败', error); } catch(_) {}
    }
  }

  /**
   * 应用性能策略
   */
  applyPerformanceStrategies() {
    const deviceLevel = this.config.deviceInfo?.performanceLevel || 'medium';
    const networkQuality = this.config.networkInfo?.quality || 'medium';
    
    // 根据设备性能调整策略
    this.adjustStrategiesForDevice(deviceLevel);
    
    // 根据网络质量调整策略
    this.adjustStrategiesForNetwork(networkQuality);
    
    // 应用具体策略
    this.applyAnimationStrategy();
    this.applyImageQualityStrategy();
    this.applyCachingStrategy();
    this.applyNetworkingStrategy();
    this.applyRenderingStrategy();
    
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[PerformanceConfig] 性能策略已应用', this.config.strategies); } catch(_) {}
  }

  /**
   * 根据设备性能调整策略
   */
  adjustStrategiesForDevice(deviceLevel) {
    switch (deviceLevel) {
      case 'low':
        this.config.strategies.animation = 'reduced';
        this.config.strategies.imageQuality = 'low';
        this.config.strategies.rendering = 'optimized';
        break;
      case 'medium':
        this.config.strategies.animation = 'auto';
        this.config.strategies.imageQuality = 'medium';
        this.config.strategies.rendering = 'auto';
        break;
      case 'high':
        this.config.strategies.animation = 'enhanced';
        this.config.strategies.imageQuality = 'high';
        this.config.strategies.rendering = 'auto';
        break;
    }
  }

  /**
   * 根据网络质量调整策略
   */
  adjustStrategiesForNetwork(networkQuality) {
    switch (networkQuality) {
      case 'low':
        this.config.strategies.imageQuality = 'low';
        this.config.strategies.caching = 'aggressive';
        this.config.strategies.networking = 'optimized';
        break;
      case 'medium':
        this.config.strategies.caching = 'normal';
        this.config.strategies.networking = 'standard';
        break;
      case 'high':
        this.config.strategies.caching = 'normal';
        this.config.strategies.networking = 'standard';
        break;
    }
  }

  /**
   * 应用动画策略
   */
  applyAnimationStrategy() {
    const strategy = this.config.strategies.animation;
    const pages = getCurrentPages();
    
    pages.forEach(page => {
      if (page.setData) {
        page.setData({
          animationStrategy: strategy,
          enableAnimations: strategy !== 'disabled',
          reducedMotion: strategy === 'reduced'
        });
      }
    });

    // 设置全局CSS类
    const body = wx.createSelectorQuery().select('body');
    if (body) {
      switch (strategy) {
        case 'disabled':
          // 通过setData通知页面禁用动画
          this.notifyGlobalChange('animation_disabled', true);
          break;
        case 'reduced':
          this.notifyGlobalChange('reduced_motion', true);
          break;
        case 'enhanced':
          this.notifyGlobalChange('enhanced_animations', true);
          break;
      }
    }
  }

  /**
   * 应用图片质量策略
   */
  applyImageQualityStrategy() {
    const strategy = this.config.strategies.imageQuality;
    
    // 设置图片质量配置
    const imageConfig = {
      low: { quality: 60, maxWidth: 800 },
      medium: { quality: 80, maxWidth: 1200 },
      high: { quality: 90, maxWidth: 1600 }
    };

    const config = imageConfig[strategy] || imageConfig.medium;
    
    // 通知全局图片配置变化
    this.notifyGlobalChange('image_quality_config', config);
  }

  /**
   * 应用缓存策略
   */
  applyCachingStrategy() {
    const strategy = this.config.strategies.caching;
    
    const cacheConfig = {
      minimal: { maxAge: 300, maxSize: 10 },      // 5分钟，10MB
      normal: { maxAge: 1800, maxSize: 50 },      // 30分钟，50MB
      aggressive: { maxAge: 86400, maxSize: 100 } // 24小时，100MB
    };

    const config = cacheConfig[strategy] || cacheConfig.normal;
    
    // 设置缓存配置
    wx.setStorageSync('cache_config', config);
    this.notifyGlobalChange('cache_config', config);
  }

  /**
   * 应用网络策略
   */
  applyNetworkingStrategy() {
    const strategy = this.config.strategies.networking;
    
    const networkConfig = {
      optimized: {
        timeout: 10000,
        retryCount: 2,
        enableGzip: true,
        enableCache: true
      },
      standard: {
        timeout: 15000,
        retryCount: 1,
        enableGzip: false,
        enableCache: false
      }
    };

    const config = networkConfig[strategy] || networkConfig.standard;
    
    this.notifyGlobalChange('network_config', config);
  }

  /**
   * 应用渲染策略
   */
  applyRenderingStrategy() {
    const strategy = this.config.strategies.rendering;
    
    const renderConfig = {
      optimized: {
        enableVirtualList: true,
        lazyLoadImages: true,
        batchUpdates: true,
        useGPUAcceleration: true
      },
      standard: {
        enableVirtualList: false,
        lazyLoadImages: false,
        batchUpdates: false,
        useGPUAcceleration: false
      }
    };

    const config = renderConfig[strategy] || renderConfig.standard;
    
    this.notifyGlobalChange('render_config', config);
  }

  /**
   * 设置性能模式
   */
  setPerformanceMode(mode) {
    this.config.performanceMode = mode;
    
    switch (mode) {
      case 'high':
        this.config.strategies = {
          animation: 'enhanced',
          imageQuality: 'high',
          caching: 'normal',
          networking: 'standard',
          rendering: 'auto'
        };
        break;
      case 'balanced':
        this.config.strategies = {
          animation: 'auto',
          imageQuality: 'medium',
          caching: 'normal',
          networking: 'standard',
          rendering: 'auto'
        };
        break;
      case 'battery':
        this.config.strategies = {
          animation: 'reduced',
          imageQuality: 'low',
          caching: 'aggressive',
          networking: 'optimized',
          rendering: 'optimized'
        };
        break;
    }
    
    this.applyPerformanceStrategies();
    this.saveUserPreferences();
  }

  /**
   * 保存用户偏好
   */
  saveUserPreferences() {
    try {
      wx.setStorageSync('performance_preferences', {
        performanceMode: this.config.performanceMode,
        strategies: this.config.strategies
      });
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[PerformanceConfig] 偏好保存失败', error); } catch(_) {}
    }
  }

  /**
   * 监听系统变化
   */
  setupSystemMonitoring() {
    // 监听网络状态变化
    wx.onNetworkStatusChange && wx.onNetworkStatusChange((res) => {
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[PerformanceConfig] 网络状态变化', res); } catch(_) {}
      
      this.config.networkInfo = {
        type: res.networkType,
        quality: this.evaluateNetworkQuality(res.networkType)
      };

      // 重新应用网络相关策略
      this.adjustStrategiesForNetwork(this.config.networkInfo.quality);
      this.applyNetworkingStrategy();
      this.applyImageQualityStrategy();
    });

    // 监听内存警告
    wx.onMemoryWarning && wx.onMemoryWarning((res) => {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[PerformanceConfig] 内存警告', res); } catch(_) {}
      
      // 临时降低性能要求
      this.handleMemoryPressure(res.level);
    });
  }

  /**
   * 处理内存压力
   */
  handleMemoryPressure(level) {
    const originalStrategies = { ...this.config.strategies };
    
    // 根据内存压力等级调整策略
    switch (level) {
      case 5: // 严重内存不足
        this.config.strategies.animation = 'disabled';
        this.config.strategies.imageQuality = 'low';
        this.config.strategies.caching = 'minimal';
        break;
      case 10: // 内存不足
        this.config.strategies.animation = 'reduced';
        this.config.strategies.imageQuality = 'medium';
        break;
      case 15: // 内存紧张
        this.config.strategies.animation = 'auto';
        break;
    }

    this.applyPerformanceStrategies();
    
    // 5分钟后恢复原策略
    setTimeout(() => {
      this.config.strategies = originalStrategies;
      this.applyPerformanceStrategies();
    }, 300000);
  }

  /**
   * 通知全局配置变化
   */
  notifyGlobalChange(type, config) {
    this.callbacks.forEach(callback => {
      try {
        callback(type, config);
      } catch (error) {
        try { const logger = require('./logger.js'); logger.error && logger.error('[PerformanceConfig] 回调执行失败', error); } catch(_) {}
      }
    });

    // 通知当前页面
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      if (currentPage && currentPage.setData) {
        currentPage.setData({
          [`performanceConfig_${type}`]: config
        });
      }
    }
  }

  /**
   * 注册配置变化回调
   */
  onChange(callback) {
    this.callbacks.push(callback);
  }

  /**
   * 移除配置变化回调
   */
  offChange(callback) {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 获取性能建议
   */
  getPerformanceRecommendations() {
    const recommendations = [];
    const deviceLevel = this.config.deviceInfo?.performanceLevel;
    const networkQuality = this.config.networkInfo?.quality;

    if (deviceLevel === 'low') {
      recommendations.push({
        type: 'device',
        message: '检测到低端设备，建议启用省电模式以获得更好的性能',
        action: 'setPerformanceMode',
        params: ['battery']
      });
    }

    if (networkQuality === 'low') {
      recommendations.push({
        type: 'network',
        message: '检测到网络较慢，建议开启激进缓存策略',
        action: 'setCachingStrategy',
        params: ['aggressive']
      });
    }

    return recommendations;
  }

  /**
   * 执行性能优化建议
   */
  applyRecommendation(recommendation) {
    switch (recommendation.action) {
      case 'setPerformanceMode':
        this.setPerformanceMode(recommendation.params[0]);
        break;
      case 'setCachingStrategy':
        this.config.strategies.caching = recommendation.params[0];
        this.applyCachingStrategy();
        break;
      default:
        try { const logger = require('./logger.js'); logger.warn && logger.warn('[PerformanceConfig] 未知的建议操作', recommendation.action); } catch(_) {}
    }
  }
}

// 创建全局实例
const performanceConfig = new PerformanceConfigManager();

// 页面混入对象
const PerformanceConfigMixin = {
  data: {
    performanceConfig: {},
    animationStrategy: 'auto',
    enableAnimations: true,
    reducedMotion: false
  },

  onLoad() {
    // 获取性能配置
    this.data.performanceConfig = performanceConfig.getConfig();
    
    // 监听配置变化
    this.performanceConfigChangeHandler = (type, config) => {
      this.setData({
        [`performanceConfig_${type}`]: config
      });
    };
    
    performanceConfig.onChange(this.performanceConfigChangeHandler);
  },

  onUnload() {
    // 清理监听器
    if (this.performanceConfigChangeHandler) {
      performanceConfig.offChange(this.performanceConfigChangeHandler);
    }
  },

  // 便捷方法
  isHighPerformanceDevice() {
    return this.data.performanceConfig.deviceInfo?.performanceLevel === 'high';
  },

  isLowPerformanceDevice() {
    return this.data.performanceConfig.deviceInfo?.performanceLevel === 'low';
  },

  shouldReduceAnimations() {
    return this.data.animationStrategy === 'reduced' || this.data.animationStrategy === 'disabled';
  },

  shouldUseHighQualityImages() {
    return this.data.performanceConfig.strategies?.imageQuality === 'high';
  }
};

module.exports = {
  PerformanceConfigManager,
  performanceConfig,
  PerformanceConfigMixin
};