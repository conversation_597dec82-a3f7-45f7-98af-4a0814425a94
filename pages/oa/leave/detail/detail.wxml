<!-- pages/oa/leave/detail/detail.wxml -->
<view class="detail-container">
  
  <block wx:if="{{!loading && leaveDetail}}">
    
    <!-- 状态头部 -->
    <view class="status-header">
      <view class="status-content">
        <view class="status-badge" style="background-color: {{leaveDetail.statusColor}};">
          {{leaveDetail.statusText}}
        </view>
        <text class="status-title">{{leaveDetail.typeLabel}}申请</text>
        <text class="status-subtitle">申请编号：{{leaveDetail.id}}</text>
      </view>
    </view>

    <!-- 申请人信息 -->
    <view class="section applicant-section">
      <view class="section-title">
        <text>申请人信息</text>
      </view>
      <view class="applicant-card">
        <image class="applicant-avatar" src="{{leaveDetail.applicant.avatar}}" mode="aspectFill"></image>
        <view class="applicant-info">
          <text class="applicant-name">{{leaveDetail.applicant.name}}</text>
          <text class="applicant-position">{{leaveDetail.applicant.position}}</text>
          <text class="applicant-department">{{leaveDetail.applicant.department}}</text>
        </view>
        <view class="contact-actions">
          <button class="contact-btn" bindtap="onMakeCall" data-phone="{{leaveDetail.applicant.phone}}">
            <image class="contact-icon" src="/images/icons/phone.png" mode="aspectFit"></image>
          </button>
        </view>
      </view>
    </view>

    <!-- 请假详情 -->
    <view class="section leave-detail-section">
      <view class="section-title">
        <text>请假详情</text>
      </view>
      
      <!-- 请假类型 -->
      <view class="detail-item">
        <text class="detail-label">请假类型</text>
        <view class="detail-value">
          <view class="type-badge" style="background-color: {{leaveDetail.typeColor}};">
            {{leaveDetail.typeLabel}}
          </view>
        </view>
      </view>
      
      <!-- 请假原因 -->
      <view class="detail-item">
        <text class="detail-label">请假原因</text>
        <text class="detail-value reason-text">{{leaveDetail.reason}}</text>
      </view>
      
      <!-- 请假时间 -->
      <view class="detail-item">
        <text class="detail-label">开始时间</text>
        <text class="detail-value">{{leaveDetail.startDate}} {{leaveDetail.startTime}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">结束时间</text>
        <text class="detail-value">{{leaveDetail.endDate}} {{leaveDetail.endTime}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">请假天数</text>
        <view class="detail-value">
          <view class="days-highlight">{{leaveDetail.days}} 天</view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="detail-item">
        <text class="detail-label">联系电话</text>
        <view class="detail-value">
          <text class="phone-text">{{leaveDetail.contact}}</text>
          <button class="call-btn" bindtap="onMakeCall" data-phone="{{leaveDetail.contact}}">拨打</button>
        </view>
      </view>
      
      <!-- 工作交接 -->
      <view class="detail-item" wx:if="{{leaveDetail.backup}}">
        <text class="detail-label">工作交接人</text>
        <text class="detail-value">{{leaveDetail.backup.name}} ({{leaveDetail.backup.department}})</text>
      </view>
      
      <!-- 紧急联系人 -->
      <view class="detail-item" wx:if="{{leaveDetail.emergency}}">
        <text class="detail-label">紧急联系人</text>
        <text class="detail-value">{{leaveDetail.emergency}}</text>
      </view>
      
      <!-- 提交时间 -->
      <view class="detail-item">
        <text class="detail-label">提交时间</text>
        <text class="detail-value">{{leaveDetail.submitTime}}</text>
      </view>
    </view>

    <!-- 相关证明 -->
    <view class="section attachment-section" wx:if="{{leaveDetail.attachment && leaveDetail.attachment.length > 0}}">
      <view class="section-title">
        <text>相关证明</text>
      </view>
      <view class="attachment-grid">
        <block wx:for="{{leaveDetail.attachment}}" wx:key="index">
          <view class="attachment-item" bindtap="onPreviewAttachment" data-url="{{item}}">
            <image class="attachment-image" src="{{item}}" mode="aspectFill"></image>
            <view class="attachment-overlay">
              <image class="preview-icon" src="/images/icons/preview.png" mode="aspectFit"></image>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 审批记录 -->
    <view class="section approval-flow-section">
      <view class="section-title">
        <text>审批流程</text>
      </view>
      <view class="flow-timeline">
        <block wx:for="{{approvalFlow}}" wx:key="step">
          <view class="flow-item {{item.status}}">
            <view class="flow-step">
              <view class="step-number {{item.status}}">{{item.step}}</view>
              <view class="step-line" wx:if="{{index < approvalFlow.length - 1}}"></view>
            </view>
            <view class="flow-content">
              <text class="flow-title">{{item.title}}</text>
              <text class="flow-user">{{item.user}}</text>
              <text class="flow-time" wx:if="{{item.time}}">{{item.time}}</text>
              <text class="flow-description">{{item.description}}</text>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 审批意见 -->
    <view class="section approval-comment-section" wx:if="{{leaveDetail.status !== 'pending'}}">
      <view class="section-title">
        <text>审批意见</text>
      </view>
      <view class="comment-card">
        <view class="comment-header">
          <text class="comment-approver">审批人：{{leaveDetail.approver}}</text>
          <text class="comment-time">{{leaveDetail.approveTime}}</text>
        </view>
        <view class="comment-content">
          <text class="comment-action" style="color: {{leaveDetail.statusColor}};">
            {{leaveDetail.status === 'approved' ? '同意' : '拒绝'}}
          </text>
          <text class="comment-text" wx:if="{{leaveDetail.rejectReason}}">
            {{leaveDetail.rejectReason}}
          </text>
        </view>
      </view>
    </view>

    <!-- 审批操作 -->
    <view class="section approval-actions-section" wx:if="{{currentUser.canApprove}}">
      <view class="section-title">
        <text>审批操作</text>
      </view>
      <view class="approval-buttons">
        <button class="reject-btn" bindtap="showApprovalForm" data-action="reject">
          <image class="btn-icon" src="/images/icons/close.png" mode="aspectFit"></image>
          <text>拒绝</text>
        </button>
        <button class="approve-btn" bindtap="showApprovalForm" data-action="approve">
          <image class="btn-icon" src="/images/icons/check.png" mode="aspectFit"></image>
          <text>通过</text>
        </button>
      </view>
    </view>

  </block>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 审批表单弹窗 -->
  <view wx:if="{{approvalForm.visible}}" class="approval-modal">
    <view class="modal-mask" bindtap="hideApprovalForm"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">
          {{approvalForm.action === 'approve' ? '通过申请' : '拒绝申请'}}
        </text>
        <view class="modal-close" bindtap="hideApprovalForm">×</view>
      </view>
      
      <view class="modal-body">
        <text class="comment-label">审批意见</text>
        <textarea 
          class="comment-textarea"
          placeholder="{{approvalForm.action === 'approve' ? '请填写通过意见...' : '请填写拒绝原因...'}}"
          value="{{approvalForm.comment}}"
          bindinput="onCommentInput"
          maxlength="200"
          show-confirm-bar="{{false}}"
          auto-height></textarea>
        <view class="char-count">{{approvalForm.comment.length}}/200</view>
      </view>
      
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="hideApprovalForm" disabled="{{submitting}}">
          取消
        </button>
        <button class="confirm-btn" bindtap="confirmApproval" disabled="{{submitting}}" loading="{{submitting}}">
          {{submitting ? '提交中...' : '确认'}}
        </button>
      </view>
    </view>
  </view>

</view>