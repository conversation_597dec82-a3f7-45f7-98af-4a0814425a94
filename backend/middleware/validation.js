/**
 * 数据验证中间件
 * 提供各种数据验证功能，支持Joi验证库
 */

const Joi = require("joi");

/**
 * 验证中间件工厂函数
 * @param {Object} schema - Joi验证模式
 * @param {string} source - 验证数据源 ('body', 'query', 'params')
 * @returns {Function} Express中间件函数
 */
const validateRequest = (schema, source = "body") => {
  return (req, res, next) => {
    const data = req[source];

    const { error, value } = schema.validate(data, {
      abortEarly: false, // 返回所有错误
      allowUnknown: true, // 允许未知字段
      stripUnknown: true, // 移除未知字段
    });

    if (error) {
      const errors = error.details.map((detail) => ({
        field: detail.path.join("."),
        message: detail.message,
        value: detail.context.value,
      }));

      return res.status(400).json({
        success: false,
        message: "输入数据验证失败",
        errors: errors,
      });
    }

    // 将验证后的数据替换原始数据
    req[source] = value;
    next();
  };
};

/**
 * 用户相关验证模式
 */
const userSchemas = {
  // 用户注册验证
  register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required().messages({
      "string.alphanum": "用户名只能包含字母和数字",
      "string.min": "用户名至少3个字符",
      "string.max": "用户名最多30个字符",
      "any.required": "用户名为必填项",
    }),

    password: Joi.string()
      .min(6)
      .max(128)
      .pattern(new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)"))
      .required()
      .messages({
        "string.min": "密码至少6个字符",
        "string.max": "密码最多128个字符",
        "string.pattern.base": "密码必须包含大小写字母和数字",
        "any.required": "密码为必填项",
      }),

    email: Joi.string().email().required().messages({
      "string.email": "请输入有效的邮箱地址",
      "any.required": "邮箱为必填项",
    }),

    name: Joi.string().min(2).max(50).optional(),

    farmName: Joi.string().min(2).max(100).optional(),

    phone: Joi.string()
      .pattern(new RegExp("^1[3-9]\\d{9}$"))
      .optional()
      .messages({
        "string.pattern.base": "请输入有效的手机号码",
      }),
  }),

  // 用户登录验证
  login: Joi.object({
    username: Joi.string().required().messages({
      "any.required": "用户名为必填项",
    }),

    password: Joi.string().required().messages({
      "any.required": "密码为必填项",
    }),
  }),
};

/**
 * 健康记录相关验证模式
 */
const healthRecordSchemas = {
  // 创建健康记录验证
  create: Joi.object({
    gooseId: Joi.string().min(1).max(50).required().messages({
      "string.min": "鹅群编号不能为空",
      "string.max": "鹅群编号最多50个字符",
      "any.required": "鹅群编号为必填项",
    }),

    healthStatus: Joi.string()
      .valid("healthy", "sick", "recovering", "dead")
      .required()
      .messages({
        "any.only": "健康状态必须是: healthy, sick, recovering, dead 之一",
        "any.required": "健康状态为必填项",
      }),

    symptoms: Joi.string().max(1000).optional().messages({
      "string.max": "症状描述最多1000个字符",
    }),

    diagnosis: Joi.string().max(1000).optional().messages({
      "string.max": "诊断结果最多1000个字符",
    }),

    treatment: Joi.string().max(1000).optional().messages({
      "string.max": "治疗方案最多1000个字符",
    }),
  }),
};

/**
 * 验证任务创建数据
 */
const validateTask = (req, res, next) => {
  const { title, priority, category } = req.body;
  const errors = [];

  // 验证标题
  if (!title || typeof title !== "string" || title.trim().length === 0) {
    errors.push("任务标题不能为空");
  } else if (title.length > 200) {
    errors.push("任务标题不能超过200个字符");
  }

  // 验证优先级
  if (priority && !["low", "medium", "high"].includes(priority)) {
    errors.push("优先级必须是 low、medium 或 high");
  }

  // 验证分类
  if (category && (typeof category !== "string" || category.length > 50)) {
    errors.push("任务分类不能超过50个字符");
  }

  // 验证负责人
  if (req.body.assignee && req.body.assignee.length > 100) {
    errors.push("负责人名称不能超过100个字符");
  }

  // 验证截止时间
  if (req.body.deadline) {
    const deadline = new Date(req.body.deadline);
    if (isNaN(deadline.getTime())) {
      errors.push("截止时间格式不正确");
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: "数据验证失败",
      errors,
    });
  }

  next();
};

/**
 * 验证任务更新数据
 */
const validateTaskUpdate = (req, res, next) => {
  const { title, priority, category } = req.body;
  const errors = [];

  // 验证标题（如果提供）
  if (title !== undefined) {
    if (!title || typeof title !== "string" || title.trim().length === 0) {
      errors.push("任务标题不能为空");
    } else if (title.length > 200) {
      errors.push("任务标题不能超过200个字符");
    }
  }

  // 验证优先级（如果提供）
  if (priority !== undefined && !["low", "medium", "high"].includes(priority)) {
    errors.push("优先级必须是 low、medium 或 high");
  }

  // 验证分类（如果提供）
  if (
    category !== undefined &&
    (typeof category !== "string" || category.length > 50)
  ) {
    errors.push("任务分类不能超过50个字符");
  }

  // 验证负责人（如果提供）
  if (req.body.assignee !== undefined && req.body.assignee.length > 100) {
    errors.push("负责人名称不能超过100个字符");
  }

  // 验证截止时间（如果提供）
  if (req.body.deadline !== undefined && req.body.deadline !== null) {
    const deadline = new Date(req.body.deadline);
    if (isNaN(deadline.getTime())) {
      errors.push("截止时间格式不正确");
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: "数据验证失败",
      errors,
    });
  }

  next();
};

/**
 * 验证公告创建数据
 */
const validateAnnouncement = (req, res, next) => {
  const { title, content, type, publisher } = req.body;
  const errors = [];

  // 验证标题
  if (!title || typeof title !== "string" || title.trim().length === 0) {
    errors.push("公告标题不能为空");
  } else if (title.length > 200) {
    errors.push("公告标题不能超过200个字符");
  }

  // 验证内容
  if (!content || typeof content !== "string" || content.trim().length === 0) {
    errors.push("公告内容不能为空");
  }

  // 验证类型
  if (!type || !["important", "notice", "policy", "activity"].includes(type)) {
    errors.push("公告类型必须是 important、notice、policy 或 activity");
  }

  // 验证发布者
  if (
    !publisher ||
    typeof publisher !== "string" ||
    publisher.trim().length === 0
  ) {
    errors.push("发布者不能为空");
  } else if (publisher.length > 100) {
    errors.push("发布者名称不能超过100个字符");
  }

  // 验证摘要
  if (req.body.summary && req.body.summary.length > 500) {
    errors.push("公告摘要不能超过500个字符");
  }

  // 验证发布时间
  if (req.body.publish_time) {
    const publishTime = new Date(req.body.publish_time);
    if (isNaN(publishTime.getTime())) {
      errors.push("发布时间格式不正确");
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: "数据验证失败",
      errors,
    });
  }

  next();
};

/**
 * 验证物料创建数据
 */
const validateMaterial = (req, res, next) => {
  const { name, code, category, unit } = req.body;
  const errors = [];

  // 验证名称
  if (!name || typeof name !== "string" || name.trim().length === 0) {
    errors.push("物料名称不能为空");
  } else if (name.length > 100) {
    errors.push("物料名称不能超过100个字符");
  }

  // 验证编码
  if (!code || typeof code !== "string" || code.trim().length === 0) {
    errors.push("物料编码不能为空");
  } else if (code.length > 50) {
    errors.push("物料编码不能超过50个字符");
  }

  // 验证分类
  if (
    !category ||
    !["feed", "medicine", "equipment", "other"].includes(category)
  ) {
    errors.push("物料分类必须是 feed、medicine、equipment 或 other");
  }

  // 验证单位
  if (!unit || typeof unit !== "string" || unit.trim().length === 0) {
    errors.push("物料单位不能为空");
  } else if (unit.length > 20) {
    errors.push("物料单位不能超过20个字符");
  }

  // 验证规格
  if (req.body.specification && req.body.specification.length > 100) {
    errors.push("物料规格不能超过100个字符");
  }

  // 验证供应商
  if (req.body.supplier && req.body.supplier.length > 100) {
    errors.push("供应商名称不能超过100个字符");
  }

  // 验证数值字段
  const numericFields = ["current_stock", "safety_stock", "max_stock"];
  numericFields.forEach((field) => {
    if (req.body[field] !== undefined) {
      const value = parseFloat(req.body[field]);
      if (isNaN(value) || value < 0) {
        errors.push(`${field} 必须是非负数`);
      }
    }
  });

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: "数据验证失败",
      errors,
    });
  }

  next();
};

/**
 * 验证库存记录数据
 */
const validateInventoryRecord = (req, res, next) => {
  const { breed_name, age_category, area_location, count } = req.body;
  const errors = [];

  // 验证品种名称
  if (
    !breed_name ||
    typeof breed_name !== "string" ||
    breed_name.trim().length === 0
  ) {
    errors.push("品种名称不能为空");
  } else if (breed_name.length > 100) {
    errors.push("品种名称不能超过100个字符");
  }

  // 验证年龄分类
  if (!age_category || !["gosling", "young", "adult"].includes(age_category)) {
    errors.push("年龄分类必须是 gosling、young 或 adult");
  }

  // 验证区域位置
  if (
    !area_location ||
    typeof area_location !== "string" ||
    area_location.trim().length === 0
  ) {
    errors.push("区域位置不能为空");
  } else if (area_location.length > 50) {
    errors.push("区域位置不能超过50个字符");
  }

  // 验证数量
  if (count === undefined || count === null) {
    errors.push("数量不能为空");
  } else {
    const countValue = parseInt(count);
    if (isNaN(countValue) || countValue < 0) {
      errors.push("数量必须是非负整数");
    }
  }

  // 验证健康状态
  if (
    req.body.health_status &&
    !["healthy", "sick", "quarantine"].includes(req.body.health_status)
  ) {
    errors.push("健康状态必须是 healthy、sick 或 quarantine");
  }

  // 验证记录日期
  if (req.body.record_date) {
    const recordDate = new Date(req.body.record_date);
    if (isNaN(recordDate.getTime())) {
      errors.push("记录日期格式不正确");
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: "数据验证失败",
      errors,
    });
  }

  next();
};

module.exports = {
  // Joi验证相关
  validateRequest,
  userSchemas,
  healthRecordSchemas,

  // 原有验证函数
  validateTask,
  validateTaskUpdate,
  validateAnnouncement,
  validateMaterial,
  validateInventoryRecord,
};
