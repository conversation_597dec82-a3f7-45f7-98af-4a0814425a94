<!-- 关于我们页面 -->
<view class="about-container">
  <!-- 应用信息 -->
  <view class="app-info-section">
    <view class="app-logo">
      <image class="logo-image" src="/images/default-logo.png" mode="aspectFit"></image>
    </view>
    <view class="app-details">
      <text class="app-name">智慧养鹅管理系统</text>
      <text class="app-version">版本 {{appInfo.version}}</text>
      <text class="app-description">专业的智能化养鹅管理解决方案</text>
    </view>
  </view>

  <!-- 产品特色 -->
  <view class="features-section">
    <view class="section-header">
      <text class="section-title">产品特色</text>
    </view>
    <view class="features-grid">
      <view class="feature-item" wx:for="{{features}}" wx:key="id">
        <view class="feature-icon">{{item.icon}}</view>
        <text class="feature-title">{{item.title}}</text>
        <text class="feature-desc">{{item.description}}</text>
      </view>
    </view>
  </view>

  <!-- 公司信息 -->
  <view class="company-section">
    <view class="section-header">
      <text class="section-title">公司信息</text>
    </view>
    <view class="company-card">
      <view class="company-info">
        <text class="company-name">{{companyInfo.name}}</text>
        <text class="company-desc">{{companyInfo.description}}</text>
        <view class="company-details">
          <view class="detail-item">
            <text class="detail-label">成立时间：</text>
            <text class="detail-value">{{companyInfo.foundedYear}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">注册地址：</text>
            <text class="detail-value">{{companyInfo.address}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">联系邮箱：</text>
            <text class="detail-value">{{companyInfo.email}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 技术团队 -->
  <view class="team-section">
    <view class="section-header">
      <text class="section-title">技术团队</text>
    </view>
    <view class="team-grid">
      <view class="team-member" wx:for="{{teamMembers}}" wx:key="id">
        <image class="member-avatar" src="{{item.avatar}}" mode="aspectFit"></image>
        <text class="member-name">{{item.name}}</text>
        <text class="member-role">{{item.role}}</text>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <view class="section-header">
      <text class="section-title">联系我们</text>
    </view>
    <view class="contact-list">
      <view class="contact-item" bindtap="onCall">
        <view class="contact-icon">📞</view>
        <view class="contact-info">
          <text class="contact-title">客服热线</text>
          <text class="contact-value">{{contactInfo.phone}}</text>
        </view>
        <text class="contact-action">拨打</text>
      </view>
      <view class="contact-item" bindtap="onCopyEmail">
        <view class="contact-icon">📧</view>
        <view class="contact-info">
          <text class="contact-title">邮箱地址</text>
          <text class="contact-value">{{contactInfo.email}}</text>
        </view>
        <text class="contact-action">复制</text>
      </view>
      <view class="contact-item">
        <view class="contact-icon">🕒</view>
        <view class="contact-info">
          <text class="contact-title">工作时间</text>
          <text class="contact-value">{{contactInfo.workTime}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 法律信息 -->
  <view class="legal-section">
    <view class="section-header">
      <text class="section-title">法律信息</text>
    </view>
    <view class="legal-links">
      <view class="legal-item" bindtap="onViewPrivacyPolicy">
        <text class="legal-title">隐私政策</text>
        <text class="legal-arrow">></text>
      </view>
      <view class="legal-item" bindtap="onViewUserAgreement">
        <text class="legal-title">用户协议</text>
        <text class="legal-arrow">></text>
      </view>
      <view class="legal-item" bindtap="onViewOpenSource">
        <text class="legal-title">开源许可</text>
        <text class="legal-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 更新日志 -->
  <view class="changelog-section">
    <view class="section-header">
      <text class="section-title">更新日志</text>
    </view>
    <view class="changelog-list">
      <view class="changelog-item" wx:for="{{changelog}}" wx:key="version">
        <view class="changelog-header">
          <text class="changelog-version">v{{item.version}}</text>
          <text class="changelog-date">{{item.date}}</text>
        </view>
        <view class="changelog-content">
          <text class="changelog-desc" wx:for="{{item.changes}}" wx:key="*this">• {{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 反馈按钮 -->
  <view class="feedback-section">
    <button class="feedback-btn" bindtap="onFeedback">
      <text class="feedback-text">意见反馈</text>
    </button>
    <button class="help-btn" bindtap="onHelp">
      <text class="help-text">帮助中心</text>
    </button>
  </view>

  <!-- 版权信息 -->
  <view class="copyright-section">
    <text class="copyright-text">© 2024 智慧养鹅管理系统</text>
    <text class="copyright-text">All rights reserved</text>
  </view>
</view>