/* pages/management/dashboard/dashboard.wxss */

.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 页面标题 */
.page-header {
  padding: var(--space-3xl) var(--space-2xl) var(--space-xl);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 数据概览 */
.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-top: 20rpx;
}

.overview-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border-radius: 16rpx;
  border: 2rpx solid #e0e8ff;
}

.overview-value {
  display: block;
  font-size: var(--text-3xl);
  font-weight: bold;
  color: var(--primary);
  margin-bottom: var(--space-sm);
}

.overview-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.overview-detail {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 待处理事项 */
.pending-tasks-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.pending-task-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  text-align: center;
}

.task-icon-wrapper {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.task-icon-wrapper.approval {
  background: linear-gradient(135deg, #ff9800 0%, #ff7043 100%);
}

.task-icon-wrapper.alert {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.task-icon-wrapper.report {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.task-icon-wrapper.maintenance {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.task-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.task-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  border: 3rpx solid white;
}

.task-label {
  font-size: 24rpx;
  color: #666;
}

/* 快捷功能 */
.quick-actions-grid {
  margin-top: 20rpx;
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  position: relative;
}

.quick-action-item:last-child {
  border-bottom: none;
}

.quick-action-item:active {
  background: #f8f9fa;
}

.action-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #0066CC 0%, #004499 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.action-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.action-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
  margin-left: 20rpx;
}

/* 最近活动 */
.recent-activities {
  margin-top: 20rpx;
}

.activity-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  width: 120rpx;
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .overview-grid {
    gap: 20rpx;
  }

  .overview-item {
    padding: 25rpx 15rpx;
  }

  .overview-value {
    font-size: 40rpx;
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background-color: #1a1a1a;
  }

  .overview-item {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
    border-color: #404040;
  }

  .overview-value {
    color: #66b3ff;
  }

  .overview-label {
    color: #ccc;
  }

  .overview-detail {
    color: #999;
  }

  .action-title {
    color: #fff;
  }

  .action-desc {
    color: #ccc;
  }

  .activity-title {
    color: #fff;
  }

  .activity-desc {
    color: #ccc;
  }
}