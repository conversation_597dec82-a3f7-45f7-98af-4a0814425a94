<!-- 统一页脚组件 -->
<footer class="footer-modern">
    <div class="footer-content">
        <div class="footer-left">
            <div class="footer-brand">
                <i class="bi bi-egg-fried brand-icon"></i>
                <span class="brand-text">智慧养鹅</span>
                <% if (platformType === 'saas') { %>
                    <span class="platform-badge">SaaS</span>
                <% } %>
            </div>
            <div class="footer-info">
                <p class="copyright">© 2024 智慧养鹅<%= platformType === 'saas' ? 'SaaS平台' : '管理系统' %>. 保留所有权利.</p>
                <div class="footer-meta">
                    <span class="version">版本 v2.6.0</span>
                    <span class="divider">•</span>
                    <span class="build">构建 <%= new Date().toISOString().split('T')[0] %></span>
                    <span class="divider">•</span>
                    <span class="uptime" id="systemUptime">运行时间: --</span>
                </div>
            </div>
        </div>
        
        <div class="footer-center">
            <div class="footer-links">
                <% if (platformType === 'saas') { %>
                    <!-- SaaS平台链接 -->
                    <div class="link-group">
                        <h6>平台管理</h6>
                        <a href="/tenants">租户管理</a>
                        <a href="/pricing">价格管理</a>
                        <a href="/monitoring">性能监控</a>
                    </div>
                    <div class="link-group">
                        <h6>系统设置</h6>
                        <a href="/ai-config">AI配置</a>
                        <a href="/announcements">公告管理</a>
                        <a href="/logs">系统日志</a>
                    </div>
                    <div class="link-group">
                        <h6>帮助支持</h6>
                        <a href="/help/platform">平台文档</a>
                        <a href="/api-docs">API文档</a>
                        <a href="/knowledge">知识库</a>
                    </div>
                <% } else { %>
                    <!-- 普通管理系统链接 -->
                    <div class="link-group">
                        <h6>养殖管理</h6>
                        <a href="/flocks">鹅群管理</a>
                        <a href="/health">健康管理</a>
                        <a href="/production">生产管理</a>
                    </div>
                    <div class="link-group">
                        <h6>数据分析</h6>
                        <a href="/reports">报表中心</a>
                        <a href="/analytics">数据分析</a>
                        <a href="/statistics">统计概览</a>
                    </div>
                    <div class="link-group">
                        <h6>系统工具</h6>
                        <a href="/settings">系统设置</a>
                        <a href="/api-docs">API文档</a>
                        <a href="/help">帮助中心</a>
                    </div>
                <% } %>
            </div>
        </div>
        
        <div class="footer-right">
            <div class="footer-stats">
                <div class="stat-item">
                    <i class="bi bi-server"></i>
                    <div class="stat-content">
                        <div class="stat-label">服务状态</div>
                        <div class="stat-value" id="serverStatus">
                            <span class="status-indicator online"></span>
                            在线
                        </div>
                    </div>
                </div>
                
                <% if (platformType === 'saas') { %>
                    <div class="stat-item">
                        <i class="bi bi-building"></i>
                        <div class="stat-content">
                            <div class="stat-label">活跃租户</div>
                            <div class="stat-value" id="activeTenants">--</div>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <i class="bi bi-cpu"></i>
                        <div class="stat-content">
                            <div class="stat-label">系统负载</div>
                            <div class="stat-value" id="systemLoad">--</div>
                        </div>
                    </div>
                <% } else { %>
                    <div class="stat-item">
                        <i class="bi bi-egg"></i>
                        <div class="stat-content">
                            <div class="stat-label">活跃鹅群</div>
                            <div class="stat-value" id="activeFlocks">--</div>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <i class="bi bi-database"></i>
                        <div class="stat-content">
                            <div class="stat-label">数据量</div>
                            <div class="stat-value" id="dataCount">--</div>
                        </div>
                    </div>
                <% } %>
            </div>
            
            <div class="footer-actions">
                <button class="footer-btn" onclick="showSystemInfo()" title="系统信息">
                    <i class="bi bi-info-circle"></i>
                </button>
                <button class="footer-btn" onclick="checkSystemHealth()" title="健康检查">
                    <i class="bi bi-heart-pulse"></i>
                </button>
                <button class="footer-btn" onclick="exportSystemReport()" title="导出报告">
                    <i class="bi bi-download"></i>
                </button>
                <button class="footer-btn" onclick="showKeyboardShortcuts()" title="快捷键">
                    <i class="bi bi-keyboard"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部分隔线 -->
    <div class="footer-bottom">
        <div class="footer-bottom-content">
            <div class="footer-bottom-left">
                <span class="tech-info">
                    基于 Node.js + MySQL + Bootstrap 构建 | 
                    <a href="https://github.com/smart-goose-farming" target="_blank" rel="noopener">
                        <i class="bi bi-github"></i> 开源项目
                    </a>
                </span>
            </div>
            <div class="footer-bottom-right">
                <div class="footer-bottom-stats">
                    <span id="responseTime">响应时间: --ms</span>
                    <span class="divider">•</span>
                    <span id="lastUpdate">最后更新: --</span>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
    /* 页脚样式 */
    .footer-modern {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        margin-top: auto;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
    
    .footer-content {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        gap: 40px;
        padding: 40px 24px 20px;
        max-width: 1400px;
        margin: 0 auto;
    }
    
    /* 页脚左侧 */
    .footer-left {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }
    
    .footer-brand {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
    }
    
    .footer-brand .brand-icon {
        font-size: 28px;
        color: var(--primary-color);
    }
    
    .footer-brand .brand-text {
        font-size: 20px;
        font-weight: 700;
    }
    
    .platform-badge {
        background: var(--primary-color);
        color: white;
        font-size: 10px;
        font-weight: 600;
        padding: 2px 8px;
        border-radius: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .footer-info {
        color: rgba(255,255,255,0.8);
    }
    
    .copyright {
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 1.4;
    }
    
    .footer-meta {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
        color: rgba(255,255,255,0.6);
    }
    
    .divider {
        color: rgba(255,255,255,0.3);
    }
    
    /* 页脚中间 */
    .footer-center {
        display: flex;
        justify-content: center;
    }
    
    .footer-links {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32px;
        width: 100%;
        max-width: 600px;
    }
    
    .link-group h6 {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: white;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 4px;
    }
    
    .link-group a {
        display: block;
        color: rgba(255,255,255,0.7);
        text-decoration: none;
        font-size: 13px;
        margin-bottom: 8px;
        transition: color 0.2s ease;
        padding: 2px 0;
    }
    
    .link-group a:hover {
        color: var(--primary-color);
        padding-left: 4px;
    }
    
    /* 页脚右侧 */
    .footer-right {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
    
    .footer-stats {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .stat-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 0;
    }
    
    .stat-item i {
        font-size: 16px;
        color: var(--primary-color);
        width: 20px;
        text-align: center;
    }
    
    .stat-content {
        flex: 1;
    }
    
    .stat-label {
        font-size: 11px;
        color: rgba(255,255,255,0.6);
        margin-bottom: 2px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .stat-value {
        font-size: 13px;
        font-weight: 600;
        color: white;
        display: flex;
        align-items: center;
        gap: 6px;
    }
    
    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }
    
    .status-indicator.online {
        background: var(--success-color);
    }
    
    .status-indicator.offline {
        background: var(--danger-color);
    }
    
    .status-indicator.warning {
        background: var(--warning-color);
    }
    
    .footer-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .footer-btn {
        background: rgba(255,255,255,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        color: rgba(255,255,255,0.8);
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: 14px;
        cursor: pointer;
    }
    
    .footer-btn:hover {
        background: rgba(255,255,255,0.2);
        color: white;
        transform: translateY(-1px);
    }
    
    /* 页脚底部 */
    .footer-bottom {
        border-top: 1px solid rgba(255,255,255,0.1);
        padding: 16px 0;
    }
    
    .footer-bottom-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 24px;
    }
    
    .footer-bottom-left {
        color: rgba(255,255,255,0.6);
        font-size: 12px;
    }
    
    .footer-bottom-left a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s ease;
    }
    
    .footer-bottom-left a:hover {
        color: var(--secondary-color);
    }
    
    .footer-bottom-stats {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 11px;
        color: rgba(255,255,255,0.5);
    }
    
    /* 响应式设计 */
    @media (max-width: 1024px) {
        .footer-content {
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .footer-right {
            grid-column: 1 / -1;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .footer-links {
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }
    }
    
    @media (max-width: 768px) {
        .footer-content {
            grid-template-columns: 1fr;
            gap: 24px;
            padding: 24px 16px 16px;
        }
        
        .footer-links {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .footer-right {
            flex-direction: column;
            gap: 16px;
        }
        
        .footer-bottom-content {
            flex-direction: column;
            gap: 12px;
            text-align: center;
            padding: 0 16px;
        }
        
        .footer-bottom-stats {
            justify-content: center;
        }
    }
    
    @media (max-width: 576px) {
        .footer-brand {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }
        
        .footer-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }
        
        .footer-actions {
            justify-content: center;
        }
    }
    
    /* 动画效果 */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.6; }
    }
    
    .footer-modern {
        animation: slideInUp 0.5s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* 深色主题适配 */
    .dark-theme .footer-modern {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }
    
    .dark-theme .footer-bottom {
        border-top-color: rgba(255,255,255,0.05);
    }
</style>

<script>
    // 页脚功能
    document.addEventListener('DOMContentLoaded', function() {
        updateSystemStats();
        updateSystemTime();
        
        // 定期更新统计信息
        setInterval(updateSystemStats, 30000); // 30秒更新一次
        setInterval(updateSystemTime, 1000);   // 1秒更新一次
    });
    
    // 更新系统统计信息
    function updateSystemStats() {
        // 获取系统状态
        fetch('/api/system/stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.data;
                    
                    // 更新服务状态
                    const statusElement = document.getElementById('serverStatus');
                    if (statusElement) {
                        const indicator = statusElement.querySelector('.status-indicator');
                        const text = statusElement.childNodes[statusElement.childNodes.length - 1];
                        
                        if (stats.online) {
                            indicator.className = 'status-indicator online';
                            text.textContent = '在线';
                        } else {
                            indicator.className = 'status-indicator offline';
                            text.textContent = '离线';
                        }
                    }
                    
                    // 更新平台特定统计
                    <% if (platformType === 'saas') { %>
                        updateElement('activeTenants', stats.activeTenants || '--');
                        updateElement('systemLoad', stats.systemLoad || '--');
                    <% } else { %>
                        updateElement('activeFlocks', stats.activeFlocks || '--');
                        updateElement('dataCount', stats.dataCount || '--');
                    <% } %>
                    
                    // 更新响应时间
                    updateElement('responseTime', `响应时间: ${stats.responseTime || '--'}ms`);
                }
            })
            .catch(error => {
                console.warn('获取系统统计失败:', error);
                
                // 设置离线状态
                const statusElement = document.getElementById('serverStatus');
                if (statusElement) {
                    const indicator = statusElement.querySelector('.status-indicator');
                    const text = statusElement.childNodes[statusElement.childNodes.length - 1];
                    indicator.className = 'status-indicator offline';
                    text.textContent = '离线';
                }
            });
    }
    
    // 更新系统时间
    function updateSystemTime() {
        // 更新运行时间
        const startTime = new Date('<%= new Date().toISOString() %>');
        const now = new Date();
        const uptimeMs = now - startTime;
        const uptimeText = formatUptime(uptimeMs);
        updateElement('systemUptime', `运行时间: ${uptimeText}`);
        
        // 更新最后更新时间
        updateElement('lastUpdate', `最后更新: ${formatTime(now)}`);
    }
    
    // 格式化运行时间
    function formatUptime(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) {
            return `${days}天 ${hours % 24}小时`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes % 60}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟 ${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }
    
    // 格式化时间
    function formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    // 更新元素内容
    function updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }
    
    // 页脚功能按钮
    function showSystemInfo() {
        fetch('/api/system/info')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const info = data.data;
                    const modal = createInfoModal('系统信息', formatSystemInfo(info));
                    document.body.appendChild(modal);
                    new bootstrap.Modal(modal).show();
                }
            })
            .catch(error => {
                console.error('获取系统信息失败:', error);
                showError('获取系统信息失败');
            });
    }
    
    function checkSystemHealth() {
        showInfo('正在检查系统健康状态...', { duration: 2000 });
        
        fetch('/api/system/health')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.healthy) {
                    showSuccess('系统运行正常');
                } else {
                    showWarning('系统存在一些问题，请检查日志');
                }
            })
            .catch(error => {
                console.error('健康检查失败:', error);
                showError('健康检查失败');
            });
    }
    
    function exportSystemReport() {
        const link = document.createElement('a');
        link.href = '/api/system/export-report';
        link.download = `system-report-${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showSuccess('系统报告导出已开始');
    }
    
    function showKeyboardShortcuts() {
        const shortcuts = [
            { key: 'Ctrl + K', description: '全局搜索' },
            { key: 'Ctrl + /', description: '显示快捷键' },
            { key: 'Ctrl + D', description: '返回仪表板' },
            { key: 'Ctrl + S', description: '保存当前表单' },
            { key: 'Ctrl + R', description: '刷新页面数据' },
            { key: 'Esc', description: '关闭模态框' }
        ];
        
        const content = shortcuts.map(shortcut => 
            `<div class="d-flex justify-content-between align-items-center mb-2">
                <span>${shortcut.description}</span>
                <kbd>${shortcut.key}</kbd>
            </div>`
        ).join('');
        
        const modal = createInfoModal('键盘快捷键', content);
        document.body.appendChild(modal);
        new bootstrap.Modal(modal).show();
    }
    
    // 创建信息模态框
    function createInfoModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        `;
        
        // 自动清理
        modal.addEventListener('hidden.bs.modal', function() {
            modal.remove();
        });
        
        return modal;
    }
    
    // 格式化系统信息
    function formatSystemInfo(info) {
        return `
            <div class="row">
                <div class="col-md-6">
                    <h6>系统信息</h6>
                    <p><strong>版本:</strong> ${info.version || 'v2.6.0'}</p>
                    <p><strong>环境:</strong> ${info.environment || '生产环境'}</p>
                    <p><strong>启动时间:</strong> ${info.startTime || '--'}</p>
                </div>
                <div class="col-md-6">
                    <h6>性能指标</h6>
                    <p><strong>内存使用:</strong> ${info.memoryUsage || '--'}</p>
                    <p><strong>CPU使用率:</strong> ${info.cpuUsage || '--'}</p>
                    <p><strong>磁盘使用:</strong> ${info.diskUsage || '--'}</p>
                </div>
            </div>
        `;
    }
</script>