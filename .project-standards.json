{"projectName": "智慧养鹅SAAS平台", "version": "2.9.2", "standards": {"enforceContext7Query": true, "requireSequentialThinking": true, "enforceConsistency": true, "mandatoryBestPractices": true}, "defaultRules": {"context7Integration": {"enabled": true, "libraries": ["/context7/developers_weixin_qq_com-miniprogram-dev-framework", "/context7/developers_weixin_qq_com-miniprogram-dev-api", "/tencent/tdesign-miniprogram"], "queryBefore": ["页面开发", "组件创建", "API设计", "数据库建模"]}, "sequentialThinking": {"enabled": true, "requiredFor": ["复杂功能设计", "架构决策", "问题分析", "重构规划"], "minThoughts": 3}, "consistencyCheck": {"enabled": true, "checkPoints": ["小程序页面与后端API一致性", "数据库模型与API接口一致性", "前端组件与设计规范一致性", "管理后台与业务逻辑一致性"]}}, "technicalStandards": {"wechatMiniProgram": {"framework": "原生微信小程序", "componentLibrary": "TDesign MiniProgram", "namingConvention": {"pages": "kebab-case", "components": "c-kebab-case", "utils": "camelCase"}, "bestPractices": ["遵循微信官方开发指南", "使用TDesign组件规范", "实施性能优化策略", "确保无障碍访问"]}, "backend": {"framework": "Express.js + Sequelize", "architecture": "RESTful API", "database": "MySQL 8.0", "standards": ["RESTful API设计规范", "统一错误处理", "JWT身份认证", "参数验证和数据过滤"]}, "database": {"engine": "MySQL 8.0", "namingConvention": "snake_case", "requirements": ["字段名使用下划线命名", "必须有适当的索引", "外键关系明确", "数据验证规则完整"]}, "adminPanel": {"framework": "Express + EJS", "requirements": ["与核心业务模型保持一致", "权限控制严格", "操作日志完整", "界面友好易用"]}}, "codeQuality": {"linting": {"enabled": true, "rules": "ESLint + <PERSON><PERSON><PERSON>"}, "testing": {"required": true, "coverage": {"minimum": 80, "backend": 85, "frontend": 70}}, "documentation": {"required": true, "types": ["API文档", "组件文档", "部署指南", "开发规范"]}}, "developmentWorkflow": {"preCommit": ["运行代码检查", "执行单元测试", "验证一致性", "更新文档"], "beforeMerge": ["代码审查", "集成测试", "性能测试", "安全扫描"]}, "enforcementLevel": "strict", "lastUpdated": "2024-01-20", "maintainers": ["Smart Goose Development Team"]}