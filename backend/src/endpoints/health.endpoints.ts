import { z } from "zod";
import { defaultEndpointsFactory } from "express-zod-api";
import {
  authMiddleware,
  tenantMiddleware,
} from "../middleware/auth.middleware";
import {
  GetHealthRecordsQuerySchema,
  CreateHealthRecordSchema,
  UpdateHealthRecordSchema,
  HealthRecordParamsSchema,
  HealthRecordSchema,
  HealthStatsSchema,
  HealthTrendSchema,
  type GetHealthRecordsInput,
  type GetHealthRecordsOutput,
  type GetHealthRecordByIdInput,
  type GetHealthRecordByIdOutput,
  type CreateHealthRecordInput,
  type CreateHealthRecordOutput,
  type UpdateHealthRecordInput,
  type UpdateHealthRecordOutput,
  type DeleteHealthRecordInput,
  type DeleteHealthRecordOutput,
  type GetHealthStatsInput,
  type GetHealthStatsOutput,
} from "../types/health.types";

// 模拟健康记录数据
const mockHealthRecords: Array<z.infer<typeof HealthRecordSchema>> = [
  {
    id: 1,
    flockId: 1,
    checkType: "routine" as const,
    checkDate: "2024-01-15T10:00:00.000Z",
    symptoms: "正常",
    temperature: 40.5,
    weight: 3.2,
    result: "healthy" as const,
    diagnosis: "健康状况良好",
    treatment: "无需治疗",
    medicine: undefined,
    dosage: undefined,
    notes: "常规检查，各项指标正常",
    checkerId: 1,
    createdAt: "2024-01-15T10:00:00.000Z",
    updatedAt: "2024-01-15T10:00:00.000Z",
    flockName: "一号鹅群",
    batchNumber: "FLOCK-1704067200000",
    checkerName: "测试用户",
  },
  {
    id: 2,
    flockId: 2,
    checkType: "disease" as const,
    checkDate: "2024-01-16T14:30:00.000Z",
    symptoms: "咳嗽、食欲不振",
    temperature: 41.8,
    weight: 2.8,
    result: "sick" as const,
    diagnosis: "疑似呼吸道感染",
    treatment: "抗生素治疗，隔离观察",
    medicine: "阿莫西林",
    dosage: "50mg/kg，每日两次",
    notes: "需要密切观察病情变化",
    checkerId: 1,
    createdAt: "2024-01-16T14:30:00.000Z",
    updatedAt: "2024-01-16T14:30:00.000Z",
    flockName: "二号鹅群",
    batchNumber: "FLOCK-1704153600000",
    checkerName: "测试用户",
  },
  {
    id: 3,
    flockId: 1,
    checkType: "treatment" as const,
    checkDate: "2024-01-20T09:00:00.000Z",
    symptoms: "恢复中",
    temperature: 40.2,
    weight: 3.0,
    result: "recovering" as const,
    diagnosis: "呼吸道感染恢复期",
    treatment: "继续观察，营养支持",
    medicine: "维生素C",
    dosage: "100mg/天",
    notes: "病情好转，食欲恢复",
    checkerId: 1,
    createdAt: "2024-01-20T09:00:00.000Z",
    updatedAt: "2024-01-20T09:00:00.000Z",
    flockName: "一号鹅群",
    batchNumber: "FLOCK-1704067200000",
    checkerName: "测试用户",
  },
];

// 获取健康记录列表端点
export const getHealthRecordsEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "get",
    input: GetHealthRecordsQuerySchema,
    output: z.object({
      items: z.array(HealthRecordSchema),
      pagination: z.object({
        page: z.number(),
        limit: z.number(),
        total: z.number(),
        pages: z.number(),
        hasNext: z.boolean(),
        hasPrev: z.boolean(),
      }),
    }),
    shortDescription: "获取健康记录列表",
    description: "获取当前租户的健康记录列表，支持分页、搜索和过滤",
    tag: "health",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { page, limit, flockId, status, checkType, sortBy, sortOrder } =
        input;

      logger.info(`用户 ${user.id} 查询健康记录列表`, { input, tenantId });

      // 过滤条件
      let filteredRecords = mockHealthRecords.filter((record) => {
        if (flockId && record.flockId !== flockId) return false;
        if (status && record.result !== status) return false;
        if (checkType && record.checkType !== checkType) return false;
        return true;
      });

      // 排序
      filteredRecords.sort((a, b) => {
        const aValue =
          sortBy === "checkDate"
            ? new Date(a.checkDate).getTime()
            : sortBy === "createdAt"
              ? new Date(a.createdAt).getTime()
              : sortBy === "result"
                ? a.result
                : a.checkType;
        const bValue =
          sortBy === "checkDate"
            ? new Date(b.checkDate).getTime()
            : sortBy === "createdAt"
              ? new Date(b.createdAt).getTime()
              : sortBy === "result"
                ? b.result
                : b.checkType;

        if (sortOrder === "DESC") {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });

      // 分页
      const total = filteredRecords.length;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const items = filteredRecords.slice(startIndex, endIndex);

      const pages = Math.ceil(total / limit);
      const hasNext = page < pages;
      const hasPrev = page > 1;

      return {
        items,
        pagination: {
          page,
          limit,
          total,
          pages,
          hasNext,
          hasPrev,
        },
      };
    },
  });

// 创建健康记录端点
export const createHealthRecordEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "post",
    input: CreateHealthRecordSchema,
    output: HealthRecordSchema,
    shortDescription: "创建健康记录",
    description: "为指定鹅群创建新的健康检查记录",
    tag: "health",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;

      logger.info(`用户 ${user.id} 创建健康记录`, { input, tenantId });

      const newId = Math.max(...mockHealthRecords.map((r) => r.id)) + 1;
      const now = new Date().toISOString();

      const newRecord = {
        id: newId,
        ...input,
        checkerId: user.id,
        createdAt: now,
        updatedAt: now,
        flockName: input.flockId === 1 ? "一号鹅群" : "二号鹅群",
        batchNumber:
          input.flockId === 1 ? "FLOCK-1704067200000" : "FLOCK-1704153600000",
        checkerName: user.username,
      };

      mockHealthRecords.push(newRecord);

      logger.info(`健康记录创建成功`, { recordId: newId });

      return newRecord;
    },
  });

// 获取健康记录详情端点
export const getHealthRecordByIdEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "get",
    input: HealthRecordParamsSchema,
    output: HealthRecordSchema,
    shortDescription: "获取健康记录详情",
    description: "根据ID获取特定健康记录的详细信息",
    tag: "health",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { id } = input;

      logger.info(`用户 ${user.id} 查询健康记录详情`, {
        recordId: id,
        tenantId,
      });

      const record = mockHealthRecords.find((r) => r.id === id);
      if (!record) {
        throw new Error(`健康记录 ${id} 不存在`);
      }

      return record;
    },
  });

// 更新健康记录端点
export const updateHealthRecordEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "patch",
    input: UpdateHealthRecordSchema.extend(HealthRecordParamsSchema.shape),
    output: HealthRecordSchema,
    shortDescription: "更新健康记录",
    description: "更新指定健康记录的信息",
    tag: "health",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { id, ...updateData } = input;

      logger.info(`用户 ${user.id} 更新健康记录`, {
        recordId: id,
        updateData,
        tenantId,
      });

      const recordIndex = mockHealthRecords.findIndex((r) => r.id === id);
      if (recordIndex === -1) {
        throw new Error(`健康记录 ${id} 不存在`);
      }

      const updatedRecord = {
        ...mockHealthRecords[recordIndex],
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      mockHealthRecords[recordIndex] = updatedRecord;

      logger.info(`健康记录更新成功`, { recordId: id });

      return updatedRecord;
    },
  });

// 删除健康记录端点
export const deleteHealthRecordEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "delete",
    input: HealthRecordParamsSchema,
    output: z.object({
      success: z.literal(true),
      message: z.string(),
    }),
    shortDescription: "删除健康记录",
    description: "删除指定的健康记录",
    tag: "health",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { id } = input;

      logger.info(`用户 ${user.id} 删除健康记录`, { recordId: id, tenantId });

      const recordIndex = mockHealthRecords.findIndex((r) => r.id === id);
      if (recordIndex === -1) {
        throw new Error(`健康记录 ${id} 不存在`);
      }

      mockHealthRecords.splice(recordIndex, 1);

      logger.info(`健康记录删除成功`, { recordId: id });

      return {
        success: true as const,
        message: `健康记录 ${id} 已成功删除`,
      };
    },
  });

// 获取健康统计端点
export const getHealthStatsEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "get",
    input: z.object({
      flockId: z.coerce.number().int().positive().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    }),
    output: z.object({
      stats: HealthStatsSchema,
      trend: z.array(HealthTrendSchema),
    }),
    shortDescription: "获取健康统计",
    description: "获取健康记录的统计信息和趋势数据",
    tag: "health",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { flockId, startDate, endDate } = input;

      logger.info(`用户 ${user.id} 查询健康统计`, { input, tenantId });

      // 过滤记录
      let records = mockHealthRecords;
      if (flockId) {
        records = records.filter((r) => r.flockId === flockId);
      }
      if (startDate) {
        records = records.filter(
          (r) => new Date(r.checkDate) >= new Date(startDate),
        );
      }
      if (endDate) {
        records = records.filter(
          (r) => new Date(r.checkDate) <= new Date(endDate),
        );
      }

      // 计算统计
      const totalRecords = records.length;
      const healthyCount = records.filter((r) => r.result === "healthy").length;
      const sickCount = records.filter((r) => r.result === "sick").length;
      const recoveringCount = records.filter(
        (r) => r.result === "recovering",
      ).length;
      const deadCount = records.filter((r) => r.result === "dead").length;
      const healthyRate =
        totalRecords > 0 ? (healthyCount / totalRecords) * 100 : 0;
      const lastCheckDate =
        records.length > 0
          ? records.sort(
              (a, b) =>
                new Date(b.checkDate).getTime() -
                new Date(a.checkDate).getTime(),
            )[0].checkDate
          : undefined;

      // 模拟趋势数据（最近7天）
      const trend = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split("T")[0];

        const dayRecords = records.filter((r) =>
          r.checkDate.startsWith(dateStr),
        );
        const dayTotal = dayRecords.length;
        const dayHealthy = dayRecords.filter(
          (r) => r.result === "healthy",
        ).length;
        const daySick = dayRecords.filter((r) => r.result === "sick").length;
        const dayHealthyRate = dayTotal > 0 ? (dayHealthy / dayTotal) * 100 : 0;

        return {
          date: dateStr,
          totalChecks: dayTotal,
          healthyCount: dayHealthy,
          sickCount: daySick,
          healthyRate: dayHealthyRate,
        };
      }).reverse();

      return {
        stats: {
          totalRecords,
          healthyCount,
          sickCount,
          recoveringCount,
          deadCount,
          healthyRate,
          lastCheckDate,
        },
        trend,
      };
    },
  });
