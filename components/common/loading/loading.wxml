<!-- 通用加载组件 -->
<view 
  wx:if="{{visible}}"
  class="c-loading c-loading--{{type}} c-loading--{{size}} {{text ? 'c-loading--text-' + textPosition : ''}} {{center ? 'c-loading--center' : ''}} {{overlay ? 'c-loading--overlay' : ''}} {{customClass}}"
  style="{{overlay ? 'background-color: ' + overlayColor + ';' : ''}} {{customStyle}}"
  bindtap="onOverlayTap"
>
  
  <!-- 加载内容容器 -->
      <view class="c-loading__content" catchtap="stopPropagation">
    
    <!-- 旋转加载器 -->
    <view wx:if="{{type === 'spinner'}}" class="c-loading__spinner" style="{{loaderStyle}}">
      <image class="c-loading__spinner-icon" src="{{loadingIcon}}" mode="aspectFit"></image>
    </view>
    
    <!-- 点状加载器 -->
    <view wx:elif="{{type === 'dots'}}" class="c-loading__dots" style="{{loaderStyle}}">
      <view class="c-loading__dot"></view>
      <view class="c-loading__dot"></view>
      <view class="c-loading__dot"></view>
    </view>
    
    <!-- 条状加载器 -->
    <view wx:elif="{{type === 'bars'}}" class="c-loading__bars" style="{{loaderStyle}}">
      <view class="c-loading__bar"></view>
      <view class="c-loading__bar"></view>
      <view class="c-loading__bar"></view>
      <view class="c-loading__bar"></view>
    </view>
    
    <!-- 骨架屏 -->
    <view wx:elif="{{type === 'skeleton'}}" class="c-loading__skeleton">
      <!-- 头像骨架 -->
      <view wx:if="{{skeletonAvatar}}" class="c-loading__skeleton-avatar"></view>
      
      <!-- 内容骨架 -->
      <view class="c-loading__skeleton-content">
        <view 
          wx:for="{{skeletonWidths}}" 
          wx:key="index"
          class="c-loading__skeleton-line"
          style="width: {{item}}"
        ></view>
      </view>
    </view>
    
    <!-- 自定义加载器 -->
    <view wx:elif="{{type === 'custom'}}" class="c-loading__custom">
      <slot name="loader"></slot>
    </view>
    
    <!-- 加载文本 -->
    <view wx:if="{{text}}" class="c-loading__text">
      <text>{{text}}</text>
    </view>
    
  </view>
  
</view>