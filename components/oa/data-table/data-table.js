// components/oa/data-table/data-table.js

/**
 * OA数据表格组件
 * 支持排序、筛选、分页的数据表格
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 表格数据
    data: {
      type: Array,
      value: []
    },
    // 列配置
    columns: {
      type: Array,
      value: []
    },
    // 是否显示边框
    bordered: {
      type: Boolean,
      value: true
    },
    // 是否显示斑马纹
    striped: {
      type: Boolean,
      value: false
    },
    // 是否可选择行
    selectable: {
      type: Boolean,
      value: false
    },
    // 选中的行
    selectedRows: {
      type: Array,
      value: []
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 空数据提示
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      value: false
    },
    // 分页配置
    pagination: {
      type: Object,
      value: {
        page: 1,
        limit: 10,
        total: 0
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 排序状态
    sortColumn: '',
    sortOrder: '', // asc/desc
    // 全选状态
    selectAll: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 表头点击事件（排序）
     */
    onHeaderTap(e) {
      const { column } = e.currentTarget.dataset;
      if (!column.sortable) return;

      let sortOrder = 'asc';
      if (this.data.sortColumn === column.key) {
        if (this.data.sortOrder === 'asc') {
          sortOrder = 'desc';
        } else if (this.data.sortOrder === 'desc') {
          sortOrder = '';
        }
      }

      this.setData({
        sortColumn: sortOrder ? column.key : '',
        sortOrder
      });

      this.triggerEvent('sort', {
        column: column.key,
        order: sortOrder
      });
    },

    /**
     * 行点击事件
     */
    onRowTap(e) {
      const { index } = e.currentTarget.dataset;
      const row = this.properties.data[index];
      
      this.triggerEvent('rowClick', {
        row,
        index
      });
    },

    /**
     * 全选/取消全选
     */
    onSelectAll() {
      const selectAll = !this.data.selectAll;
      const selectedRows = selectAll ? [...this.properties.data] : [];
      
      this.setData({ selectAll });
      
      this.triggerEvent('selectionChange', {
        selectedRows,
        selectAll
      });
    },

    /**
     * 行选择
     */
    onRowSelect(e) {
      const { index } = e.currentTarget.dataset;
      const row = this.properties.data[index];
      const selectedRows = [...this.properties.selectedRows];
      
      const existIndex = selectedRows.findIndex(item => 
        this.getRowKey(item) === this.getRowKey(row)
      );
      
      if (existIndex > -1) {
        selectedRows.splice(existIndex, 1);
      } else {
        selectedRows.push(row);
      }
      
      const selectAll = selectedRows.length === this.properties.data.length;
      this.setData({ selectAll });
      
      this.triggerEvent('selectionChange', {
        selectedRows,
        selectAll
      });
    },

    /**
     * 获取行唯一标识
     */
    getRowKey(row) {
      return row.id || row.key || JSON.stringify(row);
    },

    /**
     * 检查行是否被选中
     */
    isRowSelected(row) {
      return this.properties.selectedRows.some(item => 
        this.getRowKey(item) === this.getRowKey(row)
      );
    },

    /**
     * 获取单元格值
     */
    getCellValue(row, column) {
      if (column.render) {
        return column.render(row[column.key], row);
      }
      return row[column.key] || '';
    },

    /**
     * 获取排序图标
     */
    getSortIcon(column) {
      if (!column.sortable) return '';
      
      if (this.data.sortColumn !== column.key) {
        return '↕';
      }
      
      return this.data.sortOrder === 'asc' ? '↑' : '↓';
    },

    /**
     * 分页改变事件
     */
    onPageChange(e) {
      const { page } = e.detail;
      this.triggerEvent('pageChange', { page });
    },

    /**
     * 格式化数据
     */
    formatCellData(value, column) {
      if (!value && value !== 0) return '';

      switch (column.type) {
        case 'currency':
          return `¥${parseFloat(value).toFixed(2)}`;
        case 'date':
          return new Date(value).toLocaleDateString();
        case 'datetime':
          return new Date(value).toLocaleString();
        case 'status':
          return this.getStatusDisplay(value, column.statusMap);
        default:
          return value;
      }
    },

    /**
     * 获取状态显示
     */
    getStatusDisplay(status, statusMap) {
      if (!statusMap) return status;
      const statusConfig = statusMap[status];
      return statusConfig ? statusConfig.label : status;
    },

    /**
     * 获取状态样式
     */
    getStatusStyle(status, statusMap) {
      if (!statusMap) return '';
      const statusConfig = statusMap[status];
      return statusConfig ? `color: ${statusConfig.color}` : '';
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化全选状态
      const selectAll = this.properties.selectedRows.length === this.properties.data.length && this.properties.data.length > 0;
      this.setData({ selectAll });
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'selectedRows, data': function(selectedRows, data) {
      const selectAll = selectedRows.length === data.length && data.length > 0;
      this.setData({ selectAll });
    }
  },

  methods: {
    // ... 其他方法 ...

    /**
     * 阻止事件冒泡的通用方法
     */
    stopPropagation(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
    }
  }
});
