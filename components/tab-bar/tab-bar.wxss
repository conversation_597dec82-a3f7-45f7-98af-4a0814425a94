/* components/tab-bar/tab-bar.wxss */
.tab-bar {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  transition: all 0.3s ease;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #1890ff;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #1890ff;
  border-radius: 2rpx;
}