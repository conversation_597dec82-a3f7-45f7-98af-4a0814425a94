#!/usr/bin/env node
// backend/scripts/migrate-field-names.js
// 字段命名规范迁移脚本

const sequelize = require("../config/database");
const { QueryInterface } = require("sequelize");

class FieldNameMigration {
  constructor() {
    this.queryInterface = sequelize.getQueryInterface();
  }

  /**
   * 执行字段命名迁移
   */
  async migrate() {
    console.log("🚀 开始执行字段命名规范迁移...");

    try {
      // 1. 备份数据库
      await this.backupDatabase();

      // 2. 迁移用户表字段
      await this.migrateUserFields();

      // 3. 迁移健康记录表字段
      await this.migrateHealthRecordFields();

      // 4. 迁移生产记录表字段
      await this.migrateProductionRecordFields();

      // 5. 迁移库存表字段
      await this.migrateInventoryFields();

      // 6. 迁移公告表字段
      await this.migrateAnnouncementFields();

      // 7. 迁移知识库表字段
      await this.migrateKnowledgeBaseFields();

      // 8. 验证迁移结果
      await this.validateMigration();

      console.log("✅ 字段命名规范迁移完成！");
    } catch (error) {
      console.error("❌ 迁移失败:", error);
      await this.rollback();
      throw error;
    }
  }

  /**
   * 备份数据库
   */
  async backupDatabase() {
    console.log("📦 创建数据库备份...");

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const backupName = `zhihuiyange_backup_${timestamp}`;

    // 这里可以添加数据库备份逻辑
    console.log(`备份名称: ${backupName}`);
  }

  /**
   * 迁移用户表字段
   */
  async migrateUserFields() {
    console.log("👤 迁移用户表字段...");

    const tableName = "users";

    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      console.log(`表 ${tableName} 不存在，跳过迁移`);
      return;
    }

    // 添加新字段（如果不存在）
    try {
      await this.queryInterface.addColumn(tableName, "farm_name", {
        type: sequelize.Sequelize.STRING(100),
        allowNull: true,
        comment: "农场名称",
      });
      console.log("✓ 添加 farm_name 字段");
    } catch (error) {
      if (!error.message.includes("Duplicate column name")) {
        throw error;
      }
    }

    try {
      await this.queryInterface.addColumn(tableName, "phone", {
        type: sequelize.Sequelize.STRING(20),
        allowNull: true,
        comment: "联系电话",
      });
      console.log("✓ 添加 phone 字段");
    } catch (error) {
      if (!error.message.includes("Duplicate column name")) {
        throw error;
      }
    }
  }

  /**
   * 迁移健康记录表字段
   */
  async migrateHealthRecordFields() {
    console.log("🏥 迁移健康记录表字段...");

    const tableName = "health_records";

    // 检查并添加健康状态字段
    try {
      await this.queryInterface.addColumn(tableName, "health_status", {
        type: sequelize.Sequelize.ENUM("healthy", "sick", "recovering", "dead"),
        allowNull: true,
        defaultValue: "healthy",
        comment: "健康状态",
      });
      console.log("✓ 添加 health_status 字段");
    } catch (error) {
      if (!error.message.includes("Duplicate column name")) {
        console.log("health_status 字段可能已存在");
      }
    }

    // 更新现有记录的健康状态
    await sequelize.query(`
      UPDATE ${tableName} 
      SET health_status = CASE 
        WHEN status = 'completed' THEN 'healthy'
        WHEN status = 'processing' THEN 'sick'
        ELSE 'healthy'
      END
      WHERE health_status IS NULL
    `);

    console.log("✓ 更新健康状态数据");
  }

  /**
   * 迁移生产记录表字段
   */
  async migrateProductionRecordFields() {
    console.log("📊 迁移生产记录表字段...");

    const tableName = "production_records";

    // 添加批次号字段
    try {
      await this.queryInterface.addColumn(tableName, "batch_number", {
        type: sequelize.Sequelize.STRING(50),
        allowNull: true,
        comment: "批次号",
      });
      console.log("✓ 添加 batch_number 字段");
    } catch (error) {
      if (!error.message.includes("Duplicate column name")) {
        console.log("batch_number 字段可能已存在");
      }
    }

    // 为现有记录生成批次号
    await sequelize.query(`
      UPDATE ${tableName} 
      SET batch_number = CONCAT('BATCH_', DATE_FORMAT(recorded_date, '%Y%m%d'), '_', id)
      WHERE batch_number IS NULL
    `);

    console.log("✓ 生成批次号数据");
  }

  /**
   * 迁移库存表字段
   */
  async migrateInventoryFields() {
    console.log("📦 迁移库存表字段...");

    const tableName = "inventory_records";

    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      console.log(`表 ${tableName} 不存在，跳过迁移`);
      return;
    }

    // 添加供应商字段
    try {
      await this.queryInterface.addColumn(tableName, "supplier", {
        type: sequelize.Sequelize.STRING(100),
        allowNull: true,
        comment: "供应商",
      });
      console.log("✓ 添加 supplier 字段");
    } catch (error) {
      if (!error.message.includes("Duplicate column name")) {
        console.log("supplier 字段可能已存在");
      }
    }
  }

  /**
   * 迁移公告表字段
   */
  async migrateAnnouncementFields() {
    console.log("📢 迁移公告表字段...");

    const tableName = "announcements";

    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      console.log(`表 ${tableName} 不存在，跳过迁移`);
      return;
    }

    // 添加发布状态字段
    try {
      await this.queryInterface.addColumn(tableName, "publish_status", {
        type: sequelize.Sequelize.ENUM("draft", "published", "archived"),
        allowNull: false,
        defaultValue: "published",
        comment: "发布状态",
      });
      console.log("✓ 添加 publish_status 字段");
    } catch (error) {
      if (!error.message.includes("Duplicate column name")) {
        console.log("publish_status 字段可能已存在");
      }
    }
  }

  /**
   * 迁移知识库表字段
   */
  async migrateKnowledgeBaseFields() {
    console.log("📚 迁移知识库表字段...");

    const tableName = "knowledge_base";

    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      console.log(`表 ${tableName} 不存在，跳过迁移`);
      return;
    }

    // 添加浏览次数字段
    try {
      await this.queryInterface.addColumn(tableName, "view_count", {
        type: sequelize.Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "浏览次数",
      });
      console.log("✓ 添加 view_count 字段");
    } catch (error) {
      if (!error.message.includes("Duplicate column name")) {
        console.log("view_count 字段可能已存在");
      }
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log("🔍 验证迁移结果...");

    const tables = ["users", "health_records", "production_records"];

    for (const table of tables) {
      try {
        const columns = await this.queryInterface.describeTable(table);
        console.log(
          `✓ 表 ${table} 字段验证通过，共 ${Object.keys(columns).length} 个字段`,
        );
      } catch (error) {
        console.log(`⚠️ 表 ${table} 验证失败:`, error.message);
      }
    }
  }

  /**
   * 回滚迁移
   */
  async rollback() {
    console.log("🔄 执行回滚操作...");
    // 这里可以添加回滚逻辑
    console.log("回滚操作需要手动执行数据库恢复");
  }
}

// 执行迁移
async function main() {
  const migration = new FieldNameMigration();

  try {
    await sequelize.authenticate();
    console.log("数据库连接成功");

    await migration.migrate();
  } catch (error) {
    console.error("迁移失败:", error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FieldNameMigration;
