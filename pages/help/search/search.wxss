/* 帮助搜索页面样式 */
.search-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 40rpx;
}

/* 搜索头部 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 16rpx;
  margin-right: 16rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  font-size: 32rpx;
  color: #999;
  padding: 8rpx;
  margin-left: 8rpx;
}

.search-btn {
  width: 120rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.search-btn:active {
  transform: scale(0.95);
}

/* 搜索建议 */
.suggestions-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.suggestions-header {
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.suggestions-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  transition: background 0.3s ease;
}

.suggestion-item:active {
  background: #f8f9fa;
}

.suggestion-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 12rpx;
}

.suggestion-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

/* 搜索历史 */
.history-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.history-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.clear-history {
  font-size: 24rpx;
  color: #999;
}

.history-list {
  display: flex;
  flex-direction: column;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  transition: background 0.3s ease;
}

.history-item:active {
  background: #f8f9fa;
}

.history-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 12rpx;
}

.history-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.delete-btn {
  font-size: 28rpx;
  color: #999;
  padding: 8rpx;
  margin-left: 8rpx;
}

/* 热门搜索 */
.hot-search-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.hot-search-header {
  margin-bottom: 24rpx;
}

.hot-search-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.hot-search-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.hot-tag {
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #007AFF;
  border-radius: 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  border: 1rpx solid #e3f2fd;
}

.hot-tag:active {
  background: #007AFF;
  color: #ffffff;
  transform: scale(0.95);
}

/* 搜索结果 */
.results-section {
  margin: 20rpx;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.results-count {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: #ffffff;
  padding: 16rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  gap: 16rpx;
  overflow-x: auto;
}

.filter-tab {
  flex-shrink: 0;
  padding: 8rpx 16rpx;
  background: #f8f9fa;
  color: #666;
  border-radius: 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  border: 1rpx solid #e9ecef;
}

.filter-tab.active {
  background: #007AFF;
  color: #ffffff;
  border-color: #007AFF;
}

/* 结果列表 */
.results-list {
  background: #ffffff;
  border-radius: 0 0 16rpx 16rpx;
  overflow: hidden;
}

.result-group {
  border-bottom: 1rpx solid #f0f0f0;
}

.result-group:last-child {
  border-bottom: none;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
}

.group-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

/* 文章结果 */
.article-results {
  padding: 16rpx 0;
}

.article-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  transition: background 0.3s ease;
}

.article-item:active {
  background: #f8f9fa;
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.article-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.article-summary {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-meta {
  display: flex;
  gap: 12rpx;
  align-items: center;
  margin-top: 8rpx;
}

.category-tag {
  padding: 4rpx 8rpx;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.meta-text {
  font-size: 20rpx;
  color: #999;
}

.article-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 问题结果 */
.question-results {
  padding: 16rpx 0;
}

.question-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.question-item:last-child {
  border-bottom: none;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  transition: background 0.3s ease;
}

.question-header:active {
  background: #f8f9fa;
}

.question-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.expand-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.question-answer {
  padding: 0 24rpx 20rpx;
  background: #f8f9fa;
}

.answer-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 分类结果 */
.category-results {
  padding: 16rpx 0;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  transition: background 0.3s ease;
}

.category-item:active {
  background: #f8f9fa;
}

.category-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.category-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.category-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.category-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 无结果状态 */
.no-results {
  text-align: center;
  padding: 60rpx 40rpx;
  background: #ffffff;
  border-radius: 0 0 16rpx 16rpx;
}

.no-results-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
}

.no-results-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.no-results-tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
  display: block;
}

.suggestions-tips {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 32rpx;
  text-align: left;
}

.tip-item {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.no-results-actions {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.feedback-btn,
.contact-btn {
  flex: 1;
  max-width: 200rpx;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: none;
}

.feedback-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: #ffffff;
}

.contact-btn {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: #ffffff;
}

.feedback-btn:active,
.contact-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 24rpx;
}

/* 快速入口 */
.quick-access {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.quick-access-header {
  margin-bottom: 24rpx;
}

.quick-access-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.quick-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.quick-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.quick-title {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .filter-tabs {
    padding: 12rpx 16rpx;
    gap: 12rpx;
  }

  .quick-access-grid {
    grid-template-columns: 1fr;
  }

  .no-results-actions {
    flex-direction: column;
  }

  .search-header {
    padding: 16rpx;
  }
}

/* 动画效果 */
.search-header {
  animation: slideInDown 0.4s ease-out;
}

.suggestions-section,
.history-section,
.hot-search-section,
.results-section,
.quick-access {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}