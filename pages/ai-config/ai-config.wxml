<!-- pages/ai-config/ai-config.wxml -->
<view class="ai-config-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">AI服务配置</text>
    <text class="page-subtitle">配置您的AI API密钥以启用智能功能</text>
  </view>

  <!-- 配置状态概览 -->
  <view class="config-overview">
    <view class="overview-title">配置状态</view>
    <view class="provider-status">
      <block wx:for="{{providerStatus}}" wx:key="provider">
        <view class="status-item">
          <view class="status-info">
            <text class="provider-name">{{item.name}}</text>
            <text class="provider-desc">{{item.description}}</text>
          </view>
          <view class="status-indicator {{item.configured ? 'configured' : 'not-configured'}}">
            <text class="status-text">{{item.configured ? '已配置' : '未配置'}}</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- AI服务提供商配置 -->
  <view class="providers-config">
    <view class="section-title">服务提供商</view>
    
    <block wx:for="{{providers}}" wx:key="key">
      <view class="provider-card">
        <view class="provider-header">
          <view class="provider-info">
            <text class="provider-title">{{item.name}}</text>
            <text class="provider-subtitle">{{item.description}}</text>
          </view>
          <view class="provider-actions">
            <button class="config-btn" bindtap="onConfigProvider" data-provider="{{item.key}}">
              {{item.configured ? '重新配置' : '配置'}}
            </button>
          </view>
        </view>
        
        <view class="provider-details">
          <view class="detail-item">
            <text class="detail-label">官网:</text>
            <text class="detail-value">{{item.website}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">密钥格式:</text>
            <text class="detail-value">{{item.keyFormat}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">支持功能:</text>
            <text class="detail-value">{{item.featuresText}}</text>
          </view>
          <view wx:if="{{item.configured}}" class="detail-item">
            <text class="detail-label">当前密钥:</text>
            <text class="detail-value masked-key">{{item.maskedKey}}</text>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 推荐配置 -->
  <view class="recommendation">
    <view class="recommendation-header">
      <text class="recommendation-title">推荐配置</text>
    </view>
    <view class="recommendation-content">
      <text class="recommendation-text">
        为了获得最佳的AI识别效果，建议优先配置智谱AI或硅基流动的API密钥。
        这两个服务都提供免费额度，足够日常使用。
      </text>
      <view class="recommendation-steps">
        <view class="step-item">
          <text class="step-number">1</text>
          <text class="step-text">访问服务商官网注册账号</text>
        </view>
        <view class="step-item">
          <text class="step-number">2</text>
          <text class="step-text">在控制台创建API密钥</text>
        </view>
        <view class="step-item">
          <text class="step-number">3</text>
          <text class="step-text">复制密钥并在此处配置</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="onTestAllKeys">测试所有密钥</button>
    <button class="action-btn danger" bindtap="onClearAllKeys">清除所有配置</button>
  </view>
</view>

<!-- 配置弹窗 -->
<view wx:if="{{showConfigModal}}" class="config-modal">
  <view class="modal-mask" bindtap="onCloseModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">配置{{currentProvider.name}}</text>
      <button class="close-btn" bindtap="onCloseModal">×</button>
    </view>
    
    <view class="modal-body">
      <view class="form-item">
        <text class="form-label">API密钥</text>
        <input 
          class="form-input" 
          placeholder="请输入{{currentProvider.keyFormat}}格式的密钥" 
          value="{{inputApiKey}}" 
          bindinput="onApiKeyInput"
          password="{{!showApiKey}}"
        />
        <button class="toggle-btn" bindtap="onToggleShowKey">
          {{showApiKey ? '隐藏' : '显示'}}
        </button>
      </view>
      
      <view class="form-tips">
        <text class="tips-title">获取密钥步骤:</text>
        <text class="tips-text">1. 访问 {{currentProvider.website}}</text>
        <text class="tips-text">2. 注册并登录账号</text>
        <text class="tips-text">3. 在控制台或API管理页面创建密钥</text>
        <text class="tips-text">4. 复制密钥并粘贴到上方输入框</text>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn secondary" bindtap="onCloseModal">取消</button>
      <button class="modal-btn primary" bindtap="onSaveApiKey" disabled="{{!inputApiKey}}">
        保存
      </button>
    </view>
  </view>
</view>

<!-- 加载提示 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>
