/**
 * 小程序多租户配置工具
 * Mini Program Multi-tenant Configuration Utility
 */

// 租户配置缓存
let tenantConfig = null;
let configLoadTime = null;
const CONFIG_CACHE_TIME = 5 * 60 * 1000; // 5分钟缓存

/**
 * 获取租户配置
 * @returns {Object} 租户配置信息
 */
function getTenantConfig() {
  // 检查缓存是否有效
  if (tenantConfig && configLoadTime && (Date.now() - configLoadTime < CONFIG_CACHE_TIME)) {
    return tenantConfig;
  }
  
  // 从本地存储获取租户配置
  try {
    const storedConfig = wx.getStorageSync('tenant_config');
    if (storedConfig) {
      tenantConfig = storedConfig;
      configLoadTime = Date.now();
      return tenantConfig;
    }
  } catch (error) {
    try { const logger = require('./logger.js'); logger.error && logger.error('获取租户配置失败', error); } catch(_) {}
  }
  
  // 返回默认配置
  return getDefaultConfig();
}

/**
 * 设置租户配置
 * @param {Object} config 租户配置
 */
function setTenantConfig(config) {
  tenantConfig = config;
  configLoadTime = Date.now();
  
  try {
    wx.setStorageSync('tenant_config', config);
  } catch (error) {
    try { const logger = require('./logger.js'); logger.error && logger.error('保存租户配置失败', error); } catch(_) {}
  }
}

/**
 * 获取默认配置
 * @returns {Object} 默认配置
 */
function getDefaultConfig() {
  return {
    tenantCode: 'DEMO001',
    apiBaseUrl: 'http://localhost:3001/api/v1/tenant',
    appName: '智慧养鹅',
    theme: {
      primaryColor: '#1890ff',
      secondaryColor: '#52c41a'
    },
    features: {
      healthManagement: true,
      productionManagement: true,
      shopModule: false,
      aiDiagnosis: false
    }
  };
}

/**
 * 从服务器加载租户配置
 * @param {string} tenantCode 租户代码
 * @returns {Promise<Object>} 租户配置
 */
async function loadTenantConfigFromServer(tenantCode) {
  try {
    const { get } = require('./request.js');
    const response = await get(`${getTenantConfig().apiBaseUrl}/config/features`, {});
    
    const isSuccess = (response && (response.success === true || response.status === 'success'));
    if (isSuccess) {
      const serverConfig = response.data || response;
      
      // 合并服务器配置和本地配置
      const mergedConfig = {
        tenantCode: serverConfig.tenantCode,
        subscriptionPlan: serverConfig.subscriptionPlan,
        features: serverConfig.features,
        limits: serverConfig.limits,
        apiBaseUrl: getTenantConfig().apiBaseUrl,
        appName: `智慧养鹅 - ${serverConfig.tenantCode}`,
        theme: getTenantConfig().theme
      };
      
      setTenantConfig(mergedConfig);
      return mergedConfig;
    }
  } catch (error) {
    try { const logger = require('./logger.js'); logger.error && logger.error('从服务器加载租户配置失败', error); } catch(_) {}
  }
  
  return getTenantConfig();
}

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
function getApiBaseUrl() {
  const config = getTenantConfig();
  return config.apiBaseUrl;
}

/**
 * 获取租户代码
 * @returns {string} 租户代码
 */
function getTenantCode() {
  const config = getTenantConfig();
  return config.tenantCode;
}

/**
 * 检查功能是否可用
 * @param {string} featureName 功能名称
 * @returns {boolean} 功能是否可用
 */
function isFeatureEnabled(featureName) {
  const config = getTenantConfig();
  if (!config.features) return false;
  
  // 检查订阅计划限制
  const planFeatures = {
    trial: ['basic_management'],
    basic: ['basic_management', 'data_export'],
    standard: ['basic_management', 'data_export', 'health_management', 'production_management'],
    premium: ['basic_management', 'data_export', 'health_management', 'production_management', 'shop_module', 'ai_diagnosis'],
    enterprise: ['all_features']
  };
  
  const currentPlan = config.subscriptionPlan || 'trial';
  const allowedFeatures = planFeatures[currentPlan] || [];
  
  if (currentPlan === 'enterprise' || allowedFeatures.includes('all_features')) {
    return true;
  }
  
  return allowedFeatures.includes(featureName) || config.features.includes(featureName);
}

/**
 * 获取主题配置
 * @returns {Object} 主题配置
 */
function getThemeConfig() {
  const config = getTenantConfig();
  return config.theme || {
    primaryColor: '#1890ff',
    secondaryColor: '#52c41a'
  };
}

/**
 * 初始化租户配置
 * @param {string} tenantCode 租户代码（可选）
 * @returns {Promise<Object>} 初始化后的配置
 */
async function initTenantConfig(tenantCode) {
  if (tenantCode) {
    // 如果提供了租户代码，更新本地配置
    const currentConfig = getTenantConfig();
    const updatedConfig = {
      ...currentConfig,
      tenantCode: tenantCode
    };
    setTenantConfig(updatedConfig);
  }
  
  // 从服务器加载最新配置
  return await loadTenantConfigFromServer(getTenantCode());
}

/**
 * 清除租户配置缓存
 */
function clearTenantConfig() {
  tenantConfig = null;
  configLoadTime = null;
  
  try {
    wx.removeStorageSync('tenant_config');
  } catch (error) {
    try { const logger = require('./logger.js'); logger.error && logger.error('清除租户配置失败', error); } catch(_) {}
  }
}

module.exports = {
  getTenantConfig,
  setTenantConfig,
  getDefaultConfig,
  loadTenantConfigFromServer,
  getApiBaseUrl,
  getTenantCode,
  isFeatureEnabled,
  getThemeConfig,
  initTenantConfig,
  clearTenantConfig
};