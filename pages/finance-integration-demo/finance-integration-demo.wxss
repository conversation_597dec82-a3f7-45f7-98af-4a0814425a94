/* 财务集成演示页面样式 */

.finance-integration-demo {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 财务概览卡片 */
.overview-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.profit-positive .stat-value {
  color: #4CAF50;
}

.profit-negative .stat-value {
  color: #ff4757;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #4CAF50;
  color: white;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-name {
  font-size: 22rpx;
}

/* 标签页内容 */
.tab-content {
  margin: 0 20rpx 20rpx;
}

.tab-panel {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.panel-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.panel-subtitle {
  font-size: 24rpx;
  color: #666;
}

/* 添加按钮组 */
.add-buttons {
  display: flex;
  gap: 10rpx;
}

.add-btn {
  padding: 12rpx 20rpx;
  background: #4CAF50;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
}

.add-btn:active {
  background: #45a049;
}

/* 批次列表 */
.batch-list {
  max-height: 600rpx;
}

.batch-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.batch-item:active {
  background: #f8f9fa;
}

.batch-item.selected {
  background: #e8f5e8;
  border-left: 6rpx solid #4CAF50;
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.batch-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.batch-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: white;
}

.status-active {
  background: #4CAF50;
}

.status-inactive {
  background: #999;
}

.batch-info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.batch-finance {
  display: flex;
  justify-content: space-between;
}

.finance-item {
  text-align: center;
}

.finance-label {
  display: block;
  font-size: 20rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.finance-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.finance-item.profit-positive .finance-value {
  color: #4CAF50;
}

.finance-item.profit-negative .finance-value {
  color: #ff4757;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.filter-tab {
  padding: 12rpx 24rpx;
  background: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  border: 2rpx solid #eee;
  transition: all 0.2s ease;
}

.filter-tab.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

/* 记录列表 */
.record-list {
  max-height: 500rpx;
}

.record-item {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  border-left: 6rpx solid transparent;
}

.record-entry {
  border-left-color: #ff6b6b;
}

.record-weight {
  border-left-color: #4ecdc4;
}

.record-sale {
  border-left-color: #45b7d1;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.record-type {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.record-date {
  font-size: 22rpx;
  color: #999;
}

.record-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.supplier, .buyer {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.finance-link {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
  font-size: 22rpx;
  color: #4CAF50;
}

.link-icon {
  margin-right: 8rpx;
}

/* 图表区域 */
.chart-section, .category-section {
  margin: 30rpx;
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-title, .category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.chart-placeholder {
  height: 300rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8rpx;
  color: #999;
}

.chart-note {
  font-size: 22rpx;
  margin-top: 10rpx;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.category-name {
  font-size: 26rpx;
  color: #333;
}

.category-type {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: white;
}

.type-income {
  background: #4CAF50;
}

.type-expense {
  background: #ff4757;
}

.category-amount {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  max-height: 60vh;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
}

.form-input:focus {
  border-color: #4CAF50;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #eee;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #eee;
}

.modal-btn.confirm {
  background: #4CAF50;
  color: white;
}

.modal-btn:active {
  opacity: 0.8;
}
