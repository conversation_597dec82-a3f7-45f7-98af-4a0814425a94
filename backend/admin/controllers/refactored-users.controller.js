/**
 * 重构后的用户控制器
 * 使用BaseController来减少重复代码
 */

const BaseController = require("./base.controller");
const { validateParams } = require("../utils/response-helper");

class UsersController extends BaseController {
  constructor() {
    super("用户", "/auth/users");
  }

  /**
   * 验证创建用户数据
   */
  validateCreateData(data) {
    const rules = {
      username: {
        required: true,
        type: "string",
        minLength: 3,
        maxLength: 50,
        label: "用户名",
      },
      email: {
        required: true,
        type: "email",
        label: "邮箱",
      },
      password: {
        required: true,
        type: "string",
        minLength: 6,
        maxLength: 100,
        label: "密码",
      },
      role: {
        required: true,
        type: "string",
        label: "角色",
        validator: (value) => {
          const validRoles = ["admin", "manager", "user"];
          return validRoles.includes(value)
            ? null
            : "角色必须是admin、manager或user之一";
        },
      },
      name: {
        required: false,
        type: "string",
        maxLength: 100,
        label: "姓名",
      },
      phone: {
        required: false,
        type: "string",
        label: "手机号",
        validator: (value) => {
          if (!value) return null;
          return /^1[3-9]\d{9}$/.test(value) ? null : "手机号格式不正确";
        },
      },
    };

    return validateParams(data, rules);
  }

  /**
   * 验证更新用户数据
   */
  validateUpdateData(data) {
    const rules = {
      username: {
        required: false,
        type: "string",
        minLength: 3,
        maxLength: 50,
        label: "用户名",
      },
      email: {
        required: false,
        type: "email",
        label: "邮箱",
      },
      role: {
        required: false,
        type: "string",
        label: "角色",
        validator: (value) => {
          if (!value) return null;
          const validRoles = ["admin", "manager", "user"];
          return validRoles.includes(value)
            ? null
            : "角色必须是admin、manager或user之一";
        },
      },
      name: {
        required: false,
        type: "string",
        maxLength: 100,
        label: "姓名",
      },
      phone: {
        required: false,
        type: "string",
        label: "手机号",
        validator: (value) => {
          if (!value) return null;
          return /^1[3-9]\d{9}$/.test(value) ? null : "手机号格式不正确";
        },
      },
      status: {
        required: false,
        type: "string",
        label: "状态",
        validator: (value) => {
          if (!value) return null;
          const validStatuses = ["active", "inactive", "suspended"];
          return validStatuses.includes(value)
            ? null
            : "状态必须是active、inactive或suspended之一";
        },
      },
    };

    return validateParams(data, rules);
  }

  /**
   * 重置用户密码
   */
  async resetPassword(req, res) {
    const {
      successResponse,
      errorResponse,
    } = require("../utils/response-helper");

    try {
      const { id } = req.params;
      const { newPassword } = req.body;

      if (!id) {
        return errorResponse(res, "用户ID缺失", 400);
      }

      if (!newPassword) {
        return errorResponse(res, "新密码不能为空", 400);
      }

      if (newPassword.length < 6) {
        return errorResponse(res, "密码长度不能少于6位", 400);
      }

      // 调用API重置密码
      const apiService = require("../utils/apiService");
      const result = await apiService.post(
        `/auth/users/${id}/reset-password`,
        req.session.token,
        {
          newPassword,
        },
      );

      if (result.success) {
        successResponse(res, null, "密码重置成功");
      } else {
        errorResponse(res, result.message || "密码重置失败");
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("重置密码失败:", error); } catch(_) {}

      errorResponse(res, "重置密码失败", 500);
    }
  }

  /**
   * 切换用户状态
   */
  async toggleStatus(req, res) {
    const {
      successResponse,
      errorResponse,
    } = require("../utils/response-helper");

    try {
      const { id } = req.params;

      if (!id) {
        return errorResponse(res, "用户ID缺失", 400);
      }

      // 先获取用户当前状态
      const apiService = require("../utils/apiService");
      const userResult = await apiService.get(
        `/auth/users/${id}`,
        req.session.token,
      );

      if (!userResult.success) {
        return errorResponse(res, "获取用户信息失败");
      }

      const currentStatus = userResult.data.status;
      const newStatus = currentStatus === "active" ? "inactive" : "active";

      // 更新用户状态
      const result = await apiService.put(
        `/auth/users/${id}`,
        req.session.token,
        {
          status: newStatus,
        },
      );

      if (result.success) {
        successResponse(
          res,
          { status: newStatus },
          `用户已${newStatus === "active" ? "激活" : "禁用"}`,
        );
      } else {
        errorResponse(res, result.message || "状态更新失败");
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("切换用户状态失败:", error); } catch(_) {}

      errorResponse(res, "状态更新失败", 500);
    }
  }

  /**
   * 获取用户角色选项
   */
  async getRoleOptions(req, res) {
    const { successResponse } = require("../utils/response-helper");

    const roles = [
      { value: "admin", label: "管理员", description: "拥有全部权限" },
      { value: "manager", label: "管理者", description: "拥有管理权限" },
      { value: "user", label: "普通用户", description: "基础权限" },
    ];

    successResponse(res, roles, "获取角色选项成功");
  }

  /**
   * 获取用户活动日志
   */
  async getActivityLog(req, res) {
    const {
      successResponse,
      errorResponse,
      getPaginationConfig,
    } = require("../utils/response-helper");

    try {
      const { id } = req.params;

      if (!id) {
        return errorResponse(res, "用户ID缺失", 400);
      }

      const pagination = getPaginationConfig(req.query);
      const apiService = require("../utils/apiService");

      const result = await apiService.get(
        `/auth/users/${id}/activity-log`,
        req.session.token,
        {
          page: pagination.page,
          limit: pagination.limit,
        },
      );

      if (result.success) {
        const metadata = pagination.getMetadata(
          result.data.total || result.data.length,
        );
        successResponse(res, result.data, "获取用户活动日志成功", metadata);
      } else {
        errorResponse(res, result.message || "获取活动日志失败");
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取用户活动日志失败:", error); } catch(_) {}

      errorResponse(res, "获取活动日志失败", 500);
    }
  }
}

module.exports = new UsersController();
