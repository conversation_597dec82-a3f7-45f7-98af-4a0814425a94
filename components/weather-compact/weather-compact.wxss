/* components/weather-compact/weather-compact.wxss */
.weather-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
  padding: 0;
  min-height: auto;
  width: auto;
  box-sizing: border-box;
}

.weather-compact:active {
  opacity: 0.8;
}

.weather-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.temperature {
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8rpx;
}

.weather-desc {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1;
}

.weather-icon {
  display: none;
}
