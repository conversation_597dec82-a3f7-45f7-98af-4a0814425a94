// pages/task/detail/detail.js
const { BUSINESS } = require('../../../constants/index.js');

/**
 * 任务详情页面
 * 功能：显示任务详细信息，支持任务完成操作
 */
Page({
  data: {
    taskId: null,
    task: {
      id: null,
      title: '',
      description: '',
      time: '',
      type: '',
      typeText: '',
      completed: false,
      createTime: '',
      deadline: '',
      area: '',
      requirements: [],
      relatedPages: [],
      completedTime: ''
    },
    loading: true
  },

  /**
   * 页面加载
   */
  onLoad: function(options) {
    try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('[TaskDetail] 页面加载，参数', options); } catch(_) {}
    
    const taskId = options.id;
    if (!taskId) {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 缺少任务ID参数'); } catch(_) {}
      wx.showToast({
        title: '任务ID缺失',
        icon: 'error'
      });
      return;
    }

    this.setData({
      taskId: parseInt(taskId)
    });

    this.loadTaskDetail();
  },

  /**
   * 加载任务详情
   */
  loadTaskDetail: function() {
    const { taskId } = this.data;
    
    // 模拟任务数据，实际开发中应该从API获取
    const mockTasks = {
      1: {
        id: 1,
        title: '健康检查提醒',
        description: '今日需要对A区鹅群进行健康检查，重点检查鹅群的精神状态、食欲情况、粪便性状以及是否有异常行为。检查过程中请记录详细数据，如有发现异常情况请及时上报并采取相应措施。',
        time: '今天',
        type: 'health',
        typeText: '健康管理',
        completed: false,
        createTime: '2024-12-15 08:00',
        deadline: '2024-12-15 18:00',
        area: 'A区鹅舍',
        requirements: [
          '检查鹅群精神状态和活动情况',
          '观察食欲和饮水情况', 
          '检查粪便性状是否正常',
          '记录体温和体重数据',
          '发现异常及时上报'
        ],
        relatedPages: [
          {
            id: 'health-check',
            title: '健康记录页面',
            url: '/pages/health/health?tab=0'
          },
          {
            id: 'health-report',
            title: '健康报告',
            url: '/pages/health/report/report'
          }
        ]
      },
      2: {
        id: 2,
        title: '疫苗接种',
        description: 'B区鹅群疫苗接种到期提醒，本次需要接种禽流感疫苗。请准备好相应的疫苗和注射器材，按照标准流程进行接种操作。',
        time: '明天',
        type: 'health',
        typeText: '疫苗管理',
        completed: false,
        createTime: '2024-12-14 10:00',
        deadline: '2024-12-16 17:00',
        area: 'B区鹅舍',
        requirements: [
          '准备禽流感疫苗和注射器材',
          '检查疫苗保存条件和有效期',
          '按照标准流程进行接种',
          '记录接种时间和疫苗批次',
          '观察接种后反应'
        ],
        relatedPages: [
          {
            id: 'vaccine-record',
            title: '疫苗记录',
            url: '/pages/health/health?tab=1'
          }
        ]
      },
      3: {
        id: 3,
        title: '饲料补充',
        description: 'C区饲料库存不足，需要及时补充。请检查当前库存量，计算所需饲料数量，联系供应商采购优质饲料。',
        time: '今天',
        type: 'production',
        typeText: '生产管理',
        completed: false,
        createTime: '2024-12-15 06:00',
        deadline: '2024-12-15 16:00',
        area: 'C区饲料仓库',
        requirements: [
          '检查当前饲料库存量',
          '计算未来一周所需饲料量',
          '联系供应商确认供货时间',
          '检查饲料质量和保质期',
          '安排运输和储存'
        ],
        relatedPages: [
          {
            id: 'materials',
            title: '物料管理',
            url: '/pages/health/health?tab=2'
          },
          {
            id: 'inventory',
            title: '库存管理', 
            url: '/pages/inventory/inventory-detail/inventory-detail'
          }
        ]
      }
    };

    const task = mockTasks[taskId];
    
    if (!task) {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 未找到对应的任务'); } catch(_) {}
      wx.showToast({
        title: '任务不存在',
        icon: 'error'
      });
      return;
    }

    // 从本地存储读取任务完成状态
    const completedTasks = wx.getStorageSync('completed_tasks') || {};
    const taskStatus = completedTasks[taskId];
    
    if (taskStatus && taskStatus.completed) {
      task.completed = true;
      task.completedTime = taskStatus.completedTime;
      try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('[TaskDetail] 从本地存储读取到任务完成状态', taskId); } catch(_) {}
    }

    this.setData({
      task: task,
      loading: false
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: task.title
    });
  },

  /**
   * 完成任务
   */
  onCompleteTask: function() {
    wx.showModal({
      title: '确认完成',
      content: '确定要标记此任务为已完成吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 标记任务为完成
            const now = new Date();
            const completedTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
            
            this.setData({
              'task.completed': true,
              'task.completedTime': completedTime
            });

            // 保存任务完成状态到本地存储
            let completedTasks = wx.getStorageSync('completed_tasks') || {};
            completedTasks[this.data.taskId] = {
              completed: true,
              completedTime: completedTime
            };
            wx.setStorageSync('completed_tasks', completedTasks);

            try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('[TaskDetail] 任务完成状态已保存', this.data.taskId); } catch(_) {}

            wx.showToast({
              title: '任务已完成',
              icon: 'success'
            });

            // 2秒后返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 2000);
          } catch (error) {
            try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 任务完成操作失败', error); } catch(_) {}
            wx.showToast({
              title: '操作失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 跳转到相关页面
   */
  onRelatedPageTap: function(e) {
    const url = e.currentTarget.dataset.url;
    
    if (!url) {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 相关页面URL为空'); } catch(_) {}
      return;
    }

    try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('[TaskDetail] 跳转到相关页面', url); } catch(_) {}
    
    wx.navigateTo({
      url: url,
      fail: (err) => {
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 跳转失败', err); } catch(_) {}
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function() {
    return {
      title: `任务：${this.data.task.title}`,
      path: `/pages/task/detail/detail?id=${this.data.taskId}`
    };
  }
});