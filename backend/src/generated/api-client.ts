import { make<PERSON><PERSON>, Zodios, type ZodiosOptions } from "@zodios/core";
import { z } from "zod";

const GetHealth = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const GetV2Flocks = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const PostV2FlocksCreate = z
  .object({
    name: z.string().min(1).max(100),
    breed: z.enum([
      "white_goose",
      "grey_goose",
      "toulouse",
      "embden",
      "chinese",
      "african",
      "sebastopol",
      "other",
    ]),
    totalCount: z.number().int().gte(1).lte(10000),
    maleCount: z
      .number()
      .int()
      .gte(0)
      .lte(9007199254740991)
      .optional()
      .default(0),
    femaleCount: z
      .number()
      .int()
      .gte(0)
      .lte(9007199254740991)
      .optional()
      .default(0),
    ageGroup: z
      .enum(["gosling", "young", "adult", "breeder"])
      .optional()
      .default("young"),
    establishedDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$/)
      .datetime({ offset: true })
      .optional()
      .default("2025-07-31T16:23:41.200Z"),
    location: z.string().max(200).optional(),
    description: z.string().max(500).optional(),
  })
  .passthrough();
const GetV2FlocksId = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const PatchV2FlocksIdUpdate = z
  .object({
    name: z.string().min(1).max(100),
    breed: z.enum([
      "white_goose",
      "grey_goose",
      "toulouse",
      "embden",
      "chinese",
      "african",
      "sebastopol",
      "other",
    ]),
    totalCount: z.number().int().gte(1).lte(10000),
    maleCount: z.number().int().gte(0).lte(9007199254740991).default(0),
    femaleCount: z.number().int().gte(0).lte(9007199254740991).default(0),
    ageGroup: z.enum(["gosling", "young", "adult", "breeder"]).default("young"),
    establishedDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$/)
      .datetime({ offset: true })
      .default("2025-07-31T16:23:41.202Z"),
    location: z.string().max(200),
    description: z.string().max(500),
    status: z.enum(["active", "inactive", "sold", "deceased"]),
    currentCount: z.number().int().gte(0).lte(9007199254740991),
  })
  .partial()
  .passthrough();
const DeleteV2FlocksIdDelete = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const GetV2HealthRecords = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const PostV2HealthRecordsCreate = z
  .object({
    flockId: z.number().int().gt(0).lte(9007199254740991),
    checkType: z.enum([
      "routine",
      "disease",
      "vaccination",
      "treatment",
      "emergency",
    ]),
    checkDate: z.string(),
    symptoms: z.string().max(1000).optional(),
    temperature: z.number().gte(35).lte(45).optional(),
    weight: z.number().gt(0).lte(1.7976931348623157e308).optional(),
    result: z.enum(["healthy", "sick", "recovering", "dead"]),
    diagnosis: z.string().max(1000).optional(),
    treatment: z.string().max(1000).optional(),
    medicine: z.string().max(500).optional(),
    dosage: z.string().max(200).optional(),
    notes: z.string().max(1000).optional(),
  })
  .passthrough();
const GetV2HealthRecordsId = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const PatchV2HealthRecordsIdUpdate = z
  .object({
    checkType: z.enum([
      "routine",
      "disease",
      "vaccination",
      "treatment",
      "emergency",
    ]),
    checkDate: z.string(),
    symptoms: z.string().max(1000),
    temperature: z.number().gte(35).lte(45),
    weight: z.number().gt(0).lte(1.7976931348623157e308),
    result: z.enum(["healthy", "sick", "recovering", "dead"]),
    diagnosis: z.string().max(1000),
    treatment: z.string().max(1000),
    medicine: z.string().max(500),
    dosage: z.string().max(200),
    notes: z.string().max(1000),
  })
  .partial()
  .passthrough();
const DeleteV2HealthRecordsIdDelete = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const GetV2HealthStats = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const GetV2ProductionRecords = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const PostV2ProductionRecordsCreate = z
  .object({
    flockId: z.number().int().gt(0).lte(9007199254740991),
    recordType: z
      .enum(["daily", "feeding", "collection", "cleaning", "maintenance"])
      .optional()
      .default("daily"),
    recordDate: z.string(),
    eggCount: z
      .number()
      .int()
      .gte(0)
      .lte(9007199254740991)
      .optional()
      .default(0),
    eggWeight: z.number().gt(0).lte(1.7976931348623157e308).optional(),
    feedConsumption: z
      .number()
      .gte(0)
      .lte(1.7976931348623157e308)
      .optional()
      .default(0),
    feedCost: z.number().gte(0).lte(1.7976931348623157e308).optional(),
    waterConsumption: z.number().gte(0).lte(1.7976931348623157e308).optional(),
    temperature: z.number().gte(-20).lte(50).optional(),
    humidity: z.number().gte(0).lte(100).optional(),
    weather: z
      .enum([
        "sunny",
        "cloudy",
        "overcast",
        "rainy",
        "foggy",
        "windy",
        "stormy",
      ])
      .optional(),
    mortality: z
      .number()
      .int()
      .gte(0)
      .lte(9007199254740991)
      .optional()
      .default(0),
    healthyCount: z.number().int().gte(0).lte(9007199254740991).optional(),
    sickCount: z
      .number()
      .int()
      .gte(0)
      .lte(9007199254740991)
      .optional()
      .default(0),
    notes: z.string().max(2000).optional(),
  })
  .passthrough();
const GetV2ProductionRecordsId = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const PatchV2ProductionRecordsIdUpdate = z
  .object({
    recordType: z.enum([
      "daily",
      "feeding",
      "collection",
      "cleaning",
      "maintenance",
    ]),
    recordDate: z.string(),
    eggCount: z.number().int().gte(0).lte(9007199254740991),
    eggWeight: z.number().gt(0).lte(1.7976931348623157e308),
    feedConsumption: z.number().gte(0).lte(1.7976931348623157e308),
    feedCost: z.number().gte(0).lte(1.7976931348623157e308),
    waterConsumption: z.number().gte(0).lte(1.7976931348623157e308),
    temperature: z.number().gte(-20).lte(50),
    humidity: z.number().gte(0).lte(100),
    weather: z.enum([
      "sunny",
      "cloudy",
      "overcast",
      "rainy",
      "foggy",
      "windy",
      "stormy",
    ]),
    mortality: z.number().int().gte(0).lte(9007199254740991),
    healthyCount: z.number().int().gte(0).lte(9007199254740991),
    sickCount: z.number().int().gte(0).lte(9007199254740991),
    notes: z.string().max(2000),
  })
  .partial()
  .passthrough();
const DeleteV2ProductionRecordsIdDelete = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const GetV2ProductionStats = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();
const GetV2ProductionTrends = z
  .object({
    status: z.string(),
    error: z.object({ message: z.string() }).passthrough(),
  })
  .passthrough();

export const schemas = {
  GetHealth,
  GetV2Flocks,
  PostV2FlocksCreate,
  GetV2FlocksId,
  PatchV2FlocksIdUpdate,
  DeleteV2FlocksIdDelete,
  GetV2HealthRecords,
  PostV2HealthRecordsCreate,
  GetV2HealthRecordsId,
  PatchV2HealthRecordsIdUpdate,
  DeleteV2HealthRecordsIdDelete,
  GetV2HealthStats,
  GetV2ProductionRecords,
  PostV2ProductionRecordsCreate,
  GetV2ProductionRecordsId,
  PatchV2ProductionRecordsIdUpdate,
  DeleteV2ProductionRecordsIdDelete,
  GetV2ProductionStats,
  GetV2ProductionTrends,
};

const endpoints = makeApi([
  {
    method: "get",
    path: "/health",
    alias: "GetHealth",
    description: `检查服务器和相关服务的健康状态`,
    requestFormat: "json",
    response: GetHealth,
    errors: [
      {
        status: 400,
        description: `GET /health 错误响应`,
        schema: GetHealth,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/flocks",
    alias: "GetV2Flocks",
    description: `获取当前用户的鹅群列表，支持分页、搜索和排序`,
    requestFormat: "json",
    parameters: [
      {
        name: "page",
        type: "Query",
        schema: z
          .number()
          .int()
          .gt(0)
          .lte(9007199254740991)
          .optional()
          .default(1),
      },
      {
        name: "limit",
        type: "Query",
        schema: z.number().int().gte(1).lte(100).optional().default(10),
      },
      {
        name: "search",
        type: "Query",
        schema: z.string().max(100).optional(),
      },
      {
        name: "status",
        type: "Query",
        schema: z.enum(["healthy", "sick", "recovering", "dead"]).optional(),
      },
      {
        name: "breed",
        type: "Query",
        schema: z
          .enum([
            "white_goose",
            "grey_goose",
            "toulouse",
            "embden",
            "chinese",
            "african",
            "sebastopol",
            "other",
          ])
          .optional(),
      },
      {
        name: "ageGroup",
        type: "Query",
        schema: z.enum(["gosling", "young", "adult", "breeder"]).optional(),
      },
      {
        name: "sortBy",
        type: "Query",
        schema: z
          .enum(["recordDate", "createdAt", "eggCount", "feedConsumption"])
          .optional()
          .default("recordDate"),
      },
      {
        name: "sortOrder",
        type: "Query",
        schema: z.enum(["ASC", "DESC"]).optional().default("DESC"),
      },
    ],
    response: GetV2Flocks,
    errors: [
      {
        status: 400,
        description: `GET /v2/flocks 错误响应`,
        schema: GetV2Flocks,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/flocks/:id",
    alias: "GetV2FlocksId",
    description: `根据ID获取鹅群的详细信息`,
    requestFormat: "json",
    parameters: [
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: GetV2FlocksId,
    errors: [
      {
        status: 400,
        description: `GET /v2/flocks/:id 错误响应`,
        schema: GetV2FlocksId,
      },
    ],
  },
  {
    method: "delete",
    path: "/v2/flocks/:id/delete",
    alias: "DeleteV2FlocksIdDelete",
    description: `删除指定的鹅群记录`,
    requestFormat: "json",
    parameters: [
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: DeleteV2FlocksIdDelete,
    errors: [
      {
        status: 400,
        description: `DELETE /v2/flocks/:id/delete 错误响应`,
        schema: DeleteV2FlocksIdDelete,
      },
    ],
  },
  {
    method: "patch",
    path: "/v2/flocks/:id/update",
    alias: "PatchV2FlocksIdUpdate",
    description: `更新鹅群的信息`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        description: `PatchV2FlocksIdUpdate 请求体`,
        type: "Body",
        schema: PatchV2FlocksIdUpdate,
      },
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: PatchV2FlocksIdUpdate,
    errors: [
      {
        status: 400,
        description: `PATCH /v2/flocks/:id/update 错误响应`,
        schema: PatchV2FlocksIdUpdate,
      },
    ],
  },
  {
    method: "post",
    path: "/v2/flocks/create",
    alias: "PostV2FlocksCreate",
    description: `创建新的鹅群记录`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        description: `PostV2FlocksCreate 请求体`,
        type: "Body",
        schema: PostV2FlocksCreate,
      },
    ],
    response: PostV2FlocksCreate,
    errors: [
      {
        status: 400,
        description: `POST /v2/flocks/create 错误响应`,
        schema: PostV2FlocksCreate,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/health/records",
    alias: "GetV2HealthRecords",
    description: `获取当前租户的健康记录列表，支持分页、搜索和过滤`,
    requestFormat: "json",
    parameters: [
      {
        name: "page",
        type: "Query",
        schema: z
          .number()
          .int()
          .gt(0)
          .lte(9007199254740991)
          .optional()
          .default(1),
      },
      {
        name: "limit",
        type: "Query",
        schema: z.number().int().gte(1).lte(100).optional().default(10),
      },
      {
        name: "flockId",
        type: "Query",
        schema: z.number().int().gt(0).lte(9007199254740991).optional(),
      },
      {
        name: "status",
        type: "Query",
        schema: z.enum(["healthy", "sick", "recovering", "dead"]).optional(),
      },
      {
        name: "checkType",
        type: "Query",
        schema: z
          .enum(["routine", "disease", "vaccination", "treatment", "emergency"])
          .optional(),
      },
      {
        name: "startDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "endDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "search",
        type: "Query",
        schema: z.string().max(100).optional(),
      },
      {
        name: "sortBy",
        type: "Query",
        schema: z
          .enum(["recordDate", "createdAt", "eggCount", "feedConsumption"])
          .optional()
          .default("recordDate"),
      },
      {
        name: "sortOrder",
        type: "Query",
        schema: z.enum(["ASC", "DESC"]).optional().default("DESC"),
      },
    ],
    response: GetV2HealthRecords,
    errors: [
      {
        status: 400,
        description: `GET /v2/health/records 错误响应`,
        schema: GetV2HealthRecords,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/health/records/:id",
    alias: "GetV2HealthRecordsId",
    description: `根据ID获取特定健康记录的详细信息`,
    requestFormat: "json",
    parameters: [
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: GetV2HealthRecordsId,
    errors: [
      {
        status: 400,
        description: `GET /v2/health/records/:id 错误响应`,
        schema: GetV2HealthRecordsId,
      },
    ],
  },
  {
    method: "delete",
    path: "/v2/health/records/:id/delete",
    alias: "DeleteV2HealthRecordsIdDelete",
    description: `删除指定的健康记录`,
    requestFormat: "json",
    parameters: [
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: DeleteV2HealthRecordsIdDelete,
    errors: [
      {
        status: 400,
        description: `DELETE /v2/health/records/:id/delete 错误响应`,
        schema: DeleteV2HealthRecordsIdDelete,
      },
    ],
  },
  {
    method: "patch",
    path: "/v2/health/records/:id/update",
    alias: "PatchV2HealthRecordsIdUpdate",
    description: `更新指定健康记录的信息`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        description: `PatchV2HealthRecordsIdUpdate 请求体`,
        type: "Body",
        schema: PatchV2HealthRecordsIdUpdate,
      },
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: PatchV2HealthRecordsIdUpdate,
    errors: [
      {
        status: 400,
        description: `PATCH /v2/health/records/:id/update 错误响应`,
        schema: PatchV2HealthRecordsIdUpdate,
      },
    ],
  },
  {
    method: "post",
    path: "/v2/health/records/create",
    alias: "PostV2HealthRecordsCreate",
    description: `为指定鹅群创建新的健康检查记录`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        description: `PostV2HealthRecordsCreate 请求体`,
        type: "Body",
        schema: PostV2HealthRecordsCreate,
      },
    ],
    response: PostV2HealthRecordsCreate,
    errors: [
      {
        status: 400,
        description: `POST /v2/health/records/create 错误响应`,
        schema: PostV2HealthRecordsCreate,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/health/stats",
    alias: "GetV2HealthStats",
    description: `获取健康记录的统计信息和趋势数据`,
    requestFormat: "json",
    parameters: [
      {
        name: "flockId",
        type: "Query",
        schema: z.number().int().gt(0).lte(9007199254740991).optional(),
      },
      {
        name: "startDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "endDate",
        type: "Query",
        schema: z.string().optional(),
      },
    ],
    response: GetV2HealthStats,
    errors: [
      {
        status: 400,
        description: `GET /v2/health/stats 错误响应`,
        schema: GetV2HealthStats,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/production/records",
    alias: "GetV2ProductionRecords",
    description: `获取当前租户的生产记录列表，支持分页、搜索和过滤`,
    requestFormat: "json",
    parameters: [
      {
        name: "page",
        type: "Query",
        schema: z
          .number()
          .int()
          .gt(0)
          .lte(9007199254740991)
          .optional()
          .default(1),
      },
      {
        name: "limit",
        type: "Query",
        schema: z.number().int().gte(1).lte(100).optional().default(10),
      },
      {
        name: "flockId",
        type: "Query",
        schema: z.number().int().gt(0).lte(9007199254740991).optional(),
      },
      {
        name: "recordType",
        type: "Query",
        schema: z
          .enum(["daily", "feeding", "collection", "cleaning", "maintenance"])
          .optional(),
      },
      {
        name: "startDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "endDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "weather",
        type: "Query",
        schema: z
          .enum([
            "sunny",
            "cloudy",
            "overcast",
            "rainy",
            "foggy",
            "windy",
            "stormy",
          ])
          .optional(),
      },
      {
        name: "search",
        type: "Query",
        schema: z.string().max(100).optional(),
      },
      {
        name: "sortBy",
        type: "Query",
        schema: z
          .enum(["recordDate", "createdAt", "eggCount", "feedConsumption"])
          .optional()
          .default("recordDate"),
      },
      {
        name: "sortOrder",
        type: "Query",
        schema: z.enum(["ASC", "DESC"]).optional().default("DESC"),
      },
    ],
    response: GetV2ProductionRecords,
    errors: [
      {
        status: 400,
        description: `GET /v2/production/records 错误响应`,
        schema: GetV2ProductionRecords,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/production/records/:id",
    alias: "GetV2ProductionRecordsId",
    description: `根据ID获取特定生产记录的详细信息`,
    requestFormat: "json",
    parameters: [
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: GetV2ProductionRecordsId,
    errors: [
      {
        status: 400,
        description: `GET /v2/production/records/:id 错误响应`,
        schema: GetV2ProductionRecordsId,
      },
    ],
  },
  {
    method: "delete",
    path: "/v2/production/records/:id/delete",
    alias: "DeleteV2ProductionRecordsIdDelete",
    description: `删除指定的生产记录`,
    requestFormat: "json",
    parameters: [
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: DeleteV2ProductionRecordsIdDelete,
    errors: [
      {
        status: 400,
        description: `DELETE /v2/production/records/:id/delete 错误响应`,
        schema: DeleteV2ProductionRecordsIdDelete,
      },
    ],
  },
  {
    method: "patch",
    path: "/v2/production/records/:id/update",
    alias: "PatchV2ProductionRecordsIdUpdate",
    description: `更新指定生产记录的信息`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        description: `PatchV2ProductionRecordsIdUpdate 请求体`,
        type: "Body",
        schema: PatchV2ProductionRecordsIdUpdate,
      },
      {
        name: "id",
        type: "Path",
        schema: z.number().int().gt(0).lte(9007199254740991),
      },
    ],
    response: PatchV2ProductionRecordsIdUpdate,
    errors: [
      {
        status: 400,
        description: `PATCH /v2/production/records/:id/update 错误响应`,
        schema: PatchV2ProductionRecordsIdUpdate,
      },
    ],
  },
  {
    method: "post",
    path: "/v2/production/records/create",
    alias: "PostV2ProductionRecordsCreate",
    description: `为指定鹅群创建新的生产记录`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        description: `PostV2ProductionRecordsCreate 请求体`,
        type: "Body",
        schema: PostV2ProductionRecordsCreate,
      },
    ],
    response: PostV2ProductionRecordsCreate,
    errors: [
      {
        status: 400,
        description: `POST /v2/production/records/create 错误响应`,
        schema: PostV2ProductionRecordsCreate,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/production/stats",
    alias: "GetV2ProductionStats",
    description: `获取生产记录的统计信息`,
    requestFormat: "json",
    parameters: [
      {
        name: "flockId",
        type: "Query",
        schema: z.number().int().gt(0).lte(9007199254740991).optional(),
      },
      {
        name: "startDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "endDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "recordType",
        type: "Query",
        schema: z
          .enum(["daily", "feeding", "collection", "cleaning", "maintenance"])
          .optional(),
      },
    ],
    response: GetV2ProductionStats,
    errors: [
      {
        status: 400,
        description: `GET /v2/production/stats 错误响应`,
        schema: GetV2ProductionStats,
      },
    ],
  },
  {
    method: "get",
    path: "/v2/production/trends",
    alias: "GetV2ProductionTrends",
    description: `获取生产记录的趋势数据`,
    requestFormat: "json",
    parameters: [
      {
        name: "flockId",
        type: "Query",
        schema: z.number().int().gt(0).lte(9007199254740991).optional(),
      },
      {
        name: "startDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "endDate",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "period",
        type: "Query",
        schema: z.enum(["day", "week", "month"]).optional().default("day"),
      },
    ],
    response: GetV2ProductionTrends,
    errors: [
      {
        status: 400,
        description: `GET /v2/production/trends 错误响应`,
        schema: GetV2ProductionTrends,
      },
    ],
  },
]);

export const api = new Zodios(endpoints);

export function createApiClient(baseUrl: string, options?: ZodiosOptions) {
  return new Zodios(baseUrl, endpoints, options);
}
