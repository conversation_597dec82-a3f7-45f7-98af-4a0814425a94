/* 意见反馈页面样式 */
.feedback-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx;
  padding-bottom: 40rpx;
}

/* 通用区域样式 */
.feedback-type-section,
.description-section,
.contact-section,
.upload-section,
.system-info-section,
.history-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.required-mark {
  color: #ff4757;
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 8rpx;
}

.optional-mark {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.view-all {
  font-size: 26rpx;
  color: #007AFF;
  margin-left: auto;
}

/* 反馈类型选择 */
.type-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.type-item.active {
  background: #e3f2fd;
  border-color: #007AFF;
  transform: scale(1.02);
}

.type-item:active {
  transform: scale(0.95);
}

.type-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.type-title {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

.type-item.active .type-title {
  color: #007AFF;
  font-weight: 500;
}

/* 问题描述区域 */
.textarea-container {
  position: relative;
}

.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.feedback-textarea:focus {
  background: #ffffff;
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.feedback-textarea::placeholder {
  color: #999;
}

.char-count {
  position: absolute;
  bottom: 12rpx;
  right: 20rpx;
}

.count-text {
  font-size: 24rpx;
  color: #999;
}

/* 联系方式区域 */
.contact-inputs {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.label-icon {
  font-size: 28rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.contact-input {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.contact-input:focus {
  background: #ffffff;
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.contact-input::placeholder {
  color: #999;
}

/* 图片上传区域 */
.image-upload {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.image-list {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  color: #ffffff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  background: #f8f9fa;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.upload-btn:active {
  background: #e9ecef;
  border-color: #007AFF;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.upload-tips {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 系统信息区域 */
.section-header .info-switch {
  margin-left: auto;
}

.system-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 12rpx;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8rpx 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 160rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.system-tips {
  margin-top: 12rpx;
}

/* 历史反馈区域 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.history-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.history-type {
  font-size: 24rpx;
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.history-desc {
  font-size: 26rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: 22rpx;
  color: #999;
}

.history-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-processing {
  background: #d1ecf1;
  color: #0c5460;
}

.status-resolved {
  background: #d4edda;
  color: #155724;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 提交区域 */
.submit-section {
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.submit-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #ffffff;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.submit-btn.disabled {
  background: #ddd;
  color: #999;
  box-shadow: none;
}

.submit-btn:active:not(.disabled) {
  transform: scale(0.95);
}

.submit-text {
  font-size: 32rpx;
  font-weight: 500;
}

.submit-tips {
  text-align: center;
}

/* 成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  display: block;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24rpx;
  display: block;
}

.success-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 32rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 600;
}

.success-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.success-btn:active {
  background: #0056b3;
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .type-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .image-list {
    justify-content: center;
  }

  .feedback-container {
    padding: 16rpx;
  }

  .modal-content {
    width: 90%;
    margin: 0 5%;
  }
}

/* 动画效果 */
.feedback-type-section,
.description-section,
.contact-section,
.upload-section,
.system-info-section,
.history-section {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}