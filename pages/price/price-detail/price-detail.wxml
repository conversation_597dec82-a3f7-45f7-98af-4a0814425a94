<!-- pages/price/price-detail/price-detail.wxml -->
<view class="price-detail-container">
  <!-- 类型切换标签 -->
  <view class="type-tabs">
    <view class="tab-item {{activeType === 'gosling' ? 'active' : ''}}" 
          data-type="gosling" bindtap="onTypeChange">
      <text class="tab-text">鹅苗价格</text>
    </view>
    <view class="tab-item {{activeType === 'adult' ? 'active' : ''}}" 
          data-type="adult" bindtap="onTypeChange">
      <text class="tab-text">成鹅价格</text>
    </view>
  </view>

  <!-- 四宫格价格展示 -->
  <view class="price-grid">
    <block wx:for="{{priceList}}" wx:key="breed" wx:for-index="index">
      <view class="price-item {{selectedBreed === item.breed ? 'selected' : ''}}" 
            data-breed="{{item.breed}}" 
            data-index="{{index}}" 
            bindtap="onBreedSelect">
        <view class="breed-name">{{item.breed}}</view>
        <view class="breed-desc">{{item.description}}</view>
        <view class="price-info">
          <text class="current-price">¥{{item.price}}</text>
          <text class="price-unit">{{activeType === 'gosling' ? '/只' : '/斤'}}</text>
        </view>
        <view class="price-change {{item.change >= 0 ? 'up' : 'down'}}">
          <text class="change-value">{{item.change >= 0 ? '+' : ''}}{{item.change}}%</text>
        </view>
      </view>
    </block>
  </view>

  <!-- 选中品种的详细信息 -->
  <view wx:if="{{selectedBreed}}" class="detail-section">
    <!-- 价格走势图表 -->
    <view class="chart-section">
      <view class="section-header">
        <text class="section-title">{{selectedBreed}} - 价格趋势</text>
        <view class="time-filter">
          <view class="filter-item {{timeRange === '7d' ? 'active' : ''}}" data-range="7d" bindtap="onTimeRangeChange">7天</view>
          <view class="filter-item {{timeRange === '30d' ? 'active' : ''}}" data-range="30d" bindtap="onTimeRangeChange">30天</view>
          <view class="filter-item {{timeRange === '90d' ? 'active' : ''}}" data-range="90d" bindtap="onTimeRangeChange">90天</view>
        </view>
      </view>
      <view class="chart-container">
        <canvas type="2d" id="priceChart" class="price-chart"></canvas>
      </view>
    </view>

    <!-- 价格统计 -->
    <view class="stats-section">
      <view class="section-title">价格统计</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-label">最高价</text>
          <text class="stats-value high">¥{{stats.maxPrice}}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">最低价</text>
          <text class="stats-value low">¥{{stats.minPrice}}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">平均价</text>
          <text class="stats-value">¥{{stats.avgPrice}}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">波动率</text>
          <text class="stats-value">{{stats.volatility}}%</text>
        </view>
      </view>
    </view>

    <!-- 市场分析 -->
    <view class="analysis-section">
      <view class="section-title">市场分析</view>
      <view class="analysis-content">
        <view class="analysis-item">
          <text class="analysis-title">价格走势</text>
          <text class="analysis-desc">{{analysis.trend}}</text>
        </view>
        <view class="analysis-item">
          <text class="analysis-title">市场预测</text>
          <text class="analysis-desc">{{analysis.forecast}}</text>
        </view>
        <view class="analysis-item">
          <text class="analysis-title">建议</text>
          <text class="analysis-desc">{{analysis.suggestion}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view wx:if="{{selectedBreed}}" class="action-section">
    <button class="action-btn primary" bindtap="onSubscribeTap">订阅价格提醒</button>
    <button class="action-btn secondary" bindtap="onShareTap">分享价格信息</button>
  </view>
</view>
