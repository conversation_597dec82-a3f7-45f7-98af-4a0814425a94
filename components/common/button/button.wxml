<!-- 通用按钮组件 -->
<button
  class="c-button c-button--{{type}} c-button--{{size}} {{disabled ? 'c-button--disabled' : ''}} {{loading ? 'c-button--loading' : ''}} {{block ? 'c-button--block' : ''}} {{round ? 'c-button--round' : ''}} {{plain ? 'c-button--plain' : ''}} {{customClass}}"
  style="{{customStyle}}"
  disabled="{{disabled || loading}}"
  form-type="{{formType}}"
  open-type="{{openType}}"
  aria-label="{{ariaLabel || text}}"
  aria-disabled="{{disabled}}"
  aria-busy="{{loading}}"
  bindtap="onTap"
  bindlongpress="onLongPress"
  bindtouchstart="onTouchStart"
  bindtouchend="onTouchEnd"
  bindgetuserinfo="onGetUserInfo"
  bindgetphonenumber="onGetPhoneNumber"
  bindgetrealnameauthinfo="onGetRealNameAuthInfo"
  bindcontact="onContact"
  bindchooseavatar="onChooseAvatar"
  binderror="onError"
  bindopensetting="onOpenSetting"
>
  <view class="c-button__content">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="c-button__loading">
      <image class="c-button__loading-icon" src="{{loadingIcon}}" mode="aspectFit"></image>
    </view>
    
    <!-- 左侧图标 -->
    <view wx:elif="{{icon && iconPosition === 'left'}}" class="c-button__icon c-button__icon--left">
      <image class="c-button__icon-img" src="{{icon}}" mode="aspectFit" lazy-load="{{true}}"></image>
    </view>
    
    <!-- 按钮文本 -->
    <view wx:if="{{text}}" class="c-button__text">
      <text>{{text}}</text>
    </view>
    
    <!-- 右侧图标 -->
    <view wx:if="{{icon && iconPosition === 'right' && !loading}}" class="c-button__icon c-button__icon--right">
      <image class="c-button__icon-img" src="{{icon}}" mode="aspectFit" lazy-load="{{true}}"></image>
    </view>
    
    <!-- 自定义内容插槽 -->
    <view wx:if="{{!text}}" class="c-button__slot">
      <slot></slot>
    </view>
  </view>
</button>