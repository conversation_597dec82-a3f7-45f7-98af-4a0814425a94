/* components/section-header/section-header.wxss */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.title-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.more-btn {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 28rpx;
  padding: 8rpx 0;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
  opacity: 0.6;
}