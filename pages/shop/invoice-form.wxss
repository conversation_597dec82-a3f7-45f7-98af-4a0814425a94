/* pages/shop/invoice-form.wxss */
.invoice-form-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.form-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.form-group {
  padding: 0 30rpx;
}

.form-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
  flex-shrink: 0;
}

.label.required::after {
  content: '*';
  color: #ff4444;
  margin-left: 4rpx;
}

.input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
  padding: 20rpx 0;
}

.input::placeholder {
  color: #999;
}

.tips {
  padding: 30rpx;
  background-color: #f8f9fa;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.tips-text:last-child {
  margin-bottom: 0;
}

.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.save-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn::after {
  border: none;
}