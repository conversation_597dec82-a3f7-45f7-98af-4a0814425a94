/**
 * 通用工具函数
 */

/**
 * 格式化日期
 * @param {Date} date 日期对象
 * @param {string} format 格式化字符串
 */
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second);
}

/**
 * 格式化金额
 * @param {number} value 金额
 */
function formatCurrency(value) {
  if (value === null || value === undefined) return '0.00';
  return parseFloat(value).toFixed(2);
}

/**
 * 防抖函数
 * @param {Function} fn 执行函数
 * @param {number} delay 延迟时间
 */
function debounce(fn, delay = 300) {
  let timer = null;
  return function() {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
      timer = null;
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} fn 执行函数
 * @param {number} delay 延迟时间
 */
function throttle(fn, delay = 300) {
  let timer = null;
  return function() {
    if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, arguments);
        timer = null;
      }, delay);
    }
  };
}

/**
 * 深拷贝
 * @param {Object} obj 需要拷贝的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj);
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (obj instanceof Object) {
    const clonedObj = {};
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * 数据验证
 */
const validator = {
  // 验证手机号
  isMobile: (value) => /^1[3-9]\d{9}$/.test(value),
  
  // 验证邮箱
  isEmail: (value) => /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value),
  
  // 验证身份证号
  isIdCard: (value) => /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value),
  
  // 验证是否为空
  isEmpty: (value) => value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)
};

module.exports = {
  formatDate,
  formatCurrency,
  debounce,
  throttle,
  deepClone,
  validator
};