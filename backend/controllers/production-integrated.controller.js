/**
 * 生产记录控制器（集成财务功能）
 * 处理入栏、称重、出栏记录，并自动生成相应的财务记录
 */

const db = require('../config/database');
const ResponseHelper = require('../utils/responseHelper');
const financeController = require('./finance.controller');

/**
 * 创建入栏记录（自动生成支出记录）
 * POST /api/v1/production/entry-records
 */
exports.createEntryRecord = async (req, res) => {
  const connection = await db.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const userId = req.user.id;
    const {
      batchNumber,
      breed,
      count,
      weight,
      source,
      supplier,
      unitCost,
      totalCost,
      recordDate,
      notes,
      shedNumber,
      dayAge,
      healthStatus = 'healthy'
    } = req.body;

    // 验证必填字段
    if (!batchNumber || !breed || !count || !totalCost) {
      await connection.rollback();
      return ResponseHelper.error(res, '缺少必填字段', 400);
    }

    // 1. 创建入栏记录
    const [entryResult] = await connection.query(`
      INSERT INTO production_records_v2 (
        user_id, type, batch, date, count, weight, breed, source, supplier,
        unit_cost, total_cost, notes, shed_number, day_age, health_status, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId, 'entry', batchNumber, recordDate || new Date().toISOString().split('T')[0],
      count, weight, breed, source, supplier, unitCost, totalCost, notes,
      shedNumber, dayAge, healthStatus, 'submitted'
    ]);

    const entryRecordId = entryResult.insertId;

    // 2. 自动生成财务支出记录
    const financeRecordData = {
      userId,
      type: 'expense',
      category: '鹅苗采购',
      amount: totalCost,
      description: `批次${batchNumber}入栏${count}只${getBreedName(breed)}`,
      recordDate: recordDate || new Date().toISOString().split('T')[0],
      batchNumber,
      supplier,
      relatedRecordType: 'entry',
      relatedRecordId: entryRecordId,
      paymentMethod: 'bank_transfer'
    };

    const financeResult = await financeController.createAutoFinancialRecord(
      financeRecordData, 
      connection
    );

    if (!financeResult.success) {
      await connection.rollback();
      return ResponseHelper.error(res, '创建财务记录失败', 500);
    }

    // 3. 更新或创建鹅群记录
    await updateFlockRecord(connection, {
      userId,
      batchNumber,
      breed,
      totalCount: count,
      currentCount: count,
      status: 'active',
      establishedDate: recordDate || new Date().toISOString().split('T')[0]
    });

    await connection.commit();

    // 获取完整的入栏记录
    const [newRecord] = await connection.query(`
      SELECT * FROM production_records_v2 WHERE id = ?
    `, [entryRecordId]);

    return ResponseHelper.success(res, {
      entryRecord: newRecord[0],
      financeRecordId: financeResult.recordId,
      message: '入栏记录创建成功，已自动生成支出记录'
    }, '入栏记录创建成功', 201);

  } catch (error) {
    await connection.rollback();
    console.error('创建入栏记录失败:', error);
    return ResponseHelper.error(res, '创建入栏记录失败', 500, error.message);
  } finally {
    connection.release();
  }
};

/**
 * 创建出栏记录（自动生成收入记录）
 * POST /api/v1/production/sale-records
 */
exports.createSaleRecord = async (req, res) => {
  const connection = await db.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const userId = req.user.id;
    const {
      batchNumber,
      count,
      weight,
      unitPrice,
      totalIncome,
      buyer,
      recordDate,
      notes,
      averageWeight
    } = req.body;

    // 验证必填字段
    if (!batchNumber || !count || !totalIncome) {
      await connection.rollback();
      return ResponseHelper.error(res, '缺少必填字段', 400);
    }

    // 验证批次存在且有足够存栏数
    const [batchInfo] = await connection.query(`
      SELECT currentCount FROM flocks WHERE batchNumber = ? AND userId = ?
    `, [batchNumber, userId]);

    if (batchInfo.length === 0) {
      await connection.rollback();
      return ResponseHelper.error(res, '批次不存在', 404);
    }

    if (batchInfo[0].currentCount < count) {
      await connection.rollback();
      return ResponseHelper.error(res, `出栏数量不能超过当前存栏数(${batchInfo[0].currentCount}只)`, 400);
    }

    // 1. 创建出栏记录
    const [saleResult] = await connection.query(`
      INSERT INTO production_records_v2 (
        user_id, type, batch, date, sale_count, weight, average_weight,
        unit_price, total_income, buyer, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId, 'sale', batchNumber, recordDate || new Date().toISOString().split('T')[0],
      count, weight, averageWeight, unitPrice, totalIncome, buyer, notes, 'submitted'
    ]);

    const saleRecordId = saleResult.insertId;

    // 2. 自动生成财务收入记录
    const financeRecordData = {
      userId,
      type: 'income',
      category: '鹅只销售',
      amount: totalIncome,
      description: `批次${batchNumber}出栏${count}只成鹅`,
      recordDate: recordDate || new Date().toISOString().split('T')[0],
      batchNumber,
      supplier: buyer,
      relatedRecordType: 'sale',
      relatedRecordId: saleRecordId,
      paymentMethod: 'bank_transfer'
    };

    const financeResult = await financeController.createAutoFinancialRecord(
      financeRecordData, 
      connection
    );

    if (!financeResult.success) {
      await connection.rollback();
      return ResponseHelper.error(res, '创建财务记录失败', 500);
    }

    // 3. 更新鹅群存栏数
    await connection.query(`
      UPDATE flocks 
      SET currentCount = currentCount - ?, updated_at = NOW()
      WHERE batchNumber = ? AND userId = ?
    `, [count, batchNumber, userId]);

    await connection.commit();

    // 获取完整的出栏记录
    const [newRecord] = await connection.query(`
      SELECT * FROM production_records_v2 WHERE id = ?
    `, [saleRecordId]);

    return ResponseHelper.success(res, {
      saleRecord: newRecord[0],
      financeRecordId: financeResult.recordId,
      message: '出栏记录创建成功，已自动生成收入记录'
    }, '出栏记录创建成功', 201);

  } catch (error) {
    await connection.rollback();
    console.error('创建出栏记录失败:', error);
    return ResponseHelper.error(res, '创建出栏记录失败', 500, error.message);
  } finally {
    connection.release();
  }
};

/**
 * 创建称重记录
 * POST /api/v1/production/weight-records
 */
exports.createWeightRecord = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      batchNumber,
      count,
      averageWeight,
      totalWeight,
      recordDate,
      notes,
      feedRatio
    } = req.body;

    // 验证必填字段
    if (!batchNumber || !count || !averageWeight) {
      return ResponseHelper.error(res, '缺少必填字段', 400);
    }

    // 验证批次存在
    const [batchInfo] = await db.query(`
      SELECT currentCount FROM flocks WHERE batchNumber = ? AND userId = ?
    `, [batchNumber, userId]);

    if (batchInfo.length === 0) {
      return ResponseHelper.error(res, '批次不存在', 404);
    }

    // 创建称重记录
    const [result] = await db.query(`
      INSERT INTO production_records_v2 (
        user_id, type, batch, date, weight_count, average_weight, 
        total_weight, feed_ratio, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId, 'weight', batchNumber, recordDate || new Date().toISOString().split('T')[0],
      count, averageWeight, totalWeight, feedRatio, notes, 'submitted'
    ]);

    // 获取创建的记录
    const [newRecord] = await db.query(`
      SELECT * FROM production_records_v2 WHERE id = ?
    `, [result.insertId]);

    return ResponseHelper.success(res, newRecord[0], '称重记录创建成功', 201);

  } catch (error) {
    console.error('创建称重记录失败:', error);
    return ResponseHelper.error(res, '创建称重记录失败', 500, error.message);
  }
};

/**
 * 获取生产记录列表（支持类型筛选）
 * GET /api/v1/production/records
 */
exports.getProductionRecords = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 20,
      type, // entry, weight, sale
      batchNumber,
      startDate,
      endDate,
      status
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = ['user_id = ?'];
    let queryParams = [userId];

    // 构建查询条件
    if (type) {
      whereConditions.push('type = ?');
      queryParams.push(type);
    }
    if (batchNumber) {
      whereConditions.push('batch = ?');
      queryParams.push(batchNumber);
    }
    if (startDate) {
      whereConditions.push('date >= ?');
      queryParams.push(startDate);
    }
    if (endDate) {
      whereConditions.push('date <= ?');
      queryParams.push(endDate);
    }
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询记录
    const [records] = await db.query(`
      SELECT 
        pr.*,
        CASE 
          WHEN pr.type = 'entry' THEN '入栏记录'
          WHEN pr.type = 'weight' THEN '称重记录'
          WHEN pr.type = 'sale' THEN '出栏记录'
        END as type_name,
        fr.id as finance_record_id,
        fr.amount as finance_amount,
        fr.type as finance_type
      FROM production_records_v2 pr
      LEFT JOIN financial_records fr ON fr.related_record_id = pr.id 
        AND fr.related_record_type = pr.type AND fr.userId = pr.user_id
      WHERE ${whereClause}
      ORDER BY pr.date DESC, pr.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, parseInt(limit), offset]);

    // 查询总数
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total
      FROM production_records_v2
      WHERE ${whereClause}
    `, queryParams);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return ResponseHelper.success(res, {
      records,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('获取生产记录失败:', error);
    return ResponseHelper.error(res, '获取生产记录失败', 500, error.message);
  }
};

/**
 * 获取批次详细信息（包含财务统计）
 * GET /api/v1/production/batch-details/:batchNumber
 */
exports.getBatchDetails = async (req, res) => {
  try {
    const userId = req.user.id;
    const { batchNumber } = req.params;

    // 获取批次基本信息
    const [batchInfo] = await db.query(`
      SELECT * FROM flocks WHERE batchNumber = ? AND userId = ?
    `, [batchNumber, userId]);

    if (batchInfo.length === 0) {
      return ResponseHelper.error(res, '批次不存在', 404);
    }

    // 获取生产记录
    const [productionRecords] = await db.query(`
      SELECT * FROM production_records_v2
      WHERE batch = ? AND user_id = ?
      ORDER BY date DESC, created_at DESC
    `, [batchNumber, userId]);

    // 获取财务统计
    const [financeStats] = await db.query(`
      SELECT
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_cost,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_revenue,
        COUNT(*) as finance_record_count
      FROM financial_records
      WHERE batch_number = ? AND userId = ?
    `, [batchNumber, userId]);

    const stats = financeStats[0] || { total_cost: 0, total_revenue: 0, finance_record_count: 0 };
    const netProfit = parseFloat(stats.total_revenue) - parseFloat(stats.total_cost);
    const profitMargin = stats.total_cost > 0 ? (netProfit / parseFloat(stats.total_cost)) * 100 : 0;

    return ResponseHelper.success(res, {
      batchInfo: batchInfo[0],
      productionRecords,
      financeStats: {
        totalCost: parseFloat(stats.total_cost),
        totalRevenue: parseFloat(stats.total_revenue),
        netProfit,
        profitMargin: Math.round(profitMargin * 100) / 100,
        recordCount: stats.finance_record_count
      }
    });

  } catch (error) {
    console.error('获取批次详情失败:', error);
    return ResponseHelper.error(res, '获取批次详情失败', 500, error.message);
  }
};

/**
 * 获取活跃批次列表（用于选择器）
 * GET /api/v1/production/active-batches
 */
exports.getActiveBatches = async (req, res) => {
  try {
    const userId = req.user.id;

    const [batches] = await db.query(`
      SELECT
        f.batchNumber,
        f.name,
        f.breed,
        f.totalCount,
        f.currentCount,
        f.status,
        f.establishedDate,
        COUNT(pr.id) as record_count,
        MAX(pr.date) as last_record_date
      FROM flocks f
      LEFT JOIN production_records_v2 pr ON f.batchNumber = pr.batch AND f.userId = pr.user_id
      WHERE f.userId = ? AND f.status = 'active' AND f.currentCount > 0
      GROUP BY f.batchNumber, f.name, f.breed, f.totalCount, f.currentCount, f.status, f.establishedDate
      ORDER BY f.establishedDate DESC
    `, [userId]);

    const activeBatches = batches.map(batch => ({
      batchNumber: batch.batchNumber,
      displayName: `${batch.batchNumber} (${getBreedName(batch.breed)}, ${batch.currentCount}只)`,
      name: batch.name,
      breed: batch.breed,
      breedName: getBreedName(batch.breed),
      totalCount: batch.totalCount,
      currentCount: batch.currentCount,
      status: batch.status,
      establishedDate: batch.establishedDate,
      recordCount: batch.record_count,
      lastRecordDate: batch.last_record_date
    }));

    return ResponseHelper.success(res, activeBatches);

  } catch (error) {
    console.error('获取活跃批次失败:', error);
    return ResponseHelper.error(res, '获取活跃批次失败', 500, error.message);
  }
};

// ================================
// 辅助函数
// ================================

/**
 * 更新或创建鹅群记录
 */
async function updateFlockRecord(connection, flockData) {
  const { userId, batchNumber, breed, totalCount, currentCount, status, establishedDate } = flockData;

  // 检查是否已存在
  const [existing] = await connection.query(`
    SELECT id FROM flocks WHERE batchNumber = ? AND userId = ?
  `, [batchNumber, userId]);

  if (existing.length > 0) {
    // 更新现有记录
    await connection.query(`
      UPDATE flocks
      SET totalCount = ?, currentCount = ?, status = ?, updated_at = NOW()
      WHERE batchNumber = ? AND userId = ?
    `, [totalCount, currentCount, status, batchNumber, userId]);
  } else {
    // 创建新记录
    await connection.query(`
      INSERT INTO flocks (
        userId, batchNumber, name, breed, totalCount, currentCount,
        status, establishedDate, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      userId, batchNumber, `${batchNumber}批次`, breed,
      totalCount, currentCount, status, establishedDate
    ]);
  }
}

/**
 * 获取品种中文名称
 */
function getBreedName(breed) {
  const breedMap = {
    'taihu': '太湖鹅',
    'sichuan': '四川白鹅',
    'wanxi': '皖西白鹅',
    'yangzhou': '扬州鹅',
    'other': '其他品种'
  };
  return breedMap[breed] || breed;
}

/**
 * 生成批次号
 */
function generateBatchNumber() {
  const now = new Date();
  const baseNumber = `QY-${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
  return baseNumber;
}

/**
 * 检查批次号唯一性并生成新的批次号
 */
async function generateUniqueBatchNumber(userId) {
  const baseNumber = generateBatchNumber();

  // 查询当天已有的批次号
  const [existing] = await db.query(`
    SELECT batchNumber FROM flocks
    WHERE userId = ? AND batchNumber LIKE ?
    ORDER BY batchNumber DESC
  `, [userId, `${baseNumber}%`]);

  if (existing.length === 0) {
    return baseNumber;
  }

  // 找到最大序号
  let maxSequence = 1;
  existing.forEach(batch => {
    const match = batch.batchNumber.match(new RegExp(`^${baseNumber}-(\\d+)$`));
    if (match) {
      const sequence = parseInt(match[1]);
      if (sequence >= maxSequence) {
        maxSequence = sequence + 1;
      }
    }
  });

  return `${baseNumber}-${maxSequence}`;
}

module.exports = {
  createEntryRecord: exports.createEntryRecord,
  createSaleRecord: exports.createSaleRecord,
  createWeightRecord: exports.createWeightRecord,
  getProductionRecords: exports.getProductionRecords,
  getBatchDetails: exports.getBatchDetails,
  getActiveBatches: exports.getActiveBatches,
  generateUniqueBatchNumber
};
