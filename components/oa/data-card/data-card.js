// components/oa/data-card/data-card.js

/**
 * OA数据卡片组件
 * 用于展示统计数据和关键指标
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 卡片标题
    title: {
      type: String,
      value: ''
    },
    // 主要数值
    value: {
      type: String,
      value: '0'
    },
    // 数值单位
    unit: {
      type: String,
      value: ''
    },
    // 副标题或描述
    subtitle: {
      type: String,
      value: ''
    },
    // 图标名称
    icon: {
      type: String,
      value: ''
    },
    // 图标颜色
    iconColor: {
      type: String,
      value: '#007AFF'
    },
    // 卡片背景色
    backgroundColor: {
      type: String,
      value: '#FFFFFF'
    },
    // 趋势方向 up/down/stable
    trend: {
      type: String,
      value: 'stable'
    },
    // 趋势值
    trendValue: {
      type: String,
      value: ''
    },
    // 是否显示边框
    bordered: {
      type: Boolean,
      value: true
    },
    // 是否显示阴影
    shadow: {
      type: Boolean,
      value: true
    },
    // 卡片大小 small/medium/large
    size: {
      type: String,
      value: 'medium'
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 卡片点击事件
     */
    onCardTap() {
      if (this.properties.clickable && !this.properties.loading) {
        this.triggerEvent('tap', {
          title: this.properties.title,
          value: this.properties.value
        });
      }
    },

    /**
     * 获取趋势图标
     */
    getTrendIcon() {
      const { trend } = this.properties;
      switch (trend) {
        case 'up':
          return '↗';
        case 'down':
          return '↘';
        default:
          return '→';
      }
    },

    /**
     * 获取趋势颜色
     */
    getTrendColor() {
      const { trend } = this.properties;
      switch (trend) {
        case 'up':
          return '#00A86B';
        case 'down':
          return '#FF3B30';
        default:
          return '#8E8E93';
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    },
    
    detached() {
      // 组件实例被从页面节点树移除时执行
    }
  }
});
