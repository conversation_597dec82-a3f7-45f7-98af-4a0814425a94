# 🔐 统一权限管理系统使用指南

## 📋 概述

本指南介绍了智慧养鹅SAAS平台的统一权限管理系统，该系统整合了原先分散的权限定义和验证逻辑，提供了一致的前后端权限控制机制。

## 🏗️ 架构设计

### 权限层级结构

```
┌─────────────────────────────────────────────┐
│                平台级权限                    │
│  - 平台超级管理员                           │
│  - 平台管理员                               │
│  - 平台运维                                 │
│  - 平台客服                                 │
└─────────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────────┐
│                租户级权限                    │
│  - 企业所有者 (owner)                       │
│  - 企业管理员 (admin)                       │
│  - 部门经理 (manager)                       │
│  - 专业角色 (finance, hr)                   │
│  - 普通员工 (user)                          │
└─────────────────────────────────────────────┘
```

### 权限命名规范

```
格式: <scope>:<resource>:<action>

示例:
- platform:tenant:create     # 平台级-租户管理-创建
- tenant:flock:view          # 租户级-鹅群管理-查看
- tenant:finance:approve     # 租户级-财务管理-审批
```

## 🔧 后端使用方法

### 1. 引入统一权限中间件

```javascript
const { 
  requirePermissions, 
  requireResourceOwnership,
  PERMISSIONS 
} = require('../middleware/unified-permission.middleware');
```

### 2. 在路由中使用权限验证

```javascript
// 单个权限验证
router.get('/flocks', 
  requirePermissions(PERMISSIONS.FLOCK_VIEW),
  getFlocks
);

// 多个权限验证（需要所有权限）
router.post('/flocks', 
  requirePermissions([
    PERMISSIONS.FLOCK_CREATE,
    PERMISSIONS.FLOCK_VIEW
  ]),
  createFlock
);

// 多个权限验证（需要任意一个权限）
router.get('/reports', 
  requirePermissions([
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.FINANCE_VIEW
  ], { requireAll: false }),
  getReports
);

// 资源所有权验证
router.put('/flocks/:id',
  requirePermissions(PERMISSIONS.FLOCK_UPDATE),
  requireResourceOwnership('id', 'userId'),
  updateFlock
);
```

### 3. 在控制器中使用权限检查

```javascript
const { UnifiedPermissionManager, PERMISSIONS } = require('../middleware/unified-permission.middleware');

async function someController(req, res) {
  // 动态权限检查
  if (!UnifiedPermissionManager.hasPermission(req.user, PERMISSIONS.DATA_EXPORT)) {
    return res.status(403).json({
      success: false,
      message: '没有数据导出权限'
    });
  }
  
  // 检查用户角色类型
  if (UnifiedPermissionManager.isPlatformAdmin(req.user)) {
    // 平台管理员逻辑
  } else if (UnifiedPermissionManager.isTenantAdmin(req.user)) {
    // 租户管理员逻辑
  }
  
  // 获取用户所有权限
  const userPermissions = UnifiedPermissionManager.getUserPermissions(req.user);
  
  // 业务逻辑...
}
```

## 📱 前端使用方法

### 1. 引入权限管理工具

```javascript
const { 
  UnifiedPermissionManager, 
  PERMISSIONS 
} = require('../../utils/unified-permission');
```

### 2. 在页面中使用权限验证

```javascript
Page({
  data: {
    canCreateFlock: false,
    canManageUsers: false,
    menuItems: []
  },

  onLoad() {
    this.initPermissions();
  },

  initPermissions() {
    // 检查单个权限
    const canCreate = UnifiedPermissionManager.hasPermission(PERMISSIONS.FLOCK_CREATE);
    const canManage = UnifiedPermissionManager.hasPermission(PERMISSIONS.USER_VIEW);
    
    // 检查多个权限（任意一个）
    const canAccessFinance = UnifiedPermissionManager.hasPermission([
      PERMISSIONS.FINANCE_VIEW,
      PERMISSIONS.FINANCE_CREATE
    ], { requireAll: false });

    this.setData({
      canCreateFlock: canCreate,
      canManageUsers: canManage,
      canAccessFinance: canAccessFinance
    });

    // 过滤菜单项
    this.filterMenuItems();
  },

  filterMenuItems() {
    const allMenuItems = [
      {
        title: '鹅群管理',
        path: '/pages/flocks/flocks',
        requiredPermissions: [PERMISSIONS.FLOCK_VIEW]
      },
      {
        title: '用户管理',
        path: '/pages/users/users',
        requiredPermissions: [PERMISSIONS.USER_VIEW]
      },
      {
        title: '财务管理',
        path: '/pages/finance/finance',
        feature: 'finance_management'
      },
      {
        title: '系统设置',
        path: '/pages/settings/settings',
        requiredRole: 'admin'
      }
    ];

    const accessibleItems = UnifiedPermissionManager.getAccessibleMenuItems(allMenuItems);
    this.setData({ menuItems: accessibleItems });
  },

  onCreateFlock() {
    // 验证操作权限
    if (!UnifiedPermissionManager.validateActionPermission(PERMISSIONS.FLOCK_CREATE)) {
      return; // 会自动显示权限不足提示
    }

    // 执行创建逻辑
    this.doCreateFlock();
  },

  onDeleteFlock() {
    // 验证操作权限（自定义提示）
    if (!UnifiedPermissionManager.validateActionPermission(
      PERMISSIONS.FLOCK_DELETE,
      {
        message: '您没有删除鹅群的权限，请联系管理员'
      }
    )) {
      return;
    }

    // 执行删除逻辑
    this.doDeleteFlock();
  }
});
```

### 3. 在WXML中使用权限控制

```xml
<!-- 条件渲染：根据权限显示/隐藏元素 -->
<view wx:if="{{canCreateFlock}}">
  <button bindtap="onCreateFlock">创建鹅群</button>
</view>

<view wx:if="{{canManageUsers}}">
  <navigator url="/pages/users/users">用户管理</navigator>
</view>

<!-- 动态菜单渲染 -->
<view wx:for="{{menuItems}}" wx:key="path">
  <navigator url="{{item.path}}">{{item.title}}</navigator>
</view>
```

### 4. 组件中使用权限验证

```javascript
Component({
  data: {
    showAdminActions: false
  },

  lifetimes: {
    attached() {
      this.checkPermissions();
    }
  },

  methods: {
    checkPermissions() {
      const isAdmin = UnifiedPermissionManager.isTenantAdmin();
      const hasManagePermission = UnifiedPermissionManager.hasPermission([
        PERMISSIONS.USER_MANAGE,
        PERMISSIONS.SETTINGS_MANAGE
      ], { requireAll: false });

      this.setData({
        showAdminActions: isAdmin || hasManagePermission
      });
    },

    onAdminAction() {
      if (!UnifiedPermissionManager.validateActionPermission(PERMISSIONS.SETTINGS_MANAGE)) {
        return;
      }
      
      // 执行管理员操作
    }
  }
});
```

## 🔄 迁移指南

### 1. 替换旧的权限检查

#### 旧方式
```javascript
// 旧的权限检查方式
const authHelper = require('../../utils/auth-helper');
if (authHelper.hasPermission('admin')) {
  // 逻辑
}
```

#### 新方式
```javascript
// 新的统一权限检查
const { UnifiedPermissionManager, PERMISSIONS } = require('../../utils/unified-permission');
if (UnifiedPermissionManager.hasRole('admin')) {
  // 逻辑
}
```

### 2. 更新中间件使用

#### 旧方式
```javascript
const { validatePermission } = require('../middleware/permission.middleware');
router.get('/data', validatePermission('data_view'), controller);
```

#### 新方式
```javascript
const { requirePermissions, PERMISSIONS } = require('../middleware/unified-permission.middleware');
router.get('/data', requirePermissions(PERMISSIONS.DATA_EXPORT), controller);
```

### 3. 批量替换权限常量

使用以下脚本自动替换旧的权限常量：

```bash
# 替换权限常量
find . -name "*.js" -exec sed -i 's/OA_ACCESS/PERMISSIONS.OA_ACCESS/g' {} \;
find . -name "*.js" -exec sed -i 's/FLOCK_READ/PERMISSIONS.FLOCK_VIEW/g' {} \;
# ... 更多替换规则
```

## 📝 权限配置管理

### 1. 角色权限自定义

```javascript
// 在数据库中存储自定义权限
const customPermissions = {
  userId: 123,
  role: 'manager',
  customPermissions: [
    PERMISSIONS.FINANCE_VIEW,  // 额外授予的权限
    PERMISSIONS.AI_CONFIG      // 特殊权限
  ],
  deniedPermissions: [
    PERMISSIONS.FLOCK_DELETE   // 撤销的默认权限
  ]
};
```

### 2. 动态权限分配

```javascript
// 根据业务需求动态分配权限
async function assignPermissionsBasedOnBusiness(user, business) {
  const basePermissions = ROLE_PERMISSIONS[user.role] || [];
  let additionalPermissions = [];

  // 根据业务类型添加权限
  if (business.type === 'breeding') {
    additionalPermissions.push(PERMISSIONS.HEALTH_CREATE);
  }
  if (business.scale === 'large') {
    additionalPermissions.push(PERMISSIONS.DATA_EXPORT);
  }

  return [...basePermissions, ...additionalPermissions];
}
```

## 🔍 调试和监控

### 1. 权限调试

```javascript
// 开发环境下的权限调试
if (process.env.NODE_ENV === 'development') {
  console.log('用户权限:', UnifiedPermissionManager.getUserPermissions(req.user));
  console.log('需要权限:', requiredPermissions);
  console.log('权限检查结果:', hasPermission);
}
```

### 2. 权限审计日志

```javascript
// 记录权限相关操作
const auditLog = {
  userId: req.user.id,
  action: 'PERMISSION_CHECK',
  resource: req.path,
  permissions: requiredPermissions,
  result: hasPermission ? 'GRANTED' : 'DENIED',
  timestamp: new Date(),
  userAgent: req.get('User-Agent'),
  ip: req.ip
};

await AuditLogger.log(auditLog);
```

## ⚠️ 注意事项

### 1. 安全考虑

- 权限检查必须在后端进行，前端权限仅用于界面控制
- 敏感操作必须进行双重权限验证
- 定期审计用户权限，及时回收不必要的权限

### 2. 性能优化

- 权限信息缓存在用户会话中
- 避免在循环中进行权限检查
- 使用权限预计算减少实时查询

### 3. 错误处理

- 提供友好的权限不足提示信息
- 记录权限拒绝事件用于分析
- 为特殊场景提供权限申请流程

## 📚 相关文件

- `backend/middleware/unified-permission.middleware.js` - 后端统一权限中间件
- `utils/unified-permission.js` - 前端统一权限工具
- `docs/unified-permission-guide.md` - 本使用指南

## 🆕 版本更新

### v1.0.0 (2024年12月)
- 初始版本，整合分散的权限系统
- 支持平台级和租户级权限控制
- 提供完整的前后端权限验证

### 后续规划
- 权限可视化管理界面
- 细粒度资源权限控制
- 权限模板和批量分配功能