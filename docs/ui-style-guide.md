# 智慧养鹅小程序UI样式指南

## 概述

本文档详细说明了智慧养鹅小程序的UI设计系统、组件使用规范和最佳实践。遵循本指南可确保整个应用的视觉一致性和用户体验的统一性。

## 🎨 设计系统

### 颜色系统

#### 主品牌色
- **主色调**: `--primary: #0066CC` (智慧蓝)
- **辅助色**: `--secondary: #0099CC` (科技蓝)
- **强调色**: `--accent: #FF6B35` (活力橙)

#### 语义色彩
- **成功**: `--success: #52C41A`
- **警告**: `--warning: #FAAD14`
- **错误**: `--error: #FF4D4F`
- **信息**: `--info: #1890FF`

#### OA权限专用色系
- **管理员**: `--permission-admin: #8E44AD`
- **经理**: `--permission-manager: #E67E22`
- **用户**: `--permission-user: #3498DB`
- **访客**: `--permission-guest: #95A5A6`
- **禁止**: `--permission-denied: #E74C3C`

### 字体系统

#### 字体大小级别
```css
--text-xs: 20rpx;    /* 辅助文本 */
--text-sm: 24rpx;    /* 小文本 */
--text-base: 28rpx;  /* 正文 */
--text-lg: 32rpx;    /* 大文本 */
--text-xl: 36rpx;    /* 标题 */
--text-2xl: 40rpx;   /* 大标题 */
```

#### 字体权重
- **Regular**: 400 (正文)
- **Medium**: 500 (重要文本)
- **Semibold**: 600 (副标题)
- **Bold**: 700 (主标题)

### 间距系统

基于8px网格系统：
```css
--space-xs: 4rpx;
--space-sm: 8rpx;
--space-md: 12rpx;
--space-lg: 16rpx;
--space-xl: 24rpx;
--space-2xl: 32rpx;
```

### 圆角系统

```css
--radius-sm: 4rpx;   /* 小圆角 */
--radius-md: 8rpx;   /* 中圆角 */
--radius-lg: 12rpx;  /* 大圆角 */
--radius-xl: 16rpx;  /* 超大圆角 */
--radius-2xl: 20rpx; /* 卡片圆角 */
--radius-full: 50%;  /* 完全圆形 */
```

## 🧩 组件系统

### 按钮组件

#### 基础用法
```xml
<button class="btn btn-primary btn-md">主要按钮</button>
<button class="btn btn-secondary btn-md">次要按钮</button>
<button class="btn btn-ghost btn-md">幽灵按钮</button>
```

#### 按钮类型
- **primary**: 主要操作按钮
- **secondary**: 次要操作按钮
- **success**: 成功确认按钮
- **warning**: 警告操作按钮
- **error**: 危险操作按钮
- **ghost**: 幽灵按钮
- **text**: 文本按钮

#### 按钮尺寸
- **xs**: 44rpx高度，适用于内联操作
- **sm**: 52rpx高度，适用于紧凑布局
- **md**: 64rpx高度，标准尺寸
- **lg**: 80rpx高度，适用于重要操作
- **xl**: 96rpx高度，适用于主要CTA

### 卡片组件

#### 基础用法
```xml
<view class="card card-lg shadow-md">
  <view class="card-header">
    <text class="text-h5">卡片标题</text>
  </view>
  <view class="card-body">
    <text>卡片内容</text>
  </view>
</view>
```

#### 卡片变体
- **card-sm**: 小尺寸卡片
- **card-md**: 标准卡片
- **card-lg**: 大尺寸卡片

### 权限指示器组件

#### 基础用法
```xml
<permission-indicator 
  level="admin" 
  status="granted" 
  description="管理员权限"
  show-action="{{true}}"
  bind:action="onPermissionAction">
</permission-indicator>
```

#### 权限级别
- **admin**: 管理员权限
- **manager**: 经理权限
- **user**: 普通用户权限
- **guest**: 访客权限
- **denied**: 拒绝访问

### 无权限页面组件

#### 基础用法
```xml
<permission-denied 
  title="访问受限"
  description="您需要管理员权限才能访问此功能"
  show-request-btn="{{true}}"
  bind:request="onRequestPermission">
</permission-denied>
```

## 📱 页面布局规范

### 页面容器
```xml
<view class="page-container">
  <view class="container">
    <!-- 页面内容 -->
  </view>
</view>
```

### OA模块布局
```xml
<view class="oa-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">OA办公</text>
    <text class="page-subtitle">智能办公管理系统</text>
  </view>
  
  <!-- 统计概览 -->
  <view class="stats-section">
    <view class="stats-grid">
      <!-- 统计卡片 -->
    </view>
  </view>
  
  <!-- 功能模块 -->
  <view class="modules-section">
    <!-- 功能模块列表 -->
  </view>
</view>
```

## 🎭 交互反馈

### 状态指示
- **loading**: 加载状态
- **disabled**: 禁用状态
- **active**: 激活状态
- **hover**: 悬停状态（移动端为:active）

### 动画效果
- **hover-lift**: 悬停上浮效果
- **scale-press**: 按压缩放效果
- **fade-in**: 淡入动画
- **slide-up**: 上滑动画

### Toast提示
```javascript
wx.showToast({
  title: '操作成功',
  icon: 'success',
  duration: 2000
});
```

## 🚀 最佳实践

### 1. 设计一致性
- 始终使用设计系统中定义的颜色、字体、间距
- 保持组件在不同页面中的一致性
- 遵循微信小程序设计规范

### 2. 性能优化
- 合理使用CSS变量，避免重复定义
- 优化动画性能，使用transform和opacity
- 避免深层嵌套的选择器

### 3. 无障碍访问
- 提供有意义的替代文本
- 确保足够的对比度
- 支持屏幕阅读器

### 4. 响应式设计
- 适配不同屏幕尺寸
- 处理安全区域
- 优化触摸目标尺寸

### 5. 权限系统UI
- 明确显示用户当前权限状态
- 提供清晰的权限申请流程
- 友好的无权限访问提示

## 📋 开发规范

### CSS类命名
- 使用BEM命名规范
- 组件前缀：c-（如：c-button）
- 状态修饰符：--（如：c-button--primary）
- 元素：__（如：c-card__header）

### 文件组织
```
components/
├── common/          # 通用组件
├── oa/             # OA专用组件
└── business/       # 业务组件

styles/
├── design-system.wxss  # 设计系统
├── components.wxss     # 组件样式
└── utilities.wxss      # 工具类
```

### 代码示例
```css
/* 良好的CSS结构 */
.module-item {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.module-item:active {
  transform: translateX(8rpx) scale(0.98);
  box-shadow: var(--shadow-lg);
}
```

## 🔧 维护指南

### 定期检查
- 检查设计系统的一致性
- 验证新组件是否符合规范
- 更新过时的样式定义

### 版本控制
- 记录重大设计变更
- 保持文档与代码同步
- 提供迁移指南

### 团队协作
- 统一设计语言
- 建立代码审查流程
- 定期进行设计系统培训

---

*本文档持续更新，如有疑问请联系开发团队。*