#!/usr/bin/env node

/**
 * 智慧养鹅系统 - 创建缺失数据表脚本
 * 执行SQL脚本创建所有缺失的核心数据表
 */

const mysql = require("mysql2/promise");
const fs = require("fs").promises;
const path = require("path");

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "zhihuiyange",
  password: process.env.DB_PASSWORD || "zhihuiyange123",
  database: process.env.DB_NAME || "zhihuiyange_local",
  multipleStatements: true,
};

async function createMissingTables() {
  let connection;

  try {
    console.log("🔗 连接数据库...");
    connection = await mysql.createConnection(dbConfig);
    console.log("✅ 数据库连接成功");

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, "create-missing-tables.sql");
    console.log("📖 读取SQL文件:", sqlFilePath);

    const sqlContent = await fs.readFile(sqlFilePath, "utf8");
    console.log("✅ SQL文件读取成功");

    // 执行SQL脚本
    console.log("🚀 开始执行SQL脚本...");
    const [results] = await connection.execute(sqlContent);
    console.log("✅ SQL脚本执行成功");

    // 验证表是否创建成功
    console.log("🔍 验证表创建结果...");
    const tables = [
      "tasks",
      "announcements",
      "announcement_reads",
      "price_records",
      "price_subscriptions",
      "inventory_records",
      "inventory_alerts",
      "materials",
      "material_stock_records",
      "material_alerts",
    ];

    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SHOW TABLES LIKE '${table}'`);
        if (rows.length > 0) {
          console.log(`✅ 表 ${table} 创建成功`);

          // 检查表结构
          const [columns] = await connection.execute(`DESCRIBE ${table}`);
          console.log(`   - 字段数量: ${columns.length}`);

          // 检查数据量
          const [count] = await connection.execute(
            `SELECT COUNT(*) as count FROM ${table}`,
          );
          console.log(`   - 数据条数: ${count[0].count}`);
        } else {
          console.log(`❌ 表 ${table} 创建失败`);
        }
      } catch (error) {
        console.log(`❌ 验证表 ${table} 时出错:`, error.message);
      }
    }

    console.log("\n🎉 数据库表结构创建完成！");
    console.log("\n📋 创建的表列表:");
    tables.forEach((table, index) => {
      console.log(`${index + 1}. ${table}`);
    });
  } catch (error) {
    console.error("❌ 执行过程中出现错误:", error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 数据库连接已关闭");
    }
  }
}

// 主函数
async function main() {
  console.log("🚀 智慧养鹅系统 - 数据库表结构创建工具");
  console.log("=".repeat(50));

  try {
    await createMissingTables();
    console.log("\n✅ 所有操作完成成功！");
    process.exit(0);
  } catch (error) {
    console.error("\n❌ 操作失败:", error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { createMissingTables };
