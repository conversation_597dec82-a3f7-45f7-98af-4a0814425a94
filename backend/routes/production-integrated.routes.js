/**
 * 生产记录路由（集成财务功能）
 * 处理入栏、称重、出栏记录，并自动生成相应的财务记录
 */

const express = require('express');
const router = express.Router();
const productionController = require('../controllers/production-integrated.controller');
const { verifyToken } = require('../middleware/auth.middleware');
const { validatePermission } = require('../middleware/permission.middleware');

// 应用认证中间件
router.use(verifyToken);

// ================================
// 生产记录创建（带财务集成）
// ================================

/**
 * 创建入栏记录（自动生成支出记录）
 * POST /api/v1/production/entry-records
 * 请求体:
 * {
 *   "batchNumber": "批次号（可选，不提供则自动生成）",
 *   "breed": "品种代码",
 *   "count": 数量,
 *   "weight": 总重量,
 *   "source": "来源",
 *   "supplier": "供应商",
 *   "unitCost": 单价,
 *   "totalCost": 总成本,
 *   "recordDate": "记录日期",
 *   "notes": "备注",
 *   "shedNumber": "棚舍号",
 *   "dayAge": 日龄,
 *   "healthStatus": "健康状态"
 * }
 */
router.post('/entry-records', 
  validatePermission('production_create'), 
  async (req, res) => {
    try {
      // 如果没有提供批次号，自动生成
      if (!req.body.batchNumber) {
        req.body.batchNumber = await productionController.generateUniqueBatchNumber(req.user.id);
      }
      
      return await productionController.createEntryRecord(req, res);
    } catch (error) {
      console.error('创建入栏记录失败:', error);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.error(res, '创建入栏记录失败', 500, error.message);
    }
  }
);

/**
 * 创建出栏记录（自动生成收入记录）
 * POST /api/v1/production/sale-records
 * 请求体:
 * {
 *   "batchNumber": "批次号",
 *   "count": 出栏数量,
 *   "weight": 总重量,
 *   "unitPrice": 单价,
 *   "totalIncome": 总收入,
 *   "buyer": "买家",
 *   "recordDate": "记录日期",
 *   "notes": "备注",
 *   "averageWeight": 平均体重
 * }
 */
router.post('/sale-records', 
  validatePermission('production_create'), 
  productionController.createSaleRecord
);

/**
 * 创建称重记录
 * POST /api/v1/production/weight-records
 * 请求体:
 * {
 *   "batchNumber": "批次号",
 *   "count": 称重数量,
 *   "averageWeight": 平均体重,
 *   "totalWeight": 总重量,
 *   "recordDate": "记录日期",
 *   "notes": "备注",
 *   "feedRatio": "料肉比"
 * }
 */
router.post('/weight-records', 
  validatePermission('production_create'), 
  productionController.createWeightRecord
);

// ================================
// 生产记录查询
// ================================

/**
 * 获取生产记录列表（支持类型筛选）
 * GET /api/v1/production/records
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - limit: 每页数量 (默认: 20)
 * - type: 记录类型 (entry/weight/sale)
 * - batchNumber: 批次号
 * - startDate: 开始日期
 * - endDate: 结束日期
 * - status: 状态
 */
router.get('/records', 
  validatePermission('production_view'), 
  productionController.getProductionRecords
);

/**
 * 获取批次详细信息（包含财务统计）
 * GET /api/v1/production/batch-details/:batchNumber
 */
router.get('/batch-details/:batchNumber', 
  validatePermission('production_view'), 
  productionController.getBatchDetails
);

/**
 * 获取活跃批次列表（用于选择器）
 * GET /api/v1/production/active-batches
 */
router.get('/active-batches', 
  validatePermission('production_view'), 
  productionController.getActiveBatches
);

// ================================
// 批次管理
// ================================

/**
 * 生成新的批次号
 * GET /api/v1/production/generate-batch-number
 */
router.get('/generate-batch-number', 
  validatePermission('production_create'), 
  async (req, res) => {
    try {
      const batchNumber = await productionController.generateUniqueBatchNumber(req.user.id);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.success(res, { batchNumber });
    } catch (error) {
      console.error('生成批次号失败:', error);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.error(res, '生成批次号失败', 500, error.message);
    }
  }
);

/**
 * 获取批次统计概览
 * GET /api/v1/production/batch-overview
 */
router.get('/batch-overview', 
  validatePermission('production_view'), 
  async (req, res) => {
    try {
      const userId = req.user.id;
      const db = require('../config/database');

      // 获取所有批次的统计信息
      const [batches] = await db.query(`
        SELECT 
          f.batchNumber,
          f.name,
          f.breed,
          f.totalCount,
          f.currentCount,
          f.status,
          f.establishedDate,
          f.total_cost,
          f.total_revenue,
          (f.total_revenue - f.total_cost) as net_profit,
          COUNT(pr.id) as production_record_count,
          COUNT(fr.id) as finance_record_count,
          MAX(pr.date) as last_production_date,
          MAX(fr.recordDate) as last_finance_date
        FROM flocks f
        LEFT JOIN production_records_v2 pr ON f.batchNumber = pr.batch AND f.userId = pr.user_id
        LEFT JOIN financial_records fr ON f.batchNumber = fr.batch_number AND f.userId = fr.userId
        WHERE f.userId = ?
        GROUP BY f.batchNumber, f.name, f.breed, f.totalCount, f.currentCount, 
                 f.status, f.establishedDate, f.total_cost, f.total_revenue
        ORDER BY f.establishedDate DESC
      `, [userId]);

      const overview = batches.map(batch => {
        const totalCost = parseFloat(batch.total_cost) || 0;
        const totalRevenue = parseFloat(batch.total_revenue) || 0;
        const netProfit = totalRevenue - totalCost;
        const profitMargin = totalCost > 0 ? (netProfit / totalCost) * 100 : 0;

        return {
          batchNumber: batch.batchNumber,
          name: batch.name,
          breed: batch.breed,
          totalCount: batch.totalCount,
          currentCount: batch.currentCount,
          status: batch.status,
          establishedDate: batch.establishedDate,
          totalCost,
          totalRevenue,
          netProfit,
          profitMargin: Math.round(profitMargin * 100) / 100,
          productionRecordCount: batch.production_record_count,
          financeRecordCount: batch.finance_record_count,
          lastProductionDate: batch.last_production_date,
          lastFinanceDate: batch.last_finance_date
        };
      });

      // 计算总体统计
      const summary = overview.reduce((acc, batch) => {
        acc.totalBatches++;
        acc.totalGeese += batch.totalCount;
        acc.currentGeese += batch.currentCount;
        acc.totalCost += batch.totalCost;
        acc.totalRevenue += batch.totalRevenue;
        acc.totalProfit += batch.netProfit;
        
        if (batch.status === 'active') {
          acc.activeBatches++;
        }
        
        return acc;
      }, {
        totalBatches: 0,
        activeBatches: 0,
        totalGeese: 0,
        currentGeese: 0,
        totalCost: 0,
        totalRevenue: 0,
        totalProfit: 0
      });

      summary.overallProfitMargin = summary.totalCost > 0 ? 
        Math.round((summary.totalProfit / summary.totalCost) * 10000) / 100 : 0;

      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.success(res, {
        batches: overview,
        summary
      });

    } catch (error) {
      console.error('获取批次概览失败:', error);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.error(res, '获取批次概览失败', 500, error.message);
    }
  }
);

// ================================
// 数据验证和业务规则
// ================================

/**
 * 验证批次操作的有效性
 * POST /api/v1/production/validate-batch-operation
 * 请求体:
 * {
 *   "batchNumber": "批次号",
 *   "operation": "操作类型 (sale/weight)",
 *   "count": 数量
 * }
 */
router.post('/validate-batch-operation', 
  validatePermission('production_view'), 
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { batchNumber, operation, count } = req.body;

      if (!batchNumber || !operation || !count) {
        const ResponseHelper = require('../utils/responseHelper');
        return ResponseHelper.error(res, '缺少必填字段', 400);
      }

      const db = require('../config/database');
      const [batchInfo] = await db.query(`
        SELECT currentCount, status FROM flocks 
        WHERE batchNumber = ? AND userId = ?
      `, [batchNumber, userId]);

      if (batchInfo.length === 0) {
        const ResponseHelper = require('../utils/responseHelper');
        return ResponseHelper.error(res, '批次不存在', 404);
      }

      const batch = batchInfo[0];
      let isValid = true;
      let message = '操作有效';
      let maxAllowed = batch.currentCount;

      // 验证业务规则
      if (batch.status !== 'active') {
        isValid = false;
        message = '批次状态不是活跃状态，无法进行操作';
      } else if (operation === 'sale' && count > batch.currentCount) {
        isValid = false;
        message = `出栏数量不能超过当前存栏数(${batch.currentCount}只)`;
      } else if (operation === 'weight' && count > batch.currentCount) {
        isValid = false;
        message = `称重数量不能超过当前存栏数(${batch.currentCount}只)`;
      }

      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.success(res, {
        isValid,
        message,
        batchInfo: {
          batchNumber,
          currentCount: batch.currentCount,
          status: batch.status,
          maxAllowed
        }
      });

    } catch (error) {
      console.error('验证批次操作失败:', error);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.error(res, '验证批次操作失败', 500, error.message);
    }
  }
);

module.exports = router;
