// 本地环境数据库初始化脚本
const { Sequelize } = require("sequelize");
const bcrypt = require("bcryptjs");

// 直接使用本地配置
const sequelize = new Sequelize(
  "zhihuiyange_local",
  "zhihuiyange",
  "zhihuiyange123",
  {
    host: "127.0.0.1",
    port: 3306,
    dialect: "mysql",
    logging: true,
  },
);

// 简单的用户模型定义
const User = sequelize.define("user", {
  username: {
    type: Sequelize.STRING,
    unique: true,
    allowNull: false,
  },
  password: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  email: {
    type: Sequelize.STRING,
    unique: true,
    allowNull: false,
  },
  role: {
    type: Sequelize.ENUM("admin", "user", "manager"),
    defaultValue: "user",
  },
});

async function initDatabase() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log("数据库连接成功");

    // 同步模型到数据库
    await sequelize.sync({ alter: true });
    console.log("数据库表结构同步完成");

    // 创建测试数据
    await createTestData();

    console.log("数据库初始化完成");
    process.exit(0);
  } catch (error) {
    console.error("数据库初始化失败:", error);
    process.exit(1);
  }
}

async function createTestData() {
  try {
    // 生成密码哈希
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash("admin123", saltRounds);

    // 创建测试用户
    const [testUser, created] = await User.findOrCreate({
      where: { username: "admin" },
      defaults: {
        username: "admin",
        password: hashedPassword, // 使用正确哈希的密码
        email: "<EMAIL>",
        role: "admin",
      },
    });

    console.log("测试用户创建完成");
  } catch (error) {
    console.error("创建测试数据失败:", error);
  }
}

// 执行初始化
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
