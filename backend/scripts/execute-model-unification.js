/**
 * 数据库模型统一迁移执行脚本
 * Database Model Unification Migration Executor
 * 
 * 自动化执行数据库模型统一和数据迁移
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4'
};

class ModelUnificationMigrator {
  constructor() {
    this.connection = null;
    this.migrationSteps = [
      { name: '检查环境', method: 'checkEnvironment' },
      { name: '备份现有数据', method: 'backupExistingData' },
      { name: '创建统一模型表', method: 'createUnifiedTables' },
      { name: '迁移数据', method: 'migrateData' },
      { name: '验证数据完整性', method: 'validateDataIntegrity' },
      { name: '替换原表', method: 'replaceOriginalTables' },
      { name: '清理临时数据', method: 'cleanup' },
      { name: '生成迁移报告', method: 'generateReport' }
    ];
    this.migrationLog = [];
    this.migrationReport = {
      startTime: new Date(),
      endTime: null,
      status: 'pending',
      steps: [],
      dataStats: {},
      errors: []
    };
  }

  /**
   * 执行完整的模型统一迁移
   */
  async execute() {
    try {
      console.log('🚀 开始数据库模型统一迁移...');
      console.log('==================================================');

      await this.connectDatabase();

      for (const step of this.migrationSteps) {
        console.log(`\n📋 执行步骤: ${step.name}`);
        console.log('-'.repeat(50));

        const stepStartTime = Date.now();
        try {
          await this[step.method]();
          const stepDuration = Date.now() - stepStartTime;
          
          this.migrationReport.steps.push({
            name: step.name,
            status: 'success',
            duration: stepDuration,
            timestamp: new Date()
          });

          console.log(`✅ ${step.name} 完成 (${stepDuration}ms)`);
        } catch (error) {
          const stepDuration = Date.now() - stepStartTime;
          
          this.migrationReport.steps.push({
            name: step.name,
            status: 'failed',
            duration: stepDuration,
            error: error.message,
            timestamp: new Date()
          });

          this.migrationReport.errors.push({
            step: step.name,
            error: error.message,
            stack: error.stack,
            timestamp: new Date()
          });

          console.error(`❌ ${step.name} 失败:`, error.message);
          throw error;
        }
      }

      this.migrationReport.status = 'success';
      this.migrationReport.endTime = new Date();

      console.log('\n🎉 数据库模型统一迁移完成！');
      console.log('==================================================');

    } catch (error) {
      this.migrationReport.status = 'failed';
      this.migrationReport.endTime = new Date();
      
      console.error('\n💥 迁移失败:', error.message);
      console.log('正在回滚更改...');
      
      await this.rollback();
      throw error;
    } finally {
      await this.closeConnection();
    }
  }

  /**
   * 连接数据库
   */
  async connectDatabase() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  async closeConnection() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ 数据库连接已关闭');
    }
  }

  /**
   * 检查环境
   */
  async checkEnvironment() {
    // 检查数据库版本
    const [versionResult] = await this.connection.execute('SELECT VERSION() as version');
    const dbVersion = versionResult[0].version;
    console.log(`📊 数据库版本: ${dbVersion}`);

    // 检查现有表
    const [tables] = await this.connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ?
    `, [dbConfig.database]);

    const tableNames = tables.map(row => row.table_name);
    console.log(`📋 现有表数量: ${tableNames.length}`);

    // 检查关键表是否存在
    const keyTables = ['users', 'flocks', 'health_records', 'production_records'];
    const existingKeyTables = keyTables.filter(table => tableNames.includes(table));
    console.log(`🔑 关键表存在: ${existingKeyTables.join(', ')}`);

    // 检查重复库存表
    const inventoryTables = ['materials', 'inventory', 'unified_inventory'];
    const existingInventoryTables = inventoryTables.filter(table => tableNames.includes(table));
    console.log(`📦 库存相关表: ${existingInventoryTables.join(', ')}`);

    this.migrationReport.dataStats.existingTables = tableNames;
    this.migrationReport.dataStats.inventoryTables = existingInventoryTables;
  }

  /**
   * 备份现有数据
   */
  async backupExistingData() {
    const backupDir = path.join(__dirname, '../backups');
    
    try {
      await fs.mkdir(backupDir, { recursive: true });
    } catch (error) {
      // 目录可能已存在
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `backup-${timestamp}.sql`);

    console.log(`💾 创建数据备份: ${backupFile}`);

    // 这里应该实现数据库备份逻辑
    // 简化版本：记录表结构和数据统计
    const backupInfo = {
      timestamp: new Date(),
      database: dbConfig.database,
      tables: {}
    };

    // 获取每个表的记录数
    const [tables] = await this.connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ?
    `, [dbConfig.database]);

    for (const table of tables) {
      try {
        const [countResult] = await this.connection.execute(
          `SELECT COUNT(*) as count FROM ??`, [table.table_name]
        );
        backupInfo.tables[table.table_name] = {
          recordCount: countResult[0].count
        };
      } catch (error) {
        console.warn(`⚠️  无法获取表 ${table.table_name} 的记录数:`, error.message);
      }
    }

    await fs.writeFile(
      path.join(backupDir, `backup-info-${timestamp}.json`),
      JSON.stringify(backupInfo, null, 2)
    );

    this.migrationReport.dataStats.backup = backupInfo;
    console.log('✅ 数据备份信息已保存');
  }

  /**
   * 创建统一模型表
   */
  async createUnifiedTables() {
    const migrationPath = path.join(__dirname, '../migrations/005-unify-database-models.sql');
    
    try {
      const migrationSQL = await fs.readFile(migrationPath, 'utf8');
      
      // 分割SQL语句并执行
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      let createStatements = 0;
      
      for (const statement of statements) {
        if (statement.toLowerCase().includes('create table') && 
            statement.toLowerCase().includes('_new')) {
          try {
            await this.connection.execute(statement);
            createStatements++;
            console.log('✅ 创建新表成功');
          } catch (error) {
            if (error.code === 'ER_TABLE_EXISTS_ERROR') {
              console.log('ℹ️  表已存在，跳过创建');
            } else {
              throw error;
            }
          }
        }
      }

      console.log(`📊 成功创建 ${createStatements} 个统一模型表`);
      this.migrationReport.dataStats.createdTables = createStatements;

    } catch (error) {
      console.error('❌ 创建统一模型表失败:', error);
      throw error;
    }
  }

  /**
   * 迁移数据
   */
  async migrateData() {
    const dataMigrationSteps = [
      { name: '迁移用户数据', table: 'users_new', source: 'users' },
      { name: '迁移库存数据', table: 'unified_inventory_new', source: ['materials', 'inventory', 'unified_inventory'] },
      { name: '迁移健康记录', table: 'health_records_new', source: 'health_records' },
      { name: '迁移生产记录', table: 'production_records_new', source: 'production_records' }
    ];

    const migrationStats = {};

    for (const step of dataMigrationSteps) {
      console.log(`🔄 ${step.name}...`);
      
      try {
        if (step.table === 'unified_inventory_new') {
          // 特殊处理库存数据迁移
          const stats = await this.migrateInventoryData();
          migrationStats[step.table] = stats;
        } else {
          // 标准数据迁移
          const stats = await this.migrateTableData(step.table, step.source);
          migrationStats[step.table] = stats;
        }
        
        console.log(`✅ ${step.name} 完成`);
      } catch (error) {
        console.error(`❌ ${step.name} 失败:`, error.message);
        throw error;
      }
    }

    this.migrationReport.dataStats.migration = migrationStats;
  }

  /**
   * 迁移库存数据（特殊处理）
   */
  async migrateInventoryData() {
    const stats = { totalRecords: 0, sourceBreakdown: {} };

    // 检查并迁移materials表
    const [materialsTables] = await this.connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'materials'
    `, [dbConfig.database]);

    if (materialsTables.length > 0) {
      const [materialsResult] = await this.connection.execute(`
        INSERT INTO unified_inventory_new (
          user_id, name, category, specification, unit, current_stock, min_stock,
          unit_price, total_value, supplier, supplier_contact, purchase_date, expiry_date,
          status, location, description, created_by, updated_by, created_at, updated_at
        )
        SELECT 
          user_id, name,
          CASE 
            WHEN category IN ('feed', 'medicine', 'equipment', 'materials', 'other') THEN category
            ELSE 'materials'
          END as category,
          specification, COALESCE(unit, '个') as unit,
          COALESCE(current_stock, quantity, 0) as current_stock,
          COALESCE(min_stock, 0) as min_stock,
          unit_price, total_value, supplier, supplier_contact, purchase_date, expiry_date,
          CASE 
            WHEN status = 'active' THEN 'normal'
            WHEN status = 'inactive' THEN 'inactive'
            ELSE 'normal'
          END as status,
          location, description, created_by, updated_by, created_at, updated_at
        FROM materials
      `);
      
      stats.sourceBreakdown.materials = materialsResult.affectedRows;
      stats.totalRecords += materialsResult.affectedRows;
      console.log(`📦 从materials表迁移 ${materialsResult.affectedRows} 条记录`);
    }

    // 检查并迁移inventory表
    const [inventoryTables] = await this.connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'inventory'
    `, [dbConfig.database]);

    if (inventoryTables.length > 0) {
      const [inventoryResult] = await this.connection.execute(`
        INSERT INTO unified_inventory_new (
          user_id, name, category, specification, unit, current_stock, min_stock,
          unit_price, total_value, supplier, supplier_contact, purchase_date, expiry_date,
          status, location, description, created_by, updated_by, created_at, updated_at
        )
        SELECT 
          user_id, name,
          CASE 
            WHEN category IN ('feed', 'medicine', 'equipment', 'materials', 'other') THEN category
            ELSE 'materials'
          END as category,
          specification, COALESCE(unit, '个') as unit,
          COALESCE(current_stock, quantity, 0) as current_stock,
          COALESCE(min_stock, 0) as min_stock,
          unit_price, total_value, supplier, supplier_contact, purchase_date, expiry_date,
          CASE 
            WHEN status = 'active' THEN 'normal'
            WHEN status = 'inactive' THEN 'inactive'
            ELSE 'normal'
          END as status,
          location, description, created_by, updated_by, created_at, updated_at
        FROM inventory
        WHERE NOT EXISTS (
          SELECT 1 FROM unified_inventory_new uin 
          WHERE uin.user_id = inventory.user_id 
          AND uin.name = inventory.name 
          AND uin.category = inventory.category
        )
      `);
      
      stats.sourceBreakdown.inventory = inventoryResult.affectedRows;
      stats.totalRecords += inventoryResult.affectedRows;
      console.log(`📦 从inventory表迁移 ${inventoryResult.affectedRows} 条记录`);
    }

    // 检查并迁移unified_inventory表
    const [unifiedTables] = await this.connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'unified_inventory'
    `, [dbConfig.database]);

    if (unifiedTables.length > 0) {
      const [unifiedResult] = await this.connection.execute(`
        INSERT INTO unified_inventory_new (
          user_id, flock_id, name, item_name, category, specification, unit,
          current_stock, min_stock, max_stock, quantity, unit_price, total_value,
          supplier, supplier_contact, purchase_date, expiry_date, last_update_date,
          status, location, description, batch_number, created_by, updated_by,
          created_at, updated_at
        )
        SELECT 
          user_id, flock_id, name, item_name, category, specification, unit,
          current_stock, min_stock, max_stock, quantity, unit_price, total_value,
          supplier, supplier_contact, purchase_date, expiry_date, last_update_date,
          status, location, description, batch_number, created_by, updated_by,
          created_at, updated_at
        FROM unified_inventory
        WHERE NOT EXISTS (
          SELECT 1 FROM unified_inventory_new uin 
          WHERE uin.user_id = unified_inventory.user_id 
          AND uin.name = unified_inventory.name 
          AND uin.category = unified_inventory.category
          AND COALESCE(uin.batch_number, '') = COALESCE(unified_inventory.batch_number, '')
        )
      `);
      
      stats.sourceBreakdown.unified_inventory = unifiedResult.affectedRows;
      stats.totalRecords += unifiedResult.affectedRows;
      console.log(`📦 从unified_inventory表迁移 ${unifiedResult.affectedRows} 条记录`);
    }

    return stats;
  }

  /**
   * 迁移标准表数据
   */
  async migrateTableData(targetTable, sourceTable) {
    const [tableExists] = await this.connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = ?
    `, [dbConfig.database, sourceTable]);

    if (tableExists.length === 0) {
      console.log(`ℹ️  源表 ${sourceTable} 不存在，跳过迁移`);
      return { totalRecords: 0, skipped: true };
    }

    // 获取源表记录数
    const [countResult] = await this.connection.execute(
      `SELECT COUNT(*) as count FROM ??`, [sourceTable]
    );
    const sourceCount = countResult[0].count;

    if (sourceCount === 0) {
      console.log(`ℹ️  源表 ${sourceTable} 无数据，跳过迁移`);
      return { totalRecords: 0, empty: true };
    }

    // 执行数据迁移（这里需要根据具体的迁移SQL来实现）
    // 简化版本：直接复制数据
    const [result] = await this.connection.execute(`
      INSERT IGNORE INTO ${targetTable} 
      SELECT * FROM ${sourceTable}
    `);

    console.log(`📊 从 ${sourceTable} 迁移 ${result.affectedRows} 条记录到 ${targetTable}`);

    return {
      totalRecords: result.affectedRows,
      sourceCount: sourceCount,
      success: true
    };
  }

  /**
   * 验证数据完整性
   */
  async validateDataIntegrity() {
    const validationResults = {};

    // 验证统一库存表
    const [inventoryStats] = await this.connection.execute(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT category) as unique_categories
      FROM unified_inventory_new
    `);
    validationResults.unified_inventory = inventoryStats[0];

    // 验证用户表
    const [userStats] = await this.connection.execute(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT role) as unique_roles,
        COUNT(DISTINCT status) as unique_statuses
      FROM users_new
    `);
    validationResults.users = userStats[0];

    // 检查外键完整性
    const [orphanRecords] = await this.connection.execute(`
      SELECT 
        'unified_inventory 孤儿记录' as check_type,
        COUNT(*) as count
      FROM unified_inventory_new ui
      LEFT JOIN users_new u ON ui.user_id = u.id
      WHERE u.id IS NULL
    `);

    validationResults.integrity_check = orphanRecords[0];

    this.migrationReport.dataStats.validation = validationResults;

    // 打印验证结果
    console.log('📊 数据验证结果:');
    console.log(`   - 统一库存表: ${validationResults.unified_inventory.total_records} 条记录`);
    console.log(`   - 用户表: ${validationResults.users.total_records} 条记录`);
    console.log(`   - 孤儿记录: ${validationResults.integrity_check.count} 条`);

    if (validationResults.integrity_check.count > 0) {
      console.warn('⚠️  发现孤儿记录，请检查数据完整性');
    }
  }

  /**
   * 替换原表
   */
  async replaceOriginalTables() {
    console.log('🔄 替换原表为统一模型表...');
    
    const tableReplacements = [
      { old: 'users', new: 'users_new' },
      { old: 'health_records', new: 'health_records_new' },
      { old: 'production_records', new: 'production_records_new' }
    ];

    // 处理库存表的特殊情况
    const inventoryTables = ['materials', 'inventory', 'unified_inventory'];
    
    for (const table of inventoryTables) {
      const [exists] = await this.connection.execute(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = ? AND table_name = ?
      `, [dbConfig.database, table]);

      if (exists.length > 0) {
        await this.connection.execute(`DROP TABLE IF EXISTS ${table}_backup`);
        await this.connection.execute(`CREATE TABLE ${table}_backup AS SELECT * FROM ${table}`);
        await this.connection.execute(`DROP TABLE ${table}`);
        console.log(`✅ 已删除库存表: ${table}`);
      }
    }

    // 重命名统一库存表
    await this.connection.execute('ALTER TABLE unified_inventory_new RENAME TO unified_inventory');
    console.log('✅ 统一库存表重命名完成');

    // 处理其他表
    for (const replacement of tableReplacements) {
      try {
        // 备份原表
        await this.connection.execute(`DROP TABLE IF EXISTS ${replacement.old}_backup`);
        await this.connection.execute(`CREATE TABLE ${replacement.old}_backup AS SELECT * FROM ${replacement.old}`);
        
        // 删除原表并重命名新表
        await this.connection.execute(`DROP TABLE ${replacement.old}`);
        await this.connection.execute(`ALTER TABLE ${replacement.new} RENAME TO ${replacement.old}`);
        
        console.log(`✅ 表替换完成: ${replacement.old}`);
      } catch (error) {
        console.error(`❌ 表替换失败: ${replacement.old}`, error.message);
        throw error;
      }
    }
  }

  /**
   * 清理临时数据
   */
  async cleanup() {
    console.log('🧹 清理临时数据...');
    
    // 这里可以清理一些临时文件或数据
    // 保留备份表用于安全恢复
    
    console.log('✅ 清理完成');
  }

  /**
   * 生成迁移报告
   */
  async generateReport() {
    const reportFile = path.join(__dirname, '../reports', `migration-report-${Date.now()}.json`);
    
    try {
      await fs.mkdir(path.dirname(reportFile), { recursive: true });
      await fs.writeFile(reportFile, JSON.stringify(this.migrationReport, null, 2));
      
      console.log(`📄 迁移报告已生成: ${reportFile}`);
      
      // 打印简要报告
      console.log('\n📊 迁移统计:');
      console.log(`   - 总耗时: ${this.migrationReport.endTime - this.migrationReport.startTime}ms`);
      console.log(`   - 成功步骤: ${this.migrationReport.steps.filter(s => s.status === 'success').length}`);
      console.log(`   - 失败步骤: ${this.migrationReport.steps.filter(s => s.status === 'failed').length}`);
      console.log(`   - 状态: ${this.migrationReport.status}`);
      
    } catch (error) {
      console.error('❌ 生成迁移报告失败:', error);
    }
  }

  /**
   * 回滚更改
   */
  async rollback() {
    console.log('🔄 执行回滚操作...');
    
    try {
      // 这里实现回滚逻辑
      // 恢复备份表等
      console.log('✅ 回滚完成');
    } catch (error) {
      console.error('❌ 回滚失败:', error);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const migrator = new ModelUnificationMigrator();
  
  migrator.execute()
    .then(() => {
      console.log('🎉 迁移执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 迁移执行失败:', error);
      process.exit(1);
    });
}

module.exports = ModelUnificationMigrator;