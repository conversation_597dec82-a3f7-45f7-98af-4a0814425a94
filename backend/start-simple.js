const express = require("express");
const cors = require("cors");
const path = require("path");

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use("/admin", express.static(path.join(__dirname, "admin")));

// 简单的测试路由
app.get("/api/v1/test", (req, res) => {
  res.json({
    success: true,
    message: "服务器运行正常",
    timestamp: new Date().toISOString(),
  });
});

// 生产记录V2 API
app.get("/api/v1/production-records/v2", (req, res) => {
  // 模拟数据
  const mockRecords = [
    {
      id: 1,
      userId: 1,
      type: "growth",
      batch: "GE2023061501",
      date: "2023-06-15",
      details: {
        weight: 2.5,
        ratio: "2.8:1",
      },
      notes: "生长情况良好",
      status: "submitted",
      createdAt: "2023-06-15T10:00:00Z",
      updatedAt: "2023-06-15T10:00:00Z",
    },
    {
      id: 2,
      userId: 1,
      type: "weight",
      batch: "GE2023060801",
      date: "2023-06-14",
      details: {
        count: 500,
        weight: 2.3,
      },
      notes: "称重记录",
      status: "submitted",
      createdAt: "2023-06-14T10:00:00Z",
      updatedAt: "2023-06-14T10:00:00Z",
    },
    {
      id: 3,
      userId: 1,
      type: "sale",
      batch: "GE2023060501",
      date: "2023-06-05",
      details: {
        count: 200,
        weight: 3.2,
      },
      notes: "出栏记录",
      status: "submitted",
      createdAt: "2023-06-05T10:00:00Z",
      updatedAt: "2023-06-05T10:00:00Z",
    },
  ];

  res.json({
    success: true,
    data: {
      records: mockRecords,
      pagination: {
        page: 1,
        limit: 10,
        total: mockRecords.length,
        pages: 1,
      },
    },
  });
});

// 创建生产记录V2
app.post("/api/v1/production-records/v2", (req, res) => {
  const { type, batch, date, weight, ratio, count, notes } = req.body;

  // 模拟创建记录
  const newRecord = {
    id: Date.now(),
    userId: 1,
    type: type,
    batch: batch,
    date: date,
    details: {},
    notes: notes || "",
    status: "submitted",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // 根据类型设置详情
  if (type === "growth") {
    newRecord.details = {
      weight: parseFloat(weight) || 0,
      ratio: ratio || "",
    };
  } else if (type === "weight") {
    newRecord.details = {
      count: parseInt(count) || 0,
      weight: parseFloat(weight) || 0,
    };
  } else if (type === "sale") {
    newRecord.details = {
      count: parseInt(count) || 0,
      weight: parseFloat(weight) || 0,
    };
  }

  res.status(201).json({
    success: true,
    message: "生产记录创建成功",
    data: newRecord,
  });
});

// 物料管理 API
app.get("/api/v1/materials", (req, res) => {
  // 模拟数据
  const mockMaterials = [
    {
      id: 1,
      name: "雏鹅专用饲料",
      category: "feed",
      spec: "25kg/袋",
      stock: 120,
      unit: "袋",
      minStock: 20,
      unitPrice: 85.0,
      supplier: "优质饲料公司",
      status: "normal",
      createdAt: "2023-06-01T10:00:00Z",
      updatedAt: "2023-06-01T10:00:00Z",
    },
    {
      id: 2,
      name: "育肥期饲料",
      category: "feed",
      spec: "25kg/袋",
      stock: 45,
      unit: "袋",
      minStock: 30,
      unitPrice: 78.0,
      supplier: "优质饲料公司",
      status: "warning",
      createdAt: "2023-05-15T10:00:00Z",
      updatedAt: "2023-05-15T10:00:00Z",
    },
    {
      id: 3,
      name: "抗生素",
      category: "medicine",
      spec: "100ml/瓶",
      stock: 15,
      unit: "瓶",
      minStock: 20,
      unitPrice: 25.0,
      supplier: "兽药公司",
      status: "danger",
      createdAt: "2023-04-10T10:00:00Z",
      updatedAt: "2023-04-10T10:00:00Z",
    },
  ];

  res.json({
    success: true,
    data: {
      materials: mockMaterials,
      pagination: {
        page: 1,
        limit: 10,
        total: mockMaterials.length,
        pages: 1,
      },
    },
  });
});

// 创建物料
app.post("/api/v1/materials", (req, res) => {
  const {
    name,
    category,
    spec,
    stock,
    unit,
    minStock,
    unitPrice,
    supplier,
    description,
  } = req.body;

  // 计算状态
  let status = "normal";
  const stockNum = parseInt(stock) || 0;
  const minStockNum = parseInt(minStock) || 20;

  if (stockNum <= 0) {
    status = "danger";
  } else if (stockNum <= minStockNum) {
    status = "warning";
  }

  const newMaterial = {
    id: Date.now(),
    name: name,
    category: category,
    spec: spec,
    stock: stockNum,
    unit: unit || "个",
    minStock: minStockNum,
    unitPrice: parseFloat(unitPrice) || 0,
    supplier: supplier || "",
    description: description || "",
    status: status,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  res.status(201).json({
    success: true,
    message: "物料创建成功",
    data: newMaterial,
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: "服务器内部错误",
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "接口不存在",
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 简化服务器启动成功！`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔧 测试接口: http://localhost:${PORT}/api/v1/test`);
  console.log(
    `📊 管理后台: http://localhost:${PORT}/admin/production-records-v2.html`,
  );
  console.log(`📦 物料管理: http://localhost:${PORT}/admin/materials.html`);
});
