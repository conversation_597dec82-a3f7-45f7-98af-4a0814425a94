/**
 * 财务集成演示页面
 * 展示完整的财务集成功能
 */

Page({
  data: {
    // 页面状态
    loading: false,
    activeTab: 0,
    tabs: [
      { id: 0, name: '批次概览', icon: '📊' },
      { id: 1, name: '生产记录', icon: '📝' },
      { id: 2, name: '财务记录', icon: '💰' },
      { id: 3, name: '数据分析', icon: '📈' }
    ],

    // 批次数据
    batches: [],
    selectedBatch: null,

    // 生产记录数据
    productionRecords: [],
    productionRecordTypes: [
      { label: '全部记录', value: 'all' },
      { label: '入栏记录', value: 'entry' },
      { label: '称重记录', value: 'weight' },
      { label: '出栏记录', value: 'sale' }
    ],
    selectedRecordType: 'all',

    // 财务记录数据
    financeRecords: [],
    financeStats: {
      totalCost: 0,
      totalRevenue: 0,
      totalProfit: 0,
      overallProfitMargin: 0
    },

    // 分析数据
    trendsData: [],
    categoryStats: [],

    // 表单数据
    showAddModal: false,
    addModalType: 'entry', // entry, sale, weight
    newRecord: {}
  },

  onLoad(options) {
    this.loadInitialData();
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.batches.length > 0) {
      this.refreshCurrentTabData();
    }
  },

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    this.setData({ loading: true });

    try {
      await Promise.all([
        this.loadBatchOverview(),
        this.loadFinanceStats()
      ]);
    } catch (error) {
      console.error('加载初始数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载批次概览
   */
  async loadBatchOverview() {
    try {
      const app = getApp();
      const response = await wx.request({
        url: `${app.globalData.apiBase}/production/batch-overview`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        const batches = response.data.data.batches || [];
        this.setData({ 
          batches,
          selectedBatch: batches.length > 0 ? batches[0].batchNumber : null
        });
      }
    } catch (error) {
      console.error('加载批次概览失败:', error);
      throw error;
    }
  },

  /**
   * 加载财务统计
   */
  async loadFinanceStats() {
    try {
      const app = getApp();
      const response = await wx.request({
        url: `${app.globalData.apiBase}/finance/batch-overview`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        this.setData({
          financeStats: response.data.data.summary || {}
        });
      }
    } catch (error) {
      console.error('加载财务统计失败:', error);
      throw error;
    }
  },

  /**
   * 标签页切换
   */
  onTabChange(e) {
    const tabId = e.currentTarget.dataset.id;
    this.setData({ activeTab: tabId });
    this.refreshCurrentTabData();
  },

  /**
   * 刷新当前标签页数据
   */
  async refreshCurrentTabData() {
    const { activeTab, selectedBatch } = this.data;

    switch (activeTab) {
      case 0: // 批次概览
        await this.loadBatchOverview();
        break;
      case 1: // 生产记录
        if (selectedBatch) {
          await this.loadProductionRecords();
        }
        break;
      case 2: // 财务记录
        if (selectedBatch) {
          await this.loadFinanceRecords();
        }
        break;
      case 3: // 数据分析
        await this.loadAnalysisData();
        break;
    }
  },

  /**
   * 批次选择
   */
  onBatchSelect(e) {
    const batchNumber = e.currentTarget.dataset.batch;
    this.setData({ selectedBatch: batchNumber });
    this.refreshCurrentTabData();
  },

  /**
   * 加载生产记录
   */
  async loadProductionRecords() {
    try {
      const app = getApp();
      const { selectedBatch, selectedRecordType } = this.data;
      
      let url = `${app.globalData.apiBase}/production/records?batchNumber=${selectedBatch}`;
      if (selectedRecordType !== 'all') {
        url += `&type=${selectedRecordType}`;
      }

      const response = await wx.request({
        url,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        this.setData({
          productionRecords: response.data.data.records || []
        });
      }
    } catch (error) {
      console.error('加载生产记录失败:', error);
      wx.showToast({
        title: '加载生产记录失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载财务记录
   */
  async loadFinanceRecords() {
    try {
      const app = getApp();
      const response = await wx.request({
        url: `${app.globalData.apiBase}/finance/records?batchNumber=${this.data.selectedBatch}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        this.setData({
          financeRecords: response.data.data.records || []
        });
      }
    } catch (error) {
      console.error('加载财务记录失败:', error);
      wx.showToast({
        title: '加载财务记录失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载分析数据
   */
  async loadAnalysisData() {
    try {
      const app = getApp();
      const [trendsResponse, categoryResponse] = await Promise.all([
        wx.request({
          url: `${app.globalData.apiBase}/finance/trends?period=monthly`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
            'Content-Type': 'application/json'
          }
        }),
        wx.request({
          url: `${app.globalData.apiBase}/finance/category-stats`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      this.setData({
        trendsData: trendsResponse.data.success ? trendsResponse.data.data.trends : [],
        categoryStats: categoryResponse.data.success ? categoryResponse.data.data.categories : []
      });
    } catch (error) {
      console.error('加载分析数据失败:', error);
      wx.showToast({
        title: '加载分析数据失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生产记录类型筛选
   */
  onRecordTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({ selectedRecordType: type });
    this.loadProductionRecords();
  },

  /**
   * 显示添加记录弹窗
   */
  onShowAddModal(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      showAddModal: true,
      addModalType: type,
      newRecord: this.getDefaultRecord(type)
    });
  },

  /**
   * 获取默认记录数据
   */
  getDefaultRecord(type) {
    const baseRecord = {
      batchNumber: this.data.selectedBatch || '',
      recordDate: new Date().toISOString().split('T')[0],
      notes: ''
    };

    switch (type) {
      case 'entry':
        return {
          ...baseRecord,
          breed: 'taihu',
          count: '',
          weight: '',
          source: '',
          supplier: '',
          unitCost: '',
          totalCost: '',
          shedNumber: '',
          dayAge: '',
          healthStatus: 'healthy'
        };
      case 'sale':
        return {
          ...baseRecord,
          count: '',
          weight: '',
          unitPrice: '',
          totalIncome: '',
          buyer: '',
          averageWeight: ''
        };
      case 'weight':
        return {
          ...baseRecord,
          count: '',
          averageWeight: '',
          totalWeight: '',
          feedRatio: ''
        };
      default:
        return baseRecord;
    }
  },

  /**
   * 关闭添加记录弹窗
   */
  onCloseAddModal() {
    this.setData({
      showAddModal: false,
      newRecord: {}
    });
  },

  /**
   * 记录表单输入
   */
  onRecordInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`newRecord.${field}`]: value
    });

    // 自动计算总成本或总收入
    this.autoCalculateTotal();
  },

  /**
   * 自动计算总金额
   */
  autoCalculateTotal() {
    const { newRecord, addModalType } = this.data;
    
    if (addModalType === 'entry' && newRecord.count && newRecord.unitCost) {
      const totalCost = parseFloat(newRecord.count) * parseFloat(newRecord.unitCost);
      this.setData({
        'newRecord.totalCost': totalCost.toFixed(2)
      });
    } else if (addModalType === 'sale' && newRecord.count && newRecord.unitPrice) {
      const totalIncome = parseFloat(newRecord.count) * parseFloat(newRecord.unitPrice);
      this.setData({
        'newRecord.totalIncome': totalIncome.toFixed(2)
      });
    } else if (addModalType === 'weight' && newRecord.count && newRecord.averageWeight) {
      const totalWeight = parseFloat(newRecord.count) * parseFloat(newRecord.averageWeight);
      this.setData({
        'newRecord.totalWeight': totalWeight.toFixed(2)
      });
    }
  },

  /**
   * 提交记录
   */
  async onSubmitRecord() {
    const { newRecord, addModalType } = this.data;
    
    // 验证必填字段
    if (!this.validateRecord(newRecord, addModalType)) {
      return;
    }

    wx.showLoading({ title: '提交中...' });

    try {
      const app = getApp();
      let url = '';
      
      switch (addModalType) {
        case 'entry':
          url = `${app.globalData.apiBase}/production/entry-records`;
          break;
        case 'sale':
          url = `${app.globalData.apiBase}/production/sale-records`;
          break;
        case 'weight':
          url = `${app.globalData.apiBase}/production/weight-records`;
          break;
      }

      const response = await wx.request({
        url,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
          'Content-Type': 'application/json'
        },
        data: newRecord
      });

      if (response.data.success) {
        wx.showToast({
          title: '记录创建成功',
          icon: 'success'
        });

        this.onCloseAddModal();
        this.refreshCurrentTabData();
      } else {
        throw new Error(response.data.message || '创建记录失败');
      }

    } catch (error) {
      console.error('提交记录失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 验证记录数据
   */
  validateRecord(record, type) {
    const requiredFields = {
      entry: ['breed', 'count', 'totalCost', 'supplier'],
      sale: ['count', 'totalIncome', 'buyer'],
      weight: ['count', 'averageWeight']
    };

    const fields = requiredFields[type] || [];
    
    for (const field of fields) {
      if (!record[field]) {
        wx.showToast({
          title: `请填写${this.getFieldName(field)}`,
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },

  /**
   * 获取字段中文名称
   */
  getFieldName(field) {
    const fieldNames = {
      breed: '品种',
      count: '数量',
      totalCost: '总成本',
      supplier: '供应商',
      totalIncome: '总收入',
      buyer: '买家',
      averageWeight: '平均体重'
    };
    return fieldNames[field] || field;
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshCurrentTabData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '智慧养鹅财务集成系统',
      path: '/pages/finance-integration-demo/finance-integration-demo'
    };
  }
});
