<!-- components/form-modal/form-modal.wxml -->
<view class="form-modal" wx:if="{{visible}}">
  <view class="modal-mask" bindtap="onMaskTap"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">{{title}}</text>
      <view class="close-btn" bindtap="onClose">
        <text class="close-text">×</text>
      </view>
    </view>
    
    <scroll-view class="modal-body" scroll-y>
      <view class="form-container">
        <block wx:for="{{fields}}" wx:key="*this">
          <view class="form-item {{item.required ? 'required' : ''}}">
            <text class="form-label">{{item.label}}</text>
            
            <!-- 文本输入框 -->
            <input 
              wx:if="{{item.type === 'text' || item.type === 'number' || item.type === 'input'}}"
              class="form-input"
              type="{{item.type === 'input' ? 'text' : item.type}}"
              placeholder="{{item.placeholder}}"
              value="{{formData[item.name || item.key]}}"
              data-field="{{item.name || item.key}}"
              bindinput="onInputChange"
            />
            
            <!-- 选择器 -->
            <picker 
              wx:elif="{{item.type === 'picker'}}"
              mode="selector"
              range="{{item.options}}"
              range-key="label"
              value="{{formData[item.name || item.key]}}"
              data-field="{{item.name || item.key}}"
              bindchange="onPickerChange"
            >
              <view class="picker-value">
                <text wx:if="{{formData[item.name || item.key] !== undefined && item.options[formData[item.name || item.key]]}}">
                  {{item.options[formData[item.name || item.key]].label}}
                </text>
                <text wx:else class="placeholder">{{item.placeholder}}</text>
                <image class="arrow-icon" src="/images/icon_arrow_down.png" mode="aspectFit"></image>
              </view>
            </picker>
            
            <!-- 日期选择器 -->
            <picker 
              wx:elif="{{item.type === 'date'}}"
              class="picker-date"
              mode="date"
              value="{{formData[item.name || item.key]}}"
              data-field="{{item.name || item.key}}"
              bindchange="onDateChange"
            >
              <view class="picker-value">
                <text wx:if="{{formData[item.name || item.key]}}">{{formData[item.name || item.key]}}</text>
                <text wx:else class="placeholder">{{item.placeholder}}</text>
                <image class="arrow-icon" src="/images/icon_date.png" mode="aspectFit"></image>
              </view>
            </picker>
            
            <!-- 时间选择器 -->
            <picker 
              wx:elif="{{item.type === 'time'}}"
              class="picker-time"
              mode="time"
              value="{{formData[item.name || item.key]}}"
              data-field="{{item.name || item.key}}"
              bindchange="onTimeChange"
            >
              <view class="picker-value">
                <text wx:if="{{formData[item.name || item.key]}}">{{formData[item.name || item.key]}}</text>
                <text wx:else class="placeholder">{{item.placeholder}}</text>
                <image class="arrow-icon" src="/images/icon_time.png" mode="aspectFit"></image>
              </view>
            </picker>
            
            <!-- 多行文本 -->
            <textarea 
              wx:elif="{{item.type === 'textarea'}}"
              class="form-textarea"
              placeholder="{{item.placeholder}}"
              value="{{formData[item.name || item.key]}}"
              data-field="{{item.name || item.key}}"
              bindinput="onInputChange"
            />
            
            <!-- 开关 -->
            <switch
              wx:elif="{{item.type === 'switch'}}"
              checked="{{formData[item.name || item.key]}}"
              data-field="{{item.name || item.key}}"
              bindchange="onSwitchChange"
            />

            <!-- 显示字段 (只读显示) -->
            <view wx:elif="{{item.type === 'display'}}" class="form-display">
              <view class="display-content">
                <text class="display-text">{{formData[item.name || item.key] || item.value || item.placeholder}}</text>
              </view>
            </view>

            <!-- 图片上传 -->
            <view wx:elif="{{item.type === 'image'}}" class="image-upload">
              <view class="upload-btn" bindtap="onImageUpload" data-field="{{item.name || item.key}}">
                <image wx:if="{{formData[item.name || item.key]}}" src="{{formData[item.name || item.key]}}" mode="aspectFill" class="uploaded-image"></image>
                <view wx:else class="upload-placeholder">
                  <image src="/images/icon_camera.png" mode="aspectFit" class="camera-icon"></image>
                  <text class="upload-text">{{item.placeholder || '点击上传图片'}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </scroll-view>

    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onCancel">取消</button>
      <button class="confirm-btn {{loading ? 'disabled' : ''}}" bindtap="onConfirm" disabled="{{loading}}">
        {{loading ? '提交中...' : '确定'}}
      </button>
    </view>
  </view>
</view>
