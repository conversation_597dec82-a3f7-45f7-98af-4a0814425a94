/**
 * 🌐 统一API客户端
 * 基于环境配置管理器的统一API请求工具
 * 支持自动环境切换、错误处理、重试机制
 */

const { environmentConfig } = require('./environment-config.js');

class UnifiedAPIClient {
  constructor() {
    this.config = environmentConfig.getRequestConfig();
    this.baseUrl = environmentConfig.getApiBaseUrl();
    this.pendingRequests = new Map();
    this.requestQueue = [];
    this.maxConcurrentRequests = 5;
    this.currentRequests = 0;
  }

  /**
   * 生成请求唯一标识
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {string} 请求标识
   */
  generateRequestId(url, options = {}) {
    const key = `${options.method || 'GET'}_${url}_${JSON.stringify(options.data || {})}`;
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * 获取认证头
   * @returns {Object} 认证头信息
   */
  getAuthHeaders() {
    const token = wx.getStorageSync('access_token');
    // 兼容新旧键：优先新键 'tenant_code'，回退旧键 'tenantCode'
    const tenantCode = wx.getStorageSync('tenant_code') || wx.getStorageSync('tenantCode');
    
    const headers = {
      ...this.config.headers
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    if (tenantCode) {
      headers['X-Tenant-Code'] = tenantCode;
    }

    return headers;
  }

  /**
   * 处理请求URL
   * @param {string} url 原始URL
   * @returns {string} 处理后的完整URL
   */
  processUrl(url) {
    // 如果是完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // 如果以/api开头，使用API基础地址
    if (url.startsWith('/api')) {
      return `${this.baseUrl}${url.replace('/api/v1', '')}`;
    }

    // 其他情况，添加API版本前缀
    const cleanUrl = url.startsWith('/') ? url : `/${url}`;
    return environmentConfig.getApiEndpointUrl(cleanUrl);
  }

  /**
   * 执行HTTP请求
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  async request(url, options = {}) {
    const requestId = this.generateRequestId(url, options);
    
    // 检查是否有相同的待处理请求
    if (this.pendingRequests.has(requestId)) {
      environmentConfig.log('debug', '复用待处理请求', { url, requestId });
      return this.pendingRequests.get(requestId);
    }

    // 创建请求Promise
    const requestPromise = this.executeRequest(url, options);
    
    // 缓存请求Promise
    this.pendingRequests.set(requestId, requestPromise);
    
    // 请求完成后清理缓存
    requestPromise.finally(() => {
      this.pendingRequests.delete(requestId);
    });

    return requestPromise;
  }

  /**
   * 执行实际的HTTP请求
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  async executeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const fullUrl = this.processUrl(url);
      const headers = {
        ...this.getAuthHeaders(),
        ...options.headers
      };

      const requestConfig = {
        url: fullUrl,
        method: options.method || 'GET',
        data: options.data,
        header: headers,
        timeout: options.timeout || this.config.timeout,
        dataType: options.dataType || 'json',
        responseType: options.responseType || 'text',
        
        success: (res) => {
          environmentConfig.log('debug', 'API请求成功', {
            url: fullUrl,
            method: options.method || 'GET',
            statusCode: res.statusCode,
            data: res.data
          });

          // 统一处理响应
          this.handleResponse(res, resolve, reject);
        },

        fail: (error) => {
          environmentConfig.log('error', 'API请求失败', {
            url: fullUrl,
            method: options.method || 'GET',
            error: error
          });

          // 处理请求失败
          this.handleError(error, fullUrl, options, resolve, reject);
        },

        complete: () => {
          this.currentRequests--;
          this.processQueue();
        }
      };

      // 控制并发请求数量
      if (this.currentRequests >= this.maxConcurrentRequests) {
        this.requestQueue.push(() => {
          this.currentRequests++;
          wx.request(requestConfig);
        });
      } else {
        this.currentRequests++;
        wx.request(requestConfig);
      }
    });
  }

  /**
   * 处理请求队列
   */
  processQueue() {
    while (this.requestQueue.length > 0 && this.currentRequests < this.maxConcurrentRequests) {
      const nextRequest = this.requestQueue.shift();
      nextRequest();
    }
  }

  /**
   * 处理API响应
   * @param {Object} res 响应对象
   * @param {Function} resolve Promise resolve函数
   * @param {Function} reject Promise reject函数
   */
  handleResponse(res, resolve, reject) {
    const { statusCode, data } = res;

    // HTTP状态码检查
    if (statusCode >= 200 && statusCode < 300) {
      // 业务逻辑检查
      if (data && typeof data === 'object') {
        if (data.success !== false && data.code !== 'error') {
          resolve(data);
        } else {
          // 业务逻辑错误
          const error = new Error(data.message || '业务处理失败');
          error.type = 'BUSINESS_ERROR';
          error.code = data.code;
          error.data = data;
          reject(error);
        }
      } else {
        resolve(data);
      }
    } else {
      // HTTP错误
      const error = new Error(`HTTP ${statusCode}: ${data?.message || '请求失败'}`);
      error.type = 'HTTP_ERROR';
      error.statusCode = statusCode;
      error.data = data;
      
      // 特殊状态码处理
      if (statusCode === 401) {
        this.handleAuthError();
      }
      
      reject(error);
    }
  }

  /**
   * 处理请求错误
   * @param {Object} error 错误对象
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @param {Function} resolve Promise resolve函数
   * @param {Function} reject Promise reject函数
   */
  handleError(error, url, options, resolve, reject) {
    let errorType = 'NETWORK_ERROR';
    let errorMessage = '网络连接失败';

    // 错误类型判断
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        errorType = 'TIMEOUT_ERROR';
        errorMessage = '请求超时';
      } else if (error.errMsg.includes('fail')) {
        errorType = 'REQUEST_FAIL';
        errorMessage = '请求失败';
      }
    }

    const apiError = new Error(errorMessage);
    apiError.type = errorType;
    apiError.originalError = error;
    apiError.url = url;

    // 重试逻辑
    const retryCount = options.retryCount || 0;
    const maxRetries = this.config.retryCount || 2;

    if (retryCount < maxRetries && this.shouldRetry(errorType)) {
      environmentConfig.log('warn', `API请求重试 (${retryCount + 1}/${maxRetries})`, {
        url,
        error: errorMessage
      });

      setTimeout(() => {
        this.executeRequest(url, {
          ...options,
          retryCount: retryCount + 1
        }).then(resolve).catch(reject);
      }, 1000 * (retryCount + 1));
    } else {
      reject(apiError);
    }
  }

  /**
   * 判断是否应该重试
   * @param {string} errorType 错误类型
   * @returns {boolean} 是否重试
   */
  shouldRetry(errorType) {
    const retryableErrors = ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'REQUEST_FAIL'];
    return retryableErrors.includes(errorType);
  }

  /**
   * 处理认证错误
   */
  handleAuthError() {
    // 清除本地存储的认证信息
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    // 跳转到登录页面
    wx.reLaunch({
      url: '/pages/login/login'
    });

    // 显示提示信息
    wx.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * GET请求
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  get(url, options = {}) {
    return this.request(url, {
      ...options,
      method: 'GET'
    });
  }

  /**
   * POST请求
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  post(url, data = {}, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      data
    });
  }

  /**
   * PUT请求
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  put(url, data = {}, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      data
    });
  }

  /**
   * DELETE请求
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  delete(url, options = {}) {
    return this.request(url, {
      ...options,
      method: 'DELETE'
    });
  }

  /**
   * PATCH请求
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   * @param {Object} options 请求选项
   * @returns {Promise} 请求Promise
   */
  patch(url, data = {}, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PATCH',
      data
    });
  }

  /**
   * 文件上传
   * @param {string} url 上传URL
   * @param {string} filePath 文件路径
   * @param {Object} options 上传选项
   * @returns {Promise} 上传Promise
   */
  upload(url, filePath, options = {}) {
    return new Promise((resolve, reject) => {
      const fullUrl = this.processUrl(url);
      const headers = this.getAuthHeaders();

      wx.uploadFile({
        url: fullUrl,
        filePath,
        name: options.name || 'file',
        formData: options.formData || {},
        header: headers,
        
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (error) {
            resolve(res.data);
          }
        },
        
        fail: (error) => {
          reject(new Error(error.errMsg || '文件上传失败'));
        }
      });
    });
  }

  /**
   * 获取API统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      queuedRequests: this.requestQueue.length,
      currentRequests: this.currentRequests,
      environment: environmentConfig.getEnvironment(),
      baseUrl: this.baseUrl
    };
  }
}

// 创建全局单例
const apiClient = new UnifiedAPIClient();

// 导出API客户端和便捷方法
module.exports = {
  UnifiedAPIClient,
  apiClient,
  
  // 便捷方法
  get: (url, options) => apiClient.get(url, options),
  post: (url, data, options) => apiClient.post(url, data, options),
  put: (url, data, options) => apiClient.put(url, data, options),
  delete: (url, options) => apiClient.delete(url, options),
  patch: (url, data, options) => apiClient.patch(url, data, options),
  upload: (url, filePath, options) => apiClient.upload(url, filePath, options),
  
  // 统计信息
  getStats: () => apiClient.getStats()
};