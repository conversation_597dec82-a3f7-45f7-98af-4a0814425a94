// backend/routes/management.routes.js
const express = require("express");
const router = express.Router();
const { verifyToken } = require("../middleware/auth.middleware");
const {
  identifyTenant,
  authenticateTenantUser,
  recordTenantUsage,
} = require("../middleware/tenant.middleware");
const {
  requireTenantManagement,
  requireStaffManagement,
  requireApprovalManagement,
  ensureTenantDataSecurity,
} = require("../middleware/rbac.middleware");

/**
 * 租户内管理功能路由
 * 用于小程序的管理功能API
 */

// 应用基础中间件
router.use(verifyToken); // JWT验证
router.use(identifyTenant); // 识别租户
router.use(authenticateTenantUser); // 租户用户认证
router.use(ensureTenantDataSecurity); // 租户数据安全检查
router.use(recordTenantUsage); // 记录API使用量

/**
 * @route   GET /management/overview
 * @desc    获取管理仪表板概览数据
 * @access  Tenant Managers (admin, manager)
 */
router.get("/overview", requireTenantManagement, async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const { Flock, User, ProductionRecord, HealthRecord } = tenantDb.models;

    // 获取统计数据
    const [
      totalStaff,
      activeStaff,
      totalFlocks,
      totalGeese,
      todayProduction,
      monthlyRevenue,
    ] = await Promise.all([
      User.count(),
      User.count({ where: { status: "active" } }),
      Flock.count({ where: { status: "active" } }),
      Flock.sum("quantity") || 0,
      getTodayProduction(ProductionRecord),
      getMonthlyRevenue(tenantDb),
    ]);

    res.json({
      success: true,
      data: {
        totalStaff,
        activeStaff,
        totalFlocks,
        totalGeese,
        todayProduction,
        monthlyRevenue,
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取管理概览失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取概览数据失败",
    });
  }
});

/**
 * @route   GET /management/pending-tasks
 * @desc    获取待处理事项
 * @access  Tenant Managers
 */
router.get("/pending-tasks", requireTenantManagement, async (req, res) => {
  try {
    const tenantDb = req.tenantDb;

    // 这里应该从各个模块获取待处理事项
    // 暂时返回模拟数据
    const pendingTasks = {
      approvals: 3, // 待审批申请
      alerts: 2, // 健康告警
      reports: 1, // 待处理报表
      maintenance: 0, // 设备维护
    };

    res.json({
      success: true,
      data: pendingTasks,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取待处理事项失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取待处理事项失败",
    });
  }
});

/**
 * @route   GET /management/recent-activities
 * @desc    获取最近活动
 * @access  Tenant Managers
 */
router.get("/recent-activities", requireTenantManagement, async (req, res) => {
  try {
    // 这里应该从审计日志或活动记录中获取数据
    // 暂时返回模拟数据
    const activities = [
      {
        id: 1,
        time: "10:30",
        title: "新员工入职",
        description: "张三已完成入职手续",
      },
      {
        id: 2,
        time: "09:15",
        title: "生产记录更新",
        description: "鹅群A今日产蛋量已录入",
      },
    ];

    res.json({
      success: true,
      data: activities,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取最近活动失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取最近活动失败",
    });
  }
});

/**
 * @route   GET /staff
 * @desc    获取员工列表
 * @access  Staff Managers (admin, manager)
 */
router.get("/staff", requireStaffManagement, async (req, res) => {
  try {
    const { role, status, search } = req.query;
    const tenantDb = req.tenantDb;
    const { User } = tenantDb.models;

    // 构建查询条件
    const where = {};
    if (role && role !== "all") {
      where.role = role;
    }
    if (status && status !== "all") {
      where.status = status;
    }
    if (search) {
      where[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { phone: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
      ];
    }

    const users = await User.findAll({
      where,
      attributes: [
        "id",
        "name",
        "phone",
        "email",
        "role",
        "status",
        "createdAt",
        "updatedAt",
      ],
      order: [["updatedAt", "DESC"]],
    });

    res.json({
      success: true,
      data: users,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取员工列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取员工列表失败",
    });
  }
});

/**
 * @route   GET /staff/stats
 * @desc    获取员工统计信息
 * @access  Staff Managers
 */
router.get("/staff/stats", requireStaffManagement, async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const { User } = tenantDb.models;

    const [total, active, inactive, todayAttendance] = await Promise.all([
      User.count(),
      User.count({ where: { status: "active" } }),
      User.count({ where: { status: "inactive" } }),
      getTodayAttendance(tenantDb), // 需要实现考勤统计
    ]);

    res.json({
      success: true,
      data: {
        total,
        active,
        inactive,
        todayAttendance,
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取员工统计失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取员工统计失败",
    });
  }
});

/**
 * @route   POST /staff
 * @desc    创建新员工
 * @access  Staff Managers
 */
router.post("/staff", requireStaffManagement, async (req, res) => {
  try {
    const { name, phone, email, role, department, position } = req.body;
    const tenantDb = req.tenantDb;
    const { User } = tenantDb.models;

    // 验证必填字段
    if (!name || !phone || !role) {
      return res.status(400).json({
        success: false,
        message: "姓名、手机号和角色为必填项",
      });
    }

    // 检查手机号是否已存在
    const existingUser = await User.findOne({ where: { phone } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "该手机号已被使用",
      });
    }

    // 创建用户
    const newUser = await User.create({
      name,
      phone,
      email: email || null,
      role,
      department: department || "",
      position: position || "",
      status: "active",
      tenantId: req.tenant.id,
    });

    res.status(201).json({
      success: true,
      data: newUser,
      message: "员工创建成功",
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('创建员工失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "创建员工失败",
    });
  }
});

/**
 * @route   GET /approvals
 * @desc    获取审批列表
 * @access  Approval Managers
 */
router.get("/approvals", requireApprovalManagement, async (req, res) => {
  try {
    const { status, type } = req.query;

    // 这里应该从审批表中获取数据
    // 暂时返回模拟数据
    const approvals = [
      {
        id: 1,
        type: "expense",
        title: "饲料采购申请",
        applicant: "张三",
        amount: 5000,
        status: "pending",
        submitTime: new Date(),
        description: "购买优质鹅饲料1000斤",
      },
    ];

    res.json({
      success: true,
      data: approvals,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取审批列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取审批列表失败",
    });
  }
});

// 辅助函数
async function getTodayProduction(ProductionRecord) {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const production = await ProductionRecord.sum("eggCount", {
      where: {
        recordDate: {
          [Op.gte]: today,
          [Op.lt]: tomorrow,
        },
      },
    });

    return production || 0;
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取今日产量失败', { error: error.message, stack: error.stack }); } catch(_) {}
    return 0;
  }
}

async function getMonthlyRevenue(tenantDb) {
  try {
    const FinancialRecord = tenantDb.models.FinancialRecord;
    if (!FinancialRecord) return 0;

    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const revenue = await FinancialRecord.sum("amount", {
      where: {
        type: "income",
        recordDate: {
          [Op.gte]: currentMonth,
        },
      },
    });

    return revenue || 0;
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取月度收入失败', { error: error.message, stack: error.stack }); } catch(_) {}
    return 0;
  }
}

async function getTodayAttendance(tenantDb) {
  // 这里应该实现考勤统计逻辑
  // 暂时返回模拟数据
  return 9;
}

module.exports = router;
