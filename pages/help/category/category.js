// pages/help/category/category.js
Page({
  data: {
    categoryInfo: {
      id: 1,
      title: '健康管理',
      icon: '🩺',
      description: '学习如何使用健康管理系统，掌握鹅群健康监控技能',
      totalViews: 2580,
      articles: []
    },
    
    // 搜索相关
    searchKeyword: '',
    sortType: 'latest',
    sortText: '按最新发布',
    
    // 文章数据
    allArticles: [
      {
        id: 201,
        title: '如何添加健康记录',
        summary: '详细介绍如何在系统中添加和管理鹅群的健康记录，包括体温、体重、疫苗等信息',
        content: '健康记录是养鹅管理的重要组成部分...',
        views: 580,
        likes: 45,
        difficulty: 'easy',
        difficultyText: '简单',
        updateTime: '2024-11-28',
        tags: ['健康记录', '数据录入', '基础操作']
      },
      {
        id: 202,
        title: 'AI诊断使用说明',
        summary: '学习如何使用AI智能诊断功能，快速识别鹅群健康问题',
        content: 'AI诊断功能基于深度学习算法...',
        views: 425,
        likes: 38,
        difficulty: 'medium',
        difficultyText: '中等',
        updateTime: '2024-11-27',
        tags: ['AI诊断', '智能识别', '健康分析']
      },
      {
        id: 203,
        title: '健康报告解读',
        summary: '教您如何正确理解和分析系统生成的健康报告',
        content: '健康报告包含多个维度的数据分析...',
        views: 356,
        likes: 29,
        difficulty: 'medium',
        difficultyText: '中等',
        updateTime: '2024-11-26',
        tags: ['健康报告', '数据分析', '报告解读']
      },
      {
        id: 204,
        title: '疾病预防指南',
        summary: '全面的疾病预防策略和日常健康管理建议',
        content: '疾病预防胜于治疗，日常管理是关键...',
        views: 289,
        likes: 22,
        difficulty: 'hard',
        difficultyText: '困难',
        updateTime: '2024-11-25',
        tags: ['疾病预防', '健康管理', '专业指导']
      }
    ],
    
    filteredArticles: [],
    
    // 相关分类
    relatedCategories: [
      {
        id: 3,
        title: '生产管理',
        icon: '🏭',
        articles: [
          { id: 301, title: '环境监控数据说明' },
          { id: 302, title: '如何管理物料库存' }
        ]
      },
      {
        id: 5,
        title: '故障排除',
        icon: '🔧',
        articles: [
          { id: 501, title: '数据同步失败怎么办' },
          { id: 502, title: '登录异常处理' }
        ]
      }
    ],
    
    // 分类相关FAQ
    categoryFAQ: [
      {
        id: 1,
        question: '健康记录多久更新一次比较好？',
        answer: '建议每天至少记录一次基础健康数据，如有异常情况应及时记录。对于幼鹅，建议每天2-3次记录。',
        expanded: false
      },
      {
        id: 2,
        question: 'AI诊断的准确率如何？',
        answer: '我们的AI诊断系统准确率达到95%以上，但建议结合专业兽医意见进行最终诊断。',
        expanded: false
      },
      {
        id: 3,
        question: '如何提高健康管理效率？',
        answer: '可以通过设置自动提醒、批量操作、数据模板等功能来提高管理效率。',
        expanded: false
      }
    ]
  },

  onLoad: function (options) {
    const { id, title } = options;
    
    if (id && title) {
      this.loadCategoryData(id, title);
    }
    
    // 初始化文章列表
    this.setData({
      filteredArticles: this.data.allArticles
    });
  },

  // 加载分类数据
  loadCategoryData: function(categoryId, categoryTitle) {
    // 根据分类ID获取对应的数据
    const categoryMap = {
      1: {
        title: '账户与登录',
        icon: '👤',
        description: '账户注册、登录问题、个人信息管理相关帮助',
        articles: [
          { id: 101, title: '如何注册账户', summary: '详细的账户注册流程指导' },
          { id: 102, title: '忘记密码怎么办', summary: '密码重置和找回方法' }
        ]
      },
      2: {
        title: '健康管理',
        icon: '🩺',
        description: '学习如何使用健康管理系统，掌握鹅群健康监控技能',
        articles: this.data.allArticles
      },
      3: {
        title: '生产管理',
        icon: '🏭',
        description: '生产数据管理、环境监控、库存管理等功能使用',
        articles: [
          { id: 301, title: '环境监控数据说明', summary: '了解各项环境参数的含义和标准' },
          { id: 302, title: '如何管理物料库存', summary: '库存管理系统的使用方法' }
        ]
      }
    };

    const categoryInfo = categoryMap[categoryId] || {
      title: categoryTitle,
      icon: '❓',
      description: '相关帮助内容',
      articles: []
    };

    // 添加统计数据
    categoryInfo.totalViews = categoryInfo.articles.reduce((sum, article) => sum + (article.views || 0), 0);
    
    this.setData({
      categoryInfo: {
        id: categoryId,
        ...categoryInfo
      }
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: categoryInfo.title
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    this.filterArticles();
  },

  // 执行搜索
  onSearch: function() {
    this.filterArticles();
  },

  // 清除搜索
  onClearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    this.filterArticles();
  },

  // 筛选文章
  filterArticles: function() {
    const { searchKeyword, allArticles, sortType } = this.data;
    
    let filtered = [...allArticles];
    
    // 搜索过滤
    if (searchKeyword.trim()) {
      filtered = filtered.filter(article => 
        article.title.includes(searchKeyword) || 
        article.summary.includes(searchKeyword) ||
        article.tags.some(tag => tag.includes(searchKeyword))
      );
    }
    
    // 排序
    this.sortArticles(filtered, sortType);
  },

  // 排序文章
  sortArticles: function(articles, sortType) {
    let sortText = '';
    
    switch (sortType) {
      case 'latest':
        articles.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime));
        sortText = '按最新发布';
        break;
      case 'popular':
        articles.sort((a, b) => b.views - a.views);
        sortText = '按浏览量';
        break;
      case 'helpful':
        articles.sort((a, b) => b.likes - a.likes);
        sortText = '按点赞数';
        break;
    }
    
    this.setData({
      filteredArticles: articles,
      sortText: sortText
    });
  },

  // 切换排序方式
  onSortBy: function(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      sortType: type
    });
    this.filterArticles();
  },

  // 查看文章详情
  onViewArticle: function(e) {
    const { article } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/help/article/article?id=${article.id}&title=${encodeURIComponent(article.title)}`,
      fail: () => {
        wx.showModal({
          title: article.title,
          content: article.summary,
          showCancel: false
        });
      }
    });
  },

  // 查看其他分类
  onViewCategory: function(e) {
    const { category } = e.currentTarget.dataset;
    
    wx.redirectTo({
      url: `/pages/help/category/category?id=${category.id}&title=${encodeURIComponent(category.title)}`
    });
  },

  // 展开/折叠FAQ
  onToggleFAQ: function(e) {
    const { index } = e.currentTarget.dataset;
    const categoryFAQ = this.data.categoryFAQ;
    
    categoryFAQ[index].expanded = !categoryFAQ[index].expanded;
    
    this.setData({
      categoryFAQ: categoryFAQ
    });
  },

  // 提交反馈
  onFeedback: function() {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback'
    });
  },

  // 联系客服
  onContact: function() {
    wx.showModal({
      title: '联系客服',
      content: '客服热线：************\n工作时间：周一至周五 9:00-18:00',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    const { categoryInfo } = this.data;
    return {
      title: `智慧养鹅 - ${categoryInfo.title}帮助`,
      desc: categoryInfo.description,
      path: `/pages/help/category/category?id=${categoryInfo.id}&title=${encodeURIComponent(categoryInfo.title)}`
    };
  }
});