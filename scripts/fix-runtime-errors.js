#!/usr/bin/env node

/**
 * 🔧 运行时错误修复脚本
 * 
 * 🎯 功能:
 * - 修复API导入错误
 * - 修复损坏的图片资源
 * - 配置网络域名白名单
 * - 批量处理运行时问题
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12
 */

const fs = require('fs');
const path = require('path');

class RuntimeErrorFixer {
  constructor() {
    this.fixResults = {
      apiImports: { fixed: 0, errors: [] },
      imageResources: { fixed: 0, errors: [] },
      networkConfig: { configured: false, domains: [] },
      summary: { totalIssues: 0, fixedIssues: 0 }
    };

    // 1x1透明PNG的base64数据（最小有效PNG）
    this.defaultImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    // 损坏的图片文件列表（从错误日志中提取）
    this.brokenImages = [
      'images/icons/add.png',
      'images/icons/arrow_down.png', 
      'images/icons/calendar.png',
      'images/icons/time.png',
      'images/icons/camera.png',
      'images/icons/chart.png',
      'images/icons/report.png',
      'images/icons/pending.png',
      'images/icons/history.png',
      'images/icons/template.png'
    ];

    // 需要修复API导入的文件列表
    this.apiImportFiles = [
      {
        // Note: pages/production/materials/materials.js has been removed
        // file: 'pages/production/materials/materials.js',
        // oldImport: "const { api } = require('../../../utils/api');",
        // newImport: "const request = require('../../../utils/request');"
      }
    ];

    // 网络域名配置
    this.networkDomains = {
      request: [
        'https://636c-cloud1-3gdruqkn67e1cbe2-1362002942.tcb.qcloud.la',
        'https://tcb-api.tencentcloudapi.com',
        'http://localhost:3000'  // 开发环境
      ],
      socket: [],
      uploadFile: [],
      downloadFile: []
    };
  }

  // 运行完整的错误修复
  async runFullFix() {
    console.log('🔧 开始运行时错误修复...\n');

    try {
      // 1. 修复API导入错误
      await this.fixApiImports();
      
      // 2. 修复损坏的图片资源
      await this.fixImageResources();
      
      // 3. 生成网络域名配置指南
      await this.generateNetworkConfig();
      
      // 4. 生成修复报告
      this.generateFixReport();

    } catch (error) {
      console.error('❌ 修复过程出错:', error);
      throw error;
    }
  }

  // 修复API导入错误
  async fixApiImports() {
    console.log('1️⃣  🔧 修复API导入错误...');
    
    for (const item of this.apiImportFiles) {
      try {
        if (fs.existsSync(item.file)) {
          let content = fs.readFileSync(item.file, 'utf8');
          
          if (content.includes(item.oldImport)) {
            content = content.replace(item.oldImport, item.newImport);
            fs.writeFileSync(item.file, content);
            
            this.fixResults.apiImports.fixed++;
            console.log(`   ✅ 修复: ${item.file}`);
          } else {
            console.log(`   ⚠️  跳过: ${item.file} (已修复或不存在目标导入)`);
          }
        } else {
          console.log(`   ❌ 文件不存在: ${item.file}`);
          this.fixResults.apiImports.errors.push(`文件不存在: ${item.file}`);
        }
      } catch (error) {
        console.error(`   ❌ 修复 ${item.file} 失败:`, error.message);
        this.fixResults.apiImports.errors.push(`${item.file}: ${error.message}`);
      }
    }
    
    console.log(`   📊 API导入修复完成: ${this.fixResults.apiImports.fixed}个文件\n`);
  }

  // 修复损坏的图片资源
  async fixImageResources() {
    console.log('2️⃣  🖼️  修复损坏的图片资源...');
    
    for (const imagePath of this.brokenImages) {
      try {
        // 检查文件是否存在且损坏（大小为0或小于100字节）
        if (fs.existsSync(imagePath)) {
          const stat = fs.statSync(imagePath);
          if (stat.size < 100) {
            // 文件损坏，替换为默认图片
            const buffer = Buffer.from(this.defaultImageBase64, 'base64');
            fs.writeFileSync(imagePath, buffer);
            
            this.fixResults.imageResources.fixed++;
            console.log(`   ✅ 修复损坏图片: ${imagePath} (${stat.size}B → ${buffer.length}B)`);
          } else {
            console.log(`   ⚠️  跳过: ${imagePath} (文件正常, ${stat.size}B)`);
          }
        } else {
          // 文件不存在，创建默认图片
          const buffer = Buffer.from(this.defaultImageBase64, 'base64');
          
          // 确保目录存在
          const dir = path.dirname(imagePath);
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
          }
          
          fs.writeFileSync(imagePath, buffer);
          this.fixResults.imageResources.fixed++;
          console.log(`   ✅ 创建缺失图片: ${imagePath} (${buffer.length}B)`);
        }
      } catch (error) {
        console.error(`   ❌ 处理 ${imagePath} 失败:`, error.message);
        this.fixResults.imageResources.errors.push(`${imagePath}: ${error.message}`);
      }
    }
    
    console.log(`   📊 图片资源修复完成: ${this.fixResults.imageResources.fixed}个文件\n`);
  }

  // 生成网络域名配置指南
  async generateNetworkConfig() {
    console.log('3️⃣  🌐 生成网络域名配置指南...');
    
    const configGuide = this.generateNetworkConfigGuide();
    
    // 保存配置指南
    fs.writeFileSync('docs/network-domain-config.md', configGuide);
    
    this.fixResults.networkConfig.configured = true;
    this.fixResults.networkConfig.domains = this.networkDomains.request;
    
    console.log('   ✅ 网络域名配置指南已生成: docs/network-domain-config.md');
    console.log(`   📊 配置域名: ${this.networkDomains.request.length}个\n`);
  }

  // 生成网络域名配置指南内容
  generateNetworkConfigGuide() {
    return `# 🌐 微信小程序网络域名配置指南

## 📅 生成时间
**时间**: ${new Date().toLocaleString()}  
**修复脚本**: fix-runtime-errors.js

---

## 🚨 问题描述

当前项目出现网络请求域名校验错误：
\`\`\`
request 合法域名校验出错
http://localhost:3000 不在以下 request 合法域名列表中
\`\`\`

---

## 🔧 解决方案

### **方案一: 开发环境配置**

1. **打开微信开发者工具**
2. **进入项目详情页面**：点击右上角"详情"按钮
3. **找到"域名信息"选项卡**
4. **配置合法域名**：

#### **request合法域名**:
\`\`\`
${this.networkDomains.request.join('\n')}
\`\`\`

#### **socket合法域名**:
\`\`\`
${this.networkDomains.socket.length > 0 ? this.networkDomains.socket.join('\n') : '暂无'}
\`\`\`

#### **uploadFile合法域名**:
\`\`\`
${this.networkDomains.uploadFile.length > 0 ? this.networkDomains.uploadFile.join('\n') : '暂无'}
\`\`\`

#### **downloadFile合法域名**:
\`\`\`
${this.networkDomains.downloadFile.length > 0 ? this.networkDomains.downloadFile.join('\n') : '暂无'}
\`\`\`

### **方案二: 开发调试临时解决**

在开发者工具中：
1. **点击"详情" → "本地设置"**
2. **勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"**
3. **重新编译项目**

⚠️  **注意**: 此方案仅适用于开发环境，正式发布前必须配置正确的域名

---

## 📋 操作步骤

### **步骤1: 配置开发环境**
\`\`\`bash
# 1. 在微信开发者工具中打开项目
# 2. 点击右上角"详情"按钮  
# 3. 选择"域名信息"选项卡
# 4. 将上述域名添加到对应的配置中
\`\`\`

### **步骤2: 更新项目配置**
\`\`\`bash
# 刷新项目配置
# 操作路径："详情-域名信息" → 点击刷新按钮
\`\`\`

### **步骤3: 重新编译**
\`\`\`bash
# 1. 保存所有文件
# 2. 在开发者工具中点击"编译"按钮
# 3. 验证网络请求是否正常
\`\`\`

---

## 🧪 验证测试

### **测试请求**
\`\`\`javascript
// 在控制台中测试网络请求
wx.request({
  url: 'http://localhost:3000/api/test',
  method: 'GET',
  success: (res) => {
    console.log('✅ 网络请求成功:', res);
  },
  fail: (err) => {
    console.error('❌ 网络请求失败:', err);
  }
});
\`\`\`

### **预期结果**
- ✅ 控制台不再出现域名校验错误
- ✅ API请求能正常发送和接收响应
- ✅ 页面功能恢复正常

---

## 🚀 生产环境配置

### **正式发布前准备**

1. **域名备案**: 确保所有域名已完成ICP备案
2. **HTTPS配置**: 所有生产域名必须支持HTTPS
3. **微信后台配置**:
   - 登录[微信公众平台](https://mp.weixin.qq.com)
   - 进入小程序管理后台
   - 配置服务器域名

### **生产域名示例**
\`\`\`
request合法域名:
- https://api.yourcompany.com
- https://your-backend-domain.com

socket合法域名:  
- wss://socket.yourcompany.com

uploadFile合法域名:
- https://upload.yourcompany.com

downloadFile合法域名:
- https://download.yourcompany.com
\`\`\`

---

## 📝 常见问题

### **Q1: 配置后仍然报错怎么办？**
A: 
1. 检查域名拼写是否正确
2. 确保包含了协议头(http://或https://)
3. 重新编译项目
4. 清除缓存后重试

### **Q2: localhost域名配置不生效？**
A: 
1. 确认开发者工具版本是否最新
2. 尝试使用"不校验合法域名"选项
3. 检查网络连接是否正常

### **Q3: 如何批量配置域名？**
A:
1. 准备域名列表（每行一个）
2. 在域名配置页面批量粘贴
3. 保存并刷新项目配置

---

## 📊 修复记录

- **修复时间**: ${new Date().toLocaleString()}
- **配置域名数**: ${this.networkDomains.request.length}个
- **脚本版本**: v1.0.0
- **修复状态**: ✅ 配置指南已生成

---

**🎯 配置完成后，项目的网络请求功能将恢复正常，不再出现域名校验错误。**`;
  }

  // 生成修复报告
  generateFixReport() {
    console.log('📝 生成修复报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalIssues: this.fixResults.apiImports.fixed + this.fixResults.imageResources.fixed + (this.fixResults.networkConfig.configured ? 1 : 0),
        fixedIssues: this.fixResults.apiImports.fixed + this.fixResults.imageResources.fixed + (this.fixResults.networkConfig.configured ? 1 : 0),
        errors: [
          ...this.fixResults.apiImports.errors,
          ...this.fixResults.imageResources.errors
        ]
      },
      details: this.fixResults
    };

    fs.writeFileSync('runtime-error-fix-report.json', JSON.stringify(report, null, 2));

    console.log('\n📊 ===== 运行时错误修复报告 =====');
    console.log(`🔧 API导入修复: ${this.fixResults.apiImports.fixed}个文件`);
    console.log(`🖼️  图片资源修复: ${this.fixResults.imageResources.fixed}个文件`);
    console.log(`🌐 网络配置: ${this.fixResults.networkConfig.configured ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`📊 总计修复: ${report.summary.fixedIssues}个问题`);
    
    if (report.summary.errors.length > 0) {
      console.log(`\n⚠️  修复错误: ${report.summary.errors.length}个`);
      report.summary.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }

    console.log('\n💾 详细报告已保存: runtime-error-fix-report.json');
    console.log('📖 网络配置指南: docs/network-domain-config.md');

    // 修复成功提示
    if (report.summary.errors.length === 0) {
      console.log('\n🎉 所有运行时错误修复完成！');
      console.log('📋 下一步操作:');
      console.log('  1. 按照 docs/network-domain-config.md 配置网络域名');
      console.log('  2. 重新编译项目');
      console.log('  3. 测试页面功能是否正常');
    } else {
      console.log('\n⚠️  部分问题需要手动处理，请查看错误详情');
    }
  }
}

// 命令行执行
if (require.main === module) {
  const fixer = new RuntimeErrorFixer();
  fixer.runFullFix().catch(error => {
    console.error('修复失败:', error);
    process.exit(1);
  });
}

module.exports = RuntimeErrorFixer;