/* pages/shop/checkout.wxss - 简洁统一风格 */

.checkout-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
}

/* 地址区域 */
.address-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  padding: 30rpx 30rpx 0 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.address-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.address-info {
  flex: 1;
}

.address-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 12rpx;
}

.address-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.address-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.arrow-right {
  margin-left: 20rpx;
}

.arrow {
  font-size: 28rpx;
  color: #999;
}

/* 商品区域 */
.goods-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 商品列表 */
.goods-list {
  padding: 0 30rpx 30rpx;
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}

.goods-qty {
  font-size: 26rpx;
  color: #666;
}

/* 备注区域 */
.note-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.note-header {
  padding: 30rpx 30rpx 0 30rpx;
}

.note-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.note-input-wrapper {
  padding: 20rpx 30rpx 30rpx;
}

.note-input {
  width: 100%;
  min-height: 80rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  background: #f8f8f8;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.note-input::placeholder {
  color: #999;
}

/* 价格明细 */
.price-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.price-details {
  padding: 30rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.price-item:last-child {
  border-bottom: none;
}

.price-item.coupon {
  justify-content: flex-start;
  gap: 20rpx;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.coupon-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-selected {
  font-size: 26rpx;
  color: #ff6b35;
}

.coupon-amount {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}

/* 发票选项 */
.invoice-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.invoice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
}

.invoice-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.invoice-option {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  text-align: right;
  margin-right: 20rpx;
}

/* 底部价格和提交 */
.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 25rpx 30rpx calc(25rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #ecf0f1;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.total-price {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.total-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.total-amount {
  font-size: 36rpx;
  color: #ff5722;
  font-weight: bold;
}

.savings {
  font-size: 22rpx;
  color: #27ae60;
}

.submit-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
  min-width: 200rpx;
}

.submit-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.5);
}

/* 发票模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.invoice-modal {
  background: white;
  border-radius: 20rpx;
  margin: 40rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.invoice-types {
  padding: 20rpx 0;
}

.invoice-type-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  position: relative;
}

.type-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.type-desc {
  font-size: 26rpx;
  color: #666;
}

.type-check {
  position: absolute;
  right: 30rpx;
  color: #ff6b35;
  font-size: 32rpx;
  font-weight: bold;
}