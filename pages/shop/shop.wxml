<!-- pages/shop/shop.wxml -->
<view class="shop-container">
  <!-- 搜索区域 -->
  <view class="search-header">
    <view class="search-bar">
      <image class="search-icon" src="/images/icons/search.png" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索商品..." bindinput="onSearchInput" />
      <button class="search-btn" bindtap="onSearch">搜索</button>
    </view>
  </view>

  <!-- 分类筛选和视图切换 -->
  <view class="filter-section">
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="category-list">
        <view wx:for="{{categories}}" wx:key="id"
              class="category-item {{item.active ? 'active' : ''}}"
              bindtap="onCategorySelect"
              data-id="{{item.id}}">
          <text class="category-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>


  </view>

  <!-- 商品网格 -->
  <view class="products-grid">
    <block wx:for="{{filteredGoods}}" wx:key="id">
      <view class="product-card" bindtap="onGoodsTap" data-id="{{item.id}}">
        <!-- 商品图片 -->
        <view class="product-image-container">
          <image class="product-image" src="{{item.image}}" mode="aspectFill" binderror="onImageError"></image>
          <view class="product-image-placeholder" wx:if="{{!item.image}}">
            <text class="placeholder-icon">📦</text>
          </view>
          

        </view>

        <!-- 商品信息 -->
        <view class="product-info">
          <view class="product-title">{{item.name}}</view>
          <view class="product-price-row">
            <view class="price-section">
              <text class="current-price">¥{{item.price}}</text>
              <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            </view>
            <view class="sales-info">已售{{item.sales || 0}}</view>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredGoods.length === 0 && !isLoading}}">
    <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无商品</text>
    <text class="empty-desc">换个关键词试试吧</text>
  </view>

  <!-- 浮动购物车按钮 -->
  <view class="cart-float {{isDragging ? 'dragging' : ''}}" 
        style="--cart-x: {{cartPosition.x}}px; --cart-y: {{cartPosition.y}}px;"
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove" 
        bindtouchend="onTouchEnd"
        bindtap="onCartTap">
    <view class="cart-icon-wrapper">
      <text class="cart-icon">🛒</text>
      <view class="cart-badge" wx:if="{{cartCount > 0}}">
        <text class="badge-text">{{cartCount > 99 ? '99+' : cartCount}}</text>
      </view>
    </view>
  </view>
</view>