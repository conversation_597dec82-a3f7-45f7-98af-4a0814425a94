@import '/styles/oa-common.wxss';
/* pages/oa/leave/detail/detail.wxss */

.detail-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 40rpx;
}

/* ==================== 状态头部 ==================== */
.status-header {
  background: linear-gradient(135deg, #0066CC 0%, #004499 100%);
  padding: 40rpx 30rpx 60rpx 30rpx;
  color: white;
  position: relative;
}

.status-content {
  text-align: center;
}

.status-badge {
  display: inline-block;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 44rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 12rpx;
}

.status-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

/* ==================== 通用区域样式 ==================== */
.section {
  margin: 30rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* ==================== 申请人信息 ==================== */
.applicant-card {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.applicant-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
}

.applicant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.applicant-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.applicant-position {
  font-size: 28rpx;
  color: #0066CC;
  font-weight: 500;
}

.applicant-department {
  font-size: 26rpx;
  color: #666;
}

.contact-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.contact-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.3);
}

.contact-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

/* ==================== 详情项目 ==================== */
.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
  min-height: 48rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 48rpx;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 48rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.type-badge {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
}

.reason-text {
  line-height: 1.6;
  word-break: break-all;
}

.days-highlight {
  background: linear-gradient(135deg, #0066CC15 0%, #0066CC05 100%);
  color: #0066CC;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #0066CC;
}

.phone-text {
  flex: 1;
}

.call-btn {
  height: 60rpx;
  padding: 0 20rpx;
  background: linear-gradient(135deg, #00A86B 0%, #00C875 100%);
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* ==================== 附件区域 ==================== */
.attachment-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.attachment-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #e8e8e8;
}

.attachment-image {
  width: 100%;
  height: 100%;
}

.attachment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.attachment-item:active .attachment-overlay {
  opacity: 1;
}

.preview-icon {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* ==================== 审批流程 ==================== */
.flow-timeline {
  position: relative;
}

.flow-item {
  display: flex;
  margin-bottom: 40rpx;
  position: relative;
}

.flow-item:last-child {
  margin-bottom: 0;
}

.flow-step {
  position: relative;
  margin-right: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-number {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  color: white;
  background: #ddd;
  z-index: 2;
}

.step-number.completed {
  background: linear-gradient(135deg, #00A86B 0%, #00C875 100%);
}

.step-number.pending {
  background: linear-gradient(135deg, #FF9500 0%, #FFB84D 100%);
}

.step-number.rejected {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B60 100%);
}

.step-line {
  width: 4rpx;
  height: 60rpx;
  background: #e8e8e8;
  margin-top: 8rpx;
}

.flow-item.completed .step-line {
  background: linear-gradient(to bottom, #00A86B, #00C875);
}

.flow-content {
  flex: 1;
  padding-top: 8rpx;
}

.flow-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.flow-user {
  font-size: 26rpx;
  color: #0066CC;
  font-weight: 500;
  display: block;
  margin-bottom: 6rpx;
}

.flow-time {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 6rpx;
}

.flow-description {
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* ==================== 审批意见 ==================== */
.comment-card {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #0066CC;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.comment-approver {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.comment-action {
  font-size: 30rpx;
  font-weight: 600;
}

.comment-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* ==================== 审批操作 ==================== */
.approval-buttons {
  display: flex;
  gap: 20rpx;
}

.reject-btn,
.approve-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--radius-xl);
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.reject-btn {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B60 100%);
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(255, 59, 48, 0.3);
}

.approve-btn {
  background: linear-gradient(135deg, #00A86B 0%, #00C875 100%);
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(0, 168, 107, 0.3);
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* ==================== 加载状态 ==================== */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* ==================== 审批弹窗 ==================== */
.approval-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
}

.modal-body {
  padding: 30rpx;
}

.comment-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.comment-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.comment-textarea:focus {
  border-color: #0066CC;
  background: #f8fbff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e8e8e8;
}

.confirm-btn {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(0, 102, 204, 0.3);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}