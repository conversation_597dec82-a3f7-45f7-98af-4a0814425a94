// 用户信息工具类
// 优化OA系统用户信息复用，避免重复填写

/**
 * 获取当前用户完整信息
 * @returns {Object} 用户信息对象
 */
function getCurrentUserInfo() {
  try {
    let userInfo = wx.getStorageSync('user_info');
    const token = wx.getStorageSync('access_token');
    
    // 调试信息（开发环境输出）
    try { if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) { try { const logger = require('./logger.js'); logger.debug && logger.debug('getCurrentUserInfo - storage', userInfo, token); } catch(_) {} } } catch(_) {}
    
    // 如果本地存储没有有效的用户信息，尝试从全局数据获取
    if (!userInfo || typeof userInfo !== 'object' || !userInfo.name) {
      const app = getApp();
      try { if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) { try { const logger = require('./logger.js'); logger.debug && logger.debug('getCurrentUserInfo - trying globalData:', app.globalData.userInfo); } catch(_) {} } } catch(_) {}
      if (app.globalData.userInfo && app.globalData.userInfo.name) {
        userInfo = app.globalData.userInfo;
        // 同步到本地存储
        wx.setStorageSync('user_info', userInfo);
        try { if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) { try { const logger = require('./logger.js'); logger.debug && logger.debug('getCurrentUserInfo - synced to storage'); } catch(_) {} } } catch(_) {}
      }
    }
    
    if (!userInfo || !token) {
      try { if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) { try { const logger = require('./logger.js'); logger.debug && logger.debug('getCurrentUserInfo - no valid user info or token'); } catch(_) {} } } catch(_) {}
      return null;
    }
    
    return {
      id: userInfo.id,
      name: userInfo.name || '',
      username: userInfo.username || userInfo.name || '',
      employeeId: userInfo.employee_id || userInfo.id || '',
      department: userInfo.department || '生产部',
      position: userInfo.position || '员工',
      phone: userInfo.phone || '',
      email: userInfo.email || '',
      emergencyContact: userInfo.emergency_contact || '',
      emergencyPhone: userInfo.emergency_phone || '',
      officeLocation: userInfo.office_location || '',
      managerId: userInfo.manager_id || null,
      managerName: userInfo.manager_name || '',
      role: userInfo.roleCode || userInfo.role || 'staff', // 使用roleCode字段
      hireDate: userInfo.hire_date || ''
    };
  } catch (error) {
    try { const logger = require('./logger.js'); logger.error && logger.error('获取用户信息失败', error); } catch(_) {}
    return null;
  }
}

/**
 * 获取用户基本申请信息（用于表单自动填充）
 * @returns {Object} 申请信息对象
 */
function getUserApplicationInfo() {
  const userInfo = getCurrentUserInfo();
  
  if (!userInfo) {
    return {};
  }
  
  return {
    applicantName: userInfo.name,
    applicantId: userInfo.employeeId,
    department: userInfo.department,
    position: userInfo.position,
    phone: userInfo.phone,
    email: userInfo.email,
    managerId: userInfo.managerId,
    managerName: userInfo.managerName
  };
}

/**
 * 刷新用户信息缓存
 * @returns {Promise<Object>} 最新用户信息
 */
function refreshUserInfo() {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('access_token');
    
    if (!token) {
      reject(new Error('用户未登录'));
      return;
    }
    
    try {
      const { get } = require('./request.js');
      const { API } = require('../constants/index.js');
      get(API.ENDPOINTS.AUTH.USER_INFO)
        .then((res) => {
          const payload = res && res.success !== false ? (res.data || res) : null;
          if (!payload) return reject(new Error(res?.message || '获取用户信息失败'));
          wx.setStorageSync('user_info', payload);
          const app = getApp();
          if (app && app.globalData) {
            app.globalData.userInfo = payload;
          }
          resolve(payload);
        })
        .catch((error) => reject(error));
    } catch (e) {
      reject(e);
    }
  });
}

/**
 * 检查用户信息完整性
 * @param {Array} requiredFields 必填字段列表
 * @returns {Object} 检查结果
 */
function checkUserInfoCompleteness(requiredFields = []) {
  const userInfo = getCurrentUserInfo();
  
  if (!userInfo) {
    return {
      isComplete: false,
      missingFields: requiredFields,
      message: '请先登录'
    };
  }
  
  const missingFields = [];
  
  requiredFields.forEach(field => {
    if (!userInfo[field]) {
      missingFields.push(field);
    }
  });
  
  return {
    isComplete: missingFields.length === 0,
    missingFields,
    message: missingFields.length > 0 ? 
      `请完善以下信息：${missingFields.join('、')}` : '信息完整'
  };
}

/**
 * 格式化用户显示信息
 * @param {Object} userInfo 用户信息
 * @returns {String} 格式化后的显示文本
 */
function formatUserDisplayInfo(userInfo) {
  if (!userInfo) {
    return '未知用户';
  }
  
  const parts = [];
  
  if (userInfo.name) {
    parts.push(userInfo.name);
  }
  
  if (userInfo.employeeId) {
    parts.push(`(${userInfo.employeeId})`);
  }
  
  if (userInfo.department) {
    parts.push(userInfo.department);
  }
  
  if (userInfo.position) {
    parts.push(userInfo.position);
  }
  
  return parts.join(' ');
}

/**
 * 获取用户角色权限信息
 * @returns {Object} 权限信息
 */
function getUserPermissions() {
  const userInfo = getCurrentUserInfo();
  
  if (!userInfo) {
    return {
      canCreateApplication: false,
      canApprove: false,
      canViewFinance: false,
      canExportData: false
    };
  }
  
  const role = userInfo.role || userInfo.roleCode;
  const department = userInfo.department;
  
  // 统一角色判断函数
  const hasRole = (roles) => {
    if (!Array.isArray(roles)) roles = [roles];
    return roles.some(r => {
      if (role === r) return true;
      // 处理角色映射
      const roleMap = {
        'admin': ['管理员', 'super_admin', 'owner'],
        'manager': ['经理', 'department_manager', 'finance_manager'],
        'employee': ['员工', 'staff'],
        'finance': ['财务', 'finance_manager'],
        'hr': ['人事', 'hr_manager']
      };
      return roleMap[r] && roleMap[r].includes(role);
    });
  };
  
  // 特殊部门权限
  const isFinanceDept = department === '财务部' || department === 'finance';
  const isHRDept = department === '人事部' || department === 'hr';
  
  return {
    // 基础权限
    canCreateApplication: hasRole(['staff', 'employee', 'manager', 'admin', 'owner']) || true,
    canApprove: hasRole(['manager', 'admin', 'owner']),
    canViewFinance: hasRole(['admin', 'owner', 'finance']) || isFinanceDept,
    canExportData: hasRole(['admin', 'owner']),
    canManageUsers: hasRole(['admin', 'owner', 'hr']) || isHRDept,
    canConfigureSystem: hasRole(['admin', 'owner']),
    
    // 采购权限
    purchase_view: true,
    purchase_create: hasRole(['staff', 'employee', 'manager', 'admin', 'owner']) || true,
    purchase_edit: hasRole(['staff', 'employee', 'manager', 'admin', 'owner']) || true,
    purchase_cancel: hasRole(['staff', 'employee', 'manager', 'admin', 'owner']) || true,
    purchase_approve: hasRole(['manager', 'admin', 'owner']),
    
    // 财务权限
    finance_view: hasRole(['admin', 'owner', 'finance']) || isFinanceDept,
    finance_create: hasRole(['admin', 'owner', 'finance']) || isFinanceDept,
    finance_edit: hasRole(['admin', 'owner', 'finance']) || isFinanceDept,
    finance_export: hasRole(['admin', 'owner', 'finance']) || isFinanceDept,
    
    // OA系统权限
    oa_access: true,
    approval_view: true,
    approval_process: hasRole(['manager', 'admin', 'owner']),
    task_view: true,
    task_create: hasRole(['staff', 'employee', 'manager', 'admin', 'owner']) || true,
    task_assign: hasRole(['manager', 'admin', 'owner']),
    
    // 系统管理权限
    system_config: hasRole(['admin', 'owner']),
    user_manage: hasRole(['admin', 'owner', 'hr']) || isHRDept,
    role_manage: hasRole(['admin', 'owner'])
  };
}

module.exports = {
  getCurrentUserInfo,
  getUserApplicationInfo,
  refreshUserInfo,
  checkUserInfoCompleteness,
  formatUserDisplayInfo,
  getUserPermissions
};