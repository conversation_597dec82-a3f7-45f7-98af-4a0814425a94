# 智慧养鹅小程序常量管理系统

## 📋 概述

统一的常量管理系统，用于管理小程序中的API端点、图片路径、UI设计规范、业务逻辑和应用配置等。

## 🗂️ 文件结构

```
constants/
├── index.js              # 统一导出入口
├── api.constants.js       # API相关常量
├── images.constants.js    # 图片资源常量
├── ui.constants.js        # UI设计常量
├── business.constants.js  # 业务逻辑常量
├── config.constants.js    # 应用配置常量
└── README.md             # 使用说明文档
```

## 🚀 快速开始

### 基础用法

```javascript
// 导入所有常量
const CONSTANTS = require('../constants/index.js');

// 使用API端点
const url = CONSTANTS.API.ENDPOINTS.FLOCKS.LIST;

// 使用图片路径
const avatar = CONSTANTS.IMAGES.DEFAULTS.AVATAR;

// 使用UI颜色
const primaryColor = CONSTANTS.UI.COLORS.PRIMARY.DEFAULT;

// 使用业务状态
const healthStatus = CONSTANTS.BUSINESS.HEALTH.RESULTS.HEALTHY;
```

### 按需导入

```javascript
// 只导入需要的模块
const { API, IMAGES, UI } = require('../constants/index.js');

// 或者直接导入特定模块
const API = require('../constants/api.constants.js');
const IMAGES = require('../constants/images.constants.js');
```

## 📖 使用示例

### 1. API调用示例

**原始代码 (分散管理):**
```javascript
// utils/api.js
const BASE_URL = 'http://localhost:3001/api/v1';
const FLOCKS_API = BASE_URL + '/flocks';

// pages/home/<USER>
wx.request({
  url: 'http://localhost:3001/api/v1/health/records',
  method: 'GET'
});
```

**优化后 (统一管理):**
```javascript
// 使用新的常量系统
const { API } = require('../../constants/index.js');

// pages/home/<USER>
wx.request({
  url: API.ENDPOINTS.HEALTH.RECORDS,
  method: API.HTTP_METHODS.GET,
  timeout: API.REQUEST_CONFIG.TIMEOUT
});
```

### 2. 图片路径示例

**原始代码 (路径混乱):**
```javascript
// pages/profile/profile.js
data: {
  userInfo: {
    avatar: '/assets/icons/default_avatar.png'
  },
  menuItems: [
    {
      icon: '/images/icons/map-pin.svg',
      title: '收货地址'
    },
    {
      icon: '/assets/icons/settings.png', 
      title: '系统设置'
    }
  ]
}
```

**优化后 (统一管理):**
```javascript
// 使用新的常量系统
const { IMAGES } = require('../../constants/index.js');

// pages/profile/profile.js
data: {
  userInfo: {
    avatar: IMAGES.DEFAULTS.AVATAR
  },
  menuItems: [
    {
      icon: IMAGES.ICONS.LOCATION,
      title: '收货地址'
    },
    {
      icon: IMAGES.ICONS.SETTINGS,
      title: '系统设置'
    }
  ]
}
```

### 3. UI样式示例

**原始代码 (硬编码颜色):**
```css
/* pages/health/ai-diagnosis.wxss */
.diagnosis-container {
  background-color: #F5F6F8;
  padding: 20rpx;
}

.delete-btn {
  background-color: #FF3333;
  color: #FFFFFF;
  border-radius: 50%;
}

.confidence {
  color: #0066CC;
  background-color: #E6F4FF;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
}
```

**优化后 (使用设计系统):**
```javascript
// 在小程序中动态设置样式
const { UI } = require('../../constants/index.js');

Page({
  data: {
    styles: {
      container: {
        backgroundColor: UI.COLORS.BACKGROUND.SECONDARY,
        padding: UI.SPACING.BASE + 'rpx'
      },
      deleteBtn: {
        backgroundColor: UI.COLORS.ERROR.DEFAULT,
        color: UI.COLORS.TEXT.INVERSE,
        borderRadius: UI.BORDER_RADIUS.FULL + 'rpx'
      },
      confidence: {
        color: UI.COLORS.INFO.DEFAULT,
        backgroundColor: UI.COLORS.INFO.LIGHT,
        padding: `${UI.SPACING.XS}rpx ${UI.SPACING.SM}rpx`,
        borderRadius: UI.BORDER_RADIUS.FULL + 'rpx'
      }
    }
  }
});
```

### 4. 业务状态示例

**原始代码 (硬编码状态):**
```javascript
// pages/health/health.js
Page({
  data: {
    healthStatus: 'healthy', // 硬编码
    statusText: '健康'       // 硬编码
  },
  
  getStatusColor(status) {
    switch(status) {
      case 'healthy': return '#28A745';
      case 'sick': return '#DC3545';
      default: return '#999999';
    }
  }
});
```

**优化后 (使用业务常量):**
```javascript
// 使用新的常量系统
const { BUSINESS, UI } = require('../../constants/index.js');

Page({
  data: {
    healthStatus: BUSINESS.HEALTH.RESULTS.HEALTHY,
    statusText: BUSINESS.HEALTH.RESULT_NAMES.healthy
  },
  
  getStatusColor(status) {
    const colorMap = {
      [BUSINESS.HEALTH.RESULTS.HEALTHY]: UI.COLORS.SUCCESS.DEFAULT,
      [BUSINESS.HEALTH.RESULTS.SICK]: UI.COLORS.ERROR.DEFAULT,
      [BUSINESS.HEALTH.RESULTS.RECOVERING]: UI.COLORS.WARNING.DEFAULT
    };
    return colorMap[status] || UI.COLORS.NEUTRAL.GRAY_500;
  }
});
```

### 5. 功能开关示例

```javascript
// 使用配置管理功能开关
const { CONFIG } = require('../../constants/index.js');

Page({
  onLoad() {
    // 检查AI功能是否启用
    if (CONFIG.CONFIG_UTILS.isFeatureEnabled('AI_FEATURES.HEALTH_DIAGNOSIS')) {
      this.initAIDiagnosis();
    }
    
    // 检查实验性功能
    if (CONFIG.CONFIG_UTILS.isFeatureEnabled('EXPERIMENTAL.VOICE_ASSISTANT')) {
      this.enableVoiceAssistant();
    }
  }
});
```

## 🎯 迁移指南

### 步骤1: 替换API端点

1. 找到所有硬编码的API地址
2. 替换为 `API.ENDPOINTS.*` 常量
3. 统一使用 `API.HTTP_METHODS.*` 和 `API.HTTP_STATUS.*`

### 步骤2: 统一图片路径

1. 整理所有图片资源路径
2. 按类型分类（图标、背景、业务图片等）
3. 替换为 `IMAGES.*` 常量

### 步骤3: 应用设计系统

1. 提取所有硬编码的颜色值
2. 使用 `UI.COLORS.*` 替换
3. 统一间距和尺寸为 `UI.SPACING.*` 和 `UI.SIZES.*`

### 步骤4: 规范业务逻辑

1. 找到所有业务状态硬编码
2. 替换为 `BUSINESS.*` 常量
3. 使用统一的显示名称映射

## 📚 常量分类详解

### API常量 (`api.constants.js`)
- **环境配置**: 开发、测试、生产环境
- **版本管理**: V1、V2 API版本
- **端点配置**: 所有API端点地址
- **状态码**: HTTP状态码
- **错误处理**: 错误类型和消息

### 图片常量 (`images.constants.js`)
- **路径规范**: 统一的图片路径结构
- **分类管理**: 图标、背景、业务图片
- **工具函数**: 获取图片路径的辅助方法

### UI常量 (`ui.constants.js`)
- **色彩系统**: 主题色、功能色、中性色
- **字体系统**: 字体大小、重量、行高
- **间距系统**: 统一的间距规范
- **动画系统**: 动画时长和缓动函数

### 业务常量 (`business.constants.js`)
- **状态枚举**: 各模块的状态定义
- **显示名称**: 状态的中文显示
- **验证规则**: 数据验证正则表达式
- **业务限制**: 数量、长度等限制

### 配置常量 (`config.constants.js`)
- **功能开关**: 控制功能的启用/禁用
- **性能配置**: 缓存、分页等性能设置
- **安全配置**: 认证、密码策略等
- **第三方配置**: 外部服务配置

## 🔧 最佳实践

### 1. 命名规范
- 使用大写字母和下划线
- 保持语义化和一致性
- 分类清晰，层级明确

### 2. 维护原则
- 新增常量时要考虑分类
- 删除常量前检查引用
- 定期清理未使用的常量

### 3. 性能优化
- 按需导入减少包大小
- 使用工具函数而非直接访问
- 缓存频繁使用的常量

### 4. 团队协作
- 统一使用常量系统
- 提交前检查常量使用
- 文档保持同步更新

## 🆕 版本更新

### V2.6.0 (2025-01-31)
- 🎉 初始版本发布
- ✨ 完整的常量管理体系
- 📚 详细的使用文档
- 🛠️ 实用的工具函数

## 📝 贡献指南

1. 新增常量时请确保分类正确
2. 保持命名的一致性和语义化
3. 添加必要的注释和文档
4. 测试常量的正确性

## 🤝 技术支持

如有疑问或建议，请联系开发团队。

---

**智慧养鹅开发团队**  
*让代码更规范，让维护更简单* 🚀