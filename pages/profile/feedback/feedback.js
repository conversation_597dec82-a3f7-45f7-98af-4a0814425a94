// pages/profile/feedback/feedback.js
Page({
  data: {
    // 反馈类型
    feedbackTypes: [
      {
        value: 'bug',
        title: '功能异常',
        icon: '🐛'
      },
      {
        value: 'suggestion',
        title: '功能建议',
        icon: '💡'
      },
      {
        value: 'ui',
        title: '界面问题',
        icon: '🎨'
      },
      {
        value: 'performance',
        title: '性能问题',
        icon: '⚡'
      },
      {
        value: 'data',
        title: '数据问题',
        icon: '📊'
      },
      {
        value: 'other',
        title: '其他问题',
        icon: '❓'
      }
    ],

    // 反馈数据
    feedbackData: {
      type: '',
      description: '',
      phone: '',
      email: '',
      images: [],
      includeSystemInfo: true
    },

    // 系统信息
    systemInfo: {
      model: '',
      system: '',
      appVersion: '2.1.0'
    },

    // 历史反馈
    historyFeedbacks: [
      {
        id: 'FB001',
        type: 'bug',
        typeName: '功能异常',
        description: '环境监控数据显示异常，温度数据无法更新',
        status: 'resolved',
        statusText: '已解决',
        createTime: '2024-11-28 14:30',
        reply: '问题已修复，请更新到最新版本'
      },
      {
        id: 'FB002',
        type: 'suggestion',
        typeName: '功能建议',
        description: '希望增加数据导出功能',
        status: 'processing',
        statusText: '处理中',
        createTime: '2024-11-25 09:15'
      }
    ],

    // 提交状态
    submitting: false,
    canSubmit: false,
    showSuccessModal: false,
    feedbackId: ''
  },

  onLoad: function (options) {
    // 获取系统信息
    this.getSystemInfo();
    
    // 加载历史反馈
    this.loadHistoryFeedbacks();
  },

  onShow: function () {
    // 检查提交条件
    this.checkCanSubmit();
  },

  // 获取系统信息
  getSystemInfo: function() {
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          systemInfo: {
            model: res.model,
            system: res.system,
            appVersion: this.data.systemInfo.appVersion
          }
        });
      }
    });
  },

  // 加载历史反馈
  loadHistoryFeedbacks: function() {
    // 从本地存储获取历史反馈
    const history = wx.getStorageSync('feedbackHistory') || [];
    this.setData({
      historyFeedbacks: history
    });
  },

  // 选择反馈类型
  onSelectType: function(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      'feedbackData.type': type
    });
    this.checkCanSubmit();
  },

  // 输入问题描述
  onDescriptionInput: function(e) {
    const value = e.detail.value;
    this.setData({
      'feedbackData.description': value
    });
    this.checkCanSubmit();
  },

  // 输入手机号
  onPhoneInput: function(e) {
    const value = e.detail.value;
    this.setData({
      'feedbackData.phone': value
    });
  },

  // 输入邮箱
  onEmailInput: function(e) {
    const value = e.detail.value;
    this.setData({
      'feedbackData.email': value
    });
  },

  // 系统信息开关
  onSystemInfoChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'feedbackData.includeSystemInfo': value
    });
  },

  // 选择图片
  onChooseImage: function() {
    const maxCount = 3 - this.data.feedbackData.images.length;
    
    wx.chooseMedia({
      count: maxCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePaths = res.tempFiles.map(file => file.tempFilePath);
        
        // 压缩图片
        this.compressImages(tempFilePaths);
      },
      fail: (err) => {
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('选择图片失败', err); } catch(_) {}
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 压缩图片
  compressImages: function(tempFilePaths) {
    wx.showLoading({
      title: '处理图片中...'
    });

    const compressPromises = tempFilePaths.map(filePath => {
      return new Promise((resolve, reject) => {
        wx.compressImage({
          src: filePath,
          quality: 80,
          success: (res) => {
            resolve(res.tempFilePath);
          },
          fail: (err) => {
            try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('压缩图片失败', err); } catch(_) {}
            resolve(filePath); // 压缩失败时使用原图
          }
        });
      });
    });

    Promise.all(compressPromises).then((compressedPaths) => {
      const currentImages = this.data.feedbackData.images;
      const newImages = [...currentImages, ...compressedPaths];
      
      this.setData({
        'feedbackData.images': newImages
      });
      
      wx.hideLoading();
      wx.showToast({
        title: `已添加${compressedPaths.length}张图片`,
        icon: 'success'
      });
    });
  },

  // 预览图片
  onPreviewImage: function(e) {
    const { url } = e.currentTarget.dataset;
    const images = this.data.feedbackData.images;
    
    wx.previewImage({
      current: url,
      urls: images
    });
  },

  // 删除图片
  onDeleteImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const images = this.data.feedbackData.images;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          images.splice(index, 1);
          this.setData({
            'feedbackData.images': images
          });
          
          wx.showToast({
            title: '图片已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 检查是否可以提交
  checkCanSubmit: function() {
    const { type, description } = this.data.feedbackData;
    const canSubmit = type && description.trim().length >= 10;
    
    this.setData({
      canSubmit: canSubmit
    });
  },

  // 提交反馈
  onSubmitFeedback: function() {
    if (!this.data.canSubmit || this.data.submitting) {
      return;
    }

    // 验证必填字段
    if (!this.data.feedbackData.type) {
      wx.showToast({
        title: '请选择反馈类型',
        icon: 'none'
      });
      return;
    }

    if (this.data.feedbackData.description.trim().length < 10) {
      wx.showToast({
        title: '问题描述至少需要10个字符',
        icon: 'none'
      });
      return;
    }

    // 验证邮箱格式
    const email = this.data.feedbackData.email.trim();
    if (email && !this.validateEmail(email)) {
      wx.showToast({
        title: '邮箱格式不正确',
        icon: 'none'
      });
      return;
    }

    // 验证手机号格式
    const phone = this.data.feedbackData.phone.trim();
    if (phone && !this.validatePhone(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交API调用
    this.submitToServer();
  },

  // 提交到服务器
  submitToServer: function() {
    const feedbackData = this.data.feedbackData;
    const systemInfo = this.data.systemInfo;
    
    // 生成反馈ID
    const feedbackId = 'FB' + Date.now().toString().slice(-6);
    
    // 构造提交数据
    const submitData = {
      id: feedbackId,
      type: feedbackData.type,
      description: feedbackData.description.trim(),
      phone: feedbackData.phone.trim(),
      email: feedbackData.email.trim(),
      images: feedbackData.images,
      systemInfo: feedbackData.includeSystemInfo ? systemInfo : null,
      createTime: new Date().toLocaleString(),
      status: 'pending',
      statusText: '待处理'
    };

    // 模拟网络延迟
    setTimeout(() => {
      wx.hideLoading();
      
      // 保存到历史记录
      this.saveToHistory(submitData);
      
      this.setData({
        submitting: false,
        showSuccessModal: true,
        feedbackId: feedbackId
      });

      // 重置表单
      this.resetForm();
      
    }, 2000);
  },

  // 保存到历史记录
  saveToHistory: function(feedbackData) {
    let history = wx.getStorageSync('feedbackHistory') || [];
    
    // 添加到开头
    history.unshift(feedbackData);
    
    // 只保留最近20条
    history = history.slice(0, 20);
    
    // 保存到本地存储
    wx.setStorageSync('feedbackHistory', history);
    
    // 更新页面数据
    this.setData({
      historyFeedbacks: history
    });
  },

  // 重置表单
  resetForm: function() {
    this.setData({
      feedbackData: {
        type: '',
        description: '',
        phone: '',
        email: '',
        images: [],
        includeSystemInfo: true
      },
      canSubmit: false
    });
  },

  // 关闭成功弹窗
  onCloseSuccessModal: function() {
    this.setData({
      showSuccessModal: false,
      feedbackId: ''
    });
  },

  // 查看历史反馈详情
  onViewHistory: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/profile/feedback/detail/detail?id=${id}`,
      fail: () => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    });
  },

  // 查看全部历史反馈
  onViewAllHistory: function() {
    wx.navigateTo({
      url: '/pages/profile/feedback/history/history',
      fail: () => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    });
  },

  // 验证邮箱格式
  validateEmail: function(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 验证手机号格式
  validatePhone: function(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '智慧养鹅管理系统 - 意见反馈',
      desc: '您的建议让我们更好',
      path: '/pages/profile/feedback/feedback'
    };
  }
});