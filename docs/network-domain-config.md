# 🌐 微信小程序网络域名配置指南

## 📅 生成时间
**时间**: 2025/8/4 22:03:08  
**修复脚本**: fix-runtime-errors.js

---

## 🚨 问题描述

当前项目出现网络请求域名校验错误：
```
request 合法域名校验出错
http://localhost:3000 不在以下 request 合法域名列表中
```

---

## 🔧 解决方案

### **方案一: 开发环境配置**

1. **打开微信开发者工具**
2. **进入项目详情页面**：点击右上角"详情"按钮
3. **找到"域名信息"选项卡**
4. **配置合法域名**：

#### **request合法域名**:
```
https://636c-cloud1-3gdruqkn67e1cbe2-1362002942.tcb.qcloud.la
https://tcb-api.tencentcloudapi.com
http://localhost:3000
```

#### **socket合法域名**:
```
暂无
```

#### **uploadFile合法域名**:
```
暂无
```

#### **downloadFile合法域名**:
```
暂无
```

### **方案二: 开发调试临时解决**

在开发者工具中：
1. **点击"详情" → "本地设置"**
2. **勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"**
3. **重新编译项目**

⚠️  **注意**: 此方案仅适用于开发环境，正式发布前必须配置正确的域名

---

## 📋 操作步骤

### **步骤1: 配置开发环境**
```bash
# 1. 在微信开发者工具中打开项目
# 2. 点击右上角"详情"按钮  
# 3. 选择"域名信息"选项卡
# 4. 将上述域名添加到对应的配置中
```

### **步骤2: 更新项目配置**
```bash
# 刷新项目配置
# 操作路径："详情-域名信息" → 点击刷新按钮
```

### **步骤3: 重新编译**
```bash
# 1. 保存所有文件
# 2. 在开发者工具中点击"编译"按钮
# 3. 验证网络请求是否正常
```

---

## 🧪 验证测试

### **测试请求**
```javascript
// 在控制台中测试网络请求
wx.request({
  url: 'http://localhost:3000/api/test',
  method: 'GET',
  success: (res) => {
    console.log('✅ 网络请求成功:', res);
  },
  fail: (err) => {
    console.error('❌ 网络请求失败:', err);
  }
});
```

### **预期结果**
- ✅ 控制台不再出现域名校验错误
- ✅ API请求能正常发送和接收响应
- ✅ 页面功能恢复正常

---

## 🚀 生产环境配置

### **正式发布前准备**

1. **域名备案**: 确保所有域名已完成ICP备案
2. **HTTPS配置**: 所有生产域名必须支持HTTPS
3. **微信后台配置**:
   - 登录[微信公众平台](https://mp.weixin.qq.com)
   - 进入小程序管理后台
   - 配置服务器域名

### **生产域名示例**
```
request合法域名:
- https://api.yourcompany.com
- https://your-backend-domain.com

socket合法域名:  
- wss://socket.yourcompany.com

uploadFile合法域名:
- https://upload.yourcompany.com

downloadFile合法域名:
- https://download.yourcompany.com
```

---

## 📝 常见问题

### **Q1: 配置后仍然报错怎么办？**
A: 
1. 检查域名拼写是否正确
2. 确保包含了协议头(http://或https://)
3. 重新编译项目
4. 清除缓存后重试

### **Q2: localhost域名配置不生效？**
A: 
1. 确认开发者工具版本是否最新
2. 尝试使用"不校验合法域名"选项
3. 检查网络连接是否正常

### **Q3: 如何批量配置域名？**
A:
1. 准备域名列表（每行一个）
2. 在域名配置页面批量粘贴
3. 保存并刷新项目配置

---

## 📊 修复记录

- **修复时间**: 2025/8/4 22:03:08
- **配置域名数**: 3个
- **脚本版本**: v1.0.0
- **修复状态**: ✅ 配置指南已生成

---

**🎯 配置完成后，项目的网络请求功能将恢复正常，不再出现域名校验错误。**