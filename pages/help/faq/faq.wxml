<!-- FAQ常见问题页面 -->
<view class="faq-container">
  <!-- 页面头部 -->
  <view class="faq-header">
    <view class="header-content">
      <text class="header-icon">❓</text>
      <view class="header-text">
        <text class="header-title">常见问题</text>
        <text class="header-desc">快速找到您遇到问题的解决方案</text>
      </view>
    </view>
    <view class="header-stats">
      <text class="stats-text">已收录{{totalQuestions}}个问题</text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-container">
      <text class="search-icon">🔍</text>
      <input class="search-input" 
             placeholder="搜索问题关键词..." 
             value="{{searchKeyword}}" 
             bindinput="onSearchInput"
             bindconfirm="onSearch" />
      <text class="clear-btn" wx:if="{{searchKeyword}}" bindtap="onClearSearch">×</text>
    </view>
  </view>

  <!-- 热门标签 -->
  <view class="tags-section">
    <view class="tags-header">
      <text class="tags-title">热门标签</text>
    </view>
    <view class="tags-list">
      <text class="tag-item {{selectedTag === item ? 'active' : ''}}" 
            wx:for="{{hotTags}}" 
            wx:key="*this"
            bindtap="onSelectTag" 
            data-tag="{{item}}">{{item}}</text>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="filter-section">
    <view class="filter-tabs">
      <text class="filter-tab {{activeCategory === item.key ? 'active' : ''}}" 
            wx:for="{{categories}}" 
            wx:key="key"
            bindtap="onSelectCategory" 
            data-category="{{item.key}}">{{item.name}}</text>
    </view>
  </view>

  <!-- FAQ列表 -->
  <view class="faq-list-section">
    <view class="list-header">
      <text class="list-title">{{getCategoryName(activeCategory)}}问题</text>
      <text class="result-count">{{filteredQuestions.length}}个结果</text>
    </view>
    
    <view class="faq-list">
      <view class="faq-item" 
            wx:for="{{filteredQuestions}}" 
            wx:key="id">
        <view class="faq-question" bindtap="onToggleFAQ" data-index="{{index}}">
          <view class="question-content">
            <text class="question-text">{{item.question}}</text>
            <view class="question-meta">
              <text class="category-tag">{{item.categoryName}}</text>
              <text class="helpful-count">{{item.helpful}}人觉得有用</text>
            </view>
          </view>
          <view class="question-actions">
            <text class="arrow-icon {{item.expanded ? 'expanded' : ''}}">{{item.expanded ? '▲' : '▼'}}</text>
          </view>
        </view>
        
        <view wx:if="{{item.expanded}}" class="faq-answer">
          <text class="answer-text">{{item.answer}}</text>
          
          <!-- 答案图片 -->
          <view class="answer-images" wx:if="{{item.images && item.images.length > 0}}">
            <image class="answer-image" 
                   wx:for="{{item.images}}" 
                   wx:key="*this"
                   wx:for-item="image"
                   src="{{image}}" 
                   mode="aspectFit"
                   bindtap="onPreviewImage" 
                   data-src="{{image}}" />
          </view>
          
          <!-- 相关链接 -->
          <view class="related-links" wx:if="{{item.relatedLinks && item.relatedLinks.length > 0}}">
            <text class="links-title">相关文章：</text>
            <view class="links-list">
              <text class="link-item" 
                    wx:for="{{item.relatedLinks}}" 
                    wx:key="id"
                    wx:for-item="link"
                    bindtap="onViewArticle" 
                    data-article="{{link}}">{{link.title}}</text>
            </view>
          </view>
          
          <!-- 反馈按钮 -->
          <view class="answer-actions">
            <button class="helpful-btn {{item.userRating === true ? 'active' : ''}}" 
                    bindtap="onMarkHelpful" 
                    data-id="{{item.id}}" 
                    data-helpful="true">
              <text class="btn-icon">👍</text>
              <text class="btn-text">有帮助 ({{item.helpful}})</text>
            </button>
            <button class="unhelpful-btn {{item.userRating === false ? 'active' : ''}}" 
                    bindtap="onMarkHelpful" 
                    data-id="{{item.id}}" 
                    data-helpful="false">
              <text class="btn-icon">👎</text>
              <text class="btn-text">没帮助 ({{item.unhelpful}})</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredQuestions.length === 0}}">
      <text class="empty-icon">🤔</text>
      <text class="empty-text">没有找到相关问题</text>
      <text class="empty-tips">试试其他关键词或浏览不同分类</text>
      <button class="ask-btn" bindtap="onAskQuestion">
        <text class="ask-icon">💬</text>
        <text class="ask-text">提问题</text>
      </button>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="action-card" bindtap="onAskQuestion">
      <text class="action-icon">💬</text>
      <view class="action-content">
        <text class="action-title">没找到答案？</text>
        <text class="action-desc">提交您的问题，我们会尽快回复</text>
      </view>
      <text class="action-arrow">></text>
    </view>
    
    <view class="action-card" bindtap="onContactService">
      <text class="action-icon">🎧</text>
      <view class="action-content">
        <text class="action-title">联系客服</text>
        <text class="action-desc">获得专业的人工服务支持</text>
      </view>
      <text class="action-arrow">></text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-header">
      <text class="stats-title">帮助统计</text>
    </view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{faqStats.totalQuestions}}</text>
        <text class="stat-label">个问题</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{faqStats.totalViews}}</text>
        <text class="stat-label">次浏览</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{faqStats.resolvedRate}}%</text>
        <text class="stat-label">解决率</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{faqStats.avgResponseTime}}</text>
        <text class="stat-label">小时响应</text>
      </view>
    </view>
  </view>
</view>