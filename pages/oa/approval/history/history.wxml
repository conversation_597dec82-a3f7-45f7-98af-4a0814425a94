<!-- pages/oa/approval/history/history.wxml -->
<view class="history-container">
  
  <!-- 页面头部信息 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">审批历史</text>
      <view class="header-summary">
        <text class="summary-text">已处理 {{statistics.total}} 项审批</text>
        <view class="success-rate">
          <text class="rate-text">通过率 {{statistics.approvalRate}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="stats-row">
      <view class="stat-card approved" bindtap="onStatusFilter" data-status="approved">
        <text class="stat-number">{{statistics.approved}}</text>
        <text class="stat-label">已通过</text>
      </view>
      <view class="stat-card rejected" bindtap="onStatusFilter" data-status="rejected">
        <text class="stat-number">{{statistics.rejected}}</text>
        <text class="stat-label">已拒绝</text>
      </view>
      <view class="stat-card monthly" bindtap="onTimeFilter" data-time="month">
        <text class="stat-number">{{statistics.thisMonth}}</text>
        <text class="stat-label">本月</text>
      </view>
    </view>
  </view>

  <!-- 搜索和筛选 -->
  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索审批记录..."
        value="{{filters.keyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearchConfirm"
      />
      <view wx:if="{{filters.keyword}}" class="clear-search" bindtap="clearSearch">
        <text>×</text>
      </view>
    </view>
    
    <view class="filter-toggle" bindtap="toggleFilters">
      <text class="filter-icon">{{showFilters ? '📋' : '⚙️'}}</text>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view wx:if="{{showFilters}}" class="filter-panel">
    <view class="filter-section">
      <text class="filter-title">审批状态</text>
      <view class="filter-options">
        <view 
          wx:for="{{statusOptions}}" 
          wx:key="value"
          class="filter-option {{filters.status === item.value ? 'active' : ''}}"
          bindtap="onStatusChange"
          data-value="{{item.value}}"
        >
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="filter-title">业务类型</text>
      <view class="filter-options">
        <view 
          wx:for="{{businessTypeOptions}}" 
          wx:key="value"
          class="filter-option {{filters.businessType === item.value ? 'active' : ''}}"
          bindtap="onBusinessTypeChange"
          data-value="{{item.value}}"
        >
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="filter-title">时间范围</text>
      <view class="filter-options">
        <view 
          wx:for="{{timeRangeOptions}}" 
          wx:key="value"
          class="filter-option {{filters.timeRange === item.value ? 'active' : ''}}"
          bindtap="onTimeRangeChange"
          data-value="{{item.value}}"
        >
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="filter-actions">
      <button class="reset-btn" bindtap="resetFilters">重置</button>
      <button class="apply-btn" bindtap="applyFilters">应用</button>
    </view>
  </view>

  <!-- 审批历史列表 -->
  <view class="history-list">
    <!-- 加载状态 -->
    <view wx:if="{{refreshing}}" class="loading-state">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{approvals.length === 0 && !loading}}" class="empty-state">
      <text class="empty-icon">📋</text>
      <text class="empty-title">暂无审批记录</text>
      <text class="empty-desc">您还没有处理过任何审批申请</text>
      <button class="refresh-btn" bindtap="refreshData">刷新页面</button>
    </view>

    <!-- 历史记录列表 -->
    <view wx:else>
      <view 
        wx:for="{{approvals}}" 
        wx:key="id" 
        class="history-item"
        bindtap="onHistoryTap"
        data-approval="{{item}}"
      >
        <view class="item-header">
          <view class="title-section">
            <text class="item-title">{{item.title}}</text>
            <view class="status-info">
              <view class="approval-status {{item.status}}">
                <text class="status-text">{{item.statusText}}</text>
              </view>
              <view class="business-tag {{item.business_type}}">
                <text>{{item.businessTypeLabel}}</text>
              </view>
            </view>
          </view>
          <text class="approval-time">{{item.approvalTime}}</text>
        </view>

        <view class="item-content">
          <view class="applicant-section">
            <view class="applicant-info">
              <text class="info-icon">👤</text>
              <text class="applicant-name">{{item.applicant_name}}</text>
              <text class="applicant-dept">{{item.applicant_department}}</text>
            </view>
            <view wx:if="{{item.amount}}" class="amount-info">
              <text class="info-icon">💰</text>
              <text class="amount-text">¥{{item.amount}}</text>
            </view>
          </view>
          
          <view wx:if="{{item.remarks}}" class="remarks-section">
            <text class="remarks-label">审批意见：</text>
            <text class="remarks-content">{{item.remarks}}</text>
          </view>
        </view>

        <view class="item-footer">
          <text class="submit-time">申请时间：{{item.submitTime}}</text>
          <view class="duration-info">
            <text class="duration-text">耗时 {{item.processingDuration}}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{loading}}" class="load-more">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view wx:if="{{!pagination.hasMore && approvals.length > 0}}" class="no-more">
        <text>- 已显示全部审批记录 -</text>
      </view>
    </view>
  </view>

  <!-- 详情查看弹窗 -->
  <view wx:if="{{showDetailModal}}" class="modal-overlay" bindtap="hideDetailModal">
            <view class="detail-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">审批详情</text>
        <button class="modal-close" bindtap="hideDetailModal">×</button>
      </view>
      
      <view class="modal-content">
        <view class="detail-section">
          <text class="section-title">基本信息</text>
          <view class="detail-item">
            <text class="detail-label">申请标题</text>
            <text class="detail-value">{{selectedApproval.title}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">申请人</text>
            <text class="detail-value">{{selectedApproval.applicant_name}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">部门</text>
            <text class="detail-value">{{selectedApproval.applicant_department}}</text>
          </view>
          <view wx:if="{{selectedApproval.amount}}" class="detail-item">
            <text class="detail-label">金额</text>
            <text class="detail-value amount">¥{{selectedApproval.amount}}</text>
          </view>
        </view>
        
        <view class="detail-section">
          <text class="section-title">审批信息</text>
          <view class="detail-item">
            <text class="detail-label">审批状态</text>
            <view class="approval-status {{selectedApproval.status}} inline">
              <text>{{selectedApproval.statusText}}</text>
            </view>
          </view>
          <view class="detail-item">
            <text class="detail-label">审批时间</text>
            <text class="detail-value">{{selectedApproval.approvalTime}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">处理耗时</text>
            <text class="detail-value">{{selectedApproval.processingDuration}}</text>
          </view>
          <view wx:if="{{selectedApproval.remarks}}" class="detail-item full">
            <text class="detail-label">审批意见</text>
            <text class="detail-value remarks">{{selectedApproval.remarks}}</text>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="close-btn" bindtap="hideDetailModal">关闭</button>
        <button class="view-detail-btn" bindtap="viewFullDetail">查看完整详情</button>
      </view>
    </view>
  </view>

</view>