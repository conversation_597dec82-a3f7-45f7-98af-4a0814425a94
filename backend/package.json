{"name": "smart-goose-saas-platform", "version": "2.0.0", "description": "智慧养鹅SAAS平台 - 多租户智能养殖管理系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "local": "export NODE_ENV=local && node server.js", "local:dev": "export NODE_ENV=local && nodemon server.js", "admin": "node server.js --mode=admin", "saas": "node server.js --mode=saas", "init-saas-db": "node scripts/init-saas-platform.js", "init-tenant-db": "node scripts/init-tenant-database.js", "migrate": "node scripts/run-migrations.js", "seed": "node scripts/seed-data.js", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON>Handles --forceExit", "build": "tsc", "build:watch": "tsc --watch", "type-check": "tsc --noEmit", "lint": "eslint . --ext .js,.ts", "format": "prettier --write .", "docker:build": "docker build -t smart-goose-saas .", "docker:run": "docker run -p 3000:3000 smart-goose-saas"}, "keywords": ["saas", "multi-tenant", "farming", "goose", "agriculture", "iot", "wechat-miniprogram"], "author": "Smart Goose SAAS Team", "license": "MIT", "dependencies": {"@jest/globals": "^29.7.0", "@types/express": "^5.0.3", "@types/node": "^24.1.0", "@zodios/core": "^10.9.6", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.0", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^4.18.2", "express-ejs-layouts": "^2.5.1", "express-rate-limit": "^8.0.1", "express-session": "^1.17.3", "express-zod-api": "^21.11.7", "helmet": "^8.1.0", "joi": "^17.13.3", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^2.0.2", "mysql2": "^3.14.3", "sequelize": "^6.21.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "zod": "^3.25.76"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.10", "eslint": "^8.45.0", "jest": "^29.6.2", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "nodemon": "^3.0.1", "prettier": "^3.0.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/smart-goose-saas.git"}}