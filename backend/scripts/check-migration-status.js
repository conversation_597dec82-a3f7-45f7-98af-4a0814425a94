/**
 * 快速检查迁移状态脚本
 */

const mysql = require("mysql2/promise");
const fs = require("fs").promises;
const path = require("path");

async function checkMigrationStatus() {
  const config = {
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "zhihuiyange",
    password: process.env.DB_PASSWORD || "zhihuiyange123",
    database: process.env.DB_NAME || "zhihuiyange_local",
    charset: "utf8mb4",
  };

  let connection;

  try {
    console.log("🔍 检查数据库迁移状态...\n");

    connection = await mysql.createConnection(config);

    // 检查migrations表是否存在
    const [tables] = await connection.execute(
      "SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = 'migrations'",
      [config.database],
    );

    if (tables.length === 0) {
      console.log("❌ migrations表不存在，需要初始化");
      return;
    }

    // 获取已执行的迁移
    const [executedMigrations] = await connection.execute(
      "SELECT migration_name, version, executed_at FROM migrations ORDER BY executed_at",
    );

    // 获取迁移文件
    const migrationsDir = path.join(__dirname, "../migrations");
    const files = await fs.readdir(migrationsDir);
    const migrationFiles = files
      .filter((file) => file.endsWith(".sql"))
      .sort()
      .map((file) => file.replace(".sql", ""));

    console.log("📊 迁移状态总览:");
    console.log(`  迁移文件总数: ${migrationFiles.length}`);
    console.log(`  已执行迁移: ${executedMigrations.length}`);
    console.log(
      `  待执行迁移: ${migrationFiles.length - executedMigrations.length}\n`,
    );

    // 检查待执行的迁移
    const executedNames = new Set(
      executedMigrations.map((m) => m.migration_name),
    );
    const pendingMigrations = migrationFiles.filter(
      (name) => !executedNames.has(name),
    );

    if (pendingMigrations.length > 0) {
      console.log("⏳ 待执行的迁移:");
      pendingMigrations.forEach((migration) => {
        console.log(`  📄 ${migration}`);
      });
      console.log(
        "\n💡 运行 `node scripts/migration-manager.js migrate` 来执行迁移\n",
      );
    } else {
      console.log("✅ 所有迁移都已是最新状态\n");
    }

    // 检查数据库完整性
    console.log("🔍 数据库完整性检查:");

    // 检查关键表
    const criticalTables = ["users", "flocks", "unified_inventory"];
    for (const tableName of criticalTables) {
      const [tableExists] = await connection.execute(
        "SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = ?",
        [config.database, tableName],
      );

      if (tableExists.length > 0) {
        const [rowCount] = await connection.execute(
          `SELECT COUNT(*) as count FROM \`${tableName}\``,
        );
        console.log(`  ✅ ${tableName}: ${rowCount[0].count} 条记录`);
      } else {
        console.log(`  ❌ ${tableName}: 表不存在`);
      }
    }

    // 检查约束
    const [constraints] = await connection.execute(
      "SELECT COUNT(*) as count FROM information_schema.table_constraints WHERE table_schema = ? AND constraint_type = 'CHECK'",
      [config.database],
    );
    console.log(`  ✅ 数据约束: ${constraints[0].count} 个CHECK约束`);

    // 检查系统配置（如果存在）
    try {
      const [configCount] = await connection.execute(
        "SELECT COUNT(*) as count FROM system_config",
      );
      console.log(`  ✅ 系统配置: ${configCount[0].count} 项配置`);
    } catch {
      console.log("  ⚠️  系统配置表未创建");
    }
  } catch (error) {
    console.error("❌ 检查过程出错:", error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkMigrationStatus();
}

module.exports = checkMigrationStatus;
