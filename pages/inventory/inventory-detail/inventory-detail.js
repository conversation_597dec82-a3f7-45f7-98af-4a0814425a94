// pages/inventory/inventory-detail/inventory-detail.js
Page({
  data: {
    updateTime: '',
    totalCount: 0,
    healthyCount: 0,
    sickCount: 0,
    quarantineCount: 0,
    healthyPercent: 0,
    sickPercent: 0,
    quarantinePercent: 0,
    activeTab: 'age',
    ageCategories: [],
    breedCategories: [],
    areaCategories: [],
    alerts: []
  },

  onLoad: function (options) {
    this.loadInventoryData();
    // 延迟绘制饼图，确保页面元素加载完成
    setTimeout(() => {
      this.drawHealthChart();
    }, 100);
  },

  onPullDownRefresh: function () {
    this.loadInventoryData(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载库存数据
  loadInventoryData(callback) {
    wx.showLoading({
      title: '加载中...'
    });

    // 从健康管理模块获取真实健康数据
    this.getHealthDataFromHealthModule().then(healthData => {
      const mockData = this.getMockInventoryData(healthData);
      this.setData({
        ...mockData
      });
      
      // 延迟绘制饼图，确保数据更新完成
      setTimeout(() => {
        this.drawHealthChart();
      }, 100);
      
      wx.hideLoading();
      callback && callback();
    }).catch(error => {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('获取健康数据失败', error); } catch(_) {}
      // 使用默认数据
      const mockData = this.getMockInventoryData();
      this.setData({
        ...mockData
      });
      
      setTimeout(() => {
        this.drawHealthChart();
      }, 100);
      
      wx.hideLoading();
      callback && callback();
    });
  },

  // 获取模拟库存数据
  getMockInventoryData(healthData = null) {
    // 如果有健康数据，使用真实数据；否则使用模拟数据
    let totalCount, healthyCount, sickCount, quarantineCount;
    
    if (healthData) {
      totalCount = healthData.totalCount;
      healthyCount = healthData.healthyCount;
      sickCount = healthData.sickCount;
      quarantineCount = healthData.quarantineCount;
    } else {
      // 默认模拟数据
      totalCount = 1250;
      healthyCount = 1180;
      sickCount = 45;
      quarantineCount = 25;
    }

    const healthyPercent = totalCount > 0 ? Math.round((healthyCount / totalCount) * 100) : 0;
    const sickPercent = totalCount > 0 ? Math.round((sickCount / totalCount) * 100) : 0;
    const quarantinePercent = totalCount > 0 ? Math.round((quarantineCount / totalCount) * 100) : 0;

    return {
      updateTime: this.formatTime(new Date()),
      totalCount,
      healthyCount,
      sickCount,
      quarantineCount,
      healthyPercent,
      sickPercent,
      quarantinePercent,
      ageCategories: [
        { name: '雏鹅', description: '0-4周龄', count: 320, percent: 26 },
        { name: '青年鹅', description: '5-12周龄', count: 450, percent: 36 },
        { name: '成年鹅', description: '13周龄以上', count: 480, percent: 38 }
      ],
      breedCategories: [
        { name: '白鹅', description: '优质白鹅品种', count: 520, percent: 42 },
        { name: '灰鹅', description: '抗病能力强', count: 380, percent: 30 },
        { name: '狮头鹅', description: '大型鹅种', count: 230, percent: 18 },
        { name: '其他', description: '其他品种', count: 120, percent: 10 }
      ],
      areaCategories: [
        { name: 'A区', description: '主养殖区', count: 450, percent: 36 },
        { name: 'B区', description: '育雏区', count: 320, percent: 26 },
        { name: 'C区', description: '隔离区', count: 280, percent: 22 },
        { name: 'D区', description: '备用区', count: 200, percent: 16 }
      ],
      alerts: [
        {
          id: 1,
          level: 'warning',
          title: 'C区密度过高',
          description: 'C区当前密度超过标准值，建议调整',
          time: '2小时前'
        },
        {
          id: 2,
          level: 'info',
          title: '疫苗接种提醒',
          description: 'A区雏鹅需要进行第二次疫苗接种',
          time: '4小时前'
        }
      ]
    };
  },

  // 切换分类标签
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    // 确保tab是有效的值
    if (tab !== undefined && tab !== null) {
      this.setData({
        activeTab: tab
      });
    } else {
      try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[InventoryDetail] Invalid tab value', tab); } catch(_) {}
      return;
    }
  },

  // 点击分类项
  onCategoryTap(e) {
    const { type, value } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/inventory/category-detail/category-detail?type=${type}&value=${encodeURIComponent(value)}`
    });
  },

  // 绘制健康状态饼图 - 使用Canvas 2D接口
  async drawHealthChart() {
    const { healthyCount, sickCount, quarantineCount, totalCount } = this.data;
    
    if (totalCount === 0) return;

    try {
      // 获取Canvas 2D上下文
      const query = this.createSelectorQuery();
      const canvas = await new Promise((resolve) => {
        query.select('#healthChart')
          .fields({ node: true, size: true })
          .exec((res) => {
            resolve(res[0]);
          });
      });

      if (!canvas || !canvas.node) {
        try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('Canvas元素未找到，延迟重试'); } catch(_) {}
        setTimeout(() => {
          this.drawHealthChart();
        }, 200);
        return;
      }

      const canvasNode = canvas.node;
      const ctx = canvasNode.getContext('2d');

      // 设置画布尺寸
      const dpr = wx.getWindowInfo().pixelRatio;
      canvasNode.width = canvas.width * dpr;
      canvasNode.height = canvas.height * dpr;
      ctx.scale(dpr, dpr);

      // 根据实际容器尺寸设置画布参数
      const canvasSize = Math.min(canvas.width, canvas.height);
      const centerX = canvasSize / 2;
      const centerY = canvasSize / 2;
      const radius = (canvasSize / 2) * 0.75; // 留出更多边距
      
      // 计算角度 - 从12点钟方向开始
      const startAngle = -Math.PI / 2;
      const healthyAngle = (healthyCount / totalCount) * 2 * Math.PI;
      const sickAngle = (sickCount / totalCount) * 2 * Math.PI;
      const quarantineAngle = (quarantineCount / totalCount) * 2 * Math.PI;
      
      let currentAngle = startAngle;
      
      // 清空画布
      ctx.clearRect(0, 0, canvasSize, canvasSize);
      
      // 设置线条样式
      ctx.lineWidth = 2;
      ctx.strokeStyle = '#ffffff';
      
      // 绘制健康部分（绿色）
      if (healthyCount > 0) {
        ctx.fillStyle = '#52c41a';
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + healthyAngle);
        ctx.closePath();
        ctx.fill();
        ctx.stroke(); // 添加白色边框
        currentAngle += healthyAngle;
      }
      
      // 绘制患病部分（红色）
      if (sickCount > 0) {
        ctx.fillStyle = '#ff4d4f';
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sickAngle);
        ctx.closePath();
        ctx.fill();
        ctx.stroke(); // 添加白色边框
        currentAngle += sickAngle;
      }
      
      // 绘制隔离部分（橙色）
      if (quarantineCount > 0) {
        ctx.fillStyle = '#faad14';
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + quarantineAngle);
        ctx.closePath();
        ctx.fill();
        ctx.stroke(); // 添加白色边框
      }
      
      // 绘制中心圆（可选，增加视觉效果）
      const innerRadius = radius * 0.25;
      ctx.fillStyle = '#ffffff';
      ctx.beginPath();
      ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
      ctx.fill();
      ctx.strokeStyle = '#e8e8e8';
      ctx.lineWidth = 1;
      ctx.stroke();
      
      // Canvas 2D不需要调用draw()方法
    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('绘制健康图表失败', error); } catch(_) {}
    }
  },

  // 处理预警
  onAlertTap(e) {
    const alertId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '处理预警',
      content: '确定要处理这个预警吗？',
      success: (res) => {
        if (res.confirm) {
          // 移除预警
          const alerts = this.data.alerts.filter(alert => alert.id !== alertId);
          this.setData({ alerts });
          wx.showToast({
            title: '处理成功',
            icon: 'success'
          });
        }
      }
    });
  },



  // 从健康管理模块获取健康数据
  getHealthDataFromHealthModule() {
    return new Promise((resolve, reject) => {
      try {
        // 尝试从全局数据或本地存储获取健康管理的数据
        const app = getApp();
        let healthData = null;
        
        // 尝试从全局数据获取
        if (app.globalData && app.globalData.healthData) {
          healthData = app.globalData.healthData;
        } else {
          // 尝试从本地存储获取
          const storedHealthData = wx.getStorageSync('healthData');
          if (storedHealthData) {
            healthData = storedHealthData;
          }
        }
        
        if (healthData) {
          // 转换健康数据格式，映射到库存数据
          const convertedData = this.convertHealthDataToInventory(healthData);
          resolve(convertedData);
        } else {
          // 如果没有健康数据，生成基于当前时间的模拟数据
          const simulatedData = this.generateRealisticHealthData();
          resolve(simulatedData);
        }
      } catch (error) {
        reject(error);
      }
    });
  },

  // 转换健康数据为库存数据格式
  convertHealthDataToInventory(healthData) {
    // 如果健康数据有具体的统计信息，使用它们
    if (healthData.overview) {
      return {
        totalCount: healthData.overview.totalGeese || 1250,
        healthyCount: healthData.overview.healthyCount || 1180,
        sickCount: healthData.overview.sickCount || 45,
        quarantineCount: healthData.overview.deathCount || 25 // 用死亡数代替隔离数
      };
    }
    
    // 如果是趋势数据，取最新的数据点
    if (healthData.trendData && healthData.trendData.length > 0) {
      const latestData = healthData.trendData[healthData.trendData.length - 1];
      return {
        totalCount: latestData.healthy + latestData.sick + latestData.death,
        healthyCount: latestData.healthy,
        sickCount: latestData.sick,
        quarantineCount: latestData.death
      };
    }
    
    // 默认返回null，使用模拟数据
    return null;
  },

  // 生成真实的健康数据
  generateRealisticHealthData() {
    const now = new Date();
    const hour = now.getHours();
    
    // 基于时间的动态数据，模拟真实的养殖场情况
    const baseTotal = 1250;
    const timeVariation = Math.sin((hour / 24) * Math.PI * 2) * 0.05; // 基于时间的小幅波动
    const randomVariation = (Math.random() - 0.5) * 0.02; // 随机小幅波动
    
    const healthyRate = 0.944 + timeVariation + randomVariation; // 94.4% 左右的健康率
    const sickRate = 0.036 + (Math.random() - 0.5) * 0.01; // 3.6% 左右的患病率
    const quarantineRate = 0.02 + (Math.random() - 0.5) * 0.005; // 2% 左右的隔离率
    
    const healthyCount = Math.round(baseTotal * healthyRate);
    const sickCount = Math.round(baseTotal * sickRate);
    const quarantineCount = Math.round(baseTotal * quarantineRate);
    const totalCount = healthyCount + sickCount + quarantineCount;
    
    return {
      totalCount,
      healthyCount,
      sickCount,
      quarantineCount
    };
  },

  // 格式化时间
  formatTime(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${month}月${day}日 ${hours}:${minutes}`;
  }
});
