/**
 * 统一权限管理中间件
 * Unified Permission Management Middleware
 * 
 * 整合所有权限相关功能，提供统一的权限验证机制
 * 支持平台级和租户级权限控制
 */

const jwt = require("jsonwebtoken");
const { createError } = require("../utils/error-handler");
const { generateErrorResponse } = require("../utils/response-helper");

/**
 * 权限常量定义
 * 统一管理所有权限定义，避免分散和重复
 */
const PERMISSIONS = {
  // ====================
  // 平台级权限 (SAAS管理)
  // ====================
  
  // 超级管理员权限
  PLATFORM_SUPER_ADMIN: "platform:super_admin",
  PLATFORM_ADMIN: "platform:admin",
  PLATFORM_OPERATOR: "platform:operator",
  PLATFORM_SUPPORT: "platform:support",
  
  // 跨租户权限
  CROSS_TENANT_READ: "platform:cross_tenant_read",
  CROSS_TENANT_WRITE: "platform:cross_tenant_write",
  
  // 租户管理权限
  TENANT_CREATE: "platform:tenant:create",
  TENANT_UPDATE: "platform:tenant:update",
  TENANT_DELETE: "platform:tenant:delete",
  TENANT_VIEW: "platform:tenant:view",
  TENANT_SUSPEND: "platform:tenant:suspend",
  TENANT_ACTIVATE: "platform:tenant:activate",
  
  // 订阅管理权限
  SUBSCRIPTION_MANAGE: "platform:subscription:manage",
  SUBSCRIPTION_VIEW: "platform:subscription:view",
  
  // 平台分析权限
  PLATFORM_ANALYTICS: "platform:analytics:view",
  PLATFORM_REPORTS: "platform:reports:view",
  
  // 系统监控权限
  SYSTEM_MONITOR: "platform:system:monitor",
  SYSTEM_CONFIG: "platform:system:config",
  
  // ====================
  // 租户级权限 (企业内部)
  // ====================
  
  // 基础资源权限
  FLOCK_VIEW: "tenant:flock:view",
  FLOCK_CREATE: "tenant:flock:create",
  FLOCK_UPDATE: "tenant:flock:update",
  FLOCK_DELETE: "tenant:flock:delete",
  
  // 健康管理权限
  HEALTH_VIEW: "tenant:health:view",
  HEALTH_CREATE: "tenant:health:create",
  HEALTH_UPDATE: "tenant:health:update",
  HEALTH_DELETE: "tenant:health:delete",
  
  // 生产管理权限
  PRODUCTION_VIEW: "tenant:production:view",
  PRODUCTION_CREATE: "tenant:production:create",
  PRODUCTION_UPDATE: "tenant:production:update",
  PRODUCTION_DELETE: "tenant:production:delete",
  
  // 库存管理权限
  INVENTORY_VIEW: "tenant:inventory:view",
  INVENTORY_CREATE: "tenant:inventory:create",
  INVENTORY_UPDATE: "tenant:inventory:update",
  INVENTORY_DELETE: "tenant:inventory:delete",
  
  // 用户管理权限
  USER_VIEW: "tenant:user:view",
  USER_CREATE: "tenant:user:create",
  USER_UPDATE: "tenant:user:update",
  USER_DELETE: "tenant:user:delete",
  
  // 商城管理权限
  SHOP_VIEW: "tenant:shop:view",
  SHOP_CREATE: "tenant:shop:create",
  SHOP_UPDATE: "tenant:shop:update",
  SHOP_DELETE: "tenant:shop:delete",
  
  // 财务管理权限
  FINANCE_VIEW: "tenant:finance:view",
  FINANCE_CREATE: "tenant:finance:create",
  FINANCE_UPDATE: "tenant:finance:update",
  FINANCE_DELETE: "tenant:finance:delete",
  FINANCE_APPROVE: "tenant:finance:approve",
  FINANCE_EXPORT: "tenant:finance:export",
  
  // OA办公权限
  OA_ACCESS: "tenant:oa:access",
  OA_ADMIN: "tenant:oa:admin",
  TASK_VIEW: "tenant:task:view",
  TASK_CREATE: "tenant:task:create",
  TASK_UPDATE: "tenant:task:update",
  TASK_DELETE: "tenant:task:delete",
  TASK_ASSIGN: "tenant:task:assign",
  
  // 审批权限
  APPROVAL_VIEW: "tenant:approval:view",
  APPROVAL_PROCESS: "tenant:approval:process",
  APPROVAL_DELEGATE: "tenant:approval:delegate",
  APPROVAL_WORKFLOW_MANAGE: "tenant:approval:workflow_manage",
  
  // AI服务权限
  AI_DIAGNOSIS: "tenant:ai:diagnosis",
  AI_CHAT: "tenant:ai:chat",
  AI_CONFIG: "tenant:ai:config",
  AI_STATS: "tenant:ai:stats",
  
  // 数据权限
  DATA_EXPORT: "tenant:data:export",
  DATA_IMPORT: "tenant:data:import",
  DATA_BACKUP: "tenant:data:backup",
  
  // 租户内管理权限
  TENANT_MANAGEMENT: "tenant:management",
  STAFF_MANAGE: "tenant:staff:manage",
  ROLE_MANAGE: "tenant:role:manage",
  SETTINGS_MANAGE: "tenant:settings:manage",
  REPORTS_VIEW: "tenant:reports:view",
};

/**
 * 角色权限映射表
 * 定义各个角色的默认权限集合
 */
const ROLE_PERMISSIONS = {
  // ====================
  // 平台级角色
  // ====================
  
  // 平台超级管理员 - 拥有所有权限
  platform_super_admin: Object.values(PERMISSIONS),
  
  // 平台管理员 - 核心平台管理权限
  platform_admin: [
    PERMISSIONS.PLATFORM_ADMIN,
    PERMISSIONS.CROSS_TENANT_READ,
    PERMISSIONS.TENANT_CREATE,
    PERMISSIONS.TENANT_UPDATE,
    PERMISSIONS.TENANT_VIEW,
    PERMISSIONS.TENANT_SUSPEND,
    PERMISSIONS.TENANT_ACTIVATE,
    PERMISSIONS.SUBSCRIPTION_MANAGE,
    PERMISSIONS.SUBSCRIPTION_VIEW,
    PERMISSIONS.PLATFORM_ANALYTICS,
    PERMISSIONS.PLATFORM_REPORTS,
    PERMISSIONS.SYSTEM_MONITOR,
  ],
  
  // 平台运维人员 - 技术运维权限
  platform_operator: [
    PERMISSIONS.PLATFORM_OPERATOR,
    PERMISSIONS.CROSS_TENANT_READ,
    PERMISSIONS.TENANT_VIEW,
    PERMISSIONS.PLATFORM_ANALYTICS,
    PERMISSIONS.SYSTEM_MONITOR,
    PERMISSIONS.SYSTEM_CONFIG,
  ],
  
  // 平台客服 - 基础支持权限
  platform_support: [
    PERMISSIONS.PLATFORM_SUPPORT,
    PERMISSIONS.TENANT_VIEW,
    PERMISSIONS.SUBSCRIPTION_VIEW,
  ],
  
  // ====================
  // 租户级角色
  // ====================
  
  // 企业所有者 - 租户内所有权限
  owner: [
    // 基础资源管理
    PERMISSIONS.FLOCK_VIEW,
    PERMISSIONS.FLOCK_CREATE,
    PERMISSIONS.FLOCK_UPDATE,
    PERMISSIONS.FLOCK_DELETE,
    
    // 健康管理
    PERMISSIONS.HEALTH_VIEW,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_UPDATE,
    PERMISSIONS.HEALTH_DELETE,
    
    // 生产管理
    PERMISSIONS.PRODUCTION_VIEW,
    PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.PRODUCTION_UPDATE,
    PERMISSIONS.PRODUCTION_DELETE,
    
    // 库存管理
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.INVENTORY_CREATE,
    PERMISSIONS.INVENTORY_UPDATE,
    PERMISSIONS.INVENTORY_DELETE,
    
    // 用户管理
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    
    // 商城管理
    PERMISSIONS.SHOP_VIEW,
    PERMISSIONS.SHOP_CREATE,
    PERMISSIONS.SHOP_UPDATE,
    PERMISSIONS.SHOP_DELETE,
    
    // 财务管理
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_UPDATE,
    PERMISSIONS.FINANCE_DELETE,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_EXPORT,
    
    // OA办公
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.OA_ADMIN,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    PERMISSIONS.TASK_UPDATE,
    PERMISSIONS.TASK_DELETE,
    PERMISSIONS.TASK_ASSIGN,
    
    // 审批管理
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.APPROVAL_DELEGATE,
    PERMISSIONS.APPROVAL_WORKFLOW_MANAGE,
    
    // AI服务
    PERMISSIONS.AI_DIAGNOSIS,
    PERMISSIONS.AI_CHAT,
    PERMISSIONS.AI_CONFIG,
    PERMISSIONS.AI_STATS,
    
    // 数据管理
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.DATA_IMPORT,
    PERMISSIONS.DATA_BACKUP,
    
    // 租户管理
    PERMISSIONS.TENANT_MANAGEMENT,
    PERMISSIONS.STAFF_MANAGE,
    PERMISSIONS.ROLE_MANAGE,
    PERMISSIONS.SETTINGS_MANAGE,
    PERMISSIONS.REPORTS_VIEW,
  ],
  
  // 企业管理员 - 大部分业务权限
  admin: [
    // 基础资源管理
    PERMISSIONS.FLOCK_VIEW,
    PERMISSIONS.FLOCK_CREATE,
    PERMISSIONS.FLOCK_UPDATE,
    PERMISSIONS.FLOCK_DELETE,
    
    // 健康管理
    PERMISSIONS.HEALTH_VIEW,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_UPDATE,
    PERMISSIONS.HEALTH_DELETE,
    
    // 生产管理
    PERMISSIONS.PRODUCTION_VIEW,
    PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.PRODUCTION_UPDATE,
    PERMISSIONS.PRODUCTION_DELETE,
    
    // 库存管理
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.INVENTORY_CREATE,
    PERMISSIONS.INVENTORY_UPDATE,
    PERMISSIONS.INVENTORY_DELETE,
    
    // 用户管理（不包括删除）
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_UPDATE,
    
    // 商城管理
    PERMISSIONS.SHOP_VIEW,
    PERMISSIONS.SHOP_CREATE,
    PERMISSIONS.SHOP_UPDATE,
    PERMISSIONS.SHOP_DELETE,
    
    // 财务管理（不包括删除）
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_UPDATE,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_EXPORT,
    
    // OA办公
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.OA_ADMIN,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    PERMISSIONS.TASK_UPDATE,
    PERMISSIONS.TASK_DELETE,
    PERMISSIONS.TASK_ASSIGN,
    
    // 审批管理
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.APPROVAL_DELEGATE,
    PERMISSIONS.APPROVAL_WORKFLOW_MANAGE,
    
    // AI服务
    PERMISSIONS.AI_DIAGNOSIS,
    PERMISSIONS.AI_CHAT,
    PERMISSIONS.AI_STATS,
    
    // 数据管理
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.DATA_IMPORT,
    
    // 部分租户管理
    PERMISSIONS.TENANT_MANAGEMENT,
    PERMISSIONS.STAFF_MANAGE,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.SETTINGS_MANAGE,
  ],
  
  // 部门经理 - 部门级权限
  manager: [
    // 基础资源管理
    PERMISSIONS.FLOCK_VIEW,
    PERMISSIONS.FLOCK_CREATE,
    PERMISSIONS.FLOCK_UPDATE,
    
    // 健康管理
    PERMISSIONS.HEALTH_VIEW,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_UPDATE,
    
    // 生产管理
    PERMISSIONS.PRODUCTION_VIEW,
    PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.PRODUCTION_UPDATE,
    
    // 库存管理
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.INVENTORY_CREATE,
    PERMISSIONS.INVENTORY_UPDATE,
    
    // 用户查看
    PERMISSIONS.USER_VIEW,
    
    // 商城查看
    PERMISSIONS.SHOP_VIEW,
    
    // 财务查看
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_EXPORT,
    
    // OA办公
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    PERMISSIONS.TASK_UPDATE,
    PERMISSIONS.TASK_ASSIGN,
    
    // 审批权限
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_PROCESS,
    
    // AI服务
    PERMISSIONS.AI_DIAGNOSIS,
    PERMISSIONS.AI_CHAT,
    
    // 数据导出
    PERMISSIONS.DATA_EXPORT,
    
    // 报告查看
    PERMISSIONS.REPORTS_VIEW,
  ],
  
  // 普通员工 - 基础操作权限
  user: [
    // 基础查看权限
    PERMISSIONS.FLOCK_VIEW,
    PERMISSIONS.HEALTH_VIEW,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.PRODUCTION_VIEW,
    PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.SHOP_VIEW,
    
    // OA基础权限
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    
    // AI基础服务
    PERMISSIONS.AI_DIAGNOSIS,
    PERMISSIONS.AI_CHAT,
  ],
  
  // 财务人员 - 财务相关权限
  finance: [
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_UPDATE,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.REPORTS_VIEW,
  ],
  
  // HR人员 - 人事相关权限
  hr: [
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.STAFF_MANAGE,
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.REPORTS_VIEW,
  ],
};

/**
 * 权限验证核心类
 */
class UnifiedPermissionManager {
  /**
   * 检查用户是否拥有指定权限
   * @param {Object} user 用户对象
   * @param {string|Array} requiredPermissions 需要的权限
   * @param {Object} options 选项
   * @returns {boolean} 是否有权限
   */
  static hasPermission(user, requiredPermissions, options = {}) {
    if (!user || !user.role) {
      return false;
    }

    // 获取用户的所有权限
    const userPermissions = this.getUserPermissions(user);
    
    // 转换为数组
    const permissions = Array.isArray(requiredPermissions) 
      ? requiredPermissions 
      : [requiredPermissions];

    const { requireAll = true } = options;

    if (requireAll) {
      // 需要拥有所有权限
      return permissions.every(permission => userPermissions.includes(permission));
    } else {
      // 需要拥有任意一个权限
      return permissions.some(permission => userPermissions.includes(permission));
    }
  }

  /**
   * 获取用户的所有权限
   * @param {Object} user 用户对象
   * @returns {Array} 权限列表
   */
  static getUserPermissions(user) {
    if (!user || !user.role) {
      return [];
    }

    // 从角色获取基础权限
    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
    
    // 如果用户有自定义权限，合并进来
    const customPermissions = user.permissions || [];
    
    // 合并去重
    return [...new Set([...rolePermissions, ...customPermissions])];
  }

  /**
   * 检查用户是否是平台管理员
   * @param {Object} user 用户对象
   * @returns {boolean} 是否是平台管理员
   */
  static isPlatformAdmin(user) {
    return user && user.role && user.role.startsWith('platform_');
  }

  /**
   * 检查用户是否是租户管理员
   * @param {Object} user 用户对象
   * @returns {boolean} 是否是租户管理员
   */
  static isTenantAdmin(user) {
    return user && user.role && ['owner', 'admin'].includes(user.role);
  }

  /**
   * 验证资源所有权
   * @param {Object} user 用户对象
   * @param {number} resourceUserId 资源所有者ID
   * @returns {boolean} 是否拥有资源
   */
  static hasResourceOwnership(user, resourceUserId) {
    if (!user || !resourceUserId) {
      return false;
    }

    // 管理员可以访问所有资源
    if (this.isTenantAdmin(user) || this.isPlatformAdmin(user)) {
      return true;
    }

    // 普通用户只能访问自己的资源
    return user.id === resourceUserId;
  }
}

/**
 * 权限验证中间件工厂函数
 * @param {string|Array} requiredPermissions 需要的权限
 * @param {Object} options 选项
 * @returns {Function} Express中间件
 */
function requirePermissions(requiredPermissions, options = {}) {
  return async (req, res, next) => {
    try {
      // 检查用户是否已认证
      if (!req.user) {
        return res.status(401).json(generateErrorResponse("用户未认证", {
          code: "AUTHENTICATION_REQUIRED"
        }));
      }

      // 检查权限
      const hasPermission = UnifiedPermissionManager.hasPermission(
        req.user,
        requiredPermissions,
        options
      );

      if (!hasPermission) {
        // 记录权限拒绝事件
        await this.logPermissionDenied(req, requiredPermissions);

        return res.status(403).json(generateErrorResponse("权限不足", {
          code: "INSUFFICIENT_PERMISSIONS",
          required: Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions],
          userRole: req.user.role,
          userPermissions: UnifiedPermissionManager.getUserPermissions(req.user)
        }));
      }

      // 将权限信息附加到请求对象
      req.userPermissions = UnifiedPermissionManager.getUserPermissions(req.user);
      
      next();
    } catch (error) {
      console.error("权限验证失败:", error);
      res.status(500).json(generateErrorResponse("权限验证失败"));
    }
  };
}

/**
 * 资源所有权验证中间件
 * @param {string} resourceIdParam 资源ID参数名
 * @param {string} resourceUserIdField 资源用户ID字段名
 * @returns {Function} Express中间件
 */
function requireResourceOwnership(resourceIdParam = 'id', resourceUserIdField = 'userId') {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json(generateErrorResponse("用户未认证"));
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        return res.status(400).json(generateErrorResponse("缺少资源ID"));
      }

      // 这里需要根据具体业务逻辑获取资源的用户ID
      // 示例：从数据库查询资源的所有者
      // const resource = await getResourceById(resourceId);
      // const resourceUserId = resource[resourceUserIdField];

      // 临时实现：如果用户是管理员，允许访问所有资源
      if (UnifiedPermissionManager.isTenantAdmin(req.user) || 
          UnifiedPermissionManager.isPlatformAdmin(req.user)) {
        return next();
      }

      // 这里应该实现具体的资源所有权检查逻辑
      // 暂时允许通过，需要后续完善
      next();
    } catch (error) {
      console.error("资源所有权验证失败:", error);
      res.status(500).json(generateErrorResponse("资源所有权验证失败"));
    }
  };
}

/**
 * 记录权限拒绝事件
 * @param {Object} req 请求对象
 * @param {string|Array} requiredPermissions 需要的权限
 */
async function logPermissionDenied(req, requiredPermissions) {
  try {
    // 这里可以实现权限拒绝日志记录
    console.log(`权限拒绝: 用户 ${req.user.id} (${req.user.role}) 尝试访问需要权限: ${JSON.stringify(requiredPermissions)}`);
    
    // 可以写入数据库或发送到日志服务
    // await auditLogger.log({
    //   action: 'PERMISSION_DENIED',
    //   userId: req.user.id,
    //   userRole: req.user.role,
    //   requiredPermissions,
    //   requestPath: req.path,
    //   requestMethod: req.method,
    //   timestamp: new Date()
    // });
  } catch (error) {
    console.error("记录权限拒绝事件失败:", error);
  }
}

module.exports = {
  PERMISSIONS,
  ROLE_PERMISSIONS,
  UnifiedPermissionManager,
  requirePermissions,
  requireResourceOwnership,
  logPermissionDenied,
};