const express = require("express");
const router = express.Router();
const userController = require("../controllers/user.controller");
const { verifyToken } = require("../middleware/auth.middleware");

/**
 * 用户管理路由
 * 遵循RESTful API设计规范
 */

// 所有路由都需要验证令牌
router.use(verifyToken);

// 获取用户列表（管理员权限）
router.get("/", userController.getUsers);

// 创建用户（管理员权限）
router.post("/", userController.createUser);

// 获取指定用户详情
router.get("/:id", userController.getUserById);

// 更新指定用户信息（完整更新）
router.put("/:id", userController.updateUser);

// 部分更新指定用户信息
router.patch("/:id", userController.patchUser);

// 删除指定用户（管理员权限）
router.delete("/:id", userController.deleteUser);

// 获取用户统计信息
router.get("/stats/overview", userController.getUserStats);

// 批量操作用户
router.post("/batch/update", userController.batchUpdateUsers);
router.post("/batch/delete", userController.batchDeleteUsers);

// 用户角色管理
router.get("/:id/roles", userController.getUserRoles);
router.put("/:id/roles", userController.updateUserRoles);

// 用户权限管理
router.get("/:id/permissions", userController.getUserPermissions);

module.exports = router;
