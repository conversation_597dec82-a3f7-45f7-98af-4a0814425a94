// components/form-modal/form-modal.js
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '表单'
    },
    fields: {
      type: Array,
      value: []
    },
    formData: {
      type: Object,
      value: {}
    },
    loading: {
      type: Boolean,
      value: false
    }
  },

  data: {
    
  },

  methods: {
    // 输入框变化
    onInputChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      // 更新本地formData
      const currentFormData = this.data.formData || {};
      currentFormData[field] = value;
      
      this.setData({
        formData: currentFormData
      });
      
      this.triggerEvent('input-change', {
        field,
        value,
        formData: currentFormData
      });
    },

    // 选择器变化
    onPickerChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = parseInt(e.detail.value);
      
      this.triggerEvent('picker-change', {
        field,
        value
      });
    },

    // 日期选择器变化
    onDateChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      // 更新本地formData
      const currentFormData = this.data.formData || {};
      currentFormData[field] = value;
      
      this.setData({
        formData: currentFormData
      });
      
      this.triggerEvent('date-change', {
        field,
        value,
        formData: currentFormData
      });
    },

    // 时间选择器变化
    onTimeChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      // 更新本地formData
      const currentFormData = this.data.formData || {};
      currentFormData[field] = value;
      
      this.setData({
        formData: currentFormData
      });
      
      this.triggerEvent('time-change', {
        field,
        value,
        formData: currentFormData
      });
    },

    // 开关变化
    onSwitchChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      this.triggerEvent('switch-change', {
        field,
        value
      });
    },

    // 点击遮罩
    onMaskTap() {
      // 可以选择是否允许点击遮罩关闭
      // this.onClose();
    },

    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 取消
    onCancel() {
      this.triggerEvent('cancel');
    },

    // 确认
    onConfirm() {
      this.triggerEvent('confirm', {
        formData: this.properties.formData
      });
    },

    // 图片上传
    onImageUpload(e) {
      const field = e.currentTarget.dataset.field;
      const that = this;

      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function(res) {
          const tempFilePath = res.tempFilePaths[0];

          // 这里可以上传到服务器，暂时使用本地路径
          that.triggerEvent('image-upload', {
            field,
            value: tempFilePath
          });
        },
        fail: function(err) {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    }
  }
});
