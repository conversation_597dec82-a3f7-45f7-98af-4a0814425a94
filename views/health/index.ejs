<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        <a href="/health/create" class="btn btn-success">
            <i class="bi bi-plus-circle me-2"></i>添加健康记录
        </a>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="搜索健康记录..." id="searchInput">
            <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- 错误提示 -->
<% if (error) { %>
<div class="alert alert-warning" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    <%= error %>
</div>
<% } %>

<!-- 健康记录列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-heart-pulse me-2"></i>健康记录列表
            <span class="badge bg-success ms-2"><%= records.length %></span>
        </h5>
    </div>
    <div class="card-body">
        <% if (records.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>记录日期</th>
                        <th>健康状态</th>
                        <th>症状</th>
                        <th>治疗方案</th>
                        <th>备注</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <% records.forEach(function(record) { %>
                    <tr>
                        <td>
                            <strong><%= moment(record.记录日期 || record.createdAt).format('YYYY-MM-DD') %></strong>
                        </td>
                        <td>
                            <% const status = record.健康状态 || '健康'; %>
                            <% if (status === '健康' || status === 'healthy') { %>
                                <span class="badge bg-success">健康</span>
                            <% } else if (status === '警告' || status === 'warning') { %>
                                <span class="badge bg-warning">警告</span>
                            <% } else { %>
                                <span class="badge bg-danger">严重</span>
                            <% } %>
                        </td>
                        <td>
                            <span class="text-muted">
                                <%= record.症状 || '-' %>
                            </span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <%= record.治疗方案 || '-' %>
                            </span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <%= record.备注 ? (record.备注.length > 20 ? record.备注.substring(0, 20) + '...' : record.备注) : '-' %>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <%= moment(record.createdAt).format('MM-DD HH:mm') %>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-info" title="查看详情" 
                                        onclick="viewRecord(<%= record.id %>)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" title="删除" 
                                        onclick="deleteRecord(<%= record.id %>)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
        <% } else { %>
        <div class="text-center py-5">
            <i class="bi bi-heart-pulse" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">暂无健康记录</h5>
            <p class="text-muted">点击上方按钮添加第一条健康记录</p>
        </div>
        <% } %>
    </div>
</div>

<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// 查看记录详情
function viewRecord(recordId) {
    showMessage('查看详情功能开发中...', 'info');
}

// 删除记录
function deleteRecord(recordId) {
    if (confirm('确定要删除这条健康记录吗？此操作不可恢复。')) {
        showMessage('删除功能开发中...', 'info');
    }
}
</script>
