/**
 * 优化空组件配置脚本
 * 移除所有页面中的空 usingComponents 配置以减少主包尺寸
 */

const fs = require('fs');
const path = require('path');

// 递归查找所有JSON文件
function findJsonFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 跳过node_modules和backend目录
      if (item !== 'node_modules' && item !== 'backend' && item !== '.git') {
        findJsonFiles(fullPath, files);
      }
    } else if (item.endsWith('.json')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 处理单个JSON文件
function processJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const jsonData = JSON.parse(content);
    
    // 检查是否有空的usingComponents
    if (jsonData.usingComponents && 
        typeof jsonData.usingComponents === 'object' && 
        Object.keys(jsonData.usingComponents).length === 0) {
      
      // 删除空的usingComponents配置
      delete jsonData.usingComponents;
      
      // 重新写入文件，保持格式化
      const newContent = JSON.stringify(jsonData, null, 2) + '\n';
      fs.writeFileSync(filePath, newContent, 'utf8');
      
      console.log(`✅ 优化: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理失败: ${filePath}`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const rootDir = path.resolve(__dirname, '..');
  console.log('🚀 开始优化空组件配置...');
  
  const jsonFiles = findJsonFiles(rootDir);
  let optimizedCount = 0;
  
  for (const filePath of jsonFiles) {
    // 只处理pages和components目录下的文件
    if (filePath.includes('/pages/') || filePath.includes('/components/')) {
      if (processJsonFile(filePath)) {
        optimizedCount++;
      }
    }
  }
  
  console.log(`\n🎉 优化完成！共优化了 ${optimizedCount} 个文件`);
  
  // 计算节省的空间
  const savedBytes = optimizedCount * 25; // 每个空配置约25字节
  console.log(`💾 预计节省空间: ${savedBytes} 字节 (${(savedBytes/1024).toFixed(2)} KB)`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { processJsonFile, findJsonFiles };
