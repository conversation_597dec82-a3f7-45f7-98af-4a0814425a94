// pages/oa/purchase/detail/detail.js
const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info.js');
const { formatCurrency, formatDate, formatTimeAgo } = require('../../../../utils/format.js');
const { isLoggedIn, getCurrentUser } = require('../../../../utils/auth-helper.js');
const request = require('../../../../utils/request.js');
const { API } = require('../../../../constants/index.js');

Page({
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,
    
    // 采购申请详情
    purchaseDetail: null,
    
    // 状态配置
    statusConfig: {
      draft: { label: '草稿', color: '#999999', bgColor: '#f5f5f5' },
      submitted: { label: '待审批', color: '#FF9500', bgColor: '#fff7ed' },
      approved: { label: '已批准', color: '#00A86B', bgColor: '#f0fff4' },
      rejected: { label: '已拒绝', color: '#FF3B30', bgColor: '#fff5f5' },
      cancelled: { label: '已取消', color: '#8E8E93', bgColor: '#f9f9f9' }
    },
    
    // 紧急程度配置
    urgencyConfig: {
      low: { label: '一般', color: '#00A86B' },
      normal: { label: '正常', color: '#0066CC' },
      high: { label: '紧急', color: '#FF9500' },
      urgent: { label: '特急', color: '#FF3B30' }
    },
    
    // 类别配置
    categoryConfig: {
      feed: '饲料用品',
      equipment: '设备器具',
      medicine: '药品疫苗',
      facility: '基础设施',
      office: '办公用品',
      maintenance: '维修保养',
      other: '其他'
    },
    
    // 页面状态
    loading: false,
    refreshing: false,
    processing: false,
    
    // 审批流程
    approvalFlow: [],
    
    // 相关文件
    attachments: [],
    
    // 评论记录
    comments: [],
    
    // 操作权限
    canEdit: false,
    canCancel: false,
    canApprove: false,
    canReject: false,
    canComment: false
  },

  onLoad(options) {
    this.initPage(options);
  },

  onShow() {
    if (this.data.purchaseDetail) {
      this.loadPurchaseDetail(this.data.purchaseDetail.id);
    }
  },

  onPullDownRefresh() {
    this.loadPurchaseDetail(this.data.purchaseDetail?.id, true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    // 使用auth-helper进行可靠的登录检查

    if (!isLoggedIn()) {

      wx.showModal({

        title: '需要登录',

        content: '使用此功能需要先登录系统',

        confirmText: '去登录',

        cancelText: '取消',

        success: (res) => {

          if (res.confirm) {

            wx.redirectTo({ url: '/pages/login/login' });

          } else {

            wx.navigateBack();

          }

        }

      });

      return;

    }


    // 获取用户信息并同步数据

    const authUser = getCurrentUser();

    let userInfo = getCurrentUserInfo();


    if (!userInfo && authUser) {

      const app = getApp();

      app.globalData.userInfo = authUser;

      wx.setStorageSync('user_info', authUser);

      userInfo = getCurrentUserInfo();

    }

    

    const permissions = getUserPermissions();


    if (!userInfo) {

      wx.showModal({

        title: '数据错误',

        content: '无法获取用户信息，请重新登录',

        confirmText: '重新登录',

        showCancel: false,

        success: () => {

          wx.redirectTo({ url: '/pages/login/login' });

        }

      });

      return;

    }
    
    if (!options.id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      userInfo,
      permissions
    });
    
    this.loadPurchaseDetail(options.id);
  },

  /**
   * 加载采购申请详情
   */
  async loadPurchaseDetail(id, isRefresh = false) {
    if (this.data.loading && !isRefresh) return;
    
    this.setData({
      loading: !isRefresh,
      refreshing: isRefresh
    });
    
    try {
      const res = await request.get(`${API.ENDPOINTS.OA.PURCHASE.REQUESTS}/${id}`);
      const data = (res && res.success !== false) ? (res.data || res) : null;
      if (!data) throw new Error(res?.message || '加载失败');

      const purchaseDetail = data;
      const permissions = this.calculatePermissions(purchaseDetail);

      this.setData({
        purchaseDetail,
        ...permissions,
        approvalFlow: purchaseDetail.approvalFlow || [],
        attachments: purchaseDetail.attachments || [],
        comments: purchaseDetail.comments || []
      });

      wx.setNavigationBarTitle({ title: `采购详情 - ${purchaseDetail.requestNumber || purchaseDetail.id}` });

    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载采购详情失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 创建模拟采购详情数据
   */
  createMockPurchaseDetail(id) {
    const currentUser = getCurrentUser() || { id: 'user_001', name: '测试用户', employeeId: 'EMP001', department: '养殖部', position: '采购员', phone: '13800138000' };
    
    return {
      id: id,
      requestNumber: `PUR-${Date.now()}`,
      title: '饲料采购申请',
      status: 'submitted',
      category: 'feed',
      urgencyLevel: 'normal',
      applicantId: currentUser.id,
      applicantName: currentUser.name,
      applicantEmployeeId: currentUser.employeeId,
      department: currentUser.department,
      position: currentUser.position,
      phone: currentUser.phone,
      description: '采购优质饲料，保证养鹅营养需求，提高养殖效益。',
      businessPurpose: '为了保证鹅群健康成长，需要采购高质量的饲料。',
      totalAmount: 15000,
      expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString(),
      items: [
        {
          itemName: '优质鹅用全价配合饲料',
          specification: '25kg/袋',
          quantity: 20,
          unit: '袋',
          unitPrice: 450,
          totalPrice: 9000,
          brand: '正大集团',
          supplierSuggestion: '正大饲料经销商',
          remarks: '适合20-40日龄鹅群'
        },
        {
          itemName: '鹅用预混料',
          specification: '25kg/袋',
          quantity: 12,
          unit: '袋',
          unitPrice: 500,
          totalPrice: 6000,
          brand: '希望集团',
          supplierSuggestion: '希望饲料代理商',
          remarks: '含维生素和矿物质'
        }
      ],
      approvalFlow: [
        {
          id: 'flow_001',
          approverName: '张经理',
          actionText: '已提交审批',
          status: 'pending',
          createdAt: new Date().toISOString(),
          comment: '需要采购部门经理审批'
        }
      ],
      attachments: [
        {
          id: 'att_001',
          fileName: '饲料报价单.pdf',
          fileSize: '2.5MB',
          uploadTime: new Date().toISOString()
        }
      ],
      comments: [
        {
          id: 'comment_001',
          authorName: currentUser.name,
          content: '根据当前鹅群规模，这个采购量是合理的。',
          createdAt: new Date().toISOString()
        }
      ]
    };
  },

  /**
   * 计算操作权限
   */
  calculatePermissions(purchaseDetail) {
    const { userInfo, permissions } = this.data;
    const isOwner = purchaseDetail.applicantId === userInfo.id;
    const status = purchaseDetail.status;
    
    return {
      canEdit: isOwner && (status === 'draft' || status === 'rejected'),
      canCancel: isOwner && (status === 'submitted' || status === 'draft'),
      canApprove: permissions.canApprove && status === 'submitted',
      canReject: permissions.canApprove && status === 'submitted',
      canComment: true
    };
  },

  /**
   * 编辑采购申请
   */
  onEdit() {
    const { purchaseDetail } = this.data;
    wx.navigateTo({
      url: `/pages/oa/purchase/apply/apply?id=${purchaseDetail.id}`
    });
  },

  /**
   * 复制采购申请
   */
  onCopy() {
    const { purchaseDetail } = this.data;
    wx.showModal({
      title: '复制申请',
      content: '确定要复制这个采购申请吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: `/pages/oa/purchase/apply/apply?copyFrom=${purchaseDetail.id}`
          });
        }
      }
    });
  },

  /**
   * 取消采购申请
   */
  async onCancel() {
    const { purchaseDetail } = this.data;
    
    wx.showModal({
      title: '取消申请',
      content: '确定要取消这个采购申请吗？取消后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          this.setData({ processing: true });
          
          try {
            const response = await request.post(`${API.ENDPOINTS.OA.PURCHASE.REQUESTS}/${purchaseDetail.id}/cancel`, {});
            if (response && response.success !== false) {
              wx.showToast({
                title: '取消成功',
                icon: 'success'
              });
              
              // 刷新详情
              setTimeout(() => {
                this.loadPurchaseDetail(purchaseDetail.id);
              }, 1000);
            } else {
              throw new Error(response?.message || '取消失败');
            }
          } catch (error) {
            wx.showToast({
              title: error.message || '取消失败',
              icon: 'none'
            });
          } finally {
            this.setData({ processing: false });
          }
        }
      }
    });
  },

  /**
   * 审批采购申请
   */
  onApprove() {
    const { purchaseDetail } = this.data;
    wx.navigateTo({
      url: `/pages/oa/approval/process/process?type=purchase&id=${purchaseDetail.id}&action=approve`
    });
  },

  /**
   * 拒绝采购申请
   */
  onReject() {
    const { purchaseDetail } = this.data;
    wx.navigateTo({
      url: `/pages/oa/approval/process/process?type=purchase&id=${purchaseDetail.id}&action=reject`
    });
  },

  /**
   * 查看物品详情
   */
  onItemTap(e) {
    const { index } = e.currentTarget.dataset;
    const item = this.data.purchaseDetail.items[index];
    
    wx.showModal({
      title: item.itemName,
      content: `规格：${item.specification || '无'}\n数量：${item.quantity} ${item.unit}\n单价：${this.formatCurrency(item.unitPrice)}\n总价：${this.formatCurrency(item.totalPrice)}\n品牌：${item.brand || '无'}\n建议供应商：${item.supplierSuggestion || '无'}\n备注：${item.remarks || '无'}`,
      showCancel: false
    });
  },

  /**
   * 查看审批流程
   */
  onViewApprovalFlow() {
    const { approvalFlow } = this.data;
    if (approvalFlow.length === 0) {
      wx.showToast({
        title: '暂无审批记录',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/oa/approval/flow/flow?type=purchase&id=${this.data.purchaseDetail.id}`
    });
  },

  /**
   * 查看附件
   */
  onViewAttachment(e) {
    const { index } = e.currentTarget.dataset;
    const attachment = this.data.attachments[index];
    
    // 实际应用中应该调用文件预览API
    wx.showModal({
      title: '查看附件',
      content: `文件名：${attachment.fileName}\n大小：${attachment.fileSize}\n上传时间：${this.formatDate(attachment.uploadTime)}`,
      showCancel: false
    });
  },

  /**
   * 添加评论
   */
  onAddComment() {
    wx.navigateTo({
      url: `/pages/oa/common/comment/comment?type=purchase&id=${this.data.purchaseDetail.id}`
    });
  },

  /**
   * 查看评论详情
   */
  onCommentTap(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.comments[index];
    
    wx.showModal({
      title: comment.authorName,
      content: comment.content,
      showCancel: false
    });
  },

  /**
   * 分享采购申请
   */
  onShareAppMessage() {
    const { purchaseDetail } = this.data;
    return {
      title: `采购申请：${purchaseDetail.title}`,
      path: `/pages/oa/purchase/detail/detail?id=${purchaseDetail.id}`,
      imageUrl: '/assets/icons/purchase.png'
    };
  },

  // 格式化方法
  formatCurrency,
  formatDate,
  formatTimeAgo
});