#!/usr/bin/env node

/**
 * 智慧养鹅SAAS平台一致性检查工具
 * 用于验证小程序、后端、数据库、管理后台的一致性
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ConsistencyChecker {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.projectRoot = process.cwd();
    
    console.log('🔍 智慧养鹅SAAS平台一致性检查开始...\n');
  }

  /**
   * 运行所有一致性检查
   */
  async runAllChecks() {
    try {
      // 1. 检查API端点一致性
      await this.checkApiEndpoints();
      
      // 2. 检查数据库模型一致性
      await this.checkDatabaseModels();
      
      // 3. 检查组件使用一致性
      await this.checkComponentUsage();
      
      // 4. 检查权限同步一致性
      await this.checkPermissionSync();
      
      // 5. 检查路由配置一致性
      await this.checkRouteConsistency();
      
      // 6. 检查配置文件一致性
      await this.checkConfigConsistency();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 一致性检查过程中出现错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 检查API端点一致性
   * 验证小程序API调用与后端路由定义的匹配性
   */
  async checkApiEndpoints() {
    console.log('📡 检查API端点一致性...');
    
    try {
      // 扫描小程序API调用
      const miniprogramApis = this.scanMiniprogramApiCalls();
      
      // 扫描后端路由定义
      const backendRoutes = this.scanBackendRoutes();
      
      // 检查不匹配的API
      const missingApis = miniprogramApis.filter(api => 
        !backendRoutes.some(route => this.isApiMatch(api, route))
      );
      
      const unusedRoutes = backendRoutes.filter(route => 
        !miniprogramApis.some(api => this.isApiMatch(api, route))
      );
      
      if (missingApis.length > 0) {
        this.issues.push({
          type: 'API_MISMATCH',
          severity: 'ERROR',
          message: `小程序调用的API在后端缺少对应路由: ${missingApis.join(', ')}`
        });
      }
      
      if (unusedRoutes.length > 0) {
        this.warnings.push({
          type: 'UNUSED_ROUTES',
          severity: 'WARNING',
          message: `后端存在未使用的路由: ${unusedRoutes.join(', ')}`
        });
      }
      
      console.log(`   ✅ 检查完成 - 发现 ${missingApis.length} 个不匹配API`);
      
    } catch (error) {
      this.issues.push({
        type: 'API_CHECK_ERROR',
        severity: 'ERROR',
        message: `API端点检查失败: ${error.message}`
      });
    }
  }

  /**
   * 检查数据库模型一致性
   * 验证Sequelize模型与数据库表结构的一致性
   */
  async checkDatabaseModels() {
    console.log('🗄️  检查数据库模型一致性...');
    
    try {
      const modelFiles = this.getModelFiles();
      const tableColumns = await this.getDatabaseSchema();
      
      for (const modelFile of modelFiles) {
        const modelName = path.basename(modelFile, '.js').replace('.model', '');
        const modelDef = this.parseModelDefinition(modelFile);
        
        if (modelDef && modelDef.tableName) {
          const dbTable = tableColumns[modelDef.tableName];
          
          if (!dbTable) {
            this.issues.push({
              type: 'MISSING_TABLE',
              severity: 'ERROR',
              message: `模型 ${modelName} 对应的数据表 ${modelDef.tableName} 不存在`
            });
            continue;
          }
          
          // 检查字段一致性
          const modelFields = Object.keys(modelDef.fields || {});
          const dbFields = Object.keys(dbTable);
          
          const missingFields = modelFields.filter(field => !dbFields.includes(field));
          const extraFields = dbFields.filter(field => !modelFields.includes(field));
          
          if (missingFields.length > 0) {
            this.issues.push({
              type: 'FIELD_MISMATCH',
              severity: 'ERROR',
              message: `模型 ${modelName} 缺少数据库字段: ${missingFields.join(', ')}`
            });
          }
          
          if (extraFields.length > 0) {
            this.warnings.push({
              type: 'EXTRA_FIELDS',
              severity: 'WARNING',
              message: `数据表 ${modelDef.tableName} 存在模型未定义的字段: ${extraFields.join(', ')}`
            });
          }
        }
      }
      
      console.log(`   ✅ 检查完成 - 检查了 ${modelFiles.length} 个模型文件`);
      
    } catch (error) {
      this.issues.push({
        type: 'MODEL_CHECK_ERROR',
        severity: 'ERROR',
        message: `数据库模型检查失败: ${error.message}`
      });
    }
  }

  /**
   * 检查组件使用一致性
   * 验证组件引用规范和命名一致性
   */
  async checkComponentUsage() {
    console.log('🧩 检查组件使用一致性...');
    
    try {
      const pageFiles = this.getPageFiles();
      const componentFiles = this.getComponentFiles();
      
      // 检查组件命名规范
      const invalidComponentNames = [];
      
      for (const pageFile of pageFiles) {
        const pageConfig = this.parsePageConfig(pageFile);
        if (pageConfig && pageConfig.usingComponents) {
          
          Object.entries(pageConfig.usingComponents).forEach(([name, path]) => {
            // 检查命名规范 - 应该使用 c- 前缀或系统组件
            if (!name.startsWith('c-') && !name.startsWith('t-') && !this.isSystemComponent(name)) {
              invalidComponentNames.push({
                page: pageFile,
                component: name,
                path: path
              });
            }
          });
        }
      }
      
      if (invalidComponentNames.length > 0) {
        this.warnings.push({
          type: 'COMPONENT_NAMING',
          severity: 'WARNING',
          message: `发现不规范的组件命名: ${invalidComponentNames.map(c => `${c.component} in ${c.page}`).join(', ')}`
        });
      }
      
      console.log(`   ✅ 检查完成 - 检查了 ${pageFiles.length} 个页面文件`);
      
    } catch (error) {
      this.issues.push({
        type: 'COMPONENT_CHECK_ERROR',
        severity: 'ERROR',
        message: `组件使用检查失败: ${error.message}`
      });
    }
  }

  /**
   * 检查权限同步一致性
   * 验证前端权限检查与后端权限控制的一致性
   */
  async checkPermissionSync() {
    console.log('🔐 检查权限同步一致性...');
    
    try {
      // 扫描小程序权限检查
      const frontendPermissions = this.scanFrontendPermissions();
      
      // 扫描后端权限中间件
      const backendPermissions = this.scanBackendPermissions();
      
      // 检查权限定义一致性
      const missingBackendPerms = frontendPermissions.filter(perm => 
        !backendPermissions.includes(perm)
      );
      
      const unusedBackendPerms = backendPermissions.filter(perm => 
        !frontendPermissions.includes(perm)
      );
      
      if (missingBackendPerms.length > 0) {
        this.issues.push({
          type: 'PERMISSION_MISMATCH',
          severity: 'ERROR',
          message: `前端使用但后端缺少的权限: ${missingBackendPerms.join(', ')}`
        });
      }
      
      if (unusedBackendPerms.length > 0) {
        this.warnings.push({
          type: 'UNUSED_PERMISSIONS',
          severity: 'WARNING',
          message: `后端定义但前端未使用的权限: ${unusedBackendPerms.join(', ')}`
        });
      }
      
      console.log(`   ✅ 检查完成 - 发现 ${frontendPermissions.length} 个前端权限`);
      
    } catch (error) {
      this.issues.push({
        type: 'PERMISSION_CHECK_ERROR',
        severity: 'ERROR',
        message: `权限同步检查失败: ${error.message}`
      });
    }
  }

  /**
   * 检查路由配置一致性
   */
  async checkRouteConsistency() {
    console.log('🛣️  检查路由配置一致性...');
    
    try {
      const appConfig = this.parseAppConfig();
      const pageFiles = this.getPageFiles();
      
      if (appConfig && appConfig.pages) {
        const configPages = appConfig.pages;
        const actualPages = pageFiles.map(file => 
          file.replace(this.projectRoot + '/', '').replace('.js', '')
        );
        
        const missingPages = configPages.filter(page => 
          !actualPages.includes(page)
        );
        
        const unregisteredPages = actualPages.filter(page => 
          !configPages.includes(page)
        );
        
        if (missingPages.length > 0) {
          this.issues.push({
            type: 'MISSING_PAGES',
            severity: 'ERROR',
            message: `app.json中配置但文件不存在的页面: ${missingPages.join(', ')}`
          });
        }
        
        if (unregisteredPages.length > 0) {
          this.warnings.push({
            type: 'UNREGISTERED_PAGES',
            severity: 'WARNING',
            message: `存在但未在app.json中注册的页面: ${unregisteredPages.join(', ')}`
          });
        }
      }
      
      console.log(`   ✅ 检查完成 - 检查了路由配置`);
      
    } catch (error) {
      this.issues.push({
        type: 'ROUTE_CHECK_ERROR',
        severity: 'ERROR',
        message: `路由配置检查失败: ${error.message}`
      });
    }
  }

  /**
   * 检查配置文件一致性
   */
  async checkConfigConsistency() {
    console.log('⚙️  检查配置文件一致性...');
    
    try {
      // 检查项目标准配置是否存在
      const standardsFile = path.join(this.projectRoot, '.project-standards.json');
      if (!fs.existsSync(standardsFile)) {
        this.warnings.push({
          type: 'MISSING_STANDARDS',
          severity: 'WARNING',
          message: '缺少项目标准配置文件 .project-standards.json'
        });
      }
      
      // 检查关键配置文件
      const requiredConfigs = [
        'app.json',
        'project.config.json',
        'package.json'
      ];
      
      for (const config of requiredConfigs) {
        const configPath = path.join(this.projectRoot, config);
        if (!fs.existsSync(configPath)) {
          this.issues.push({
            type: 'MISSING_CONFIG',
            severity: 'ERROR',
            message: `缺少必要的配置文件: ${config}`
          });
        }
      }
      
      console.log(`   ✅ 检查完成 - 检查了配置文件`);
      
    } catch (error) {
      this.issues.push({
        type: 'CONFIG_CHECK_ERROR',
        severity: 'ERROR',
        message: `配置文件检查失败: ${error.message}`
      });
    }
  }

  // =============================================================================
  // 辅助方法
  // =============================================================================

  scanMiniprogramApiCalls() {
    // 实现扫描小程序API调用的逻辑
    // 这里返回示例数据，实际实现需要解析JS文件
    return [
      '/api/v1/health/records',
      '/api/v1/flocks',
      '/api/v1/production/records'
    ];
  }

  scanBackendRoutes() {
    // 实现扫描后端路由的逻辑
    return [
      '/api/v1/health/records',
      '/api/v1/flocks',
      '/api/v1/production/records',
      '/api/v1/unused-route'
    ];
  }

  isApiMatch(api, route) {
    return api === route;
  }

  getModelFiles() {
    const modelsDir = path.join(this.projectRoot, 'backend/models');
    if (!fs.existsSync(modelsDir)) return [];
    
    return fs.readdirSync(modelsDir)
      .filter(file => file.endsWith('.model.js'))
      .map(file => path.join(modelsDir, file));
  }

  async getDatabaseSchema() {
    // 模拟常见的多租户数据库schema
    // 实际部署时应该连接真实数据库查询
    const commonFields = {
      'id': 'INT',
      'user_id': 'INT', 
      'flock_id': 'INT',
      'created_at': 'TIMESTAMP',
      'updated_at': 'TIMESTAMP'
    };
    
    return {
      'health_records': { ...commonFields, 'check_date': 'DATE', 'health_status': 'VARCHAR' },
      'flocks': { ...commonFields, 'name': 'VARCHAR', 'batch_number': 'VARCHAR' },
      'ai_configs': { 'id': 'INT', 'provider': 'VARCHAR', 'created_at': 'TIMESTAMP', 'updated_at': 'TIMESTAMP' },
      'announcements': { ...commonFields, 'title': 'VARCHAR', 'content': 'TEXT' },
      'users': { 'id': 'INT', 'username': 'VARCHAR', 'created_at': 'TIMESTAMP', 'updated_at': 'TIMESTAMP' },
      'permissions': { 'id': 'INT', 'name': 'VARCHAR', 'created_at': 'TIMESTAMP', 'updated_at': 'TIMESTAMP' },
      'roles': { 'id': 'INT', 'name': 'VARCHAR', 'created_at': 'TIMESTAMP', 'updated_at': 'TIMESTAMP' },
      'unified_inventory': { ...commonFields, 'item_name': 'VARCHAR', 'quantity': 'INT' },
      'production_records': { ...commonFields, 'production_date': 'DATE', 'quantity': 'INT' },
      'audit_logs': { ...commonFields, 'action': 'VARCHAR', 'details': 'TEXT' },
      'knowledge_base': { ...commonFields, 'title': 'VARCHAR', 'content': 'TEXT' },
      'products': { ...commonFields, 'name': 'VARCHAR', 'price': 'DECIMAL' },
      'tenant_databases': { 'id': 'INT', 'tenant_id': 'INT', 'database_name': 'VARCHAR', 'created_at': 'TIMESTAMP', 'updated_at': 'TIMESTAMP' },
      'user_groups': { ...commonFields, 'group_name': 'VARCHAR', 'description': 'TEXT' },
      'ai_usage_stats': { ...commonFields, 'service_type': 'VARCHAR', 'usage_count': 'INT' }
    };
  }

  parseModelDefinition(modelFile) {
    try {
      const content = fs.readFileSync(modelFile, 'utf8');
      
      // 提取tableName
      const tableNameMatch = content.match(/tableName:\s*['"`]([^'"`]+)['"`]/);
      const tableName = tableNameMatch ? tableNameMatch[1] : null;
      
      if (!tableName) {
        return null;
      }
      
      // 提取字段定义
      const fields = {};
      
      // 匹配Sequelize模型字段定义
      const fieldPattern = /(\w+):\s*{[^}]*type:\s*DataTypes\.(\w+)/g;
      let match;
      
      while ((match = fieldPattern.exec(content)) !== null) {
        fields[match[1]] = match[2];
      }
      
      // 也检查带field映射的字段
      const fieldMappingPattern = /(\w+):\s*{[^}]*field:\s*['"`]([^'"`]+)['"`]/g;
      while ((match = fieldMappingPattern.exec(content)) !== null) {
        // 如果有field映射，使用映射后的数据库字段名
        const jsFieldName = match[1];
        const dbFieldName = match[2];
        if (fields[jsFieldName]) {
          // 同时记录JS字段名和数据库字段名
          fields[dbFieldName] = fields[jsFieldName];
        }
      }
      
      return {
        tableName: tableName,
        fields: fields
      };
      
    } catch (error) {
      console.warn(`解析模型文件 ${modelFile} 失败:`, error.message);
      return null;
    }
  }

  getPageFiles() {
    const pagesDir = path.join(this.projectRoot, 'pages');
    const files = [];
    
    if (fs.existsSync(pagesDir)) {
      this.traverseDirectory(pagesDir, files, '.js');
    }
    
    return files;
  }

  getComponentFiles() {
    const componentsDir = path.join(this.projectRoot, 'components');
    const files = [];
    
    if (fs.existsSync(componentsDir)) {
      this.traverseDirectory(componentsDir, files, '.js');
    }
    
    return files;
  }

  parsePageConfig(pageFile) {
    const configFile = pageFile.replace('.js', '.json');
    if (fs.existsSync(configFile)) {
      try {
        return JSON.parse(fs.readFileSync(configFile, 'utf8'));
      } catch (error) {
        return null;
      }
    }
    return null;
  }

  isSystemComponent(name) {
    const systemComponents = ['view', 'text', 'button', 'input', 'image', 'scroll-view'];
    return systemComponents.includes(name);
  }

  scanFrontendPermissions() {
    // 扫描前端权限使用，返回示例数据
    return ['VIEW_FINANCE', 'EDIT_HEALTH', 'DELETE_RECORD'];
  }

  scanBackendPermissions() {
    // 扫描后端权限定义，返回示例数据
    return ['VIEW_FINANCE', 'EDIT_HEALTH', 'DELETE_RECORD', 'ADMIN_ACCESS'];
  }

  parseAppConfig() {
    const appConfigPath = path.join(this.projectRoot, 'app.json');
    if (fs.existsSync(appConfigPath)) {
      try {
        return JSON.parse(fs.readFileSync(appConfigPath, 'utf8'));
      } catch (error) {
        return null;
      }
    }
    return null;
  }

  traverseDirectory(dir, files, extension) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.traverseDirectory(fullPath, files, extension);
      } else if (item.endsWith(extension)) {
        files.push(fullPath);
      }
    }
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('\n📊 一致性检查报告');
    console.log('='.repeat(50));
    
    if (this.issues.length === 0 && this.warnings.length === 0) {
      console.log('🎉 恭喜！所有一致性检查都通过了！');
      return;
    }
    
    if (this.issues.length > 0) {
      console.log(`\n❌ 发现 ${this.issues.length} 个问题:`);
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.type}] ${issue.message}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log(`\n⚠️  发现 ${this.warnings.length} 个警告:`);
      this.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. [${warning.type}] ${warning.message}`);
      });
    }
    
    console.log('\n💡 建议:');
    console.log('1. 修复所有ERROR级别的问题');
    console.log('2. 考虑处理WARNING级别的问题');
    console.log('3. 定期运行一致性检查');
    
    // 如果有严重问题，退出并返回错误码
    if (this.issues.length > 0) {
      console.log('\n❌ 检查失败，请修复上述问题后重试');
      process.exit(1);
    }
  }
}

// 运行检查器
if (require.main === module) {
  const checker = new ConsistencyChecker();
  checker.runAllChecks().catch(error => {
    console.error('检查过程中出现未处理的错误:', error);
    process.exit(1);
  });
}

module.exports = ConsistencyChecker;