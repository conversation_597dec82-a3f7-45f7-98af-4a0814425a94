/* pages/orders/orders.wxss - 重新设计的订单列表页面样式 */
@import '/styles/design-system.wxss';

.orders-page {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 状态标签栏 */
.status-tabs {
  background: var(--bg-primary);
  border-bottom: 1rpx solid var(--border-light);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs-scroll {
  padding: 0;
}

.tabs-container {
  display: flex;
  padding: 0 var(--space-lg);
}

.tab-item {
  position: relative;
  padding: var(--space-xl) var(--space-lg);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  transition: var(--transition-all);
  min-width: fit-content;
}

.tab-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  transition: var(--transition-all);
}

.tab-item.active .tab-text {
  color: var(--primary);
  font-weight: var(--font-semibold);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 32rpx;
  height: 4rpx;
  background: var(--primary);
  border-radius: var(--radius-full);
}

.tab-badge {
  background: var(--error);
  color: var(--text-inverse);
  font-size: var(--text-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
}

/* 订单列表 */
.orders-list {
  padding: var(--space-lg);
  height: calc(100vh - 120rpx);
}

.order-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
  transition: var(--transition-all);
}

.order-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1rpx solid var(--border-subtle);
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-xs);
}

.order-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.order-status {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-pending {
  background: var(--warning-subtle);
  color: var(--warning);
  border: 1rpx solid var(--warning-light);
}

.status-pending .status-dot {
  background: var(--warning);
  animation: pulse 2s infinite;
}

.status-paid {
  background: var(--info-subtle);
  color: var(--info);
  border: 1rpx solid var(--info-light);
}

.status-paid .status-dot {
  background: var(--info);
}

.status-shipped {
  background: var(--success-subtle);
  color: var(--success);
  border: 1rpx solid var(--success-light);
}

.status-shipped .status-dot {
  background: var(--success);
}

.status-completed {
  background: var(--neutral-subtle);
  color: var(--neutral);
  border: 1rpx solid var(--neutral-light);
}

.status-completed .status-dot {
  background: var(--neutral);
}

.status-cancelled {
  background: var(--error-subtle);
  color: var(--error);
  border: 1rpx solid var(--error-light);
}

.status-cancelled .status-dot {
  background: var(--error);
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

/* 商品区域 */
.goods-section {
  padding: 0 var(--space-xl) var(--space-lg);
}

.shop-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-sm);
  border-bottom: 1rpx solid var(--border-subtle);
}

.shop-icon {
  font-size: var(--text-lg);
}

.shop-name {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.goods-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.goods-item {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.goods-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  flex-shrink: 0;
  border: 1rpx solid var(--border-light);
}

.goods-detail {
  flex: 1;
  min-width: 0;
}

.goods-name {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: var(--text-sm);
  color: var(--primary);
  font-weight: var(--font-semibold);
}

.goods-qty {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.more-goods {
  text-align: center;
  padding: var(--space-sm) 0;
}

.more-text {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* 订单金额 */
.order-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1rpx solid var(--border-subtle);
}

.amount-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.amount-value {
  font-size: var(--text-lg);
  color: var(--primary);
  font-weight: var(--font-bold);
}

/* 操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-lg);
  padding: var(--space-lg) var(--space-xl);
}

.action-btn {
  padding: var(--space-sm) var(--space-xl);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border: none;
  transition: var(--transition-all);
  min-width: 120rpx;
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-light);
}

.action-btn.secondary:active {
  background: var(--bg-tertiary);
  transform: scale(0.98);
}

.action-btn.primary {
  background: var(--primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.action-btn.primary:active {
  background: var(--primary-dark);
  transform: scale(0.98);
  box-shadow: none;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: var(--space-2xl) 0;
}

.load-text {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-6xl) var(--space-4xl);
  text-align: center;
  min-height: 60vh;
}

.empty-icon {
  font-size: 120rpx;
  opacity: 0.4;
  margin-bottom: var(--space-2xl);
}

.empty-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--space-lg);
}

.empty-desc {
  font-size: var(--text-base);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-2xl);
}

.go-shop-btn {
  background: var(--primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  box-shadow: var(--shadow-primary);
  transition: var(--transition-all);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.go-shop-btn:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
  background: var(--primary-dark);
}

.btn-text {
  font-size: var(--text-base);
}

.btn-icon {
  font-size: var(--text-lg);
}

/* 评价模态框 */
.rating-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  width: 600rpx;
  max-width: 90vw;
  max-height: 80vh;
  box-shadow: var(--shadow-xl);
  position: relative;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl) var(--space-2xl);
  border-bottom: 1rpx solid var(--border-light);
}

.modal-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.modal-close {
  font-size: var(--text-2xl);
  color: var(--text-tertiary);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-all);
}

.modal-close:active {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.modal-body {
  padding: var(--space-2xl);
}

.rating-section {
  margin-bottom: var(--space-2xl);
}

.rating-label {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-lg);
}

.rating-stars {
  display: flex;
  gap: var(--space-sm);
}

.star {
  font-size: var(--text-2xl);
  color: var(--border-light);
  transition: var(--transition-all);
}

.star.active {
  color: var(--warning);
}

.comment-section {
  margin-bottom: var(--space-lg);
}

.comment-label {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-lg);
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  padding: var(--space-lg);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: var(--bg-secondary);
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-xl) var(--space-2xl);
  border-top: 1rpx solid var(--border-light);
}

.modal-footer .cancel-btn {
  flex: 1;
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-light);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
}

.modal-footer .submit-btn {
  flex: 1;
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  background: var(--primary);
  color: var(--text-inverse);
  border: none;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  box-shadow: var(--shadow-sm);
}

.modal-footer .submit-btn:active {
  background: var(--primary-dark);
  transform: scale(0.98);
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .orders-list {
    padding: var(--space-sm);
  }

  .order-card {
    margin-bottom: var(--space-sm);
  }

  .order-header,
  .goods-section,
  .order-amount,
  .order-actions {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }

  .action-btn {
    min-width: 100rpx;
    padding: var(--space-sm) var(--space-lg);
  }

  .modal-content {
    width: 95vw;
  }
}

/* 可访问性支持 */
@media (prefers-reduced-motion: reduce) {

  .order-card,
  .tab-badge,
  .status-dot,
  .action-btn,
  .go-shop-btn,
  .modal-content {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {
  .order-card {
    border-width: 2rpx;
  }

  .order-status {
    border-width: 2rpx;
  }

  .action-btn {
    border-width: 2rpx;
  }
}