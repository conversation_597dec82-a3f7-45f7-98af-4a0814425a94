/**
 * 🛠️ 开发环境HTTPS代理设置脚本
 * 自动安装依赖、生成SSL证书、配置开发环境
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class DevProxySetup {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.certDir = path.join(this.projectRoot, 'certs');
    this.scriptsDir = path.join(this.projectRoot, 'scripts');
    
    this.requiredPackages = [
      'express',
      'http-proxy-middleware',
      'cors'
    ];
    
    this.config = {
      domain: 'dev-api.zhihuiyange.local',
      httpsPort: 8443,
      httpPort: 8080,
      targetPort: 3000
    };
  }

  /**
   * 打印带颜色的消息
   */
  log(level, message, data = null) {
    const colors = {
      info: '\x1b[36m',    // 青色
      success: '\x1b[32m', // 绿色
      warn: '\x1b[33m',    // 黄色
      error: '\x1b[31m',   // 红色
      reset: '\x1b[0m'     // 重置
    };

    const color = colors[level] || colors.info;
    const timestamp = new Date().toISOString().substring(11, 19);
    
    console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
    
    if (data && typeof data === 'object') {
      console.log('  ', JSON.stringify(data, null, 2));
    } else if (data) {
      console.log('  ', data);
    }
  }

  /**
   * 检查Node.js和npm环境
   */
  async checkEnvironment() {
    this.log('info', '🔍 检查开发环境...');

    try {
      const { stdout: nodeVersion } = await execAsync('node --version');
      this.log('success', `✅ Node.js版本: ${nodeVersion.trim()}`);

      const { stdout: npmVersion } = await execAsync('npm --version');
      this.log('success', `✅ npm版本: ${npmVersion.trim()}`);

      return true;
    } catch (error) {
      this.log('error', '❌ Node.js或npm未安装，请先安装Node.js');
      this.log('info', '下载地址: https://nodejs.org/');
      return false;
    }
  }

  /**
   * 检查和安装依赖包
   */
  async installDependencies() {
    this.log('info', '📦 检查项目依赖...');

    try {
      // 读取package.json
      const packageJsonPath = path.join(this.projectRoot, 'package.json');
      let packageJson = {};
      
      if (fs.existsSync(packageJsonPath)) {
        packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      } else {
        this.log('warn', '⚠️  未找到package.json，创建基础配置');
        packageJson = {
          name: 'zhihuiyange-miniprogram',
          version: '1.0.0',
          description: '智慧养鹅小程序',
          main: 'app.js',
          scripts: {
            'start:proxy': 'node scripts/dev-https-proxy.js',
            'setup:proxy': 'node scripts/setup-dev-proxy.js'
          },
          dependencies: {},
          devDependencies: {}
        };
      }

      // 检查缺失的依赖
      const missingPackages = [];
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      for (const pkg of this.requiredPackages) {
        if (!dependencies[pkg]) {
          missingPackages.push(pkg);
        }
      }

      if (missingPackages.length > 0) {
        this.log('info', `📦 安装缺失的依赖包: ${missingPackages.join(', ')}`);
        
        const installCommand = `npm install ${missingPackages.join(' ')} --save-dev`;
        await execAsync(installCommand, { cwd: this.projectRoot });
        
        this.log('success', '✅ 依赖包安装完成');
      } else {
        this.log('success', '✅ 所有依赖包已安装');
      }

      // 更新package.json脚本
      if (!packageJson.scripts) packageJson.scripts = {};
      packageJson.scripts['start:proxy'] = 'node scripts/dev-https-proxy.js';
      packageJson.scripts['setup:proxy'] = 'node scripts/setup-dev-proxy.js';

      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      
      return true;
    } catch (error) {
      this.log('error', '❌ 依赖包安装失败:', error.message);
      return false;
    }
  }

  /**
   * 创建证书目录
   */
  createCertDirectory() {
    if (!fs.existsSync(this.certDir)) {
      fs.mkdirSync(this.certDir, { recursive: true });
      this.log('success', `✅ 创建证书目录: ${this.certDir}`);
    } else {
      this.log('info', '📁 证书目录已存在');
    }
  }

  /**
   * 检查OpenSSL
   */
  async checkOpenSSL() {
    try {
      await execAsync('openssl version');
      this.log('success', '✅ OpenSSL已安装');
      return true;
    } catch (error) {
      this.log('warn', '⚠️  OpenSSL未安装或不在PATH中');
      this.log('info', '安装方法:');
      
      const platform = process.platform;
      if (platform === 'darwin') {
        this.log('info', '  macOS: brew install openssl');
      } else if (platform === 'linux') {
        this.log('info', '  Ubuntu/Debian: sudo apt-get install openssl');
        this.log('info', '  CentOS/RHEL: sudo yum install openssl');
      } else if (platform === 'win32') {
        this.log('info', '  Windows: 下载并安装 OpenSSL for Windows');
        this.log('info', '  https://slproweb.com/products/Win32OpenSSL.html');
      }
      
      return false;
    }
  }

  /**
   * 生成SSL证书
   */
  async generateSSLCertificate() {
    const keyPath = path.join(this.certDir, 'dev-cert.key');
    const certPath = path.join(this.certDir, 'dev-cert.crt');

    // 检查证书是否已存在
    if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
      this.log('info', '📜 SSL证书已存在，跳过生成');
      return true;
    }

    this.log('info', '🔐 生成SSL证书...');

    try {
      // 生成私钥
      await execAsync(`openssl genrsa -out "${keyPath}" 2048`);
      
      // 生成证书
      const certCommand = `openssl req -new -x509 -key "${keyPath}" -out "${certPath}" -days 365 -subj "/C=CN/ST=Beijing/L=Beijing/O=DevProxy/OU=Development/CN=${this.config.domain}"`;
      await execAsync(certCommand);

      this.log('success', '✅ SSL证书生成成功');
      this.log('info', `   私钥: ${keyPath}`);
      this.log('info', `   证书: ${certPath}`);
      
      return true;
    } catch (error) {
      this.log('error', '❌ SSL证书生成失败:', error.message);
      this.log('info', '请手动生成SSL证书，或使用临时的不校验域名方案');
      return false;
    }
  }

  /**
   * 配置hosts文件
   */
  async configureHosts() {
    const hostsPath = process.platform === 'win32' 
      ? 'C:\\Windows\\System32\\drivers\\etc\\hosts'
      : '/etc/hosts';

    try {
      const hostsContent = fs.readFileSync(hostsPath, 'utf8');
      const hostEntry = `127.0.0.1 ${this.config.domain}`;

      if (hostsContent.includes(hostEntry)) {
        this.log('info', '🌐 hosts文件已配置');
        return true;
      }

      this.log('info', '🌐 配置hosts文件...');
      this.log('warn', '需要管理员权限来修改hosts文件');

      if (process.platform === 'win32') {
        this.log('info', '请以管理员身份运行命令提示符，然后执行:');
        this.log('info', `echo ${hostEntry} >> C:\\Windows\\System32\\drivers\\etc\\hosts`);
      } else {
        this.log('info', '请执行以下命令:');
        this.log('info', `echo "${hostEntry}" | sudo tee -a /etc/hosts`);
      }

      return false;
    } catch (error) {
      this.log('error', '❌ 无法访问hosts文件:', error.message);
      this.log('info', `请手动添加到hosts文件: 127.0.0.1 ${this.config.domain}`);
      return false;
    }
  }

  /**
   * 创建启动脚本
   */
  createStartupScripts() {
    // Windows批处理脚本
    const batScript = `@echo off
echo 启动智慧养鹅开发环境HTTPS代理服务器...
cd /d "%~dp0"
node dev-https-proxy.js
pause`;

    const batPath = path.join(this.scriptsDir, 'start-dev-proxy.bat');
    fs.writeFileSync(batPath, batScript);

    // 确保shell脚本存在且有执行权限
    const shellScriptPath = path.join(this.scriptsDir, 'start-dev-proxy.sh');
    if (fs.existsSync(shellScriptPath)) {
      try {
        fs.chmodSync(shellScriptPath, '755');
      } catch (error) {
        this.log('warn', '⚠️  无法设置shell脚本执行权限');
      }
    }

    this.log('success', '✅ 启动脚本已创建');
    this.log('info', `   Windows: ${batPath}`);
    this.log('info', `   Unix/Linux/macOS: ${shellScriptPath}`);
  }

  /**
   * 生成配置文档
   */
  generateConfigGuide() {
    const guidePath = path.join(this.projectRoot, 'docs', 'dev-proxy-setup-guide.md');
    
    const guideContent = `# 🚀 开发环境HTTPS代理设置指南

## 自动设置完成 ✅

您的开发环境HTTPS代理已设置完成！

### 配置信息
- **代理域名**: ${this.config.domain}
- **HTTPS端口**: ${this.config.httpsPort}
- **HTTP端口**: ${this.config.httpPort}
- **目标端口**: ${this.config.targetPort}

### 启动代理服务器

#### 方法1: 使用npm脚本
\`\`\`bash
npm run start:proxy
\`\`\`

#### 方法2: 直接运行脚本
\`\`\`bash
# Unix/Linux/macOS
./scripts/start-dev-proxy.sh

# Windows
scripts\\start-dev-proxy.bat
\`\`\`

#### 方法3: 使用Node.js
\`\`\`bash
node scripts/dev-https-proxy.js
\`\`\`

### 微信小程序配置

1. **打开微信开发者工具**
2. **点击右上角"详情"按钮**
3. **选择"域名信息"选项卡**
4. **添加以下域名到request合法域名:**
   \`\`\`
   https://${this.config.domain}:${this.config.httpsPort}
   \`\`\`

### 临时方案（仅开发环境）

如果SSL证书有问题，可以临时使用：
1. **详情** → **本地设置**
2. **勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"**
3. **重新编译项目**

### 验证配置

在小程序控制台运行：
\`\`\`javascript
wx.request({
  url: 'https://${this.config.domain}:${this.config.httpsPort}/health',
  success: (res) => console.log('✅ 代理服务器连接成功', res),
  fail: (err) => console.log('❌ 代理服务器连接失败', err)
});
\`\`\`

---

🎯 **现在您可以开始开发了！代理服务器将自动将HTTPS请求转发到您的本地后端服务器。**
`;

    // 确保docs目录存在
    const docsDir = path.dirname(guidePath);
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }

    fs.writeFileSync(guidePath, guideContent);
    this.log('success', `✅ 配置指南已生成: ${guidePath}`);
  }

  /**
   * 运行完整设置流程
   */
  async run() {
    this.log('info', '🚀 开始设置开发环境HTTPS代理...');
    console.log('');

    // 检查环境
    const envOk = await this.checkEnvironment();
    if (!envOk) {
      process.exit(1);
    }

    // 安装依赖
    const depsOk = await this.installDependencies();
    if (!depsOk) {
      this.log('error', '❌ 依赖安装失败，请手动安装后重试');
      process.exit(1);
    }

    // 创建证书目录
    this.createCertDirectory();

    // 检查OpenSSL
    const opensslOk = await this.checkOpenSSL();
    
    // 生成SSL证书
    if (opensslOk) {
      await this.generateSSLCertificate();
    } else {
      this.log('warn', '⚠️  跳过SSL证书生成，建议安装OpenSSL后重新运行');
    }

    // 配置hosts文件
    await this.configureHosts();

    // 创建启动脚本
    this.createStartupScripts();

    // 生成配置指南
    this.generateConfigGuide();

    // 输出完成信息
    console.log('');
    this.log('success', '🎉 开发环境HTTPS代理设置完成！');
    console.log('');
    this.log('info', '📋 下一步操作:');
    this.log('info', '1. 启动后端服务器 (端口3000)');
    this.log('info', '2. 运行代理服务器: npm run start:proxy');
    this.log('info', '3. 配置微信开发者工具域名');
    this.log('info', '4. 开始开发！');
    console.log('');
    this.log('info', `📖 详细指南: docs/dev-proxy-setup-guide.md`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const setup = new DevProxySetup();
  setup.run().catch(error => {
    console.error('设置失败:', error);
    process.exit(1);
  });
}

module.exports = DevProxySetup;