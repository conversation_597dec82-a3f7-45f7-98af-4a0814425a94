<!-- pages/oa/reimbursement/detail/detail.wxml -->

<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 报销详情内容 -->
  <view wx:else class="detail-content">
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="title-area">
          <text class="main-title">{{reimbursement.title}}</text>
          <text class="reimbursement-number">{{reimbursement.reimbursement_number}}</text>
        </view>
        <oa-status-tag
          text="{{getStatusConfig(reimbursement.status || '').label || ''}}"
          color="{{getStatusConfig(reimbursement.status || '').color || '#8E8E93'}}"
          backgroundColor="{{getStatusConfig(reimbursement.status || '').bgColor || '#F2F2F7'}}"
          size="medium"
        />
      </view>

      <view class="amount-section">
        <text class="amount-label">报销金额</text>
        <text class="amount-value">{{reimbursement.totalAmountFormatted}}</text>
      </view>
    </view>

    <!-- 申请人信息 -->
    <view class="info-section">
      <view class="section-title">申请人信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">姓名</text>
          <text class="info-value">{{reimbursement.applicant_name}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工号</text>
          <text class="info-value">{{reimbursement.applicant_employee_id}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">部门</text>
          <text class="info-value">{{reimbursement.department}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">职位</text>
          <text class="info-value">{{reimbursement.position}}</text>
        </view>
      </view>
    </view>

    <!-- 报销信息 -->
    <view class="info-section">
      <view class="section-title">报销信息</view>
      <view class="info-list">
        <view class="info-row">
          <text class="row-label">报销类别</text>
          <text class="row-value">{{getCategoryLabel(reimbursement.category)}}</text>
        </view>
        <view class="info-row">
          <text class="row-label">业务目的</text>
          <text class="row-value">{{reimbursement.business_purpose}}</text>
        </view>
        <view wx:if="{{reimbursement.description}}" class="info-row">
          <text class="row-label">报销说明</text>
          <text class="row-value">{{reimbursement.description}}</text>
        </view>
        <view class="info-row">
          <text class="row-label">费用期间</text>
          <text class="row-value">{{reimbursement.expense_period_start}} 至 {{reimbursement.expense_period_end}}</text>
        </view>
        <view class="info-row">
          <text class="row-label">付款方式</text>
          <text class="row-value">{{reimbursement.payment_method === 'bank_transfer' ? '银行转账' : reimbursement.payment_method}}</text>
        </view>
        <view wx:if="{{reimbursement.bank_account}}" class="info-row">
          <text class="row-label">银行账户</text>
          <text class="row-value">{{reimbursement.bank_account}}</text>
        </view>
        <view class="info-row">
          <text class="row-label">申请时间</text>
          <text class="row-value">{{reimbursement.created_at}}</text>
        </view>
        <view wx:if="{{reimbursement.submitted_at}}" class="info-row">
          <text class="row-label">提交时间</text>
          <text class="row-value">{{reimbursement.submitted_at}}</text>
        </view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="info-section">
      <view class="section-title">费用明细</view>
      <view class="expense-list">
        <view
          wx:for="{{reimbursement.items}}"
          wx:key="id"
          class="expense-item"
          data-index="{{index}}"
          bindtap="onViewExpenseItem"
        >
          <view class="expense-header">
            <text class="expense-description">{{item.description}}</text>
            <text class="expense-amount">{{item.amountFormatted}}</text>
          </view>
          <view class="expense-details">
            <view class="expense-detail">
              <text class="detail-label">日期：</text>
              <text class="detail-value">{{item.expense_date}}</text>
            </view>
            <view class="expense-detail">
              <text class="detail-label">类别：</text>
              <text class="detail-value">{{getCategoryLabel(item.category)}}</text>
            </view>
            <view wx:if="{{item.location}}" class="expense-detail">
              <text class="detail-label">地点：</text>
              <text class="detail-value">{{item.location}}</text>
            </view>
            <view wx:if="{{item.has_invoice}}" class="expense-detail">
              <text class="detail-label">发票：</text>
              <text class="detail-value">{{item.invoice_number || '有发票'}}</text>
            </view>
            <view wx:if="{{item.remarks}}" class="expense-detail">
              <text class="detail-label">备注：</text>
              <text class="detail-value">{{item.remarks}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 费用汇总 -->
      <view class="expense-summary">
        <view class="summary-row">
          <text class="summary-label">费用明细总计</text>
          <text class="summary-value">{{reimbursement.totalAmountFormatted}}</text>
        </view>
      </view>
    </view>

    <!-- 审批流程 -->
    <view wx:if="{{reimbursement.approvals && reimbursement.approvals.length > 0}}" class="approval-section">
      <oa-approval-flow
        approvals="{{reimbursement.approvals}}"
        currentStatus="{{reimbursement.status}}"
        actionable="{{canApprove()}}"
        currentUserId="{{userInfo.id}}"
        bindapproval="onApprovalAction"
      />
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <!-- 申请人操作 -->
      <view wx:if="{{reimbursement.user_id === userInfo.id}}" class="owner-actions">
        <button
          wx:if="{{canEdit()}}"
          class="action-btn secondary"
          bindtap="onEdit"
        >
          编辑
        </button>
        <button
          wx:if="{{canDelete()}}"
          class="action-btn danger"
          bindtap="onDelete"
        >
          删除
        </button>
      </view>

      <!-- 审批人操作 -->
      <view wx:if="{{canApprove()}}" class="approver-actions">
        <button
          class="action-btn danger"
          bindtap="showApprovalModal"
          data-action="reject"
        >
          拒绝
        </button>
        <button
          class="action-btn primary"
          bindtap="showApprovalModal"
          data-action="approve"
        >
          通过
        </button>
      </view>
    </view>
  </view>

  <!-- 审批模态框 -->
  <view wx:if="{{showApprovalModal}}" class="modal-overlay" bindtap="hideApprovalModal">
            <view class="approval-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{approvalAction === 'approve' ? '审批通过' : '审批拒绝'}}</text>
        <button class="modal-close" bindtap="hideApprovalModal">×</button>
      </view>

      <view class="modal-content">
        <view class="form-item">
          <text class="form-label">审批意见</text>
          <textarea
            class="form-textarea"
            placeholder="请输入审批意见..."
            value="{{approvalRemarks}}"
            bindinput="onRemarksInput"
            maxlength="500"
          />
        </view>
      </view>

      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="hideApprovalModal">取消</button>
        <button
          class="modal-btn confirm"
          loading="{{submitting}}"
          bindtap="confirmApproval"
        >
          确认
        </button>
      </view>
    </view>
  </view>
</view>