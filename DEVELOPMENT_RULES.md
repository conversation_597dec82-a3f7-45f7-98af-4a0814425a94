# 智慧养鹅SAAS平台开发规则 🚀

## 🎯 核心开发规则

> **重要**: 所有开发工作必须遵循以下规则，确保项目质量和一致性

### 📖 1. Context7最佳实践查询 (强制)

**规则**: 开发任何功能前，必须先查询Context7中的微信小程序最佳实践

```bash
# 在开发前运行
npm run context7:query
```

**必查内容**:
- 微信小程序开发框架规范
- TDesign组件库使用规范
- API接口设计最佳实践
- 数据库设计规范

### 🧠 2. Sequential Thinking (强制)

**规则**: 处理复杂问题时必须使用分步思考模式

**适用场景**:
- 新功能架构设计
- 复杂问题分析和解决
- 系统重构规划
- 性能优化方案设计

**要求**: 至少3步思考过程，记录决策依据

### 🔄 3. 四端一致性保证 (强制)

**规则**: 确保小程序、后端、数据库、管理后台完全一致

**检查命令**:
```bash
npm run check:consistency
```

**一致性要求**:
- 📱 小程序 API调用 ↔️ 🖥️ 后端路由定义
- 🖥️ 后端模型 ↔️ 🗄️ 数据库表结构  
- 🗄️ 数据操作 ↔️ 🔧 管理后台功能
- 🔐 权限验证在各端完全同步

### 📏 4. 开发标准遵循 (强制)

**规则**: 所有代码必须符合项目开发标准

**检查命令**:
```bash
npm run check:standards
```

## 🛠️ 开发工作流

### 新功能开发流程

1. **📋 需求分析** (使用Sequential Thinking)
   ```bash
   # 记录思考过程，至少3步分析
   ```

2. **📚 技术调研** (查询Context7)
   ```bash
   # 查询相关最佳实践和规范
   npm run context7:query
   ```

3. **🏗️ 设计开发**
   - 数据库设计 (snake_case命名)
   - API接口设计 (RESTful规范)
   - 小程序页面设计 (TDesign组件)
   - 管理后台设计 (业务逻辑一致)

4. **💻 编码实施**
   ```bash
   # 开发过程中持续检查
   npm run pre-commit
   ```

5. **✅ 质量验证** 
   ```bash
   # 完整验证流程
   npm run validate
   ```

### 提交前检查清单

- [ ] 运行Context7查询确认最佳实践
- [ ] 复杂功能使用Sequential Thinking
- [ ] 通过一致性检查
- [ ] 通过标准检查
- [ ] 代码格式化和Lint检查
- [ ] 单元测试通过

## 🔧 快速命令

### 质量检查
```bash
npm run check:all          # 运行所有检查
npm run check:consistency  # 一致性检查
npm run check:standards    # 标准检查
npm run validate           # 完整验证
```

### 代码质量
```bash
npm run lint               # 代码检查
npm run format             # 代码格式化
npm run pre-commit         # 提交前检查
```

### 开发辅助
```bash
npm run context7:query     # Context7查询提醒
npm run docs:standards     # 查看详细规范
```

## 📚 技术规范速查

### 命名规范
```javascript
// ✅ 正确示例
// 页面目录: kebab-case
pages/health-records/

// 组件: c-kebab-case
"c-health-card": "/components/health-card/health-card"

// 数据库字段: snake_case
user_id, created_at, health_status

// API路由: RESTful
GET /api/v1/health/records
POST /api/v1/health/records
```

### 组件使用
```javascript
// ✅ TDesign组件使用
"usingComponents": {
  "t-button": "tdesign-miniprogram/button/button",
  "c-health-card": "/components/health-card/health-card"
}
```

### API设计
```javascript
// ✅ 标准响应格式
{
  "success": true,
  "data": { /* 实际数据 */ },
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 🚫 禁止事项

- ❌ 跳过Context7最佳实践查询
- ❌ 复杂功能不使用Sequential Thinking  
- ❌ 提交未通过一致性检查的代码
- ❌ 使用不规范的命名方式
- ❌ 忽略代码质量检查结果

## 💡 最佳实践提示

### 开发前
1. 查询Context7相关规范
2. 使用Sequential Thinking分析需求
3. 确认技术方案符合项目标准

### 开发中
1. 遵循命名规范
2. 确保四端一致性
3. 及时运行质量检查

### 提交前
1. 运行完整验证流程
2. 确认所有检查通过
3. 更新相关文档

## 🔗 相关文档

- [详细开发规范](docs/development-standards.md)
- [API设计规范](backend/docs/restful-api-standards.md)
- [部署指南](backend/DEPLOYMENT_GUIDE.md)
- [项目配置](.project-standards.json)

---

**遵循规则，保证质量，确保一致性！** ✨

*最后更新: 2024-01-20*