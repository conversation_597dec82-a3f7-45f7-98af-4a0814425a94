import { z } from "zod";
import { defaultEndpointsFactory } from "express-zod-api";
import {
  authMiddleware,
  tenantMiddleware,
} from "../middleware/auth.middleware";
import {
  GetProductionRecordsQuerySchema,
  CreateProductionRecordSchema,
  UpdateProductionRecordSchema,
  ProductionRecordParamsSchema,
  ProductionRecordSchema,
  ProductionStatsSchema,
  ProductionTrendSchema,
  BatchCreateProductionRecordsSchema,
  BatchUpdateProductionRecordsSchema,
  BatchDeleteProductionRecordsSchema,
  type GetProductionRecordsInput,
  type GetProductionRecordsOutput,
  type GetProductionRecordByIdInput,
  type GetProductionRecordByIdOutput,
  type CreateProductionRecordInput,
  type CreateProductionRecordOutput,
  type UpdateProductionRecordInput,
  type UpdateProductionRecordOutput,
  type DeleteProductionRecordInput,
  type DeleteProductionRecordOutput,
  type GetProductionStatsInput,
  type GetProductionStatsOutput,
  type GetProductionTrendsInput,
  type GetProductionTrendsOutput,
} from "../types/production.types";

// 模拟生产记录数据
const mockProductionRecords: Array<z.infer<typeof ProductionRecordSchema>> = [
  {
    id: 1,
    userId: 1,
    flockId: 1,
    recordType: "daily" as const,
    recordDate: "2024-01-15T00:00:00.000Z",
    eggCount: 85,
    eggWeight: 4.5,
    feedConsumption: 25.5,
    feedCost: 102.0,
    waterConsumption: 80.0,
    temperature: 18.5,
    humidity: 65.0,
    weather: "sunny" as const,
    mortality: 0,
    healthyCount: 95,
    sickCount: 0,
    notes: "正常产蛋，鹅群状态良好",
    recorderId: 1,
    createdAt: "2024-01-15T08:00:00.000Z",
    updatedAt: "2024-01-15T08:00:00.000Z",
    flockName: "一号鹅群",
    batchNumber: "FLOCK-1704067200000",
    recorderName: "测试用户",
  },
  {
    id: 2,
    userId: 1,
    flockId: 1,
    recordType: "feeding" as const,
    recordDate: "2024-01-16T00:00:00.000Z",
    eggCount: 78,
    eggWeight: 4.2,
    feedConsumption: 26.0,
    feedCost: 104.0,
    waterConsumption: 82.0,
    temperature: 16.2,
    humidity: 70.0,
    weather: "cloudy" as const,
    mortality: 1,
    healthyCount: 94,
    sickCount: 0,
    notes: "阴天，产蛋量略有下降",
    recorderId: 1,
    createdAt: "2024-01-16T08:00:00.000Z",
    updatedAt: "2024-01-16T08:00:00.000Z",
    flockName: "一号鹅群",
    batchNumber: "FLOCK-1704067200000",
    recorderName: "测试用户",
  },
  {
    id: 3,
    userId: 1,
    flockId: 2,
    recordType: "collection" as const,
    recordDate: "2024-01-17T00:00:00.000Z",
    eggCount: 62,
    eggWeight: 3.8,
    feedConsumption: 22.0,
    feedCost: 88.0,
    waterConsumption: 68.0,
    temperature: 20.0,
    humidity: 60.0,
    weather: "sunny" as const,
    mortality: 0,
    healthyCount: 78,
    sickCount: 0,
    notes: "二号鹅群产蛋正常",
    recorderId: 1,
    createdAt: "2024-01-17T08:00:00.000Z",
    updatedAt: "2024-01-17T08:00:00.000Z",
    flockName: "二号鹅群",
    batchNumber: "FLOCK-1704153600000",
    recorderName: "测试用户",
  },
];

// 获取生产记录列表端点
export const getProductionRecordsEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "get",
    input: GetProductionRecordsQuerySchema,
    output: z.object({
      items: z.array(ProductionRecordSchema),
      pagination: z.object({
        page: z.number(),
        limit: z.number(),
        total: z.number(),
        pages: z.number(),
        hasNext: z.boolean(),
        hasPrev: z.boolean(),
      }),
    }),
    shortDescription: "获取生产记录列表",
    description: "获取当前租户的生产记录列表，支持分页、搜索和过滤",
    tag: "production",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const {
        page,
        limit,
        flockId,
        recordType,
        startDate,
        endDate,
        weather,
        sortBy,
        sortOrder,
      } = input;

      logger.info(`用户 ${user.id} 查询生产记录列表`, { input, tenantId });

      // 过滤条件
      let filteredRecords = mockProductionRecords.filter((record) => {
        if (flockId && record.flockId !== flockId) return false;
        if (recordType && record.recordType !== recordType) return false;
        if (weather && record.weather !== weather) return false;
        if (startDate && new Date(record.recordDate) < new Date(startDate))
          return false;
        if (endDate && new Date(record.recordDate) > new Date(endDate))
          return false;
        return true;
      });

      // 排序
      filteredRecords.sort((a, b) => {
        const aValue =
          sortBy === "recordDate"
            ? new Date(a.recordDate).getTime()
            : sortBy === "createdAt"
              ? new Date(a.createdAt).getTime()
              : sortBy === "eggCount"
                ? a.eggCount
                : a.feedConsumption;
        const bValue =
          sortBy === "recordDate"
            ? new Date(b.recordDate).getTime()
            : sortBy === "createdAt"
              ? new Date(b.createdAt).getTime()
              : sortBy === "eggCount"
                ? b.eggCount
                : b.feedConsumption;

        if (sortOrder === "DESC") {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });

      // 分页
      const total = filteredRecords.length;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const items = filteredRecords.slice(startIndex, endIndex);

      const pages = Math.ceil(total / limit);
      const hasNext = page < pages;
      const hasPrev = page > 1;

      return {
        items,
        pagination: {
          page,
          limit,
          total,
          pages,
          hasNext,
          hasPrev,
        },
      };
    },
  });

// 创建生产记录端点
export const createProductionRecordEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "post",
    input: CreateProductionRecordSchema,
    output: ProductionRecordSchema,
    shortDescription: "创建生产记录",
    description: "为指定鹅群创建新的生产记录",
    tag: "production",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;

      logger.info(`用户 ${user.id} 创建生产记录`, { input, tenantId });

      const newId = Math.max(...mockProductionRecords.map((r) => r.id)) + 1;
      const now = new Date().toISOString();

      const newRecord = {
        id: newId,
        userId: user.id,
        ...input,
        recorderId: user.id,
        createdAt: now,
        updatedAt: now,
        flockName: input.flockId === 1 ? "一号鹅群" : "二号鹅群",
        batchNumber:
          input.flockId === 1 ? "FLOCK-1704067200000" : "FLOCK-1704153600000",
        recorderName: user.username,
      };

      mockProductionRecords.push(newRecord);

      logger.info(`生产记录创建成功`, { recordId: newId });

      return newRecord;
    },
  });

// 获取生产记录详情端点
export const getProductionRecordByIdEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "get",
    input: ProductionRecordParamsSchema,
    output: ProductionRecordSchema,
    shortDescription: "获取生产记录详情",
    description: "根据ID获取特定生产记录的详细信息",
    tag: "production",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { id } = input;

      logger.info(`用户 ${user.id} 查询生产记录详情`, {
        recordId: id,
        tenantId,
      });

      const record = mockProductionRecords.find((r) => r.id === id);
      if (!record) {
        throw new Error(`生产记录 ${id} 不存在`);
      }

      return record;
    },
  });

// 更新生产记录端点
export const updateProductionRecordEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "patch",
    input: UpdateProductionRecordSchema.extend(
      ProductionRecordParamsSchema.shape,
    ),
    output: ProductionRecordSchema,
    shortDescription: "更新生产记录",
    description: "更新指定生产记录的信息",
    tag: "production",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { id, ...updateData } = input;

      logger.info(`用户 ${user.id} 更新生产记录`, {
        recordId: id,
        updateData,
        tenantId,
      });

      const recordIndex = mockProductionRecords.findIndex((r) => r.id === id);
      if (recordIndex === -1) {
        throw new Error(`生产记录 ${id} 不存在`);
      }

      const updatedRecord = {
        ...mockProductionRecords[recordIndex],
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      mockProductionRecords[recordIndex] = updatedRecord;

      logger.info(`生产记录更新成功`, { recordId: id });

      return updatedRecord;
    },
  });

// 删除生产记录端点
export const deleteProductionRecordEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "delete",
    input: ProductionRecordParamsSchema,
    output: z.object({
      success: z.literal(true),
      message: z.string(),
    }),
    shortDescription: "删除生产记录",
    description: "删除指定的生产记录",
    tag: "production",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { id } = input;

      logger.info(`用户 ${user.id} 删除生产记录`, { recordId: id, tenantId });

      const recordIndex = mockProductionRecords.findIndex((r) => r.id === id);
      if (recordIndex === -1) {
        throw new Error(`生产记录 ${id} 不存在`);
      }

      mockProductionRecords.splice(recordIndex, 1);

      logger.info(`生产记录删除成功`, { recordId: id });

      return {
        success: true as const,
        message: `生产记录 ${id} 已成功删除`,
      };
    },
  });

// 获取生产统计端点
export const getProductionStatsEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "get",
    input: z.object({
      flockId: z.coerce.number().int().positive().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      recordType: z
        .enum(["daily", "feeding", "collection", "cleaning", "maintenance"])
        .optional(),
    }),
    output: ProductionStatsSchema,
    shortDescription: "获取生产统计",
    description: "获取生产记录的统计信息",
    tag: "production",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { flockId, startDate, endDate, recordType } = input;

      logger.info(`用户 ${user.id} 查询生产统计`, { input, tenantId });

      // 过滤记录
      let records = mockProductionRecords;
      if (flockId) {
        records = records.filter((r) => r.flockId === flockId);
      }
      if (recordType) {
        records = records.filter((r) => r.recordType === recordType);
      }
      if (startDate) {
        records = records.filter(
          (r) => new Date(r.recordDate) >= new Date(startDate),
        );
      }
      if (endDate) {
        records = records.filter(
          (r) => new Date(r.recordDate) <= new Date(endDate),
        );
      }

      // 计算统计
      const totalRecords = records.length;
      const totalEggs = records.reduce((sum, r) => sum + r.eggCount, 0);
      const totalFeedConsumption = records.reduce(
        (sum, r) => sum + r.feedConsumption,
        0,
      );
      const totalFeedCost = records.reduce(
        (sum, r) => sum + (r.feedCost || 0),
        0,
      );
      const totalEggWeight = records.reduce(
        (sum, r) => sum + (r.eggWeight || 0),
        0,
      );
      const totalMortality = records.reduce((sum, r) => sum + r.mortality, 0);

      // 计算日期范围
      const dates = records.map((r) => new Date(r.recordDate));
      const minDate =
        dates.length > 0
          ? new Date(Math.min(...dates.map((d) => d.getTime())))
          : new Date();
      const maxDate =
        dates.length > 0
          ? new Date(Math.max(...dates.map((d) => d.getTime())))
          : new Date();
      const days = Math.max(
        1,
        Math.ceil(
          (maxDate.getTime() - minDate.getTime()) / (1000 * 60 * 60 * 24),
        ) + 1,
      );

      const averageEggsPerDay = totalRecords > 0 ? totalEggs / days : 0;
      const averageFeedPerDay =
        totalRecords > 0 ? totalFeedConsumption / days : 0;
      const eggProductionRate =
        totalRecords > 0 ? (totalEggs / (totalRecords * 100)) * 100 : 0; // 假设每群100只鹅
      const feedEfficiency =
        totalFeedConsumption > 0 ? totalEggWeight / totalFeedConsumption : 0;
      const mortalityRate =
        totalRecords > 0 ? (totalMortality / (totalRecords * 100)) * 100 : 0;

      return {
        totalRecords,
        totalEggs,
        totalFeedConsumption,
        totalFeedCost,
        averageEggsPerDay,
        averageFeedPerDay,
        eggProductionRate,
        feedEfficiency,
        mortality: totalMortality,
        mortalityRate,
        period: {
          startDate: startDate || minDate.toISOString().split("T")[0],
          endDate: endDate || maxDate.toISOString().split("T")[0],
          days,
        },
      };
    },
  });

// 获取生产趋势端点
export const getProductionTrendsEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .addMiddleware(tenantMiddleware)
  .build({
    method: "get",
    input: z.object({
      flockId: z.coerce.number().int().positive().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      period: z.enum(["day", "week", "month"]).default("day"),
    }),
    output: z.object({
      trends: z.array(ProductionTrendSchema),
    }),
    shortDescription: "获取生产趋势",
    description: "获取生产记录的趋势数据",
    tag: "production",
    handler: async ({ input, options, logger }) => {
      const { user, tenantId } = options;
      const { flockId, startDate, endDate, period } = input;

      logger.info(`用户 ${user.id} 查询生产趋势`, { input, tenantId });

      // 过滤记录
      let records = mockProductionRecords;
      if (flockId) {
        records = records.filter((r) => r.flockId === flockId);
      }
      if (startDate) {
        records = records.filter(
          (r) => new Date(r.recordDate) >= new Date(startDate),
        );
      }
      if (endDate) {
        records = records.filter(
          (r) => new Date(r.recordDate) <= new Date(endDate),
        );
      }

      // 根据时间段聚合数据
      const trendMap = new Map<
        string,
        {
          eggCount: number;
          eggWeight: number;
          feedConsumption: number;
          feedCost: number;
          mortality: number;
          recordCount: number;
        }
      >();

      records.forEach((record) => {
        const date = new Date(record.recordDate);
        let key: string;

        switch (period) {
          case "week":
            // 获取周的开始日期
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            key = weekStart.toISOString().split("T")[0];
            break;
          case "month":
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
            break;
          default: // day
            key = date.toISOString().split("T")[0];
        }

        if (!trendMap.has(key)) {
          trendMap.set(key, {
            eggCount: 0,
            eggWeight: 0,
            feedConsumption: 0,
            feedCost: 0,
            mortality: 0,
            recordCount: 0,
          });
        }

        const trend = trendMap.get(key)!;
        trend.eggCount += record.eggCount;
        trend.eggWeight += record.eggWeight || 0;
        trend.feedConsumption += record.feedConsumption;
        trend.feedCost += record.feedCost || 0;
        trend.mortality += record.mortality;
        trend.recordCount += 1;
      });

      // 转换为趋势数组
      const trends = Array.from(trendMap.entries())
        .map(([date, data]) => ({
          date,
          eggCount: data.eggCount,
          eggWeight: data.eggWeight,
          feedConsumption: data.feedConsumption,
          feedCost: data.feedCost,
          productionRate:
            data.recordCount > 0
              ? (data.eggCount / (data.recordCount * 100)) * 100
              : 0,
          mortality: data.mortality,
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return { trends };
    },
  });
