import { z } from "zod";

// 健康状态枚举
export const HealthStatusSchema = z.enum([
  "healthy", // 健康
  "sick", // 生病
  "recovering", // 恢复中
  "dead", // 死亡
]);

// 检查类型枚举
export const CheckTypeSchema = z.enum([
  "routine", // 常规检查
  "disease", // 疾病检查
  "vaccination", // 疫苗接种
  "treatment", // 治疗跟踪
  "emergency", // 紧急检查
]);

// 基础健康记录Schema
export const HealthRecordSchema = z.object({
  id: z.number().int().positive(),
  flockId: z.number().int().positive(),
  checkType: CheckTypeSchema,
  checkDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "检查日期格式无效",
  }),
  symptoms: z.string().max(1000).optional(),
  temperature: z.number().min(35).max(45).optional(), // 鹅的正常体温范围
  weight: z.number().positive().optional(),
  result: HealthStatusSchema,
  diagnosis: z.string().max(1000).optional(),
  treatment: z.string().max(1000).optional(),
  medicine: z.string().max(500).optional(),
  dosage: z.string().max(200).optional(),
  notes: z.string().max(1000).optional(),
  checkerId: z.number().int().positive(),
  createdAt: z.string(),
  updatedAt: z.string(),
  // 关联字段
  flockName: z.string().optional(),
  batchNumber: z.string().optional(),
  checkerName: z.string().optional(),
});

// 请求参数Schema
export const HealthRecordParamsSchema = z.object({
  id: z.coerce.number().int().positive(),
});

// 分页查询Schema
export const GetHealthRecordsQuerySchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  flockId: z.coerce.number().int().positive().optional(),
  status: HealthStatusSchema.optional(),
  checkType: CheckTypeSchema.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().max(100).optional(),
  sortBy: z
    .enum(["checkDate", "createdAt", "result", "checkType"])
    .default("checkDate"),
  sortOrder: z.enum(["ASC", "DESC"]).default("DESC"),
});

// 创建健康记录Schema
export const CreateHealthRecordSchema = z.object({
  flockId: z.number().int().positive(),
  checkType: CheckTypeSchema,
  checkDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "检查日期格式无效",
  }),
  symptoms: z.string().max(1000).optional(),
  temperature: z.number().min(35).max(45).optional(),
  weight: z.number().positive().optional(),
  result: HealthStatusSchema,
  diagnosis: z.string().max(1000).optional(),
  treatment: z.string().max(1000).optional(),
  medicine: z.string().max(500).optional(),
  dosage: z.string().max(200).optional(),
  notes: z.string().max(1000).optional(),
});

// 更新健康记录Schema
export const UpdateHealthRecordSchema = z.object({
  checkType: CheckTypeSchema.optional(),
  checkDate: z
    .string()
    .refine((date) => !isNaN(Date.parse(date)), {
      message: "检查日期格式无效",
    })
    .optional(),
  symptoms: z.string().max(1000).optional(),
  temperature: z.number().min(35).max(45).optional(),
  weight: z.number().positive().optional(),
  result: HealthStatusSchema.optional(),
  diagnosis: z.string().max(1000).optional(),
  treatment: z.string().max(1000).optional(),
  medicine: z.string().max(500).optional(),
  dosage: z.string().max(200).optional(),
  notes: z.string().max(1000).optional(),
});

// 健康统计Schema
export const HealthStatsSchema = z.object({
  totalRecords: z.number().int().nonnegative(),
  healthyCount: z.number().int().nonnegative(),
  sickCount: z.number().int().nonnegative(),
  recoveringCount: z.number().int().nonnegative(),
  deadCount: z.number().int().nonnegative(),
  healthyRate: z.number().min(0).max(100),
  lastCheckDate: z.string().optional(),
});

// 健康趋势Schema
export const HealthTrendSchema = z.object({
  date: z.string(),
  totalChecks: z.number().int().nonnegative(),
  healthyCount: z.number().int().nonnegative(),
  sickCount: z.number().int().nonnegative(),
  healthyRate: z.number().min(0).max(100),
});

// API输入输出类型定义
export type GetHealthRecordsInput = z.infer<typeof GetHealthRecordsQuerySchema>;
export type GetHealthRecordsOutput = {
  items: z.infer<typeof HealthRecordSchema>[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
};

export type GetHealthRecordByIdInput = z.infer<typeof HealthRecordParamsSchema>;
export type GetHealthRecordByIdOutput = z.infer<typeof HealthRecordSchema>;

export type CreateHealthRecordInput = z.infer<typeof CreateHealthRecordSchema>;
export type CreateHealthRecordOutput = z.infer<typeof HealthRecordSchema>;

export type UpdateHealthRecordInput = z.infer<typeof UpdateHealthRecordSchema> &
  z.infer<typeof HealthRecordParamsSchema>;
export type UpdateHealthRecordOutput = z.infer<typeof HealthRecordSchema>;

export type DeleteHealthRecordInput = z.infer<typeof HealthRecordParamsSchema>;
export type DeleteHealthRecordOutput = {
  success: true;
  message: string;
};

export type GetHealthStatsInput = {
  flockId?: number;
  startDate?: string;
  endDate?: string;
};
export type GetHealthStatsOutput = {
  stats: z.infer<typeof HealthStatsSchema>;
  trend: z.infer<typeof HealthTrendSchema>[];
};
