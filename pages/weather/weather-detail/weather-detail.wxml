<!-- pages/weather/weather-detail/weather-detail.wxml -->
<view class="weather-detail-container">
  <!-- 当前天气 -->
  <view class="current-weather">
    <view class="location">
      <image class="location-icon" src="/images/icons/location.png" mode="aspectFit"></image>
      <text class="location-text">{{location}}</text>
    </view>
    <view class="weather-main">
      <view class="temperature">{{currentTemp}}°</view>
      <view class="weather-desc">{{weatherDesc}}</view>
    </view>
    <view class="weather-icon">
      <image src="{{weatherIcon}}" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 天气详情 -->
  <view class="weather-details">
    <view class="detail-item">
      <text class="detail-label">湿度</text>
      <text class="detail-value">{{humidity}}%</text>
    </view>
    <view class="detail-item">
      <text class="detail-label">风力</text>
      <text class="detail-value">{{windLevel}}级</text>
    </view>
    <view class="detail-item">
      <text class="detail-label">空气质量</text>
      <text class="detail-value">{{airQuality}}</text>
    </view>
  </view>

  <!-- 未来天气预报 -->
  <view class="forecast">
    <view class="forecast-title">未来3天</view>
    <view class="forecast-list">
      <block wx:for="{{forecast}}" wx:key="date">
        <view class="forecast-item">
          <text class="forecast-date">{{item.date}}</text>
          <image class="forecast-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="forecast-temp">{{item.minTemp}}°/{{item.maxTemp}}°</text>
        </view>
      </block>
    </view>
  </view>

  <!-- 更新时间 -->
  <view class="update-info">
    <text>更新时间：{{updateTime}}</text>
  </view>
</view>
