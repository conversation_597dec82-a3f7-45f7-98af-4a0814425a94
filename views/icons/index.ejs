<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="bi bi-cloud-upload me-2"></i>上传图标
        </button>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="搜索图标..." id="searchInput">
            <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- 图标网格 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-image me-2"></i>图标库
        </h5>
    </div>
    <div class="card-body">
        <div class="row" id="iconGrid">
            <!-- Bootstrap Icons 示例 -->
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-house-door" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">house-door</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-house-door')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('house-door')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">people</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-people')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('people')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-gear" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">gear</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-gear')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('gear')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-heart-pulse" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">heart-pulse</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-heart-pulse')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('heart-pulse')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-clipboard-data" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">clipboard-data</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-clipboard-data')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('clipboard-data')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-egg-fried" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">egg-fried</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-egg-fried')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('egg-fried')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传图标</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm">
                    <div class="mb-3">
                        <label for="iconFile" class="form-label">选择图标文件</label>
                        <input type="file" class="form-control" id="iconFile" accept=".svg,.png,.jpg,.jpeg" multiple>
                        <div class="form-text">支持 SVG、PNG、JPG 格式，最大 2MB</div>
                    </div>
                    <div class="mb-3">
                        <label for="iconName" class="form-label">图标名称</label>
                        <input type="text" class="form-control" id="iconName" placeholder="输入图标名称">
                    </div>
                    <div class="mb-3">
                        <label for="iconCategory" class="form-label">图标分类</label>
                        <select class="form-select" id="iconCategory">
                            <option value="general">通用</option>
                            <option value="navigation">导航</option>
                            <option value="action">操作</option>
                            <option value="status">状态</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadIcon()">上传</button>
            </div>
        </div>
    </div>
</div>

<style>
.icon-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.icon-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.icon-card .btn-group {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.icon-card:hover .btn-group {
    opacity: 1;
}
</style>

<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const iconCards = document.querySelectorAll('.icon-card');
    
    iconCards.forEach(card => {
        const iconName = card.querySelector('.card-text').textContent.toLowerCase();
        const cardContainer = card.closest('.col-md-2, .col-sm-3, .col-4');
        
        if (iconName.includes(searchTerm)) {
            cardContainer.style.display = '';
        } else {
            cardContainer.style.display = 'none';
        }
    });
});

// 复制图标类名
function copyIcon(iconClass) {
    navigator.clipboard.writeText(iconClass).then(() => {
        showMessage(`图标类名 "${iconClass}" 已复制到剪贴板`, 'success');
    }).catch(() => {
        showMessage('复制失败', 'danger');
    });
}

// 删除图标
function deleteIcon(iconName) {
    if (confirm(`确定要删除图标 "${iconName}" 吗？`)) {
        showMessage('删除功能开发中...', 'info');
    }
}

// 上传图标
function uploadIcon() {
    const fileInput = document.getElementById('iconFile');
    const nameInput = document.getElementById('iconName');
    
    if (!fileInput.files.length) {
        showMessage('请选择要上传的图标文件', 'warning');
        return;
    }
    
    if (!nameInput.value.trim()) {
        showMessage('请输入图标名称', 'warning');
        return;
    }
    
    // 这里应该实现实际的上传逻辑
    showMessage('图标上传功能开发中...', 'info');
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
    modal.hide();
}

// 文件选择变化时自动填充名称
document.getElementById('iconFile').addEventListener('change', function() {
    const nameInput = document.getElementById('iconName');
    if (this.files.length > 0 && !nameInput.value) {
        const fileName = this.files[0].name;
        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
        nameInput.value = nameWithoutExt;
    }
});
</script>
