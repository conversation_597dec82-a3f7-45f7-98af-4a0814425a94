<!-- pages/production/environment/environment.wxml -->
<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">环境监控</text>
    <text class="page-subtitle">实时监控养殖环境数据</text>
  </view>

  <!-- 当前环境数据 -->
  <view class="content-section">
    <view class="section-header">
      <text class="section-title">当前环境数据</text>
      <view class="refresh-button" bindtap="loadData">
        <image src="/images/icon_refresh.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="current-data-grid">
      <view class="data-item" bindtap="onViewTrend" data-type="temperature">
        <image class="item-icon" src="/images/icon_temperature.png" mode="aspectFit"></image>
        <view class="item-content">
          <text class="item-value {{currentData.temperature < normalRanges.temperature.min || currentData.temperature > normalRanges.temperature.max ? 'warning' : ''}}">{{currentData.temperature}}°C</text>
          <text class="item-label">温度</text>
          <text class="item-status normal">正常</text>
        </view>
        <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
      </view>
      <view class="data-item" bindtap="onViewTrend" data-type="humidity">
        <image class="item-icon" src="/images/icon_humidity.png" mode="aspectFit"></image>
        <view class="item-content">
          <text class="item-value {{currentData.humidity < normalRanges.humidity.min || currentData.humidity > normalRanges.humidity.max ? 'warning' : ''}}">{{currentData.humidity}}%</text>
          <text class="item-label">湿度</text>
          <text class="item-status normal">正常</text>
        </view>
        <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
      </view>
      <view class="data-item" bindtap="onViewTrend" data-type="pm25">
        <image class="item-icon" src="/images/icon_pm25.png" mode="aspectFit"></image>
        <view class="item-content">
          <text class="item-value {{currentData.pm25 > normalRanges.pm25.max ? 'warning' : ''}}">{{currentData.pm25}}</text>
          <text class="item-label">PM2.5</text>
          <text class="item-status good">良好</text>
        </view>
        <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
      </view>
      <view class="data-item" bindtap="onViewTrend" data-type="light">
        <image class="item-icon" src="/images/icon_light.png" mode="aspectFit"></image>
        <view class="item-content">
          <text class="item-value {{currentData.light < normalRanges.light.min || currentData.light > normalRanges.light.max ? 'warning' : ''}}">{{currentData.light}}lx</text>
          <text class="item-label">光照</text>
          <text class="item-status suitable">适宜</text>
        </view>
        <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="update-time">更新时间: {{currentData.updateTime}}</view>
  </view>

  <!-- 历史趋势图 -->
  <view class="section">
    <view wx:if="{{!selectedTrendType}}" class="chart-placeholder">
      <image src="/images/icon_chart.png" mode="aspectFit"></image>
      <text>点击上方监控项目查看趋势图</text>
    </view>
    <view wx:else class="chart-section">
      <view class="chart-container">
        <!-- 关闭按钮 -->
        <view class="close-chart-btn" bindtap="onCloseChart">
          <text class="close-icon">✕</text>
        </view>
        
        <!-- 使用统一的趋势图组件 -->
        <c-trend-chart
          title="{{selectedTrendType}}趋势图"
          subtitle="实时环境监控数据"
          chart-data="{{chartData}}"
          chart-config="{{chartConfig}}"
          time-ranges="{{timeRanges}}"
          active-time-range="{{activeTimeRange}}"
          show-time-filter="{{true}}"
          legend-data="{{legendData}}"
          show-current-values="{{false}}"
          show-footer="{{true}}"
          update-time="{{currentData.updateTime}}"
          allow-export="{{false}}"
          allow-fullscreen="{{false}}"
          canvas-width="{{350}}"
          canvas-height="{{250}}"
          bind:timeRangeChange="onTimeRangeChange"
          bind:rendered="onChartRendered"
          bind:refresh="onRefreshChart"
        />
      </view>
    </view>
  </view>

  <!-- 报警信息 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">报警信息</text>
      <text class="more" bindtap="onSetThreshold">设置阈值</text>
    </view>
    <view class="alerts-list">
      <block wx:for="{{alerts}}" wx:key="id">
        <view class="alert-item {{item.type}}" bindtap="onViewAlert" data-alert="{{item}}">
          <view class="alert-content">
            <text class="alert-message">{{item.message}}</text>
            <text class="alert-time">{{item.time}}</text>
          </view>
          <view class="alert-status {{item.status === '未处理' ? 'pending' : 'resolved'}}">
            {{item.status}}
          </view>
        </view>
      </block>
      <view wx:if="{{alerts.length === 0}}" class="no-alerts">
        <text>暂无报警信息</text>
      </view>
    </view>
  </view>
</view>