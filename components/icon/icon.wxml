<!-- 智慧养鹅小程序 - 统一图标组件 -->
<view 
  class="icon {{sizeClass}} {{colorClass}} {{stateClass}} {{bgClass}} {{animationClass}} custom-class"
  style="{{customStyle}}"
  bindtap="onIconTap"
  role="{{role}}"
  aria-label="{{ariaLabel}}"
  aria-hidden="{{ariaHidden}}"
>
  <!-- SVG图标 -->
  <image 
    wx:if="{{type === 'svg'}}"
    class="icon-image"
    src="{{iconPath}}"
    mode="aspectFit"
    lazy-load="{{lazyLoad}}"
  />
  
  <!-- 图标字体 --> 
  <text 
    wx:elif="{{type === 'font'}}"
    class="iconfont {{iconClass}}"
  ></text>
  
  <!-- 网络图片 -->
  <image 
    wx:elif="{{type === 'image'}}"
    class="icon-image"
    src="{{src}}"
    mode="aspectFit"
    lazy-load="{{lazyLoad}}"
  />
  
  <!-- Unicode字符 -->
  <text wx:else class="icon-unicode">{{name}}</text>
</view>

<!-- 带标签的图标 -->
<view wx:if="{{label}}" class="icon-with-label {{vertical ? 'vertical' : ''}}">
  <view 
    class="icon {{sizeClass}} {{colorClass}} {{stateClass}} {{bgClass}} {{animationClass}} custom-class"
    style="{{customStyle}}"
    bindtap="onIconTap"
    role="{{role}}"
    aria-label="{{ariaLabel}}"
    aria-hidden="{{ariaHidden}}"
  >
    <!-- 图标内容 -->
    <image 
      wx:if="{{type === 'svg'}}"
      class="icon-image"
      src="{{iconPath}}"
      mode="aspectFit"
      lazy-load="{{lazyLoad}}"
    />
    
    <text 
      wx:elif="{{type === 'font'}}"
      class="iconfont {{iconClass}}"
    ></text>
    
    <image 
      wx:elif="{{type === 'image'}}"
      class="icon-image"
      src="{{src}}"
      mode="aspectFit"
      lazy-load="{{lazyLoad}}"
    />
    
    <text wx:else class="icon-unicode">{{name}}</text>
  </view>
  
  <text class="icon-label">{{label}}</text>
</view>