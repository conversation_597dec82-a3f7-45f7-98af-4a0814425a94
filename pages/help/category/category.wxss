/* 帮助分类详情页样式 */
.category-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 40rpx;
}

/* 分类头部 */
.category-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
  color: #ffffff;
}

.header-content {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.category-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.header-text {
  flex: 1;
}

.category-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.category-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
  display: block;
}

.category-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.8;
}

/* 搜索区域 */
.search-section {
  padding: 20rpx;
}

.search-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 0 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 68rpx;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  font-size: 32rpx;
  color: #999;
  padding: 8rpx;
  margin-left: 8rpx;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 12rpx;
  padding: 0 20rpx 20rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.12);
}

.action-icon {
  font-size: 28rpx;
  margin-bottom: 6rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 通用区域样式 */
.articles-section,
.related-section,
.faq-section {
  background: #ffffff;
  border-radius: 20rpx;
  margin: 0 20rpx 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.sort-indicator {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 文章列表 */
.articles-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.article-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.article-item:active {
  background: #e9ecef;
  transform: scale(0.98);
  border-color: #007AFF;
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.article-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.article-summary {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-meta {
  display: flex;
  gap: 16rpx;
  margin-top: 8rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.meta-icon {
  font-size: 20rpx;
}

.meta-text {
  font-size: 20rpx;
  color: #999;
}

.article-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  margin-left: 16rpx;
}

.difficulty-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.difficulty-easy {
  background: #d4edda;
  color: #155724;
}

.difficulty-medium {
  background: #fff3cd;
  color: #856404;
}

.difficulty-hard {
  background: #f8d7da;
  color: #721c24;
}

.article-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.empty-tips {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 相关分类 */
.related-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.related-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.related-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.related-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.related-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.related-count {
  font-size: 22rpx;
  color: #999;
}

/* FAQ区域 */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.faq-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  transition: background 0.3s ease;
}

.faq-question:active {
  background: #e9ecef;
}

.question-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.arrow-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.arrow-icon.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 20rpx 20rpx;
  border-top: 1rpx solid #e9ecef;
  background: #ffffff;
}

.answer-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 反馈区域 */
.feedback-section {
  margin: 0 20rpx 20rpx;
}

.feedback-card {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  text-align: center;
  color: #ffffff;
}

.feedback-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.feedback-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
  margin-bottom: 32rpx;
  display: block;
}

.feedback-actions {
  display: flex;
  gap: 16rpx;
}

.feedback-btn,
.contact-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  backdrop-filter: blur(10rpx);
}

.feedback-btn:active,
.contact-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .category-stats {
    gap: 20rpx;
  }

  .quick-actions {
    flex-wrap: wrap;
  }

  .action-item {
    min-width: 120rpx;
  }

  .related-grid {
    grid-template-columns: 1fr;
  }

  .feedback-actions {
    flex-direction: column;
  }

  .article-meta {
    flex-wrap: wrap;
    gap: 8rpx 12rpx;
  }
}

/* 动画效果 */
.category-header {
  animation: slideInDown 0.6s ease-out;
}

.search-section,
.quick-actions,
.articles-section,
.related-section,
.faq-section,
.feedback-section {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}