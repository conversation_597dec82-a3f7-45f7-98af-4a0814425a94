/* pages/announcement/announcement-list/announcement-list.wxss */
.announcement-list-container {
  background-color: #f5f6f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 筛选栏 */
.filter-section {
  background-color: #f5f6f8;
  padding: 20rpx;
}

.filter-scroll {
  width: 100%;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
  white-space: nowrap;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 102, 204, 0.1);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  color: #666666;
  white-space: nowrap;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-tab.active {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.3);
}

/* 搜索栏 */
.search-section {
  padding: 0 20rpx 20rpx 20rpx;
  background-color: #f5f6f8;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 102, 204, 0.1);
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.7;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.clear-btn {
  width: 36rpx;
  height: 36rpx;
  margin-left: 16rpx;
  border-radius: 18rpx;
  background-color: rgba(0, 102, 204, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-btn image {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.7;
}

/* 公告列表 */
.announcement-list {
  padding: 20rpx;
}

.announcement-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 102, 204, 0.1);
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

.announcement-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 102, 204, 0.15);
}

.announcement-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
}

.announcement-item.read {
  opacity: 0.85;
}

/* 置顶标识 */
.top-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  background-color: #ff4d4f;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 18rpx;
}

.top-badge image {
  width: 16rpx;
  height: 16rpx;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.announcement-type {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid transparent;
}

.announcement-type.important {
  background: linear-gradient(135deg, #FFF2F0 0%, #FFE6E6 100%);
  color: #FF4D4F;
  border-color: rgba(255, 77, 79, 0.2);
}

.announcement-type.notice {
  background: linear-gradient(135deg, #E6F7FF 0%, #D1EAFF 100%);
  color: #1890FF;
  border-color: rgba(24, 144, 255, 0.2);
}

.announcement-type.policy {
  background: linear-gradient(135deg, #F6FFED 0%, #E6F9E6 100%);
  color: #52C41A;
  border-color: rgba(82, 196, 26, 0.2);
}

.announcement-type.activity {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFE8CC 100%);
  color: #FAAD14;
  border-color: rgba(250, 173, 20, 0.2);
}

.type-icon {
  width: 16rpx;
  height: 16rpx;
}

.announcement-time {
  font-size: 24rpx;
  color: #0066CC;
  font-weight: 500;
  background: rgba(0, 102, 204, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
}

.announcement-content {
  margin-bottom: 25rpx;
}

.announcement-title {
  display: block;
  font-size: 34rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 15rpx;
  line-height: 1.5;
  position: relative;
  padding-left: 20rpx;
}

.announcement-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  border-radius: 3rpx;
}

.announcement-summary {
  display: block;
  font-size: 28rpx;
  color: #555555;
  line-height: 1.7;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.announcement-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding-left: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  background: rgba(0, 102, 204, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 102, 204, 0.1);
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
}

.meta-text {
  font-size: 24rpx;
  color: #0066CC;
  font-weight: 500;
}

/* 未读标识 */
.unread-dot {
  position: absolute;
  top: 24rpx;
  left: 12rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #ff4d4f;
  border-radius: 3rpx;
}

/* 重要标识 */
.important-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
}

.important-badge image {
  width: 100%;
  height: 100%;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 26rpx;
  color: #666666;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40rpx;
  font-size: 24rpx;
  color: #666666;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx;
  font-size: 26rpx;
  color: #0066cc;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 全部已读按钮 */
.mark-all-read {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background-color: #0066cc;
  color: #ffffff;
  border-radius: 24rpx;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.3);
  z-index: 100;
}

.mark-all-read image {
  width: 24rpx;
  height: 24rpx;
}
