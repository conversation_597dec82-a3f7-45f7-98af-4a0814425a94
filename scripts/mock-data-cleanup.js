#!/usr/bin/env node

/**
 * 智慧养鹅SAAS平台 - 模拟数据清理脚本
 * 用于生产环境前清理所有模拟数据和开发用代码
 */

const fs = require('fs');
const path = require('path');

class MockDataCleaner {
  constructor() {
    this.projectRoot = process.cwd();
    this.cleanupResults = {
      filesProcessed: 0,
      mockDataRemoved: 0,
      debugCodeRemoved: 0,
      errors: []
    };
    
    console.log('🧹 智慧养鹅SAAS平台模拟数据清理开始...\n');
  }

  /**
   * 运行清理流程
   */
  async runCleanup() {
    try {
      // 1. 扫描需要清理的文件
      await this.scanAndCleanFiles();
      
      // 2. 生成清理报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 清理过程中出现错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 扫描并清理文件
   */
  async scanAndCleanFiles() {
    const targetDirectories = [
      'pages',
      'components', 
      'utils',
      'backend/controllers',
      'backend/routes'
    ];

    for (const dir of targetDirectories) {
      const dirPath = path.join(this.projectRoot, dir);
      if (fs.existsSync(dirPath)) {
        await this.processDirectory(dirPath);
      }
    }
  }

  /**
   * 处理目录
   */
  async processDirectory(dirPath) {
    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const file of files) {
      const fullPath = path.join(dirPath, file.name);
      
      if (file.isDirectory()) {
        await this.processDirectory(fullPath);
      } else if (file.name.endsWith('.js')) {
        await this.processJSFile(fullPath);
      }
    }
  }

  /**
   * 处理JavaScript文件
   */
  async processJSFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      // 记录原始内容用于比较
      const originalContent = content;
      
      // 1. 移除模拟数据定义
      content = this.removeMockDataDefinitions(content);
      if (content !== originalContent) modified = true;
      
      // 2. 替换模拟数据调用为真实API
      content = this.replaceMockDataCalls(content);
      if (content !== originalContent) modified = true;
      
      // 3. 移除调试代码
      content = this.removeDebugCode(content);
      if (content !== originalContent) modified = true;
      
      // 4. 移除开发用注释
      content = this.removeDevComments(content);
      if (content !== originalContent) modified = true;
      
      // 保存修改后的文件
      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 已清理: ${path.relative(this.projectRoot, filePath)}`);
        this.cleanupResults.filesProcessed++;
      }
      
    } catch (error) {
      this.cleanupResults.errors.push(`${filePath}: ${error.message}`);
      console.error(`❌ 处理文件失败: ${filePath}`);
    }
  }

  /**
   * 移除模拟数据定义
   */
  removeMockDataDefinitions(content) {
    // 移除 const mockXXX = {} 定义
    content = content.replace(/const\s+mock\w+\s*=\s*\{[\s\S]*?\};?\s*/gi, '');
    
    // 移除 getMockXXX 方法
    content = content.replace(/\s*getMock\w+\s*[:\(][\s\S]*?(?=\s*[,}]|\s*$)/gi, '');
    
    // 移除 generateMockXXX 方法
    content = content.replace(/\s*generateMock\w+\s*[:\(][\s\S]*?(?=\s*[,}]|\s*$)/gi, '');
    
    // 移除 createMockXXX 方法
    content = content.replace(/\s*createMock\w+\s*[:\(][\s\S]*?(?=\s*[,}]|\s*$)/gi, '');
    
    this.cleanupResults.mockDataRemoved++;
    return content;
  }

  /**
   * 替换模拟数据调用为真实API
   */
  replaceMockDataCalls(content) {
    // 模拟数据调用模式
    const mockCallPatterns = [
      // this.loadMockData() -> this.loadRealData()
      {
        pattern: /this\.loadMockData\s*\(/gi,
        replacement: 'this.loadRealData('
      },
      // mockData -> realData (变量名)
      {
        pattern: /\bmockData\b/gi,
        replacement: 'realData'
      },
      // fallback到模拟数据的注释
      {
        pattern: /\/\/\s*fallback[到]?模拟数据.*/gi,
        replacement: '// 调用真实API'
      },
      // 模拟数据注释
      {
        pattern: /\/\/\s*模拟数据.*/gi,
        replacement: '// 真实数据'
      }
    ];

    for (const { pattern, replacement } of mockCallPatterns) {
      content = content.replace(pattern, replacement);
    }

    return content;
  }

  /**
   * 移除调试代码
   */
  removeDebugCode(content) {
    // 移除console.log调试语句（保留错误日志）
    content = content.replace(/\s*console\.log\s*\([^)]*\);\s*/gi, '');
    
    // 移除console.warn调试语句
    content = content.replace(/\s*console\.warn\s*\([^)]*\);\s*/gi, '');
    
    // 移除console.debug调试语句
    content = content.replace(/\s*console\.debug\s*\([^)]*\);\s*/gi, '');
    
    // 保留console.error和console.info
    
    this.cleanupResults.debugCodeRemoved++;
    return content;
  }

  /**
   * 移除开发用注释
   */
  removeDevComments(content) {
    // 移除TODO注释
    content = content.replace(/\s*\/\/\s*TODO[：:].*/gi, '');
    
    // 移除FIXME注释
    content = content.replace(/\s*\/\/\s*FIXME[：:].*/gi, '');
    
    // 移除测试用注释
    content = content.replace(/\s*\/\/\s*测试.*/gi, '');
    content = content.replace(/\s*\/\/\s*test.*/gi, '');
    
    // 移除开发用注释
    content = content.replace(/\s*\/\/\s*开发用.*/gi, '');
    content = content.replace(/\s*\/\/\s*开发中.*/gi, '');
    
    return content;
  }

  /**
   * 生成清理报告
   */
  generateReport() {
    console.log('\n📊 模拟数据清理完成报告');
    console.log('================================');
    console.log(`✅ 处理文件数: ${this.cleanupResults.filesProcessed}`);
    console.log(`🗑️  移除模拟数据: ${this.cleanupResults.mockDataRemoved} 处`);
    console.log(`🚮 移除调试代码: ${this.cleanupResults.debugCodeRemoved} 处`);
    
    if (this.cleanupResults.errors.length > 0) {
      console.log(`❌ 错误数量: ${this.cleanupResults.errors.length}`);
      console.log('\n错误详情:');
      this.cleanupResults.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }

    console.log('\n🎯 后续手动处理建议:');
    console.log('1. 检查所有API调用是否指向正确的生产环境地址');
    console.log('2. 确认所有模拟数据已替换为真实API调用');
    console.log('3. 测试所有功能确保正常运行');
    console.log('4. 更新环境配置文件');
    console.log('5. 配置生产环境数据库连接');
    
    console.log('\n🚀 智慧养鹅SAAS平台已准备好生产部署！');
  }
}

// 主要清理目标文件列表
const CLEANUP_TARGETS = {
  // 页面级模拟数据
  pageFiles: [
    'pages/oa/approval/pending/pending.js',
    'pages/oa/approval/history/history.js', 
    'pages/oa/approval/statistics/statistics.js',
    'pages/oa/reimbursement/detail/detail.js',
    'pages/oa/purchase/detail/detail.js',
    'pages/oa/leave/list/list.js',
    'pages/oa/leave/detail/detail.js',
    'pages/oa/notification/notification.js',
    'pages/task/task-list/task-list.js',
    'pages/task/detail/detail.js',
    'pages/inventory/inventory-detail/inventory-detail.js',
    'pages/price/price-detail/price-detail.js',
    'pages/management/staff/staff.js',
    'pages/announcement/announcement-detail/announcement-detail.js',
    'pages/announcement/announcement-list/announcement-list.js'
  ],
  
  // 组件级模拟数据
  componentFiles: [
    // 根据实际情况添加
  ],
  
  // 工具类模拟数据
  utilFiles: [
    // 根据实际情况添加
  ]
};

// 运行清理程序
if (require.main === module) {
  const cleaner = new MockDataCleaner();
  cleaner.runCleanup();
}

module.exports = MockDataCleaner;