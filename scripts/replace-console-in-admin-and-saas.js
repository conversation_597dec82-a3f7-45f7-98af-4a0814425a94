const fs = require('fs');
const path = require('path');

const projectRoot = path.resolve(__dirname, '..');
const backendRoot = path.join(projectRoot, 'backend');
const targets = [
  path.join(backendRoot, 'admin'),
  path.join(backendRoot, 'saas-admin'),
];

const mapMethod = { error: 'error', warn: 'warn', log: 'info' };

function getRequirePathForFile(filePath) {
  const dir = path.dirname(filePath);
  if (filePath.includes(`${path.sep}backend${path.sep}admin${path.sep}`)) {
    const target = path.join(backendRoot, 'admin', 'middleware', 'errorHandler.js');
    return normalizeRequirePath(path.relative(dir, target));
  }
  if (filePath.includes(`${path.sep}backend${path.sep}saas-admin${path.sep}`)) {
    const target = path.join(backendRoot, 'middleware', 'errorHandler.js');
    return normalizeRequirePath(path.relative(dir, target));
  }
  const target = path.join(backendRoot, 'middleware', 'errorHandler.js');
  return normalizeRequirePath(path.relative(dir, target));
}

function normalizeRequirePath(p) {
  let out = p.replace(/\\/g, '/');
  if (out.endsWith('.js')) out = out.slice(0, -3);
  if (!out.startsWith('.')) out = './' + out;
  return out;
}

function transformLine(line, requirePath) {
  const trimmed = line.trimStart();
  if (/^\/\//.test(trimmed)) {
    return line.replace(/(\/\/.*)console\.(log|warn|error)\(/g, (m, prefix, mtd) => `${prefix}Logger.${mapMethod[mtd]}(`);
  }
  let replaced = line;
  let hit = false;
  ['error', 'warn', 'log'].forEach((m) => {
    const re = new RegExp(`console\\.${m}\\(`, 'g');
    if (re.test(replaced)) {
      hit = true;
      replaced = replaced.replace(re, `try { const { Logger } = require('${requirePath}'); Logger.${mapMethod[m]}(`);
    }
  });
  if (hit && !/}\s*catch\s*\(\_\)\s*\}\s*$/.test(replaced)) {
    replaced = replaced.replace(/\r?\n?$/, '') + ' } catch(_) {}\n';
  }
  return replaced;
}

function shouldSkip(filePath) {
  if (/backend\/(admin|saas-admin)\/middleware\/errorHandler\.js$/.test(filePath)) return true;
  if (/\/node_modules\//.test(filePath)) return true;
  if (/\/public\//.test(filePath)) return true;
  return false;
}

function processFile(filePath) {
  if (!filePath.endsWith('.js')) return;
  if (shouldSkip(filePath)) return;
  const requirePath = getRequirePathForFile(filePath);
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split(/\r?\n/);
  const newLines = lines.map((line) => transformLine(line, requirePath));
  const newContent = newLines.join('\n');
  if (newContent !== content) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    process.stdout.write(`Updated: ${path.relative(projectRoot, filePath)}\n`);
  }
}

function walk(dir) {
  for (const entry of fs.readdirSync(dir)) {
    const full = path.join(dir, entry);
    const stat = fs.statSync(full);
    if (stat.isDirectory()) {
      if (entry === 'node_modules' || entry === 'public') continue;
      walk(full);
    } else {
      processFile(full);
    }
  }
}

function main() {
  targets.forEach((t) => {
    if (fs.existsSync(t)) walk(t);
  });
}

main();
