/**
 * 统一验证工具类
 * Unified Validation Helper for data validation
 */

class ValidationHelper {
  /**
   * 验证必填字段
   * @param {Array} fields 必填字段列表
   * @param {Object} data 数据对象
   * @returns {Array} 缺失字段列表
   */
  static validateRequired(fields, data) {
    const missing = [];
    fields.forEach((field) => {
      if (!data[field] && data[field] !== 0 && data[field] !== false) {
        missing.push(field);
      }
    });
    return missing;
  }

  /**
   * 验证数字类型和范围
   * @param {*} value 要验证的值
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {boolean} 是否有效
   */
  static validateNumber(value, min = null, max = null) {
    const num = parseFloat(value);
    if (isNaN(num)) return false;
    if (min !== null && num < min) return false;
    if (max !== null && num > max) return false;
    return true;
  }

  /**
   * 验证整数
   * @param {*} value 要验证的值
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {boolean} 是否有效
   */
  static validateInteger(value, min = null, max = null) {
    const num = parseInt(value);
    if (isNaN(num) || !Number.isInteger(num)) return false;
    if (min !== null && num < min) return false;
    if (max !== null && num > max) return false;
    return true;
  }

  /**
   * 验证字符串长度
   * @param {string} value 要验证的字符串
   * @param {number} min 最小长度
   * @param {number} max 最大长度
   * @returns {boolean} 是否有效
   */
  static validateStringLength(value, min = 0, max = null) {
    if (typeof value !== "string") return false;
    if (value.length < min) return false;
    if (max !== null && value.length > max) return false;
    return true;
  }

  /**
   * 验证邮箱格式
   * @param {string} email 邮箱地址
   * @returns {boolean} 是否有效
   */
  static validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证手机号格式（中国大陆）
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  static validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 验证日期格式
   * @param {string} dateString 日期字符串
   * @returns {boolean} 是否有效
   */
  static validateDate(dateString) {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * 验证日期范围
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   * @returns {boolean} 是否有效
   */
  static validateDateRange(startDate, endDate) {
    if (!startDate || !endDate) return true;
    const start = new Date(startDate);
    const end = new Date(endDate);
    return start <= end;
  }

  /**
   * 验证URL格式
   * @param {string} url URL字符串
   * @returns {boolean} 是否有效
   */
  static validateUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证正则表达式
   * @param {string} value 要验证的值
   * @param {RegExp} pattern 正则表达式
   * @returns {boolean} 是否匹配
   */
  static validatePattern(value, pattern) {
    return pattern.test(value);
  }

  /**
   * 验证枚举值
   * @param {*} value 要验证的值
   * @param {Array} allowedValues 允许的值列表
   * @returns {boolean} 是否有效
   */
  static validateEnum(value, allowedValues) {
    return allowedValues.includes(value);
  }

  /**
   * 验证文件类型
   * @param {string} filename 文件名
   * @param {Array} allowedExtensions 允许的扩展名
   * @returns {boolean} 是否有效
   */
  static validateFileType(filename, allowedExtensions) {
    const extension = filename.split(".").pop().toLowerCase();
    return allowedExtensions.includes(extension);
  }

  /**
   * 验证文件大小
   * @param {number} fileSize 文件大小（字节）
   * @param {number} maxSize 最大大小（字节）
   * @returns {boolean} 是否有效
   */
  static validateFileSize(fileSize, maxSize) {
    return fileSize <= maxSize;
  }

  /**
   * 验证密码强度
   * @param {string} password 密码
   * @param {Object} options 选项
   * @returns {Object} 验证结果
   */
  static validatePassword(password, options = {}) {
    const {
      minLength = 6,
      requireUppercase = false,
      requireLowercase = false,
      requireNumbers = false,
      requireSpecialChars = false,
    } = options;

    const result = {
      isValid: true,
      errors: [],
    };

    if (password.length < minLength) {
      result.isValid = false;
      result.errors.push(`密码长度不能少于${minLength}位`);
    }

    if (requireUppercase && !/[A-Z]/.test(password)) {
      result.isValid = false;
      result.errors.push("密码必须包含大写字母");
    }

    if (requireLowercase && !/[a-z]/.test(password)) {
      result.isValid = false;
      result.errors.push("密码必须包含小写字母");
    }

    if (requireNumbers && !/\d/.test(password)) {
      result.isValid = false;
      result.errors.push("密码必须包含数字");
    }

    if (
      requireSpecialChars &&
      !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
    ) {
      result.isValid = false;
      result.errors.push("密码必须包含特殊字符");
    }

    return result;
  }

  /**
   * 批量验证
   * @param {Object} data 要验证的数据
   * @param {Object} rules 验证规则
   * @returns {Object} 验证结果
   */
  static validateBatch(data, rules) {
    const errors = {};
    let isValid = true;

    for (const [field, fieldRules] of Object.entries(rules)) {
      const value = data[field];
      const fieldErrors = [];

      for (const rule of fieldRules) {
        const { type, options = {}, message } = rule;

        let ruleResult = true;
        switch (type) {
          case "required":
            ruleResult = value !== undefined && value !== null && value !== "";
            break;
          case "string":
            ruleResult = this.validateStringLength(
              value,
              options.min,
              options.max,
            );
            break;
          case "number":
            ruleResult = this.validateNumber(value, options.min, options.max);
            break;
          case "email":
            ruleResult = this.validateEmail(value);
            break;
          case "phone":
            ruleResult = this.validatePhone(value);
            break;
          case "date":
            ruleResult = this.validateDate(value);
            break;
          case "enum":
            ruleResult = this.validateEnum(value, options.values);
            break;
          case "pattern":
            ruleResult = this.validatePattern(value, options.regex);
            break;
        }

        if (!ruleResult) {
          fieldErrors.push(message || `${field}验证失败`);
          isValid = false;
        }
      }

      if (fieldErrors.length > 0) {
        errors[field] = fieldErrors;
      }
    }

    return {
      isValid,
      errors,
    };
  }
}

module.exports = ValidationHelper;
