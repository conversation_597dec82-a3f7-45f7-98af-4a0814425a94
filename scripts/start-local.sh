#!/bin/bash

# 智慧养鹅SAAS平台本地启动脚本（针对无root密码的MySQL）

set -e

GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "🚀 启动智慧养鹅SAAS平台（本地版）"
echo "=================================="

# 1. 创建saas_admin用户（如果不存在）
echo -e "${BLUE}📋 设置数据库用户...${NC}"
mysql -u root << 'EOF' 2>/dev/null || echo "用户可能已存在"
CREATE USER IF NOT EXISTS 'saas_admin'@'localhost' IDENTIFIED BY 'SmartGoose2024!';
CREATE DATABASE IF NOT EXISTS smart_goose_saas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON smart_goose_saas.* TO 'saas_admin'@'localhost';
GRANT CREATE, DROP, ALTER, INDEX, REFERENCES ON *.* TO 'saas_admin'@'localhost';
FLUSH PRIVILEGES;
EOF

# 2. 初始化数据库结构
echo -e "${BLUE}🗄️ 初始化数据库结构...${NC}"
mysql -u saas_admin -pSmartGoose2024! smart_goose_saas < database/saas-platform-init.sql 2>/dev/null || echo "数据库结构可能已存在"

# 3. 创建测试数据
echo -e "${BLUE}🧪 创建测试数据...${NC}"
mysql -u saas_admin -pSmartGoose2024! smart_goose_saas << 'EOF'
INSERT IGNORE INTO tenants (companyName, tenantCode, status, subscriptionPlan, maxUsers, maxFlocks, contactEmail, contactPhone, contactName, createdAt, updatedAt) VALUES 
('Demo农场', 'demo', 'active', 'premium', 100, 50, '<EMAIL>', '138-0000-0000', '张经理', NOW(), NOW()),
('测试农场', 'test', 'active', 'standard', 50, 20, '<EMAIL>', '139-0000-0000', '李经理', NOW(), NOW()),
('体验农场', 'trial', 'active', 'trial', 10, 5, '<EMAIL>', '137-0000-0000', '王经理', NOW(), NOW());

INSERT IGNORE INTO tenant_miniprogram_configs (tenantId, appId, appSecret, version, status, apiEndpoint, themeConfig, featuresConfig, createdAt, updatedAt) VALUES 
(1, 'wxdemo123456789', 'demo_app_secret_here', '1.0.0', 'development', 'http://localhost:3001/api/v1/tenant', '{"primaryColor": "#28a745", "logo": "/images/demo-logo.png"}', '{"ai_diagnosis": true, "shop_module": true, "data_export": true, "advanced_analytics": true}', NOW(), NOW()),
(2, 'wxtest123456789', 'test_app_secret_here', '1.0.0', 'development', 'http://localhost:3001/api/v1/tenant', '{"primaryColor": "#17a2b8", "logo": "/images/test-logo.png"}', '{"ai_diagnosis": true, "shop_module": false, "data_export": true, "advanced_analytics": false}', NOW(), NOW()),
(3, 'wxtrial123456789', 'trial_app_secret_here', '1.0.0', 'development', 'http://localhost:3001/api/v1/tenant', '{"primaryColor": "#6c757d", "logo": "/images/trial-logo.png"}', '{"ai_diagnosis": false, "shop_module": false, "data_export": false, "advanced_analytics": false}', NOW(), NOW());

INSERT IGNORE INTO platform_admins (username, email, password, role, status, createdAt, updatedAt) VALUES 
('admin', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', 'active', NOW(), NOW());
EOF

echo -e "${GREEN}✅ 数据库设置完成${NC}"

# 4. 创建环境配置
echo -e "${BLUE}📝 创建环境配置...${NC}"
cat > .env.local << 'EOF'
NODE_ENV=development
PORT=3001

SAAS_DB_HOST=localhost
SAAS_DB_PORT=3306
SAAS_DB_USER=saas_admin
SAAS_DB_PASSWORD=SmartGoose2024!
SAAS_DB_NAME=smart_goose_saas

TENANT_DB_HOST=localhost
TENANT_DB_PORT=3306
TENANT_DB_USER=saas_admin
TENANT_DB_PASSWORD=SmartGoose2024!

JWT_SECRET=smart_goose_saas_jwt_secret_key_2024_make_it_very_secure_and_long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

WECHAT_API_URL=https://api.weixin.qq.com

SAAS_ADMIN_PORT=3002
SAAS_ADMIN_SESSION_SECRET=smart_goose_admin_session_secret_2024

LOG_LEVEL=debug
ENABLE_CORS=true
ENABLE_DETAILED_LOGS=true
EOF

echo -e "${GREEN}✅ 环境配置完成${NC}"

# 5. 安装依赖
echo -e "${BLUE}📦 安装依赖...${NC}"
npm install --silent

# 6. 启动服务
echo -e "${BLUE}🚀 启动服务...${NC}"
echo ""
echo -e "${GREEN}✅ 准备完成！启动服务中...${NC}"
echo ""
echo "🌐 访问地址："
echo "   📱 主应用API: http://localhost:3001"
echo "   🛠️  SAAS管理后台: http://localhost:3002"
echo ""
echo "🧪 测试账号："
echo "   管理后台: admin / password"
echo "   租户代码: demo, test, trial"
echo ""
echo "🛑 停止服务: Ctrl+C"
echo ""

# 启动服务
echo "启动主应用..."
NODE_ENV=development node backend/app.js &
MAIN_PID=$!

echo "启动管理后台..."
NODE_ENV=development node backend/saas-admin/app.js &
ADMIN_PID=$!

# 等待服务启动
sleep 3

# 测试服务
if curl -s http://localhost:3001/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 主应用启动成功${NC}"
else
    echo -e "${YELLOW}⚠️ 主应用可能未完全启动${NC}"
fi

if curl -s http://localhost:3002 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 管理后台启动成功${NC}"
else
    echo -e "${YELLOW}⚠️ 管理后台可能未完全启动${NC}"
fi

echo ""
echo -e "${GREEN}🎉 所有服务已启动！${NC}"

# 等待用户中断
trap "echo ''; echo '🛑 正在停止服务...'; kill $MAIN_PID $ADMIN_PID 2>/dev/null; exit 0" INT
wait