// pages/orders/orders.js - 重新设计的订单列表页面逻辑
Page({
  data: {
    currentStatus: 'all',
    statusTabs: [
      { status: 'all', name: '全部', count: 0 },
      { status: 'pending', name: '待付款', count: 0 },
      { status: 'paid', name: '待发货', count: 0 },
      { status: 'shipped', name: '待收货', count: 0 },
      { status: 'completed', name: '已完成', count: 0 }
    ],
    orders: [],
    filteredOrders: [],
    loading: false,
    page: 1,
    pageSize: 10,
    hasMore: true,
    
    // 评价相关
    showRatingModal: false,
    currentOrder: null,
    ratingData: {
      rating: 5,
      comment: ''
    }
  },

  onLoad(options) {
    // 如果有传入状态参数，设置当前状态
    if (options.status) {
      this.setData({
        currentStatus: options.status
      });
    }
    this.loadOrders(true);
  },

  // 加载订单数据
  loadOrders(reset = false) {
    if (reset) {
      this.setData({ page: 1, hasMore: true, orders: [] });
    }
    // TODO: 连接真实API，移除模拟数据
    // this.loadOrdersFromAPI(); // 待实现真实API时启用
    const all = [
      {
        id: '1',
        orderNumber: 'GO202401150001',
        createTime: '2024-01-15 10:30',
        status: 'pending',
        statusText: '待付款',
        totalQuantity: 2,
        totalPrice: '299.98',
        goods: [
          {
            id: '1',
            name: '优质鹅饲料',
            price: '99.99',
            quantity: 1,
            image: '/images/icons/goods1.png'
          },
          {
            id: '2',
            name: '疫苗套装',
            price: '199.99',
            quantity: 1,
            image: '/images/icons/goods2.png'
          }
        ]
      },
      {
        id: '2',
        orderNumber: 'GO202401140001',
        createTime: '2024-01-14 15:20',
        status: 'shipped',
        statusText: '待收货',
        totalQuantity: 1,
        totalPrice: '599.00',
        goods: [
          {
            id: '3',
            name: '自动喂食器',
            price: '599.00',
            quantity: 1,
            image: '/images/icons/goods3.png'
          }
        ]
      },
      {
        id: '3',
        orderNumber: 'GO202401130001',
        createTime: '2024-01-13 09:15',
        status: 'completed',
        statusText: '已完成',
        totalQuantity: 3,
        totalPrice: '450.00',
        goods: [
          {
            id: '4',
            name: '鹅舍清洁剂',
            price: '150.00',
            quantity: 3,
            image: '/images/icons/goods4.png'
          }
        ]
      }
    ];

    // 简单分页模拟
    const start = (this.data.page - 1) * this.data.pageSize;
    const next = all.slice(start, start + this.data.pageSize);
    const orders = this.data.orders.concat(next);
    const hasMore = start + this.data.pageSize < all.length;

    // 计算各状态订单数量（仅首次重置时计算）
    let statusTabs = this.data.statusTabs;
    if (reset) {
      const statusCounts = {
        all: all.length,
        pending: all.filter(o => o.status === 'pending').length,
        paid: all.filter(o => o.status === 'paid').length,
        shipped: all.filter(o => o.status === 'shipped').length,
        completed: all.filter(o => o.status === 'completed').length
      };
      statusTabs = statusTabs.map(tab => ({ ...tab, count: statusCounts[tab.status] }));
    }

    this.setData({ orders, statusTabs, hasMore });
    this.filterOrders();
  },

  // 筛选订单
  filterOrders() {
    const { orders, currentStatus } = this.data;
    let filteredOrders = orders;

    if (currentStatus !== 'all') {
      filteredOrders = orders.filter(order => order.status === currentStatus);
    }

    this.setData({ filteredOrders });
  },

  // 切换状态
  onStatusChange(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ currentStatus: status });
    this.filterOrders();
  },

  // 查看订单详情
  onOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`
    });
  },

  // 取消订单
  onCancelOrder(e) {
    e && e.stopPropagation && e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === orderId);

    if (!order || order.status !== 'pending') {
      wx.showToast({
        title: '当前状态不能取消',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认取消订单',
      content: '取消后订单无法恢复，确定要取消这个订单吗？',
      confirmText: '确认取消',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder(orderId);
        }
      }
    });
  },

  // 执行取消订单
  cancelOrder(orderId) {
    wx.showLoading({
      title: '正在取消订单...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();

      // 更新本地订单状态
      const orders = this.data.orders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: 'cancelled',
            statusText: '已取消'
          };
        }
        return order;
      });

      // 重新计算状态数量
      const statusCounts = {
        all: orders.length,
        pending: orders.filter(o => o.status === 'pending').length,
        paid: orders.filter(o => o.status === 'paid').length,
        shipped: orders.filter(o => o.status === 'shipped').length,
        completed: orders.filter(o => o.status === 'completed').length
      };

      const statusTabs = this.data.statusTabs.map(tab => ({
        ...tab,
        count: statusCounts[tab.status]
      }));

      this.setData({
        orders,
        statusTabs
      });

      this.filterOrders();

      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      });
    }, 1500);
  },

  // 立即付款
  onPayOrder(e) {
    e && e.stopPropagation && e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === orderId);

    if (!order || order.status !== 'pending') {
      wx.showToast({
        title: '订单状态异常',
        icon: 'none'
      });
      return;
    }

    // 直接跳转到支付页面，而不是订单详情页面
    wx.navigateTo({
      url: `/pages/payment/payment?orderId=${orderId}`
    });
  },

  // 查看物流
  onTrackOrder(e) {
    e && e.stopPropagation && e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/logistics/logistics?orderId=${orderId}`
    });
  },

  // 确认收货
  onConfirmOrder(e) {
    e && e.stopPropagation && e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里调用确认收货的API
          wx.showToast({
            title: '确认收货成功',
            icon: 'success'
          });
          this.loadOrders();
        }
      }
    });
  },

  // 去购物
  onGoShop() {
    wx.switchTab({
      url: '/pages/shop/shop'
    });
  },

  // 联系客服
  onContactService(e) {
    e && e.stopPropagation && e.stopPropagation();
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 评价订单
  onReviewOrder(e) {
    e && e.stopPropagation && e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === orderId);
    
    if (order) {
      // 实现评价功能
      this.showRatingModal(order);
    }
  },

  // 再次购买
  onRebuyOrder(e) {
    e && e.stopPropagation && e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === orderId);

    if (order && order.goods.length > 0) {
      // 将商品添加到购物车
      wx.showToast({
        title: '商品已加入购物车',
        icon: 'success'
      });

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/shop/cart'
        });
      }, 1500);
    }
  },

  // 删除订单
  onDeleteOrder(e) {
    e && e.stopPropagation && e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === orderId);

    if (!order || (order.status !== 'cancelled' && order.status !== 'completed')) {
      wx.showToast({
        title: '当前状态不能删除',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '删除订单',
      content: '删除后订单记录将无法找回，确定要删除吗？',
      confirmText: '确认删除',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.deleteOrder(orderId);
        }
      }
    });
  },

  // 执行删除订单
  deleteOrder(orderId) {
    wx.showLoading({
      title: '正在删除...'
    });

    setTimeout(() => {
      wx.hideLoading();

      // 从订单列表中移除
      const orders = this.data.orders.filter(order => order.id !== orderId);

      // 重新计算状态数量
      const statusCounts = {
        all: orders.length,
        pending: orders.filter(o => o.status === 'pending').length,
        paid: orders.filter(o => o.status === 'paid').length,
        shipped: orders.filter(o => o.status === 'shipped').length,
        completed: orders.filter(o => o.status === 'completed').length
      };

      const statusTabs = this.data.statusTabs.map(tab => ({
        ...tab,
        count: statusCounts[tab.status]
      }));

      this.setData({
        orders,
        statusTabs
      });

      this.filterOrders();

      wx.showToast({
        title: '订单已删除',
        icon: 'success'
      });
    }, 1000);
  },

  // ==================== 评价功能 ====================
  
  // 显示评价模态框
  showRatingModal: function(order) {
    this.setData({
      showRatingModal: true,
      currentOrder: order,
      ratingData: {
        rating: 5,
        comment: '',
        images: [],
        anonymous: false
      }
    });
  },

  // 关闭评价模态框
  onCloseRatingModal: function() {
    this.setData({
      showRatingModal: false,
      currentOrder: null,
      ratingData: {
        rating: 5,
        comment: '',
        images: [],
        anonymous: false
      }
    });
  },

  // 评分变化
  onRatingChange: function(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({
      'ratingData.rating': rating
    });
  },

  // 评价内容输入
  onCommentInput: function(e) {
    const comment = e.detail.value;
    this.setData({
      'ratingData.comment': comment
    });
  },

  // 匿名评价切换
  onAnonymousChange: function(e) {
    const anonymous = e.detail.value;
    this.setData({
      'ratingData.anonymous': anonymous
    });
  },



  // 添加下拉刷新
  onPullDownRefresh() {
    this.loadOrders(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 触底加载更多
  onReachBottom() {
    if (!this.data.hasMore) return;
    this.setData({ page: this.data.page + 1 });
    this.loadOrders();
  },

  // 提交评价
  onSubmitRating: function() {
    const { currentOrder, ratingData } = this.data;
    
    if (!ratingData.comment.trim()) {
      wx.showToast({
        title: '请填写评价内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交评价中...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      
      // 更新订单状态为已评价
      const orders = this.data.orders.map(order => {
        if (order.id === currentOrder.id) {
          return {
            ...order,
            hasReview: true,
            reviewData: {
              ...ratingData,
              reviewTime: new Date().toISOString()
            }
          };
        }
        return order;
      });

      this.setData({ orders });
      this.filterOrders();

      wx.showToast({
        title: '评价提交成功',
        icon: 'success'
      });

      this.onCloseRatingModal();
    }, 2000);
  },

  // 查看评价
  onViewRating: function(e) {
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === orderId);
    
    if (order && order.reviewData) {
      const reviewData = order.reviewData;
      const ratingText = '★'.repeat(reviewData.rating) + '☆'.repeat(5 - reviewData.rating);
      
      wx.showModal({
        title: '我的评价',
        content: `评分：${ratingText}\n评价：${reviewData.comment}\n时间：${new Date(reviewData.reviewTime).toLocaleString()}`,
        showCancel: false
      });
    }
  }
});
