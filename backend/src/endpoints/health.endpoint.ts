import { z } from "zod";
import { defaultEndpointsFactory } from "express-zod-api";

// 健康检查响应模式
const healthResponseSchema = z.object({
  status: z.literal("ok"),
  timestamp: z.string(),
  version: z.string(),
  environment: z.string(),
  services: z.object({
    database: z.object({
      status: z.enum(["connected", "disconnected", "error"]),
      responseTime: z.number().optional(),
    }),
    redis: z
      .object({
        status: z.enum(["connected", "disconnected", "error"]),
        responseTime: z.number().optional(),
      })
      .optional(),
    external_apis: z
      .object({
        weather_api: z.enum(["available", "unavailable"]).optional(),
        ai_service: z.enum(["available", "unavailable"]).optional(),
      })
      .optional(),
  }),
  uptime: z.number(),
  memory: z.object({
    used: z.number(),
    total: z.number(),
    percentage: z.number(),
  }),
});

// 健康检查端点
export const healthEndpoint = defaultEndpointsFactory.build({
  method: "get",
  input: z.object({}),
  output: healthResponseSchema,
  shortDescription: "健康检查",
  description: "检查服务器和相关服务的健康状态",
  tag: "system",
  handler: async ({ logger }) => {
    const startTime = Date.now();

    // 检查数据库连接
    let dbStatus: "connected" | "disconnected" | "error" = "connected";
    let dbResponseTime: number | undefined;

    try {
      // 这里应该实际检查数据库连接
      // const dbStart = Date.now();
      // await sequelize.authenticate();
      // dbResponseTime = Date.now() - dbStart;
      dbResponseTime = Math.random() * 50; // 模拟响应时间
    } catch (error) {
      logger.error("数据库健康检查失败:", error);
      dbStatus = "error";
    }

    // 获取内存使用情况
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;

    // 获取进程运行时间
    const uptime = process.uptime();

    return {
      status: "ok" as const,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || "1.0.0",
      environment: process.env.NODE_ENV || "development",
      services: {
        database: {
          status: dbStatus,
          responseTime: dbResponseTime,
        },
        redis: {
          status: "disconnected" as const,
        },
        external_apis: {
          weather_api: "available" as const,
          ai_service: "available" as const,
        },
      },
      uptime: Math.round(uptime),
      memory: {
        used: Math.round(usedMemory / 1024 / 1024), // MB
        total: Math.round(totalMemory / 1024 / 1024), // MB
        percentage: Math.round((usedMemory / totalMemory) * 100),
      },
    };
  },
});
