import { z } from "zod";
import { Middleware } from "express-zod-api";
import jwt from "jsonwebtoken";

// 用户信息类型定义
export const UserSchema = z.object({
  id: z.number(),
  username: z.string(),
  email: z.string().email(),
  role: z.enum(["admin", "manager", "user"]),
  permissions: z.array(z.string()),
  tenantId: z.number().optional(),
});

export type User = z.infer<typeof UserSchema>;

// 认证错误类
class UnauthorizedError extends Error {
  name = "UnauthorizedError";
  constructor(message: string = "未授权访问") {
    super(message);
  }
}

// JWT验证函数
const verifyToken = (token: string): Promise<User> => {
  return new Promise((resolve, reject) => {
    const secret = process.env.JWT_SECRET || "your-secret-key";

    jwt.verify(token, secret, (err, decoded: any) => {
      if (err) {
        reject(new UnauthorizedError("无效的访问令牌"));
        return;
      }

      try {
        // 验证decoded数据结构
        const user = UserSchema.parse(decoded);
        resolve(user);
      } catch (parseError) {
        reject(new UnauthorizedError("令牌数据格式无效"));
      }
    });
  });
};

// 基础认证中间件
export const authMiddleware = new Middleware({
  input: z.object({}),
  handler: async ({ request }) => {
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedError("缺少认证令牌");
    }

    const token = authHeader.replace("Bearer ", "");
    if (!token) {
      throw new UnauthorizedError("令牌格式无效");
    }

    try {
      const user = await verifyToken(token);
      return { user };
    } catch (error) {
      throw error instanceof UnauthorizedError
        ? error
        : new UnauthorizedError("认证失败");
    }
  },
});

// 权限检查中间件工厂
export const createPermissionMiddleware = (requiredPermissions: string[]) => {
  return new Middleware({
    input: z.object({}),
    handler: async ({ options }) => {
      const { user } = options as { user: User };

      if (!user) {
        throw new UnauthorizedError("用户信息缺失");
      }

      const hasPermission = requiredPermissions.every((permission) =>
        user.permissions.includes(permission),
      );

      if (!hasPermission) {
        throw new Error("权限不足");
      }

      return { user };
    },
  });
};

// 角色检查中间件工厂
export const createRoleMiddleware = (allowedRoles: User["role"][]) => {
  return new Middleware({
    input: z.object({}),
    handler: async ({ options }) => {
      const { user } = options as { user: User };

      if (!user) {
        throw new UnauthorizedError("用户信息缺失");
      }

      if (!allowedRoles.includes(user.role)) {
        throw new Error("角色权限不足");
      }

      return { user };
    },
  });
};

// 租户检查中间件
export const tenantMiddleware = new Middleware({
  input: z.object({}),
  handler: async ({ request, options }) => {
    const { user } = options as { user: User };
    const tenantCode = request.headers["x-tenant-code"] as string;

    if (!tenantCode && user?.role !== "admin") {
      throw new UnauthorizedError("缺少租户标识");
    }

    // 这里可以添加租户验证逻辑
    // 比如检查用户是否有权限访问该租户的数据

    return { user, tenantCode };
  },
});

// 可选认证中间件（不强制要求认证）
export const optionalAuthMiddleware = new Middleware({
  input: z.object({}),
  handler: async ({ request }) => {
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      return { user: null };
    }

    const token = authHeader.replace("Bearer ", "");
    if (!token) {
      return { user: null };
    }

    try {
      const user = await verifyToken(token);
      return { user };
    } catch (error) {
      return { user: null };
    }
  },
});
