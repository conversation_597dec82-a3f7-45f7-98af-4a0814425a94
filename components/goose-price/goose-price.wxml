<!-- components/goose-price/goose-price.wxml -->
<view class="price-container" bindtap="onPriceContainerTap">
  <view class="price-header">
    <text class="title-text">今日鹅价</text>
    <view class="update-time">{{updateTime}}</view>
  </view>

  <view class="price-tabs">
    <view class="tab-item {{activeTab === 'gosling' ? 'active' : ''}}" 
          data-tab="gosling" bindtap="onTabChange">
      <text class="tab-text">鹅苗价格</text>
    </view>
    <view class="tab-item {{activeTab === 'adult' ? 'active' : ''}}" 
          data-tab="adult" bindtap="onTabChange">
      <text class="tab-text">成鹅价格</text>
    </view>
  </view>

  <view class="price-content">
    <!-- 鹅苗价格 -->
    <view wx:if="{{activeTab === 'gosling'}}" class="price-list">
      <block wx:for="{{goslingPrices}}" wx:key="breed">
        <view class="price-item">
          <view class="breed-info">
            <text class="breed-name">{{item.breed}}</text>
            <text class="breed-desc">{{item.description}}</text>
          </view>
          <view class="price-info">
            <text class="current-price">¥{{item.price}}</text>
            <text class="price-unit">/只</text>
            <view class="price-change {{item.change >= 0 ? 'up' : 'down'}}">
              <text class="change-value">{{item.change >= 0 ? '+' : ''}}{{item.change}}%</text>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 成鹅价格 -->
    <view wx:if="{{activeTab === 'adult'}}" class="price-list">
      <block wx:for="{{adultPrices}}" wx:key="breed">
        <view class="price-item">
          <view class="breed-info">
            <text class="breed-name">{{item.breed}}</text>
            <text class="breed-desc">{{item.description}}</text>
          </view>
          <view class="price-info">
            <text class="current-price">¥{{item.price}}</text>
            <text class="price-unit">/斤</text>
            <view class="price-change {{item.change >= 0 ? 'up' : 'down'}}">
              <text class="change-value">{{item.change >= 0 ? '+' : ''}}{{item.change}}%</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</view>
