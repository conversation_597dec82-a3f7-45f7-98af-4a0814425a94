/* pages/oa/approval/history/history.wxss */

/* 页面容器 */
.history-container {
  min-height: 100vh;
  background: #f2f2f7;
  padding-bottom: 120rpx;
}

/* ==================== 页面头部 ==================== */
.page-header {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  padding: 32rpx 24rpx 40rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.2;
}

.header-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.summary-text {
  font-size: 24rpx;
  opacity: 0.8;
}

.success-rate {
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
}

.rate-text {
  font-size: 22rpx;
  font-weight: 600;
}

/* ==================== 统计概览 ==================== */
.stats-overview {
  background: white;
  margin: -20rpx 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.1);
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  min-width: 140rpx;
}

.stat-card:active {
  background: #f8f9fa;
  transform: scale(0.95);
}

.stat-card.approved {
  background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
  border: 2rpx solid #34c759;
}

.stat-card.rejected {
  background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%);
  border: 2rpx solid #ff3b30;
}

.stat-card.monthly {
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
  border: 2rpx solid #007aff;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 6rpx;
}

.stat-card.approved .stat-number {
  color: #34c759;
}

.stat-card.rejected .stat-number {
  color: #ff3b30;
}

.stat-card.monthly .stat-number {
  color: #007aff;
}

.stat-label {
  font-size: 24rpx;
  color: #8e8e93;
  font-weight: 500;
}

/* ==================== 搜索区域 ==================== */
.search-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin: 0 24rpx 24rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.search-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #8e8e93;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #1d1d1f;
}

.clear-search {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #8e8e93;
  border-radius: 50%;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.filter-toggle {
  width: 72rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.filter-icon {
  font-size: 28rpx;
}

/* ==================== 筛选面板 ==================== */
.filter-panel {
  background: white;
  margin: 0 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section:last-of-type {
  margin-bottom: 24rpx;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 16rpx;
  display: block;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-option {
  padding: 12rpx 20rpx;
  background: #f2f2f7;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #1d1d1f;
  transition: all 0.2s ease;
}

.filter-option.active {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.filter-option:active {
  transform: scale(0.95);
}

.filter-actions {
  display: flex;
  gap: 16rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.reset-btn {
  background: #f2f2f7;
  color: #1d1d1f;
}

.apply-btn {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* ==================== 审批历史列表 ==================== */
.history-list {
  margin: 0 24rpx;
}

.history-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.history-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 24rpx 16rpx;
}

.title-section {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1d1d1f;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-info {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.approval-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  border: 2rpx solid transparent;
}

.approval-status.approved {
  background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);
}

.approval-status.rejected {
  background: linear-gradient(135deg, #ff3b30 0%, #ff2d55 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}

.approval-status.inline {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
}

.status-text {
  font-size: inherit;
}

.business-tag {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  border: 1rpx solid #e5e5e7;
  background: #f8f9fa;
  color: #1d1d1f;
}

.business-tag.reimbursement {
  background: #e8f5e8;
  color: #2d7d2d;
  border-color: #34c759;
}

.business-tag.leave {
  background: #fff4e6;
  color: #cc7a00;
  border-color: #ff9500;
}

.business-tag.purchase {
  background: #f0f0ff;
  color: #4a4a8a;
  border-color: #5856d6;
}

.approval-time {
  font-size: 24rpx;
  color: #8e8e93;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.item-content {
  padding: 0 24rpx 16rpx;
}

.applicant-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.applicant-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.info-icon {
  font-size: 24rpx;
  width: 32rpx;
}

.applicant-name {
  font-size: 26rpx;
  color: #1d1d1f;
  font-weight: 500;
}

.applicant-dept {
  font-size: 24rpx;
  color: #8e8e93;
  margin-left: auto;
}

.amount-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.amount-text {
  font-size: 26rpx;
  color: #34c759;
  font-weight: 600;
}

.remarks-section {
  background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
  padding: 20rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #e5f3ff;
}

.remarks-label {
  font-size: 24rpx;
  color: #8e8e93;
  margin-bottom: 8rpx;
  display: block;
}

.remarks-content {
  font-size: 26rpx;
  color: #1d1d1f;
  line-height: 1.5;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #f2f2f7;
}

.submit-time {
  font-size: 24rpx;
  color: #8e8e93;
}

.duration-info {
  padding: 6rpx 16rpx;
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.2);
}

.duration-text {
  font-size: 22rpx;
  color: white;
  font-weight: 600;
}

/* ==================== 状态组件 ==================== */
.loading-state,
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
  border-width: 2rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #8e8e93;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #1d1d1f;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #8e8e93;
  margin-bottom: 40rpx;
}

.refresh-btn {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #8e8e93;
  font-size: 24rpx;
}

/* ==================== 详情查看弹窗 ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.detail-modal {
  width: 100%;
  max-width: 640rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f2f2f7;
  flex-shrink: 0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f2f2f7;
  border-radius: 50%;
  font-size: 32rpx;
  color: #8e8e93;
}

.modal-content {
  padding: 24rpx 32rpx;
  overflow-y: auto;
  flex: 1;
}

.detail-section {
  margin-bottom: 32rpx;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 16rpx;
  display: block;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #f2f2f7;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item.full {
  flex-direction: column;
  align-items: stretch;
}

.detail-label {
  min-width: 120rpx;
  font-size: 26rpx;
  color: #8e8e93;
  font-weight: 500;
  margin-right: 16rpx;
}

.detail-item.full .detail-label {
  margin-bottom: 8rpx;
}

.detail-value {
  flex: 1;
  font-size: 26rpx;
  color: #1d1d1f;
  line-height: 1.4;
}

.detail-value.amount {
  color: #34c759;
  font-weight: 600;
}

.detail-value.remarks {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f2f2f7;
  flex-shrink: 0;
}

.close-btn,
.view-detail-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.close-btn {
  background: #f2f2f7;
  color: #1d1d1f;
}

.view-detail-btn {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* ==================== 动画 ==================== */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .stats-row {
    flex-direction: column;
    gap: 16rpx;
  }

  .stat-card {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }

  .filter-options {
    flex-direction: column;
  }

  .filter-option {
    text-align: center;
  }

  .applicant-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
}

/* ==================== 小程序特有样式 ==================== */
button::after {
  border: none;
}

.stat-card:active,
.filter-option:active,
.history-item:active {
  opacity: 0.8;
}