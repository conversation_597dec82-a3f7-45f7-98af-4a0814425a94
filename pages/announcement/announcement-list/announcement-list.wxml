<!-- pages/announcement/announcement-list/announcement-list.wxml -->
<view class="announcement-list-container">
  <!-- 筛选栏 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-tabs">
        <view class="filter-tab {{activeFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="onFilterChange">
          全部 ({{announcementCounts.all}})
        </view>
        <view class="filter-tab {{activeFilter === 'important' ? 'active' : ''}}" data-filter="important" bindtap="onFilterChange">
          重要 ({{announcementCounts.important}})
        </view>
        <view class="filter-tab {{activeFilter === 'notice' ? 'active' : ''}}" data-filter="notice" bindtap="onFilterChange">
          通知 ({{announcementCounts.notice}})
        </view>
        <view class="filter-tab {{activeFilter === 'policy' ? 'active' : ''}}" data-filter="policy" bindtap="onFilterChange">
          政策 ({{announcementCounts.policy}})
        </view>
        <view class="filter-tab {{activeFilter === 'activity' ? 'active' : ''}}" data-filter="activity" bindtap="onFilterChange">
          活动 ({{announcementCounts.activity}})
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
              <image class="search-icon" src="/images/icons/search.png" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索公告..." value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
      <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">
                  <image src="/images/icons/close.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 公告列表 -->
  <view class="announcement-list">
    <block wx:for="{{filteredAnnouncements}}" wx:key="id">
      <view class="announcement-item {{item.isRead ? 'read' : 'unread'}}" bindtap="onAnnouncementTap" data-id="{{item.id}}">
        <!-- 置顶标识 -->
        <view wx:if="{{item.isTop}}" class="top-badge">
          <image src="/images/icons/pin.png" mode="aspectFit"></image>
          <text>置顶</text>
        </view>

        <view class="announcement-header">
          <view class="announcement-type {{item.type}}">
            <image class="type-icon" src="/images/icons/{{item.type}}.png" mode="aspectFit"></image>
            <text class="type-text">{{item.typeText}}</text>
          </view>
          <view class="announcement-time">{{item.publishTime}}</view>
        </view>

        <view class="announcement-content">
          <text class="announcement-title">{{item.title}}</text>
          <text class="announcement-summary">{{item.summary}}</text>
          
          <view class="announcement-meta">
            <view class="meta-item">
              <image class="meta-icon" src="/images/icons/user.png" mode="aspectFit"></image>
              <text class="meta-text">{{item.publisher}}</text>
            </view>
            <view class="meta-item">
              <image class="meta-icon" src="/images/icons/eye.png" mode="aspectFit"></image>
              <text class="meta-text">{{item.viewCount}}次浏览</text>
            </view>
            <view wx:if="{{item.attachmentCount > 0}}" class="meta-item">
              <image class="meta-icon" src="/images/icons/attachment.png" mode="aspectFit"></image>
              <text class="meta-text">{{item.attachmentCount}}个附件</text>
            </view>
          </view>
        </view>

        <!-- 未读标识 -->
        <view wx:if="{{!item.isRead}}" class="unread-dot"></view>

        <!-- 重要标识 -->
        <view wx:if="{{item.isImportant}}" class="important-badge">
          <image src="/images/icons/important.png" mode="aspectFit"></image>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{filteredAnnouncements.length === 0 && !loading}}" class="empty-state">
      <image class="empty-icon" src="/images/icon_announcement.png" mode="aspectFit"></image>
      <text class="empty-text">{{searchKeyword ? '没有找到相关公告' : '暂无公告'}}</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <text>加载中...</text>
    </view>
  </view>

  <!-- 底部加载更多 -->
  <view wx:if="{{hasMore && !loading}}" class="load-more" bindtap="onLoadMore">
    <text>点击加载更多</text>
  </view>

  <!-- 全部已读按钮 -->
  <view wx:if="{{unreadCount > 0}}" class="mark-all-read" bindtap="onMarkAllRead">
    <image src="/images/icons/check_all.png" mode="aspectFit"></image>
    <text>全部已读 ({{unreadCount}})</text>
  </view>
</view>
