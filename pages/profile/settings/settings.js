// pages/profile/settings/settings.js
Page({
  data: {
    // 用户设置
    userSettings: {
      name: '张三',
      phone: '138****8888',
      email: '<PERSON><PERSON><PERSON>@example.com',
      farmName: '某某养鹅场'
    },
    
    // 系统设置
    systemSettings: {
      notification: true,
      sound: true,
      autoSync: true,
      theme: 'light' // light, dark
    },
    
    // 通知设置
    notificationSettings: {
      healthAlert: true,
      environmentAlert: true,
      materialAlert: true,
      financeAlert: true
    },

    // 密码修改相关
    showPasswordModal: false,
    passwordData: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  },

  onLoad: function (options) {
    // 页面加载时获取设置信息
    this.loadSettings();
  },

  onShow: function () {
    // 页面显示
  },

  // 加载设置信息
  loadSettings: function () {
    // 模拟从本地存储获取设置信息
    const settings = wx.getStorageSync('settings');
    if (settings) {
      this.setData({
        userSettings: settings.userSettings || this.data.userSettings,
        systemSettings: settings.systemSettings || this.data.systemSettings,
        notificationSettings: settings.notificationSettings || this.data.notificationSettings
      });
    }
  },

  // 保存设置
  saveSettings: function () {
    // 保存到本地存储
    wx.setStorageSync('settings', {
      userSettings: this.data.userSettings,
      systemSettings: this.data.systemSettings,
      notificationSettings: this.data.notificationSettings
    });
    
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
  },

  // 用户信息输入
  onUserInfoInput: function (e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`userSettings.${field}`]: value
    });
  },

  // 系统设置切换
  onSystemSettingChange: function (e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`systemSettings.${field}`]: value
    });
  },

  // 通知设置切换
  onNotificationSettingChange: function (e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`notificationSettings.${field}`]: value
    });
  },

  // 修改密码
  onChangePassword: function () {
    this.setData({
      showPasswordModal: true,
      passwordData: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    });
  },

  // 关闭密码修改模态框
  onClosePasswordModal: function () {
    this.setData({
      showPasswordModal: false,
      passwordData: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    });
  },

  // 密码输入
  onPasswordInput: function (e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`passwordData.${field}`]: value
    });
  },

  // 确认修改密码
  onConfirmChangePassword: function () {
    const { oldPassword, newPassword, confirmPassword } = this.data.passwordData;
    
    // 验证输入
    if (!oldPassword || !newPassword || !confirmPassword) {
      wx.showToast({
        title: '请填写所有密码字段',
        icon: 'none'
      });
      return;
    }
    
    if (newPassword.length < 6) {
      wx.showToast({
        title: '新密码长度至少6位',
        icon: 'none'
      });
      return;
    }
    
    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载
    wx.showLoading({
      title: '修改中...'
    });
    
    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟验证旧密码
      const storedPassword = wx.getStorageSync('userPassword') || 'password123';
      if (oldPassword !== storedPassword) {
        wx.showToast({
          title: '原密码错误',
          icon: 'none'
        });
        return;
      }
      
      // 保存新密码
      wx.setStorageSync('userPassword', newPassword);
      
      wx.showToast({
        title: '密码修改成功',
        icon: 'success'
      });
      
      this.onClosePasswordModal();
    }, 1500);
  },

  // 清除缓存
  onClearCache: function () {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除缓存吗？这将删除所有本地数据。',
      success: (res) => {
        if (res.confirm) {
          // 清除缓存逻辑
          wx.clearStorage({
            success: () => {
              wx.showToast({
                title: '缓存清除成功',
                icon: 'success'
              });
            },
            fail: () => {
              wx.showToast({
                title: '清除失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 帮助中心
  onHelp: function () {
    wx.navigateTo({
      url: '/pages/profile/help/help'
    });
  },

  // 保存设置
  onSaveSettings: function () {
    this.saveSettings();
  }
});