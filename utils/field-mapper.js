// utils/field-mapper.js
// 字段映射工具 - 统一前后端字段命名

/**
 * 字段映射配置
 */
const FIELD_MAPPINGS = {
  // 用户相关字段映射
  user: {
    // 中文 -> 英文
    '用户名': 'username',
    '密码': 'password', 
    '邮箱': 'email',
    '角色': 'role',
    '姓名': 'name',
    '农场名称': 'farmName',
    '电话': 'phone',
    // 英文 -> 中文 (用于向后兼容)
    'username': '用户名',
    'password': '密码',
    'email': '邮箱',
    'role': '角色',
    'name': '姓名',
    'farmName': '农场名称',
    'phone': '电话'
  },

  // 健康记录相关字段映射
  healthRecord: {
    // 中文 -> 英文
    '鹅群编号': 'gooseId',
    '健康状态': 'healthStatus',
    '症状': 'symptoms',
    '诊断': 'diagnosis',
    '治疗': 'treatment',
    '状态': 'status',
    // 英文 -> 中文
    'gooseId': '鹅群编号',
    'healthStatus': '健康状态',
    'symptoms': '症状',
    'diagnosis': '诊断',
    'treatment': '治疗',
    'status': '状态'
  },

  // 生产记录相关字段映射
  productionRecord: {
    // 中文 -> 英文
    '产蛋数量': 'eggCount',
    '饲料消耗': 'feedConsumption',
    '温度': 'temperature',
    '湿度': 'humidity',
    '备注': 'notes',
    '记录日期': 'recordedDate',
    '批次号': 'batchNumber',
    '平均体重': 'averageWeight',
    '饲料配比': 'feedRatio',
    // 英文 -> 中文
    'eggCount': '产蛋数量',
    'feedConsumption': '饲料消耗',
    'temperature': '温度',
    'humidity': '湿度',
    'notes': '备注',
    'recordedDate': '记录日期',
    'batchNumber': '批次号',
    'averageWeight': '平均体重',
    'feedRatio': '饲料配比'
  },

  // 库存管理相关字段映射
  inventory: {
    // 中文 -> 英文
    '物料名称': 'materialName',
    '物料类型': 'materialType',
    '当前库存': 'currentStock',
    '最小库存': 'minStock',
    '最大库存': 'maxStock',
    '单位': 'unit',
    '供应商': 'supplier',
    // 英文 -> 中文
    'materialName': '物料名称',
    'materialType': '物料类型',
    'currentStock': '当前库存',
    'minStock': '最小库存',
    'maxStock': '最大库存',
    'unit': '单位',
    'supplier': '供应商'
  },

  // 公告相关字段映射
  announcement: {
    // 中文 -> 英文
    '标题': 'title',
    '内容': 'content',
    '类型': 'type',
    '优先级': 'priority',
    '发布状态': 'publishStatus',
    '发布时间': 'publishTime',
    // 英文 -> 中文
    'title': '标题',
    'content': '内容',
    'type': '类型',
    'priority': '优先级',
    'publishStatus': '发布状态',
    'publishTime': '发布时间'
  },

  // 知识库相关字段映射
  knowledgeBase: {
    // 中文 -> 英文
    '问题': 'question',
    '答案': 'answer',
    '分类': 'category',
    '标签': 'tags',
    '浏览次数': 'viewCount',
    // 英文 -> 中文
    'question': '问题',
    'answer': '答案',
    'category': '分类',
    'tags': '标签',
    'viewCount': '浏览次数'
  }
};

/**
 * 字段映射工具类
 */
class FieldMapper {
  /**
   * 将对象的字段名从中文转换为英文
   * @param {Object} data - 要转换的数据对象
   * @param {string} type - 数据类型 (user, healthRecord, productionRecord, etc.)
   * @returns {Object} 转换后的对象
   */
  static chineseToEnglish(data, type) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const mapping = FIELD_MAPPINGS[type];
    if (!mapping) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('未找到字段映射配置', type); } catch(_) {}
      return data;
    }

    const result = {};
    
    for (const [key, value] of Object.entries(data)) {
      const englishKey = mapping[key] || key;
      result[englishKey] = value;
    }

    return result;
  }

  /**
   * 将对象的字段名从英文转换为中文
   * @param {Object} data - 要转换的数据对象
   * @param {string} type - 数据类型
   * @returns {Object} 转换后的对象
   */
  static englishToChinese(data, type) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const mapping = FIELD_MAPPINGS[type];
    if (!mapping) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('未找到字段映射配置', type); } catch(_) {}
      return data;
    }

    const result = {};
    
    for (const [key, value] of Object.entries(data)) {
      const chineseKey = mapping[key] || key;
      result[chineseKey] = value;
    }

    return result;
  }

  /**
   * 批量转换数组中的对象字段名
   * @param {Array} dataArray - 数据数组
   * @param {string} type - 数据类型
   * @param {string} direction - 转换方向 ('toEnglish' | 'toChinese')
   * @returns {Array} 转换后的数组
   */
  static convertArray(dataArray, type, direction = 'toEnglish') {
    if (!Array.isArray(dataArray)) {
      return dataArray;
    }

    return dataArray.map(item => {
      if (direction === 'toEnglish') {
        return this.chineseToEnglish(item, type);
      } else {
        return this.englishToChinese(item, type);
      }
    });
  }

  /**
   * 获取字段的英文名称
   * @param {string} chineseField - 中文字段名
   * @param {string} type - 数据类型
   * @returns {string} 英文字段名
   */
  static getEnglishField(chineseField, type) {
    const mapping = FIELD_MAPPINGS[type];
    return mapping ? (mapping[chineseField] || chineseField) : chineseField;
  }

  /**
   * 获取字段的中文名称
   * @param {string} englishField - 英文字段名
   * @param {string} type - 数据类型
   * @returns {string} 中文字段名
   */
  static getChineseField(englishField, type) {
    const mapping = FIELD_MAPPINGS[type];
    return mapping ? (mapping[englishField] || englishField) : englishField;
  }

  /**
   * 验证字段映射配置
   * @param {string} type - 数据类型
   * @returns {boolean} 是否有效
   */
  static validateMapping(type) {
    return FIELD_MAPPINGS.hasOwnProperty(type);
  }

  /**
   * 获取所有支持的数据类型
   * @returns {Array} 支持的数据类型列表
   */
  static getSupportedTypes() {
    return Object.keys(FIELD_MAPPINGS);
  }
}

/**
 * 便捷方法 - 健康记录字段转换
 */
const healthRecordMapper = {
  toEnglish: (data) => FieldMapper.chineseToEnglish(data, 'healthRecord'),
  toChinese: (data) => FieldMapper.englishToChinese(data, 'healthRecord')
};

/**
 * 便捷方法 - 生产记录字段转换
 */
const productionRecordMapper = {
  toEnglish: (data) => FieldMapper.chineseToEnglish(data, 'productionRecord'),
  toChinese: (data) => FieldMapper.englishToChinese(data, 'productionRecord')
};

/**
 * 便捷方法 - 用户字段转换
 */
const userMapper = {
  toEnglish: (data) => FieldMapper.chineseToEnglish(data, 'user'),
  toChinese: (data) => FieldMapper.englishToChinese(data, 'user')
};

// 导出
module.exports = {
  FieldMapper,
  healthRecordMapper,
  productionRecordMapper,
  userMapper,
  FIELD_MAPPINGS
};
