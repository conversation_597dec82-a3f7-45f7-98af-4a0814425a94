-- 智慧养鹅系统 - 数据库性能优化脚本
-- 为新创建的数据表添加合适的索引，优化查询性能

USE zhihuiyange_local;

-- ================================
-- 1. 任务管理表索引优化
-- ================================

-- 复合索引：用户ID + 状态 + 截止时间（用于任务列表查询）
CREATE INDEX idx_tasks_user_status_deadline ON tasks(user_id, status, deadline);

-- 复合索引：用户ID + 分类 + 创建时间（用于分类筛选）
CREATE INDEX idx_tasks_user_category_created ON tasks(user_id, category, created_at);

-- 复合索引：用户ID + 优先级 + 状态（用于优先级筛选）
CREATE INDEX idx_tasks_user_priority_status ON tasks(user_id, priority, status);

-- 全文索引：标题和描述（用于搜索功能）
ALTER TABLE tasks ADD FULLTEXT(title, description);

-- ================================
-- 2. 公告管理表索引优化
-- ================================

-- 复合索引：类型 + 发布时间 + 置顶状态（用于公告列表查询）
CREATE INDEX idx_announcements_type_publish_top ON announcements(type, publish_time, is_top);

-- 复合索引：重要标识 + 发布时间（用于重要公告筛选）
CREATE INDEX idx_announcements_important_publish ON announcements(is_important, publish_time);

-- 索引：发布者（用于发布者筛选）
CREATE INDEX idx_announcements_publisher ON announcements(publisher);

-- 全文索引：标题、摘要和内容（用于搜索功能）
ALTER TABLE announcements ADD FULLTEXT(title, summary, content);

-- ================================
-- 3. 公告已读记录表索引优化
-- ================================

-- 复合索引：用户ID + 阅读时间（用于已读记录查询）
CREATE INDEX idx_announcement_reads_user_time ON announcement_reads(user_id, read_at);

-- 复合索引：公告ID + 用户ID（用于检查已读状态）
CREATE INDEX idx_announcement_reads_announcement_user ON announcement_reads(announcement_id, user_id);

-- ================================
-- 4. 价格记录表索引优化
-- ================================

-- 复合索引：品种名称 + 类型 + 记录日期（用于价格趋势查询）
CREATE INDEX idx_price_records_breed_type_date ON price_records(breed_name, breed_type, record_date);

-- 复合索引：市场地点 + 记录日期（用于市场价格查询）
CREATE INDEX idx_price_records_market_date ON price_records(market_location, record_date);

-- 复合索引：记录日期 + 价格（用于价格排序）
CREATE INDEX idx_price_records_date_price ON price_records(record_date, price);

-- 索引：变化率（用于价格变化分析）
CREATE INDEX idx_price_records_change_rate ON price_records(change_rate);

-- ================================
-- 5. 价格订阅表索引优化
-- ================================

-- 复合索引：用户ID + 激活状态（用于用户订阅查询）
CREATE INDEX idx_price_subscriptions_user_active ON price_subscriptions(user_id, is_active);

-- 复合索引：品种名称 + 类型 + 激活状态（用于订阅匹配）
CREATE INDEX idx_price_subscriptions_breed_active ON price_subscriptions(breed_name, breed_type, is_active);

-- 索引：阈值价格（用于价格比较）
CREATE INDEX idx_price_subscriptions_threshold ON price_subscriptions(threshold_price);

-- ================================
-- 6. 库存记录表索引优化
-- ================================

-- 复合索引：用户ID + 记录日期 + 品种（用于库存查询）
CREATE INDEX idx_inventory_records_user_date_breed ON inventory_records(user_id, record_date, breed_name);

-- 复合索引：用户ID + 区域 + 健康状态（用于区域健康统计）
CREATE INDEX idx_inventory_records_user_area_health ON inventory_records(user_id, area_location, health_status);

-- 复合索引：用户ID + 年龄分类 + 记录日期（用于年龄分类统计）
CREATE INDEX idx_inventory_records_user_age_date ON inventory_records(user_id, age_category, record_date);

-- 复合索引：品种名称 + 健康状态 + 记录日期（用于品种健康分析）
CREATE INDEX idx_inventory_records_breed_health_date ON inventory_records(breed_name, health_status, record_date);

-- ================================
-- 7. 库存预警表索引优化
-- ================================

-- 复合索引：用户ID + 解决状态 + 预警级别（用于预警列表查询）
CREATE INDEX idx_inventory_alerts_user_resolved_level ON inventory_alerts(user_id, is_resolved, level);

-- 复合索引：预警类型 + 创建时间（用于预警类型统计）
CREATE INDEX idx_inventory_alerts_type_created ON inventory_alerts(alert_type, created_at);

-- 索引：区域位置（用于区域预警查询）
CREATE INDEX idx_inventory_alerts_area ON inventory_alerts(area_location);

-- ================================
-- 8. 物料管理表索引优化
-- ================================

-- 复合索引：用户ID + 分类 + 状态（用于物料列表查询）
CREATE INDEX idx_materials_user_category_status ON materials(user_id, category, status);

-- 复合索引：用户ID + 当前库存 + 安全库存（用于库存预警）
CREATE INDEX idx_materials_user_stock_safety ON materials(user_id, current_stock, safety_stock);

-- 索引：供应商（用于供应商筛选）
CREATE INDEX idx_materials_supplier ON materials(supplier);

-- 全文索引：名称和规格（用于搜索功能）
ALTER TABLE materials ADD FULLTEXT(name, specification);

-- ================================
-- 9. 物料库存记录表索引优化
-- ================================

-- 复合索引：物料ID + 类型 + 创建时间（用于出入库记录查询）
CREATE INDEX idx_material_stock_records_material_type_created ON material_stock_records(material_id, type, created_at);

-- 复合索引：操作员ID + 创建时间（用于操作员记录查询）
CREATE INDEX idx_material_stock_records_operator_created ON material_stock_records(operator_id, created_at);

-- 索引：批次号（用于批次追踪）
CREATE INDEX idx_material_stock_records_batch ON material_stock_records(batch_number);

-- 索引：过期日期（用于过期预警）
CREATE INDEX idx_material_stock_records_expiry ON material_stock_records(expiry_date);

-- ================================
-- 10. 物料预警设置表索引优化
-- ================================

-- 复合索引：物料ID + 预警类型 + 启用状态（用于预警设置查询）
CREATE INDEX idx_material_alerts_material_type_enabled ON material_alerts(material_id, alert_type, is_enabled);

-- 索引：阈值（用于预警触发）
CREATE INDEX idx_material_alerts_threshold ON material_alerts(threshold_value);

-- ================================
-- 查询性能分析和优化建议
-- ================================

-- 分析表统计信息
ANALYZE TABLE tasks, announcements, announcement_reads, price_records, price_subscriptions;
ANALYZE TABLE inventory_records, inventory_alerts, materials, material_stock_records, material_alerts;

-- 显示索引使用情况（可用于性能监控）
-- SELECT 
--   TABLE_NAME,
--   INDEX_NAME,
--   CARDINALITY,
--   SUB_PART,
--   PACKED,
--   NULLABLE,
--   INDEX_TYPE
-- FROM INFORMATION_SCHEMA.STATISTICS 
-- WHERE TABLE_SCHEMA = 'zhihuiyange_local'
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- ================================
-- 性能优化配置建议
-- ================================

-- 设置查询缓存（如果MySQL版本支持）
-- SET GLOBAL query_cache_size = 268435456; -- 256MB
-- SET GLOBAL query_cache_type = ON;

-- 设置InnoDB缓冲池大小（建议设置为可用内存的70-80%）
-- SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB

-- 启用慢查询日志
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 2; -- 记录执行时间超过2秒的查询

-- ================================
-- 定期维护脚本
-- ================================

-- 定期优化表（建议每周执行一次）
-- OPTIMIZE TABLE tasks, announcements, announcement_reads, price_records, price_subscriptions;
-- OPTIMIZE TABLE inventory_records, inventory_alerts, materials, material_stock_records, material_alerts;

-- 定期分析表（建议每天执行一次）
-- ANALYZE TABLE tasks, announcements, announcement_reads, price_records, price_subscriptions;
-- ANALYZE TABLE inventory_records, inventory_alerts, materials, material_stock_records, material_alerts;
