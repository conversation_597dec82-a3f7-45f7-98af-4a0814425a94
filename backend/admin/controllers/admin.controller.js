const axios = require("axios");

class AdminController {
  constructor() {
    this.apiBaseUrl = "http://localhost:3000/api/v1";
  }

  // 获取仪表板数据
  async getDashboardData(req, res) {
    try {
      // 获取统计数据
      const [usersResponse, productionResponse, healthResponse] =
        await Promise.all([
          this.makeApiRequest("/auth/users"),
          this.makeApiRequest("/production-records"),
          this.makeApiRequest("/health/records"),
        ]);

      const dashboardData = {
        totalUsers: usersResponse?.data?.length || 0,
        totalProduction: productionResponse?.data?.records?.length || 0,
        totalHealth: healthResponse?.data?.length || 0,
        recentActivities: [],
      };

      res.render("dashboard", {
        title: "仪表板 - 智慧养鹅管理系统",
        page: "dashboard",
        data: dashboardData,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取仪表板数据失败:", error); } catch(_) {}

      res.render("dashboard", {
        title: "仪表板 - 智慧养鹅管理系统",
        page: "dashboard",
        data: {
          totalUsers: 0,
          totalProduction: 0,
          totalHealth: 0,
          recentActivities: [],
        },
      });
    }
  }

  // 获取用户列表
  async getUsersList(req, res) {
    try {
      const response = await this.makeApiRequest("/auth/users");
      const users = response?.data || [];

      res.render("users", {
        title: "用户管理 - 智慧养鹅管理系统",
        page: "users",
        users: users,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取用户列表失败:", error); } catch(_) {}

      res.render("users", {
        title: "用户管理 - 智慧养鹅管理系统",
        page: "users",
        users: [],
      });
    }
  }

  // 获取生产记录列表
  async getProductionList(req, res) {
    try {
      const response = await this.makeApiRequest("/production-records");
      const records = response?.data?.records || [];

      res.render("production", {
        title: "生产管理 - 智慧养鹅管理系统",
        page: "production",
        records: records,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取生产记录失败:", error); } catch(_) {}

      res.render("production", {
        title: "生产管理 - 智慧养鹅管理系统",
        page: "production",
        records: [],
      });
    }
  }

  // 获取健康记录列表
  async getHealthList(req, res) {
    try {
      const response = await this.makeApiRequest("/health/records");
      const records = response?.data || [];

      res.render("health", {
        title: "健康管理 - 智慧养鹅管理系统",
        page: "health",
        records: records,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取健康记录失败:", error); } catch(_) {}

      res.render("health", {
        title: "健康管理 - 智慧养鹅管理系统",
        page: "health",
        records: [],
      });
    }
  }

  // 创建用户
  async createUser(req, res) {
    try {
      const userData = req.body;
      const response = await this.makeApiRequest(
        "/auth/register",
        "POST",
        userData,
      );

      res.json({
        success: true,
        message: "用户创建成功",
        data: response.data,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建用户失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "创建用户失败",
        error: error.message,
      });
    }
  }

  // 更新用户
  async updateUser(req, res) {
    try {
      const userId = req.params.id;
      const userData = req.body;
      const response = await this.makeApiRequest(
        `/auth/users/${userId}`,
        "PUT",
        userData,
      );

      res.json({
        success: true,
        message: "用户更新成功",
        data: response.data,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("更新用户失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "更新用户失败",
        error: error.message,
      });
    }
  }

  // 删除用户
  async deleteUser(req, res) {
    try {
      const userId = req.params.id;
      await this.makeApiRequest(`/auth/users/${userId}`, "DELETE");

      res.json({
        success: true,
        message: "用户删除成功",
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("删除用户失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "删除用户失败",
        error: error.message,
      });
    }
  }

  // 创建生产记录
  async createProductionRecord(req, res) {
    try {
      const recordData = req.body;
      const response = await this.makeApiRequest(
        "/production-records",
        "POST",
        recordData,
      );

      res.json({
        success: true,
        message: "生产记录创建成功",
        data: response.data,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建生产记录失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "创建生产记录失败",
        error: error.message,
      });
    }
  }

  // 更新生产记录
  async updateProductionRecord(req, res) {
    try {
      const recordId = req.params.id;
      const recordData = req.body;
      const response = await this.makeApiRequest(
        `/production-records/${recordId}`,
        "PUT",
        recordData,
      );

      res.json({
        success: true,
        message: "生产记录更新成功",
        data: response.data,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("更新生产记录失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "更新生产记录失败",
        error: error.message,
      });
    }
  }

  // 删除生产记录
  async deleteProductionRecord(req, res) {
    try {
      const recordId = req.params.id;
      await this.makeApiRequest(`/production-records/${recordId}`, "DELETE");

      res.json({
        success: true,
        message: "生产记录删除成功",
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("删除生产记录失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "删除生产记录失败",
        error: error.message,
      });
    }
  }

  // 创建健康记录
  async createHealthRecord(req, res) {
    try {
      const recordData = req.body;
      const response = await this.makeApiRequest(
        "/health/records",
        "POST",
        recordData,
      );

      res.json({
        success: true,
        message: "健康记录创建成功",
        data: response.data,
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建健康记录失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "创建健康记录失败",
        error: error.message,
      });
    }
  }

  // 通用API请求方法
  async makeApiRequest(endpoint, method = "GET", data = null) {
    try {
      const config = {
        method: method,
        url: `${this.apiBaseUrl}${endpoint}`,
        headers: {
          "Content-Type": "application/json",
        },
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`API请求失败 ${method} ${endpoint}:`, error.message); } catch(_) {}

      throw error;
    }
  }
}

module.exports = new AdminController();
