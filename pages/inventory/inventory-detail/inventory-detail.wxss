/* pages/inventory/inventory-detail/inventory-detail.wxss */
.inventory-container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 总览部分 */
.overview-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.update-time {
  font-size: 22rpx;
  color: #999999;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #0066cc;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

/* 分类统计 */
.category-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.filter-tabs {
  display: flex;
  gap: 8rpx;
}

.tab-item {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666666;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.tab-item.active {
  background-color: #0066cc;
  color: #ffffff;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.category-item {
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.category-item:active {
  background-color: #e8f4fd;
  transform: scale(0.98);
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.category-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
}

.category-desc {
  font-size: 22rpx;
  color: #666666;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.category-count {
  font-size: 24rpx;
  color: #0066cc;
  font-weight: bold;
}

.category-percent {
  font-size: 22rpx;
  color: #666666;
}

.category-chart {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #0066cc;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 健康状态分析 */
.health-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.health-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30rpx 0;
  min-height: 250rpx;
}

.chart-canvas {
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.health-legend {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 4rpx;
}

.legend-color.healthy {
  background-color: #52c41a;
}

.legend-color.sick {
  background-color: #ff4d4f;
}

.legend-color.quarantine {
  background-color: #faad14;
}

.legend-text {
  font-size: 22rpx;
  color: #666666;
}

/* 预警信息 */
.alert-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.alert-list {
  margin-top: 20rpx;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border-left: 6rpx solid;
}

.alert-item.warning {
  background-color: #fff7e6;
  border-left-color: #faad14;
}

.alert-item.info {
  background-color: #e6f7ff;
  border-left-color: #1890ff;
}

.alert-item.error {
  background-color: #fff2f0;
  border-left-color: #ff4d4f;
}

.alert-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.alert-icon image {
  width: 100%;
  height: 100%;
}

.alert-content {
  flex: 1;
}

.alert-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4rpx;
}

.alert-desc {
  display: block;
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.alert-time {
  font-size: 20rpx;
  color: #999999;
}

.alert-action {
  padding: 12rpx 20rpx;
  background-color: #0066cc;
  color: #ffffff;
  border-radius: 20rpx;
  font-size: 22rpx;
}


