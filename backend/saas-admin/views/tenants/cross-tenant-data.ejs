<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨租户数据监控 - 智慧养鹅SAAS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        .tenant-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tenant-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .data-table {
            font-size: 0.9rem;
        }
        .data-table th {
            background: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .tenant-status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .filter-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        .nav-pills .nav-link.active {
            background: #0066CC;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <%- include('../partials/navbar') %>
    
    <div class="container-fluid py-4">
        <div class="row">
            <!-- 侧边栏 -->
            <%- include('../partials/sidebar') %>
            
            <!-- 主内容区 -->
            <div class="col-md-9 col-lg-10 ms-sm-auto px-md-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-diagram-3"></i> 跨租户数据监控
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新数据
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="exportData()">
                                <i class="bi bi-download"></i> 导出报表
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <h5><i class="bi bi-funnel"></i> 数据筛选</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">租户状态</label>
                            <select class="form-select" id="tenantStatusFilter">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="suspended">暂停</option>
                                <option value="trial">试用中</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">订阅计划</label>
                            <select class="form-select" id="subscriptionFilter">
                                <option value="">全部计划</option>
                                <option value="trial">试用版</option>
                                <option value="basic">基础版</option>
                                <option value="standard">标准版</option>
                                <option value="premium">高级版</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">时间范围</label>
                            <select class="form-select" id="timeRangeFilter">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">操作</label>
                            <div>
                                <button class="btn btn-primary w-100" onclick="applyFilters()">
                                    <i class="bi bi-search"></i> 应用筛选
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据视图切换 -->
                <ul class="nav nav-pills mb-4">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="pill" href="#overview-tab">
                            <i class="bi bi-speedometer2"></i> 综合概览
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="pill" href="#production-tab">
                            <i class="bi bi-bar-chart"></i> 生产数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="pill" href="#health-tab">
                            <i class="bi bi-heart-pulse"></i> 健康数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="pill" href="#financial-tab">
                            <i class="bi bi-currency-dollar"></i> 财务数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="pill" href="#comparison-tab">
                            <i class="bi bi-graph-up"></i> 对比分析
                        </a>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content">
                    <!-- 综合概览 -->
                    <div class="tab-pane fade show active" id="overview-tab">
                        <!-- 关键指标卡片 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="metric-value" id="totalTenants">-</div>
                                            <div>活跃租户</div>
                                        </div>
                                        <i class="bi bi-building fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="metric-value" id="totalGeese">-</div>
                                            <div>总鹅群数量</div>
                                        </div>
                                        <i class="bi bi-egg fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="metric-value" id="totalProduction">-</div>
                                            <div>日产蛋量</div>
                                        </div>
                                        <i class="bi bi-graph-up-arrow fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="metric-value" id="totalRevenue">-</div>
                                            <div>月度收入 (万元)</div>
                                        </div>
                                        <i class="bi bi-currency-yen fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 租户概览列表 -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-buildings"></i> 租户概览</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table data-table table-hover">
                                        <thead>
                                            <tr>
                                                <th>租户信息</th>
                                                <th>状态</th>
                                                <th>鹅群数量</th>
                                                <th>日产蛋量</th>
                                                <th>月收入</th>
                                                <th>健康状况</th>
                                                <th>最后活跃</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="tenantsTableBody">
                                            <!-- 数据将通过JavaScript加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 生产数据 -->
                    <div class="tab-pane fade" id="production-tab">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-bar-chart-line"></i> 生产趋势分析</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="productionChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-pie-chart"></i> 产量分布</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="productionPieChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 健康数据 -->
                    <div class="tab-pane fade" id="health-tab">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-heart-pulse"></i> 健康状况统计</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="healthChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-exclamation-triangle"></i> 健康告警</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="healthAlerts">
                                            <!-- 告警信息将通过JavaScript加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 财务数据 -->
                    <div class="tab-pane fade" id="financial-tab">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-graph-up"></i> 收入趋势</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="revenueChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-cash-stack"></i> 收入排行</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="revenueRanking">
                                            <!-- 排行榜将通过JavaScript加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 对比分析 -->
                    <div class="tab-pane fade" id="comparison-tab">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-graph-up"></i> 租户对比分析</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">选择对比租户</label>
                                        <select class="form-select" id="comparisonTenants" multiple>
                                            <!-- 选项将通过JavaScript加载 -->
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">对比指标</label>
                                        <select class="form-select" id="comparisonMetric">
                                            <option value="production">生产效率</option>
                                            <option value="revenue">收入对比</option>
                                            <option value="health">健康状况</option>
                                            <option value="growth">增长趋势</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="comparisonChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // 全局变量
        let tenantsData = [];
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadInitialData();
            initializeCharts();
        });

        // 加载初始数据
        async function loadInitialData() {
            try {
                showLoading();
                
                // 并行加载数据
                const [overviewData, tenantsListData] = await Promise.all([
                    fetch('/saas-admin/api/cross-tenant-overview').then(r => r.json()),
                    fetch('/saas-admin/api/tenants-data').then(r => r.json())
                ]);

                // 更新关键指标
                updateOverviewMetrics(overviewData);
                
                // 更新租户列表
                updateTenantsTable(tenantsListData);
                
                // 保存数据用于图表
                tenantsData = tenantsListData;
                
                hideLoading();
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('数据加载失败，请刷新重试');
                hideLoading();
            }
        }

        // 更新概览指标
        function updateOverviewMetrics(data) {
            document.getElementById('totalTenants').textContent = data.totalTenants || 0;
            document.getElementById('totalGeese').textContent = data.totalGeese || 0;
            document.getElementById('totalProduction').textContent = data.totalProduction || 0;
            document.getElementById('totalRevenue').textContent = (data.totalRevenue / 10000).toFixed(1) || 0;
        }

        // 更新租户表格
        function updateTenantsTable(tenants) {
            const tbody = document.getElementById('tenantsTableBody');
            tbody.innerHTML = '';

            tenants.forEach(tenant => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div>
                            <strong>${tenant.companyName}</strong>
                            <br><small class="text-muted">${tenant.tenantCode}</small>
                        </div>
                    </td>
                    <td>
                        <span class="tenant-status-indicator" style="background: ${getStatusColor(tenant.status)}"></span>
                        <span class="status-badge status-${tenant.status}">${getStatusText(tenant.status)}</span>
                    </td>
                    <td>${tenant.flockCount || 0}</td>
                    <td>${tenant.dailyProduction || 0}</td>
                    <td>¥${(tenant.monthlyRevenue || 0).toLocaleString()}</td>
                    <td>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-${getHealthColor(tenant.healthScore)}" 
                                 style="width: ${tenant.healthScore || 0}%"></div>
                        </div>
                        <small>${tenant.healthScore || 0}%</small>
                    </td>
                    <td>${formatDateTime(tenant.lastActiveTime)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewTenantDetail('${tenant.tenantCode}')">
                            <i class="bi bi-eye"></i> 查看
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 初始化图表
        function initializeCharts() {
            // 生产趋势图表
            const productionCtx = document.getElementById('productionChart')?.getContext('2d');
            if (productionCtx) {
                charts.production = new Chart(productionCtx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '总产量',
                            data: [],
                            borderColor: '#0066CC',
                            backgroundColor: 'rgba(0, 102, 204, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '近30天生产趋势'
                            }
                        }
                    }
                });
            }

            // 其他图表初始化...
        }

        // 工具函数
        function getStatusColor(status) {
            const colors = {
                'active': '#28a745',
                'suspended': '#ffc107',
                'trial': '#17a2b8',
                'expired': '#dc3545'
            };
            return colors[status] || '#6c757d';
        }

        function getStatusText(status) {
            const texts = {
                'active': '活跃',
                'suspended': '暂停',
                'trial': '试用',
                'expired': '过期'
            };
            return texts[status] || '未知';
        }

        function getHealthColor(score) {
            if (score >= 80) return 'success';
            if (score >= 60) return 'warning';
            return 'danger';
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function viewTenantDetail(tenantCode) {
            window.open(`/saas-admin/tenants/${tenantCode}/detail`, '_blank');
        }

        function refreshData() {
            loadInitialData();
        }

        function exportData() {
            window.open('/saas-admin/api/export-cross-tenant-data', '_blank');
        }

        function applyFilters() {
            // 实现筛选逻辑
            const statusFilter = document.getElementById('tenantStatusFilter').value;
            const subscriptionFilter = document.getElementById('subscriptionFilter').value;
            const timeRange = document.getElementById('timeRangeFilter').value;
            
            // 重新加载数据
            loadInitialData();
        }

        function showLoading() {
            // 显示加载状态
        }

        function hideLoading() {
            // 隐藏加载状态
        }

        function showError(message) {
            // 显示错误消息
            console.error(message);
        }
    </script>
</body>
</html>