/**
 * SVG转PNG图标转换脚本
 * 用于将项目中的SVG图标转换为PNG格式，解决微信小程序对SVG支持有限的问题
 */

const fs = require('fs');
const path = require('path');

// 需要转换的SVG图标列表
const svgIcons = [
  // images/icons/ 目录下的SVG文件
  {
    input: 'images/icons/search.svg',
    output: 'images/icons/search.png',
    size: '24x24',
    description: '搜索图标'
  },
  {
    input: 'images/icons/cart.svg',
    output: 'images/icons/cart.png',
    size: '24x24',
    description: '购物车图标'
  },
  {
    input: 'images/icons/list.svg',
    output: 'images/icons/list.png',
    size: '24x24',
    description: '列表视图图标'
  },
  {
    input: 'images/icons/grid.svg',
    output: 'images/icons/grid.png',
    size: '24x24',
    description: '网格视图图标'
  },
  {
    input: 'images/icons/eye.svg',
    output: 'images/icons/eye.png',
    size: '24x24',
    description: '查看详情图标'
  },
  {
    input: 'images/icons/package.svg',
    output: 'images/icons/package.png',
    size: '48x48',
    description: '包裹/空状态图标'
  },
  {
    input: 'images/icons/edit.svg',
    output: 'images/icons/edit.png',
    size: '24x24',
    description: '编辑图标'
  },
  {
    input: 'images/icons/trash.svg',
    output: 'images/icons/trash.png',
    size: '24x24',
    description: '删除图标'
  },
  {
    input: 'images/icons/plus.svg',
    output: 'images/icons/plus.png',
    size: '24x24',
    description: '添加图标'
  },
  {
    input: 'images/icons/check-circle.svg',
    output: 'images/icons/check-circle.png',
    size: '24x24',
    description: '完成图标'
  },
  {
    input: 'images/icons/clock.svg',
    output: 'images/icons/clock.png',
    size: '24x24',
    description: '时间图标'
  },
  {
    input: 'images/icons/map-pin.svg',
    output: 'images/icons/map-pin.png',
    size: '24x24',
    description: '位置图标'
  },
  {
    input: 'images/icons/star.svg',
    output: 'images/icons/star.png',
    size: '24x24',
    description: '星标图标'
  },
  {
    input: 'images/icons/truck.svg',
    output: 'images/icons/truck.png',
    size: '24x24',
    description: '配送图标'
  },

  // assets/icons/ 目录下的SVG文件
  {
    input: 'assets/icons/home_new.svg',
    output: 'assets/icons/home_new.png',
    size: '81x81',
    description: '首页导航图标'
  },
  {
    input: 'assets/icons/health_new.svg',
    output: 'assets/icons/health_new.png',
    size: '81x81',
    description: '健康导航图标'
  },
  {
    input: 'assets/icons/production_new.svg',
    output: 'assets/icons/production_new.png',
    size: '81x81',
    description: '生产导航图标'
  },
  {
    input: 'assets/icons/shop_new.svg',
    output: 'assets/icons/shop_new.png',
    size: '81x81',
    description: '商城导航图标'
  },
  {
    input: 'assets/icons/profile_new.svg',
    output: 'assets/icons/profile_new.png',
    size: '81x81',
    description: '个人中心导航图标'
  },
  {
    input: 'assets/icons/home_icon.svg',
    output: 'assets/icons/home_icon.png',
    size: '48x48',
    description: '首页功能图标'
  },
  {
    input: 'assets/icons/health_icon.svg',
    output: 'assets/icons/health_icon.png',
    size: '48x48',
    description: '健康功能图标'
  },
  {
    input: 'assets/icons/production_icon.svg',
    output: 'assets/icons/production_icon.png',
    size: '48x48',
    description: '生产功能图标'
  },
  {
    input: 'assets/icons/shop_icon.svg',
    output: 'assets/icons/shop_icon.png',
    size: '48x48',
    description: '商城功能图标'
  },
  {
    input: 'assets/icons/profile_icon.svg',
    output: 'assets/icons/profile_icon.png',
    size: '48x48',
    description: '个人中心功能图标'
  }
];

// 颜色配置
const colors = {
  default: '#666666',      // 默认颜色
  primary: '#0066CC',      // 主色调
  selected: '#0066CC',     // 选中状态
  unselected: '#7A7E83'    // 未选中状态
};

console.log('🎨 SVG转PNG图标转换工具');
console.log('=====================================');
console.log(`需要转换的图标数量: ${svgIcons.length}`);
console.log('');

// 检查SVG文件是否存在
console.log('📋 检查SVG文件状态:');
svgIcons.forEach((icon, index) => {
  const exists = fs.existsSync(icon.input);
  const status = exists ? '✅' : '❌';
  console.log(`${index + 1}. ${status} ${icon.input} -> ${icon.output} (${icon.size})`);
});

console.log('');
console.log('🔧 转换方法:');
console.log('');
console.log('方法1: 在线转换工具 (推荐)');
console.log('- 访问: https://convertio.co/svg-png/');
console.log('- 上传SVG文件');
console.log('- 设置输出尺寸');
console.log('- 下载PNG文件');
console.log('');
console.log('方法2: 命令行工具');
console.log('# 安装ImageMagick');
console.log('brew install imagemagick  # macOS');
console.log('sudo apt-get install imagemagick  # Ubuntu');
console.log('');
console.log('# 批量转换命令');
svgIcons.forEach(icon => {
  console.log(`convert -background transparent -size ${icon.size} "${icon.input}" "${icon.output}"`);
});
console.log('');
console.log('方法3: 设计软件');
console.log('- Figma: 导入SVG，导出为PNG');
console.log('- Sketch: 导入SVG，导出为PNG');
console.log('- Adobe Illustrator: 打开SVG，导出为PNG');
console.log('');
console.log('📝 转换完成后需要更新的文件:');
console.log('- pages/shop/shop.wxml');
console.log('- 其他引用SVG图标的WXML文件');
console.log('');
console.log('⚠️  注意事项:');
console.log('1. 确保PNG图标背景透明');
console.log('2. 保持图标的清晰度和视觉效果');
console.log('3. 导航图标需要81x81px尺寸');
console.log('4. 功能图标建议24x24px或48x48px尺寸');
console.log('5. 转换完成后删除对应的SVG文件');
