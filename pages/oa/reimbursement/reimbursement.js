// pages/oa/reimbursement/reimbursement.js

/**
 * 费用报销模块主页面
 * 功能：费用报销管理、查看报销记录
 */

Page({
  data: {
    loading: false,
    
    quickActions: [
      {
        id: 'apply-reimbursement',
        title: '费用报销',
        description: '提交新的报销申请',
        iconText: '💸',
        url: '/pages/oa/reimbursement/apply/apply',
        color: '#0066CC'
      },
      {
        id: 'my-reimbursements',
        title: '我的报销',
        description: '查看我的报销记录',
        iconText: '📋',
        url: '/pages/oa/reimbursement/list/list?type=my',
        color: '#00A86B'
      }
    ],
    
    statistics: {
      pending: 2,
      approved: 15,
      rejected: 1,
      total: 18
    },
    
    recentReimbursements: [
      {
        id: 'reimb_001',
        type: '差旅费',
        description: '出差北京交通住宿费',
        amount: 1200.00,
        status: 'pending',
        statusText: '待审批',
        statusColor: '#FF9500',
        submitTime: '2024-01-10 16:30'
      },
      {
        id: 'reimb_002',
        type: '招待费',
        description: '客户接待餐费',
        amount: 380.00,
        status: 'approved',
        statusText: '已批准',
        statusColor: '#00A86B',
        submitTime: '2024-01-08 14:20'
      }
    ]
  },

  onLoad: function (options) {
    this.loadData();
  },

  onShow: function () {
    this.refreshData();
  },

  loadData: function() {
    this.setData({ loading: true });
    setTimeout(() => {
      this.setData({ loading: false });
    }, 500);
  },

  refreshData: function() {
    this.loadData();
  },

  onQuickActionTap: function(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.navigateTo({
        url: url,
        fail: () => {
          wx.showToast({
            title: '页面开发中',
            icon: 'none'
          });
        }
      });
    }
  },

  onStatTap: function(e) {
    const { type } = e.currentTarget.dataset;
    
    // 根据统计类型映射到对应的状态筛选参数
    const statusMap = {
      'pending': 'submitted',     // 待审批
      'approved': 'approved',     // 已批准  
      'rejected': 'rejected',     // 已拒绝
      'total': 'all'             // 总申请
    };
    
    const status = statusMap[type] || 'all';
    
    // 跳转到报销列表页面，传递状态参数
    wx.navigateTo({
      url: `/pages/oa/reimbursement/list/list?status=${status}&from=statistics`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 生成详细统计信息
  generateDetailedStats: function(type) {
    const reimbursements = this.data.reimbursements;
    
    let filteredData = [];
    let content = '';
    
    switch(type) {
      case '待审批':
        filteredData = reimbursements.filter(item => item.status === 'pending');
        content = `待审批申请：${filteredData.length}笔\n总金额：¥${filteredData.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}\n最早申请：${filteredData.length > 0 ? this.formatDate(filteredData[0].createTime) : '无'}`;
        break;
      case '已通过':
        filteredData = reimbursements.filter(item => item.status === 'approved');
        content = `已通过申请：${filteredData.length}笔\n总金额：¥${filteredData.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}\n本月通过：${filteredData.filter(item => this.isCurrentMonth(item.approveTime)).length}笔`;
        break;
      case '已拒绝':
        filteredData = reimbursements.filter(item => item.status === 'rejected');
        content = `已拒绝申请：${filteredData.length}笔\n拒绝金额：¥${filteredData.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}\n主要原因：票据不全`;
        break;
      case '本月':
        filteredData = reimbursements.filter(item => this.isCurrentMonth(item.createTime));
        content = `本月申请：${filteredData.length}笔\n本月金额：¥${filteredData.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}\n平均金额：¥${filteredData.length > 0 ? (filteredData.reduce((sum, item) => sum + item.amount, 0) / filteredData.length).toFixed(2) : '0.00'}`;
        break;
    }

    return {
      content: content,
      data: filteredData,
      type: type
    };
  },

  // 显示详细统计
  showDetailedStatistics: function(type, stats) {
    this.setData({
      showStatsModal: true,
      statsData: {
        type: type,
        data: stats.data,
        summary: {
          count: stats.data.length,
          totalAmount: stats.data.reduce((sum, item) => sum + item.amount, 0),
          avgAmount: stats.data.length > 0 ? stats.data.reduce((sum, item) => sum + item.amount, 0) / stats.data.length : 0,
          categories: this.groupByCategory(stats.data)
        }
      }
    });
  },

  // 按类别分组
  groupByCategory: function(data) {
    const categories = {};
    data.forEach(item => {
      if (!categories[item.category]) {
        categories[item.category] = {
          count: 0,
          amount: 0
        };
      }
      categories[item.category].count++;
      categories[item.category].amount += item.amount;
    });
    return categories;
  },

  // 判断是否为当月
  isCurrentMonth: function(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    const now = new Date();
    return date.getFullYear() === now.getFullYear() && 
           date.getMonth() === now.getMonth();
  },

  // 格式化日期
  formatDate: function(dateString) {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  },

  // 关闭统计模态框
  onCloseStatsModal: function() {
    this.setData({
      showStatsModal: false,
      statsData: null
    });
  },

  onReimbursementItemTap: function(e) {
    const { id } = e.currentTarget.dataset;
    
    if (!id) {
      wx.showToast({
        title: '数据错误',
        icon: 'none'
      });
      return;
    }

    // 跳转到报销详情页面
    wx.navigateTo({
      url: `/pages/oa/reimbursement/detail/detail?id=${id}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 生成报销详细信息
  generateReimbursementDetail: function(reimbursement) {
    return {
      basicInfo: {
        title: reimbursement.title,
        amount: reimbursement.amount,
        category: reimbursement.category,
        date: reimbursement.date,
        status: reimbursement.status,
        applicant: reimbursement.applicant || '当前用户'
      },
      timeline: [
        {
          step: '申请提交',
          time: reimbursement.createTime,
          status: 'completed',
          description: '报销申请已提交'
        },
        {
          step: '部门审核',
          time: reimbursement.status !== 'pending' ? reimbursement.reviewTime : null,
          status: reimbursement.status === 'pending' ? 'current' : 'completed',
          description: reimbursement.status === 'pending' ? '等待部门领导审核' : '部门审核已完成'
        },
        {
          step: '财务审核',
          time: reimbursement.status === 'approved' ? reimbursement.approveTime : null,
          status: reimbursement.status === 'approved' ? 'completed' : 'pending',
          description: reimbursement.status === 'approved' ? '财务审核通过' : '等待财务审核'
        },
        {
          step: '打款发放',
          time: reimbursement.status === 'approved' ? reimbursement.payTime : null,
          status: reimbursement.payTime ? 'completed' : 'pending',
          description: reimbursement.payTime ? '款项已发放' : '等待打款'
        }
      ],
      attachments: reimbursement.attachments || [
        { name: '发票.jpg', size: '245KB', type: 'image' },
        { name: '报销单.pdf', size: '156KB', type: 'pdf' }
      ],
      approvalHistory: reimbursement.approvalHistory || [
        {
          approver: '张经理',
          action: '审核通过',
          time: reimbursement.reviewTime,
          comment: '票据齐全，同意报销'
        }
      ]
    };
  },

  // 关闭详情模态框
  onCloseDetailModal: function() {
    this.setData({
      showDetailModal: false,
      selectedReimbursement: null
    });
  },

  onViewAllReimbursements: function() {
    // 跳转到报销列表页面，显示全部数据
    wx.navigateTo({
      url: '/pages/oa/reimbursement/list/list?status=all&from=viewAll',
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 过滤报销列表
  onFilterReimbursements: function(e) {
    const { filter } = e.currentTarget.dataset;
    let filtered = [];
    
    switch(filter) {
      case 'all':
        filtered = this.data.reimbursements;
        break;
      case 'pending':
        filtered = this.data.reimbursements.filter(item => item.status === 'pending');
        break;
      case 'approved':
        filtered = this.data.reimbursements.filter(item => item.status === 'approved');
        break;
      case 'rejected':
        filtered = this.data.reimbursements.filter(item => item.status === 'rejected');
        break;
      case 'thisMonth':
        filtered = this.data.reimbursements.filter(item => this.isCurrentMonth(item.createTime));
        break;
    }

    this.setData({
      'listData.filtered': filtered,
      'listData.currentFilter': filter
    });
  },

  // 排序报销列表
  onSortReimbursements: function(e) {
    const { sortBy } = e.currentTarget.dataset;
    const currentSortBy = this.data.listData.sortBy;
    const currentSortOrder = this.data.listData.sortOrder;
    
    let newSortOrder = 'desc';
    if (currentSortBy === sortBy && currentSortOrder === 'desc') {
      newSortOrder = 'asc';
    }

    const sorted = [...this.data.listData.filtered].sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      if (sortBy === 'amount') {
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      }
      
      if (newSortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    this.setData({
      'listData.filtered': sorted,
      'listData.sortBy': sortBy,
      'listData.sortOrder': newSortOrder
    });
  },

  // 关闭列表模态框
  onCloseListModal: function() {
    this.setData({
      showListModal: false,
      listData: null
    });
  },

  onPullDownRefresh: function () {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});