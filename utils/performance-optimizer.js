/**
 * 智慧养鹅小程序 - 性能优化工具 V2.0
 * 解决小程序加载缓慢的核心问题
 * 
 * 主要优化策略：
 * 1. 首屏加载优化
 * 2. 数据预加载和缓存
 * 3. 图片懒加载
 * 4. API请求优化
 * 5. 组件按需加载
 */

const { performanceMixin } = require('./performance.js');

/**
 * 首屏性能优化器
 */
class FirstScreenOptimizer {
  
  /**
   * 首屏关键资源预加载
   */
  static preloadCriticalResources() {
    return new Promise((resolve) => {
      const criticalData = {
        userInfo: wx.getStorageSync('user_info') || {},
        systemInfo: (() => {
      try {
        // 使用新API获取系统信息
        const deviceInfo = wx.getDeviceInfo ? wx.getDeviceInfo() : {};
        const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};
        const appBaseInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};
        
        return {
          ...deviceInfo,
          ...windowInfo,
          ...appBaseInfo
        };
      } catch (error) {
        try { const logger = require('./logger.js'); logger.warn && logger.warn('获取系统信息失败，使用默认值', error); } catch(_) {}
        return {
          platform: 'unknown',
          brand: 'unknown',
          model: 'unknown',
          pixelRatio: 1,
          screenWidth: 375,
          screenHeight: 667
        };
      }
    })(),
        networkType: 'unknown'
      };
      
      // 获取网络类型
      wx.getNetworkType({
        success: (res) => {
          criticalData.networkType = res.networkType;
          resolve(criticalData);
        },
        fail: () => resolve(criticalData)
      });
    });
  }
  
  /**
   * 智能数据缓存策略
   */
  static smartDataCache(key, fetchFunction, maxAge = 5 * 60 * 1000) {
    const cacheKey = `cache_${key}`;
    const timeKey = `cache_time_${key}`;
    
    try {
      const cachedData = wx.getStorageSync(cacheKey);
      const cacheTime = wx.getStorageSync(timeKey);
      
      // 检查缓存是否有效
      if (cachedData && cacheTime && (Date.now() - cacheTime < maxAge)) {
        return Promise.resolve(cachedData);
      }
      
      // 缓存失效，重新获取数据
      return fetchFunction().then(data => {
        try {
          wx.setStorageSync(cacheKey, data);
          wx.setStorageSync(timeKey, Date.now());
        } catch (e) {
          try { const logger = require('./logger.js'); logger.warn && logger.warn('[Cache] 缓存失败', e); } catch(_) {}
        }
        return data;
      });
      
    } catch (e) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[Cache] 缓存读取失败', e); } catch(_) {}
      return fetchFunction();
    }
  }
  
  /**
   * 图片懒加载优化
   */
  static createLazyImageLoader() {
    const observer = wx.createIntersectionObserver({
      rootMargin: '50px'
    });
    
    return {
      observe: (selector, callback) => {
        observer.relativeToViewport().observe(selector, (res) => {
          if (res.intersectionRatio > 0) {
            callback(res);
            observer.unobserve(selector);
          }
        });
      },
      
      disconnect: () => observer.disconnect()
    };
  }
}

/**
 * API请求优化器
 */
class APIOptimizer {
  
  /**
   * 并发请求控制器
   */
  static createBatchRequester(maxConcurrent = 3) {
    let pending = 0;
    const queue = [];
    
    const processQueue = () => {
      while (pending < maxConcurrent && queue.length > 0) {
        const { request, resolve, reject } = queue.shift();
        pending++;
        
        request()
          .then(resolve)
          .catch(reject)
          .finally(() => {
            pending--;
            processQueue();
          });
      }
    };
    
    return (request) => {
      return new Promise((resolve, reject) => {
        queue.push({ request, resolve, reject });
        processQueue();
      });
    };
  }
  
  /**
   * 请求去重器
   */
  static createRequestDeduplicator() {
    const cache = new Map();
    
    return (key, request) => {
      if (cache.has(key)) {
        return cache.get(key);
      }
      
      const promise = request().finally(() => {
        cache.delete(key);
      });
      
      cache.set(key, promise);
      return promise;
    };
  }
  
  /**
   * 网络状态感知请求
   */
  static networkAwareRequest(request, options = {}) {
    return new Promise((resolve, reject) => {
      wx.getNetworkType({
        success: (res) => {
          const { networkType } = res;
          
          if (networkType === 'none') {
            // 无网络，尝试使用缓存
            const cachedData = options.fallbackCache ? 
              wx.getStorageSync(options.fallbackCache) : null;
            
            if (cachedData) {
              resolve(cachedData);
            } else {
              reject(new Error('Network unavailable'));
            }
            return;
          }
          
          // 根据网络类型调整超时时间
          const timeout = networkType === 'wifi' ? 5000 : 
                         networkType === '4g' ? 8000 : 15000;
          
          const timeoutId = setTimeout(() => {
            reject(new Error('Request timeout'));
          }, timeout);
          
          request()
            .then(resolve)
            .catch(reject)
            .finally(() => clearTimeout(timeoutId));
        },
        fail: () => {
          request().then(resolve).catch(reject);
        }
      });
    });
  }
}

/**
 * 组件加载优化器
 */
class ComponentOptimizer {
  
  /**
   * 按需加载组件
   */
  static lazyLoadComponent(componentPath, condition = true) {
    if (!condition) {
      return null;
    }
    
    try {
      return require(componentPath);
    } catch (e) {
      try { const logger = require('./logger.js'); logger.error && logger.error(`[LazyLoad] 组件加载失败: ${componentPath}`, e); } catch(_) {}
      return null;
    }
  }
  
  /**
   * 组件分包加载
   */
  static loadSubpackageComponent(subPackage, componentName) {
    return new Promise((resolve, reject) => {
      wx.loadSubpackage({
        name: subPackage,
        success: () => {
          try {
            const component = require(`/${subPackage}/${componentName}`);
            resolve(component);
          } catch (e) {
            reject(e);
          }
        },
        fail: reject
      });
    });
  }
}

/**
 * 页面性能优化混入
 */
const optimizedPageMixin = {
  ...performanceMixin,
  
  data: {
    ...performanceMixin.data,
    _isOptimizedPage: true,
    _loadingStates: {}
  },
  
  onLoad(options) {
    // 调用原有的onLoad
    if (performanceMixin.onLoad) {
      performanceMixin.onLoad.call(this, options);
    }
    
    // 启动性能监控
    this._performanceStartTime = Date.now();
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[Perf] 页面开始加载', this.route); } catch(_) {}
    
    // 预加载关键资源
    this._preloadCriticalResources();
  },
  
  onReady() {
    // 记录首屏加载时间
    const loadTime = Date.now() - this._performanceStartTime;
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[Perf] 页面加载完成', { route: this.route, loadTime }); } catch(_) {}
    
    // 上报性能数据
    this._reportPerformanceMetrics(loadTime);
  },
  
  onUnload() {
    // 调用原有的onUnload
    if (performanceMixin.onUnload) {
      performanceMixin.onUnload.call(this);
    }
    
    // 清理性能监控资源
    this._cleanupPerformanceResources();
  },
  
  /**
   * 预加载关键资源
   */
  _preloadCriticalResources() {
    FirstScreenOptimizer.preloadCriticalResources()
      .then(data => {
        this.setData({
          _criticalData: data
        });
      })
      .catch(e => {
        try { const logger = require('./logger.js'); logger.warn && logger.warn('[Perf] 关键资源预加载失败', e); } catch(_) {}
      });
  },
  
  /**
   * 优化的数据请求方法
   */
  optimizedRequest(key, request, options = {}) {
    const { useCache = true, maxAge = 5 * 60 * 1000 } = options;
    
    if (useCache) {
      return FirstScreenOptimizer.smartDataCache(key, request, maxAge);
    } else {
      return APIOptimizer.networkAwareRequest(request, options);
    }
  },
  
  /**
   * 批量数据加载
   */
  batchLoadData(requests) {
    const batchRequester = APIOptimizer.createBatchRequester(3);
    
    return Promise.all(
      Object.entries(requests).map(([key, request]) =>
        batchRequester(request).then(data => ({ key, data }))
      )
    );
  },
  
  /**
   * 上报性能指标
   */
  _reportPerformanceMetrics(loadTime) {
    // 只在开发环境上报，避免影响生产性能
    if (wx.getAccountInfoSync().miniProgram.envVersion === 'develop') {
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[Perf] 性能指标', {
        page: this.route,
        loadTime,
        timestamp: Date.now()
      }); } catch(_) {}
    }
  },
  
  /**
   * 清理性能资源
   */
  _cleanupPerformanceResources() {
    // 清理定时器、监听器等资源
    if (this._performanceTimers) {
      this._performanceTimers.forEach(clearTimeout);
    }
    
    if (this._performanceObservers) {
      this._performanceObservers.forEach(observer => observer.disconnect());
    }
  }
};

module.exports = {
  FirstScreenOptimizer,
  APIOptimizer,
  ComponentOptimizer,
  optimizedPageMixin
};