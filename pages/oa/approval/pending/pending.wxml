<!-- pages/oa/approval/pending/pending.wxml -->
<view class="pending-container">
  
  <!-- 页面头部信息 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">我的待审批</text>
      <view class="header-summary">
        <text class="summary-text">共 {{statistics.total}} 项待处理</text>
        <view wx:if="{{statistics.urgent > 0}}" class="urgent-tip">
          <text class="urgent-count">{{statistics.urgent}}</text>
          <text class="urgent-label">项紧急</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速统计 -->
  <view class="quick-stats">
    <view class="stats-row">
      <view class="stat-item" bindtap="onQuickFilter" data-type="all">
        <text class="stat-number primary">{{statistics.total}}</text>
        <text class="stat-label">全部</text>
      </view>
      <view class="stat-item" bindtap="onQuickFilter" data-type="urgent">
        <text class="stat-number urgent">{{statistics.urgent}}</text>
        <text class="stat-label">紧急</text>
      </view>
      <view class="stat-item" bindtap="onQuickFilter" data-type="today">
        <text class="stat-number today">{{statistics.today}}</text>
        <text class="stat-label">今日</text>
      </view>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索申请人、标题..."
        value="{{filters.keyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearchConfirm"
      />
      <view wx:if="{{filters.keyword}}" class="clear-search" bindtap="clearSearch">
        <text>×</text>
      </view>
    </view>
    
    <view class="filter-toggle" bindtap="toggleFilters">
      <text class="filter-icon">{{showFilters ? '📋' : '⚙️'}}</text>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view wx:if="{{showFilters}}" class="filter-panel">
    <view class="filter-section">
      <text class="filter-title">业务类型</text>
      <view class="filter-options">
        <view 
          wx:for="{{businessTypeOptions}}" 
          wx:key="value"
          class="filter-option {{filters.businessType === item.value ? 'active' : ''}}"
          bindtap="onBusinessTypeChange"
          data-value="{{item.value}}"
        >
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="filter-title">时间范围</text>
      <view class="filter-options">
        <view 
          wx:for="{{timeRangeOptions}}" 
          wx:key="value"
          class="filter-option {{filters.timeRange === item.value ? 'active' : ''}}"
          bindtap="onTimeRangeChange"
          data-value="{{item.value}}"
        >
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="filter-actions">
      <button class="reset-btn" bindtap="resetFilters">重置</button>
      <button class="apply-btn" bindtap="applyFilters">应用</button>
    </view>
  </view>

  <!-- 审批列表 -->
  <view class="approval-list">
    <!-- 加载状态 -->
    <view wx:if="{{refreshing}}" class="loading-state">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{approvals.length === 0 && !loading}}" class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-title">暂无待审批事项</text>
      <text class="empty-desc">您当前没有需要处理的审批申请</text>
      <button class="refresh-btn" bindtap="refreshData">刷新页面</button>
    </view>

    <!-- 审批项列表 -->
    <view wx:else>
      <view 
        wx:for="{{approvals}}" 
        wx:key="id" 
        class="approval-item"
        bindtap="onApprovalTap"
        data-approval="{{item}}"
      >
        <view class="item-header">
          <view class="title-section">
            <text class="item-title">{{item.title}}</text>
            <view class="status-tags">
              <view class="business-tag {{item.business_type}}">
                <text>{{item.businessTypeLabel}}</text>
              </view>
              <view wx:if="{{item.isUrgent}}" class="urgent-tag">
                <text>紧急</text>
              </view>
              <view wx:if="{{item.isOverdue}}" class="overdue-tag">
                <text>逾期</text>
              </view>
            </view>
          </view>
          <text class="time-info">{{item.timeAgo}}</text>
        </view>

        <view class="item-content">
          <view class="applicant-info">
            <view class="info-row">
              <text class="info-icon">👤</text>
              <text class="info-text">{{item.applicant_name}}</text>
              <text class="dept-text">{{item.applicant_department}}</text>
            </view>
            <view wx:if="{{item.amount}}" class="info-row">
              <text class="info-icon">💰</text>
              <text class="amount-text">¥{{item.amount}}</text>
            </view>
            <view wx:if="{{item.deadline}}" class="info-row deadline-row">
              <text class="info-icon">⏰</text>
              <text class="deadline-text">{{item.deadline}}</text>
            </view>
          </view>
        </view>

        <view class="item-actions">
          <button 
            class="action-btn reject-btn"
            bindtap="onActionTap"
            data-action="reject"
            data-approval="{{item}}"
            catchtap="stopPropagation"
          >
            拒绝
          </button>
          <button 
            class="action-btn approve-btn"
            bindtap="onActionTap"
            data-action="approve"
            data-approval="{{item}}"
            catchtap="stopPropagation"
          >
            通过
          </button>
        </view>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{loading}}" class="load-more">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view wx:if="{{!pagination.hasMore && approvals.length > 0}}" class="no-more">
        <text>- 已显示全部审批事项 -</text>
      </view>
    </view>
  </view>

  <!-- 审批确认弹窗 -->
  <view wx:if="{{showActionModal}}" class="modal-overlay" bindtap="hideActionModal">
    <view class="action-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{modalAction === 'approve' ? '审批通过' : '审批拒绝'}}</text>
        <button class="modal-close" bindtap="hideActionModal">×</button>
      </view>
      
      <view class="modal-content">
        <view class="approval-summary">
          <text class="summary-title">{{currentApproval.title}}</text>
          <text class="summary-applicant">申请人：{{currentApproval.applicant_name}}</text>
        </view>
        
        <view class="form-section">
          <text class="form-label">审批意见 *</text>
          <textarea 
            class="remarks-input"
            placeholder="请输入审批意见..."
            value="{{actionRemarks}}"
            bindinput="onRemarksInput"
            maxlength="200"
            show-count
          />
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="cancel-btn" bindtap="hideActionModal">取消</button>
        <button 
          class="confirm-btn {{modalAction}}"
          loading="{{submitting}}"
          bindtap="confirmAction"
        >
          确认{{modalAction === 'approve' ? '通过' : '拒绝'}}
        </button>
      </view>
    </view>
  </view>

</view>