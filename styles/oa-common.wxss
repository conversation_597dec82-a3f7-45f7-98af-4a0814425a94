/* OA系统通用样式 - 智慧养鹅 */
/* 确保所有OA页面风格统一 */

@import '/styles/design-system.wxss';

/* ==================== OA页面通用布局 ==================== */

/* 页面容器 */
.oa-container {
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

/* 页面头部 */
.oa-page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  padding: var(--space-3xl) var(--space-2xl) var(--space-2xl);
  color: var(--text-inverse);
  position: relative;
  overflow: hidden;
}

.oa-page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: skewX(-15deg);
}

.oa-header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.oa-page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-sm);
  text-align: center;
}

.oa-page-subtitle {
  font-size: var(--text-lg);
  opacity: 0.9;
  text-align: center;
}

/* ==================== 智能提示区块 ==================== */

.oa-smart-tip {
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
  margin: var(--space-lg);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  border: 2rpx solid #4A90E2;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.12);
  position: relative;
  overflow: hidden;
}

.oa-smart-tip::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.oa-tip-icon {
  font-size: var(--text-2xl);
  margin-right: var(--space-lg);
  color: #4A90E2;
  font-weight: var(--font-bold);
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  z-index: 2;
}

.oa-tip-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.oa-tip-title {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: #2E5BBA;
  display: block;
  margin-bottom: var(--space-xs);
}

.oa-tip-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  display: block;
}

/* ==================== 通用区块样式 ==================== */

.oa-section {
  background: var(--bg-primary);
  margin: var(--space-lg);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.oa-section:hover {
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);
  transform: translateY(-4rpx);
}

.oa-section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  position: relative;
  padding-left: var(--space-lg);
}

.oa-section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border-radius: 3rpx;
}

/* ==================== 表单样式 ==================== */

.oa-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.oa-form-row {
  display: flex;
  gap: var(--space-xl);
}

.oa-form-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  position: relative;
}

.oa-form-item.full-width {
  width: 100%;
}

.oa-label {
  font-size: var(--text-base);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  font-weight: var(--font-semibold);
  position: relative;
  transition: color 0.2s ease;
}

.oa-label.required::after {
  content: ' *';
  color: #FF6B6B;
  font-weight: var(--font-bold);
  animation: pulse 2s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }
}

.oa-input,
.oa-textarea {
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.oa-input:focus,
.oa-textarea:focus {
  border-color: #4A90E2;
  outline: none;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.15);
  transform: translateY(-2rpx);
}

.oa-input::placeholder,
.oa-textarea::placeholder {
  color: var(--text-placeholder);
  transition: opacity 0.2s ease;
}

.oa-input:focus::placeholder,
.oa-textarea:focus::placeholder {
  opacity: 0.6;
}

/* 选择器样式 */
.oa-picker {
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg) var(--space-xl);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  min-height: 88rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

.oa-picker:active {
  border-color: #4A90E2;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.15);
  transform: translateY(-2rpx);
}

/* ==================== 按钮样式 ==================== */

.oa-actions-section {
  padding: var(--space-xl) var(--space-lg) calc(var(--space-xl) + env(safe-area-inset-bottom));
  background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.8) 20%, var(--bg-primary) 100%);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  gap: var(--space-lg);
  backdrop-filter: blur(20rpx);
}

.oa-action-btn {
  flex: 1;
  height: 96rpx;
  border-radius: var(--radius-xl);
  border: none;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.oa-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.oa-action-btn:active::before {
  left: 100%;
}

.oa-action-btn.primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: var(--text-inverse);
  box-shadow: 0 12rpx 48rpx rgba(74, 144, 226, 0.3);
}

.oa-action-btn.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.4);
}

.oa-action-btn.secondary {
  background: linear-gradient(135deg, var(--bg-primary) 0%, #f8fafc 100%);
  color: var(--text-primary);
  border: 2rpx solid #E2E8F0;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.oa-action-btn.secondary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* ==================== 权限提示样式 ==================== */

.oa-permission-notice {
  background: linear-gradient(135deg, var(--warning-bg) 0%, #fff8e1 100%);
  border: 2rpx solid var(--warning);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin: var(--space-lg);
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(250, 173, 20, 0.15);
}

.oa-permission-notice.error {
  background: linear-gradient(135deg, var(--error-bg) 0%, #fff5f5 100%);
  border-color: var(--error);
  box-shadow: 0 8rpx 32rpx rgba(255, 77, 79, 0.15);
}

.oa-permission-notice.success {
  background: linear-gradient(135deg, var(--success-bg) 0%, #f0fff4 100%);
  border-color: var(--success);
  box-shadow: 0 8rpx 32rpx rgba(82, 196, 26, 0.15);
}

.oa-notice-icon {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  margin-right: var(--space-lg);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.8);
  color: var(--warning);
}

.oa-permission-notice.error .oa-notice-icon {
  color: var(--error);
}

.oa-permission-notice.success .oa-notice-icon {
  color: var(--success);
}

/* ==================== 列表样式 ==================== */

.oa-list {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  margin: var(--space-lg);
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
}

.oa-list-item {
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color 0.2s ease;
}

.oa-list-item:last-child {
  border-bottom: none;
}

.oa-list-item:active {
  background: var(--bg-secondary);
}

/* ==================== 状态标签 ==================== */

.oa-status-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-xl);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  text-align: center;
}

.oa-status-tag.pending {
  background: var(--warning-bg);
  color: var(--warning);
  border: 1rpx solid var(--warning);
}

.oa-status-tag.approved {
  background: var(--success-bg);
  color: var(--success);
  border: 1rpx solid var(--success);
}

.oa-status-tag.rejected {
  background: var(--error-bg);
  color: var(--error);
  border: 1rpx solid var(--error);
}

.oa-status-tag.draft {
  background: var(--info-bg);
  color: var(--info);
  border: 1rpx solid var(--info);
}

/* ==================== 加载状态 ==================== */

.oa-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(10rpx);
}

.oa-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
}

.oa-loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid var(--border-light);
  border-top: 6rpx solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.oa-loading-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

/* ==================== 响应式适配 ==================== */

@media (max-width: 600rpx) {
  .oa-form-row {
    flex-direction: column;
  }

  .oa-actions-section {
    flex-direction: column;
  }

  .oa-action-btn {
    height: 88rpx;
  }
}