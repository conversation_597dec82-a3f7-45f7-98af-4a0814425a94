/* components/list-item/list-item.wxss */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #fff;
  transition: background-color 0.2s ease;
}

.list-item.clickable:active {
  background: #f8f8f8;
}

.list-item.border {
  border-bottom: 1rpx solid #f0f0f0;
}

.item-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.item-icon-image {
  width: 100%;
  height: 100%;
}

.item-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 32rpx;
  color: #1a1a1a;
  line-height: 1.4;
}

.item-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 8rpx;
  line-height: 1.3;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.item-badge {
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  white-space: nowrap;
}

.item-extra {
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
}

.item-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}