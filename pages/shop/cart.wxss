/* pages/shop/cart.wxss - 购物车页面样式 V2.0 */
@import '/styles/design-system.wxss';

.cart-container {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--bg-secondary) 0%, rgba(255, 245, 230, 0.3) 100%);
  padding: var(--space-lg);
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.cart-header {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 245, 230, 0.5) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-3xl);
  margin-bottom: var(--space-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--warning-light);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
}

.cart-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--warning) 0%, var(--warning-light) 100%);
}

.cart-header::after {
  content: '🛒';
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  font-size: var(--text-xl);
  opacity: 0.3;
}

.header-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.select-all {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 245, 230, 0.3) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  backdrop-filter: blur(5rpx);
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
  animation-delay: 0.1s;
}

.select-all text {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  margin-left: var(--space-lg);
}

.checkbox {
  width: 50rpx;
  height: 50rpx;
  border: 3rpx solid var(--border-medium);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-all);
  background-color: var(--bg-primary);
}

.checkbox.checked {
  background: linear-gradient(135deg, var(--warning) 0%, var(--warning-light) 100%);
  border-color: var(--warning);
  box-shadow: var(--shadow-sm);
  transform: scale(1.05);
}

.checkmark {
  color: var(--text-inverse);
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
}

.cart-items {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 245, 230, 0.2) 100%);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  backdrop-filter: blur(5rpx);
}

.cart-item {
  display: flex;
  align-items: flex-start;
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
  position: relative;
  transition: var(--transition-all);
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
}

.cart-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, var(--warning) 0%, var(--warning-light) 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.cart-item:active {
  background-color: var(--warning-subtle);
  transform: translateX(4rpx);
}

.cart-item:active::before {
  transform: scaleY(1);
}

.cart-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  flex: 1;
  align-items: flex-start;
  margin: 0 var(--space-lg);
  gap: var(--space-lg);
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: var(--radius-xl);
  object-fit: cover;
  border: 2rpx solid var(--border-light);
  background-color: var(--bg-secondary);
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.item-name {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-price {
  font-size: var(--text-xl);
  color: var(--warning);
  font-weight: var(--font-bold);
}

.quantity-controls {
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-xl);
  border: 2rpx solid var(--border-light);
  overflow: hidden;
  margin-top: var(--space-sm);
}

.quantity-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--warning);
  transition: var(--transition-all);
  border: none;
}

.quantity-btn:active {
  background: linear-gradient(135deg, var(--warning) 0%, var(--warning-light) 100%);
  color: var(--text-inverse);
  transform: scale(0.95);
}

.quantity-input {
  width: 80rpx;
  height: 64rpx;
  text-align: center;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: none;
}

.delete-btn {
  font-size: var(--text-sm);
  color: var(--error);
  padding: var(--space-sm) var(--space-lg);
  border: 2rpx solid var(--error-light);
  border-radius: var(--radius-xl);
  background-color: var(--error-subtle);
  transition: var(--transition-all);
  margin-top: var(--space-sm);
}

.delete-btn:active {
  background: linear-gradient(135deg, var(--error) 0%, var(--error-light) 100%);
  color: var(--text-inverse);
  border-color: var(--error);
  transform: scale(0.95);
}

.empty-cart {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 245, 230, 0.3) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-4xl) var(--space-2xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 2rpx dashed var(--border-medium);
  margin: var(--space-xl) 0;
  backdrop-filter: blur(10rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.empty-cart::before {
  content: '🛒';
  display: block;
  font-size: 120rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.4;
  animation: float 3s ease-in-out infinite;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.3;
  margin-bottom: var(--space-xl);
  filter: grayscale(100%);
}

.empty-text {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-lg);
}

.go-shopping {
  background: linear-gradient(135deg, var(--warning) 0%, var(--warning-light) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-all);
}

.go-shopping:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-md);
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20rpx);
  }
}

.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 160rpx;
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 245, 230, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xl) var(--space-2xl);
  padding-bottom: calc(var(--space-xl) + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.12);
  border-top: 1rpx solid var(--border-light);
  backdrop-filter: blur(20rpx);
}

.total-price {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.total-price .label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.total-price .price {
  font-size: var(--text-2xl);
  color: var(--warning);
  font-weight: var(--font-bold);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.checkout-btn {
  background: linear-gradient(135deg, var(--warning) 0%, var(--warning-light) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-all);
  min-width: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
}

.checkout-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-md);
}

.checkout-btn::after {
  content: '→';
  font-size: var(--text-xl);
  margin-left: var(--space-xs);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}