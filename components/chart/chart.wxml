<!-- components/chart/chart.wxml -->
<view class="chart-container" style="width: {{width}}rpx; height: {{height}}rpx;">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="chart-loading">
    <c-loading text="图表加载中..."></c-loading>
  </view>
  
  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="chart-error">
    <view class="error-icon">📊</view>
    <text class="error-text">{{error}}</text>
  </view>
  
  <!-- 图表画布 -->
  <canvas 
    wx:else
    id="{{canvasId}}"
    type="2d"
    class="chart-canvas"
    style="width: {{width}}rpx; height: {{height}}rpx;"
    bindtap="onChartTap">
  </canvas>
  
  <!-- 图例 -->
  <view wx:if="{{data.length > 0 && type === 'pie'}}" class="chart-legend">
    <block wx:for="{{data}}" wx:key="index">
      <view class="legend-item">
        <view class="legend-color" style="background-color: {{themes.default[index % themes.default.length]}};"></view>
        <text class="legend-text">{{item.label}}</text>
      </view>
    </block>
  </view>
</view>