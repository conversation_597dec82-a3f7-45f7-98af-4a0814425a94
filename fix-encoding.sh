#!/bin/bash

# 修复微信小程序文件编码问题的脚本

echo "========================================="
echo "  微信小程序编码问题修复脚本"
echo "========================================="

# 检查并修复WXML文件的编码问题
echo "检查WXML文件编码..."

# 查找所有WXML文件
find . -name "*.wxml" -type f | while read file; do
    echo "检查文件: $file"
    
    # 检查是否有BOM字符
    if [ "$(head -c 3 "$file" | hexdump -ve '1/1 "%.2x"')" = "efbbbf" ]; then
        echo "发现BOM字符，正在移除..."
        # 移除BOM字符
        tail -c +4 "$file" > "$file.tmp" && mv "$file.tmp" "$file"
        echo "已移除BOM字符: $file"
    fi
    
    # 检查文件编码
    encoding=$(file -b --mime-encoding "$file")
    echo "文件编码: $encoding"
    
    # 如果不是UTF-8，转换为UTF-8
    if [ "$encoding" != "utf-8" ] && [ "$encoding" != "us-ascii" ]; then
        echo "转换编码为UTF-8..."
        iconv -f "$encoding" -t UTF-8 "$file" > "$file.tmp" && mv "$file.tmp" "$file"
        echo "已转换编码: $file"
    fi
done

# 检查JS文件
echo "检查JS文件编码..."
find . -name "*.js" -path "./pages/*" -type f | while read file; do
    echo "检查文件: $file"
    
    # 检查是否有BOM字符
    if [ "$(head -c 3 "$file" | hexdump -ve '1/1 "%.2x"')" = "efbbbf" ]; then
        echo "发现BOM字符，正在移除..."
        tail -c +4 "$file" > "$file.tmp" && mv "$file.tmp" "$file"
        echo "已移除BOM字符: $file"
    fi
done

# 检查JSON文件
echo "检查JSON配置文件编码..."
for file in app.json project.config.json project.private.config.json; do
    if [ -f "$file" ]; then
        echo "检查文件: $file"
        
        # 检查是否有BOM字符
        if [ "$(head -c 3 "$file" | hexdump -ve '1/1 "%.2x"')" = "efbbbf" ]; then
            echo "发现BOM字符，正在移除..."
            tail -c +4 "$file" > "$file.tmp" && mv "$file.tmp" "$file"
            echo "已移除BOM字符: $file"
        fi
    fi
done

echo "========================================="
echo "编码修复完成！"
echo "建议重新启动微信开发者工具以确保更改生效。"
echo "========================================="
