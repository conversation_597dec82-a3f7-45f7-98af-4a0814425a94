/* pages/logistics/logistics.wxss - 物流追踪页面样式 V2.0 */
@import '/styles/design-system.wxss';

.logistics-container {
  background: linear-gradient(180deg, var(--bg-secondary) 0%, rgba(230, 247, 255, 0.2) 100%);
  min-height: 100vh;
  padding: var(--space-lg);
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 物流信息头部 */
.logistics-header {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(230, 247, 255, 0.3) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-3xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--info-light);
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
}

.logistics-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--info) 0%, var(--info-light) 100%);
}

.logistics-header::after {
  content: '🚚';
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  font-size: var(--text-xl);
  opacity: 0.3;
}

.logistics-status {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(215deg) brightness(119%) contrast(119%);
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.logistics-company {
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.company-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.tracking-number {
  font-size: 24rpx;
  color: #999;
}

/* 物流进度 */
.logistics-progress {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(230, 247, 255, 0.2) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-3xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  backdrop-filter: blur(5rpx);
  position: relative;
  overflow: hidden;
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
  animation-delay: 0.1s;
}

.logistics-progress::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--success) 0%, var(--success-light) 100%);
}

.progress-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.progress-list {
  position: relative;
}

.progress-item {
  position: relative;
  display: flex;
  padding-bottom: 40rpx;
}

.progress-item:last-child {
  padding-bottom: 0;
}

.progress-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 6rpx;
  background: #ddd;
  margin-right: 30rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.progress-item.current .progress-dot {
  background: linear-gradient(135deg, var(--info) 0%, var(--info-light) 100%);
  box-shadow: 0 0 0 12rpx var(--info-subtle);
  animation: pulse 2s infinite;
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.progress-line {
  position: absolute;
  left: 12rpx;
  top: 32rpx;
  width: 2rpx;
  height: calc(100% - 16rpx);
  background: #eee;
  z-index: 1;
}

.progress-content {
  flex: 1;
  padding-top: 4rpx;
}

.progress-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.progress-desc {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4rpx;
}

.progress-location {
  font-size: 24rpx;
  color: #666;
}

.progress-item.current .progress-time,
.progress-item.current .progress-desc {
  color: #0066CC;
  font-weight: 500;
}

/* 信息区块 */
.order-info-section,
.goods-info-section {
  background: white;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 订单详情 */
.order-details {}

.detail-row {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

/* 商品列表 */
.goods-list {}

.goods-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  object-fit: cover;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.goods-qty {
  font-size: 24rpx;
  color: #999;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(230, 247, 255, 0.95) 100%);
  padding: var(--space-xl) var(--space-2xl);
  padding-bottom: calc(var(--space-xl) + env(safe-area-inset-bottom));
  display: flex;
  gap: var(--space-lg);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  border-top: 1rpx solid var(--border-light);
  backdrop-filter: blur(20rpx);
  z-index: 100;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: var(--radius-xl);
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.copy-btn {
  background: linear-gradient(135deg, var(--info-subtle) 0%, var(--info-light) 50%);
  color: var(--info);
  border: 2rpx solid var(--info-light);
}

.copy-btn:active {
  background: linear-gradient(135deg, var(--info) 0%, var(--info-light) 100%);
  color: var(--text-inverse);
  transform: translateY(2rpx) scale(0.98);
}

.contact-btn {
  background: linear-gradient(135deg, var(--info) 0%, var(--info-light) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.contact-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-md);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}