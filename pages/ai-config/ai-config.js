// pages/ai-config/ai-config.js
const { aiKeyManager } = require('../../utils/ai-key-manager.js');

Page({
  data: {
    providers: [],
    providerStatus: [],
    showConfigModal: false,
    currentProvider: {},
    inputApiKey: '',
    showApiKey: false,
    loading: false,
    loadingText: ''
  },

  onLoad: function (options) {
    this.loadProviderData();
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.loadProviderData();
  },

  // 加载服务提供商数据
  loadProviderData: function () {
    const providerInfo = aiKeyManager.getProviderInfo();
    const allKeys = aiKeyManager.getAllKeys();
    
    const providers = [];
    const providerStatus = [];
    
    for (const [key, info] of Object.entries(providerInfo)) {
      const keyInfo = allKeys[key];
      const provider = {
        key: key,
        name: info.name,
        description: info.description,
        website: info.website,
        keyFormat: info.keyFormat,
        features: info.features,
        featuresText: info.features.join('、'),
        configured: keyInfo.configured,
        maskedKey: keyInfo.masked
      };
      
      providers.push(provider);
      
      providerStatus.push({
        provider: key,
        name: info.name,
        description: info.description,
        configured: keyInfo.configured
      });
    }
    
    this.setData({
      providers: providers,
      providerStatus: providerStatus
    });
  },

  // 配置服务提供商
  onConfigProvider: function (e) {
    const providerKey = e.currentTarget.dataset.provider;
    const provider = this.data.providers.find(p => p.key === providerKey);
    
    if (provider) {
      this.setData({
        showConfigModal: true,
        currentProvider: provider,
        inputApiKey: '',
        showApiKey: false
      });
    }
  },

  // 关闭配置弹窗
  onCloseModal: function () {
    this.setData({
      showConfigModal: false,
      currentProvider: {},
      inputApiKey: '',
      showApiKey: false
    });
  },

  // API密钥输入
  onApiKeyInput: function (e) {
    this.setData({
      inputApiKey: e.detail.value
    });
  },

  // 切换显示/隐藏密钥
  onToggleShowKey: function () {
    this.setData({
      showApiKey: !this.data.showApiKey
    });
  },

  // 保存API密钥
  onSaveApiKey: function () {
    const { currentProvider, inputApiKey } = this.data;
    
    if (!inputApiKey.trim()) {
      wx.showToast({
        title: '请输入API密钥',
        icon: 'none'
      });
      return;
    }

    // 验证密钥格式
    if (!aiKeyManager.validateApiKey(currentProvider.key, inputApiKey)) {
      wx.showModal({
        title: '密钥格式错误',
        content: `请输入正确的${currentProvider.keyFormat}格式密钥`,
        showCancel: false
      });
      return;
    }

    this.setData({
      loading: true,
      loadingText: '保存配置中...'
    });

    try {
      // 保存密钥
      const success = aiKeyManager.setApiKey(currentProvider.key, inputApiKey);
      
      if (success) {
        wx.showToast({
          title: '配置保存成功',
          icon: 'success'
        });
        
        // 刷新数据
        this.loadProviderData();
        this.onCloseModal();
      } else {
        throw new Error('保存失败');
      }
    } catch (error) {
    try { const logger = require('../../utils/logger.js'); logger.error && logger.error('保存API密钥失败', error); } catch(_) {}
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        loadingText: ''
      });
    }
  },

  // 测试所有密钥
  onTestAllKeys: function () {
    this.setData({
      loading: true,
      loadingText: '测试密钥中...'
    });

    const testPromises = [];
    const configuredProviders = this.data.providers.filter(p => p.configured);
    
    if (configuredProviders.length === 0) {
      wx.showToast({
        title: '没有已配置的密钥',
        icon: 'none'
      });
      this.setData({
        loading: false
      });
      return;
    }

    // 测试所有已配置的密钥
    Promise.all(
      configuredProviders.map(provider => 
        aiKeyManager.testApiKey(provider.key)
          .then(valid => ({ provider: provider.name, valid }))
          .catch(() => ({ provider: provider.name, valid: false }))
      )
    ).then(results => {
      const validCount = results.filter(r => r.valid).length;
      const totalCount = results.length;
      
      let message = `测试完成：${validCount}/${totalCount} 个密钥有效\n\n`;
      results.forEach(result => {
        message += `${result.provider}: ${result.valid ? '✓ 有效' : '✗ 无效'}\n`;
      });
      
      wx.showModal({
        title: '密钥测试结果',
        content: message,
        showCancel: false
      });
    }).catch(error => {
    try { const logger = require('../../utils/logger.js'); logger.error && logger.error('测试密钥失败', error); } catch(_) {}
      wx.showToast({
        title: '测试失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({
        loading: false,
        loadingText: ''
      });
    });
  },

  // 清除所有配置
  onClearAllKeys: function () {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有API密钥配置吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            loading: true,
            loadingText: '清除配置中...'
          });

          try {
            const success = aiKeyManager.clearAllKeys();
            
            if (success) {
              wx.showToast({
                title: '配置已清除',
                icon: 'success'
              });
              
              // 刷新数据
              this.loadProviderData();
            } else {
              throw new Error('清除失败');
            }
          } catch (error) {
          try { const logger = require('../../utils/logger.js'); logger.error && logger.error('清除配置失败', error); } catch(_) {}
            wx.showToast({
              title: '清除失败，请重试',
              icon: 'none'
            });
          } finally {
            this.setData({
              loading: false,
              loadingText: ''
            });
          }
        }
      }
    });
  },

  // 分享配置
  onShareConfig: function () {
    try {
      const config = aiKeyManager.exportConfig();
      // 这里可以实现配置分享功能
      wx.showToast({
        title: '配置导出成功',
        icon: 'success'
      });
    } catch (error) {
    try { const logger = require('../../utils/logger.js'); logger.error && logger.error('导出配置失败', error); } catch(_) {}
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '智慧养鹅 - AI服务配置',
      path: '/pages/ai-config/ai-config'
    };
  }
});
