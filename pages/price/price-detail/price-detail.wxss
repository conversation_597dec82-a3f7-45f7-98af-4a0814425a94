/* pages/price/price-detail/price-detail.wxss - 四宫格价格详情版 */
.price-detail-container {
  padding: 32rpx;
  background: #f2f2f7;
  min-height: 100vh;
}

/* 类型切换标签 */
.type-tabs {
  display: flex;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 6rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.tab-item.active {
  background: #0066cc;
  color: #ffffff;
  font-weight: bold;
}

.tab-item .tab-text {
  position: relative;
  z-index: 1;
}

/* 四宫格价格展示 */
.price-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.price-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
  overflow: hidden;
}

.price-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(0, 102, 204, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.price-item:active {
  transform: scale(0.98);
}

.price-item.selected {
  border-color: #0066cc;
  background: linear-gradient(135deg, #ffffff 0%, rgba(0, 102, 204, 0.02) 100%);
}

.price-item.selected::before {
  opacity: 1;
}

.breed-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.breed-desc {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 24rpx;
  line-height: 1.3;
}

.price-info {
  margin-bottom: 16rpx;
}

.current-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #0066cc;
}

.price-unit {
  font-size: 24rpx;
  color: #666666;
  margin-left: 8rpx;
}

.price-change {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  display: inline-block;
}

.price-change.up {
  background: #e8f5e8;
  color: #52c41a;
}

.price-change.down {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 详细信息区域 */
.detail-section {
  margin-top: 32rpx;
}

/* 图表区域 */
.chart-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.time-filter {
  display: flex;
  gap: 8rpx;
}

.filter-item {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666666;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.filter-item.active {
  background-color: #0066cc;
  color: #ffffff;
}

.chart-container {
  height: 300rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.price-chart {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

/* 统计数据 */
.stats-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.stats-item {
  text-align: center;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.stats-label {
  display: block;
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.stats-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.stats-value.high {
  color: #52c41a;
}

.stats-value.low {
  color: #ff4d4f;
}

/* 市场分析 */
.analysis-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.analysis-content {
  margin-top: 24rpx;
}

.analysis-item {
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.analysis-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.analysis-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.analysis-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
}



/* 操作按钮 */
.action-section {
  display: flex;
  gap: 24rpx;
  margin-top: 32rpx;
}

.action-btn {
  flex: 1;
  padding: 28rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background-color: #0066cc;
  color: #ffffff;
}

.action-btn.primary:active {
  background-color: #0056b3;
  transform: scale(0.98);
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666666;
  border: 2rpx solid #e0e0e0;
}

.action-btn.secondary:active {
  background-color: #e9ecef;
  border-color: #d1d5db;
  transform: scale(0.98);
}

.action-btn::after {
  border: none;
}