/* pages/announcement/announcement-detail/announcement-detail.wxss */
.detail-container {
  min-height: 100vh;
  background-color: #f5f6f8;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  font-size: 28rpx;
  color: #666;
}

/* 公告详情 */
.announcement-detail {
  padding: 20rpx;
}

/* 详情头部 */
.detail-header {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.announcement-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.announcement-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.publish-time {
  font-size: 24rpx;
  color: #666;
}

.important-badge {
  padding: 4rpx 12rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A50 100%);
  color: white;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

/* 详情内容 */
.detail-content {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
}

/* 附件列表 */
.attachments {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.attachment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.attachment-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.download-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background: #0066CC;
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 28rpx;
}