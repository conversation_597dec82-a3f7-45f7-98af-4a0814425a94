<!-- 统一页面头部组件 -->
<div class="page-header-modern">
    <div class="page-header-content">
        <div class="page-title-section">
            <h1 class="page-title-modern">
                <% if (locals.icon) { %>
                    <i class="<%= icon %> me-2"></i>
                <% } %>
                <%= title || '页面标题' %>
            </h1>
            
            <% if (locals.subtitle) { %>
                <p class="page-subtitle-modern"><%= subtitle %></p>
            <% } %>
            
            <!-- 面包屑导航 -->
            <% if (locals.breadcrumb && breadcrumb.length > 0) { %>
                <div class="page-breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <% breadcrumb.forEach(function(item, index) { %>
                                <li class="breadcrumb-item <%= index === breadcrumb.length - 1 ? 'active' : '' %>">
                                    <% if (item.url && index !== breadcrumb.length - 1) { %>
                                        <a href="<%= item.url %>"><%= item.text %></a>
                                    <% } else { %>
                                        <%= item.text %>
                                    <% } %>
                                </li>
                            <% }); %>
                        </ol>
                    </nav>
                </div>
            <% } %>
        </div>
        
        <!-- 页面操作按钮 -->
        <% if (locals.actions && actions.length > 0) { %>
            <div class="page-actions-modern">
                <% actions.forEach(function(action) { %>
                    <% if (action.type === 'dropdown') { %>
                        <!-- 下拉菜单按钮 -->
                        <div class="btn-group me-2">
                            <button type="button" class="btn <%= action.class || 'btn-outline-primary' %> dropdown-toggle" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <% if (action.icon) { %>
                                    <i class="<%= action.icon %> me-1"></i>
                                <% } %>
                                <%= action.text %>
                            </button>
                            <ul class="dropdown-menu">
                                <% action.items.forEach(function(item) { %>
                                    <% if (item.divider) { %>
                                        <li><hr class="dropdown-divider"></li>
                                    <% } else { %>
                                        <li>
                                            <a class="dropdown-item" href="<%= item.url || '#' %>" 
                                               <% if (item.onclick) { %>onclick="<%= item.onclick %>"<% } %>>
                                                <% if (item.icon) { %>
                                                    <i class="<%= item.icon %> me-2"></i>
                                                <% } %>
                                                <%= item.text %>
                                            </a>
                                        </li>
                                    <% } %>
                                <% }); %>
                            </ul>
                        </div>
                    <% } else { %>
                        <!-- 普通按钮 -->
                        <button type="button" 
                                class="btn <%= action.class || 'btn-primary' %> me-2"
                                <% if (action.onclick) { %>onclick="<%= action.onclick %>"<% } %>
                                <% if (action.id) { %>id="<%= action.id %>"<% } %>
                                <% if (action.disabled) { %>disabled<% } %>>
                            <% if (action.icon) { %>
                                <i class="<%= action.icon %> me-1"></i>
                            <% } %>
                            <%= action.text %>
                        </button>
                    <% } %>
                <% }); %>
            </div>
        <% } %>
    </div>
</div>

<style>
    /* 页面头部样式 */
    .page-header-modern {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-bottom: 1px solid rgba(0,0,0,0.08);
        padding: 24px 0 20px;
        margin: 0 -15px 24px -15px;
        position: relative;
        overflow: hidden;
    }
    
    .page-header-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    }
    
    .page-header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 24px;
    }
    
    .page-title-section {
        flex: 1;
        min-width: 0;
    }
    
    .page-title-modern {
        font-size: 28px;
        font-weight: 700;
        color: var(--dark-color);
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        line-height: 1.2;
    }
    
    .page-title-modern i {
        color: var(--primary-color);
        font-size: 24px;
    }
    
    .page-subtitle-modern {
        color: #6c757d;
        font-size: 16px;
        margin: 0 0 12px 0;
        line-height: 1.4;
    }
    
    .page-breadcrumb {
        margin: 0;
    }
    
    .page-breadcrumb .breadcrumb {
        background: transparent;
        padding: 0;
        margin: 0;
        font-size: 14px;
    }
    
    .page-breadcrumb .breadcrumb-item {
        color: #6c757d;
    }
    
    .page-breadcrumb .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s ease;
    }
    
    .page-breadcrumb .breadcrumb-item a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .page-breadcrumb .breadcrumb-item.active {
        color: var(--dark-color);
        font-weight: 500;
    }
    
    .page-actions-modern {
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        gap: 8px;
        margin-left: 20px;
    }
    
    .page-actions-modern .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 10px 20px;
        font-size: 14px;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        white-space: nowrap;
    }
    
    .page-actions-modern .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .page-actions-modern .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-color: var(--primary-color);
        color: white;
    }
    
    .page-actions-modern .btn-outline-primary {
        border-color: var(--primary-color);
        color: var(--primary-color);
        background: white;
    }
    
    .page-actions-modern .btn-outline-primary:hover {
        background: var(--primary-color);
        color: white;
    }
    
    .page-actions-modern .btn-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
        border-color: var(--success-color);
        color: white;
    }
    
    .page-actions-modern .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        background: white;
    }
    
    .page-actions-modern .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px 0;
            margin: 0 -15px 16px -15px;
        }
        
        .page-header-content {
            flex-direction: column;
            align-items: stretch;
            padding: 0 16px;
        }
        
        .page-title-modern {
            font-size: 24px;
            margin-bottom: 12px;
        }
        
        .page-title-modern i {
            font-size: 20px;
        }
        
        .page-subtitle-modern {
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .page-actions-modern {
            margin-left: 0;
            margin-top: 16px;
            justify-content: flex-start;
        }
        
        .page-actions-modern .btn {
            padding: 8px 16px;
            font-size: 13px;
        }
    }
    
    @media (max-width: 576px) {
        .page-actions-modern {
            flex-direction: column;
            align-items: stretch;
        }
        
        .page-actions-modern .btn {
            margin-right: 0 !important;
            margin-bottom: 8px;
        }
    }
    
    /* 动画效果 */
    .page-header-modern {
        animation: slideDown 0.4s ease-out;
    }
    
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .page-actions-modern .btn {
        animation: fadeInUp 0.6s ease-out backwards;
    }
    
    .page-actions-modern .btn:nth-child(1) { animation-delay: 0.1s; }
    .page-actions-modern .btn:nth-child(2) { animation-delay: 0.2s; }
    .page-actions-modern .btn:nth-child(3) { animation-delay: 0.3s; }
    .page-actions-modern .btn:nth-child(4) { animation-delay: 0.4s; }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>