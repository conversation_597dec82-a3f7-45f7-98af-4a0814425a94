// pages/oa/reimbursement/list/list.js

const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info');
const { isLoggedIn, getCurrentUser } = require('../../../../utils/auth-helper');
const { formatDate } = require('../../../../utils/format');
const request = require('../../../../utils/request.js');
const { API } = require('../../../../constants/index.js');
let logger; try { logger = require('../../../../utils/logger.js'); } catch(_) { logger = console; }
const rbac = require('../../../../utils/rbac.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,

    // 列表数据
    reimbursements: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      hasMore: true
    },

    // 筛选和搜索
    filters: {
      status: 'all',
      category: 'all',
      timeRange: 'all',
      keyword: ''
    },

    // 状态选项
    statusOptions: [
      { value: 'all', label: '全部状态', count: 0 },
      { value: 'draft', label: '草稿', count: 0, color: '#8E8E93' },
      { value: 'submitted', label: '待审批', count: 0, color: '#FF9500' },
      { value: 'approved', label: '已批准', count: 0, color: '#00A86B' },
      { value: 'rejected', label: '已拒绝', count: 0, color: '#FF3B30' },
      { value: 'paid', label: '已付款', count: 0, color: '#007AFF' },
      { value: 'cancelled', label: '已取消', count: 0, color: '#8E8E93' }
    ],

    categoryOptions: [
      { value: 'all', label: '全部类别' },
      { value: 'travel', label: '差旅费' },
      { value: 'office', label: '办公用品' },
      { value: 'meal', label: '餐费' },
      { value: 'transport', label: '交通费' },
      { value: 'communication', label: '通讯费' },
      { value: 'training', label: '培训费' },
      { value: 'entertainment', label: '招待费' },
      { value: 'other', label: '其他' }
    ],

    timeRangeOptions: [
      { value: 'all', label: '全部时间' },
      { value: 'today', label: '今天' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' }
    ],

    // 选择器索引
    categoryIndex: 0, // 默认为'all'
    timeRangeIndex: 0, // 默认为'all'

    // 显示标签
    selectedCategoryLabel: '全部类别',
    selectedTimeRangeLabel: '全部时间',

    // 页面状态
    loading: false,
    refreshing: false,
    showFilters: false,

    // 统计数据
    statistics: {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      totalAmount: 0
    }
  },

  onLoad(options) {
    this.initPage(options);
  },

  onShow() {
    this.loadReimbursementList(true);
  },

  onPullDownRefresh() {
    this.loadReimbursementList(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadReimbursementList(false);
    }
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    // 使用auth-helper进行可靠的登录检查

    if (!isLoggedIn()) {

      wx.showModal({

        title: '需要登录',

        content: '使用此功能需要先登录系统',

        confirmText: '去登录',

        cancelText: '取消',

        success: (res) => {

          if (res.confirm) {

            wx.redirectTo({ url: '/pages/login/login' });

          } else {

            wx.navigateBack();

          }

        }

      });

      return;

    }


    // 获取用户信息并同步数据

    const authUser = getCurrentUser();

    let userInfo = getCurrentUserInfo();


    if (!userInfo && authUser) {

      const app = getApp();

      app.globalData.userInfo = authUser;

      wx.setStorageSync('user_info', authUser);

      userInfo = getCurrentUserInfo();

    }

    

    const permissions = getUserPermissions();


    if (!userInfo) {

      wx.showModal({

        title: '数据错误',

        content: '无法获取用户信息，请重新登录',

        confirmText: '重新登录',

        showCancel: false,

        success: () => {

          wx.redirectTo({ url: '/pages/login/login' });

        }

      });

      return;

    }

    // 检查权限（兼容旧权限结构 + RBAC）
    const canViewList = permissions.canCreateApplication || permissions.canApprove || 
      rbac.hasAny(userInfo, [rbac.PERMISSIONS.OA.ACCESS, rbac.PERMISSIONS.OA.REIMBURSEMENT_VIEW, rbac.PERMISSIONS.OA.FINANCE_VIEW]);
    if (!canViewList) {
      wx.showModal({
        title: '没有权限',
        content: '您没有查看报销申请的权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    // 处理URL参数
    const listType = options.type || 'all';
    const statusFilter = options.status || 'all';
    
    this.setData({
      userInfo,
      permissions,
      listType,
      'filters.status': statusFilter
    });

    // 如果从统计概览跳转过来，需要更新状态标签的激活状态
    if (options.from === 'statistics' && statusFilter !== 'all') {
      this.updateStatusSelection(statusFilter);
    }

    // 设置页面标题
    const titles = {
      all: '全部报销',
      my: '我的申请',
      pending: '待审批',
      approved: '已批准',
      rejected: '已拒绝'
    };

    wx.setNavigationBarTitle({
      title: titles[this.data.listType] || '报销列表'
    });

    // 初始化选择器索引
    this.updateFilterIndexes();
  },

  /**
   * 更新筛选器索引
   */
  updateFilterIndexes() {
    const { filters, categoryOptions, timeRangeOptions } = this.data;

    // 更新类别索引
    const categoryIndex = categoryOptions.findIndex(item => item.value === filters.category);
    const selectedCategory = categoryOptions[categoryIndex >= 0 ? categoryIndex : 0];

    // 更新时间范围索引
    const timeRangeIndex = timeRangeOptions.findIndex(item => item.value === filters.timeRange);
    const selectedTimeRange = timeRangeOptions[timeRangeIndex >= 0 ? timeRangeIndex : 0];

    this.setData({
      categoryIndex: categoryIndex >= 0 ? categoryIndex : 0,
      timeRangeIndex: timeRangeIndex >= 0 ? timeRangeIndex : 0,
      selectedCategoryLabel: selectedCategory.label,
      selectedTimeRangeLabel: selectedTimeRange.label
    });
  },

  /**
   * 加载报销申请列表
   */
  async loadReimbursementList(refresh = false) {
    if (this.data.loading) return;

    if (refresh) {
      this.setData({
        refreshing: true,
        'pagination.page': 1,
        'pagination.hasMore': true
      });
    } else {
      this.setData({ loading: true });
    }

    try {
      const { pagination, filters, listType } = this.data;

      const params = {
        page: refresh ? 1 : pagination.page,
        limit: pagination.limit,
        type: listType,
        ...filters
      };

      const res = await request.get(API.ENDPOINTS.OA.REIMBURSEMENT.LIST, params);

      if (res && res.success !== false) {
        const { list, pagination: newPagination } = res.data || res;

        let reimbursements = [];
        if (refresh) {
          reimbursements = list;
        } else {
          reimbursements = [...this.data.reimbursements, ...list];
        }

        this.setData({
          reimbursements,
          'pagination.page': newPagination.page,
          'pagination.total': newPagination.total,
          'pagination.hasMore': newPagination.page < newPagination.pages
        });

        if (refresh) this.loadStatistics();
      } else {
        throw new Error(res?.message || '加载失败');
      }
    } catch (error) {
      try { logger.error && logger.error('加载报销申请列表失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const res = await request.get(API.ENDPOINTS.OA.REIMBURSEMENT.STATISTICS);
      if (res && res.success !== false) {
        const data = res.data || res;
        this.setData({ statistics: data });
        this.updateStatusCounts(data);
      }
    } catch (error) {
      try { logger.error && logger.error('加载统计数据失败', error); } catch(_) {}
    }
  },

  /**
   * 更新状态计数
   */
  updateStatusCounts(statistics) {
    const statusOptions = this.data.statusOptions.map(option => {
      switch (option.value) {
        case 'all':
          return { ...option, count: statistics.total };
        case 'submitted':
          return { ...option, count: statistics.pending };
        case 'approved':
          return { ...option, count: statistics.approved };
        case 'rejected':
          return { ...option, count: statistics.rejected };
        default:
          return option;
      }
    });

    this.setData({ statusOptions });
  },

  /**
   * 显示/隐藏筛选器
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      'filters.status': status
    });
    this.loadReimbursementList(true);
  },

  /**
   * 类别筛选
   */
  onCategoryFilter(e) {
    const categoryIndex = e.detail.value;
    const selectedCategory = this.data.categoryOptions[categoryIndex];

    this.setData({
      'filters.category': selectedCategory.value,
      categoryIndex: categoryIndex,
      selectedCategoryLabel: selectedCategory.label
    });
    this.loadReimbursementList(true);
  },

  /**
   * 时间范围筛选
   */
  onTimeRangeFilter(e) {
    const timeRangeIndex = e.detail.value;
    const selectedRange = this.data.timeRangeOptions[timeRangeIndex];

    this.setData({
      'filters.timeRange': selectedRange.value,
      timeRangeIndex: timeRangeIndex,
      selectedTimeRangeLabel: selectedRange.label
    });
    this.loadReimbursementList(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      'filters.keyword': keyword
    });

    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadReimbursementList(true);
    }, 500);
  },

  /**
   * 清除筛选
   */
  clearFilters() {
    this.setData({
      filters: {
        status: 'all',
        category: 'all',
        timeRange: 'all',
        keyword: ''
      },
      showFilters: false,
      categoryIndex: 0,
      timeRangeIndex: 0,
      selectedCategoryLabel: '全部类别',
      selectedTimeRangeLabel: '全部时间'
    });
    this.loadReimbursementList(true);
  },

  /**
   * 查看详情
   */
  onViewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/oa/reimbursement/detail/detail?id=${id}`
    });
  },

  /**
   * 新建报销申请
   */
  onCreateReimbursement() {
    wx.navigateTo({
      url: '/pages/oa/reimbursement/apply/apply'
    });
  },

  /**
   * 编辑报销申请
   */
  onEditReimbursement(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/oa/reimbursement/apply/apply?id=${id}`
    });
  },

  /**
   * 删除报销申请
   */
  onDeleteReimbursement(e) {
    const { id, title } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除报销申请"${title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteReimbursement(id);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async deleteReimbursement(id) {
    try {
      const res = await request.del(API.ENDPOINTS.OA.REIMBURSEMENT.DELETE(id));
      if (res && res.success !== false) {
        wx.showToast({ title: '删除成功', icon: 'success' });
        this.loadReimbursementList(true);
      } else {
        throw new Error(res?.message || '删除失败');
      }
    } catch (error) {
      try { logger.error && logger.error('删除报销申请失败', error); } catch(_) {}
      wx.showToast({ title: error.message || '删除失败', icon: 'none' });
    }
  },

  /**
   * 更新状态标签选中状态
   */
  updateStatusSelection: function(status) {
    // 这个方法用于确保从统计概览跳转过来时状态标签显示正确
    // 状态过滤器已经在 initPage 中设置，这里主要是确保UI状态同步
    try { logger.debug && logger.debug('Status filter updated to', status); } catch(_) {}
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  }
});