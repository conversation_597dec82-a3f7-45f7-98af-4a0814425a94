/* components/modal/modal.wxss */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 遮罩层 */
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
}

/* 弹窗包装器 */
.modal-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 40rpx;
  box-sizing: border-box;
}

/* 弹窗内容 */
.modal-content {
  position: relative;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: scale(0.9) translateY(-50rpx);
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 关闭按钮 */
.modal-close {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  z-index: 1;
  transition: all 0.2s ease;
}

.modal-close:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}

.close-icon {
  font-size: 32rpx;
  color: #999;
  line-height: 1;
}

/* 标题区域 */
.modal-header {
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid var(--border-light);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

/* 内容区域 */
.modal-body {
  flex: 1;
  padding: 32rpx 40rpx;
  overflow-y: auto;
  min-height: 0;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
}

.modal-slot {
  width: 100%;
}

/* 底部按钮区域 */
.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 40rpx 40rpx;
  border-top: 1rpx solid var(--border-light);
}

.modal-footer.single-btn {
  padding: 32rpx 40rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn.full-width {
  width: 100%;
}

.cancel-btn {
  background-color: var(--bg-disabled);
  color: #666;
}

.cancel-btn:active {
  background-color: #e0e0e0;
}

.confirm-btn {
  background-color: var(--primary);
  color: #fff;
}

.confirm-btn:active {
  background-color: var(--primary-dark);
}

/* 不同类型的弹窗样式 */
.modal-default {
  /* 默认样式 */
}

.modal-confirm .modal-body {
  text-align: center;
  padding: 40rpx;
}

.modal-alert .modal-body {
  text-align: center;
  padding: 40rpx;
}

.modal-alert .modal-text {
  color: #333;
  font-size: 30rpx;
}

.modal-custom {
  /* 自定义类型样式 */
}

/* 底部插槽 */
.modal-footer-slot {
  border-top: 1rpx solid var(--border-light);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .modal-wrapper {
    padding: 32rpx;
  }

  .modal-content {
    border-radius: 12rpx;
  }

  .modal-header {
    padding: 32rpx 32rpx 16rpx;
  }

  .modal-body {
    padding: 24rpx 32rpx;
  }

  .modal-footer {
    padding: 20rpx 32rpx 32rpx;
  }

  .modal-title {
    font-size: 32rpx;
  }

  .modal-text {
    font-size: 26rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background-color: #1f1f1f;
  }

  .modal-title {
    color: #fff;
  }

  .modal-text {
    color: #ccc;
  }

  .modal-header,
  .modal-footer,
  .modal-footer-slot {
    border-color: #333;
  }

  .modal-close {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .close-icon {
    color: #ccc;
  }

  .cancel-btn {
    background-color: #333;
    color: #ccc;
  }
}