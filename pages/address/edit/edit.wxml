<!-- pages/address/edit/edit.wxml - 地址编辑页面 -->
<view class="address-edit-page">
  <scroll-view class="edit-content" scroll-y="true">
    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 收件人信息 -->
      <view class="form-group">
        <view class="group-header">
          <text class="group-icon">👤</text>
          <text class="group-title">收件人信息</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">姓名</text>
          <input class="item-input" 
                 type="text" 
                 placeholder="请输入收件人姓名" 
                 value="{{formData.name}}"
                 bindinput="onInputChange"
                 data-field="name"
                 maxlength="20" />
          <text class="required-mark">*</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">手机号</text>
          <input class="item-input" 
                 type="number" 
                 placeholder="请输入手机号码" 
                 value="{{formData.phone}}"
                 bindinput="onInputChange"
                 data-field="phone"
                 maxlength="11" />
          <text class="required-mark">*</text>
        </view>
      </view>
      
      <!-- 地址信息 -->
      <view class="form-group">
        <view class="group-header">
          <text class="group-icon">📍</text>
          <text class="group-title">收货地址</text>
        </view>
        
        <!-- 省市区选择 -->
        <view class="form-item">
          <text class="item-label">所在地区</text>
          <picker mode="region" 
                  bindchange="onRegionChange" 
                  value="{{formData.region}}">
            <view class="region-picker-display">
              <text class="region-picker-text {{formData.region && formData.region.length === 3 ? '' : 'placeholder'}}">
                {{formData.region && formData.region.length === 3 ? formData.region[0] + ' ' + formData.region[1] + ' ' + formData.region[2] : '请选择省市区'}}
              </text>
              <text class="region-picker-arrow">></text>
            </view>
          </picker>
          <text class="required-mark">*</text>
        </view>
        
        <!-- 详细地址 -->
        <view class="form-item textarea-item">
          <text class="item-label">详细地址</text>
          <textarea class="item-textarea" 
                    placeholder="请输入详细地址（街道、门牌号等）" 
                    value="{{formData.detail}}"
                    bindinput="onInputChange"
                    data-field="detail"
                    maxlength="100"
                    auto-height="true"
                    show-confirm-bar="false" />
          <text class="required-mark">*</text>
        </view>
      </view>
      
      <!-- 地址标签 -->
      <view class="form-group">
        <view class="group-header">
          <text class="group-icon">🏷️</text>
          <text class="group-title">地址标签</text>
          <text class="group-desc">（可选）</text>
        </view>
        
        <view class="tag-selector">
          <view class="tag-item {{formData.tag === '家' ? 'active' : ''}}" 
                bindtap="onSelectTag" 
                data-tag="家">
            <text class="tag-icon">🏠</text>
            <text class="tag-text">家</text>
          </view>
          <view class="tag-item {{formData.tag === '公司' ? 'active' : ''}}" 
                bindtap="onSelectTag" 
                data-tag="公司">
            <text class="tag-icon">🏢</text>
            <text class="tag-text">公司</text>
          </view>
          <view class="tag-item {{formData.tag === '学校' ? 'active' : ''}}" 
                bindtap="onSelectTag" 
                data-tag="学校">
            <text class="tag-icon">🎓</text>
            <text class="tag-text">学校</text>
          </view>
          <view class="tag-item custom-tag">
            <input class="custom-input" 
                   type="text" 
                   placeholder="自定义" 
                   value="{{formData.customTag}}"
                   bindinput="onInputChange"
                   data-field="customTag"
                   maxlength="10" />
          </view>
        </view>
      </view>
      
      <!-- 设置选项 -->
      <view class="form-group">
        <view class="group-header">
          <text class="group-icon">⚙️</text>
          <text class="group-title">设置选项</text>
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-content">
            <text class="switch-label">设为默认地址</text>
            <text class="switch-desc">下次下单时优先使用此地址</text>
          </view>
          <switch class="switch-control" 
                  checked="{{formData.isDefault}}" 
                  bindchange="onSwitchChange"
                  data-field="isDefault"
                  color="#007AFF" />
        </view>
      </view>
    </view>
    
    <!-- 底部间距 -->
    <view class="form-bottom-space"></view>
  </scroll-view>
  
  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-buttons">
      <view class="action-btn cancel-btn" bindtap="onCancel">
        <text class="btn-text">取消</text>
      </view>
      <view class="action-btn save-btn {{isFormValid ? '' : 'disabled'}}" bindtap="onSave">
        <text class="btn-text">{{isEdit ? '保存' : '添加'}}</text>
      </view>
    </view>
  </view>
</view>