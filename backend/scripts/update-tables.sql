-- 使用数据库
USE zhihuiyange_local;

-- 更新用户表结构
ALTER TABLE users 
  ADD COLUMN IF NOT EXISTS name VARCHAR(100),
  ADD COLUMN IF NOT EXISTS farm_name VARCHAR(100),
  ADD COLUMN IF NOT EXISTS phone VARCHAR(20);

-- 确保外键约束存在
SET FOREIGN_KEY_CHECKS = 0;

-- 重新创建健康记录表
DROP TABLE IF EXISTS health_records;
CREATE TABLE health_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  goose_id VARCHAR(50) NOT NULL,
  symptoms TEXT,
  diagnosis TEXT,
  treatment TEXT,
  status ENUM('pending', 'processing', 'completed') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 重新创建生产记录表
DROP TABLE IF EXISTS production_records;
CREATE TABLE production_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  egg_count INT DEFAULT 0,
  feed_consumption DECIMAL(10,2) DEFAULT 0,
  temperature DECIMAL(5,2),
  humidity DECIMAL(5,2),
  notes TEXT,
  recorded_date DATE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

SET FOREIGN_KEY_CHECKS = 1;