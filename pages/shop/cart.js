// pages/shop/cart.js
Page({
  data: {
    cartItems: [],
    selectedItems: [],
    totalPrice: 0,
    isAllSelected: false,
    selectedCount: 0
  },

  onShow: function () {
    this.loadCartData();
  },

  loadCartData: function() {
    const cart = wx.getStorageSync('cart') || [];
    const selectedItems = this.data.selectedItems;
    
    // 为每个商品项添加选中状态
    const cartItems = cart.map(item => {
      return {
        ...item,
        selected: selectedItems.includes(item.id)
      };
    });
    
    this.setData({
      cartItems: cartItems
    });
    
    this.calculateTotal();
  },

  calculateTotal: function() {
    let totalPrice = 0;
    let selectedCount = 0;
    
    this.data.cartItems.forEach(item => {
      if (item.selected) {
        totalPrice += item.price * item.quantity;
        selectedCount++;
      }
    });
    
    this.setData({
      totalPrice: totalPrice.toFixed(2),
      isAllSelected: selectedCount > 0 && selectedCount === this.data.cartItems.length,
      selectedCount: selectedCount
    });
  },

  onSelectItem: function(e) {
    const id = e.currentTarget.dataset.id;
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id) {
        return {
          ...item,
          selected: !item.selected
        };
      }
      return item;
    });
    
    this.setData({
      cartItems: cartItems
    });
    
    this.calculateTotal();
  },

  onSelectAll: function() {
    const isAllSelected = !this.data.isAllSelected;
    const cartItems = this.data.cartItems.map(item => {
      return {
        ...item,
        selected: isAllSelected
      };
    });
    
    this.setData({
      cartItems: cartItems,
      isAllSelected: isAllSelected
    });
    
    this.calculateTotal();
  },

  onQuantityChange: function(e) {
    const id = e.currentTarget.dataset.id;
    const value = e.detail.value;
    
    if (value > 0) {
      const cartItems = this.data.cartItems.map(item => {
        if (item.id === id) {
          return {
            ...item,
            quantity: parseInt(value)
          };
        }
        return item;
      });
      
      this.setData({
        cartItems: cartItems
      });
      
      // 更新本地存储
      this.updateCartStorage(cartItems);
      this.calculateTotal();
    }
  },
  
  onQuantityMinus: function(e) {
    const id = e.currentTarget.dataset.id;
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id && item.quantity > 1) {
        return {
          ...item,
          quantity: item.quantity - 1
        };
      }
      return item;
    });
    
    this.setData({
      cartItems: cartItems
    });
    
    // 更新本地存储
    this.updateCartStorage(cartItems);
    this.calculateTotal();
  },
  
  onQuantityPlus: function(e) {
    const id = e.currentTarget.dataset.id;
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id) {
        return {
          ...item,
          quantity: item.quantity + 1
        };
      }
      return item;
    });
    
    this.setData({
      cartItems: cartItems
    });
    
    // 更新本地存储
    this.updateCartStorage(cartItems);
    this.calculateTotal();
  },

  onDeleteItem: function(e) {
    const id = e.currentTarget.dataset.id;
    const cartItems = this.data.cartItems.filter(item => item.id !== id);
    
    this.setData({
      cartItems: cartItems
    });
    
    // 更新本地存储
    this.updateCartStorage(cartItems);
    this.calculateTotal();
    
    wx.showToast({
      title: '已删除',
      icon: 'success',
      duration: 1000
    });
  },

  updateCartStorage: function(cartItems) {
    // 过滤掉数量为0的商品
    const validItems = cartItems.filter(item => item.quantity > 0);
    wx.setStorageSync('cart', validItems);
  },

  onCheckout: function() {
    const selectedItems = this.data.cartItems.filter(item => item.selected);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到结算页面
    wx.navigateTo({
      url: '/pages/shop/checkout'
    });
  }
});