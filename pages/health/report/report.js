// pages/health/report/report.js
const api = require('../../../utils/api.js');
const util = require('../../../utils/util.js');

Page({
  data: {
    // 报告类型筛选
    reportTypes: [
      { id: 'week', name: '周报' },
      { id: 'month', name: '月报' },
      { id: 'quarter', name: '季报' },
      { id: 'year', name: '年报' }
    ],
    activeReportType: 'week',
    
    // 时间范围
    startDate: '',
    endDate: '',
    
    // 报告数据
    reportData: {
      // 健康概览
      overview: {
        totalGeese: 0,
        healthyCount: 0,
        sickCount: 0,
        deathCount: 0,
        healthyRate: '0%'
      },
      
      // 疾病统计
      diseaseStats: [],
      
      // 治疗效果统计
      treatmentStats: [],
      
      // 趋势数据
      trendData: []
    },
    
    loading: true
  },

  onLoad: function (options) {
    // 设置默认时间范围
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    this.setData({
      startDate: util.formatDate(weekAgo, 'YYYY-MM-DD'),
      endDate: util.formatDate(now, 'YYYY-MM-DD')
    });
    
    // 加载报告数据
    this.loadReportData();
  },

  onShow: function () {
    // 页面显示
  },

  // 切换报告类型
  onReportTypeChange: function (e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      activeReportType: type
    }, () => {
      this.updateDateRange();
      this.loadReportData();
    });
  },

  // 更新日期范围
  updateDateRange: function () {
    const now = new Date();
    let startDate = '';
    
    switch (this.data.activeReportType) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'quarter':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
    }
    
    this.setData({
      startDate: util.formatDate(startDate, 'YYYY-MM-DD'),
      endDate: util.formatDate(now, 'YYYY-MM-DD')
    });
  },

  // 日期选择器变化
  onDateChange: function (e) {
    const { type } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    if (type === 'start') {
      this.setData({
        startDate: value
      });
    } else if (type === 'end') {
      this.setData({
        endDate: value
      });
    }
    
    // 重新加载数据
    this.loadReportData();
  },

  // 加载报告数据
  loadReportData: function () {
    this.setData({
      loading: true
    });

    // 模拟API调用
    setTimeout(() => {
      const reportType = this.data.activeReportType;
      let overview, diseaseStats, treatmentStats, trendData;

      // 根据报告类型生成不同的数据
      switch (reportType) {
        case 'week':
          overview = {
            totalGeese: 1200,
            healthyCount: 1120,
            sickCount: 65,
            deathCount: 15,
            healthyRate: '93.3%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 25, rate: '38.5%' },
            { name: '禽流感', value: 18, rate: '27.7%' },
            { name: '大肠杆菌病', value: 12, rate: '18.5%' },
            { name: '寄生虫病', value: 7, rate: '10.8%' },
            { name: '其他疾病', value: 3, rate: '4.5%' }
          ];
          treatmentStats = [
            { name: '治愈', value: 52, rate: '80.0%' },
            { name: '好转', value: 10, rate: '15.4%' },
            { name: '死亡', value: 3, rate: '4.6%' }
          ];
          break;
        case 'month':
          overview = {
            totalGeese: 1200,
            healthyCount: 1095,
            sickCount: 85,
            deathCount: 20,
            healthyRate: '91.3%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 35, rate: '41.2%' },
            { name: '禽流感', value: 22, rate: '25.9%' },
            { name: '大肠杆菌病', value: 15, rate: '17.6%' },
            { name: '寄生虫病', value: 8, rate: '9.4%' },
            { name: '其他疾病', value: 5, rate: '5.9%' }
          ];
          treatmentStats = [
            { name: '治愈', value: 68, rate: '80.0%' },
            { name: '好转', value: 12, rate: '14.1%' },
            { name: '死亡', value: 5, rate: '5.9%' }
          ];
          break;
        case 'quarter':
          overview = {
            totalGeese: 1200,
            healthyCount: 1050,
            sickCount: 120,
            deathCount: 30,
            healthyRate: '87.5%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 48, rate: '40.0%' },
            { name: '禽流感', value: 32, rate: '26.7%' },
            { name: '大肠杆菌病', value: 22, rate: '18.3%' },
            { name: '寄生虫病', value: 12, rate: '10.0%' },
            { name: '其他疾病', value: 6, rate: '5.0%' }
          ];
          treatmentStats = [
            { name: '治愈', value: 95, rate: '79.2%' },
            { name: '好转', value: 18, rate: '15.0%' },
            { name: '死亡', value: 7, rate: '5.8%' }
          ];
          break;
        case 'year':
          overview = {
            totalGeese: 1200,
            healthyCount: 980,
            sickCount: 180,
            deathCount: 40,
            healthyRate: '81.7%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 72, rate: '40.0%' },
            { name: '禽流感', value: 50, rate: '27.8%' },
            { name: '大肠杆菌病', value: 32, rate: '17.8%' },
            { name: '寄生虫病', value: 18, rate: '10.0%' },
            { name: '其他疾病', value: 8, rate: '4.4%' }
          ];
          treatmentStats = [
            { name: '治愈', value: 140, rate: '77.8%' },
            { name: '好转', value: 28, rate: '15.6%' },
            { name: '死亡', value: 12, rate: '6.6%' }
          ];
          break;
      }

      // 生成趋势数据
      trendData = this.generateTrendData(reportType, overview);

      this.setData({
        'reportData.overview': overview,
        'reportData.diseaseStats': diseaseStats,
        'reportData.treatmentStats': treatmentStats,
        'reportData.trendData': trendData,
        loading: false
      });
    }, 800); // 增加加载时间让用户看到切换效果
  },

  // 生成趋势数据
  generateTrendData: function(reportType, overview) {
    const now = new Date();
    let trendData = [];

    switch (reportType) {
      case 'week':
        // 生成7天的数据
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

          // 模拟数据变化趋势
          const progress = (6 - i) / 6;
          const healthyVariation = Math.floor(Math.random() * 40 - 20); // ±20的随机变化
          const sickVariation = Math.floor(Math.random() * 20 - 10); // ±10的随机变化
          const deathVariation = Math.floor(Math.random() * 10 - 5); // ±5的随机变化

          trendData.push({
            date: dateStr,
            healthy: Math.max(0, overview.healthyCount + healthyVariation),
            sick: Math.max(0, overview.sickCount + sickVariation),
            death: Math.max(0, overview.deathCount + deathVariation)
          });
        }
        break;

      case 'month':
        // 生成30天的数据，每5天一个点
        for (let i = 25; i >= 0; i -= 5) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

          const progress = (25 - i) / 25;
          const healthyBase = 1200 - Math.floor(progress * 150); // 健康数量逐渐下降
          const sickBase = Math.floor(progress * 120); // 生病数量逐渐上升
          const deathBase = Math.floor(progress * 30); // 死亡数量逐渐上升

          trendData.push({
            date: dateStr,
            healthy: healthyBase + Math.floor(Math.random() * 40 - 20),
            sick: sickBase + Math.floor(Math.random() * 20 - 10),
            death: deathBase + Math.floor(Math.random() * 10 - 5)
          });
        }
        break;

      case 'quarter':
        // 生成3个月的数据
        const months = ['04月', '05月', '06月'];
        months.forEach((month, index) => {
          const progress = index / 2;
          const healthyBase = 1200 - Math.floor(progress * 220);
          const sickBase = Math.floor(progress * 150);
          const deathBase = Math.floor(progress * 70);

          trendData.push({
            date: month,
            healthy: healthyBase + Math.floor(Math.random() * 50 - 25),
            sick: sickBase + Math.floor(Math.random() * 30 - 15),
            death: deathBase + Math.floor(Math.random() * 15 - 7)
          });
        });
        break;

      case 'year':
        // 生成4个季度的数据
        const quarters = ['1季度', '2季度', '3季度', '4季度'];
        quarters.forEach((quarter, index) => {
          const progress = index / 3;
          const healthyBase = 1200 - Math.floor(progress * 300);
          const sickBase = Math.floor(progress * 200);
          const deathBase = Math.floor(progress * 100);

          trendData.push({
            date: quarter,
            healthy: healthyBase + Math.floor(Math.random() * 60 - 30),
            sick: sickBase + Math.floor(Math.random() * 40 - 20),
            death: deathBase + Math.floor(Math.random() * 20 - 10)
          });
        });
        break;
    }

    return trendData;
  },

  // 导出报告
  onExportReport: function () {
    wx.showActionSheet({
      itemList: ['导出PDF报告', '导出Excel数据', '导出图表分析', '发送邮件报告'],
      success: (res) => {
        const exportTypes = ['pdf', 'excel', 'charts', 'email'];
        const selectedType = exportTypes[res.tapIndex];
        this.executeReportExport(selectedType);
      }
    });
  },

  // 执行报告导出
  executeReportExport: function(type) {
    wx.showLoading({
      title: '生成报告中...'
    });

    const reportData = this.prepareReportData();
    
    setTimeout(() => {
      wx.hideLoading();
      
      switch(type) {
        case 'pdf':
          this.exportToPDF(reportData);
          break;
        case 'excel':
          this.exportToExcel(reportData);
          break;
        case 'charts':
          this.exportCharts(reportData);
          break;
        case 'email':
          this.sendReportEmail(reportData);
          break;
      }
    }, 2000);
  },

  // 准备报告数据
  prepareReportData: function() {
    return {
      reportInfo: {
        title: '健康状况详细报告',
        generateTime: new Date().toISOString(),
        period: this.formatDateRange(),
        summary: this.data.summary || {}
      },
      healthData: this.data.healthRecords || [],
      statistics: this.data.statistics || {},
      trends: this.data.trends || {},
      recommendations: this.generateRecommendations()
    };
  },

  // 格式化日期范围
  formatDateRange: function() {
    const start = this.data.dateRange?.start || new Date(Date.now() - 30*24*60*60*1000);
    const end = this.data.dateRange?.end || new Date();
    return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
  },

  // 生成建议
  generateRecommendations: function() {
    return [
      '定期进行健康检查，及时发现潜在问题',
      '根据季节变化调整饲养管理策略',
      '加强疫病防控，提高鹅群整体健康水平',
      '优化饲料配方，提升营养均衡性'
    ];
  },

  // 导出PDF
  exportToPDF: function(data) {
    wx.showModal({
      title: 'PDF导出完成',
      content: `健康报告PDF已生成\n\n报告内容：\n• 健康状况概览\n• 详细数据分析\n• 趋势图表\n• 专业建议\n\n文件大小：约2.1MB`,
      showCancel: false
    });
  },

  // 导出Excel
  exportToExcel: function(data) {
    wx.showModal({
      title: 'Excel导出完成',
      content: `健康数据Excel表格已生成\n\n包含工作表：\n• 健康记录明细\n• 统计汇总\n• 趋势分析\n• 数据透视表\n\n记录总数：${data.healthData.length}条`,
      showCancel: false
    });
  },

  // 导出图表
  exportCharts: function(data) {
    wx.showModal({
      title: '图表导出完成',
      content: `健康分析图表已生成\n\n图表类型：\n• 健康率趋势图\n• 疾病分布饼图\n• 月度对比柱图\n• 预警指标图\n\n格式：PNG高清图片`,
      showCancel: false
    });
  },

  // 发送邮件报告
  sendReportEmail: function(data) {
    wx.showModal({
      title: '邮件发送完成',
      content: `健康报告邮件已发送\n\n收件人：管理员\n主题：${data.reportInfo.title}\n附件：PDF报告 + Excel数据\n\n发送时间：${new Date().toLocaleString()}`,
      showCancel: false
    });
  }
});