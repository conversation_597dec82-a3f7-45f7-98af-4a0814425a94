/**
 * 认证中间件
 */

// 检查用户是否已登录
const requireAuth = (req, res, next) => {
  if (req.session && req.session.user) {
    return next();
  }

  // 如果是AJAX请求，返回JSON错误
  if (req.xhr || req.headers.accept?.indexOf("json") > -1) {
    return res.status(401).json({
      success: false,
      message: "请先登录",
      redirect: "/login",
    });
  }

  // 重定向到登录页面
  res.redirect("/login");
};

// 检查用户角色权限
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.session || !req.session.user) {
      return res.status(401).json({
        success: false,
        message: "请先登录",
      });
    }

    const userRole = req.session.user.role || "user";

    if (roles.includes(userRole)) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: "权限不足",
    });
  };
};

// 检查是否已登录（用于登录页面重定向）
const redirectIfAuthenticated = (req, res, next) => {
  if (req.session && req.session.user) {
    return res.redirect("/dashboard");
  }
  next();
};

module.exports = {
  requireAuth,
  requireRole,
  redirectIfAuthenticated,
};
