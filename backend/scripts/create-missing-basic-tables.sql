-- 创建缺失的基础表，确保与模型定义一致
-- 基于开发规范创建标准表结构

USE smart_goose;

-- 角色表（独立的基础角色表）
CREATE TABLE IF NOT EXISTS roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
  display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
  description TEXT COMMENT '角色描述',
  permissions JSON COMMENT '权限列表',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_name (name),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基础角色表';

-- 权限表（独立的基础权限表）
CREATE TABLE IF NOT EXISTS permissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE COMMENT '权限名称',
  code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
  resource VARCHAR(100) COMMENT '资源',
  action VARCHAR(50) COMMENT '操作',
  description TEXT COMMENT '权限描述',
  category VARCHAR(50) DEFAULT 'general' COMMENT '权限分类',
  level INT DEFAULT 1 COMMENT '权限级别',
  is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
  is_system TINYINT(1) DEFAULT 0 COMMENT '是否系统权限',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_code (code),
  INDEX idx_resource (resource),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基础权限表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  role_id INT NOT NULL COMMENT '角色ID',
  permission_id INT NOT NULL COMMENT '权限ID',
  granted_by INT COMMENT '授权人ID',
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  UNIQUE KEY uk_role_permission (role_id, permission_id),
  INDEX idx_role_id (role_id),
  INDEX idx_permission_id (permission_id),
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
  FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  role_id INT NOT NULL COMMENT '角色ID',
  assigned_by INT COMMENT '分配人ID',
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  expires_at DATETIME COMMENT '过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  UNIQUE KEY uk_user_role (user_id, role_id),
  INDEX idx_user_id (user_id),
  INDEX idx_role_id (role_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 用户组表
CREATE TABLE IF NOT EXISTS user_groups (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  flock_id INT COMMENT '鹅群ID（如果是鹅群相关组）',
  name VARCHAR(100) NOT NULL COMMENT '组名',
  group_name VARCHAR(100) NOT NULL COMMENT '组显示名称',
  permissions JSON COMMENT '组权限',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_flock_id (flock_id),
  INDEX idx_name (name),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- AI配置表
CREATE TABLE IF NOT EXISTS ai_configs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT COMMENT '用户ID',
  flock_id INT COMMENT '鹅群ID',
  name VARCHAR(100) NOT NULL COMMENT '配置名称',
  api_key VARCHAR(255) COMMENT 'API密钥',
  base_url VARCHAR(255) COMMENT '基础URL',
  models JSON COMMENT '可用模型',
  max_tokens INT DEFAULT 1000 COMMENT '最大令牌数',
  temperature DECIMAL(3,2) DEFAULT 0.7 COMMENT '温度参数',
  enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
  is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认配置',
  priority INT DEFAULT 0 COMMENT '优先级',
  config JSON COMMENT '其他配置',
  created_by INT COMMENT '创建者ID',
  updated_by INT COMMENT '更新者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_flock_id (flock_id),
  INDEX idx_enabled (enabled),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI配置表';

-- AI使用统计表  
CREATE TABLE IF NOT EXISTS ai_usage_stats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  flock_id INT COMMENT '鹅群ID',
  provider VARCHAR(50) COMMENT '服务提供商',
  model VARCHAR(100) COMMENT '模型名称',
  scenario VARCHAR(50) COMMENT '使用场景',
  feature VARCHAR(50) COMMENT '使用功能',
  service_type VARCHAR(50) COMMENT '服务类型',
  usage_count INT DEFAULT 1 COMMENT '使用次数',
  request_tokens INT DEFAULT 0 COMMENT '请求令牌数',
  response_tokens INT DEFAULT 0 COMMENT '响应令牌数', 
  total_tokens INT DEFAULT 0 COMMENT '总令牌数',
  cost DECIMAL(10,4) DEFAULT 0 COMMENT '成本',
  response_time INT COMMENT '响应时间(毫秒)',
  success TINYINT(1) DEFAULT 1 COMMENT '是否成功',
  error_message TEXT COMMENT '错误信息',
  request_data JSON COMMENT '请求数据',
  response_data JSON COMMENT '响应数据',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  session_id VARCHAR(255) COMMENT '会话ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_flock_id (flock_id),
  INDEX idx_provider (provider),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用统计表';

-- 公告表
CREATE TABLE IF NOT EXISTS announcements (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT COMMENT '发布者ID',
  flock_id INT COMMENT '关联鹅群ID',
  title VARCHAR(200) NOT NULL COMMENT '公告标题',
  content TEXT NOT NULL COMMENT '公告内容',
  type ENUM('system', 'user', 'maintenance', 'warning') DEFAULT 'user' COMMENT '公告类型',
  priority INT DEFAULT 0 COMMENT '优先级',
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
  publish_time DATETIME COMMENT '发布时间',
  expire_time DATETIME COMMENT '过期时间',
  target_users JSON COMMENT '目标用户',
  attachments JSON COMMENT '附件',
  read_count INT DEFAULT 0 COMMENT '阅读次数',
  is_top TINYINT(1) DEFAULT 0 COMMENT '是否置顶',
  allow_comment TINYINT(1) DEFAULT 1 COMMENT '是否允许评论',
  created_by INT COMMENT '创建者',
  updated_by INT COMMENT '更新者',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_flock_id (flock_id),
  INDEX idx_status (status),
  INDEX idx_publish_time (publish_time),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

-- 审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT COMMENT '操作用户ID',
  flock_id INT COMMENT '关联鹅群ID',
  username VARCHAR(100) COMMENT '用户名',
  resource VARCHAR(100) COMMENT '资源',
  resource_id INT COMMENT '资源ID',
  method VARCHAR(10) COMMENT 'HTTP方法',
  url VARCHAR(500) COMMENT '请求URL',
  user_agent TEXT COMMENT '用户代理',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  request_data JSON COMMENT '请求数据',
  response_status INT COMMENT '响应状态码',
  response_data JSON COMMENT '响应数据',
  duration INT COMMENT '耗时(毫秒)',
  success TINYINT(1) DEFAULT 1 COMMENT '是否成功',
  error_message TEXT COMMENT '错误信息',
  description TEXT COMMENT '操作描述',
  session_id VARCHAR(255) COMMENT '会话ID',
  level ENUM('info', 'warning', 'error', 'debug') DEFAULT 'info' COMMENT '日志级别',
  category VARCHAR(50) DEFAULT 'general' COMMENT '分类',
  tags JSON COMMENT '标签',
  details JSON COMMENT '详细信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_flock_id (flock_id),
  INDEX idx_resource (resource),
  INDEX idx_created_at (created_at),
  INDEX idx_level (level),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审计日志表';

-- 知识库表
CREATE TABLE IF NOT EXISTS knowledge_base (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT COMMENT '创建者ID',
  flock_id INT COMMENT '关联鹅群ID',
  title VARCHAR(200) NOT NULL COMMENT '标题',
  content TEXT NOT NULL COMMENT '内容',
  category VARCHAR(100) COMMENT '分类',
  tags JSON COMMENT '标签',
  keywords JSON COMMENT '关键词',
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度级别',
  read_time INT COMMENT '阅读时间(分钟)',
  view_count INT DEFAULT 0 COMMENT '查看次数',
  like_count INT DEFAULT 0 COMMENT '点赞次数',
  images JSON COMMENT '图片',
  videos JSON COMMENT '视频',
  attachments JSON COMMENT '附件',
  related_articles JSON COMMENT '相关文章',
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
  is_recommended TINYINT(1) DEFAULT 0 COMMENT '是否推荐',
  publish_time DATETIME COMMENT '发布时间',
  last_review_time DATETIME COMMENT '最后审核时间',
  created_by INT COMMENT '创建者',
  updated_by INT COMMENT '更新者',
  reviewed_by INT COMMENT '审核者',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_flock_id (flock_id),
  INDEX idx_category (category),
  INDEX idx_status (status),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库表';

-- 插入默认角色
INSERT IGNORE INTO roles (name, display_name, description, permissions, status) VALUES
('admin', '管理员', '系统管理员，拥有所有权限', '["*"]', 'active'),
('manager', '经理', '管理经理，拥有管理权限', '["manage_*", "view_*"]', 'active'), 
('staff', '员工', '普通员工，拥有基础权限', '["view_*", "create_basic"]', 'active'),
('viewer', '观察者', '只读用户，仅查看权限', '["view_*"]', 'active');

-- 插入默认权限
INSERT IGNORE INTO permissions (name, code, resource, action, description, category) VALUES
('查看用户', 'VIEW_USERS', 'users', 'view', '查看用户列表和详情', 'user'),
('管理用户', 'MANAGE_USERS', 'users', 'manage', '创建、编辑、删除用户', 'user'),
('查看鹅群', 'VIEW_FLOCKS', 'flocks', 'view', '查看鹅群信息', 'flock'),
('管理鹅群', 'MANAGE_FLOCKS', 'flocks', 'manage', '创建、编辑、删除鹅群', 'flock'),
('查看生产记录', 'VIEW_PRODUCTION', 'production', 'view', '查看生产记录', 'production'),
('管理生产记录', 'MANAGE_PRODUCTION', 'production', 'manage', '创建、编辑生产记录', 'production'),
('查看健康记录', 'VIEW_HEALTH', 'health', 'view', '查看健康记录', 'health'),
('管理健康记录', 'MANAGE_HEALTH', 'health', 'manage', '创建、编辑健康记录', 'health'),
('查看财务', 'VIEW_FINANCE', 'finance', 'view', '查看财务数据', 'finance'),
('管理财务', 'MANAGE_FINANCE', 'finance', 'manage', '管理财务记录', 'finance'),
('系统管理', 'ADMIN_ACCESS', 'system', 'admin', '系统管理权限', 'system');

COMMIT;