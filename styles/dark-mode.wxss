/* 智慧养鹅小程序 - 深色模式支持 V1.0 */
/* 完整的明暗主题切换系统 */

/* ==================== 深色模式变量定义 ==================== */

/* 深色模式主题色彩 */
.dark-theme {
  /* 主色系 - 深色模式下稍微调亮 */
  --primary: #4D94FF;
  --primary-light: #66A3FF;
  --primary-dark: #0066CC;
  --primary-subtle: rgba(77, 148, 255, 0.15);

  /* 辅助色系 */
  --secondary: #33B5E5;
  --accent: #FF8A50;
  --accent-light: #FFA366;
  --accent-subtle: rgba(255, 138, 80, 0.15);

  /* 语义色系 - 深色模式适配 */
  --success: #4CAF50;
  --success-light: #66BB6A;
  --success-bg: rgba(76, 175, 80, 0.15);
  --warning: #FFC107;
  --warning-light: #FFD54F;
  --warning-bg: rgba(255, 193, 7, 0.15);
  --error: #F44336;
  --error-light: #EF5350;
  --error-bg: rgba(244, 67, 54, 0.15);
  --info: #2196F3;
  --info-light: #42A5F5;
  --info-bg: rgba(33, 150, 243, 0.15);

  /* 中性色系 - 深色模式核心 */
  --text-primary: #FFFFFF;
  --text-secondary: #B3B3B3;
  --text-tertiary: #808080;
  --text-quaternary: #4D4D4D;
  --text-disabled: #333333;
  --text-inverse: #1A1A1A;

  /* 背景色系 */
  --bg-primary: #1E1E1E;
  --bg-secondary: #121212;
  --bg-tertiary: #0A0A0A;
  --bg-surface: #2D2D2D;
  --bg-overlay: rgba(255, 255, 255, 0.05);

  /* 边框色系 */
  --border-light: #333333;
  --border-medium: #404040;
  --border-heavy: #4D4D4D;

  /* 阴影色系 - 深色模式使用更明亮的阴影 */
  --shadow-sm: 0 1rpx 2rpx rgba(255, 255, 255, 0.05);
  --shadow-md: 0 4rpx 6rpx -1rpx rgba(255, 255, 255, 0.08);
  --shadow-lg: 0 10rpx 15rpx -3rpx rgba(255, 255, 255, 0.1);
  --shadow-xl: 0 20rpx 25rpx -5rpx rgba(255, 255, 255, 0.12);
  --shadow-primary: 0 8rpx 25rpx rgba(77, 148, 255, 0.3);
}

/* ==================== 主题切换动画 ==================== */

/* 主题切换过渡动画 */
.theme-transition {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

/* 主题切换按钮 */
.theme-toggle {
  position: relative;
  width: 88rpx;
  height: 48rpx;
  background-color: var(--border-medium);
  border-radius: var(--radius-full);
  padding: 4rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.theme-toggle.dark {
  background-color: var(--primary);
}

.theme-toggle-slider {
  width: 40rpx;
  height: 40rpx;
  background-color: var(--bg-primary);
  border-radius: var(--radius-full);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.theme-toggle.dark .theme-toggle-slider {
  transform: translateX(40rpx);
}

.theme-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.theme-toggle.dark .theme-icon.sun {
  opacity: 0;
}

.theme-toggle:not(.dark) .theme-icon.moon {
  opacity: 0;
}

/* ==================== 深色模式组件适配 ==================== */

/* 卡片在深色模式下的适配 */
.dark-theme .card,
.dark-theme .section {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.dark-theme .card::before,
.dark-theme .section::before {
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
}

/* 按钮在深色模式下的适配 */
.dark-theme .btn-secondary {
  background-color: var(--bg-surface);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.dark-theme .btn-secondary:active {
  background-color: var(--border-light);
}

/* 输入框在深色模式下的适配 */
.dark-theme .input,
.dark-theme .search-input,
.dark-theme .symptom-input {
  background-color: var(--bg-surface);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

.dark-theme .input:focus,
.dark-theme .search-input:focus,
.dark-theme .symptom-input:focus {
  border-color: var(--primary);
  background-color: var(--bg-primary);
}

.dark-theme .input::placeholder,
.dark-theme .search-input::placeholder,
.dark-theme .symptom-input::placeholder {
  color: var(--text-tertiary);
}

/* ==================== 深色模式图标适配 ==================== */

/* 图标在深色模式下的颜色调整 */
.dark-theme .icon-primary {
  filter: brightness(1.2);
}

.dark-theme .icon-secondary {
  opacity: 0.8;
}

.dark-theme .icon-inverse {
  filter: invert(1);
}

/* SVG图标适配 */
.dark-theme .svg-icon {
  fill: var(--text-primary);
}

.dark-theme .svg-icon-secondary {
  fill: var(--text-secondary);
}

/* ==================== 深色模式状态适配 ==================== */

/* 成功状态在深色模式下的适配 */
.dark-theme .status-success,
.dark-theme .item-status.normal {
  background-color: var(--success-bg);
  color: var(--success-light);
  border-color: var(--success);
}

/* 警告状态在深色模式下的适配 */
.dark-theme .status-warning,
.dark-theme .item-status.suitable {
  background-color: var(--warning-bg);
  color: var(--warning-light);
  border-color: var(--warning);
}

/* 错误状态在深色模式下的适配 */
.dark-theme .status-error,
.dark-theme .item-status.danger {
  background-color: var(--error-bg);
  color: var(--error-light);
  border-color: var(--error);
}

/* 信息状态在深色模式下的适配 */
.dark-theme .status-info,
.dark-theme .item-status.good {
  background-color: var(--info-bg);
  color: var(--info-light);
  border-color: var(--info);
}

/* ==================== 深色模式特殊效果 ==================== */

/* 玻璃态效果在深色模式下的适配 */
.dark-theme .btn-glass {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .btn-glass:active {
  background: rgba(255, 255, 255, 0.1);
}

/* 发光效果在深色模式下的增强 */
.dark-theme .glow-effect:active::before {
  opacity: 0.9;
}

/* 霓虹效果在深色模式下的适配 */
.dark-theme .btn-neon {
  border-color: var(--primary);
  color: var(--primary);
  box-shadow: 0 0 10rpx rgba(77, 148, 255, 0.3);
}

.dark-theme .btn-neon:active {
  box-shadow: 0 0 30rpx var(--primary);
}

/* ==================== 深色模式动画适配 ==================== */

/* 骨架屏在深色模式下的适配 */
.dark-theme .skeleton {
  background: linear-gradient(90deg, var(--bg-surface) 25%, var(--border-light) 50%, var(--bg-surface) 75%);
}

/* 进度条在深色模式下的适配 */
.dark-theme .progress-bar {
  background-color: var(--bg-surface);
}

/* 流光效果在深色模式下的适配 */
.dark-theme .shimmer::after {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* ==================== 深色模式页面适配 ==================== */

/* 首页在深色模式下的适配 */
.dark-theme .user-info {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.dark-theme .overview {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

/* 商城页面在深色模式下的适配 */
.dark-theme .shop-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.dark-theme .goods-item {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.dark-theme .category-item {
  background-color: var(--bg-surface);
  border-color: var(--border-medium);
}

/* 健康页面在深色模式下的适配 */
.dark-theme .tab-list {
  background-color: var(--bg-surface);
}

.dark-theme .record-item {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

/* ==================== 深色模式工具类 ==================== */

/* 深色模式专用边框 */
.dark-border {
  border-color: var(--border-light);
}

.dark-theme .dark-border {
  border-color: var(--border-medium);
}

/* 深色模式专用背景 */
.dark-bg {
  background-color: var(--bg-secondary);
}

.dark-theme .dark-bg {
  background-color: var(--bg-surface);
}

/* 深色模式专用文字颜色 */
.dark-text {
  color: var(--text-primary);
}

.dark-theme .dark-text {
  color: var(--text-primary);
}

/* ==================== 自动深色模式检测 ==================== */

/* 系统偏好自动切换 - 小程序通过类名控制 */
/* @media (prefers-color-scheme: dark) { */
/* 小程序不支持 */
.auto-dark-mode {
  --primary: #4D94FF;
  --text-primary: #FFFFFF;
  --text-secondary: #B3B3B3;
  --bg-primary: #1E1E1E;
  --bg-secondary: #121212;
  --border-light: #333333;
  --border-medium: #404040;
}

/* } */

/* ==================== 深色模式切换JavaScript接口 ==================== */

/* 
使用方法：
1. 在页面中添加主题切换按钮
2. 通过JavaScript调用以下方法：

// 切换到深色模式
function enableDarkMode() {
  document.body.classList.add('dark-theme');
  wx.setStorageSync('theme', 'dark');
}

// 切换到浅色模式  
function enableLightMode() {
  document.body.classList.remove('dark-theme');
  wx.setStorageSync('theme', 'light');
}

// 切换主题
function toggleTheme() {
  const isDark = document.body.classList.contains('dark-theme');
  if (isDark) {
    enableLightMode();
  } else {
    enableDarkMode();
  }
}

// 初始化主题
function initTheme() {
  const savedTheme = wx.getStorageSync('theme');
  const systemDark = (() => {
    try {
      const appBaseInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};
      return appBaseInfo.theme === 'dark';
    } catch (error) {
      return false;
    }
  })();
  
  if (savedTheme === 'dark' || (!savedTheme && systemDark)) {
    enableDarkMode();
  }
}
*/

/* ==================== 深色模式性能优化 ==================== */

/* 减少深色模式切换时的重排 */
.theme-optimized {
  /* 小程序中使用transform来优化主题切换性能 */
  transform: translateZ(0);
  will-change: background-color, color;
}

/* 预加载深色模式样式 */
.preload-dark-theme {
  /* 小程序中使用opacity来控制主题预加载 */
  opacity: 1;
  transition: opacity 0.3s ease;
}