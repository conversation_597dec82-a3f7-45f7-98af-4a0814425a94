// backend/controllers/ai-config.controller.js
// AI配置管理控制器

const AIConfig = require("../models/ai-config.model");
const AIUsageStats = require("../models/ai-usage-stats.model");
const { Op } = require("sequelize");
const crypto = require("crypto");

// 加密API密钥
function encryptApiKey(apiKey) {
  const algorithm = "aes-256-cbc";
  const key = process.env.ENCRYPTION_KEY || "default-encryption-key-32-chars!!";
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(apiKey, "utf8", "hex");
  encrypted += cipher.final("hex");
  return iv.toString("hex") + ":" + encrypted;
}

// 解密API密钥
function decryptApiKey(encryptedApiKey) {
  try {
    const algorithm = "aes-256-cbc";
    const key =
      process.env.ENCRYPTION_KEY || "default-encryption-key-32-chars!!";
    const textParts = encryptedApiKey.split(":");
    const iv = Buffer.from(textParts.shift(), "hex");
    const encryptedText = textParts.join(":");
    const decipher = crypto.createDecipher(algorithm, key);
    let decrypted = decipher.update(encryptedText, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("解密API密钥失败:", error); } catch(_) {}

    return encryptedApiKey; // 返回原始值作为降级处理
  }
}

class AIConfigController {
  // 获取所有AI配置
  async getAllConfigs(req, res) {
    try {
      const configs = await AIConfig.findAll({
        order: [
          ["priority", "DESC"],
          ["createdAt", "ASC"],
        ],
      });

      // 脱敏处理API密钥
      const sanitizedConfigs = configs.map((config) => {
        const configData = config.toJSON();
        if (configData.apiKey) {
          configData.apiKey = configData.apiKey.substring(0, 8) + "****";
        }
        return configData;
      });

      res.json({
        success: true,
        data: sanitizedConfigs,
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取AI配置失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "获取AI配置失败",
        error: error.message,
      });
    }
  }

  // 获取单个AI配置
  async getConfig(req, res) {
    try {
      const { id } = req.params;
      const config = await AIConfig.findByPk(id);

      if (!config) {
        return res.status(404).json({
          success: false,
          message: "AI配置不存在",
        });
      }

      const configData = config.toJSON();
      // 脱敏处理API密钥
      if (configData.apiKey) {
        configData.apiKey = configData.apiKey.substring(0, 8) + "****";
      }

      res.json({
        success: true,
        data: configData,
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取AI配置失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "获取AI配置失败",
        error: error.message,
      });
    }
  }

  // 创建AI配置
  async createConfig(req, res) {
    try {
      const {
        provider,
        name,
        apiKey,
        baseUrl,
        models,
        maxTokens,
        temperature,
        enabled,
        isDefault,
        priority,
        config,
      } = req.body;

      // 如果设置为默认，先取消其他默认配置
      if (isDefault) {
        await AIConfig.update(
          { isDefault: false },
          { where: { isDefault: true } },
        );
      }

      // 加密API密钥
      const encryptedApiKey = encryptApiKey(apiKey);

      const newConfig = await AIConfig.create({
        provider,
        name,
        apiKey: encryptedApiKey,
        baseUrl,
        models,
        maxTokens,
        temperature,
        enabled,
        isDefault,
        priority,
        config,
        createdBy: req.user?.id || 1,
      });

      res.status(201).json({
        success: true,
        message: "AI配置创建成功",
        data: { id: newConfig.id },
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("创建AI配置失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "创建AI配置失败",
        error: error.message,
      });
    }
  }

  // 更新AI配置
  async updateConfig(req, res) {
    try {
      const { id } = req.params;
      const updateData = { ...req.body };

      // 如果包含API密钥，进行加密
      if (updateData.apiKey && !updateData.apiKey.includes("****")) {
        updateData.apiKey = encryptApiKey(updateData.apiKey);
      } else if (updateData.apiKey && updateData.apiKey.includes("****")) {
        // 如果是脱敏的密钥，不更新
        delete updateData.apiKey;
      }

      // 如果设置为默认，先取消其他默认配置
      if (updateData.isDefault) {
        await AIConfig.update(
          { isDefault: false },
          { where: { isDefault: true, id: { [Op.ne]: id } } },
        );
      }

      updateData.updatedBy = req.user?.id || 1;

      const [updatedRows] = await AIConfig.update(updateData, {
        where: { id },
      });

      if (updatedRows === 0) {
        return res.status(404).json({
          success: false,
          message: "AI配置不存在",
        });
      }

      res.json({
        success: true,
        message: "AI配置更新成功",
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新AI配置失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "更新AI配置失败",
        error: error.message,
      });
    }
  }

  // 删除AI配置
  async deleteConfig(req, res) {
    try {
      const { id } = req.params;

      const config = await AIConfig.findByPk(id);
      if (!config) {
        return res.status(404).json({
          success: false,
          message: "AI配置不存在",
        });
      }

      // 不允许删除默认配置
      if (config.isDefault) {
        return res.status(400).json({
          success: false,
          message: "不能删除默认AI配置",
        });
      }

      await config.destroy();

      res.json({
        success: true,
        message: "AI配置删除成功",
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("删除AI配置失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "删除AI配置失败",
        error: error.message,
      });
    }
  }

  // 获取使用统计
  async getUsageStats(req, res) {
    try {
      const { period = "7d", provider = "all" } = req.query;

      // 计算时间范围
      const now = new Date();
      let startDate;
      switch (period) {
        case "1d":
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case "7d":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "30d":
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      const whereClause = {
        createdAt: {
          [Op.gte]: startDate,
        },
      };

      if (provider !== "all") {
        whereClause.provider = provider;
      }

      const stats = await AIUsageStats.findAll({
        where: whereClause,
        attributes: [
          "provider",
          "scenario",
          [sequelize.fn("COUNT", sequelize.col("id")), "totalRequests"],
          [sequelize.fn("SUM", sequelize.col("totalTokens")), "totalTokens"],
          [sequelize.fn("SUM", sequelize.col("cost")), "totalCost"],
          [
            sequelize.fn("AVG", sequelize.col("responseTime")),
            "avgResponseTime",
          ],
        ],
        group: ["provider", "scenario"],
      });

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取使用统计失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "获取使用统计失败",
        error: error.message,
      });
    }
  }

  // 测试AI配置连接
  async testConfig(req, res) {
    try {
      const { id } = req.params;
      const config = await AIConfig.findByPk(id);

      if (!config) {
        return res.status(404).json({
          success: false,
          message: "AI配置不存在",
        });
      }

      // 这里可以添加实际的API连接测试逻辑
      // 暂时返回模拟结果
      res.json({
        success: true,
        message: "AI配置连接测试成功",
        data: {
          provider: config.provider,
          status: "connected",
          responseTime: Math.floor(Math.random() * 1000) + 200,
        },
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("测试AI配置失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "测试AI配置失败",
        error: error.message,
      });
    }
  }
}

module.exports = new AIConfigController();
