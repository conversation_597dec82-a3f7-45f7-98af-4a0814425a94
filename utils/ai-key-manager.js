// utils/ai-key-manager.js
// AI API密钥管理器

const STORAGE_KEY = 'ai_api_keys';

/**
 * AI密钥管理器
 */
class AIKeyManager {
  constructor() {
    this.keys = this.loadKeys();
  }

  /**
   * 从本地存储加载API密钥
   */
  loadKeys() {
    try {
      const stored = wx.getStorageSync(STORAGE_KEY);
      return stored || {
        siliconflow: '',
        zhipu: '',
        openai: '',
        qianwen: ''
      };
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('加载API密钥失败', error); } catch(_) {}
      return {
        siliconflow: '',
        zhipu: '',
        openai: '',
        qianwen: ''
      };
    }
  }

  /**
   * 保存API密钥到本地存储
   */
  saveKeys() {
    try {
      wx.setStorageSync(STORAGE_KEY, this.keys);
      return true;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('保存API密钥失败', error); } catch(_) {}
      return false;
    }
  }

  /**
   * 设置API密钥
   * @param {string} provider - 服务提供商
   * @param {string} apiKey - API密钥
   */
  setApiKey(provider, apiKey) {
    if (this.keys.hasOwnProperty(provider)) {
      this.keys[provider] = apiKey;
      return this.saveKeys();
    } else {
      throw new Error(`不支持的服务提供商: ${provider}`);
    }
  }

  /**
   * 获取API密钥
   * @param {string} provider - 服务提供商
   * @returns {string} API密钥
   */
  getApiKey(provider) {
    return this.keys[provider] || '';
  }

  /**
   * 检查API密钥是否已配置
   * @param {string} provider - 服务提供商
   * @returns {boolean} 是否已配置
   */
  hasApiKey(provider) {
    const key = this.getApiKey(provider);
    return key && key.trim().length > 0;
  }

  /**
   * 验证API密钥格式
   * @param {string} provider - 服务提供商
   * @param {string} apiKey - API密钥
   * @returns {boolean} 格式是否正确
   */
  validateApiKey(provider, apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }

    switch (provider) {
      case 'siliconflow':
        return apiKey.startsWith('sk-');
      case 'zhipu':
        return apiKey.includes('.') && apiKey.length > 20;
      case 'openai':
        return apiKey.startsWith('sk-');
      case 'qianwen':
        return apiKey.length > 10;
      default:
        return false;
    }
  }

  /**
   * 获取所有已配置的密钥
   * @returns {Object} 密钥对象（隐藏敏感信息）
   */
  getAllKeys() {
    const result = {};
    for (const [provider, key] of Object.entries(this.keys)) {
      if (key && key.length > 0) {
        // 只显示前4位和后4位，中间用*代替
        const masked = key.length > 8 
          ? key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4)
          : '*'.repeat(key.length);
        result[provider] = {
          configured: true,
          masked: masked
        };
      } else {
        result[provider] = {
          configured: false,
          masked: ''
        };
      }
    }
    return result;
  }

  /**
   * 清除所有API密钥
   */
  clearAllKeys() {
    this.keys = {
      siliconflow: '',
      zhipu: '',
      openai: '',
      qianwen: ''
    };
    return this.saveKeys();
  }

  /**
   * 清除特定服务商的API密钥
   * @param {string} provider - 服务提供商
   */
  clearApiKey(provider) {
    if (this.keys.hasOwnProperty(provider)) {
      this.keys[provider] = '';
      return this.saveKeys();
    } else {
      throw new Error(`不支持的服务提供商: ${provider}`);
    }
  }

  /**
   * 获取推荐的服务提供商（基于已配置的密钥）
   * @returns {string|null} 推荐的服务提供商
   */
  getRecommendedProvider() {
    // 优先级：智谱AI > 硅基流动 > OpenAI > 千问
    const priority = ['zhipu', 'siliconflow', 'openai', 'qianwen'];
    
    for (const provider of priority) {
      if (this.hasApiKey(provider)) {
        return provider;
      }
    }
    
    return null;
  }

  /**
   * 测试API密钥是否有效
   * @param {string} provider - 服务提供商
   * @param {string} apiKey - API密钥（可选，不提供则使用已保存的）
   * @returns {Promise<boolean>} 是否有效
   */
  async testApiKey(provider, apiKey = null) {
    const keyToTest = apiKey || this.getApiKey(provider);
    
    if (!keyToTest) {
      return false;
    }

    try {
      // 这里可以实现实际的API测试调用
      // 暂时只做格式验证
      return this.validateApiKey(provider, keyToTest);
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('测试API密钥失败', error); } catch(_) {}
      return false;
    }
  }

  /**
   * 获取服务提供商信息
   * @returns {Object} 服务提供商信息
   */
  getProviderInfo() {
    return {
      siliconflow: {
        name: '硅基流动',
        description: '免费的AI模型服务，支持多种开源模型',
        website: 'https://siliconflow.cn',
        keyFormat: 'sk-xxxxxx',
        features: ['文本生成', '图像识别', '免费额度']
      },
      zhipu: {
        name: '智谱AI',
        description: '清华大学技术成果转化的AI公司',
        website: 'https://open.bigmodel.cn',
        keyFormat: 'xxxxxx.xxxxxx',
        features: ['GLM模型', '图像识别', '免费额度']
      },
      openai: {
        name: 'OpenAI',
        description: '全球领先的AI研究公司',
        website: 'https://openai.com',
        keyFormat: 'sk-xxxxxx',
        features: ['GPT模型', '图像识别', '付费服务']
      },
      qianwen: {
        name: '通义千问',
        description: '阿里云推出的大语言模型',
        website: 'https://dashscope.aliyun.com',
        keyFormat: 'xxxxxx',
        features: ['千问模型', '图像识别', '免费额度']
      }
    };
  }

  /**
   * 导出配置
   * @returns {Object} 配置数据
   */
  exportConfig() {
    return {
      keys: this.keys,
      timestamp: new Date().toISOString(),
      version: '1.0'
    };
  }

  /**
   * 导入配置
   * @param {Object} config - 配置数据
   * @returns {boolean} 是否成功
   */
  importConfig(config) {
    try {
      if (config && config.keys && typeof config.keys === 'object') {
        // 验证配置格式
        const validKeys = ['siliconflow', 'zhipu', 'openai', 'qianwen'];
        const importKeys = {};
        
        for (const key of validKeys) {
          importKeys[key] = config.keys[key] || '';
        }
        
        this.keys = importKeys;
        return this.saveKeys();
      } else {
        throw new Error('配置格式错误');
      }
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('导入配置失败', error); } catch(_) {}
      return false;
    }
  }
}

// 创建单例实例
const aiKeyManager = new AIKeyManager();

module.exports = {
  AIKeyManager,
  aiKeyManager
};
