/**
 * SaaS平台管理后台全面测试
 * 使用Playwright测试所有功能模块
 */

const { test, expect } = require('@playwright/test');

const BASE_URL = 'http://localhost:3002';
const ADMIN_USERNAME = 'platform_admin';
const ADMIN_PASSWORD = 'admin123';

// 测试数据
const testData = {
  tenant: {
    companyName: '测试养鹅场有限公司',
    contactName: '张经理',
    contactPhone: '13800138000',
    contactEmail: '<EMAIL>',
    tenantCode: 'TEST001'
  },
  user: {
    username: 'testuser',
    email: '<EMAIL>',
    name: '测试用户'
  }
};

test.describe('SaaS平台管理后台全面测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 每个测试前都需要登录
    await page.goto(`${BASE_URL}/saas-admin/login`);
    
    // 等待登录页面加载
    await expect(page.locator('title')).toContainText('登录');
    
    // 执行登录
    await page.fill('input[name="username"]', ADMIN_USERNAME);
    await page.fill('input[name="password"]', ADMIN_PASSWORD);
    await page.click('button[type="submit"]');
    
    // 等待重定向到仪表盘
    await page.waitForURL('**/saas-admin/dashboard');
    await expect(page.locator('h5')).toContainText('仪表板');
  });

  test('1. 登录流程测试', async ({ page }) => {
    // 已在beforeEach中完成登录测试
    console.log('✅ 登录流程测试通过');
    
    // 验证仪表盘基本元素
    await expect(page.locator('.sidebar')).toBeVisible();
    await expect(page.locator('.navbar')).toBeVisible();
    await expect(page.locator('[data-bs-target="#dashboard"]')).toBeVisible();
  });

  test('2. 仪表盘页面测试', async ({ page }) => {
    console.log('🔍 测试仪表盘页面...');
    
    // 验证统计卡片
    await expect(page.locator('.stat-card')).toHaveCount(4);
    
    // 验证图表元素
    await expect(page.locator('#apiChart')).toBeVisible();
    await expect(page.locator('#tenantChart')).toBeVisible();
    
    // 验证最新租户表格
    await expect(page.locator('.table')).toBeVisible();
    
    console.log('✅ 仪表盘页面测试通过');
  });

  test('3. 租户管理模块测试', async ({ page }) => {
    console.log('🔍 测试租户管理模块...');
    
    // 点击租户管理菜单
    await page.click('a[href="/saas-admin/tenants"]');
    await page.waitForURL('**/saas-admin/tenants');
    
    // 验证页面标题
    await expect(page.locator('h1')).toContainText('租户管理');
    
    // 验证搜索功能
    await expect(page.locator('input[placeholder*="搜索"]')).toBeVisible();
    
    // 验证筛选功能
    await expect(page.locator('select')).toHaveCount(4); // 状态、订阅计划、规模、排序
    
    // 验证租户列表表格
    await expect(page.locator('.table')).toBeVisible();
    await expect(page.locator('th')).toContainText(['租户代码', '公司名称', '状态']);
    
    // 验证分页
    await expect(page.locator('.pagination')).toBeVisible();
    
    console.log('✅ 租户管理模块测试通过');
  });

  test('4. 用户管理模块测试', async ({ page }) => {
    console.log('🔍 测试用户管理模块...');
    
    await page.click('a[href="/saas-admin/users"]');
    await page.waitForURL('**/saas-admin/users');
    
    // 验证页面标题
    await expect(page.locator('h1')).toContainText('用户管理');
    
    // 验证新增用户按钮
    await expect(page.locator('button:has-text("新增用户")')).toBeVisible();
    
    // 验证用户列表表格
    await expect(page.locator('.table')).toBeVisible();
    await expect(page.locator('th')).toContainText(['用户名', '邮箱', '角色', '状态']);
    
    console.log('✅ 用户管理模块测试通过');
  });

  test('5. 鹅群管理模块测试', async ({ page }) => {
    console.log('🔍 测试鹅群管理模块...');
    
    await page.click('a[href="/saas-admin/flocks"]');
    await page.waitForURL('**/saas-admin/flocks');
    
    await expect(page.locator('h1')).toContainText('鹅群管理');
    await expect(page.locator('button:has-text("新增")')).toBeVisible();
    
    console.log('✅ 鹅群管理模块测试通过');
  });

  test('6. 健康记录模块测试', async ({ page }) => {
    console.log('🔍 测试健康记录模块...');
    
    await page.click('a[href="/saas-admin/health"]');
    await page.waitForURL('**/saas-admin/health');
    
    await expect(page.locator('h1')).toContainText('健康记录');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 健康记录模块测试通过');
  });

  test('7. 生产记录模块测试', async ({ page }) => {
    console.log('🔍 测试生产记录模块...');
    
    await page.click('a[href="/saas-admin/production"]');
    await page.waitForURL('**/saas-admin/production');
    
    await expect(page.locator('h1')).toContainText('生产记录');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 生产记录模块测试通过');
  });

  test('8. 库存管理模块测试', async ({ page }) => {
    console.log('🔍 测试库存管理模块...');
    
    await page.click('a[href="/saas-admin/inventory"]');
    await page.waitForURL('**/saas-admin/inventory');
    
    await expect(page.locator('h1')).toContainText('库存管理');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 库存管理模块测试通过');
  });

  test('9. 商城管理模块测试', async ({ page }) => {
    console.log('🔍 测试商城管理模块...');
    
    await page.click('a[href="/saas-admin/shop"]');
    await page.waitForURL('**/saas-admin/shop');
    
    await expect(page.locator('h1')).toContainText('商城管理');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 商城管理模块测试通过');
  });

  test('10. 知识库模块测试', async ({ page }) => {
    console.log('🔍 测试知识库模块...');
    
    await page.click('a[href="/saas-admin/knowledge"]');
    await page.waitForURL('**/saas-admin/knowledge');
    
    await expect(page.locator('h1')).toContainText('知识库');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 知识库模块测试通过');
  });

  test('11. AI诊断模块测试', async ({ page }) => {
    console.log('🔍 测试AI诊断模块...');
    
    await page.click('a[href="/saas-admin/ai"]');
    await page.waitForURL('**/saas-admin/ai');
    
    await expect(page.locator('h1')).toContainText('AI诊断');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ AI诊断模块测试通过');
  });

  test('12. 任务管理模块测试', async ({ page }) => {
    console.log('🔍 测试任务管理模块...');
    
    await page.click('a[href="/saas-admin/tasks"]');
    await page.waitForURL('**/saas-admin/tasks');
    
    await expect(page.locator('h1')).toContainText('任务管理');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 任务管理模块测试通过');
  });

  test('13. 公告管理模块测试', async ({ page }) => {
    console.log('🔍 测试公告管理模块...');
    
    await page.click('a[href="/saas-admin/announcements"]');
    await page.waitForURL('**/saas-admin/announcements');
    
    await expect(page.locator('h1')).toContainText('公告管理');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 公告管理模块测试通过');
  });

  test('14. 价格管理模块测试', async ({ page }) => {
    console.log('🔍 测试价格管理模块...');
    
    await page.click('a[href="/saas-admin/pricing"]');
    await page.waitForURL('**/saas-admin/pricing');
    
    await expect(page.locator('h1')).toContainText('价格管理');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 价格管理模块测试通过');
  });

  test('15. 订阅计划模块测试', async ({ page }) => {
    console.log('🔍 测试订阅计划模块...');
    
    await page.click('a[href="/saas-admin/plans"]');
    await page.waitForURL('**/saas-admin/plans');
    
    await expect(page.locator('h1')).toContainText('订阅计划管理');
    
    // 验证计划卡片
    await expect(page.locator('.card')).toHaveCount.greaterThan(3);
    
    console.log('✅ 订阅计划模块测试通过');
  });

  test('16. 系统监控模块测试', async ({ page }) => {
    console.log('🔍 测试系统监控模块...');
    
    await page.click('a[href="/saas-admin/monitoring"]');
    await page.waitForURL('**/saas-admin/monitoring');
    
    await expect(page.locator('h1')).toContainText('系统监控');
    await expect(page.locator('.alert-info')).toContainText('功能正在开发中');
    
    console.log('✅ 系统监控模块测试通过');
  });

  test('17. 导航和UI一致性测试', async ({ page }) => {
    console.log('🔍 测试导航和UI一致性...');
    
    // 测试所有菜单项
    const menuItems = [
      { href: '/saas-admin/dashboard', text: '仪表板' },
      { href: '/saas-admin/tenants', text: '租户管理' },
      { href: '/saas-admin/users', text: '用户管理' },
      { href: '/saas-admin/flocks', text: '鹅群管理' },
      { href: '/saas-admin/health', text: '健康记录' },
      { href: '/saas-admin/production', text: '生产记录' },
      { href: '/saas-admin/inventory', text: '库存管理' },
      { href: '/saas-admin/shop', text: '商城管理' },
      { href: '/saas-admin/knowledge', text: '知识库' },
      { href: '/saas-admin/ai', text: 'AI诊断' },
      { href: '/saas-admin/tasks', text: '任务管理' },
      { href: '/saas-admin/announcements', text: '公告管理' },
      { href: '/saas-admin/pricing', text: '价格管理' },
      { href: '/saas-admin/plans', text: '订阅计划' },
      { href: '/saas-admin/monitoring', text: '系统监控' }
    ];

    for (const menuItem of menuItems) {
      await page.click(`a[href="${menuItem.href}"]`);
      await page.waitForURL(`**${menuItem.href}`);
      
      // 验证页面基本结构
      await expect(page.locator('.sidebar')).toBeVisible();
      await expect(page.locator('.navbar')).toBeVisible();
      await expect(page.locator('main')).toBeVisible();
      
      console.log(`✅ ${menuItem.text} 页面结构验证通过`);
    }
    
    console.log('✅ 导航和UI一致性测试通过');
  });

  test('18. 响应式设计测试', async ({ page }) => {
    console.log('🔍 测试响应式设计...');
    
    // 测试不同屏幕尺寸
    const viewports = [
      { width: 1920, height: 1080, name: '桌面端' },
      { width: 1024, height: 768, name: '平板端' },
      { width: 375, height: 667, name: '移动端' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      // 验证关键元素在不同尺寸下的可见性
      await expect(page.locator('.sidebar')).toBeVisible();
      await expect(page.locator('main')).toBeVisible();
      
      console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) 响应式测试通过`);
    }
    
    console.log('✅ 响应式设计测试通过');
  });

  test('19. 性能和加载测试', async ({ page }) => {
    console.log('🔍 测试页面性能和加载时间...');
    
    // 测试主要页面的加载性能
    const testPages = [
      '/saas-admin/dashboard',
      '/saas-admin/tenants',
      '/saas-admin/users',
      '/saas-admin/plans'
    ];

    for (const testPage of testPages) {
      const startTime = Date.now();
      await page.goto(`${BASE_URL}${testPage}`);
      const loadTime = Date.now() - startTime;
      
      console.log(`📊 ${testPage} 加载时间: ${loadTime}ms`);
      
      // 验证页面在合理时间内加载完成 (< 3秒)
      expect(loadTime).toBeLessThan(3000);
    }
    
    console.log('✅ 性能和加载测试通过');
  });

  test('20. 错误处理测试', async ({ page }) => {
    console.log('🔍 测试错误处理...');
    
    // 测试404页面
    await page.goto(`${BASE_URL}/saas-admin/nonexistent-page`);
    await expect(page.locator('title')).toContainText('页面未找到');
    
    console.log('✅ 错误处理测试通过');
  });

});

// 测试统计和报告
test.afterAll(async () => {
  console.log('\n🎉 SaaS平台管理后台全面测试完成！');
  console.log('📊 测试覆盖模块：');
  console.log('   ✅ 登录流程');
  console.log('   ✅ 仪表盘');
  console.log('   ✅ 租户管理');
  console.log('   ✅ 用户管理');
  console.log('   ✅ 鹅群管理');
  console.log('   ✅ 健康记录');
  console.log('   ✅ 生产记录');
  console.log('   ✅ 库存管理');
  console.log('   ✅ 商城管理');
  console.log('   ✅ 知识库');
  console.log('   ✅ AI诊断');
  console.log('   ✅ 任务管理');
  console.log('   ✅ 公告管理');
  console.log('   ✅ 价格管理');
  console.log('   ✅ 订阅计划');
  console.log('   ✅ 系统监控');
  console.log('   ✅ UI一致性');
  console.log('   ✅ 响应式设计');
  console.log('   ✅ 性能测试');
  console.log('   ✅ 错误处理');
});