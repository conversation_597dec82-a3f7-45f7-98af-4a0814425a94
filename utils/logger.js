/**
 * 统一日志封装
 * - 遵循微信小程序规范：生产环境降噪，开发保留调试
 * - 等级：debug < info < warn < error
 */

const { CONFIG } = (() => {
  try {
    return require('../constants/index.js');
  } catch (e) {
    return { CONFIG: { DEBUG_CONFIG: { LOGGING: { LEVEL: 'info', CONSOLE_OUTPUT: true } } } };
  }
})();

// 将等级转为数值，便于比较
const LEVEL_WEIGHT = { debug: 10, info: 20, warn: 30, error: 40 };

function getCurrentLevel() {
  const level = CONFIG?.DEBUG_CONFIG?.LOGGING?.LEVEL || 'info';
  return LEVEL_WEIGHT[level] ? level : 'info';
}

function shouldOutput(level) {
  const configured = getCurrentLevel();
  return LEVEL_WEIGHT[level] >= LEVEL_WEIGHT[configured] && (CONFIG?.DEBUG_CONFIG?.LOGGING?.CONSOLE_OUTPUT !== false);
}

function format(args) {
  try {
    const app = getApp && getApp();
    const tenant = wx?.getStorageSync ? (wx.getStorageSync('tenant_code') || '') : '';
    const requestId = '';
    return [`[${new Date().toISOString()}]`, tenant ? `(tenant:${tenant})` : '', ...args];
  } catch (e) {
    return args;
  }
}

const logger = {
  debug: (...args) => {
    if (shouldOutput('debug')) console.debug.apply(console, format(args));
  },
  info: (...args) => {
    if (shouldOutput('info')) console.info.apply(console, format(args));
  },
  warn: (...args) => {
    if (shouldOutput('warn')) console.warn.apply(console, format(args));
  },
  error: (...args) => {
    if (shouldOutput('error')) console.error.apply(console, format(args));
  }
};

module.exports = logger;


