/* 优化后的生产记录页面样式 */

.production-records-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f2f5 100%);
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  padding: 60rpx 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.add-record-btn {
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 筛选标签栏 */
.filter-tabs {
  background: white;
  padding: 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: inline-flex;
  padding: 0 20rpx;
  gap: 16rpx;
}

.filter-tab {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  background: #f6f6f6;
  color: #666;
  font-size: 26rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
  position: relative;
}

.filter-tab.active {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

.tab-icon {
  font-size: 24rpx;
}

.tab-text {
  font-weight: 500;
}

.tab-badge {
  background: #ff4d4f;
  color: white;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
  position: absolute;
  top: -8rpx;
  right: -8rpx;
}

.filter-tab.active .tab-badge {
  background: rgba(255, 255, 255, 0.9);
  color: #52c41a;
}

/* 统计卡片 */
.stats-cards {
  display: flex;
  gap: 16rpx;
  padding: 20rpx;
  margin-top: -20rpx;
}

.stats-card {
  flex: 1;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #262626;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 22rpx;
  color: #8c8c8c;
}

/* 记录容器 */
.records-container {
  flex: 1;
  margin-top: 16rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #52c41a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #8c8c8c;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #262626;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #8c8c8c;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 记录列表 */
.records-list {
  height: calc(100vh - 400rpx);
}

/* 日期分组 */
.date-group {
  margin-bottom: 32rpx;
}

.date-header {
  display: flex;
  align-items: center;
  margin: 32rpx 20rpx 16rpx;
}

.date-line {
  flex: 1;
  height: 1rpx;
  background: #e8e8e8;
}

.date-text {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #8c8c8c;
  font-weight: 500;
}

.date-records {
  /* 记录项由组件自己处理样式 */
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.load-more-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #f0f0f0;
  border-top: 2rpx solid #52c41a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.load-more-text {
  font-size: 24rpx;
  color: #8c8c8c;
}

.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #bfbfbf;
}

/* 添加记录菜单 */
.add-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.add-menu {
  width: 100%;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx;
  animation: slideUp 0.3s ease;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.menu-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #262626;
}

.menu-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #8c8c8c;
  background: #f6f6f6;
  border-radius: 50%;
}

.menu-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.menu-option {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.menu-option:active {
  background: #e6f7ff;
  transform: scale(0.98);
}

.option-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
}

.option-icon.entry {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.option-icon.weight {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.option-icon.sale {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.option-content {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 22rpx;
  color: #8c8c8c;
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-cards {
    flex-direction: column;
  }
  
  .stats-card {
    margin-bottom: 16rpx;
  }
  
  .header-title {
    font-size: 36rpx;
  }
  
  .fab {
    bottom: 30rpx;
    right: 30rpx;
    width: 80rpx;
    height: 80rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}
