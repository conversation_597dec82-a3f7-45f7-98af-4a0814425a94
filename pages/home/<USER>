<!-- pages/home/<USER>
<view class="container">
  <!-- 用户信息和天气区域 -->
  <view class="user-info-banner">
    <view class="user-info-content">
      <view class="user-section">
        <view class="avatar">
          <image src="{{userInfo.avatar}}" mode="aspectFill" lazy-load="{{true}}"></image>
        </view>
        <view class="user-details">
          <view class="user-name-line">
            <text class="username">{{userInfo.name || '养殖户'}}</text>
          </view>
          <view class="farm-name-line">
            <text class="farm-name">{{userInfo.farmName || '未设置养殖场'}}</text>
          </view>
        </view>
      </view>
      <view class="weather-info">
        <c-weather-compact auto-location="{{true}}"></c-weather-compact>
      </view>
    </view>
  </view>

  <!-- 今日鹅价组件 -->
  <c-goose-price default-tab="gosling"></c-goose-price>

  <!-- 公告栏 -->
  <view class="announcement-section">
    <view class="section-header">
      <text class="section-title">公告</text>
      <text class="more-text" bindtap="onViewAllAnnouncements">查看全部</text>
    </view>
    <view class="announcement-list">
      <block wx:for="{{announcements}}" wx:key="id">
        <view class="announcement-item" bindtap="onAnnouncementTap" data-id="{{item.id}}">
          <view class="announcement-content">
            <text class="announcement-title">{{item.title}}</text>
          </view>
          <view class="announcement-time">
            <text>{{item.publishTime}}</text>
          </view>
        </view>
      </block>
      <view wx:if="{{announcements.length === 0}}" class="empty-state">
        <text class="empty-text">暂无公告</text>
      </view>
    </view>
  </view>

  <!-- 待办任务 -->
  <view class="task-section">
    <view class="section-header">
      <text class="section-title">待办任务</text>
      <text class="more-text" bindtap="onViewAllTasks">查看全部</text>
    </view>
    <view class="task-list">
      <block wx:for="{{tasks}}" wx:key="id">
        <view class="task-item {{item.completed ? 'completed' : ''}}" bindtap="onTaskTap" data-id="{{item.id}}">
          <view class="task-content">
            <text class="task-title">{{item.title}}</text>
            <text class="task-desc">{{item.description}}</text>
            <text wx:if="{{item.completed}}" class="completed-time">已完成 {{item.completedTime}}</text>
          </view>
          <view class="task-time">
            <text>{{item.time}}</text>
            <text wx:if="{{item.completed}}" class="completed-icon">✓</text>
          </view>
        </view>
      </block>
      <view wx:if="{{tasks.length === 0}}" class="empty-state">
        <text class="empty-text">暂无待办任务</text>
      </view>
    </view>
  </view>

  <!-- 知识库 -->
  <view class="knowledge-section">
    <view class="section-header">
      <text class="section-title">知识库</text>
      <text class="more-text" bindtap="onViewAllKnowledge">查看全部</text>
    </view>
    <view class="knowledge-list">
      <block wx:for="{{knowledgeList}}" wx:key="id">
        <view class="knowledge-item" bindtap="onKnowledgeItemTap" data-id="{{item.id}}">
          <text class="knowledge-title">{{item.title}}</text>
          <text class="knowledge-summary">{{item.summary}}</text>
          <text class="knowledge-time">{{item.publishTime}}</text>
        </view>
      </block>
      <view wx:if="{{knowledgeList.length === 0}}" class="empty-state">
        <text class="empty-text">暂无知识内容</text>
      </view>
    </view>
  </view>
</view>