/**
 * 通用组件库统一导出
 * 提供组件注册配置和工具函数
 */

// 组件路径映射
const COMPONENT_PATHS = {
  // 基础组件
  'c-button': '/components/common/button/button',
  'c-card': '/components/common/card/card',
  'c-loading': '/components/common/loading/loading',
  'c-empty-state': '/components/common/empty-state/empty-state',
  'c-search-bar': '/components/common/search-bar/search-bar',
  
  // 业务组件映射
  'c-list-item': '/components/list-item/list-item',
  'c-tab-bar': '/components/tab-bar/tab-bar',
  'c-icon': '/components/icon/icon',
  'c-modal': '/components/modal/modal',
  'c-form-modal': '/components/form-modal/form-modal',
  'c-form-builder': '/components/form-builder/form-builder',
  'c-weather': '/components/weather/weather',
  'c-weather-compact': '/components/weather-compact/weather-compact',
  'c-goose-price': '/components/goose-price/goose-price',
  'c-health-trend-chart': '/components/health-trend-chart/health-trend-chart',
  'c-environment-data-item': '/components/environment-data-item/environment-data-item',
  'c-section-header': '/components/section-header/section-header',
  'c-trend-chart': '/components/trend-chart/trend-chart'
};

// 常用组件集合
const COMMON_COMPONENTS = {
  'c-button': COMPONENT_PATHS['c-button'],
  'c-card': COMPONENT_PATHS['c-card'],
  'c-loading': COMPONENT_PATHS['c-loading'],
  'c-empty-state': COMPONENT_PATHS['c-empty-state'],
  'c-search-bar': COMPONENT_PATHS['c-search-bar'],
  'c-list-item': COMPONENT_PATHS['c-list-item'],
  'c-tab-bar': COMPONENT_PATHS['c-tab-bar']
};

// 表单组件集合
const FORM_COMPONENTS = {
  'c-form-modal': COMPONENT_PATHS['c-form-modal'],
  'c-form-builder': COMPONENT_PATHS['c-form-builder'],
  'c-modal': COMPONENT_PATHS['c-modal']
};

// 业务组件集合
const BUSINESS_COMPONENTS = {
  'c-weather': COMPONENT_PATHS['c-weather'],
  'c-weather-compact': COMPONENT_PATHS['c-weather-compact'],
  'c-goose-price': COMPONENT_PATHS['c-goose-price'],
  'c-health-trend-chart': COMPONENT_PATHS['c-health-trend-chart'],
  'c-environment-data-item': COMPONENT_PATHS['c-environment-data-item']
};

// 全部组件
const ALL_COMPONENTS = {
  ...COMMON_COMPONENTS,
  ...FORM_COMPONENTS,
  ...BUSINESS_COMPONENTS
};

/**
 * 获取组件注册配置
 * @param {Array|String} components 组件名称或数组，支持预设集合名
 * @returns {Object} 组件注册配置对象
 */
function getComponentConfig(components = 'common') {
  const config = {};
  
  if (typeof components === 'string') {
    // 预设集合
    switch (components) {
      case 'common':
        Object.assign(config, COMMON_COMPONENTS);
        break;
      case 'form':
        Object.assign(config, FORM_COMPONENTS);
        break;
      case 'business':
        Object.assign(config, BUSINESS_COMPONENTS);
        break;
      case 'all':
        Object.assign(config, ALL_COMPONENTS);
        break;
      default:
        // 单个组件
        if (COMPONENT_PATHS[components]) {
          config[components] = COMPONENT_PATHS[components];
        }
        break;
    }
  } else if (Array.isArray(components)) {
    // 组件数组
    components.forEach(name => {
      if (COMPONENT_PATHS[name]) {
        config[name] = COMPONENT_PATHS[name];
      }
    });
  }
  
  return config;
}

/**
 * 检查组件是否存在
 * @param {String} componentName 组件名称
 * @returns {Boolean}
 */
function hasComponent(componentName) {
  return !!COMPONENT_PATHS[componentName];
}

/**
 * 获取组件路径
 * @param {String} componentName 组件名称
 * @returns {String|null}
 */
function getComponentPath(componentName) {
  return COMPONENT_PATHS[componentName] || null;
}

/**
 * 获取所有可用组件列表
 * @returns {Array}
 */
function getAvailableComponents() {
  return Object.keys(COMPONENT_PATHS);
}

/**
 * 组件使用统计（用于性能优化）
 */
const componentUsage = {
  stats: {},
  
  // 记录组件使用
  record(componentName) {
    if (!this.stats[componentName]) {
      this.stats[componentName] = 0;
    }
    this.stats[componentName]++;
  },
  
  // 获取使用统计
  getStats() {
    return { ...this.stats };
  },
  
  // 获取最常用的组件
  getMostUsed(limit = 5) {
    return Object.entries(this.stats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([name]) => name);
  },
  
  // 清除统计
  clear() {
    this.stats = {};
  }
};

// 导出所有功能
module.exports = {
  // 组件路径映射
  COMPONENT_PATHS,
  COMMON_COMPONENTS,
  FORM_COMPONENTS,
  BUSINESS_COMPONENTS,
  ALL_COMPONENTS,
  
  // 工具函数
  getComponentConfig,
  hasComponent,
  getComponentPath,
  getAvailableComponents,
  
  // 使用统计
  componentUsage
};