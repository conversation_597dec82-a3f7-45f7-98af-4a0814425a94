/* components/oa/approval-flow/approval-flow.wxss */

.approval-flow {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx;
}

/* 流程头部 */
.flow-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.flow-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.flow-status {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 审批步骤列表 */
.approval-steps {
  position: relative;
}

.approval-step {
  display: flex;
  margin-bottom: 32rpx;
  position: relative;
}

.approval-step:last-child {
  margin-bottom: 0;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
  position: relative;
}

.step-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F2F2F7;
  border: 2rpx solid #E5E5E7;
  position: relative;
  z-index: 2;
}

.icon-text {
  font-size: 24rpx;
}

/* 状态样式 */
.approval-step.status-approved .step-icon {
  background-color: #E8F5E8;
  border-color: #00A86B;
}

.approval-step.status-rejected .step-icon {
  background-color: #FFE8E8;
  border-color: #FF3B30;
}

.approval-step.status-pending .step-icon {
  background-color: #FFF4E6;
  border-color: #FF9500;
}

.approval-step.current-user .step-icon {
  background-color: #E3F2FD;
  border-color: #007AFF;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.4); }
  70% { box-shadow: 0 0 0 20rpx rgba(0, 122, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 122, 255, 0); }
}

/* 连接线 */
.step-connector {
  width: 4rpx;
  height: 60rpx;
  margin-top: 8rpx;
  position: relative;
  z-index: 1;
}

.step-connector.completed {
  background-color: #00A86B;
}

.step-connector.rejected {
  background-color: #FF3B30;
}

.step-connector.pending {
  background-color: #E5E5E7;
}

/* 步骤内容 */
.step-content {
  flex: 1;
  min-width: 0;
}

.step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.step-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  flex: 1;
  margin-right: 16rpx;
}

/* 步骤信息 */
.step-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 26rpx;
}

.step-info:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #8E8E93;
  margin-right: 8rpx;
  min-width: 120rpx;
}

.info-value {
  color: #1D1D1F;
  flex: 1;
}

.info-value.deadline {
  color: #FF9500;
}

/* 审批意见 */
.step-remarks {
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
}

.remarks-label {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.remarks-content {
  font-size: 26rpx;
  color: #1D1D1F;
  line-height: 1.5;
}

/* 操作按钮 */
.step-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn::after {
  border: none;
}

.action-btn.approve {
  background-color: #00A86B;
  color: #FFFFFF;
}

.action-btn.reject {
  background-color: #FF3B30;
  color: #FFFFFF;
}

/* 空状态 */
.empty-flow {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 尺寸变体 */
.approval-flow.small {
  padding: 24rpx;
}

.approval-flow.small .flow-title {
  font-size: 28rpx;
}

.approval-flow.small .step-icon {
  width: 48rpx;
  height: 48rpx;
}

.approval-flow.small .step-name {
  font-size: 26rpx;
}

.approval-flow.small .step-info {
  font-size: 24rpx;
}

.approval-flow.large {
  padding: 40rpx;
}

.approval-flow.large .flow-title {
  font-size: 36rpx;
}

.approval-flow.large .step-icon {
  width: 80rpx;
  height: 80rpx;
}

.approval-flow.large .step-name {
  font-size: 32rpx;
}

.approval-flow.large .step-info {
  font-size: 28rpx;
}
