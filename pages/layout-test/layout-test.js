/**
 * 布局测试页面
 * 用于展示优化后的生产记录项布局
 */

Page({
  data: {
    // 入栏记录 - 待审核状态
    entryRecord: {
      id: 'entry_001',
      type: 'entry',
      batch: 'QY-20230616',
      date: '2023-06-16',
      status: 'pending',
      count: 1000,
      source: '江苏优质鹅苗场',
      totalCost: 28500,
      financeLinked: true,
      details: {
        count: 1000,
        source: '江苏优质鹅苗场',
        cost: '28500.00'
      }
    },

    // 出栏记录 - 已审核状态
    saleRecord: {
      id: 'sale_001',
      type: 'sale',
      batch: 'QY-20230616',
      date: '2023-06-15',
      status: 'approved',
      saleCount: 200,
      totalWeight: 640,
      unitPrice: 28,
      totalIncome: 17920,
      financeLinked: true,
      details: {
        count: 200,
        weight: '640.0',
        price: '28.00',
        totalIncome: '17920.00'
      }
    },

    // 称重记录 - 已完成状态
    weightRecord: {
      id: 'weight_001',
      type: 'weight',
      batch: 'QY-20230605',
      date: '2023-06-14',
      status: 'completed',
      weightCount: 500,
      averageWeight: 2.3,
      financeLinked: false,
      details: {
        count: 500,
        weight: '2.3'
      }
    },

    // 无状态记录
    noStatusRecord: {
      id: 'entry_002',
      type: 'entry',
      batch: 'QY-20230605',
      date: '2023-06-05',
      status: null,
      count: 800,
      source: '本地养殖场',
      totalCost: 22400,
      financeLinked: false,
      details: {
        count: 800,
        source: '本地养殖场',
        cost: '22400.00'
      }
    }
  },

  onLoad() {
    console.log('布局测试页面加载完成');
  },

  onShow() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '布局优化测试'
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '生产记录布局优化测试',
      path: '/pages/layout-test/layout-test'
    };
  }
});
