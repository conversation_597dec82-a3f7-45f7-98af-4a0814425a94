/* pages/task/detail/detail.wxss */

.container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
}

/* ==================== 任务头部 ==================== */
.task-header {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  color: white;
  position: relative;
  overflow: hidden;
}

.task-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.task-status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-md);
  position: relative;
  z-index: 1;
}

.task-status.pending {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1rpx solid rgba(255, 193, 7, 0.3);
}

.task-status.completed {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1rpx solid rgba(40, 167, 69, 0.3);
}

.task-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-lg);
  position: relative;
  z-index: 1;
}

.task-meta {
  position: relative;
  z-index: 1;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-size: var(--text-sm);
  opacity: 0.8;
  margin-right: var(--space-sm);
  min-width: 140rpx;
}

.meta-value {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* ==================== 卡片样式 ==================== */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
}

.card-header {
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-md);
  border-bottom: 1rpx solid var(--border-light);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.card-content {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* ==================== 任务描述 ==================== */
.task-description {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

/* ==================== 任务要求列表 ==================== */
.requirement-list {
  margin-top: var(--space-sm);
}

.requirement-item {
  margin-bottom: var(--space-sm);
  padding-left: var(--space-md);
}

.requirement-item:last-child {
  margin-bottom: 0;
}

.requirement-text {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

/* ==================== 区域信息 ==================== */
.area-text {
  font-size: var(--text-base);
  color: var(--primary);
  font-weight: var(--font-medium);
  padding: var(--space-sm) var(--space-md);
  background-color: var(--primary-subtle);
  border-radius: var(--radius-md);
  display: inline-block;
}

/* ==================== 相关链接 ==================== */
.link-list {
  margin-top: var(--space-sm);
}

.link-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  margin-bottom: var(--space-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  transition: var(--transition-all);
  cursor: pointer;
}

.link-item:last-child {
  margin-bottom: 0;
}

.link-item:active {
  background-color: var(--primary-subtle);
  transform: scale(0.98);
}

.link-text {
  font-size: var(--text-base);
  color: var(--primary);
  font-weight: var(--font-medium);
}

.link-arrow {
  font-size: var(--text-lg);
  color: var(--text-tertiary);
  font-weight: var(--font-bold);
}

/* ==================== 操作按钮 ==================== */
.action-buttons {
  margin-top: var(--space-xl);
  padding: var(--space-lg) 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-lg {
  height: 96rpx;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  border-radius: var(--radius-xl);
}

/* ==================== 完成状态 ==================== */
.completed-notice {
  text-align: center;
  padding: var(--space-xl);
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  border-radius: var(--radius-xl);
  margin-top: var(--space-xl);
}

.notice-icon {
  font-size: 120rpx;
  color: var(--success);
  margin-bottom: var(--space-md);
  animation: bounce 0.6s ease-in-out;
}

.notice-text {
  display: block;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--success);
  margin-bottom: var(--space-sm);
}

.notice-time {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

/* ==================== 动画效果 ==================== */
@keyframes bounce {

  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }

  40%,
  43% {
    transform: translateY(-20rpx);
  }

  70% {
    transform: translateY(-10rpx);
  }

  90% {
    transform: translateY(-4rpx);
  }
}

/* ==================== 响应式设计 ==================== */
@media screen and (max-width: 600rpx) {
  .container {
    padding: var(--space-md);
  }

  .task-header {
    padding: var(--space-lg);
  }

  .card {
    padding: var(--space-md);
  }

  .meta-label {
    min-width: 120rpx;
  }
}

/* ==================== 深色模式适配 ==================== */
.dark-mode .card {
  background-color: var(--bg-primary);
  border-color: var(--border-medium);
}

.dark-mode .link-item {
  background-color: var(--bg-tertiary);
}

.dark-mode .link-item:active {
  background-color: var(--primary-subtle);
}

.dark-mode .completed-notice {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 167, 69, 0.1));
}