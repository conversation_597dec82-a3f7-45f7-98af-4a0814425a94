/* components/trend-chart/trend-chart.wxss */

.trend-chart-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  /* 强制覆盖所有颜色指示器为方形 */
  --color-indicator-radius: 4rpx;
}

/* 全局颜色指示器样式重置 */
.trend-chart-container .legend-color,
.trend-chart-container .value-color,
.trend-chart-container .current-color {
  border-radius: var(--color-indicator-radius) !important;
}

/* 强制方形指示器样式 v4 - 超级最高优先级 */
.trend-chart-container .legend-color,
.trend-chart-container .value-color,
.trend-chart-container .current-color,
.square-indicators-v2 .legend-color,
.square-indicators-v2 .value-color,
.square-indicators-v2 .current-color,
.legend-color,
.value-color,
.current-color {
  border-radius: 4rpx !important;
  -webkit-border-radius: 4rpx !important;
  -moz-border-radius: 4rpx !important;
  width: 20rpx !important;
  height: 20rpx !important;
}

.square-indicators-v2 .legend-color {
  width: 24rpx !important;
  height: 24rpx !important;
}

.square-indicators-v2 .value-color {
  width: 20rpx !important;
  height: 20rpx !important;
}

.square-indicators-v2 .current-color {
  width: 20rpx !important;
  height: 20rpx !important;
}

/* 图表头部 */
.chart-header {
  padding: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.chart-title-section {
  margin-bottom: var(--space-md);
}

.chart-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: var(--leading-tight);
}

.chart-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-top: var(--space-xs);
  display: block;
}

/* 时间筛选器 */
.time-filter {
  margin-top: var(--space-md);
}

.filter-scroll {
  width: 100%;
}

.filter-options {
  display: flex;
  gap: var(--space-sm);
  white-space: nowrap;
  padding: var(--space-xs) 0;
}

.filter-option {
  padding: 8rpx 16rpx !important;
  margin: 0 4rpx !important;
  background: #F5F5F5 !important;
  color: #666666 !important;
  border-radius: 12rpx !important;
  font-size: 24rpx !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  border: none !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  display: inline-block !important;
  text-align: center !important;
  min-width: auto !important;
  max-width: none !important;
  box-sizing: border-box !important;
}

.filter-option.active {
  background: #0066CC !important;
  color: #FFFFFF !important;
  font-weight: 500 !important;
  border: none !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 102, 204, 0.2) !important;
}

.filter-option:not(.active):hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* 强制修复时间选择器样式 - 最高优先级 */
.square-indicators-v2 .filter-option,
.time-filter-fixed .filter-option-fixed,
.filter-options-fixed .filter-option {
  padding: 8rpx 16rpx !important;
  margin: 0 4rpx !important;
  background: #F5F5F5 !important;
  color: #666666 !important;
  border-radius: 12rpx !important;
  font-size: 24rpx !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  border: none !important;
  box-shadow: none !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  display: inline-block !important;
  text-align: center !important;
  min-width: auto !important;
  max-width: none !important;
}

/* 激活状态的时间选择器样式 */
.square-indicators-v2 .filter-option.active,
.time-filter-fixed .filter-option-fixed.active,
.filter-options-fixed .filter-option.active {
  background: #0066CC !important;
  color: #FFFFFF !important;
  font-weight: 500 !important;
  border: none !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 102, 204, 0.2) !important;
}

/* ==================== 超强优先级时间选择器修复 ==================== */
/* 多重选择器确保最高优先级 - 解决椭圆形指示器问题 */

/* 删除冲突的样式定义 - 已被上面的样式覆盖 */

/* 激活状态 */
.trend-chart-container .time-filter .filter-option.active,
.square-indicators-v2 .time-filter .filter-option.active,
.trend-chart-container .filter-options .filter-option.active,
.square-indicators-v2 .filter-options .filter-option.active,
.chart-header .time-filter .filter-option.active,
.chart-header .filter-options .filter-option.active {
  background: #0066CC !important;
  color: #FFFFFF !important;
  font-weight: 500 !important;
  border-color: #0066CC !important;
}

/* 悬停状态 */
.trend-chart-container .time-filter .filter-option:not(.active):hover,
.square-indicators-v2 .time-filter .filter-option:not(.active):hover,
.trend-chart-container .filter-options .filter-option:not(.active):hover,
.square-indicators-v2 .filter-options .filter-option:not(.active):hover,
.chart-header .time-filter .filter-option:not(.active):hover,
.chart-header .filter-options .filter-option:not(.active):hover {
  background: #F0F0F0 !important;
  color: #333333 !important;
}

/* ==================== 删除冲突的调试样式 ==================== */
/* 已被上面的统一样式替代 */

/* 图例 */
.chart-legend {
  padding: 0 var(--space-lg) var(--space-md);
}

.legend-scroll {
  width: 100%;
}

.legend-list {
  display: flex;
  gap: var(--space-lg);
  white-space: nowrap;
  padding: var(--space-xs) 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  flex-shrink: 0;
}

.legend-color {
  width: 20rpx !important;
  height: 20rpx !important;
  border-radius: 4rpx !important;
  -webkit-border-radius: 4rpx !important;
  -moz-border-radius: 4rpx !important;
  flex-shrink: 0;
  box-sizing: border-box !important;
}

/* 最强制的方形图例样式 - 解决椭圆形问题，修复WXSS兼容性 */
.trend-chart-container .legend-item .legend-color,
.chart-legend .legend-item .legend-color,
.legend-scroll .legend-item .legend-color,
.legend-color {
  border-radius: 4rpx !important;
  -webkit-border-radius: 4rpx !important;
  -moz-border-radius: 4rpx !important;
  width: 20rpx !important;
  height: 20rpx !important;
  box-sizing: border-box !important;
}

.legend-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  white-space: nowrap;
}

/* 图表主体 */
.chart-body {
  position: relative;
  min-height: 500rpx;
}

.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-top: var(--space-md);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Canvas */
.trend-canvas {
  width: 100%;
  display: block;
  background: transparent;
}

/* 提示框 - 透明背景，内容清晰可见 */
.tooltip {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx;
  padding: 14rpx 18rpx;
  min-width: 140rpx;
  max-width: 180rpx;
  z-index: 200;
  opacity: 0;
  transform: translateY(-6rpx) scale(0.9);
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  pointer-events: none;
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.15),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  font-size: 22rpx;
  line-height: 1.25;
}

.tooltip.show {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.tooltip-content {
  color: #333333;
}

.tooltip-time {
  font-size: 22rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  padding-bottom: 6rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.15);
  color: #666666;
}

.tooltip-values {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.tooltip-value-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  line-height: 1.2;
}

.value-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 3rpx !important;
  flex-shrink: 0;
}

.value-label {
  color: #666666;
  flex: 1;
  font-size: 20rpx;
}

.value-number {
  color: #333333;
  font-weight: 700;
  white-space: nowrap;
  font-size: 22rpx;
}

/* 不显示箭头，使用圆角卡片风格 */
.tooltip-arrow {
  display: none;
}

/* 当前值显示 */
.current-values {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  min-width: 240rpx;
}

.current-time {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  text-align: center;
}

.current-data {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.current-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.current-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx !important;
  flex-shrink: 0;
}

.current-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.current-value {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-2xl);
  min-height: 500rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.4;
  margin-bottom: var(--space-lg);
}

.empty-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin-bottom: var(--space-lg);
}

.refresh-btn {
  background: var(--primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-md) var(--space-xl);
  font-size: var(--text-sm);
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2rpx);
}

/* 图表底部 */
.chart-footer {
  padding: var(--space-md) var(--space-lg);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
}

.update-time {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.chart-actions {
  display: flex;
  gap: var(--space-md);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  background: transparent;
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-dark);
}

.btn-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 响应式适配 */
@media screen and (max-width: 600rpx) {
  .chart-header {
    padding: var(--space-md);
  }

  .filter-options {
    gap: var(--space-xs);
  }

  .filter-option {
    padding: 4rpx 6rpx;
    font-size: var(--text-xs);
    border-radius: 10rpx;
  }

  .legend-list {
    gap: var(--space-md);
  }

  .current-values {
    position: static;
    margin: var(--space-md);
  }

  .tooltip {
    max-width: 300rpx;
    padding: var(--space-sm);
  }
}

/* 深色模式适配 */
.dark .trend-chart-container {
  background: var(--bg-primary-dark);
  border-color: var(--border-dark);
}

.dark .chart-header {
  border-color: var(--border-dark);
}

.dark .filter-option {
  background: var(--bg-secondary-dark);
  color: var(--text-secondary-dark);
}

.dark .filter-option.active {
  background: var(--primary);
  color: var(--text-inverse);
}

.dark .legend-text {
  color: var(--text-secondary-dark);
}

.dark .current-values {
  background: var(--bg-primary-dark);
  border-color: var(--border-dark);
}

.dark .chart-footer {
  background: var(--bg-secondary-dark);
  border-color: var(--border-dark);
}

.dark .action-btn {
  border-color: var(--border-dark);
  color: var(--text-secondary-dark);
}

.dark .action-btn:hover {
  background: var(--bg-hover-dark);
  color: var(--text-primary-dark);
}