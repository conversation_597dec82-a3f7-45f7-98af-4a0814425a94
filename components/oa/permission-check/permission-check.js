// components/oa/permission-check/permission-check.js

/**
 * OA权限检查组件
 * 统一处理权限验证和错误提示
 */

const { isLoggedIn, getCurrentUser, hasPermission } = require('../../../utils/auth-helper');
const { getCurrentUserInfo, getUserPermissions } = require('../../../utils/user-info');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 需要的权限
    requiredPermissions: {
      type: Array,
      value: []
    },
    // 需要的角色
    requiredRoles: {
      type: Array,
      value: []
    },
    // 权限不足时的提示标题
    deniedTitle: {
      type: String,
      value: '权限不足'
    },
    // 权限不足时的提示内容
    deniedContent: {
      type: String,
      value: '您当前没有访问此功能的权限'
    },
    // 是否显示详细的权限要求
    showRequirements: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    hasPermission: false,
    userInfo: null,
    permissions: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 检查权限
     */
    checkPermission() {
      // 检查登录状态
      if (!isLoggedIn()) {
        this.triggerEvent('permissionDenied', {
          type: 'not_logged_in',
          message: '请先登录系统'
        });
        return false;
      }

      // 获取用户信息和权限
      const userInfo = getCurrentUserInfo();
      const permissions = getUserPermissions();
      
      if (!userInfo) {
        this.triggerEvent('permissionDenied', {
          type: 'user_info_error',
          message: '无法获取用户信息'
        });
        return false;
      }

      this.setData({
        userInfo,
        permissions
      });

      // 检查角色权限
      if (this.properties.requiredRoles.length > 0) {
        const userRole = userInfo.roleCode || userInfo.role;
        const hasRequiredRole = this.properties.requiredRoles.includes(userRole);
        
        if (!hasRequiredRole) {
          this.triggerEvent('permissionDenied', {
            type: 'role_insufficient',
            message: this.generateRoleErrorMessage(),
            requiredRoles: this.properties.requiredRoles,
            userRole
          });
          return false;
        }
      }

      // 检查具体权限
      if (this.properties.requiredPermissions.length > 0) {
        const hasRequiredPermissions = this.properties.requiredPermissions.every(perm => {
          return permissions[perm] === true;
        });

        if (!hasRequiredPermissions) {
          this.triggerEvent('permissionDenied', {
            type: 'permission_insufficient',
            message: this.generatePermissionErrorMessage(),
            requiredPermissions: this.properties.requiredPermissions,
            userPermissions: permissions
          });
          return false;
        }
      }

      this.setData({ hasPermission: true });
      this.triggerEvent('permissionGranted', {
        userInfo,
        permissions
      });
      
      return true;
    },

    /**
     * 生成角色权限错误信息
     */
    generateRoleErrorMessage() {
      const roleNames = {
        'admin': '管理员',
        'manager': '经理',
        'finance': '财务',
        'hr': '人事',
        'user': '普通员工'
      };

      const requiredRoleNames = this.properties.requiredRoles.map(role => 
        roleNames[role] || role
      ).join('、');

      return `此功能仅限${requiredRoleNames}使用。\n\n如需使用请联系管理员分配相应角色权限。`;
    },

    /**
     * 生成权限错误信息
     */
    generatePermissionErrorMessage() {
      const permissionNames = {
        'canCreateApplication': '创建申请',
        'canApprove': '审批权限',
        'finance_view': '查看财务',
        'finance_export': '导出财务',
        'user_manage': '用户管理',
        'role_manage': '角色管理'
      };

      const requiredPermissionNames = this.properties.requiredPermissions.map(perm =>
        permissionNames[perm] || perm
      ).join('、');

      return `您缺少必要的权限：${requiredPermissionNames}。\n\n如需使用此功能，请联系管理员分配相应权限。`;
    },

    /**
     * 显示权限不足提示
     */
    showPermissionDeniedModal(details = {}) {
      wx.showModal({
        title: this.properties.deniedTitle,
        content: details.message || this.properties.deniedContent,
        confirmText: '我知道了',
        showCancel: false,
        success: () => {
          this.triggerEvent('modalClosed', details);
        }
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.checkPermission();
    }
  }
});