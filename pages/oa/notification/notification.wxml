<!-- pages/oa/notification/notification.wxml -->

<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">系统通知</text>
    <text class="page-subtitle">消息中心 · 及时获取重要信息</text>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-card total">
        <text class="stat-number">{{statistics.total}}</text>
        <text class="stat-label">总通知</text>
      </view>
      <view class="stat-card unread">
        <text class="stat-number">{{statistics.unread}}</text>
        <text class="stat-label">未读</text>
        <view wx:if="{{statistics.unread > 0}}" class="stat-badge">{{statistics.unread}}</view>
      </view>
      <view class="stat-card today">
        <text class="stat-number">{{statistics.today}}</text>
        <text class="stat-label">今日</text>
      </view>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <button class="filter-btn" bindtap="toggleFilters">
      筛选 {{showFilters ? '▲' : '▼'}}
    </button>
    <button class="mark-all-btn" bindtap="markAllAsRead" disabled="{{statistics.unread === 0}}">
      全部已读
    </button>
  </view>

  <!-- 筛选面板 -->
  <view wx:if="{{showFilters}}" class="filter-panel">
    <view class="filter-row">
      <view class="filter-item">
        <text class="filter-label">类型</text>
        <picker
          mode="selector"
          range="{{typeOptions}}"
          range-key="label"
          value="{{typeIndex}}"
          bindchange="onFilterChange"
          data-field="type"
        >
          <view class="picker">
            {{selectedTypeLabel}}
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="filter-item">
        <text class="filter-label">状态</text>
        <picker
          mode="selector"
          range="{{readOptions}}"
          range-key="label"
          value="{{readIndex}}"
          bindchange="onFilterChange"
          data-field="read"
        >
          <view class="picker">
            {{selectedReadLabel}}
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 通知列表 -->
  <view class="notification-list">
    <block wx:for="{{notifications}}" wx:key="id">
      <view 
        class="notification-item {{!item.read ? 'unread' : ''}}"
        bindtap="onNotificationTap"
        data-notification="{{item}}"
      >
        <!-- 通知类型图标 -->
        <view class="notification-icon {{item.type}}">
                      <text wx:if="{{item.type === 'system'}}">系统</text>
                      <text wx:elif="{{item.type === 'approval'}}">审批</text>
                      <text wx:elif="{{item.type === 'task'}}">任务</text>
          <text wx:elif="{{item.type === 'announcement'}}">📢</text>
          <text wx:else>💬</text>
        </view>

        <!-- 通知内容 -->
        <view class="notification-content">
          <view class="notification-header">
            <text class="notification-title">{{item.title}}</text>
            <view class="notification-meta">
              <text class="type-tag {{item.type}}">{{item.typeLabel}}</text>
              <text wx:if="{{!item.read}}" class="unread-badge">未读</text>
            </view>
          </view>
          
          <text class="notification-text">{{item.content}}</text>
          
          <view class="notification-footer">
            <text class="sender">{{item.sender}}</text>
            <text class="time">{{item.createdTimeAgo}}</text>
            <text wx:if="{{item.isToday}}" class="today-badge">今天</text>
          </view>
        </view>

        <!-- 未读指示器 -->
        <view wx:if="{{!item.read}}" class="unread-indicator"></view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{notifications.length === 0 && !loading}}" class="empty-state">
      <text class="empty-icon">📭</text>
      <text class="empty-text">暂无通知</text>
      <text class="empty-desc">系统会在这里推送重要消息给您</text>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-more">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{!pagination.hasMore && notifications.length > 0}}" class="no-more">
      <text>没有更多通知了</text>
    </view>
  </view>
</view>