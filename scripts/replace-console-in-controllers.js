const fs = require('fs');
const path = require('path');

const controllersDir = path.resolve(__dirname, '../backend/controllers');

const mapMethod = {
  error: 'error',
  warn: 'warn',
  log: 'info',
};

function transformLine(line) {
  // 跳过整行注释
  const trimmed = line.trimStart();
  if (trimmed.startsWith('//')) return line;

  let replaced = line;
  let hit = false;

  // 逐类替换，保持其它内容不变
  ['error', 'warn', 'log'].forEach((m) => {
    const re = new RegExp(`console\\.${m}\\(`, 'g');
    if (re.test(replaced)) {
      hit = true;
      replaced = replaced.replace(re, `try { const { Logger } = require('..\\/middleware\\/errorHandler'); Logger.${mapMethod[m]}(`);
    }
  });

  if (hit) {
    // 若本行未包含 try 包裹（避免重复包裹），在行尾补充防御性 catch
    if (!/}\s*catch\s*\(\_\)\s*\}\s*$/.test(replaced)) {
      // 去掉行尾换行后再附加
      replaced = replaced.replace(/\r?\n?$/, '') + ' } catch(_) {}\n';
    }
  }

  return replaced;
}

function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split(/\r?\n/);
  const newLines = lines.map((line) => transformLine(line));
  const newContent = newLines.join('\n');
  if (newContent !== content) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`Updated: ${path.relative(process.cwd(), filePath)}`);
  }
}

function main() {
  const files = fs.readdirSync(controllersDir)
    .filter((f) => f.endsWith('.js'))
    .map((f) => path.join(controllersDir, f));

  files.forEach(processFile);
}

main();
