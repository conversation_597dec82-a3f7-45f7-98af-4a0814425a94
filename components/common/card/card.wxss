/* 通用卡片组件样式 */

:host {
  --c-card-bg-color: #FFFFFF;
  --c-card-border-color: #E5E7EB;
  --c-card-shadow-color: rgba(0, 0, 0, 0.1);
  --c-card-title-color: #333333;
  --c-card-subtitle-color: #666666;
  --c-card-arrow-color: #9CA3AF;
  
  --c-card-border-radius-small: 4rpx;
  --c-card-border-radius-medium: 12rpx;
  --c-card-border-radius-large: 20rpx;
  --c-card-border-radius-round: 1000rpx;
  
  --c-card-padding-small: 20rpx;
  --c-card-padding-medium: 30rpx;
  --c-card-padding-large: 40rpx;
  
  --c-card-margin-small: 10rpx;
  --c-card-margin-medium: 20rpx;
  --c-card-margin-large: 30rpx;
  
  --c-card-shadow-small: 0 2rpx 4rpx var(--c-card-shadow-color);
  --c-card-shadow-medium: 0 4rpx 8rpx var(--c-card-shadow-color);
  --c-card-shadow-large: 0 8rpx 16rpx var(--c-card-shadow-color);
  
  --c-card-transition: all 0.2s ease;
}

/* 基础卡片样式 */
.c-card {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: var(--c-card-bg-color);
  border: 2rpx solid transparent;
  overflow: hidden;
  transition: var(--c-card-transition);
  box-sizing: border-box;
}

/* ===== 内边距样式 ===== */
.c-card--padding-none {
  padding: 0;
}

.c-card--padding-small {
  padding: var(--c-card-padding-small);
}

.c-card--padding-medium {
  padding: var(--c-card-padding-medium);
}

.c-card--padding-large {
  padding: var(--c-card-padding-large);
}

/* ===== 外边距样式 ===== */
.c-card--margin-small {
  margin: var(--c-card-margin-small);
}

.c-card--margin-medium {
  margin: var(--c-card-margin-medium);
}

.c-card--margin-large {
  margin: var(--c-card-margin-large);
}

/* ===== 阴影样式 ===== */
.c-card--shadow-small {
  box-shadow: var(--c-card-shadow-small);
}

.c-card--shadow-medium {
  box-shadow: var(--c-card-shadow-medium);
}

.c-card--shadow-large {
  box-shadow: var(--c-card-shadow-large);
}

/* ===== 圆角样式 ===== */
.c-card--radius-none {
  border-radius: 0;
}

.c-card--radius-small {
  border-radius: var(--c-card-border-radius-small);
}

.c-card--radius-medium {
  border-radius: var(--c-card-border-radius-medium);
}

.c-card--radius-large {
  border-radius: var(--c-card-border-radius-large);
}

.c-card--radius-round {
  border-radius: var(--c-card-border-radius-round);
}

/* ===== 边框样式 ===== */
.c-card--border {
  border-color: var(--c-card-border-color);
}

/* ===== 交互状态 ===== */
.c-card--clickable {
  cursor: pointer;
  user-select: none;
}

.c-card--clickable:active {
  transform: scale(0.98);
  box-shadow: var(--c-card-shadow-small);
}

.c-card--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== 卡片内容区域 ===== */

/* 卡片头部 */
.c-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.c-card__header:last-child {
  margin-bottom: 0;
}

.c-card__header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.c-card__header-extra {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

/* 卡片标题 */
.c-card__title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--c-card-title-color);
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.c-card__title:last-child {
  margin-bottom: 0;
}

/* 卡片副标题 */
.c-card__subtitle {
  font-size: 26rpx;
  color: var(--c-card-subtitle-color);
  line-height: 1.4;
}

/* 箭头图标 */
.c-card__arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

.c-card__arrow-icon {
  font-size: 32rpx;
  color: var(--c-card-arrow-color);
  line-height: 1;
}

/* 卡片主体 */
.c-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 卡片底部 */
.c-card__footer {
  margin-top: 20rpx;
}

.c-card__footer:empty {
  display: none;
}

/* ===== 特殊变体 ===== */

/* 无头部时的主体样式 */
.c-card__body:first-child {
  margin-top: 0;
}

/* 无底部时的主体样式 */
.c-card__body:last-child {
  margin-bottom: 0;
}

/* ===== 响应式设计 ===== */

/* 小屏幕适配 */
@media (max-width: 750rpx) {
  .c-card--padding-large {
    padding: var(--c-card-padding-medium);
  }
  
  .c-card--margin-large {
    margin: var(--c-card-margin-medium);
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  :host {
    --c-card-bg-color: #1F2937;
    --c-card-border-color: #374151;
    --c-card-title-color: #F9FAFB;
    --c-card-subtitle-color: #D1D5DB;
    --c-card-arrow-color: #6B7280;
  }
}