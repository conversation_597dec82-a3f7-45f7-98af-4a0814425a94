/**
 * API响应工具类
 * 提供标准化的API响应格式
 */

const { Logger } = require("../middleware/errorHandler");

class ApiResponse {
  /**
   * 成功响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} statusCode - HTTP状态码
   */
  static success(res, data = null, message = "操作成功", statusCode = 200) {
    const response = {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
    };

    Logger.info("API Success Response", {
      statusCode,
      message,
      url: res.req?.url,
      method: res.req?.method,
      userId: res.req?.user?.id,
    });

    return res.status(statusCode).json(response);
  }

  /**
   * 分页响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 响应数据
   * @param {Object} pagination - 分页信息
   * @param {string} message - 响应消息
   */
  static paginated(res, data, pagination, message = "获取成功") {
    const response = {
      success: true,
      message,
      data,
      pagination: {
        page: parseInt(pagination.page) || 1,
        limit: parseInt(pagination.limit) || 10,
        total: parseInt(pagination.total) || 0,
        totalPages: Math.ceil(
          (pagination.total || 0) / (pagination.limit || 10),
        ),
        hasNext:
          pagination.page <
          Math.ceil((pagination.total || 0) / (pagination.limit || 10)),
        hasPrev: pagination.page > 1,
      },
      timestamp: new Date().toISOString(),
    };

    Logger.info("API Paginated Response", {
      message,
      pagination: response.pagination,
      url: res.req?.url,
      method: res.req?.method,
      userId: res.req?.user?.id,
    });

    return res.json(response);
  }

  /**
   * 错误响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   * @param {number} statusCode - HTTP状态码
   * @param {string} code - 错误代码
   * @param {*} details - 错误详情
   */
  static error(
    res,
    message = "操作失败",
    statusCode = 500,
    code = null,
    details = null,
  ) {
    const response = {
      success: false,
      error: {
        message,
        code,
        ...(details && { details }),
        ...(process.env.NODE_ENV === "development" &&
          details && { stack: details.stack }),
      },
      timestamp: new Date().toISOString(),
      path: res.req?.url,
      method: res.req?.method,
    };

    Logger.error("API Error Response", {
      statusCode,
      message,
      code,
      url: res.req?.url,
      method: res.req?.method,
      userId: res.req?.user?.id,
      details: details?.message || details,
    });

    return res.status(statusCode).json(response);
  }

  /**
   * 验证错误响应
   * @param {Object} res - Express响应对象
   * @param {Array} errors - 验证错误数组
   * @param {string} message - 错误消息
   */
  static validationError(res, errors, message = "数据验证失败") {
    const response = {
      success: false,
      error: {
        message,
        code: "VALIDATION_ERROR",
        errors: Array.isArray(errors) ? errors : [errors],
      },
      timestamp: new Date().toISOString(),
      path: res.req?.url,
      method: res.req?.method,
    };

    Logger.warn("API Validation Error", {
      message,
      errors: response.error.errors,
      url: res.req?.url,
      method: res.req?.method,
      userId: res.req?.user?.id,
    });

    return res.status(400).json(response);
  }

  /**
   * 未授权响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static unauthorized(res, message = "未授权访问") {
    return this.error(res, message, 401, "UNAUTHORIZED");
  }

  /**
   * 禁止访问响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static forbidden(res, message = "禁止访问") {
    return this.error(res, message, 403, "FORBIDDEN");
  }

  /**
   * 资源未找到响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static notFound(res, message = "资源不存在") {
    return this.error(res, message, 404, "NOT_FOUND");
  }

  /**
   * 冲突响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static conflict(res, message = "资源冲突") {
    return this.error(res, message, 409, "CONFLICT");
  }

  /**
   * 服务器错误响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   * @param {*} details - 错误详情
   */
  static serverError(res, message = "服务器内部错误", details = null) {
    return this.error(res, message, 500, "INTERNAL_ERROR", details);
  }

  /**
   * 创建成功响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 创建的数据
   * @param {string} message - 响应消息
   */
  static created(res, data, message = "创建成功") {
    return this.success(res, data, message, 201);
  }

  /**
   * 更新成功响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 更新的数据
   * @param {string} message - 响应消息
   */
  static updated(res, data = null, message = "更新成功") {
    return this.success(res, data, message, 200);
  }

  /**
   * 删除成功响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 响应消息
   */
  static deleted(res, message = "删除成功") {
    return this.success(res, null, message, 200);
  }

  /**
   * 无内容响应
   * @param {Object} res - Express响应对象
   */
  static noContent(res) {
    Logger.info("API No Content Response", {
      statusCode: 204,
      url: res.req?.url,
      method: res.req?.method,
      userId: res.req?.user?.id,
    });

    return res.status(204).send();
  }

  /**
   * 自定义响应
   * @param {Object} res - Express响应对象
   * @param {number} statusCode - HTTP状态码
   * @param {Object} body - 响应体
   */
  static custom(res, statusCode, body) {
    Logger.info("API Custom Response", {
      statusCode,
      body: typeof body === "object" ? JSON.stringify(body) : body,
      url: res.req?.url,
      method: res.req?.method,
      userId: res.req?.user?.id,
    });

    return res.status(statusCode).json({
      ...body,
      timestamp: new Date().toISOString(),
    });
  }
}

module.exports = ApiResponse;
