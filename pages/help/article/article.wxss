/* 帮助文章详情页样式 */
.article-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 120rpx;
}

/* 文章头部 */
.article-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
  color: #ffffff;
}

.header-meta {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.category-badge,
.difficulty-badge {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.category-badge {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.difficulty-easy {
  background: #d4edda;
  color: #155724;
}

.difficulty-medium {
  background: #fff3cd;
  color: #856404;
}

.difficulty-hard {
  background: #f8d7da;
  color: #721c24;
}

.article-title {
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 20rpx;
  display: block;
}

.article-info {
  display: flex;
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.info-icon {
  font-size: 20rpx;
}

.info-text {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 目录区域 */
.toc-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.toc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.toc-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.toc-toggle {
  font-size: 24rpx;
  color: #007AFF;
}

.toc-content {
  padding: 16rpx 0;
}

.toc-item {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  transition: background 0.3s ease;
}

.toc-item:active {
  background: #f8f9fa;
}

.toc-number {
  width: 40rpx;
  height: 40rpx;
  background: #007AFF;
  color: #ffffff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 500;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.toc-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

/* 文章内容 */
.article-content {
  background: #ffffff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.content-section {
  margin-bottom: 32rpx;
}

.content-section:last-child {
  margin-bottom: 0;
}

/* 章节标题 */
.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
}

/* 段落内容 */
.section-paragraph {
  margin-bottom: 20rpx;
}

.paragraph-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  text-align: justify;
}

/* 图片内容 */
.section-image {
  margin-bottom: 20rpx;
  text-align: center;
}

.content-image {
  width: 100%;
  max-width: 600rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.image-caption {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 12rpx;
  font-style: italic;
}

/* 步骤列表 */
.section-steps {
  margin-bottom: 20rpx;
}

.step-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #ffffff;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.step-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  padding-top: 8rpx;
}

/* 提示框 */
.section-tip {
  background: #e3f2fd;
  border-left: 6rpx solid #2196f3;
  padding: 20rpx;
  border-radius: 0 12rpx 12rpx 0;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.tip-text {
  flex: 1;
  font-size: 26rpx;
  color: #1976d2;
  line-height: 1.5;
}

/* 警告框 */
.section-warning {
  background: #fff3e0;
  border-left: 6rpx solid #ff9800;
  padding: 20rpx;
  border-radius: 0 12rpx 12rpx 0;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
}

.warning-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
  font-size: 26rpx;
  color: #f57c00;
  line-height: 1.5;
}

/* 标签区域 */
.tags-section {
  background: #ffffff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tags-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  color: #666;
  border-radius: 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #007AFF;
  color: #ffffff;
  transform: scale(0.95);
}

/* 操作区域 */
.actions-section {
  background: #ffffff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  transition: all 0.3s ease;
  border: 2rpx solid #e9ecef;
  background: #ffffff;
  color: #666;
}

.action-btn:active {
  transform: scale(0.95);
}

.like-btn.liked {
  background: #ff6b6b;
  color: #ffffff;
  border-color: #ff6b6b;
}

.share-btn:active {
  background: #4ecdc4;
  color: #ffffff;
  border-color: #4ecdc4;
}

.feedback-btn:active {
  background: #feca57;
  color: #ffffff;
  border-color: #feca57;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 22rpx;
}

/* 相关文章 */
.related-articles {
  background: #ffffff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.related-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.related-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.related-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.related-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.related-summary {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-meta {
  display: flex;
  gap: 16rpx;
}

.meta-text {
  font-size: 22rpx;
  color: #999;
}

.related-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 评价区域 */
.rating-section {
  background: #ffffff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  text-align: center;
}

.rating-header {
  margin-bottom: 24rpx;
}

.rating-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.rating-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.rating-btn {
  flex: 1;
  height: 100rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: 2rpx solid #e9ecef;
  background: #ffffff;
  color: #666;
}

.rating-btn:active {
  transform: scale(0.95);
}

.helpful-btn.active {
  background: #d4edda;
  color: #155724;
  border-color: #28a745;
}

.unhelpful-btn.active {
  background: #f8d7da;
  color: #721c24;
  border-color: #dc3545;
}

.rating-icon {
  font-size: 32rpx;
}

.rating-text {
  font-size: 24rpx;
}

.rating-count {
  font-size: 20rpx;
  opacity: 0.8;
}

.rating-tips {
  margin-top: 16rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 16rpx 20rpx calc(16rpx + env(safe-area-inset-bottom));
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid #f0f0f0;
}

.nav-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: 1rpx solid #e9ecef;
  background: #ffffff;
  color: #666;
  margin: 0 6rpx;
}

.nav-btn:active:not([disabled]) {
  transform: scale(0.95);
  background: #007AFF;
  color: #ffffff;
}

.nav-btn[disabled] {
  opacity: 0.4;
}

.nav-icon {
  font-size: 24rpx;
}

.nav-text {
  font-size: 24rpx;
}

/* 浮动目录按钮 */
.floating-toc {
  position: fixed;
  bottom: 140rpx;
  right: 30rpx;
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
}

.floating-toc:active {
  transform: scale(0.9);
}

.toc-icon {
  font-size: 32rpx;
  color: #ffffff;
}

/* 浮动目录弹窗 */
.toc-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  max-height: 70vh;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 36rpx;
  color: #999;
  padding: 8rpx;
}

.modal-toc {
  max-height: 50vh;
  overflow-y: auto;
  padding: 16rpx 0;
}

.modal-toc-item {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  transition: background 0.3s ease;
}

.modal-toc-item:active {
  background: #f8f9fa;
}

.modal-toc-number {
  width: 40rpx;
  height: 40rpx;
  background: #007AFF;
  color: #ffffff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 500;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.modal-toc-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

/* 响应式适配 */
@media (max-width: 480rpx) {

  .action-buttons,
  .rating-buttons {
    flex-direction: column;
  }

  .article-info {
    flex-wrap: wrap;
    gap: 12rpx 20rpx;
  }

  .modal-content {
    width: 90%;
  }
}

/* 动画效果 */
.article-header {
  animation: slideInDown 0.6s ease-out;
}

.toc-section,
.article-content,
.tags-section,
.actions-section,
.related-articles,
.rating-section {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}