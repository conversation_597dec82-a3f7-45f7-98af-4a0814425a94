// pages/profile/help/help.js
Page({
  data: {
    // 帮助分类
    helpCategories: [
      {
        id: 1,
        title: '账户与登录',
        icon: '👤',
        articles: [
          { id: 101, title: '如何注册账户' },
          { id: 102, title: '忘记密码怎么办' },
          { id: 103, title: '如何修改个人信息' }
        ]
      },
      {
        id: 2,
        title: '健康管理',
        icon: '🩺',
        articles: [
          { id: 201, title: '如何添加健康记录' },
          { id: 202, title: 'AI诊断使用说明' },
          { id: 203, title: '健康报告解读' },
          { id: 204, title: '疾病预防指南' }
        ]
      },
      {
        id: 3,
        title: '生产管理',
        icon: '🏭',
        articles: [
          { id: 301, title: '环境监控数据说明' },
          { id: 302, title: '如何管理物料库存' },
          { id: 303, title: '财务记录录入指南' },
          { id: 304, title: '生产报表导出' }
        ]
      },
      {
        id: 4,
        title: '系统设置',
        icon: '⚙️',
        articles: [
          { id: 401, title: '通知设置说明' },
          { id: 402, title: '数据同步配置' },
          { id: 403, title: '账户安全设置' }
        ]
      },
      {
        id: 5,
        title: '故障排除',
        icon: '🔧',
        articles: [
          { id: 501, title: '数据同步失败怎么办' },
          { id: 502, title: '登录异常处理' },
          { id: 503, title: '设备连接问题' }
        ]
      },
      {
        id: 6,
        title: '其他问题',
        icon: '❓',
        articles: [
          { id: 601, title: '如何联系客服' },
          { id: 602, title: '如何反馈问题' },
          { id: 603, title: '常见错误代码' }
        ]
      }
    ],



    // 热门教程
    hotTutorials: [
      {
        id: 1,
        title: '如何使用AI智能诊断',
        description: '学习使用AI功能快速诊断鹅群健康状况',
        icon: '🤖',
        views: 1250,
        duration: '5分钟',
        url: '/pages/help/tutorial/ai-diagnosis'
      },
      {
        id: 2,
        title: '环境数据监控设置',
        description: '配置温湿度等环境参数的监控阈值',
        icon: '🌡️',
        views: 980,
        duration: '3分钟',
        url: '/pages/help/tutorial/environment'
      },
      {
        id: 3,
        title: '生产数据统计分析',
        description: '查看和分析养殖场的各项生产数据',
        icon: '📊',
        views: 756,
        duration: '8分钟',
        url: '/pages/help/tutorial/statistics'
      }
    ],


    
    // 搜索相关
    searchKeyword: '',
    showSuggestions: false,
    suggestions: [],
    isSearching: false,
    searchResults: {
      categories: 0,
      articles: 0,
      questions: 0,
      total: 0
    },
    
    // 常见问题
    faqList: [
      {
        id: 1,
        question: '如何查看鹅群的健康状况？',
        answer: '您可以在首页的健康概览中查看鹅群的整体健康状况，也可以进入健康管理模块查看详细记录。点击具体的鹅只可以查看个体健康详情。',
        helpful: 45,
        unhelpful: 2,
        expanded: false
      },
      {
        id: 2,
        question: '环境数据异常如何处理？',
        answer: '当环境数据超出正常范围时，系统会发出预警通知。您可以根据提示检查设备运行状态或调整环境设置。建议及时处理异常情况以保证鹅群健康。',
        helpful: 38,
        unhelpful: 1,
        expanded: false
      },
      {
        id: 3,
        question: '如何导出生产数据？',
        answer: '在各个功能模块中，您可以找到数据导出选项，将数据导出为Excel格式进行保存。支持按时间范围和数据类型筛选导出。',
        helpful: 32,
        unhelpful: 0,
        expanded: false
      },
      {
        id: 4,
        question: 'AI诊断准确率如何？',
        answer: '我们的AI诊断系统基于大量的养鹅数据训练，准确率达到95%以上。建议结合专业兽医意见进行最终诊断。',
        helpful: 28,
        unhelpful: 3,
        expanded: false
      },
      {
        id: 5,
        question: '数据同步失败怎么办？',
        answer: '请检查网络连接，确保设备正常联网。如果问题持续存在，可以尝试重启应用或联系客服支持。',
        helpful: 25,
        unhelpful: 1,
        expanded: false
      },
      {
        id: 6,
        question: '如何使用AI智能诊断？',
        answer: '进入健康管理模块，点击AI诊断按钮，拍摄或上传鹅只照片，系统会自动分析并给出诊断建议。支持多种疾病识别。',
        helpful: 42,
        unhelpful: 2,
        expanded: false
      },
      {
        id: 7,
        question: '环境数据监控设备如何安装？',
        answer: '请参考设备安装手册，将传感器安装在鹅舍内合适位置，确保数据采集准确。建议每个鹅舍至少安装3个监测点。',
        helpful: 35,
        unhelpful: 1,
        expanded: false
      },
      {
        id: 8,
        question: '生产数据统计包含哪些内容？',
        answer: '包括产蛋量、鹅只体重、饲料消耗、死亡率、疫苗接种记录等全方位生产指标，支持图表展示和趋势分析。',
        helpful: 38,
        unhelpful: 0,
        expanded: false
      }
    ],
    
    // 联系方式
    contactInfo: {
      phone: '************',
      email: '<EMAIL>',
      workTime: '周一至周五 9:00-18:00'
    }
  },

  onLoad: function (options) {
    // 页面加载
  },

  onShow: function () {
    // 页面显示
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch: function () {
    if (this.data.searchKeyword.trim() === '') {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '搜索中...'
    });

    // 执行帮助内容搜索
    this.searchHelpContent(this.data.searchKeyword.trim());
  },

  // 查看帮助文章
  onViewArticle: function (e) {
    const { id, title } = e.currentTarget.dataset;
    wx.showToast({
      title: `查看文章：${title}`,
      icon: 'none'
    });
  },

  // 展开/收起常见问题
  onToggleFAQ: function (e) {
    const { index } = e.currentTarget.dataset;
    const faqList = this.data.faqList;
    faqList[index].expanded = !faqList[index].expanded;
    
    this.setData({
      faqList: faqList
    });
  },

  // 拨打电话
  onCall: function () {
    wx.makePhoneCall({
      phoneNumber: this.data.contactInfo.phone
    });
  },

  // 复制邮箱
  onCopyEmail: function () {
    wx.setClipboardData({
      data: this.data.contactInfo.email,
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        });
      }
    });
  },

  // 意见反馈
  onFeedback: function () {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback'
    });
  },

  // 搜索帮助内容
  searchHelpContent: function(keyword) {
    // 跳转到专门的搜索页面
    wx.navigateTo({
      url: `/pages/help/search/search?keyword=${encodeURIComponent(keyword)}`
    });
  },

  // 清除搜索
  onClearSearch: function() {
    this.setData({
      searchKeyword: '',
      categories: this.data.allCategories || this.data.categories,
      articles: this.data.allArticles || this.data.articles,
      commonQuestions: this.data.allQuestions || this.data.commonQuestions,
      isSearching: false,
      searchResults: null
    });
  },

  // 智能搜索推荐
  getSearchSuggestions: function(keyword) {
    const suggestions = [];
    const allContent = [
      ...this.data.categories.map(c => c.title),
      ...this.data.articles.map(a => a.title),
      ...this.data.commonQuestions.map(q => q.question)
    ];

    // 模糊匹配建议
    allContent.forEach(content => {
      if (content.toLowerCase().includes(keyword.toLowerCase()) && 
          !suggestions.includes(content) && 
          suggestions.length < 5) {
        suggestions.push(content);
      }
    });

    return suggestions;
  },

  // 搜索历史记录
  saveSearchHistory: function(keyword) {
    let history = wx.getStorageSync('helpSearchHistory') || [];
    
    // 移除已存在的关键词
    history = history.filter(item => item !== keyword);
    
    // 添加到开头
    history.unshift(keyword);
    
    // 只保留最近10个
    history = history.slice(0, 10);
    
    wx.setStorageSync('helpSearchHistory', history);
  },

  // 获取搜索历史
  getSearchHistory: function() {
    return wx.getStorageSync('helpSearchHistory') || [];
  },

  // 显示搜索历史
  onShowSearchHistory: function() {
    const history = this.getSearchHistory();
    if (history.length === 0) {
      wx.showToast({
        title: '暂无搜索历史',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: history,
      success: (res) => {
        const selectedKeyword = history[res.tapIndex];
        this.setData({
          searchKeyword: selectedKeyword
        });
        this.searchHelpContent(selectedKeyword);
      }
    });
  },

  // 清除搜索历史
  onClearSearchHistory: function() {
    wx.removeStorageSync('helpSearchHistory');
    wx.showToast({
      title: '搜索历史已清除',
      icon: 'success'
    });
  },

  // 搜索框聚焦
  onSearchFocus: function() {
    if (this.data.searchKeyword.trim()) {
      const suggestions = this.getSearchSuggestions(this.data.searchKeyword);
      this.setData({
        showSuggestions: true,
        suggestions: suggestions
      });
    }
  },

  // 选择搜索建议
  onSelectSuggestion: function(e) {
    const { keyword } = e.currentTarget.dataset;
    this.setData({
      searchKeyword: keyword,
      showSuggestions: false
    });
    this.searchHelpContent(keyword);
  },



  // 查看分类
  onViewCategory: function(e) {
    const { category } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/help/category/category?id=${category.id}&title=${category.title}`,
      fail: () => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    });
  },

  // 查看教程
  onViewTutorial: function(e) {
    const { tutorial } = e.currentTarget.dataset;
    wx.navigateTo({
      url: tutorial.url,
      fail: () => {
        wx.showModal({
          title: tutorial.title,
          content: tutorial.description,
          showCancel: false
        });
      }
    });
  },

  // 查看更多FAQ
  onViewMoreFAQ: function() {
    wx.navigateTo({
      url: '/pages/help/faq/faq',
      fail: () => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    });
  },

  // 标记有帮助
  onMarkHelpful: function(e) {
    const { id } = e.currentTarget.dataset;
    const faqList = this.data.faqList;
    const faq = faqList.find(item => item.id === id);
    
    if (faq) {
      faq.helpful = (faq.helpful || 0) + 1;
      this.setData({ faqList });
      
      wx.showToast({
        title: '感谢您的反馈',
        icon: 'success'
      });
    }
  },

  // 标记没帮助
  onMarkUnhelpful: function(e) {
    const { id } = e.currentTarget.dataset;
    const faqList = this.data.faqList;
    const faq = faqList.find(item => item.id === id);
    
    if (faq) {
      faq.unhelpful = (faq.unhelpful || 0) + 1;
      this.setData({ faqList });
      
      wx.showModal({
        title: '反馈收到',
        content: '我们会持续改进答案质量，您也可以联系客服获得更详细的帮助。',
        confirmText: '联系客服',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            // 跳转到意见反馈页面
            wx.navigateTo({
              url: '/pages/profile/feedback/feedback'
            });
          }
        }
      });
    }
  },





  // 更新搜索建议算法
  getSearchSuggestions: function(keyword) {
    const suggestions = [];
    const allContent = [
      ...this.data.helpCategories.map(c => c.title),
      ...this.data.faqList.map(q => q.question),
      ...this.data.hotTutorials.map(t => t.title),
      '健康诊断', 'AI诊断', '环境监控', '数据导出', '登录问题', 
      '密码重置', '设备连接', '数据同步', '通知设置', '账户安全'
    ];

    // 模糊匹配建议
    allContent.forEach(content => {
      if (content.toLowerCase().includes(keyword.toLowerCase()) && 
          !suggestions.includes(content) && 
          suggestions.length < 5) {
        suggestions.push(content);
      }
    });

    return suggestions;
  }
});