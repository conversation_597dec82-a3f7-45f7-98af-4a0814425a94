/**
 * 租户模型 - 使用真实数据库连接
 * Tenant Model - Using Real Database Connection
 */

const { initSaasModels, getModels } = require("./saas-platform.model");

// 初始化模型（如果还没有初始化）
let modelsInitialized = false;

async function ensureModelsInitialized() {
  if (!modelsInitialized) {
    await initSaasModels();
    modelsInitialized = true;
  }
  return getModels();
}

class Tenant {
  static async count(options = {}) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.count(options);
  }

  static async sum(field, options = {}) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.sum(field, options);
  }

  static async findAll(options = {}) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.findAll(options);
  }

  static async findAndCountAll(options = {}) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.findAndCountAll(options);
  }

  static async findByPk(id, options = {}) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.findByPk(id, options);
  }

  static async create(data) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.create(data);
  }

  static async update(values, options) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.update(values, options);
  }

  static async destroy(options) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.destroy(options);
  }

  static async findOne(options) {
    const models = await ensureModelsInitialized();
    return await models.Tenant.findOne(options);
  }

  // 添加实例方法支持
  async update(values) {
    const models = await ensureModelsInitialized();
    if (this.id) {
      return await models.Tenant.update(values, { where: { id: this.id } });
    }
    throw new Error("Cannot update tenant without ID");
  }

  async destroy() {
    const models = await ensureModelsInitialized();
    if (this.id) {
      return await models.Tenant.destroy({ where: { id: this.id } });
    }
    throw new Error("Cannot destroy tenant without ID");
  }
}

module.exports = Tenant;
