// backend/saas-admin/controllers/cross-tenant.controller.js
const { Op } = require("sequelize");
const tenantDatabaseManager = require("../../models/tenant-database.model");

/**
 * 跨租户数据控制器
 * 专门处理SAAS平台管理者查看所有租户业务数据的功能
 */
class CrossTenantController {
  /**
   * 显示跨租户数据监控页面
   */
  async showCrossTenantDataPage(req, res) {
    try {
      res.render("tenants/cross-tenant-data", {
        title: "跨租户数据监控",
        user: req.user,
        currentPage: "cross-tenant-data",
      });
    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("显示跨租户数据页面失败:", error); } catch(_) {}

      res.status(500).render("error", {
        message: "页面加载失败",
        error: process.env.NODE_ENV === "development" ? error : {},
      });
    }
  }

  /**
   * 获取跨租户概览数据
   */
  async getCrossTenantOverview(req, res) {
    try {
      const saasDb = tenantDatabaseManager.getSaasConnection();

      // 获取所有活跃租户
      const Tenant = saasDb.models.Tenant;
      const activeTenants = await Tenant.findAll({
        where: {
          status: ["active", "trial"],
        },
        attributes: [
          "id",
          "tenantCode",
          "companyName",
          "status",
          "subscriptionPlan",
        ],
      });

      let totalGeese = 0;
      let totalProduction = 0;
      let totalRevenue = 0;

      // 遍历每个租户数据库，获取业务数据
      for (const tenant of activeTenants) {
        try {
          const tenantDb = tenantDatabaseManager.getTenantConnection(
            tenant.tenantCode.toLowerCase(),
          );
          if (!tenantDb) continue;

          // 获取鹅群总数
          const Flock = tenantDb.models.Flock;
          if (Flock) {
            const flockCount = await Flock.count({
              where: { status: "active" },
            });
            totalGeese += flockCount;
          }

          // 获取今日产蛋量
          const ProductionRecord = tenantDb.models.ProductionRecord;
          if (ProductionRecord) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const todayProduction = await ProductionRecord.sum("eggCount", {
              where: {
                recordDate: {
                  [Op.gte]: today,
                  [Op.lt]: tomorrow,
                },
              },
            });
            totalProduction += todayProduction || 0;
          }

          // 获取本月收入
          const FinancialRecord = tenantDb.models.FinancialRecord;
          if (FinancialRecord) {
            const currentMonth = new Date();
            currentMonth.setDate(1);
            currentMonth.setHours(0, 0, 0, 0);

            const monthlyRevenue = await FinancialRecord.sum("amount", {
              where: {
                type: "income",
                recordDate: {
                  [Op.gte]: currentMonth,
                },
              },
            });
            totalRevenue += monthlyRevenue || 0;
          }
        } catch (tenantError) {
          try { const { Logger } = require('../../middleware/errorHandler'); Logger.error(`获取租户 ${tenant.tenantCode} 数据失败:`, tenantError); } catch(_) {}

          // 继续处理其他租户
        }
      }

      const overviewData = {
        totalTenants: activeTenants.length,
        totalGeese,
        totalProduction,
        totalRevenue,
      };

      res.json({
        success: true,
        data: overviewData,
      });
    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取跨租户概览数据失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "获取概览数据失败",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  /**
   * 获取所有租户详细数据列表
   */
  async getTenantsData(req, res) {
    try {
      const { status, subscriptionPlan, timeRange = 30 } = req.query;
      const saasDb = tenantDatabaseManager.getSaasConnection();

      // 构建查询条件
      const whereConditions = {};
      if (status) {
        whereConditions.status = status;
      }
      if (subscriptionPlan) {
        whereConditions.subscriptionPlan = subscriptionPlan;
      }

      // 获取租户列表
      const Tenant = saasDb.models.Tenant;
      const tenants = await Tenant.findAll({
        where: whereConditions,
        attributes: [
          "id",
          "tenantCode",
          "companyName",
          "status",
          "subscriptionPlan",
          "createdAt",
          "updatedAt",
        ],
        order: [["updatedAt", "DESC"]],
      });

      const tenantsData = [];

      // 获取每个租户的业务数据
      for (const tenant of tenants) {
        try {
          const tenantDb = tenantDatabaseManager.getTenantConnection(
            tenant.tenantCode.toLowerCase(),
          );

          let tenantData = {
            tenantCode: tenant.tenantCode,
            companyName: tenant.companyName,
            status: tenant.status,
            subscriptionPlan: tenant.subscriptionPlan,
            flockCount: 0,
            dailyProduction: 0,
            monthlyRevenue: 0,
            healthScore: 0,
            lastActiveTime: tenant.updatedAt,
          };

          if (tenantDb) {
            // 获取鹅群数量
            const Flock = tenantDb.models.Flock;
            if (Flock) {
              tenantData.flockCount = await Flock.count({
                where: { status: "active" },
              });
            }

            // 获取昨日产蛋量
            const ProductionRecord = tenantDb.models.ProductionRecord;
            if (ProductionRecord) {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              yesterday.setHours(0, 0, 0, 0);
              const today = new Date(yesterday);
              today.setDate(today.getDate() + 1);

              tenantData.dailyProduction =
                (await ProductionRecord.sum("eggCount", {
                  where: {
                    recordDate: {
                      [Op.gte]: yesterday,
                      [Op.lt]: today,
                    },
                  },
                })) || 0;
            }

            // 获取本月收入
            const FinancialRecord = tenantDb.models.FinancialRecord;
            if (FinancialRecord) {
              const currentMonth = new Date();
              currentMonth.setDate(1);
              currentMonth.setHours(0, 0, 0, 0);

              tenantData.monthlyRevenue =
                (await FinancialRecord.sum("amount", {
                  where: {
                    type: "income",
                    recordDate: {
                      [Op.gte]: currentMonth,
                    },
                  },
                })) || 0;
            }

            // 计算健康分数（简化算法）
            const HealthRecord = tenantDb.models.HealthRecord;
            if (HealthRecord) {
              const recentHealthRecords = await HealthRecord.findAll({
                where: {
                  checkDate: {
                    [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
                  },
                },
                attributes: ["healthStatus"],
              });

              if (recentHealthRecords.length > 0) {
                const healthyCount = recentHealthRecords.filter(
                  (record) => record.healthStatus === "healthy",
                ).length;
                tenantData.healthScore = Math.round(
                  (healthyCount / recentHealthRecords.length) * 100,
                );
              } else {
                tenantData.healthScore = 85; // 默认分数
              }
            } else {
              tenantData.healthScore = 85;
            }
          }

          tenantsData.push(tenantData);
        } catch (tenantError) {
          try { const { Logger } = require('../../middleware/errorHandler'); Logger.error(`处理租户 ${tenant.tenantCode} 数据失败:`, tenantError); } catch(_) {}

          // 添加基础数据，即使业务数据获取失败
          tenantsData.push({
            tenantCode: tenant.tenantCode,
            companyName: tenant.companyName,
            status: tenant.status,
            subscriptionPlan: tenant.subscriptionPlan,
            flockCount: 0,
            dailyProduction: 0,
            monthlyRevenue: 0,
            healthScore: 0,
            lastActiveTime: tenant.updatedAt,
          });
        }
      }

      res.json({
        success: true,
        data: tenantsData,
      });
    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取租户数据列表失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "获取租户数据失败",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  /**
   * 获取特定租户的生产数据
   */
  async getTenantProductionData(req, res) {
    try {
      const { tenantCode } = req.params;
      const { timeRange = 30 } = req.query;

      const tenantDb = tenantDatabaseManager.getTenantConnection(
        tenantCode.toLowerCase(),
      );
      if (!tenantDb) {
        return res.status(404).json({
          success: false,
          message: "租户不存在或数据库连接失败",
        });
      }

      const ProductionRecord = tenantDb.models.ProductionRecord;
      if (!ProductionRecord) {
        return res.json({
          success: true,
          data: { records: [], summary: {} },
        });
      }

      // 获取指定时间范围的生产记录
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(timeRange));
      startDate.setHours(0, 0, 0, 0);

      const productionRecords = await ProductionRecord.findAll({
        where: {
          recordDate: {
            [Op.gte]: startDate,
          },
        },
        attributes: ["recordDate", "eggCount", "flockId"],
        order: [["recordDate", "ASC"]],
      });

      // 按日期聚合数据
      const dailyProduction = {};
      productionRecords.forEach((record) => {
        const date = record.recordDate.toISOString().split("T")[0];
        if (!dailyProduction[date]) {
          dailyProduction[date] = 0;
        }
        dailyProduction[date] += record.eggCount || 0;
      });

      // 计算汇总信息
      const totalEggs = productionRecords.reduce(
        (sum, record) => sum + (record.eggCount || 0),
        0,
      );
      const avgDaily =
        totalEggs / Math.max(Object.keys(dailyProduction).length, 1);

      const responseData = {
        records: Object.entries(dailyProduction).map(([date, count]) => ({
          date,
          eggCount: count,
        })),
        summary: {
          totalEggs,
          avgDaily: Math.round(avgDaily),
          timeRange: parseInt(timeRange),
        },
      };

      res.json({
        success: true,
        data: responseData,
      });
    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取租户生产数据失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "获取生产数据失败",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  /**
   * 获取租户对比数据
   */
  async getTenantComparison(req, res) {
    try {
      const { tenantCodes, metric = "production" } = req.query;
      const codes = Array.isArray(tenantCodes)
        ? tenantCodes
        : [tenantCodes].filter(Boolean);

      if (codes.length === 0) {
        return res.status(400).json({
          success: false,
          message: "请选择要对比的租户",
        });
      }

      const comparisonData = [];

      for (const tenantCode of codes) {
        try {
          const tenantDb = tenantDatabaseManager.getTenantConnection(
            tenantCode.toLowerCase(),
          );
          if (!tenantDb) continue;

          let data = { tenantCode, name: tenantCode };

          switch (metric) {
            case "production":
              // 获取生产数据
              const ProductionRecord = tenantDb.models.ProductionRecord;
              if (ProductionRecord) {
                const last30Days = new Date();
                last30Days.setDate(last30Days.getDate() - 30);

                const totalProduction = await ProductionRecord.sum("eggCount", {
                  where: {
                    recordDate: {
                      [Op.gte]: last30Days,
                    },
                  },
                });
                data.value = totalProduction || 0;
              }
              break;

            case "revenue":
              // 获取收入数据
              const FinancialRecord = tenantDb.models.FinancialRecord;
              if (FinancialRecord) {
                const currentMonth = new Date();
                currentMonth.setDate(1);
                currentMonth.setHours(0, 0, 0, 0);

                const revenue = await FinancialRecord.sum("amount", {
                  where: {
                    type: "income",
                    recordDate: {
                      [Op.gte]: currentMonth,
                    },
                  },
                });
                data.value = revenue || 0;
              }
              break;

            case "health":
              // 获取健康数据
              const HealthRecord = tenantDb.models.HealthRecord;
              if (HealthRecord) {
                const recentRecords = await HealthRecord.findAll({
                  where: {
                    checkDate: {
                      [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                    },
                  },
                });

                if (recentRecords.length > 0) {
                  const healthyCount = recentRecords.filter(
                    (r) => r.healthStatus === "healthy",
                  ).length;
                  data.value = Math.round(
                    (healthyCount / recentRecords.length) * 100,
                  );
                } else {
                  data.value = 85;
                }
              }
              break;
          }

          comparisonData.push(data);
        } catch (tenantError) {
          try { const { Logger } = require('../../middleware/errorHandler'); Logger.error(`处理租户 ${tenantCode} 对比数据失败:`, tenantError); } catch(_) {}

        }
      }

      res.json({
        success: true,
        data: comparisonData,
      });
    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取租户对比数据失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "获取对比数据失败",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  /**
   * 导出跨租户数据报表
   */
  async exportCrossTenantData(req, res) {
    try {
      // 这里可以实现Excel导出功能
      // 暂时返回CSV格式的示例
      const tenantsData = await this.getTenantsDataForExport();

      let csvContent =
        "租户代码,公司名称,状态,订阅计划,鹅群数量,日产蛋量,月收入,健康分数\n";

      tenantsData.forEach((tenant) => {
        csvContent += `${tenant.tenantCode},${tenant.companyName},${tenant.status},${tenant.subscriptionPlan},${tenant.flockCount},${tenant.dailyProduction},${tenant.monthlyRevenue},${tenant.healthScore}\n`;
      });

      res.setHeader("Content-Type", "text/csv; charset=utf-8");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=cross-tenant-data-${new Date().toISOString().split("T")[0]}.csv`,
      );
      res.send("\uFEFF" + csvContent); // 添加BOM支持中文
    } catch (error) {
      try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("导出跨租户数据失败:", error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: "导出失败",
      });
    }
  }

  /**
   * 获取用于导出的租户数据
   */
  async getTenantsDataForExport() {
    // 重用 getTenantsData 的逻辑，但返回原始数据
    // 这里简化实现
    return [];
  }
}

module.exports = new CrossTenantController();
