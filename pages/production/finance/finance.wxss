/* pages/production/finance/finance.wxss */

/* 页面容器 */
.finance-container {
  padding: 20rpx 0 40rpx 0;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 功能标签页 */
.function-tabs {
  display: flex;
  background: white;
  border-radius: 20rpx;
  padding: 8rpx;
  margin: 20rpx;
  margin-bottom: 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  font-weight: bold;
}

/* 通用按钮样式 */
.add-btn {
  background-color: #0066CC;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140rpx;
}

/* 财务导航操作 */
.finance-nav-actions {
  display: flex;
  gap: 20rpx;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f0f8ff;
  border-radius: 8rpx;
  border: 2rpx solid #0066CC;
  transition: all 0.3s ease;
}

.nav-item:active {
  background: #0066CC;
}

.nav-item:active .nav-text {
  color: white;
}

.nav-icon {
  font-size: 28rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #0066CC;
  font-weight: 500;
}

.add-btn::after {
  border: none;
}

/* 报销统计 */
.reimbursement-stats {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #0066CC;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 报销列表 */
.reimbursement-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.reimbursement-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #0066CC;
}

.item-info {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 24rpx;
  color: #666;
}

.item-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #0066CC;
  margin: 0 20rpx;
}

.item-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.item-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.item-status.approved {
  background: #f6ffed;
  color: #52c41a;
}

.item-status.rejected {
  background: #fff2f0;
  color: #ff4d4f;
}

.finance-container {
  min-height: 100vh;
  background-color: #f5f6f8;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 时间范围选择 */
.time-range-selector {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  gap: 20rpx;
}

.range-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 26rpx;
  color: #666666;
  border-radius: 10rpx;
}

.range-item.active {
  background-color: #0066cc;
  color: #ffffff;
  font-weight: bold;
}

/* 区块 */
.section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.overview-section {
  border-left: 10rpx solid #0066cc;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

/* AI分析按钮 */
.ai-analysis-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.ai-analysis-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.ai-icon {
  margin-right: 6rpx;
  font-size: 28rpx;
}

.ai-text {
  font-weight: bold;
}

/* AI分析结果 */
.ai-analysis-section {
  background: linear-gradient(135deg, #f5f7ff 0%, #e8f2ff 100%);
  border: 2rpx solid #e0e8ff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.ai-analysis-content {
  padding: 20rpx;
}

.analysis-item {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border-left: 6rpx solid #667eea;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.analysis-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  white-space: pre-wrap;
}

.ai-analysis-footer {
  text-align: center;
  padding-top: 16rpx;
  border-top: 2rpx solid #e0e8ff;
  margin-top: 20rpx;
}

.ai-disclaimer {
  font-size: 22rpx;
  color: #999999;
  font-style: italic;
}

.close-analysis {
  font-size: 36rpx;
  color: #999999;
  cursor: pointer;
  line-height: 1;
  padding: 8rpx;
}

.close-analysis:active {
  color: #666666;
}

.add-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #0066cc;
  color: #ffffff;
  font-size: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 财务概览 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.overview-item {
  padding: 20rpx;
  border-radius: 10rpx;
  text-align: center;
}

.overview-item.income {
  background-color: #e6f9f8;
}

.overview-item.expense {
  background-color: #ffe6e6;
}

.overview-item.profit {
  background-color: #e6f4ff;
}

.overview-item.margin {
  background-color: #fff3e6;
}

.item-label {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.item-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

/* 图表容器 */
.chart-container {
  padding: 20rpx 0;
}

.chart-placeholder {
  text-align: center;
  padding: 50rpx 0;
}

.chart-placeholder image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.chart-placeholder text {
  font-size: 28rpx;
  color: #999999;
}



/* 财务记录 */
.record-filter {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 6rpx;
  margin-bottom: 20rpx;
}

.record-filter .filter-item {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.record-filter .filter-item.active {
  background-color: #0066cc;
  color: #ffffff;
  font-weight: bold;
}

.records-list {
  padding: 10rpx 0;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f5f5f5;
}

.record-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.record-type {
  font-size: 24rpx;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  white-space: nowrap;
}

.record-type.income {
  background-color: #e6f9f8;
  color: #00cc99;
}

.record-type.expense {
  background-color: #ffe6e6;
  color: #ff3333;
}

.record-content {
  display: flex;
  flex-direction: column;
}

.record-category {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 5rpx;
}

.record-remark {
  font-size: 24rpx;
  color: #999999;
}

.record-amount {
  font-size: 28rpx;
  font-weight: bold;
  margin: 0 20rpx;
  white-space: nowrap;
}

.record-amount.income {
  color: #00cc99;
}

.record-amount.expense {
  color: #ff3333;
}

.record-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  position: relative;
}

.record-arrow {
  color: #ccc;
  font-size: 24rpx;
  margin-top: 5rpx;
}

.record-date {
  font-size: 24rpx;
  color: #999999;
  white-space: nowrap;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
}

/* 加载更多 */
.load-more,
.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 财务概览点击效果 */
.overview-item {
  cursor: pointer;
  transition: all 0.3s ease;
}

.overview-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 图表样式 */
.close-chart {
  font-size: 40rpx;
  color: #999;
  font-weight: bold;
  cursor: pointer;
  padding: 10rpx;
}

.bar-chart {
  padding: 40rpx 20rpx;
}

.chart-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 400rpx;
  padding: 20rpx 0;
}

.chart-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 8rpx;
}

.bar-container {
  height: 320rpx;
  width: 50rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  display: flex;
  align-items: flex-end;
  margin-bottom: 20rpx;
  position: relative;
}

.bar-fill {
  width: 100%;
  border-radius: 8rpx;
  transition: height 0.6s ease;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10rpx;
}

.bar-value {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  transform: rotate(-90deg);
  transform-origin: center;
}

.bar-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}



/* 底部添加按钮 */
.bottom-actions {
  padding: 40rpx 20rpx;
  text-align: center;
}

.add-record-btn {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: normal;
  display: inline-block;
  min-width: 200rpx;
}

.add-record-btn::after {
  border: none;
}

/* 弹窗模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 50rpx;
  color: #999;
  font-weight: bold;
  cursor: pointer;
  padding: 10rpx;
}

.modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.radio-group {
  display: flex;
  gap: 20rpx;
}

.radio-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.radio-item.active {
  border-color: #0066CC;
  background: #0066CC;
  color: white;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
}

.picker-input .placeholder {
  color: #999;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx;
  border-top: 1rpx solid #eee;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  padding: 24rpx 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  border: none;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-confirm {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
}

.btn-confirm[disabled] {
  background: #ccc;
  color: #999;
}

.btn-cancel::after,
.btn-confirm::after {
  border: none;
}

/* 详情弹窗样式 */
.detail-modal {
  max-width: 700rpx;
}

.detail-section {
  padding: 20rpx 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  min-width: 120rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.detail-value.income {
  color: #00cc99;
}

.detail-value.expense {
  color: #ff3333;
}

.detail-value.amount {
  font-weight: bold;
  font-size: 32rpx;
}

.related-record {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.related-record:active {
  background: #e9ecef;
}

.related-type {
  background: #0066CC;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  margin-right: 16rpx;
}

.related-desc {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.related-arrow {
  color: #999;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.btn-secondary,
.btn-primary {
  flex: 1;
  padding: 24rpx 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  border: none;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  margin-right: 10rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  margin-left: 10rpx;
}

.btn-secondary::after,
.btn-primary::after {
  border: none;
}