/* pages/health/material-detail/material-detail.wxss */

.material-detail-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999999;
}

.material-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 表单项样式 */
.form-item {
  padding: 30rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item .label {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

/* 物料类型选择样式 */
.category-options {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.category-option {
  flex: 1;
  min-width: 0;
  padding: 20rpx 12rpx;
  text-align: center;
  font-size: 25rpx;
  color: #666666;
  background-color: #f8f8f8;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.category-option.selected {
  color: #007aff;
  background-color: #e6f3ff;
  border-color: #007aff;
  font-weight: 600;
}

/* 类型特定字段样式 */
.category-specific-fields {
  margin-top: 8rpx;
}

.feed-fields,
.medicine-fields,
.other-fields {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-top: 16rpx;
}

/* 字段样式简化 */
.feed-fields .field-item:first-child,
.medicine-fields .field-item:first-child,
.other-fields .field-item:first-child {
  /* 移除突兀的边框和背景色 */
}

.field-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.field-label {
  font-size: 30rpx;
  font-weight: 500;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  padding: 30rpx 22rpx;
  font-size: 32rpx;
  color: #333333;
  background-color: #fafafa;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  min-height: 90rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

.form-input:focus {
  background-color: #ffffff;
  border-color: #007aff;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.form-input::placeholder {
  color: #999999;
  font-size: 12rpx;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  padding: 30rpx 22rpx;
  font-size: 32rpx;
  color: #333333;
  background-color: #fafafa;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  min-height: 140rpx;
  line-height: 1.5;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  background-color: #ffffff;
  border-color: #007aff;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.form-textarea::placeholder {
  color: #999999;
  font-size: 12rpx;
}

/* 选择器样式 */
.picker {
  width: 100%;
  padding: 30rpx 22rpx;
  font-size: 32rpx;
  color: #333333;
  background-color: #fafafa;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  position: relative;
  transition: all 0.2s ease;
  min-height: 90rpx;
  line-height: 1.5;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.picker:active {
  background-color: #ffffff;
  border-color: #007aff;
}

.picker::after {
  content: '';
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border: 8rpx solid transparent;
  border-top-color: #999999;
}

.picker-display {
  width: 100%;
  padding: 30rpx 22rpx;
  font-size: 32rpx;
  color: #333333;
  background-color: #fafafa;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  position: relative;
  transition: all 0.2s ease;
  min-height: 90rpx;
  line-height: 1.5;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.picker-display:active {
  background-color: #ffffff;
  border-color: #007aff;
}

.picker-display::after {
  content: '';
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border: 8rpx solid transparent;
  border-top-color: #999999;
}

.placeholder {
  color: #999999;
  font-size: 12rpx;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 24rpx 24rpx 24rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 16rpx 0;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 6rpx;
  transition: all 0.2s ease;
  border: none;
}

.cancel-btn {
  color: #666666;
  background-color: #f5f5f5;
}

.cancel-btn:active {
  background-color: #e8e8e8;
  transform: scale(0.98);
}

.save-btn {
  color: #ffffff;
  background: linear-gradient(135deg, #007aff 0%, #0056d6 100%);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.save-btn:active {
  background: linear-gradient(135deg, #0056d6 0%, #004bb8 100%);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);
}

.save-btn[loading] {
  background: linear-gradient(135deg, #cccccc 0%, #999999 100%);
  box-shadow: none;
}

/* 响应式调整 */
@media screen and (max-width: 400px) {
  .category-options {
    flex-direction: column;
  }

  .category-option {
    flex: none;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    flex: none;
  }
}