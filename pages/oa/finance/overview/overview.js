// pages/oa/finance/overview/overview.js

/**
 * 财务概览页面
 * 功能：展示关键财务指标、趋势图表、财务明细等
 */

const utils = require('../../../../utils/format');
const request = require('../../../../utils/request.js');
const { API } = require('../../../../constants/index.js');
const { PERMISSIONS, PermissionMixin } = require('../../../../utils/oa-permissions');

Page({
  // 混入权限检查功能
  ...PermissionMixin,
  data: {
    loading: false,
    
    // 时间范围选择
    timeRange: 'month', // week, month, quarter, year
    timeRangeOptions: [
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本季', value: 'quarter' },
      { label: '本年', value: 'year' }
    ],
    
    // 财务概览数据
    overview: {
      totalRevenue: 125000.00,     // 总收入
      totalExpense: 89000.00,      // 总支出  
      netProfit: 36000.00,         // 净利润
      pendingPayments: 15800.00,   // 待付款
      revenueGrowth: 12.5,         // 收入增长率%
      expenseGrowth: -8.2,         // 支出增长率%
      profitGrowth: 15.8,          // 利润增长率%
      cashFlow: 42500.00           // 现金流
    },
    
    // 收支趋势数据（最近12个月）
    trendData: {
      months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      revenue: [8000, 9200, 10500, 12000, 11800, 13200, 14500, 13800, 15200, 16000, 14800, 16500],
      expense: [6500, 7200, 8000, 8800, 9200, 9800, 10200, 9500, 10800, 11200, 10500, 11800]
    },
    
    // 支出分类数据
    expenseCategories: [
      { name: '饲料采购', amount: 35000, percentage: 40, color: '#FF6B35' },
      { name: '人工成本', amount: 25000, percentage: 28, color: '#0066CC' },
      { name: '设备维护', amount: 15000, percentage: 17, color: '#00A86B' },
      { name: '其他费用', amount: 14000, percentage: 15, color: '#9B59B6' }
    ],
    
    // 最近交易记录
    recentTransactions: [
      {
        id: 'txn_001',
        type: 'income',
        description: '销售收入 - 鹅肉批发',
        amount: 8500.00,
        date: '2024-01-15',
        category: '销售收入',
        status: 'completed'
      },
      {
        id: 'txn_002', 
        type: 'expense',
        description: '饲料采购 - 玉米饲料',
        amount: 3200.00,
        date: '2024-01-14',
        category: '采购支出',
        status: 'completed'
      },
      {
        id: 'txn_003',
        type: 'expense',
        description: '设备维护 - 孵化器维修',
        amount: 1800.00,
        date: '2024-01-13',
        category: '维护费用',
        status: 'pending'
      }
    ],
    
    // 财务提醒
    alerts: [
      {
        id: 'alert_001',
        type: 'warning',
        title: '待付款项提醒',
        message: '有3笔待付款项，总金额￥15,800',
        action: 'view_payments'
      },
      {
        id: 'alert_002',
        type: 'info',
        title: '月度报表',
        message: '1月份财务报表已生成，请及时查看',
        action: 'view_report'
      }
    ]
  },

  onLoad: function (options) {
    this.loadOverviewData();
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查财务查看权限
    if (!this.hasPermission(PERMISSIONS.OA.FINANCE_VIEW)) {
      wx.showModal({
        title: '权限不足',
        content: '您没有查看财务数据的权限，请联系管理员',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限验证通过，继续加载数据
    this.loadOverviewData();
  },

  onShow: function () {
    this.refreshData();
  },

  onPullDownRefresh: function () {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },



  // 加载概览数据
  loadOverviewData: function() {
    this.setData({ loading: true });
    
    Promise.all([
      this.fetchFinancialOverview(),
      this.fetchTrendData(),
      this.fetchRecentTransactions()
    ]).then(() => {
      this.setData({ loading: false });
    }).catch(error => {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载财务概览数据失败', error); } catch(_) {}
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  // 刷新数据
  refreshData: function() {
    this.loadOverviewData();
  },

  // 获取财务概览数据
  fetchFinancialOverview: function() {
    return request.get(`${API.ENDPOINTS.OA.FINANCE.OVERVIEW}`, { timeRange: this.data.timeRange })
      .then(res => {
        const data = res && res.success !== false ? (res.data || res) : null;
        if (!data) throw new Error(res?.message || '获取概览数据失败');
        this.setData({ overview: data });
        return data;
      });
  },

  // 获取趋势数据
  fetchTrendData: function() {
    return request.get(`${API.ENDPOINTS.OA.FINANCE.REPORTS}`, { timeRange: this.data.timeRange, type: 'trend' })
      .then(res => {
        const data = res && res.success !== false ? (res.data || res) : null;
        if (!data) throw new Error(res?.message || '获取趋势数据失败');
        this.setData({
          trendData: data.trend || data,
          expenseCategories: data.categories || this.data.expenseCategories
        });
        return data.trend || data;
      });
  },

  // 获取最近交易
  fetchRecentTransactions: function() {
    return request.get(`${API.ENDPOINTS.OA.FINANCE.REPORTS}`, { type: 'transactions', limit: 10 })
      .then(res => {
        const data = res && res.success !== false ? (res.data || res) : null;
        if (!data) throw new Error(res?.message || '获取交易记录失败');
        this.setData({ recentTransactions: data.list || data });
        return data.list || data;
      });
  },

  // 时间范围选择
  onTimeRangeChange: function(e) {
    const value = e.detail.value;
    const selectedRange = this.data.timeRangeOptions[value];
    
    this.setData({
      timeRange: selectedRange.value
    });
    
    this.loadOverviewData();
  },

  // 点击概览卡片
  onOverviewCardTap: function(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';
    
    switch(type) {
      case 'revenue':
        url = '/pages/oa/finance/reports/reports?type=revenue';
        break;
      case 'expense':
        url = '/pages/oa/finance/reports/reports?type=expense';
        break;
      case 'profit':
        url = '/pages/oa/finance/reports/reports?type=profit';
        break;
      case 'pending':
        url = '/pages/oa/finance/payments/pending';
        break;
    }
    
    if (url) {
      wx.navigateTo({ url });
    }
  },

  // 点击支出分类
  onExpenseCategoryTap: function(e) {
    const category = e.currentTarget.dataset.category;
    wx.navigateTo({
      url: `/pages/oa/finance/reports/reports?type=expense&category=${category.name}`
    });
  },

  // 点击交易记录
  onTransactionTap: function(e) {
    const transaction = e.currentTarget.dataset.transaction;
    wx.navigateTo({
      url: `/pages/oa/finance/transaction/detail?id=${transaction.id}`
    });
  },

  // 查看全部交易
  onViewAllTransactions: function() {
    wx.navigateTo({
      url: '/pages/oa/finance/transactions/list'
    });
  },

  // 处理提醒点击
  onAlertTap: function(e) {
    const alert = e.currentTarget.dataset.alert;
    
    switch(alert.action) {
      case 'view_payments':
        wx.navigateTo({
          url: '/pages/oa/finance/payments/pending'
        });
        break;
      case 'view_report':
        wx.navigateTo({
          url: '/pages/oa/finance/reports/reports'
        });
        break;
    }
  },

  // 导出报表
  onExportReport: function() {
    wx.showActionSheet({
      itemList: ['导出Excel', '导出PDF', '发送邮件'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.exportExcel();
            break;
          case 1:
            this.exportPDF();
            break;
          case 2:
            this.sendEmail();
            break;
        }
      }
    });
  },

  // 导出Excel
  exportExcel: function() {
    wx.showLoading({ title: '导出中...' });
    
    request.post(`${API.ENDPOINTS.OA.FINANCE.EXPORT}`, {
      timeRange: this.data.timeRange,
      type: 'overview',
      format: 'excel'
    }).then(res => {
      wx.hideLoading();
      if (!res || res.success === false) {
        wx.showToast({ title: (res && res.message) || '导出失败', icon: 'none' });
        return;
      }
      wx.showToast({ title: '导出成功', icon: 'success' });
    }).catch(() => {
      wx.hideLoading();
      wx.showToast({ title: '导出失败', icon: 'none' });
    });
  },

  // 导出PDF
  exportPDF: function() {
    wx.showLoading({
      title: '生成PDF中...'
    });

    // 准备PDF数据
    const pdfData = this.preparePDFData();
    
    setTimeout(() => {
      wx.hideLoading();
      
      wx.showModal({
        title: 'PDF生成完成',
        content: `财务概览报告已生成\n\n报告内容：\n• 收支统计\n• 趋势分析\n• 分类明细\n• 图表展示\n\n文件大小：约1.8MB\n生成时间：${new Date().toLocaleString()}`,
        showCancel: true,
        cancelText: '关闭',
        confirmText: '查看详情',
        success: (res) => {
          if (res.confirm) {
            this.showPDFPreview(pdfData);
          }
        }
      });
    }, 2000);
  },

  // 准备PDF数据
  preparePDFData: function() {
    const overview = this.data.overview;
    const statistics = this.data.statistics;
    
    return {
      reportInfo: {
        title: '智慧养鹅财务概览报告',
        generateTime: new Date().toISOString(),
        period: '当前月度',
        department: '财务部门',
        operator: wx.getStorageSync('userInfo')?.name || '系统用户'
      },
      summary: {
        totalIncome: overview.totalIncome,
        totalExpense: overview.totalExpense,
        netProfit: overview.totalIncome - overview.totalExpense,
        profitRate: overview.totalIncome > 0 ? 
          ((overview.totalIncome - overview.totalExpense) / overview.totalIncome * 100) : 0,
        reimbursementTotal: overview.reimbursementTotal || 0
      },
      categoryBreakdown: statistics.categoryData || {},
      monthlyTrend: statistics.monthlyData || {},
      topExpenses: this.getTopExpenses(),
      riskAnalysis: this.generateRiskAnalysis()
    };
  },

  // 获取最大支出项
  getTopExpenses: function() {
    const categoryData = this.data.statistics.categoryData || {};
    const expenses = Object.keys(categoryData)
      .filter(key => categoryData[key].expense > 0)
      .map(key => ({
        category: key,
        amount: categoryData[key].expense,
        percentage: this.data.overview.totalExpense > 0 ? 
          (categoryData[key].expense / this.data.overview.totalExpense * 100) : 0
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);
    
    return expenses;
  },

  // 生成风险分析
  generateRiskAnalysis: function() {
    const overview = this.data.overview;
    const risks = [];
    
    // 现金流风险
    if (overview.totalExpense > overview.totalIncome * 1.2) {
      risks.push({
        level: 'high',
        type: '现金流风险',
        description: '支出超过收入20%以上，存在现金流不足风险'
      });
    }
    
    // 报销集中风险
    if (overview.reimbursementTotal > overview.totalExpense * 0.3) {
      risks.push({
        level: 'medium',
        type: '报销集中风险',
        description: '报销占总支出比例过高，需要加强报销管理'
      });
    }
    
    // 利润率风险
    const profitRate = overview.totalIncome > 0 ? 
      ((overview.totalIncome - overview.totalExpense) / overview.totalIncome * 100) : 0;
    
    if (profitRate < 5) {
      risks.push({
        level: 'high',
        type: '利润率风险',
        description: `利润率仅为${profitRate.toFixed(1)}%，盈利能力需要改善`
      });
    }
    
    return risks;
  },

  // 显示PDF预览
  showPDFPreview: function(pdfData) {
    this.setData({
      showPDFPreview: true,
      pdfData: pdfData
    });
  },

  // 关闭PDF预览
  onClosePDFPreview: function() {
    this.setData({
      showPDFPreview: false,
      pdfData: null
    });
  },

  // 发送邮件
  sendEmail: function() {
    this.setData({
      showEmailModal: true,
      emailData: {
        to: '',
        cc: '',
        subject: `财务概览报告 - ${new Date().toLocaleDateString()}`,
        content: this.generateEmailContent(),
        attachPDF: true,
        attachExcel: false
      }
    });
  },

  // 生成邮件内容
  generateEmailContent: function() {
    const overview = this.data.overview;
    const profitRate = overview.totalIncome > 0 ? 
      ((overview.totalIncome - overview.totalExpense) / overview.totalIncome * 100) : 0;
    
    return `尊敬的领导：

附件是智慧养鹅系统财务概览报告，具体情况如下：

📊 财务概况：
• 总收入：¥${overview.totalIncome.toFixed(2)}
• 总支出：¥${overview.totalExpense.toFixed(2)}
• 净利润：¥${(overview.totalIncome - overview.totalExpense).toFixed(2)}
• 利润率：${profitRate.toFixed(1)}%

📈 本期亮点：
• 收支结构合理，运营状况良好
• 成本控制在预期范围内
• 各项财务指标保持稳定

如有疑问，请随时与财务部门联系。

此致
敬礼！

财务部门
${new Date().toLocaleDateString()}`;
  },

  // 邮件表单输入
  onEmailInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`emailData.${field}`]: value
    });
  },

  // 切换附件选项
  onToggleAttachment: function(e) {
    const { type } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`emailData.${type}`]: value
    });
  },

  // 发送邮件
  onSendEmail: function() {
    const emailData = this.data.emailData;
    
    // 验证邮件地址
    if (!emailData.to.trim()) {
      wx.showToast({
        title: '请输入收件人邮箱',
        icon: 'none'
      });
      return;
    }

    if (!this.validateEmail(emailData.to)) {
      wx.showToast({
        title: '收件人邮箱格式错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '发送中...'
    });

    // 模拟发送邮件
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '邮件发送成功',
        icon: 'success'
      });

      this.setData({
        showEmailModal: false,
        emailData: null
      });

      // 记录发送历史
      const history = wx.getStorageSync('emailHistory') || [];
      history.unshift({
        to: emailData.to,
        subject: emailData.subject,
        sendTime: new Date().toISOString(),
        status: 'sent'
      });
      wx.setStorageSync('emailHistory', history.slice(0, 50)); // 只保留最近50条
    }, 2000);
  },

  // 验证邮箱格式
  validateEmail: function(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 关闭邮件模态框
  onCloseEmailModal: function() {
    this.setData({
      showEmailModal: false,
      emailData: null
    });
  }
});