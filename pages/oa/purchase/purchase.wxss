/* pages/oa/purchase/purchase.wxss */
@import '/styles/design-system.wxss';
@import '/styles/oa-common.wxss';

.purchase-container {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 20rpx 0 var(--space-2xl) 0;
}

/* 页面标题已移除 */

/* 通用区域样式 */
.quick-actions-section,
.statistics-section,
.recent-section {
  margin: var(--space-lg) var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
}

.quick-actions-section:first-child {
  margin-top: 10rpx;
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.view-all-btn {
  font-size: var(--text-lg);
  color: var(--primary);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-lg);
  background: var(--primary-subtle);
  font-weight: var(--font-semibold);
}

/* 快捷操作 */
.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  border-radius: var(--radius-xl);
  transition: var(--transition-all);
  border: 2rpx solid transparent;
  position: relative;
  overflow: hidden;
}

.quick-action-item:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: var(--shadow-lg);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
}

.action-icon image {
  width: 48rpx;
  height: 48rpx;
}

.icon-text {
  font-size: 44rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.action-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.action-description {
  font-size: 26rpx;
  color: #666;
}

.action-arrow {
  color: #999;
  font-size: 32rpx;
  font-weight: bold;
}

/* 统计概览 */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  background: #f8fbff;
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.1);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.stat-number.pending {
  color: #FF9500;
}

.stat-number.approved {
  color: #00A86B;
}

.stat-number.rejected {
  color: #FF3B30;
}

.stat-number.total {
  color: #0066CC;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 最近记录 */
.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.purchase-item {
  background: #f8fbff;
  border: 2rpx solid #e8f3ff;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}

.purchase-item:active {
  transform: translateY(-2rpx);
  border-color: #0066CC;
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.1);
}

.purchase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.purchase-category {
  background: #0066CC;
  color: white;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-weight: 500;
}

.purchase-status {
  font-size: 26rpx;
  font-weight: 600;
}

.urgency-badge {
  background: #FF3B30;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-weight: 500;
}

.purchase-content {
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.purchase-items {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.purchase-amount {
  font-size: 32rpx;
  color: #FF9500;
  font-weight: 700;
}

.purchase-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 2rpx solid #f0f0f0;
}

.submit-time {
  font-size: 24rpx;
  color: #999;
}

.arrow-icon {
  color: #ccc;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}