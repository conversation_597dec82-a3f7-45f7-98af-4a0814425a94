#!/bin/bash

# 智慧养鹅SAAS平台快速启动脚本
# Quick Start Script for Smart Goose SAAS Platform

set -e

echo "🚀 智慧养鹅SAAS平台快速启动脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}❌ $1 未安装，请先安装 $1${NC}"
        return 1
    else
        echo -e "${GREEN}✅ $1 已安装${NC}"
        return 0
    fi
}

# 检查环境
check_environment() {
    echo -e "${BLUE}📋 检查环境依赖...${NC}"
    
    local all_ok=true
    
    if ! check_command node; then
        echo "   请访问 https://nodejs.org/ 下载安装 Node.js"
        all_ok=false
    else
        node_version=$(node --version)
        echo "   Node.js 版本: $node_version"
    fi
    
    if ! check_command npm; then
        all_ok=false
    else
        npm_version=$(npm --version)
        echo "   npm 版本: $npm_version"
    fi
    
    if ! check_command mysql; then
        echo "   请安装 MySQL 数据库"
        echo "   macOS: brew install mysql"
        echo "   Ubuntu: sudo apt-get install mysql-server"
        all_ok=false
    else
        echo -e "${GREEN}✅ MySQL 已安装${NC}"
    fi
    
    if [ "$all_ok" = false ]; then
        echo -e "${RED}❌ 环境检查失败，请安装缺失的依赖后重试${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 检查MySQL服务
check_mysql_service() {
    echo -e "${BLUE}🔍 检查MySQL服务...${NC}"
    
    if mysqladmin ping -h localhost --silent 2>/dev/null; then
        echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
    else
        echo -e "${YELLOW}⚠️  MySQL服务未运行，尝试启动...${NC}"
        
        # 尝试启动MySQL (macOS)
        if command -v brew &> /dev/null; then
            brew services start mysql 2>/dev/null || true
        fi
        
        # 再次检查
        sleep 2
        if mysqladmin ping -h localhost --silent 2>/dev/null; then
            echo -e "${GREEN}✅ MySQL服务启动成功${NC}"
        else
            echo -e "${RED}❌ MySQL服务启动失败${NC}"
            echo "   请手动启动MySQL服务："
            echo "   macOS: brew services start mysql"
            echo "   Linux: sudo systemctl start mysql"
            echo "   Windows: 在服务管理器中启动MySQL服务"
            exit 1
        fi
    fi
}

# 创建环境配置文件
create_env_file() {
    if [ ! -f ".env.local" ]; then
        echo -e "${BLUE}📝 创建环境配置文件...${NC}"
        
        cat > .env.local << 'EOF'
# 智慧养鹅SAAS平台本地开发环境配置

# 服务器配置
NODE_ENV=development
PORT=3001

# SAAS平台数据库配置
SAAS_DB_HOST=localhost
SAAS_DB_PORT=3306
SAAS_DB_USER=saas_admin
SAAS_DB_PASSWORD=SmartGoose2024!
SAAS_DB_NAME=smart_goose_saas

# 租户数据库配置模板
TENANT_DB_HOST=localhost
TENANT_DB_PORT=3306
TENANT_DB_USER=saas_admin
TENANT_DB_PASSWORD=SmartGoose2024!

# JWT配置
JWT_SECRET=smart_goose_saas_jwt_secret_key_2024_make_it_very_secure_and_long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 微信小程序配置（演示用）
WECHAT_API_URL=https://api.weixin.qq.com

# SAAS管理后台配置
SAAS_ADMIN_PORT=3002
SAAS_ADMIN_SESSION_SECRET=smart_goose_admin_session_secret_2024

# 日志配置
LOG_LEVEL=debug

# 开发模式配置
ENABLE_CORS=true
ENABLE_DETAILED_LOGS=true
EOF
        
        echo -e "${GREEN}✅ 环境配置文件已创建: .env.local${NC}"
    else
        echo -e "${YELLOW}⚠️  环境配置文件已存在: .env.local${NC}"
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装项目依赖...${NC}"
    
    if [ ! -d "node_modules" ]; then
        npm install
        echo -e "${GREEN}✅ 依赖安装完成${NC}"
    else
        echo -e "${YELLOW}⚠️  依赖已安装，跳过${NC}"
    fi
}

# 初始化数据库
init_database() {
    echo -e "${BLUE}🗄️  初始化数据库...${NC}"
    
    # 检查SAAS用户是否存在
    user_exists=$(mysql -u root -e "SELECT User FROM mysql.user WHERE User='saas_admin' AND Host='localhost';" 2>/dev/null | grep saas_admin || echo "")
    
    if [ -z "$user_exists" ]; then
        echo "创建数据库用户和数据库..."
        
        # 创建数据库和用户
        mysql -u root << 'EOF'
-- 创建SAAS平台专用用户
CREATE USER 'saas_admin'@'localhost' IDENTIFIED BY 'SmartGoose2024!';

-- 创建SAAS平台数据库
CREATE DATABASE IF NOT EXISTS smart_goose_saas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 授权SAAS用户管理平台数据库
GRANT ALL PRIVILEGES ON smart_goose_saas.* TO 'saas_admin'@'localhost';

-- 授权创建和管理租户数据库
GRANT CREATE, DROP, ALTER, INDEX, REFERENCES ON *.* TO 'saas_admin'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
EOF
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 数据库用户创建成功${NC}"
        else
            echo -e "${RED}❌ 数据库用户创建失败${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  数据库用户已存在${NC}"
        # 确保数据库存在
        mysql -u root -e "CREATE DATABASE IF NOT EXISTS smart_goose_saas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || true
    fi
    
    # 检查数据库表是否已存在
    table_count=$(mysql -u saas_admin -pSmartGoose2024! smart_goose_saas -e "SHOW TABLES;" 2>/dev/null | wc -l)
    
    if [ "$table_count" -lt 2 ]; then
        echo "正在初始化数据库结构..."
        if mysql -u saas_admin -pSmartGoose2024! smart_goose_saas < database/saas-platform-init.sql 2>/dev/null; then
            echo -e "${GREEN}✅ 数据库结构初始化完成${NC}"
        else
            echo -e "${RED}❌ 数据库结构初始化失败${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  数据库表已存在，跳过结构初始化${NC}"
    fi
}

# 创建测试数据
create_test_data() {
    echo -e "${BLUE}🧪 创建测试数据...${NC}"
    
    # 检查是否已有测试数据
    tenant_count=$(mysql -u saas_admin -pSmartGoose2024! smart_goose_saas -e "SELECT COUNT(*) FROM tenants;" 2>/dev/null | tail -1)
    
    if [ "$tenant_count" -gt 0 ]; then
        echo -e "${YELLOW}⚠️  测试数据已存在，跳过创建${NC}"
        return
    fi
    
    # 创建测试数据脚本
    cat > scripts/test-data.sql << 'EOF'
-- 插入测试租户
INSERT IGNORE INTO tenants (
    name, code, status, plan, maxUsers, maxFlocks, 
    contactEmail, contactPhone, contactPerson,
    createdAt, updatedAt
) VALUES 
('Demo农场', 'demo', 'active', 'premium', 100, 50, '<EMAIL>', '138-0000-0000', '张经理', NOW(), NOW()),
('测试农场', 'test', 'active', 'standard', 50, 20, '<EMAIL>', '139-0000-0000', '李经理', NOW(), NOW()),
('体验农场', 'trial', 'trial', 'free', 10, 5, '<EMAIL>', '137-0000-0000', '王经理', NOW(), NOW());

-- 插入租户小程序配置
INSERT IGNORE INTO tenant_miniprogram_configs (
    tenantId, appId, appSecret, version, status, 
    apiEndpoint, themeConfig, featuresConfig,
    createdAt, updatedAt
) VALUES 
(1, 'wxdemo123456789', 'demo_app_secret_here', '1.0.0', 'development', 
 'http://localhost:3001/api/v1/tenant', 
 '{"primaryColor": "#28a745", "logo": "/images/demo-logo.png"}',
 '{"ai_diagnosis": true, "shop_module": true, "data_export": true, "advanced_analytics": true}',
 NOW(), NOW()),
(2, 'wxtest123456789', 'test_app_secret_here', '1.0.0', 'development',
 'http://localhost:3001/api/v1/tenant',
 '{"primaryColor": "#17a2b8", "logo": "/images/test-logo.png"}',
 '{"ai_diagnosis": true, "shop_module": false, "data_export": true, "advanced_analytics": false}',
 NOW(), NOW()),
(3, 'wxtrial123456789', 'trial_app_secret_here', '1.0.0', 'development',
 'http://localhost:3001/api/v1/tenant',
 '{"primaryColor": "#6c757d", "logo": "/images/trial-logo.png"}',
 '{"ai_diagnosis": false, "shop_module": false, "data_export": false, "advanced_analytics": false}',
 NOW(), NOW());

-- 插入平台管理员（密码: admin123）
INSERT IGNORE INTO platform_admins (
    username, email, password, role, status,
    createdAt, updatedAt
) VALUES 
('admin', '<EMAIL>', '$2b$10$8TpKSy2VE.BKqNM7BZCrI.dM8TJhYZQ8KZoJz5dJ5J5J5J5J5J5J5u', 'super_admin', 'active', NOW(), NOW());
EOF
    
    # 执行测试数据脚本
    if mysql -u saas_admin -pSmartGoose2024! smart_goose_saas < scripts/test-data.sql 2>/dev/null; then
        echo -e "${GREEN}✅ 测试数据创建完成${NC}"
    else
        echo -e "${RED}❌ 测试数据创建失败${NC}"
        echo "   请检查数据库连接和权限"
        return 1
    fi
}

# 启动服务
start_services() {
    echo -e "${BLUE}🚀 启动服务...${NC}"
    
    # 检查端口是否占用
    if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 3001 已被占用${NC}"
        echo "请先停止占用该端口的进程，或者修改配置文件中的端口号"
        return 1
    fi
    
    if lsof -Pi :3002 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 3002 已被占用${NC}"
        echo "请先停止占用该端口的进程，或者修改配置文件中的端口号"
        return 1
    fi
    
    echo "启动主应用服务 (端口 3001)..."
    NODE_ENV=development node backend/app.js &
    MAIN_PID=$!
    sleep 2
    
    echo "启动SAAS管理后台 (端口 3002)..."
    NODE_ENV=development node backend/saas-admin/app.js &
    ADMIN_PID=$!
    sleep 2
    
    # 检查服务是否启动成功
    if kill -0 $MAIN_PID 2>/dev/null && kill -0 $ADMIN_PID 2>/dev/null; then
        echo -e "${GREEN}✅ 所有服务启动成功！${NC}"
        echo ""
        echo "🌐 访问地址："
        echo "   📱 主应用API: http://localhost:3001"
        echo "   🛠️  SAAS管理后台: http://localhost:3002"
        echo "   📊 API文档: http://localhost:3001/api/docs"
        echo ""
        echo "🧪 快速测试："
        echo "   curl http://localhost:3001/health"
        echo "   curl -H \"X-Tenant-Code: demo\" http://localhost:3001/api/v1/tenant/config/features"
        echo ""
        echo "⚡ 测试工具："
        echo "   在浏览器中打开: scripts/test-miniprogram.html"
        echo ""
        echo "🛑 停止服务: Ctrl+C"
        
        # 等待用户中断
        trap "echo ''; echo '🛑 正在停止服务...'; kill $MAIN_PID $ADMIN_PID 2>/dev/null; exit 0" INT
        wait
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        kill $MAIN_PID $ADMIN_PID 2>/dev/null
        exit 1
    fi
}

# 主函数
main() {
    echo ""
    echo "开始时间: $(date)"
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f "backend/app.js" ]; then
        echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 执行各个步骤
    check_environment
    echo ""
    
    check_mysql_service
    echo ""
    
    create_env_file
    echo ""
    
    install_dependencies
    echo ""
    
    init_database
    echo ""
    
    create_test_data
    echo ""
    
    start_services
}

# 运行主函数
main "$@"