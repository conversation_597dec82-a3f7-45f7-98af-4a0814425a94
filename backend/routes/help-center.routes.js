const express = require('express');
const router = express.Router();
const HelpCenterController = require('../controllers/help-center.controller');
const authMiddleware = require('../middleware/auth.middleware');
const adminMiddleware = require('../middleware/admin.middleware');

// ======================== 公开API路由 ========================

// 获取帮助分类
router.get('/categories', HelpCenterController.getCategories);

// 获取分类详情
router.get('/categories/:id', HelpCenterController.getCategoryDetail);

// 获取文章列表
router.get('/articles', HelpCenterController.getArticles);

// 获取文章详情
router.get('/articles/:id', HelpCenterController.getArticleDetail);

// 获取FAQ列表
router.get('/faqs', HelpCenterController.getFAQs);

// 获取教程列表
router.get('/tutorials', HelpCenterController.getTutorials);

// 获取教程详情
router.get('/tutorials/:id', HelpCenterController.getTutorialDetail);

// 综合搜索
router.get('/search', HelpCenterController.search);

// 获取搜索建议
router.get('/search/suggestions', HelpCenterController.getSearchSuggestions);

// 获取统计数据
router.get('/statistics', HelpCenterController.getStatistics);

// 获取公开设置
router.get('/settings', HelpCenterController.getPublicSettings);

// 提交反馈（可选择性需要登录）
router.post('/feedback', HelpCenterController.submitFeedback);

// ======================== 管理员API路由 ========================

// 分类管理
router.post('/admin/categories', authMiddleware, adminMiddleware, HelpCenterController.createCategory);
router.put('/admin/categories/:id', authMiddleware, adminMiddleware, HelpCenterController.updateCategory);
router.delete('/admin/categories/:id', authMiddleware, adminMiddleware, HelpCenterController.deleteCategory);

// 文章管理
router.post('/admin/articles', authMiddleware, adminMiddleware, HelpCenterController.createArticle);
router.put('/admin/articles/:id', authMiddleware, adminMiddleware, HelpCenterController.updateArticle);
router.delete('/admin/articles/:id', authMiddleware, adminMiddleware, HelpCenterController.deleteArticle);

// FAQ管理
router.post('/admin/faqs', authMiddleware, adminMiddleware, HelpCenterController.createFAQ);
router.put('/admin/faqs/:id', authMiddleware, adminMiddleware, HelpCenterController.updateFAQ);
router.delete('/admin/faqs/:id', authMiddleware, adminMiddleware, HelpCenterController.deleteFAQ);

// 管理员统计
router.get('/admin/statistics', authMiddleware, adminMiddleware, HelpCenterController.getAdminStatistics);

// 设置管理
router.put('/admin/settings', authMiddleware, adminMiddleware, HelpCenterController.updateSetting);

module.exports = router;