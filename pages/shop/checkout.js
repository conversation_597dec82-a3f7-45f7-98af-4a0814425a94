// pages/shop/checkout.js
Page({
  data: {
    cartItems: [],
    selectedItems: [],
    totalPrice: 0,
    discountAmount: 0,
    actualAmount: 0,
    address: {
      name: '张三',
      phone: '***********', 
      detail: '银丰路10号财津银座B座417'
    },
    note: '',
    // 发票相关数据
    invoiceData: {
      type: 'none', // none: 不开发票, personal: 个人发票, company: 企业发票
      personal: {
        email: ''
      },
      company: {
        title: '',
        taxNumber: '',
        address: '',
        phone: '',
        bank: '',
        account: '',
        email: ''
      }
    },
    // 发票选择模态框
    showInvoiceModal: false
  },

  onLoad: function (options) {
    // 页面加载
    this.loadCheckoutData(options);
  },

  loadCheckoutData: function(options) {
    // 如果是从商品详情页直接购买
    if (options.goodsId && options.quantity) {
      // 模拟获取商品信息
      const goodsData = [
        {
          id: 1,
          name: '优质鹅饲料',
          price: 99.99,
          image: '/images/icons/goods1.png',
          quantity: parseInt(options.quantity)
        },
        {
          id: 2,
          name: '疫苗套装',
          price: 199.99,
          image: '/images/icons/goods2.png',
          quantity: parseInt(options.quantity)
        },
        {
          id: 3,
          name: '养殖设备',
          price: 299.99,
          image: '/images/icons/goods3.png',
          quantity: parseInt(options.quantity)
        }
      ];
      
      const goods = goodsData.find(item => item.id == options.goodsId);
      if (goods) {
        // 计算单项商品总价
        const itemsWithTotal = [{
          ...goods,
          total: (goods.price * goods.quantity).toFixed(2)
        }];
        
        const totalPrice = (goods.price * goods.quantity).toFixed(2);
        this.setData({
          cartItems: itemsWithTotal,
          totalPrice: totalPrice,
          actualAmount: totalPrice
        });
      }
      return;
    }
    
    // 如果是从购物车进入
    const cart = wx.getStorageSync('cart') || [];
    const selectedItems = cart.filter(item => item.selected !== false);
    
    if (selectedItems.length > 0) {
      // 为每个商品项添加总价字段
      const itemsWithTotal = selectedItems.map(item => {
        return {
          ...item,
          total: (item.price * item.quantity).toFixed(2)
        };
      });
      
      this.setData({
        cartItems: itemsWithTotal
      });
      
      this.calculateTotal();
    } else {
      // 如果没有选中的商品，返回购物车
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      });
      wx.navigateBack();
    }
  },

  calculateTotal: function() {
    let totalPrice = 0;
    
    this.data.cartItems.forEach(item => {
      totalPrice += item.price * item.quantity;
    });
    
    const finalTotalPrice = totalPrice.toFixed(2);
    const actualAmount = (totalPrice - this.data.discountAmount).toFixed(2);
    
    this.setData({
      totalPrice: finalTotalPrice,
      actualAmount: actualAmount
    });
  },

  onNoteChange: function(e) {
    this.setData({
      note: e.detail.value
    });
  },

  onAddressSelect: function() {
    wx.navigateTo({
      url: '/pages/address/address?select=true'
    });
  },

  // 发票选择
  onInvoiceSelect: function() {
    this.setData({
      showInvoiceModal: true
    });
  },

  // 关闭发票模态框
  onCloseInvoiceModal: function() {
    this.setData({
      showInvoiceModal: false
    });
  },

  // 选择发票类型
  onInvoiceTypeSelect: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      'invoiceData.type': type,
      showInvoiceModal: false
    });

    // 如果选择了需要发票，跳转到发票信息填写页面
    if (type !== 'none') {
      wx.navigateTo({
        url: `/pages/shop/invoice-form?type=${type}`
      });
    }
  },

  onSubmitOrder: function() {
    // 检查是否有商品
    if (this.data.cartItems.length === 0) {
      wx.showToast({
        title: '没有可结算的商品',
        icon: 'none'
      });
      return;
    }
    
    // 模拟提交订单
    wx.showLoading({
      title: '提交订单中...'
    });
    
    // 模拟网络请求
    setTimeout(() => {
      wx.hideLoading();
      
      // 清除购物车中已购买的商品
      const cart = wx.getStorageSync('cart') || [];
      const purchasedItemIds = this.data.cartItems.map(item => item.id);
      const remainingCart = cart.filter(item => !purchasedItemIds.includes(item.id));
      wx.setStorageSync('cart', remainingCart);
      
      wx.showToast({
        title: '订单提交成功',
        icon: 'success'
      });
      
      // 跳转到订单详情页面
      setTimeout(() => {
        const orderId = 'ORDER' + Date.now();
        wx.redirectTo({
          url: `/pages/order-detail/order-detail?id=${orderId}`
        });
      }, 1500);
    }, 1500);
  }
});