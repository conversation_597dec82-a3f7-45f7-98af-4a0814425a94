<!-- pages/oa/reimbursement/apply/apply.wxml -->
<view class="container">
  <!-- 报销申请表单 -->
  <view class="form-section">
    <!-- 基本信息 -->
    <view class="form-item">
      <text class="form-label">报销标题 <text class="required">*</text></text>
      <input 
        class="form-input"
        placeholder="请输入报销标题"
        value="{{formData.title}}"
        bindinput="onInputChange"
        data-field="title"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">报销说明</text>
      <textarea 
        class="form-textarea"
        placeholder="请输入报销说明（可选）"
        value="{{formData.description}}"
        bindinput="onInputChange"
        data-field="description"
        maxlength="200"
      ></textarea>
    </view>
    
    <view class="form-row">
      <view class="form-item half">
        <text class="form-label">报销类别 <text class="required">*</text></text>
        <picker
          mode="selector"
          range="{{categories}}"
          range-key="label"
          value="{{categoryIndex}}"
          bindchange="onInputChange"
          data-field="category"
        >
          <view class="form-picker">
            <text class="picker-text {{!formData.category ? 'placeholder' : ''}}">{{selectedCategoryLabel}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="form-item half">
        <text class="form-label">报销金额 <text class="required">*</text></text>
        <input 
          class="form-input"
          type="digit"
          placeholder="0.00"
          value="{{formData.amount}}"
          bindinput="onInputChange"
          data-field="amount"
        />
      </view>
    </view>
    
    <view class="form-item">
      <text class="form-label">费用发生日期 <text class="required">*</text></text>
      <picker 
        mode="date" 
        value="{{formData.expenseDate}}"
        bindchange="onDateChange"
        data-field="expenseDate"
      >
        <view class="form-picker">
          <text class="picker-text {{!formData.expenseDate ? 'placeholder' : ''}}">{{formData.expenseDate || '请选择日期'}}</text>
          <text class="picker-icon">📅</text>
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <text class="form-label">业务目的 <text class="required">*</text></text>
      <textarea 
        class="form-textarea"
        placeholder="请详细说明此次费用的业务目的"
        value="{{formData.businessPurpose}}"
        bindinput="onInputChange"
        data-field="businessPurpose"
        maxlength="300"
      ></textarea>
    </view>
    
    <view class="form-item">
      <text class="form-label">支付方式</text>
      <picker
        mode="selector"
        range="{{paymentMethods}}"
        range-key="label"
        value="{{paymentMethodIndex}}"
        bindchange="onInputChange"
        data-field="paymentMethod"
      >
        <view class="form-picker">
          <text class="picker-text {{!formData.paymentMethod ? 'placeholder' : ''}}">{{selectedPaymentMethodLabel}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="draft-btn" 
      bindtap="onSaveDraft" 
      disabled="{{savingDraft}}"
    >
      {{savingDraft ? '保存中...' : '保存草稿'}}
    </button>
    <button 
      class="submit-btn" 
      bindtap="onSubmit" 
      disabled="{{submitting}}"
      loading="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交申请'}}
    </button>
  </view>
</view>