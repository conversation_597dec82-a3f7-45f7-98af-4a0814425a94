<!--pages/oa/purchase/list/list.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索采购标题、物品名称..."
        value="{{filters.keyword}}"
        bindinput="onSearchInput"
      />
    </view>
    <button class="filter-toggle {{showFilters ? 'active' : ''}}" bindtap="toggleFilters">
      筛选
    </button>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel {{showFilters ? 'show' : ''}}" wx:if="{{showFilters}}">
    <view class="filter-item">
      <text class="filter-label">类别</text>
      <picker
        mode="selector"
        range="{{categoryOptions}}"
        range-key="label"
        value="{{categoryIndex}}"
        bindchange="onCategoryFilter"
      >
        <view class="filter-selector">
          <text>{{selectedCategoryLabel}}</text>
          <text class="arrow">▼</text>
        </view>
      </picker>
    </view>
    
    <view class="filter-item">
      <text class="filter-label">时间</text>
      <picker
        mode="selector"
        range="{{timeRangeOptions}}"
        range-key="label"
        value="{{timeRangeIndex}}"
        bindchange="onTimeRangeFilter"
      >
        <view class="filter-selector">
          <text>{{selectedTimeRangeLabel}}</text>
          <text class="arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 状态标签 -->
  <view class="status-bar">
    <scroll-view class="status-scroll" scroll-x="true">
      <view class="status-tabs">
        <view 
          wx:for="{{statusOptions}}" 
          wx:key="value" 
          class="status-tab {{filters.status === item.value ? 'active' : ''}}"
          bindtap="onStatusFilter" 
          data-status="{{item.value}}"
        >
          <text>{{item.label}}</text>
          <text class="count" wx:if="{{item.count > 0}}">{{item.count}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 采购列表 -->
  <view class="list-container">
    <view 
      wx:for="{{purchaseList}}" 
      wx:key="id" 
      class="purchase-card"
      bindtap="onPurchaseItemTap"
      data-id="{{item.id}}"
    >
      <!-- 卡片头部 -->
      <view class="card-header">
        <view class="title-row">
          <text class="title">{{item.title}}</text>
          <view class="status-tag" style="background-color: {{getStatusStyle(item.status)}}">
            {{item.statusText}}
          </view>
        </view>
        <view class="meta-row">
          <text class="order-number">#{{item.requestNumber}}</text>
          <text class="time">{{formatTimeAgo(item.createdAt)}}</text>
        </view>
      </view>
      
      <!-- 卡片内容 -->
      <view class="card-content">
        <view class="info-row">
          <text class="label">申请人</text>
          <text class="value">{{item.applicantName}}</text>
        </view>
        
        <view class="info-row">
          <text class="label">类别</text>
          <text class="category">{{item.categoryLabel}}</text>
        </view>
        
        <view class="info-row" wx:if="{{item.items && item.items.length > 0}}">
          <text class="label">物品</text>
          <text class="items">
            {{item.items[0].itemName}}
            <text wx:if="{{item.items.length > 1}}" class="more">等{{item.items.length}}项</text>
          </text>
        </view>
        
        <view class="info-row amount-row">
          <text class="label">金额</text>
          <text class="amount">{{formatCurrency(item.totalAmount)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && purchaseList.length === 0}}">
    <view class="empty-icon">📋</view>
    <text class="empty-title">暂无采购申请</text>
    <text class="empty-desc">
      还没有任何采购申请，点击右下角按钮开始创建
    </text>
    <button class="create-btn" bindtap="onCreatePurchase" wx:if="{{permissions.canCreateApplication}}">
      立即创建
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading && !refreshing}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载...</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!pagination.hasMore && purchaseList.length > 0}}">
    没有更多数据了
  </view>

  <!-- 悬浮按钮 -->
  <view class="fab-container" wx:if="{{permissions.canCreateApplication && purchaseList.length > 0}}">
    <button class="fab-btn" bindtap="onCreatePurchase">
      ➕
    </button>
  </view>
</view>