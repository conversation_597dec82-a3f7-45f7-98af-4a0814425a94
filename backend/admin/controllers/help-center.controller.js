const HelpCenterModel = require('../../models/help-center.model');

class AdminHelpCenterController {

  // ======================== 主页面渲染 ========================

  // 帮助中心管理主页
  static async index(req, res) {
    try {
      // 获取统计数据
      const statistics = await HelpCenterModel.getStatistics();
      
      // 获取最近的文章、FAQ、教程
      const recentArticles = await HelpCenterModel.getArticles({ limit: 5 });
      const recentFAQs = await HelpCenterModel.getFAQs({ limit: 5 });
      const recentTutorials = await HelpCenterModel.getTutorials({ limit: 5 });

      res.render('help-center/index', {
        title: '帮助中心管理',
        statistics,
        recentArticles,
        recentFAQs,
        recentTutorials,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载帮助中心主页失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载帮助中心主页失败',
        error: error 
      });
    }
  }

  // ======================== 分类管理 ========================

  // 分类管理页面
  static async categories(req, res) {
    try {
      const categories = await HelpCenterModel.getCategories();
      
      res.render('help-center/categories', {
        title: '分类管理',
        categories,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载分类管理页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载分类管理页面失败',
        error: error 
      });
    }
  }

  // 创建分类页面
  static async createCategoryPage(req, res) {
    res.render('help-center/category-form', {
      title: '创建分类',
      category: null,
      isEdit: false,
      user: req.user
    });
  }

  // 编辑分类页面
  static async editCategoryPage(req, res) {
    try {
      const { id } = req.params;
      const category = await HelpCenterModel.getCategoryById(id);
      
      if (!category) {
        return res.status(404).render('error', { 
          message: '分类不存在' 
        });
      }

      res.render('help-center/category-form', {
        title: '编辑分类',
        category,
        isEdit: true,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载编辑分类页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载编辑分类页面失败',
        error: error 
      });
    }
  }

  // ======================== 文章管理 ========================

  // 文章管理页面
  static async articles(req, res) {
    try {
      const { page = 1, category_id, keyword } = req.query;
      const limit = 20;
      
      const filters = {
        category_id,
        keyword,
        limit: limit + 1 // 多查询一条用于判断是否有下一页
      };

      const articles = await HelpCenterModel.getArticles(filters);
      const categories = await HelpCenterModel.getCategories();
      
      const hasNextPage = articles.length > limit;
      if (hasNextPage) articles.pop(); // 移除多查询的那一条

      res.render('help-center/articles', {
        title: '文章管理',
        articles,
        categories,
        currentPage: parseInt(page),
        hasNextPage,
        filters: { category_id, keyword },
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载文章管理页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载文章管理页面失败',
        error: error 
      });
    }
  }

  // 创建文章页面
  static async createArticlePage(req, res) {
    try {
      const categories = await HelpCenterModel.getCategories();
      
      res.render('help-center/article-form', {
        title: '创建文章',
        article: null,
        categories,
        isEdit: false,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载创建文章页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载创建文章页面失败',
        error: error 
      });
    }
  }

  // 编辑文章页面
  static async editArticlePage(req, res) {
    try {
      const { id } = req.params;
      const article = await HelpCenterModel.getArticleById(id);
      const categories = await HelpCenterModel.getCategories();
      
      if (!article) {
        return res.status(404).render('error', { 
          message: '文章不存在' 
        });
      }

      // 解析tags
      if (article.tags) {
        article.tagsArray = JSON.parse(article.tags);
      }

      res.render('help-center/article-form', {
        title: '编辑文章',
        article,
        categories,
        isEdit: true,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载编辑文章页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载编辑文章页面失败',
        error: error 
      });
    }
  }

  // ======================== FAQ管理 ========================

  // FAQ管理页面
  static async faqs(req, res) {
    try {
      const { page = 1, category_id, keyword } = req.query;
      const limit = 20;
      
      const filters = {
        category_id,
        keyword,
        limit: limit + 1
      };

      const faqs = await HelpCenterModel.getFAQs(filters);
      const categories = await HelpCenterModel.getCategories();
      
      const hasNextPage = faqs.length > limit;
      if (hasNextPage) faqs.pop();

      res.render('help-center/faqs', {
        title: 'FAQ管理',
        faqs,
        categories,
        currentPage: parseInt(page),
        hasNextPage,
        filters: { category_id, keyword },
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载FAQ管理页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载FAQ管理页面失败',
        error: error 
      });
    }
  }

  // 创建FAQ页面
  static async createFAQPage(req, res) {
    try {
      const categories = await HelpCenterModel.getCategories();
      
      res.render('help-center/faq-form', {
        title: '创建FAQ',
        faq: null,
        categories,
        isEdit: false,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载创建FAQ页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载创建FAQ页面失败',
        error: error 
      });
    }
  }

  // 编辑FAQ页面
  static async editFAQPage(req, res) {
    try {
      const { id } = req.params;
      const faq = await HelpCenterModel.getFAQById(id);
      const categories = await HelpCenterModel.getCategories();
      
      if (!faq) {
        return res.status(404).render('error', { 
          message: 'FAQ不存在' 
        });
      }

      // 解析tags
      if (faq.tags) {
        faq.tagsArray = JSON.parse(faq.tags);
      }

      res.render('help-center/faq-form', {
        title: '编辑FAQ',
        faq,
        categories,
        isEdit: true,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载编辑FAQ页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载编辑FAQ页面失败',
        error: error 
      });
    }
  }

  // ======================== 教程管理 ========================

  // 教程管理页面
  static async tutorials(req, res) {
    try {
      const { page = 1, category_id, difficulty } = req.query;
      const limit = 20;
      
      const filters = {
        category_id,
        difficulty,
        limit: limit + 1
      };

      const tutorials = await HelpCenterModel.getTutorials(filters);
      const categories = await HelpCenterModel.getCategories();
      
      const hasNextPage = tutorials.length > limit;
      if (hasNextPage) tutorials.pop();

      res.render('help-center/tutorials', {
        title: '教程管理',
        tutorials,
        categories,
        currentPage: parseInt(page),
        hasNextPage,
        filters: { category_id, difficulty },
        difficulties: [
          { value: 'beginner', label: '初级' },
          { value: 'intermediate', label: '中级' },
          { value: 'advanced', label: '高级' }
        ],
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载教程管理页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载教程管理页面失败',
        error: error 
      });
    }
  }

  // 创建教程页面
  static async createTutorialPage(req, res) {
    try {
      const categories = await HelpCenterModel.getCategories();
      
      res.render('help-center/tutorial-form', {
        title: '创建教程',
        tutorial: null,
        categories,
        difficulties: [
          { value: 'beginner', label: '初级' },
          { value: 'intermediate', label: '中级' },
          { value: 'advanced', label: '高级' }
        ],
        isEdit: false,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载创建教程页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载创建教程页面失败',
        error: error 
      });
    }
  }

  // 编辑教程页面
  static async editTutorialPage(req, res) {
    try {
      const { id } = req.params;
      const tutorial = await HelpCenterModel.getTutorialById(id);
      const categories = await HelpCenterModel.getCategories();
      
      if (!tutorial) {
        return res.status(404).render('error', { 
          message: '教程不存在' 
        });
      }

      // 解析JSON字段
      if (tutorial.tags) {
        tutorial.tagsArray = JSON.parse(tutorial.tags);
      }
      if (tutorial.steps) {
        tutorial.stepsArray = JSON.parse(tutorial.steps);
      }

      res.render('help-center/tutorial-form', {
        title: '编辑教程',
        tutorial,
        categories,
        difficulties: [
          { value: 'beginner', label: '初级' },
          { value: 'intermediate', label: '中级' },
          { value: 'advanced', label: '高级' }
        ],
        isEdit: true,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载编辑教程页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载编辑教程页面失败',
        error: error 
      });
    }
  }

  // ======================== 反馈管理 ========================

  // 反馈管理页面
  static async feedback(req, res) {
    try {
      const { page = 1, type, status } = req.query;
      
      // 简化的反馈查询（如果需要，可以在HelpCenterModel中添加getFeedback方法）
      const feedback = []; // 这里可以实现实际的反馈查询逻辑
      
      res.render('help-center/feedback', {
        title: '反馈管理',
        feedback,
        types: [
          { value: 'article', label: '文章反馈' },
          { value: 'faq', label: 'FAQ反馈' },
          { value: 'tutorial', label: '教程反馈' },
          { value: 'general', label: '一般反馈' }
        ],
        statuses: [
          { value: 'pending', label: '待处理' },
          { value: 'reviewed', label: '已查看' },
          { value: 'resolved', label: '已解决' }
        ],
        filters: { type, status },
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载反馈管理页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载反馈管理页面失败',
        error: error 
      });
    }
  }

  // ======================== 设置管理 ========================

  // 设置管理页面
  static async settings(req, res) {
    try {
      const settings = await HelpCenterModel.getPublicSettings();
      
      res.render('help-center/settings', {
        title: '设置管理',
        settings,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载设置管理页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载设置管理页面失败',
        error: error 
      });
    }
  }

  // ======================== 统计报告 ========================

  // 统计报告页面
  static async analytics(req, res) {
    try {
      const statistics = await HelpCenterModel.getStatistics();
      
      // 这里可以添加更详细的统计数据查询，如按日期分组的数据等
      
      res.render('help-center/analytics', {
        title: '统计报告',
        statistics,
        user: req.user
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('加载统计报告页面失败:', error); } catch(_) {}

      res.status(500).render('error', { 
        message: '加载统计报告页面失败',
        error: error 
      });
    }
  }

  // ======================== API处理方法 ========================

  // 处理分类相关API请求
  static async handleCategoryAPI(req, res) {
    const HelpCenterController = require('../../controllers/help-center.controller');
    
    switch (req.method) {
      case 'POST':
        return await HelpCenterController.createCategory(req, res);
      case 'PUT':
        return await HelpCenterController.updateCategory(req, res);
      case 'DELETE':
        return await HelpCenterController.deleteCategory(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  }

  // 处理文章相关API请求
  static async handleArticleAPI(req, res) {
    const HelpCenterController = require('../../controllers/help-center.controller');
    
    switch (req.method) {
      case 'POST':
        return await HelpCenterController.createArticle(req, res);
      case 'PUT':
        return await HelpCenterController.updateArticle(req, res);
      case 'DELETE':
        return await HelpCenterController.deleteArticle(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  }

  // 处理FAQ相关API请求
  static async handleFAQAPI(req, res) {
    const HelpCenterController = require('../../controllers/help-center.controller');
    
    switch (req.method) {
      case 'POST':
        return await HelpCenterController.createFAQ(req, res);
      case 'PUT':
        return await HelpCenterController.updateFAQ(req, res);
      case 'DELETE':
        return await HelpCenterController.deleteFAQ(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  }

  // 处理设置相关API请求
  static async handleSettingsAPI(req, res) {
    const HelpCenterController = require('../../controllers/help-center.controller');
    return await HelpCenterController.updateSetting(req, res);
  }
}

module.exports = AdminHelpCenterController;