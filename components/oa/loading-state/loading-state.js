// components/oa/loading-state/loading-state.js

/**
 * OA加载状态组件
 * 统一的加载、错误、空状态处理
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 加载状态类型: loading, error, empty, success
    type: {
      type: String,
      value: 'loading'
    },
    // 自定义加载文本
    loadingText: {
      type: String,
      value: '正在加载...'
    },
    // 自定义错误文本
    errorText: {
      type: String,
      value: '加载失败，请重试'
    },
    // 自定义空状态文本
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      value: true
    },
    // 是否显示全屏模式
    fullScreen: {
      type: Boolean,
      value: false
    },
    // 自定义图标
    customIcon: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 重试按钮点击
     */
    onRetryTap() {
      this.triggerEvent('retry');
    },

    /**
     * 获取状态图标
     */
    getStateIcon() {
      const { type, customIcon } = this.properties;
      
      if (customIcon) {
        return customIcon;
      }
      
      const icons = {
        loading: '⏳',
        error: '❌',
        empty: '📝',
        success: '✅'
      };
      
      return icons[type] || '📋';
    },

    /**
     * 获取状态文本
     */
    getStateText() {
      const { type, loadingText, errorText, emptyText } = this.properties;
      
      const texts = {
        loading: loadingText,
        error: errorText,
        empty: emptyText,
        success: '操作成功'
      };
      
      return texts[type] || '未知状态';
    }
  }
});