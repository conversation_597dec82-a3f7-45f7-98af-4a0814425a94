/* components/health-trend-chart/health-trend-chart.wxss */
.chart-container {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.chart-legend {
  display: flex;
  gap: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 4rpx;
}

.legend-color.healthy {
  background: #52C41A;
}

.legend-color.sick {
  background: #FAAD14;
}

.legend-color.death {
  background: #FF4D4F;
}

.legend-item .legend-text {
  font-size: 24rpx;
  color: #666;
}

.chart-content {
  position: relative;
  height: 400rpx;
  width: 100%;
}

.chart-canvas {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

/* 提示框样式 */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  z-index: 10;
  pointer-events: none;
  transform: translateX(-50%);
}

.tooltip-content {
  color: white;
  font-size: 24rpx;
}

.tooltip-date {
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.tooltip-item {
  margin-bottom: 4rpx;
}

.tooltip-label {
  font-size: 22rpx;
}

.tooltip-label.healthy {
  color: #52C41A;
}

.tooltip-label.sick {
  color: #FAAD14;
}

.tooltip-label.death {
  color: #FF4D4F;
}

/* 空状态 */
.empty-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
