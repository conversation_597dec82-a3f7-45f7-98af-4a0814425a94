<!-- 帮助搜索页面 -->
<view class="search-container">
  <!-- 搜索头部 -->
  <view class="search-header">
    <view class="search-bar">
      <text class="search-icon">🔍</text>
      <input class="search-input" 
             placeholder="搜索帮助内容、问题、教程..." 
             value="{{searchKeyword}}" 
             bindinput="onSearchInput"
             bindconfirm="onSearch"
             focus="{{true}}" />
      <text class="clear-btn" wx:if="{{searchKeyword}}" bindtap="onClearSearch">×</text>
    </view>
    <button class="search-btn" bindtap="onSearch">搜索</button>
  </view>

  <!-- 搜索建议 -->
  <view class="suggestions-section" wx:if="{{showSuggestions && suggestions.length > 0}}">
    <view class="suggestions-header">
      <text class="suggestions-title">搜索建议</text>
    </view>
    <view class="suggestions-list">
      <view class="suggestion-item" 
            wx:for="{{suggestions}}" 
            wx:key="*this"
            bindtap="onSelectSuggestion" 
            data-keyword="{{item}}">
        <text class="suggestion-icon">🔍</text>
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="history-section" wx:if="{{showHistory && searchHistory.length > 0}}">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <text class="clear-history" bindtap="onClearHistory">清空</text>
    </view>
    <view class="history-list">
      <view class="history-item" 
            wx:for="{{searchHistory}}" 
            wx:key="*this"
            bindtap="onSelectHistory" 
            data-keyword="{{item}}">
        <text class="history-icon">🕒</text>
        <text class="history-text">{{item}}</text>
        <text class="delete-btn" bindtap="onDeleteHistory" data-keyword="{{item}}" data-index="{{index}}">×</text>
      </view>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="hot-search-section" wx:if="{{!isSearching && !showSuggestions}}">
    <view class="hot-search-header">
      <text class="hot-search-title">🔥 热门搜索</text>
    </view>
    <view class="hot-search-tags">
      <text class="hot-tag" 
            wx:for="{{hotSearches}}" 
            wx:key="*this"
            bindtap="onSelectHotSearch" 
            data-keyword="{{item}}">{{item}}</text>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="results-section" wx:if="{{isSearching}}">
    <view class="results-header">
      <text class="results-title">搜索结果</text>
      <text class="results-count">共找到{{totalResults}}条结果</text>
    </view>

    <!-- 分类筛选 -->
    <view class="filter-tabs">
      <text class="filter-tab {{activeFilter === item.key ? 'active' : ''}}" 
            wx:for="{{filterTypes}}" 
            wx:key="key"
            bindtap="onSelectFilter" 
            data-filter="{{item.key}}">{{item.name}} ({{item.count}})</text>
    </view>

    <!-- 搜索结果列表 -->
    <view class="results-list">
      <!-- 文章结果 -->
      <view class="result-group" wx:if="{{filteredResults.articles.length > 0}}">
        <view class="group-header">
          <text class="group-icon">📄</text>
          <text class="group-title">相关文章</text>
        </view>
        <view class="article-results">
          <view class="article-item" 
                wx:for="{{filteredResults.articles}}" 
                wx:key="id"
                bindtap="onViewArticle" 
                data-article="{{item}}">
            <view class="article-content">
              <text class="article-title">{{item.title}}</text>
              <text class="article-summary">{{item.summary}}</text>
              <view class="article-meta">
                <text class="category-tag">{{item.categoryName}}</text>
                <text class="meta-text">{{item.views}}次浏览</text>
              </view>
            </view>
            <text class="article-arrow">></text>
          </view>
        </view>
      </view>

      <!-- FAQ结果 -->
      <view class="result-group" wx:if="{{filteredResults.questions.length > 0}}">
        <view class="group-header">
          <text class="group-icon">❓</text>
          <text class="group-title">常见问题</text>
        </view>
        <view class="question-results">
          <view class="question-item" 
                wx:for="{{filteredResults.questions}}" 
                wx:key="id"
                bindtap="onToggleQuestion" 
                data-index="{{index}}">
            <view class="question-header">
              <text class="question-text">{{item.question}}</text>
              <text class="expand-icon {{item.expanded ? 'expanded' : ''}}">{{item.expanded ? '▲' : '▼'}}</text>
            </view>
            <view wx:if="{{item.expanded}}" class="question-answer">
              <text class="answer-text">{{item.answer}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 分类结果 */
      <view class="result-group" wx:if="{{filteredResults.categories.length > 0}}">
        <view class="group-header">
          <text class="group-icon">📂</text>
          <text class="group-title">相关分类</text>
        </view>
        <view class="category-results">
          <view class="category-item" 
                wx:for="{{filteredResults.categories}}" 
                wx:key="id"
                bindtap="onViewCategory" 
                data-category="{{item}}">
            <text class="category-icon">{{item.icon}}</text>
            <view class="category-content">
              <text class="category-title">{{item.title}}</text>
              <text class="category-desc">{{item.description}}</text>
            </view>
            <text class="category-arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 无结果状态 -->
    <view class="no-results" wx:if="{{totalResults === 0}}">
      <text class="no-results-icon">😕</text>
      <text class="no-results-text">没有找到相关内容</text>
      <text class="no-results-tips">试试以下方法：</text>
      <view class="suggestions-tips">
        <text class="tip-item">• 检查关键词拼写</text>
        <text class="tip-item">• 使用更简单的关键词</text>
        <text class="tip-item">• 尝试相关的同义词</text>
      </view>
      <view class="no-results-actions">
        <button class="feedback-btn" bindtap="onFeedback">
          <text class="btn-icon">💬</text>
          <text class="btn-text">提交反馈</text>
        </button>
        <button class="contact-btn" bindtap="onContact">
          <text class="btn-icon">🎧</text>
          <text class="btn-text">联系客服</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 快速入口 -->
  <view class="quick-access" wx:if="{{!isSearching}}">
    <view class="quick-access-header">
      <text class="quick-access-title">快速入口</text>
    </view>
    <view class="quick-access-grid">
      <view class="quick-item" 
            wx:for="{{quickAccess}}" 
            wx:key="id"
            bindtap="onQuickAccess" 
            data-action="{{item.action}}">
        <text class="quick-icon">{{item.icon}}</text>
        <text class="quick-title">{{item.title}}</text>
      </view>
    </view>
  </view>
</view>