/* pages/management/staff/staff.wxss */

.staff-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border-radius: 12rpx;
  border: 2rpx solid #e0e8ff;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #0066CC;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 搜索筛选栏 */
.search-filter-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 0 30rpx;
  border: 2rpx solid #e9ecef;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.filter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx 25rpx;
  background: #0066CC;
  color: white;
  border-radius: 25rpx;
  font-size: 26rpx;
  gap: 8rpx;
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(0) invert(1);
}

/* 功能按钮 */
.action-buttons {
  margin: 0 30rpx 20rpx;
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 15rpx;
  font-size: 30rpx;
  font-weight: 500;
  gap: 12rpx;
  box-shadow: 0 4rpx 15rpx rgba(40, 167, 69, 0.3);
}

.add-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
}

.add-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* 员工列表 */
.staff-list {
  margin-top: 20rpx;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  position: relative;
}

.staff-item:last-child {
  border-bottom: none;
}

.staff-item:active {
  background: #f8f9fa;
}

.staff-avatar {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 25rpx;
  flex-shrink: 0;
}

.staff-avatar image {
  width: 100%;
  height: 100%;
  background: #e9ecef;
}

.status-dot {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx;
  border: 3rpx solid white;
}

.status-dot.active {
  background: #28a745;
}

.status-dot.inactive {
  background: #6c757d;
}

.status-dot.suspended {
  background: #ffc107;
}

.staff-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.staff-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.staff-meta {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.role-tag {
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 22rpx;
  color: white;
  font-weight: 500;
}

.role-tag.admin {
  background: #dc3545;
}

.role-tag.manager {
  background: #fd7e14;
}

.role-tag.user {
  background: #6c757d;
}

.department {
  font-size: 26rpx;
  color: #666;
}

.staff-contact {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.phone {
  font-size: 24rpx;
  color: #666;
}

.last-active {
  font-size: 22rpx;
  color: #999;
}

.staff-actions {
  display: flex;
  gap: 15rpx;
  margin-left: 20rpx;
}

.action-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button.edit {
  background: #17a2b8;
}

.action-button.delete {
  background: #dc3545;
}

.action-button image {
  width: 30rpx;
  height: 30rpx;
  filter: brightness(0) invert(1);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.3;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 筛选弹窗内容 */
.filter-content {
  padding: 40rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 25rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

.filter-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.reset-button {
  flex: 1;
  padding: 25rpx;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.confirm-button {
  flex: 1;
  padding: 25rpx;
  background: #0066CC;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

/* 添加员工表单 */
.add-staff-form {
  padding: 40rpx;
}

.form-section {
  margin-bottom: 40rpx;
}

.form-field {
  margin-bottom: 30rpx;
}

.field-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.field-input {
  width: 100%;
  padding: 20rpx 25rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.field-input.error {
  border-color: #dc3545;
  background: #fff5f5;
}

.error-text {
  display: block;
  font-size: 24rpx;
  color: #dc3545;
  margin-top: 8rpx;
}

.form-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-button {
  flex: 1;
  padding: 25rpx;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.submit-button {
  flex: 1;
  padding: 25rpx;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .stats-grid {
    gap: 15rpx;
  }
  
  .stat-item {
    padding: 15rpx;
  }
  
  .stat-value {
    font-size: 30rpx;
  }
  
  .staff-avatar {
    width: 80rpx;
    height: 80rpx;
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  .staff-container {
    background-color: #1a1a1a;
  }
  
  .stat-item {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
    border-color: #404040;
  }
  
  .stat-value {
    color: #66b3ff;
  }
  
  .stat-label {
    color: #ccc;
  }
  
  .search-wrapper {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .search-input {
    color: #fff;
  }
  
  .staff-name {
    color: #fff;
  }
  
  .department {
    color: #ccc;
  }
  
  .phone {
    color: #ccc;
  }
  
  .field-input {
    background: #2a2a2a;
    border-color: #404040;
    color: #fff;
  }
  
  .picker-display {
    background: #2a2a2a;
    border-color: #404040;
    color: #fff;
  }
}