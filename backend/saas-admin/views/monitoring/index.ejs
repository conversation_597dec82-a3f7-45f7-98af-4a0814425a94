<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-content {
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <%- include('../partials/navbar') %>

    <div class="container-fluid">
        <div class="row">
            <%- include('../partials/sidebar') %>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="main-content">
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h2">
                                <i class="bi-activity me-2"></i>系统监控
                            </h1>
                            <button type="button" class="btn btn-primary">
                                <i class="bi bi-plus"></i> 新增
                            </button>
                        </div>
                    </div>
                    
                    <div class="data-card">
                        <% if (typeof monitoringData !== 'undefined' && monitoringData.length > 0) { %>
                            <div class="row">
                                <% monitoringData.forEach(function(item, index) { %>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h5 class="card-title">
                                                    <% if (item.name) { %>
                                                        <%= item.name %>
                                                    <% } else if (item.title) { %>
                                                        <%= item.title %>
                                                    <% } else { %>
                                                        项目 <%= index + 1 %>
                                                    <% } %>
                                                </h5>
                                                <p class="card-text">
                                                    <% if (item.description) { %>
                                                        <%= item.description %>
                                                    <% } else { %>
                                                        详细信息...
                                                    <% } %>
                                                </p>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-primary">查看</button>
                                                    <button type="button" class="btn btn-outline-secondary">编辑</button>
                                                    <button type="button" class="btn btn-outline-danger">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <% }) %>
                            </div>
                        <% } else { %>
                            <div class="text-center py-5">
                                <i class="bi-activity display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">暂无数据</h4>
                                <p class="text-muted">点击上方"新增"按钮来添加系统监控数据</p>
                            </div>
                        <% } %>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>