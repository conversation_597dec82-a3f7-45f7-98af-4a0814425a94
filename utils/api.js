/**
 * API接口管理（兼容层）
 * - 统一走 constants/api.constants.js + request.smartRequest
 * - 保留原有导出结构，内部改用新常量与智能请求
 */

const request = require('./request.js');
const { API: API_CONST } = require('../constants/index.js');

// 适配函数：将完整URL映射为相对路径供 request 使用
function toRelative(url) {
  try {
    const base = API_CONST.BASE_URL; // 如 http://localhost:3001
    return url.replace(base, '');
  } catch (e) {
    return url;
  }
}

// 根据常量重新组织模块化API（仅保留少量常用端点示例；其余页面建议直接使用 constants + request）
const auth = {
  login: (data) => request.post(toRelative(API_CONST.ENDPOINTS.AUTH.LOGIN), data),
  logout: () => request.post(toRelative(API_CONST.ENDPOINTS.AUTH.LOGOUT)),
  getUserInfo: () => request.get(toRelative(API_CONST.ENDPOINTS.AUTH.USER_INFO)),
  changePassword: (data) => request.put(toRelative(API_CONST.ENDPOINTS.AUTH.CHANGE_PASSWORD), data)
};

const home = {
  getHomeData: () => request.get(toRelative(API_CONST.ENDPOINTS.HOME.DATA)),
  getAnnouncements: (params) => request.get(toRelative(API_CONST.ENDPOINTS.HOME.ANNOUNCEMENTS), params)
};

const health = {
  getRecords: (params) => request.get(toRelative(API_CONST.ENDPOINTS.HEALTH.RECORDS), params),
  getRecordDetail: (id) => request.get(toRelative(API_CONST.ENDPOINTS.HEALTH.RECORD_DETAIL(id))),
  createRecord: (data) => request.post(toRelative(API_CONST.ENDPOINTS.HEALTH.CREATE_RECORD), data),
  updateRecord: (id, data) => request.put(toRelative(API_CONST.ENDPOINTS.HEALTH.UPDATE_RECORD(id)), data),
  deleteRecord: (id) => request.del(toRelative(API_CONST.ENDPOINTS.HEALTH.DELETE_RECORD(id))),
  aiDiagnosis: (data) => request.post(toRelative(API_CONST.ENDPOINTS.HEALTH.AI_DIAGNOSIS), data),
  getKnowledgeList: (params) => request.get(toRelative(API_CONST.ENDPOINTS.HEALTH.KNOWLEDGE), params),
  getKnowledgeDetail: (id) => request.get(toRelative(API_CONST.ENDPOINTS.HEALTH.RECORD_DETAIL(id))),
  getReport: (params) => request.get(toRelative(API_CONST.ENDPOINTS.HEALTH.REPORT), params)
};

const production = {
  getEnvironmentData: (params) => request.get(toRelative(API_CONST.ENDPOINTS.PRODUCTION.ENVIRONMENT), params),
  getFinanceData: (params) => request.get(toRelative(API_CONST.ENDPOINTS.PRODUCTION.FINANCE), params),
  createPurchaseRequest: (data) => request.post(toRelative(API_CONST.ENDPOINTS.PRODUCTION.PURCHASE_REQUESTS), data),
  createReimbursementRequest: (data) => request.post(toRelative(API_CONST.ENDPOINTS.PRODUCTION.REIMBURSEMENT_REQUESTS), data),
  saveAIInventoryRecord: (data) => request.post(toRelative(API_CONST.ENDPOINTS.PRODUCTION.AI_INVENTORY), data),
  getAIInventoryRecords: (params) => request.get(toRelative(API_CONST.ENDPOINTS.PRODUCTION.AI_INVENTORY), params)
};

const profile = {
  getSettings: () => request.get(toRelative(API_CONST.ENDPOINTS.PROFILE.SETTINGS)),
  updateSettings: (data) => request.put(toRelative(API_CONST.ENDPOINTS.PROFILE.SETTINGS), data),
  getHelpList: () => request.get(toRelative(API_CONST.ENDPOINTS.PROFILE.HELP))
};

module.exports = {
  API_ENDPOINTS: API_CONST.ENDPOINTS,
  auth,
  home,
  health,
  production,
  profile,
  API: API_CONST.ENDPOINTS
};