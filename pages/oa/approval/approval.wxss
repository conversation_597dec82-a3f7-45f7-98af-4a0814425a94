@import '/styles/oa-common.wxss';
/* pages/oa/approval/approval.wxss */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: #f2f2f7;
  padding-bottom: 120rpx;
}

/* ==================== 统计卡片区域 ==================== */
.stats-container {
  padding: 24rpx 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.stat-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.stat-card.primary {
  border-left: 6rpx solid #007aff;
}

.stat-card.warning {
  border-left: 6rpx solid #ff9500;
}

.stat-card.success {
  border-left: 6rpx solid #34c759;
}

.stat-card.info {
  border-left: 6rpx solid #5ac8fa;
}

.stat-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d1d1f;
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #8e8e93;
  font-weight: 400;
}

.urgent-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff3b30;
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}

/* ==================== 功能入口区域 ==================== */
.function-container {
  margin: 0 32rpx 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.function-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 24rpx;
}

.function-grid {
  display: flex;
  justify-content: space-around;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 16rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.function-item:active {
  background: #f2f2f7;
  transform: scale(0.95);
}

.function-icon {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.2);
}

.icon-text {
  font-size: 28rpx;
}

.function-text {
  font-size: 24rpx;
  color: #1d1d1f;
  font-weight: 500;
  text-align: center;
}

.function-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #ff3b30;
  color: white;
  font-size: 18rpx;
  font-weight: 600;
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

/* ==================== 最近审批动态 ==================== */
.recent-container {
  margin: 0 32rpx 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 24rpx 16rpx;
}

.recent-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
}

.recent-more {
  font-size: 26rpx;
  color: #007aff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: rgba(0, 122, 255, 0.1);
}

.recent-list {
  padding: 0 24rpx 16rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f2f2f7;
  transition: all 0.2s ease;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-item:active {
  background: #f8f9fa;
  border-radius: 8rpx;
}

.recent-item-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.approval-status {
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.approval-status.pending {
  background: rgba(255, 149, 0, 0.1);
  color: #ff9500;
}

.approval-status.approved {
  background: rgba(52, 199, 89, 0.1);
  color: #34c759;
}

.approval-status.rejected {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

.approval-info {
  flex: 1;
  min-width: 0;
}

.approval-title {
  font-size: 28rpx;
  color: #1d1d1f;
  font-weight: 500;
  display: block;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.approval-meta {
  font-size: 24rpx;
  color: #8e8e93;
}

.recent-item-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.urgent-tag {
  background: #ff3b30;
  color: white;
  font-size: 20rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
}

.arrow {
  font-size: 24rpx;
  color: #c7c7cc;
}

/* ==================== 空状态 ==================== */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  margin: 32rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #1d1d1f;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #8e8e93;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #8e8e93;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .stat-card {
    padding: 20rpx;
  }

  .stat-number {
    font-size: 32rpx;
  }

  .function-grid {
    flex-direction: column;
    gap: 24rpx;
  }

  .function-item {
    flex-direction: row;
    justify-content: flex-start;
    min-width: auto;
  }

  .function-icon {
    margin-right: 16rpx;
    margin-bottom: 0;
  }
}

/* ==================== 小程序特有样式 ==================== */
button::after {
  border: none;
}

.recent-more:active,
.function-item:active {
  opacity: 0.6;
}

/* 确保卡片在小程序中的触摸反馈 */
.stat-card:hover,
.function-item:hover,
.recent-item:hover {
  opacity: 0.8;
}