// components/tab-bar/tab-bar.js
const { UI } = require('../../constants/index.js');

Component({
  properties: {
    // Tab数据
    tabs: {
      type: Array,
      value: []
    },
    // 当前激活的Tab索引
    current: {
      type: Number,
      value: 0
    },
    // Tab容器样式
    containerClass: {
      type: String,
      value: ''
    },
    // Tab项样式
    tabClass: {
      type: String,
      value: ''
    },
    // 激活Tab项样式
    activeTabClass: {
      type: String,
      value: ''
    },
    // 是否显示底部指示器
    showIndicator: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // UI常量
    colors: UI.COLORS
  },

  methods: {
    // 切换Tab
    onTabTap(e) {
      const index = e.currentTarget.dataset.index;
      if (index !== this.data.current) {
        this.triggerEvent('change', {
          index: index,
          tab: this.data.tabs[index]
        });
      }
    }
  }
});