/**
 * 统一数据库模型定义
 * Unified Database Models
 * 
 * 解决模型重复和字段命名不一致问题
 * 建立统一的数据库模型规范
 */

const { DataTypes, Sequelize } = require('sequelize');

/**
 * 模型定义工厂
 * 统一创建和管理所有数据库模型
 */
class UnifiedModelFactory {
  constructor(sequelize) {
    this.sequelize = sequelize;
    this.models = {};
  }

  /**
   * 初始化所有模型
   */
  async initializeModels() {
    // 基础模型
    this.defineUserModel();
    this.defineFlockModel();
    
    // 核心业务模型
    this.defineHealthRecordModel();
    this.defineProductionRecordModel();
    this.defineUnifiedInventoryModel(); // 统一库存模型
    
    // OA系统模型
    this.defineTaskModel();
    this.defineApprovalModel();
    this.defineReimbursementModel();
    
    // 商城模型
    this.defineProductModel();
    this.defineOrderModel();
    
    // AI服务模型
    this.defineAiConfigModel();
    this.defineAiUsageStatsModel();
    
    // 系统模型
    this.defineAnnouncementModel();
    this.defineAuditLogModel();

    // 建立模型关联
    this.defineAssociations();

    return this.models;
  }

  /**
   * 用户模型
   * 标准化字段命名，统一用户管理
   */
  defineUserModel() {
    this.models.User = this.sequelize.define('User', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '用户ID'
      },
      username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '用户名'
      },
      password: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '密码'
      },
      name: {
        type: DataTypes.STRING(100),
        comment: '真实姓名'
      },
      farm_name: {
        type: DataTypes.STRING(100),
        comment: '养殖场名称',
        field: 'farm_name'
      },
      phone: {
        type: DataTypes.STRING(20),
        comment: '手机号'
      },
      email: {
        type: DataTypes.STRING(100),
        unique: true,
        comment: '邮箱'
      },
      role: {
        type: DataTypes.ENUM('platform_super_admin', 'platform_admin', 'platform_operator', 'platform_support', 'owner', 'admin', 'manager', 'finance', 'hr', 'user'),
        defaultValue: 'user',
        comment: '用户角色'
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'suspended'),
        defaultValue: 'active',
        comment: '用户状态'
      },
      avatar: {
        type: DataTypes.STRING(500),
        comment: '头像URL'
      },
      last_login_at: {
        type: DataTypes.DATE,
        comment: '最后登录时间',
        field: 'last_login_at'
      },
      permissions: {
        type: DataTypes.JSON,
        comment: '自定义权限列表'
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
        field: 'updated_at'
      }
    }, {
      tableName: 'users',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: '用户表',
      indexes: [
        { fields: ['username'] },
        { fields: ['email'] },
        { fields: ['role'] },
        { fields: ['status'] },
        { fields: ['phone'] }
      ]
    });
  }

  /**
   * 鹅群模型
   * 统一鹅群管理数据结构
   */
  defineFlockModel() {
    this.models.Flock = this.sequelize.define('Flock', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '鹅群ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        field: 'user_id'
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '鹅群名称'
      },
      description: {
        type: DataTypes.TEXT,
        comment: '鹅群描述'
      },
      breed: {
        type: DataTypes.STRING(50),
        comment: '品种'
      },
      initial_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '初始数量',
        field: 'initial_count'
      },
      current_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '当前数量',
        field: 'current_count'
      },
      birth_date: {
        type: DataTypes.DATE,
        comment: '出生日期',
        field: 'birth_date'
      },
      location: {
        type: DataTypes.STRING(200),
        comment: '养殖位置'
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'sold', 'deceased'),
        defaultValue: 'active',
        comment: '鹅群状态'
      },
      notes: {
        type: DataTypes.TEXT,
        comment: '备注信息'
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
        field: 'updated_at'
      }
    }, {
      tableName: 'flocks',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: '鹅群表',
      indexes: [
        { fields: ['user_id'] },
        { fields: ['name'] },
        { fields: ['status'] },
        { fields: ['breed'] }
      ]
    });
  }

  /**
   * 统一库存管理模型
   * 合并 material、inventory、unified-inventory 三个模型
   */
  defineUnifiedInventoryModel() {
    this.models.UnifiedInventory = this.sequelize.define('UnifiedInventory', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '库存ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        field: 'user_id'
      },
      flock_id: {
        type: DataTypes.INTEGER,
        comment: '关联鹅群ID',
        field: 'flock_id'
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '物料名称'
      },
      item_name: {
        type: DataTypes.STRING(100),
        comment: '物品名称（别名）',
        field: 'item_name'
      },
      category: {
        type: DataTypes.ENUM('feed', 'medicine', 'equipment', 'materials', 'other'),
        allowNull: false,
        comment: '物料类别'
      },
      specification: {
        type: DataTypes.STRING(100),
        comment: '规格型号'
      },
      unit: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '个',
        comment: '计量单位'
      },
      
      // 库存数量管理
      current_stock: {
        type: DataTypes.DECIMAL(12, 3),
        defaultValue: 0,
        comment: '当前库存',
        field: 'current_stock'
      },
      min_stock: {
        type: DataTypes.DECIMAL(12, 3),
        defaultValue: 0,
        comment: '最低库存警戒线',
        field: 'min_stock'
      },
      max_stock: {
        type: DataTypes.DECIMAL(12, 3),
        comment: '最高库存',
        field: 'max_stock'
      },
      quantity: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '数量（整数型，兼容旧数据）'
      },
      
      // 价格管理
      unit_price: {
        type: DataTypes.DECIMAL(10, 2),
        comment: '单价',
        field: 'unit_price'
      },
      total_value: {
        type: DataTypes.DECIMAL(15, 2),
        comment: '总价值',
        field: 'total_value'
      },
      
      // 供应商信息
      supplier: {
        type: DataTypes.STRING(100),
        comment: '供应商'
      },
      supplier_contact: {
        type: DataTypes.STRING(100),
        comment: '供应商联系方式',
        field: 'supplier_contact'
      },
      
      // 日期管理
      purchase_date: {
        type: DataTypes.DATE,
        comment: '采购日期',
        field: 'purchase_date'
      },
      expiry_date: {
        type: DataTypes.DATE,
        comment: '过期日期',
        field: 'expiry_date'
      },
      last_update_date: {
        type: DataTypes.DATE,
        comment: '最后更新日期',
        field: 'last_update_date'
      },
      
      // 状态和位置
      status: {
        type: DataTypes.ENUM('normal', 'low_stock', 'out_of_stock', 'expired', 'warning', 'inactive'),
        defaultValue: 'normal',
        comment: '库存状态'
      },
      location: {
        type: DataTypes.STRING(100),
        comment: '存放位置'
      },
      
      // 其他信息
      description: {
        type: DataTypes.TEXT,
        comment: '描述信息'
      },
      batch_number: {
        type: DataTypes.STRING(50),
        comment: '批次号',
        field: 'batch_number'
      },
      
      // 审计字段
      created_by: {
        type: DataTypes.INTEGER,
        comment: '创建者ID',
        field: 'created_by'
      },
      updated_by: {
        type: DataTypes.INTEGER,
        comment: '更新者ID',
        field: 'updated_by'
      },
      
      // 时间戳
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
        field: 'updated_at'
      }
    }, {
      tableName: 'unified_inventory',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: '统一库存管理表',
      indexes: [
        { fields: ['user_id'] },
        { fields: ['flock_id'] },
        { fields: ['category'] },
        { fields: ['status'] },
        { fields: ['name'] },
        { fields: ['expiry_date'] },
        { fields: ['purchase_date'] },
        { fields: ['current_stock'] },
        { fields: ['user_id', 'category'] },
        { fields: ['user_id', 'status'] },
        { fields: ['user_id', 'current_stock', 'min_stock'] },
        { fields: ['status', 'expiry_date'] },
        { fields: ['name', 'category'] }
      ]
    });
  }

  /**
   * 健康记录模型
   * 规范化字段命名
   */
  defineHealthRecordModel() {
    this.models.HealthRecord = this.sequelize.define('HealthRecord', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '健康记录ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        field: 'user_id'
      },
      flock_id: {
        type: DataTypes.INTEGER,
        comment: '鹅群ID',
        field: 'flock_id'
      },
      goose_id: {
        type: DataTypes.STRING(50),
        comment: '鹅只ID',
        field: 'goose_id'
      },
      symptoms: {
        type: DataTypes.TEXT,
        comment: '症状描述'
      },
      diagnosis: {
        type: DataTypes.TEXT,
        comment: '诊断结果'
      },
      treatment: {
        type: DataTypes.TEXT,
        comment: '治疗方案'
      },
      status: {
        type: DataTypes.ENUM('pending', 'processing', 'completed', 'cancelled'),
        defaultValue: 'pending',
        comment: '处理状态'
      },
      severity: {
        type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
        defaultValue: 'low',
        comment: '严重程度'
      },
      veterinarian: {
        type: DataTypes.STRING(100),
        comment: '兽医姓名'
      },
      treatment_cost: {
        type: DataTypes.DECIMAL(10, 2),
        comment: '治疗费用',
        field: 'treatment_cost'
      },
      follow_up_date: {
        type: DataTypes.DATE,
        comment: '复查日期',
        field: 'follow_up_date'
      },
      notes: {
        type: DataTypes.TEXT,
        comment: '备注信息'
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
        field: 'updated_at'
      }
    }, {
      tableName: 'health_records',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: '健康记录表',
      indexes: [
        { fields: ['user_id'] },
        { fields: ['flock_id'] },
        { fields: ['goose_id'] },
        { fields: ['status'] },
        { fields: ['severity'] },
        { fields: ['created_at'] }
      ]
    });
  }

  /**
   * 生产记录模型
   * 标准化生产数据管理
   */
  defineProductionRecordModel() {
    this.models.ProductionRecord = this.sequelize.define('ProductionRecord', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '生产记录ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        field: 'user_id'
      },
      flock_id: {
        type: DataTypes.INTEGER,
        comment: '鹅群ID',
        field: 'flock_id'
      },
      record_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '记录日期',
        field: 'record_date'
      },
      
      // 产蛋数据
      egg_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '产蛋数量',
        field: 'egg_count'
      },
      egg_weight: {
        type: DataTypes.DECIMAL(8, 2),
        comment: '鹅蛋总重量(kg)',
        field: 'egg_weight'
      },
      
      // 饲料消耗
      feed_consumption: {
        type: DataTypes.DECIMAL(10, 2),
        defaultValue: 0,
        comment: '饲料消耗量(kg)',
        field: 'feed_consumption'
      },
      feed_type: {
        type: DataTypes.STRING(100),
        comment: '饲料类型',
        field: 'feed_type'
      },
      
      // 环境数据
      temperature: {
        type: DataTypes.DECIMAL(5, 2),
        comment: '温度(℃)'
      },
      humidity: {
        type: DataTypes.DECIMAL(5, 2),
        comment: '湿度(%)'
      },
      
      // 健康状态
      mortality_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '死亡数量',
        field: 'mortality_count'
      },
      sick_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '生病数量',
        field: 'sick_count'
      },
      
      // 体重监测
      average_weight: {
        type: DataTypes.DECIMAL(8, 2),
        comment: '平均体重(kg)',
        field: 'average_weight'
      },
      weight_gain: {
        type: DataTypes.DECIMAL(8, 2),
        comment: '增重(kg)',
        field: 'weight_gain'
      },
      
      // 其他信息
      notes: {
        type: DataTypes.TEXT,
        comment: '备注信息'
      },
      weather: {
        type: DataTypes.STRING(50),
        comment: '天气情况'
      },
      recorded_by: {
        type: DataTypes.INTEGER,
        comment: '记录人ID',
        field: 'recorded_by'
      },
      
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
        field: 'updated_at'
      }
    }, {
      tableName: 'production_records',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: '生产记录表',
      indexes: [
        { fields: ['user_id'] },
        { fields: ['flock_id'] },
        { fields: ['record_date'] },
        { fields: ['user_id', 'record_date'] },
        { fields: ['created_at'] }
      ]
    });
  }

  /**
   * AI配置模型
   * 标准化AI服务配置
   */
  defineAiConfigModel() {
    this.models.AiConfig = this.sequelize.define('AiConfig', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: 'AI配置ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        comment: '用户ID（null表示全局配置）',
        field: 'user_id'
      },
      service_type: {
        type: DataTypes.ENUM('chat', 'diagnosis', 'analysis', 'recommendation'),
        allowNull: false,
        comment: 'AI服务类型',
        field: 'service_type'
      },
      provider: {
        type: DataTypes.ENUM('openai', 'claude', 'baidu', 'aliyun', 'custom'),
        allowNull: false,
        comment: 'AI服务提供商'
      },
      model_name: {
        type: DataTypes.STRING(100),
        comment: '模型名称',
        field: 'model_name'
      },
      api_key: {
        type: DataTypes.STRING(500),
        comment: 'API密钥（加密存储）',
        field: 'api_key'
      },
      api_url: {
        type: DataTypes.STRING(500),
        comment: 'API地址',
        field: 'api_url'
      },
      config_data: {
        type: DataTypes.JSON,
        comment: '配置数据',
        field: 'config_data'
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'testing'),
        defaultValue: 'active',
        comment: '配置状态'
      },
      is_default: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否为默认配置',
        field: 'is_default'
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
        field: 'updated_at'
      }
    }, {
      tableName: 'ai_configs',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: 'AI配置表',
      indexes: [
        { fields: ['user_id'] },
        { fields: ['service_type'] },
        { fields: ['provider'] },
        { fields: ['status'] },
        { fields: ['is_default'] }
      ]
    });
  }

  /**
   * 任务模型
   * OA系统任务管理
   */
  defineTaskModel() {
    this.models.Task = this.sequelize.define('Task', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '任务ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '创建者ID',
        field: 'user_id'
      },
      assigned_to: {
        type: DataTypes.INTEGER,
        comment: '分配给用户ID',
        field: 'assigned_to'
      },
      title: {
        type: DataTypes.STRING(200),
        allowNull: false,
        comment: '任务标题'
      },
      description: {
        type: DataTypes.TEXT,
        comment: '任务描述'
      },
      priority: {
        type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
        defaultValue: 'medium',
        comment: '优先级'
      },
      status: {
        type: DataTypes.ENUM('pending', 'in_progress', 'completed', 'cancelled'),
        defaultValue: 'pending',
        comment: '任务状态'
      },
      category: {
        type: DataTypes.STRING(50),
        comment: '任务分类'
      },
      due_date: {
        type: DataTypes.DATE,
        comment: '截止日期',
        field: 'due_date'
      },
      completed_at: {
        type: DataTypes.DATE,
        comment: '完成时间',
        field: 'completed_at'
      },
      progress: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '完成进度(0-100)'
      },
      attachments: {
        type: DataTypes.JSON,
        comment: '附件列表'
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
        field: 'updated_at'
      }
    }, {
      tableName: 'tasks',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: '任务表',
      indexes: [
        { fields: ['user_id'] },
        { fields: ['assigned_to'] },
        { fields: ['status'] },
        { fields: ['priority'] },
        { fields: ['due_date'] }
      ]
    });
  }

  /**
   * 审计日志模型
   * 系统操作审计
   */
  defineAuditLogModel() {
    this.models.AuditLog = this.sequelize.define('AuditLog', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '审计日志ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        comment: '操作用户ID',
        field: 'user_id'
      },
      action: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '操作动作'
      },
      resource_type: {
        type: DataTypes.STRING(50),
        comment: '资源类型',
        field: 'resource_type'
      },
      resource_id: {
        type: DataTypes.STRING(50),
        comment: '资源ID',
        field: 'resource_id'
      },
      details: {
        type: DataTypes.JSON,
        comment: '操作详情'
      },
      ip_address: {
        type: DataTypes.STRING(45),
        comment: 'IP地址',
        field: 'ip_address'
      },
      user_agent: {
        type: DataTypes.TEXT,
        comment: '用户代理',
        field: 'user_agent'
      },
      status: {
        type: DataTypes.ENUM('success', 'failed', 'error'),
        comment: '操作状态'
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
        field: 'created_at'
      }
    }, {
      tableName: 'audit_logs',
      timestamps: false, // 审计日志不需要updated_at
      comment: '审计日志表',
      indexes: [
        { fields: ['user_id'] },
        { fields: ['action'] },
        { fields: ['resource_type'] },
        { fields: ['created_at'] },
        { fields: ['status'] }
      ]
    });
  }

  // 其他模型定义方法...
  defineApprovalModel() { /* 审批模型 */ }
  defineReimbursementModel() { /* 报销模型 */ }
  defineProductModel() { /* 商品模型 */ }
  defineOrderModel() { /* 订单模型 */ }
  defineAiUsageStatsModel() { /* AI使用统计模型 */ }
  defineAnnouncementModel() { /* 公告模型 */ }

  /**
   * 定义模型关联关系
   */
  defineAssociations() {
    const { User, Flock, HealthRecord, ProductionRecord, UnifiedInventory, Task, AuditLog } = this.models;

    // 用户与鹅群关联
    User.hasMany(Flock, { foreignKey: 'user_id', as: 'flocks' });
    Flock.belongsTo(User, { foreignKey: 'user_id', as: 'owner' });

    // 用户与健康记录关联
    User.hasMany(HealthRecord, { foreignKey: 'user_id', as: 'healthRecords' });
    HealthRecord.belongsTo(User, { foreignKey: 'user_id', as: 'owner' });

    // 鹅群与健康记录关联
    Flock.hasMany(HealthRecord, { foreignKey: 'flock_id', as: 'healthRecords' });
    HealthRecord.belongsTo(Flock, { foreignKey: 'flock_id', as: 'flock' });

    // 用户与生产记录关联
    User.hasMany(ProductionRecord, { foreignKey: 'user_id', as: 'productionRecords' });
    ProductionRecord.belongsTo(User, { foreignKey: 'user_id', as: 'owner' });

    // 鹅群与生产记录关联
    Flock.hasMany(ProductionRecord, { foreignKey: 'flock_id', as: 'productionRecords' });
    ProductionRecord.belongsTo(Flock, { foreignKey: 'flock_id', as: 'flock' });

    // 用户与库存关联
    User.hasMany(UnifiedInventory, { foreignKey: 'user_id', as: 'inventory' });
    UnifiedInventory.belongsTo(User, { foreignKey: 'user_id', as: 'owner' });

    // 鹅群与库存关联（可选）
    Flock.hasMany(UnifiedInventory, { foreignKey: 'flock_id', as: 'inventory' });
    UnifiedInventory.belongsTo(Flock, { foreignKey: 'flock_id', as: 'flock' });

    // 用户与任务关联
    User.hasMany(Task, { foreignKey: 'user_id', as: 'createdTasks' });
    User.hasMany(Task, { foreignKey: 'assigned_to', as: 'assignedTasks' });
    Task.belongsTo(User, { foreignKey: 'user_id', as: 'creator' });
    Task.belongsTo(User, { foreignKey: 'assigned_to', as: 'assignee' });

    // 用户与审计日志关联
    User.hasMany(AuditLog, { foreignKey: 'user_id', as: 'auditLogs' });
    AuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  }

  /**
   * 获取模型实例
   * @param {string} modelName 模型名称
   * @returns {Model} 模型实例
   */
  getModel(modelName) {
    return this.models[modelName];
  }

  /**
   * 获取所有模型
   * @returns {Object} 所有模型
   */
  getAllModels() {
    return this.models;
  }
}

module.exports = {
  UnifiedModelFactory
};