/**
 * 数据验证工具类
 * 基于常量系统的验证规则，提供统一的数据验证功能
 */
const { Constants } = require('../constants/constants-manager.js');

/**
 * 验证器类
 */
class Validator {
  constructor() {
    this.rules = {};
    this.errors = {};
  }

  /**
   * 添加验证规则
   * @param {string} field 字段名
   * @param {Array} rules 规则数组
   */
  addRule(field, rules) {
    this.rules[field] = Array.isArray(rules) ? rules : [rules];
    return this;
  }

  /**
   * 验证单个字段
   * @param {string} field 字段名
   * @param {*} value 字段值
   * @param {Array} rules 验证规则
   * @returns {Array} 错误信息数组
   */
  validateField(field, value, rules) {
    const errors = [];

    for (const rule of rules) {
      const error = this.applyRule(field, value, rule);
      if (error) {
        errors.push(error);
      }
    }

    return errors;
  }

  /**
   * 应用单个验证规则
   * @param {string} field 字段名
   * @param {*} value 字段值
   * @param {Object} rule 验证规则
   * @returns {string|null} 错误信息
   */
  applyRule(field, value, rule) {
    switch (rule.type) {
      case 'required':
        return this.validateRequired(field, value, rule);
      
      case 'minLength':
      case 'maxLength':
        return this.validateLength(field, value, rule);
      
      case 'minValue':
      case 'maxValue':
        return this.validateRange(field, value, rule);
      
      case 'pattern':
        return this.validatePattern(field, value, rule);
      
      case 'email':
        return this.validateEmail(field, value, rule);
      
      case 'phone':
        return this.validatePhone(field, value, rule);
      
      case 'number':
        return this.validateNumber(field, value, rule);
      
      case 'integer':
        return this.validateInteger(field, value, rule);
      
      case 'decimal':
        return this.validateDecimal(field, value, rule);
      
      case 'url':
        return this.validateURL(field, value, rule);
      
      case 'date':
        return this.validateDate(field, value, rule);
      
      case 'custom':
        return this.validateCustom(field, value, rule);
      
      default:
        try { const logger = require('./logger.js'); logger.warn && logger.warn(`[Validator] 未知的验证类型: ${rule.type}`); } catch(_) {}
        return null;
    }
  }

  /**
   * 必填验证
   */
  validateRequired(field, value, rule) {
    const isEmpty = value === null || value === undefined || 
                   (typeof value === 'string' && value.trim() === '') ||
                   (Array.isArray(value) && value.length === 0);
    
    return isEmpty ? (rule.message || `${field}不能为空`) : null;
  }

  /**
   * 长度验证
   */
  validateLength(field, value, rule) {
    if (value === null || value === undefined) return null;
    
    const length = value.toString().length;
    
    if (rule.type === 'minLength' && length < rule.value) {
      return rule.message || `${field}长度不能少于${rule.value}位`;
    }
    
    if (rule.type === 'maxLength' && length > rule.value) {
      return rule.message || `${field}长度不能超过${rule.value}位`;
    }
    
    return null;
  }

  /**
   * 数值范围验证
   */
  validateRange(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const num = parseFloat(value);
    if (isNaN(num)) {
      return rule.message || `${field}必须是有效数字`;
    }
    
    if (rule.type === 'minValue' && num < rule.value) {
      return rule.message || `${field}不能小于${rule.value}`;
    }
    
    if (rule.type === 'maxValue' && num > rule.value) {
      return rule.message || `${field}不能大于${rule.value}`;
    }
    
    return null;
  }

  /**
   * 正则表达式验证
   */
  validatePattern(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const pattern = new RegExp(rule.value);
    return pattern.test(value) ? null : (rule.message || `${field}格式不正确`);
  }

  /**
   * 邮箱验证
   */
  validateEmail(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const emailRegex = Constants.get('BUSINESS.VALIDATION.EMAIL_REGEX');
    return emailRegex.test(value) ? null : (rule.message || `${field}格式不正确`);
  }

  /**
   * 手机号验证
   */
  validatePhone(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const phoneRegex = Constants.get('BUSINESS.VALIDATION.PHONE_REGEX');
    return phoneRegex.test(value) ? null : (rule.message || `${field}格式不正确`);
  }

  /**
   * 数字验证
   */
  validateNumber(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const num = parseFloat(value);
    return isNaN(num) ? (rule.message || `${field}必须是数字`) : null;
  }

  /**
   * 整数验证
   */
  validateInteger(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const num = parseInt(value);
    return (isNaN(num) || num.toString() !== value.toString()) ? 
           (rule.message || `${field}必须是整数`) : null;
  }

  /**
   * 小数验证
   */
  validateDecimal(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const num = parseFloat(value);
    if (isNaN(num)) {
      return rule.message || `${field}必须是有效的小数`;
    }
    
    if (rule.precision) {
      const decimalPlaces = (value.toString().split('.')[1] || '').length;
      if (decimalPlaces > rule.precision) {
        return rule.message || `${field}小数点后不能超过${rule.precision}位`;
      }
    }
    
    return null;
  }

  /**
   * URL验证
   */
  validateURL(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const urlRegex = Constants.get('BUSINESS.VALIDATION.URL_REGEX');
    return urlRegex.test(value) ? null : (rule.message || `${field}必须是有效的URL`);
  }

  /**
   * 日期验证
   */
  validateDate(field, value, rule) {
    if (value === null || value === undefined || value === '') return null;
    
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return rule.message || `${field}必须是有效的日期`;
    }
    
    if (rule.minDate && date < new Date(rule.minDate)) {
      return rule.message || `${field}不能早于${rule.minDate}`;
    }
    
    if (rule.maxDate && date > new Date(rule.maxDate)) {
      return rule.message || `${field}不能晚于${rule.maxDate}`;
    }
    
    return null;
  }

  /**
   * 自定义验证
   */
  validateCustom(field, value, rule) {
    if (typeof rule.validator !== 'function') {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[Validator] 自定义验证器必须是函数'); } catch(_) {}
      return null;
    }
    
    try {
      const result = rule.validator(value, field);
      return result === true ? null : (result || rule.message || `${field}验证失败`);
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[Validator] 自定义验证器执行失败', error); } catch(_) {}
      return rule.message || `${field}验证失败`;
    }
  }

  /**
   * 验证对象
   * @param {Object} data 要验证的数据
   * @param {Object} rules 验证规则 (可选，使用已添加的规则)
   * @returns {Object} 验证结果 {valid, errors}
   */
  validate(data, rules = null) {
    const validationRules = rules || this.rules;
    const errors = {};
    let isValid = true;

    for (const [field, fieldRules] of Object.entries(validationRules)) {
      const value = data[field];
      const fieldErrors = this.validateField(field, value, fieldRules);
      
      if (fieldErrors.length > 0) {
        errors[field] = fieldErrors;
        isValid = false;
      }
    }

    this.errors = errors;
    return {
      valid: isValid,
      errors
    };
  }

  /**
   * 获取字段错误
   * @param {string} field 字段名
   * @returns {Array} 错误数组
   */
  getFieldErrors(field) {
    return this.errors[field] || [];
  }

  /**
   * 获取第一个字段错误
   * @param {string} field 字段名
   * @returns {string|null} 错误信息
   */
  getFirstFieldError(field) {
    const errors = this.getFieldErrors(field);
    return errors.length > 0 ? errors[0] : null;
  }

  /**
   * 清除验证错误
   * @param {string} field 字段名，为空则清除所有
   */
  clearErrors(field = null) {
    if (field) {
      delete this.errors[field];
    } else {
      this.errors = {};
    }
  }

  /**
   * 重置验证器
   */
  reset() {
    this.rules = {};
    this.errors = {};
  }
}

/**
 * 快速验证函数
 */
const ValidationUtils = {
  /**
   * 验证邮箱
   * @param {string} email 邮箱地址
   * @returns {boolean} 是否有效
   */
  isValidEmail: (email) => {
    const emailRegex = Constants.get('BUSINESS.VALIDATION.EMAIL_REGEX');
    return emailRegex.test(email);
  },

  /**
   * 验证手机号
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  isValidPhone: (phone) => {
    const phoneRegex = Constants.get('BUSINESS.VALIDATION.PHONE_REGEX');
    return phoneRegex.test(phone);
  },

  /**
   * 验证密码强度
   * @param {string} password 密码
   * @returns {Object} 验证结果 {valid, strength, message}
   */
  validatePassword: (password) => {
    const passwordRegex = Constants.get('BUSINESS.VALIDATION.PASSWORD_REGEX');
    const valid = passwordRegex.test(password);
    
    let strength = 0;
    let strengthText = '弱';
    
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
    
    if (strength >= 4) strengthText = '强';
    else if (strength >= 2) strengthText = '中';
    
    return {
      valid,
      strength: strengthText,
      message: valid ? '' : '密码必须包含字母和数字，长度6-20位'
    };
  },

  /**
   * 验证数值范围
   * @param {number} value 数值
   * @param {string} rangeType 范围类型
   * @returns {boolean} 是否有效
   */
  isInRange: (value, rangeType) => {
    const ranges = Constants.get('BUSINESS.VALIDATION.NUMBER_RANGES');
    const range = ranges[rangeType];
    
    if (!range) return false;
    
    const num = parseFloat(value);
    return !isNaN(num) && num >= range.min && num <= range.max;
  },

  /**
   * 验证字符串长度
   * @param {string} str 字符串
   * @param {string} limitType 长度限制类型
   * @returns {boolean} 是否有效
   */
  isValidLength: (str, limitType) => {
    const limits = Constants.get('BUSINESS.VALIDATION.LENGTH_LIMITS');
    const limit = limits[limitType];
    
    if (!limit) return false;
    
    const length = str ? str.length : 0;
    return length >= limit.min && length <= limit.max;
  }
};

module.exports = {
  Validator,
  ValidationUtils
};