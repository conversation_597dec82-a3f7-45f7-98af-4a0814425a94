// pages/oa/purchase/apply/apply.js
const { getCurrentUserInfo } = require('../../../../utils/user-info.js');
const { isLoggedIn, getCurrentUser } = require('../../../../utils/auth-helper');

Page({
  data: {
    // 表单数据
    formData: {
      title: '',
      description: '',
      category: '',
      urgencyLevel: 'normal',
      expectedDate: '',
      businessPurpose: ''
    },
    
    // 表单配置
    categories: [
      { value: 'feed', label: '饲料用品' },
      { value: 'equipment', label: '设备器具' },
      { value: 'medicine', label: '药品疫苗' },
      { value: 'facility', label: '基础设施' },
      { value: 'office', label: '办公用品' },
      { value: 'maintenance', label: '维修保养' },
      { value: 'other', label: '其他' }
    ],
    
    urgencyLevels: [
      { value: 'low', label: '一般', color: '#00A86B' },
      { value: 'normal', label: '正常', color: '#0066CC' },
      { value: 'high', label: '紧急', color: '#FF9500' },
      { value: 'urgent', label: '特急', color: '#FF3B30' }
    ],

    // 当前选中的索引
    categoryIndex: -1,
    urgencyIndex: 1, // 默认为'normal'

    // 显示用的标签
    selectedCategoryLabel: '请选择类别',
    selectedUrgencyLabel: '正常',
    selectedUrgencyColor: '#0066CC',

    // 页面状态
    loading: false,
    submitting: false,
    
    // 错误信息
    errors: {}
  },

  onLoad: function (options) {
    this.checkUserPermissions();
  },

  // 检查用户权限
  checkUserPermissions: function() {
    if (!isLoggedIn()) {
      wx.showModal({
        title: '提示',
        content: '请先登录后使用此功能',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    const userInfo = getCurrentUser() || getCurrentUserInfo();
    this.setData({ userInfo });
  },

  // 输入框变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    if (field === 'category') {
      const categoryIndex = value;
      const selectedCategoryLabel = categoryIndex >= 0 ? this.data.categories[categoryIndex].label : '请选择类别';
      this.setData({
        [`formData.${field}`]: categoryIndex >= 0 ? this.data.categories[categoryIndex].value : '',
        categoryIndex,
        selectedCategoryLabel
      });
    } else if (field === 'urgencyLevel') {
      const urgencyIndex = value;
      const selectedUrgency = this.data.urgencyLevels[urgencyIndex];
      this.setData({
        [`formData.${field}`]: selectedUrgency.value,
        urgencyIndex,
        selectedUrgencyLabel: selectedUrgency.label,
        selectedUrgencyColor: selectedUrgency.color
      });
    } else {
      this.setData({
        [`formData.${field}`]: value
      });
    }
    
    // 清除错误信息
    if (this.data.errors[field]) {
      this.setData({
        [`errors.${field}`]: ''
      });
    }
  },

  // 日期选择
  onDateChange: function(e) {
    this.setData({
      'formData.expectedDate': e.detail.value
    });
  },

  // 表单验证
  validateForm: function() {
    const { formData } = this.data;
    const errors = {};

    if (!formData.title.trim()) {
      errors.title = '请输入采购标题';
    }

    if (!formData.category) {
      errors.category = '请选择采购类别';
    }

    if (!formData.expectedDate) {
      errors.expectedDate = '请选择预期到货日期';
    }

    if (!formData.businessPurpose.trim()) {
      errors.businessPurpose = '请说明业务目的';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  // 提交申请
  onSubmit: function() {
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    // 构造提交数据
    const submitData = {
      ...this.data.formData,
      applicant: this.data.userInfo?.name || '当前用户',
      submitTime: new Date().toISOString(),
      status: 'pending'
    };

    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('提交采购申请', submitData); } catch(_) {}

    // 模拟API调用
    setTimeout(() => {
      this.setData({ submitting: false });
      
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  }
});