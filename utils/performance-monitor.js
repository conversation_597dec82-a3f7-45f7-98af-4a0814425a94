// 智慧养鹅小程序 - 性能监控工具 V1.0
// 提供全面的性能监控和优化建议

/**
 * 性能监控工具类
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoad: {},
      userInteraction: {},
      memory: {},
      network: {},
      rendering: {}
    };
    this.observers = [];
    this.thresholds = {
      pageLoadTime: 3000,      // 页面加载时间阈值(ms)
      interactionTime: 100,    // 交互响应时间阈值(ms)
      memoryUsage: 50,         // 内存使用率阈值(%)
      fpsTarget: 60,           // 目标FPS
      renderTime: 16           // 渲染时间阈值(ms)
    };
    this.init();
  }

  /**
   * 初始化性能监控
   */
  init() {
    // 监控页面生命周期
    this.setupPageLifecycleMonitoring();
    
    // 监控内存使用
    this.setupMemoryMonitoring();
    
    // 监控网络请求
    this.setupNetworkMonitoring();
    
    // 设置性能报告
    this.setupPerformanceReporting();
  }

  /**
   * 页面生命周期监控
   */
  setupPageLifecycleMonitoring() {
    const originalPage = Page;
    const self = this;

    // 重写Page构造函数
    Page = function(options) {
      const originalOnLoad = options.onLoad || function() {};
      const originalOnShow = options.onShow || function() {};
      const originalOnReady = options.onReady || function() {};
      const originalOnHide = options.onHide || function() {};
      const originalOnUnload = options.onUnload || function() {};

      // 页面加载开始
      options.onLoad = function(...args) {
        const startTime = Date.now();
        self.startPageLoad(this.route || 'unknown', startTime);
        
        const result = originalOnLoad.apply(this, args);
        
        // 添加页面性能监控方法
        this.performanceMonitor = self;
        this.markInteractionStart = (name) => self.markInteractionStart(name);
        this.markInteractionEnd = (name) => self.markInteractionEnd(name);
        this.reportCustomMetric = (name, value) => self.reportCustomMetric(name, value);
        
        return result;
      };

      // 页面显示
      options.onShow = function(...args) {
        self.markPageShow(this.route || 'unknown');
        return originalOnShow.apply(this, args);
      };

      // 页面准备完成
      options.onReady = function(...args) {
        const endTime = Date.now();
        self.endPageLoad(this.route || 'unknown', endTime);
        return originalOnReady.apply(this, args);
      };

      // 页面隐藏
      options.onHide = function(...args) {
        self.markPageHide(this.route || 'unknown');
        return originalOnHide.apply(this, args);
      };

      // 页面卸载
      options.onUnload = function(...args) {
        self.markPageUnload(this.route || 'unknown');
        return originalOnUnload.apply(this, args);
      };

      return originalPage(options);
    };
  }

  /**
   * 开始页面加载监控
   */
  startPageLoad(route, startTime) {
    this.metrics.pageLoad[route] = {
      startTime,
      route,
      status: 'loading'
    };
    
    try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 页面加载开始: ${route}`); } catch(_) {}
  }

  /**
   * 结束页面加载监控
   */
  endPageLoad(route, endTime) {
    const pageMetric = this.metrics.pageLoad[route];
    if (pageMetric) {
      pageMetric.endTime = endTime;
      pageMetric.loadTime = endTime - pageMetric.startTime;
      pageMetric.status = 'loaded';

      // 检查是否超过阈值
      if (pageMetric.loadTime > this.thresholds.pageLoadTime) {
        this.reportPerformanceIssue('slow_page_load', {
          route,
          loadTime: pageMetric.loadTime,
          threshold: this.thresholds.pageLoadTime
        });
      }

      try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 页面加载完成: ${route}, 耗时: ${pageMetric.loadTime}ms`); } catch(_) {}
    }
  }

  /**
   * 标记页面显示
   */
  markPageShow(route) {
    const timestamp = Date.now();
    if (!this.metrics.pageLoad[route]) {
      this.metrics.pageLoad[route] = {};
    }
    this.metrics.pageLoad[route].showTime = timestamp;
    
    // 开始FPS监控
    this.startFPSMonitoring(route);
  }

  /**
   * 标记页面隐藏
   */
  markPageHide(route) {
    const timestamp = Date.now();
    if (this.metrics.pageLoad[route]) {
      this.metrics.pageLoad[route].hideTime = timestamp;
      
      // 停止FPS监控
      this.stopFPSMonitoring(route);
      
      // 计算页面停留时间
      if (this.metrics.pageLoad[route].showTime) {
        const stayTime = timestamp - this.metrics.pageLoad[route].showTime;
        this.metrics.pageLoad[route].stayTime = stayTime;
        try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 页面停留时间: ${route}, ${stayTime}ms`); } catch(_) {}
      }
    }
  }

  /**
   * 标记页面卸载
   */
  markPageUnload(route) {
    const timestamp = Date.now();
    if (this.metrics.pageLoad[route]) {
      this.metrics.pageLoad[route].unloadTime = timestamp;
      try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 页面卸载: ${route}`); } catch(_) {}
    }
  }

  /**
   * 交互响应时间监控
   */
  markInteractionStart(name) {
    const startTime = Date.now();
    this.metrics.userInteraction[name] = {
      startTime,
      name
    };
    
    try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 交互开始: ${name}`); } catch(_) {}
  }

  markInteractionEnd(name) {
    const endTime = Date.now();
    const interaction = this.metrics.userInteraction[name];
    
    if (interaction) {
      interaction.endTime = endTime;
      interaction.responseTime = endTime - interaction.startTime;

      // 检查响应时间
      if (interaction.responseTime > this.thresholds.interactionTime) {
        this.reportPerformanceIssue('slow_interaction', {
          name,
          responseTime: interaction.responseTime,
          threshold: this.thresholds.interactionTime
        });
      }

      try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 交互完成: ${name}, 响应时间: ${interaction.responseTime}ms`); } catch(_) {}
    }
  }

  /**
   * 内存使用监控
   */
  setupMemoryMonitoring() {
    // 定期检查内存使用
    setInterval(() => {
      try {
        const { getDeviceInfo } = require('./system-info-helper');
        const memoryInfo = getDeviceInfo();
        const currentTime = Date.now();
        
        // 记录内存信息
        this.metrics.memory[currentTime] = {
          timestamp: currentTime,
          platform: memoryInfo.platform,
          system: memoryInfo.system,
          pixelRatio: memoryInfo.pixelRatio
        };

        // 小程序内存监控（近似值）
        this.checkMemoryUsage();
        
      } catch (error) {
        try { const logger = require('./logger.js'); logger.warn && logger.warn('[Performance] 内存监控失败', error); } catch(_) {}
      }
    }, 10000); // 每10秒检查一次
  }

  /**
   * 检查内存使用情况
   */
  checkMemoryUsage() {
    try {
      // 获取当前页面数量（间接反映内存使用）
      const pages = getCurrentPages();
      const pageCount = pages.length;
      
      // 如果页面栈过深，可能存在内存泄露
      if (pageCount > 10) {
        this.reportPerformanceIssue('memory_leak_risk', {
          pageCount,
          pages: pages.map(page => page.route)
        });
      }

      // 记录页面栈信息
      this.metrics.memory.pageStack = {
        count: pageCount,
        routes: pages.map(page => page.route),
        timestamp: Date.now()
      };

    } catch (error) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[Performance] 内存使用检查失败', error); } catch(_) {}
    }
  }

  /**
   * 网络请求监控
   */
  setupNetworkMonitoring() {
    const originalRequest = wx.request;
    const self = this;

    wx.request = function(options) {
      const startTime = Date.now();
      const requestId = `req_${startTime}_${Math.random().toString(36).substr(2, 9)}`;

      // 记录请求开始
      self.metrics.network[requestId] = {
        url: options.url,
        method: options.method || 'GET',
        startTime,
        requestId
      };

      // 包装success回调
      const originalSuccess = options.success || function() {};
      options.success = function(res) {
        const endTime = Date.now();
        const networkMetric = self.metrics.network[requestId];
        
        if (networkMetric) {
          networkMetric.endTime = endTime;
          networkMetric.responseTime = endTime - startTime;
          networkMetric.statusCode = res.statusCode;
          networkMetric.success = true;

          try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 网络请求成功: ${options.url}, 耗时: ${networkMetric.responseTime}ms`); } catch(_) {}
        }

        return originalSuccess(res);
      };

      // 包装fail回调
      const originalFail = options.fail || function() {};
      options.fail = function(error) {
        const endTime = Date.now();
        const networkMetric = self.metrics.network[requestId];
        
        if (networkMetric) {
          networkMetric.endTime = endTime;
          networkMetric.responseTime = endTime - startTime;
          networkMetric.error = error;
          networkMetric.success = false;

          try { const logger = require('./logger.js'); logger.error && logger.error(`[Performance] 网络请求失败: ${options.url}, 耗时: ${networkMetric.responseTime}ms`, error); } catch(_) {}
        }

        return originalFail(error);
      };

      return originalRequest(options);
    };
  }

  /**
   * FPS监控
   */
  startFPSMonitoring(route) {
    if (this.fpsMonitor) {
      this.stopFPSMonitoring();
    }

    let frameCount = 0;
    let lastTime = Date.now();
    const fpsData = [];

    const measureFPS = () => {
      frameCount++;
      const currentTime = Date.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        fpsData.push(fps);
        
        // 记录FPS数据
        if (!this.metrics.rendering[route]) {
          this.metrics.rendering[route] = { fps: [] };
        }
        this.metrics.rendering[route].fps.push({
          timestamp: currentTime,
          fps
        });

        // 检查FPS是否过低
        if (fps < this.thresholds.fpsTarget * 0.8) { // 低于80%目标FPS
          this.reportPerformanceIssue('low_fps', {
            route,
            fps,
            target: this.thresholds.fpsTarget
          });
        }

        try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] FPS: ${fps} (${route})`); } catch(_) {}
        
        frameCount = 0;
        lastTime = currentTime;
      }

      this.fpsMonitor = requestAnimationFrame(measureFPS);
    };

    this.fpsMonitor = requestAnimationFrame(measureFPS);
  }

  /**
   * 停止FPS监控
   */
  stopFPSMonitoring() {
    if (this.fpsMonitor) {
      cancelAnimationFrame(this.fpsMonitor);
      this.fpsMonitor = null;
    }
  }

  /**
   * 自定义指标报告
   */
  reportCustomMetric(name, value, metadata = {}) {
    const timestamp = Date.now();
    
    if (!this.metrics.custom) {
      this.metrics.custom = {};
    }

    this.metrics.custom[name] = {
      value,
      timestamp,
      metadata
    };

    try { const logger = require('./logger.js'); logger.debug && logger.debug(`[Performance] 自定义指标: ${name} = ${value}`, metadata); } catch(_) {}
  }

  /**
   * 性能问题报告
   */
  reportPerformanceIssue(type, data) {
    const issue = {
      type,
      timestamp: Date.now(),
      data,
      severity: this.getIssueSeverity(type, data)
    };

    try { const logger = require('./logger.js'); logger.warn && logger.warn(`[Performance] 性能问题 [${issue.severity}]`, issue); } catch(_) {}

    // 存储问题报告
    if (!this.metrics.issues) {
      this.metrics.issues = [];
    }
    this.metrics.issues.push(issue);

    // 如果是严重问题，立即上报
    if (issue.severity === 'critical') {
      this.reportToDeveloper(issue);
    }
  }

  /**
   * 获取问题严重程度
   */
  getIssueSeverity(type, data) {
    switch (type) {
      case 'slow_page_load':
        return data.loadTime > this.thresholds.pageLoadTime * 2 ? 'critical' : 'warning';
      case 'slow_interaction':
        return data.responseTime > this.thresholds.interactionTime * 3 ? 'critical' : 'warning';
      case 'low_fps':
        return data.fps < this.thresholds.fpsTarget * 0.5 ? 'critical' : 'warning';
      case 'memory_leak_risk':
        return data.pageCount > 15 ? 'critical' : 'warning';
      default:
        return 'info';
    }
  }

  /**
   * 向开发者报告严重问题
   */
  reportToDeveloper(issue) {
    try {
      // 这里可以集成错误上报服务
      try { const logger = require('./logger.js'); logger.error && logger.error('[Performance] 严重性能问题', issue); } catch(_) {}
      
      // 可以发送到后端或第三方服务
      // wx.request({
      //   url: 'your-error-reporting-endpoint',
      //   method: 'POST',
      //   data: issue
      // });
      
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[Performance] 问题上报失败', error); } catch(_) {}
    }
  }

  /**
   * 设置性能报告
   */
  setupPerformanceReporting() {
    // 每5分钟生成一次性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 300000);
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport() {
    const report = {
      timestamp: Date.now(),
      summary: this.generateSummary(),
      metrics: this.metrics,
      recommendations: this.generateRecommendations()
    };

    try { const logger = require('./logger.js'); logger.debug && logger.debug('[Performance] 性能报告', report); } catch(_) {}
    return report;
  }

  /**
   * 生成性能摘要
   */
  generateSummary() {
    const pageLoadTimes = Object.values(this.metrics.pageLoad)
      .filter(page => page.loadTime)
      .map(page => page.loadTime);

    const networkTimes = Object.values(this.metrics.network)
      .filter(req => req.responseTime)
      .map(req => req.responseTime);

    return {
      pageLoad: {
        count: pageLoadTimes.length,
        average: pageLoadTimes.length > 0 ? Math.round(pageLoadTimes.reduce((a, b) => a + b, 0) / pageLoadTimes.length) : 0,
        max: pageLoadTimes.length > 0 ? Math.max(...pageLoadTimes) : 0
      },
      network: {
        count: networkTimes.length,
        average: networkTimes.length > 0 ? Math.round(networkTimes.reduce((a, b) => a + b, 0) / networkTimes.length) : 0,
        max: networkTimes.length > 0 ? Math.max(...networkTimes) : 0
      },
      issues: {
        total: this.metrics.issues ? this.metrics.issues.length : 0,
        critical: this.metrics.issues ? this.metrics.issues.filter(i => i.severity === 'critical').length : 0
      }
    };
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = [];

    // 检查页面加载时间
    const slowPages = Object.values(this.metrics.pageLoad)
      .filter(page => page.loadTime && page.loadTime > this.thresholds.pageLoadTime);

    if (slowPages.length > 0) {
      recommendations.push({
        type: 'page_load_optimization',
        message: `有${slowPages.length}个页面加载较慢，建议优化页面结构和资源加载`,
        pages: slowPages.map(p => p.route)
      });
    }

    // 检查网络请求
    const slowRequests = Object.values(this.metrics.network)
      .filter(req => req.responseTime && req.responseTime > 3000);

    if (slowRequests.length > 0) {
      recommendations.push({
        type: 'network_optimization',
        message: `有${slowRequests.length}个网络请求响应较慢，建议优化接口性能或添加缓存`,
        requests: slowRequests.map(r => r.url)
      });
    }

    // 检查内存使用
    if (this.metrics.memory.pageStack && this.metrics.memory.pageStack.count > 8) {
      recommendations.push({
        type: 'memory_optimization',
        message: '页面栈层级较深，建议优化导航结构，避免内存泄露',
        pageCount: this.metrics.memory.pageStack.count
      });
    }

    return recommendations;
  }

  /**
   * 获取性能指标
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * 清理旧数据
   */
  cleanup() {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24小时前

    // 清理旧的网络请求数据
    Object.keys(this.metrics.network).forEach(key => {
      if (this.metrics.network[key].startTime < cutoffTime) {
        delete this.metrics.network[key];
      }
    });

    // 清理旧的内存数据
    Object.keys(this.metrics.memory).forEach(key => {
      if (typeof this.metrics.memory[key] === 'object' && 
          this.metrics.memory[key].timestamp && 
          this.metrics.memory[key].timestamp < cutoffTime) {
        delete this.metrics.memory[key];
      }
    });

    try { const logger = require('./logger.js'); logger.debug && logger.debug('[Performance] 清理完成'); } catch(_) {}
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopFPSMonitoring();
    this.cleanup();
    this.observers.forEach(observer => {
      if (observer.disconnect) {
        observer.disconnect();
      }
    });
    this.observers = [];
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

// 页面混入对象
const PerformanceMixin = {
  onLoad() {
    // 页面加载性能标记
    this.performanceStartTime = Date.now();
  },

  onReady() {
    // 页面准备完成性能标记
    const loadTime = Date.now() - this.performanceStartTime;
    console.log(`[Performance] 页面准备完成: ${this.route}, 耗时: ${loadTime}ms`);
  },

  // 性能监控方法
  markInteractionStart(name) {
    if (this.performanceMonitor) {
      this.performanceMonitor.markInteractionStart(name);
    }
  },

  markInteractionEnd(name) {
    if (this.performanceMonitor) {
      this.performanceMonitor.markInteractionEnd(name);
    }
  },

  reportCustomMetric(name, value, metadata) {
    if (this.performanceMonitor) {
      this.performanceMonitor.reportCustomMetric(name, value, metadata);
    }
  }
};

module.exports = {
  PerformanceMonitor,
  performanceMonitor,
  PerformanceMixin
};