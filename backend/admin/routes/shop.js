const express = require("express");
const router = express.Router();
const apiService = require("../utils/apiService");

/**
 * 商品管理列表页面
 */
router.get("/", async (req, res) => {
  try {
    if (!req.session.user || !req.session.token) {
      return res.redirect("/login");
    }

    let products = [];
    let categories = [];
    let total = 0;
    let error = null;
    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const category = req.query.category || "";
    const search = req.query.search || "";

    try {
      // 获取商品列表
      const result = await apiService.getProducts(req.session.token, {
        page,
        limit,
        category,
        search
      });
      
      if (result.success) {
        products = result.data.products || [];
        total = result.data.pagination?.totalItems || 0;
      } else {
        error = result.message;
      }

      // 获取分类列表
      const categoriesResult = await apiService.getProductCategories(req.session.token);
      if (categoriesResult.success) {
        categories = categoriesResult.data || [];
      }
    } catch (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取商品列表失败:", err); } catch(_) {}

      error = "获取商品列表失败，请稍后再试";
    }

    res.render("shop/index", {
      title: "商品管理 - 智慧养鹅管理系统",
      products: products,
      categories: categories,
      total: total,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      filters: {
        category,
        search
      },
      error: error,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("商品管理路由错误:", error); } catch(_) {}

    res.status(500).render("error", {
      title: "服务器错误 - 智慧养鹅管理系统",
      error: {
        status: 500,
        message: "获取商品数据失败",
        stack: process.env.NODE_ENV === "development" ? error.stack : "",
      },
    });
  }
});

/**
 * 创建商品页面
 */
router.get("/create", async (req, res) => {
  try {
    if (!req.session.user || !req.session.token) {
      return res.redirect("/login");
    }

    let categories = [];
    try {
      const categoriesResult = await apiService.getProductCategories(req.session.token);
      if (categoriesResult.success) {
        categories = categoriesResult.data || [];
      }
    } catch (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取分类失败:", err); } catch(_) {}

    }

    res.render("shop/create", {
      title: "添加商品 - 智慧养鹅管理系统",
      categories: categories,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建商品页面错误:", error); } catch(_) {}

    res.status(500).render("error", {
      title: "服务器错误 - 智慧养鹅管理系统",
      error: {
        status: 500,
        message: "加载创建页面失败",
        stack: process.env.NODE_ENV === "development" ? error.stack : "",
      },
    });
  }
});

/**
 * 编辑商品页面
 */
router.get("/edit/:id", async (req, res) => {
  try {
    if (!req.session.user || !req.session.token) {
      return res.redirect("/login");
    }

    const { id } = req.params;
    let product = null;
    let categories = [];
    let error = null;

    try {
      // 获取商品详情
      const result = await apiService.getProductById(req.session.token, id);
      if (result.success) {
        product = result.data;
      } else {
        error = result.message;
      }

      // 获取分类列表
      const categoriesResult = await apiService.getProductCategories(req.session.token);
      if (categoriesResult.success) {
        categories = categoriesResult.data || [];
      }
    } catch (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取商品详情失败:", err); } catch(_) {}

      error = "获取商品详情失败，请稍后再试";
    }

    if (!product) {
      return res.status(404).render("error", {
        title: "商品不存在 - 智慧养鹅管理系统",
        error: {
          status: 404,
          message: "商品不存在或已被删除",
        },
      });
    }

    res.render("shop/edit", {
      title: `编辑商品：${product.name} - 智慧养鹅管理系统`,
      product: product,
      categories: categories,
      error: error,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("编辑商品页面错误:", error); } catch(_) {}

    res.status(500).render("error", {
      title: "服务器错误 - 智慧养鹅管理系统",
      error: {
        status: 500,
        message: "加载编辑页面失败",
        stack: process.env.NODE_ENV === "development" ? error.stack : "",
      },
    });
  }
});

/**
 * 订单管理页面
 */
router.get("/orders", async (req, res) => {
  try {
    if (!req.session.user || !req.session.token) {
      return res.redirect("/login");
    }

    let orders = [];
    let total = 0;
    let error = null;
    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const status = req.query.status || "";

    try {
      const result = await apiService.getOrders(req.session.token, {
        page,
        limit,
        status
      });
      
      if (result.success) {
        orders = result.data.orders || [];
        total = result.data.pagination?.totalItems || 0;
      } else {
        error = result.message;
      }
    } catch (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取订单列表失败:", err); } catch(_) {}

      error = "获取订单列表失败，请稍后再试";
    }

    res.render("shop/orders", {
      title: "订单管理 - 智慧养鹅管理系统",
      orders: orders,
      total: total,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      filters: {
        status
      },
      error: error,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("订单管理路由错误:", error); } catch(_) {}

    res.status(500).render("error", {
      title: "服务器错误 - 智慧养鹅管理系统",
      error: {
        status: 500,
        message: "获取订单数据失败",
        stack: process.env.NODE_ENV === "development" ? error.stack : "",
      },
    });
  }
});

/**
 * 发票管理页面
 */
router.get("/invoices", async (req, res) => {
  try {
    if (!req.session.user || !req.session.token) {
      return res.redirect("/login");
    }

    let invoices = [];
    let total = 0;
    let error = null;
    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const status = req.query.status || "";
    const type = req.query.type || "";

    try {
      const result = await apiService.getInvoices(req.session.token, {
        page,
        limit,
        status,
        type
      });
      
      if (result.success) {
        invoices = result.data.invoices || [];
        total = result.data.pagination?.totalItems || 0;
      } else {
        error = result.message;
      }
    } catch (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取发票列表失败:", err); } catch(_) {}

      error = "获取发票列表失败，请稍后再试";
    }

    res.render("shop/invoices", {
      title: "发票管理 - 智慧养鹅管理系统",
      invoices: invoices,
      total: total,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      filters: {
        status,
        type
      },
      error: error,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("发票管理路由错误:", error); } catch(_) {}

    res.status(500).render("error", {
      title: "服务器错误 - 智慧养鹅管理系统",
      error: {
        status: 500,
        message: "获取发票数据失败",
        stack: process.env.NODE_ENV === "development" ? error.stack : "",
      },
    });
  }
});

module.exports = router;