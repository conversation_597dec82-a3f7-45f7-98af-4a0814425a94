/**
 * Jest测试配置
 * 智慧养鹅项目的测试配置文件
 */

module.exports = {
  // 测试环境
  testEnvironment: "node",

  // 根目录
  rootDir: ".",

  // 测试文件匹配模式
  testMatch: [
    "**/tests/**/*.test.js",
    "**/tests/**/*.spec.js",
    "**/__tests__/**/*.js",
  ],

  // 忽略的测试文件或目录
  testPathIgnorePatterns: [
    "/node_modules/",
    "/admin/node_modules/",
    "/dist/",
    "/build/",
    "/coverage/",
  ],

  // 覆盖率收集配置
  collectCoverage: true,
  collectCoverageFrom: [
    // 包含的文件
    "utils/**/*.js",
    "constants/**/*.js",
    "controllers/**/*.js",
    "middleware/**/*.js",
    "models/**/*.js",
    "routes/**/*.js",
    "admin/utils/**/*.js",
    "admin/controllers/**/*.js",
    "admin/middleware/**/*.js",
    "admin/routes/**/*.js",

    // 排除的文件
    "!**/node_modules/**",
    "!**/tests/**",
    "!**/coverage/**",
    "!**/dist/**",
    "!**/build/**",
    "!**/*.config.js",
    "!**/*.spec.js",
    "!**/*.test.js",
  ],

  // 覆盖率报告格式
  coverageReporters: [
    "text", // 控制台输出
    "text-summary", // 简要摘要
    "html", // HTML报告
    "lcov", // LCOV格式（用于CI/CD）
    "json", // JSON格式
  ],

  // 覆盖率输出目录
  coverageDirectory: "coverage",

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 75,
      lines: 80,
      statements: 80,
    },
    // 重要模块的更高要求
    "./utils/api-client.js": {
      branches: 80,
      functions: 85,
      lines: 90,
      statements: 90,
    },
    "./constants/": {
      branches: 90,
      functions: 90,
      lines: 95,
      statements: 95,
    },
  },

  // 测试设置文件
  setupFilesAfterEnv: ["<rootDir>/tests/setup.js"],

  // 模块路径映射
  moduleNameMapping: {
    "^@/(.*)$": "<rootDir>/$1",
    "^@utils/(.*)$": "<rootDir>/utils/$1",
    "^@constants/(.*)$": "<rootDir>/constants/$1",
    "^@admin/(.*)$": "<rootDir>/admin/$1",
  },

  // 转换配置（如果需要）
  transform: {
    "^.+\\.js$": "babel-jest",
  },

  // 模拟配置
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // 测试超时时间（毫秒）
  testTimeout: 30000,

  // 详细输出
  verbose: true,

  // 错误输出配置
  errorOnDeprecated: true,

  // 监听模式配置
  watchPathIgnorePatterns: [
    "/node_modules/",
    "/admin/node_modules/",
    "/coverage/",
    "/logs/",
    "/uploads/",
  ],

  // 全局变量
  globals: {
    NODE_ENV: "test",
  },

  // 模块文件扩展名
  moduleFileExtensions: ["js", "json", "node"],

  // 报告器配置
  reporters: [
    "default",
    [
      "jest-html-reporters",
      {
        publicPath: "./coverage/html-report",
        filename: "report.html",
        expand: true,
      },
    ],
    [
      "jest-junit",
      {
        outputDirectory: "./coverage",
        outputName: "junit.xml",
        ancestorSeparator: " › ",
        uniqueOutputName: "false",
        suiteNameTemplate: "{filepath}",
        classNameTemplate: "{classname}",
        titleTemplate: "{title}",
      },
    ],
  ],

  // 测试结果处理器
  testResultsProcessor: "jest-sonar-reporter",

  // 通知配置（可选）
  notify: false,
  notifyMode: "failure-change",

  // 缓存配置
  cache: true,
  cacheDirectory: "<rootDir>/.jest-cache",

  // 并发配置
  maxWorkers: "50%",
  maxConcurrency: 5,

  // 失败时停止
  bail: 0, // 0表示不停止，设置为数字表示失败N个测试后停止

  // 强制退出配置
  forceExit: false,
  detectOpenHandles: true,

  // 自定义测试序列器（如果需要）
  testSequencer: "@jest/test-sequencer",

  // 预设配置
  preset: null,

  // 项目配置（多项目时使用）
  projects: [
    {
      displayName: "Backend API Tests",
      testMatch: ["**/tests/api/**/*.test.js"],
      setupFilesAfterEnv: ["<rootDir>/tests/setup.js"],
    },
    {
      displayName: "Admin System Tests",
      testMatch: ["**/tests/admin/**/*.test.js"],
      setupFilesAfterEnv: ["<rootDir>/tests/setup.js"],
    },
    {
      displayName: "Integration Tests",
      testMatch: ["**/tests/integration/**/*.test.js"],
      setupFilesAfterEnv: ["<rootDir>/tests/setup.js"],
      testTimeout: 60000, // 集成测试需要更长时间
    },
  ],
};
