-- SaaS平台管理系统数据库初始化脚本
-- SaaS Platform Management System Database Initialization

-- 创建SaaS平台数据库
CREATE DATABASE IF NOT EXISTS smart_goose_saas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE smart_goose_saas;

-- 平台管理员表
CREATE TABLE IF NOT EXISTS platform_admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('super_admin', 'admin', 'operator', 'support') DEFAULT 'admin' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    avatar VARCHAR(500) COMMENT '头像URL',
    lastLoginAt DATETIME COMMENT '最后登录时间',
    permissions JSON COMMENT '权限列表',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='平台管理员表';

-- 租户表
CREATE TABLE IF NOT EXISTS tenants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantCode VARCHAR(50) NOT NULL UNIQUE COMMENT '租户代码',
    companyName VARCHAR(200) NOT NULL COMMENT '公司名称',
    contactName VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    contactPhone VARCHAR(20) NOT NULL COMMENT '联系电话',
    contactEmail VARCHAR(100) NOT NULL COMMENT '联系邮箱',
    address TEXT COMMENT '公司地址',
    businessLicense VARCHAR(100) COMMENT '营业执照号',
    industry VARCHAR(100) COMMENT '行业类型',
    scale ENUM('small', 'medium', 'large', 'enterprise') DEFAULT 'small' COMMENT '企业规模',
    status ENUM('pending', 'active', 'suspended', 'expired', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    subscriptionPlan ENUM('trial', 'basic', 'standard', 'premium', 'enterprise') DEFAULT 'trial' COMMENT '订阅计划',
    subscriptionStartDate DATE COMMENT '订阅开始日期',
    subscriptionEndDate DATE COMMENT '订阅结束日期',
    maxUsers INT DEFAULT 5 COMMENT '最大用户数',
    maxFlocks INT DEFAULT 10 COMMENT '最大鹅群数',
    storageLimit BIGINT DEFAULT ********** COMMENT '存储限制(字节)',
    apiCallsLimit INT DEFAULT 10000 COMMENT 'API调用限制/月',
    customDomain VARCHAR(200) COMMENT '自定义域名',
    logo VARCHAR(500) COMMENT '企业Logo',
    theme JSON COMMENT '主题配置',
    features JSON COMMENT '功能权限配置',
    settings JSON COMMENT '租户设置',
    monthlyFee DECIMAL(10,2) DEFAULT 0 COMMENT '月费',
    totalRevenue DECIMAL(12,2) DEFAULT 0 COMMENT '总收入',
    lastActiveAt DATETIME COMMENT '最后活跃时间',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_tenant_code (tenantCode),
    INDEX idx_status (status),
    INDEX idx_subscription_plan (subscriptionPlan),
    INDEX idx_subscription_end_date (subscriptionEndDate),
    INDEX idx_created_at (createdAt)
) ENGINE=InnoDB COMMENT='租户表';

-- 租户用户表
CREATE TABLE IF NOT EXISTS tenant_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantId INT NOT NULL COMMENT '租户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('owner', 'admin', 'manager', 'user') DEFAULT 'user' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    avatar VARCHAR(500) COMMENT '头像URL',
    lastLoginAt DATETIME COMMENT '最后登录时间',
    permissions JSON COMMENT '权限列表',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_username (tenantId, username),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='租户用户表';

-- 租户鹅群表
CREATE TABLE IF NOT EXISTS tenant_flocks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantId INT NOT NULL COMMENT '租户ID',
    userId INT NOT NULL COMMENT '用户ID',
    name VARCHAR(100) NOT NULL COMMENT '鹅群名称',
    batchNumber VARCHAR(50) NOT NULL COMMENT '批次号',
    breed VARCHAR(50) NOT NULL COMMENT '品种',
    totalCount INT NOT NULL DEFAULT 0 COMMENT '总数量',
    currentCount INT NOT NULL DEFAULT 0 COMMENT '当前数量',
    maleCount INT NOT NULL DEFAULT 0 COMMENT '雄性数量',
    femaleCount INT NOT NULL DEFAULT 0 COMMENT '雌性数量',
    ageGroup ENUM('young', 'adult', 'breeding', 'retired') DEFAULT 'young' COMMENT '年龄组',
    status ENUM('active', 'inactive', 'sold', 'deceased') DEFAULT 'active' COMMENT '状态',
    establishedDate DATE NOT NULL COMMENT '建群日期',
    location VARCHAR(200) COMMENT '饲养位置',
    description TEXT COMMENT '描述备注',
    healthStatus ENUM('excellent', 'good', 'fair', 'poor') DEFAULT 'good' COMMENT '健康状态',
    avgDailyEggProduction DECIMAL(5,2) DEFAULT 0 COMMENT '平均日产蛋量',
    totalCost DECIMAL(10,2) DEFAULT 0 COMMENT '总成本',
    totalRevenue DECIMAL(10,2) DEFAULT 0 COMMENT '总收入',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (userId) REFERENCES tenant_users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_batch (tenantId, batchNumber),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_user_id (userId),
    INDEX idx_status (status),
    INDEX idx_established_date (establishedDate)
) ENGINE=InnoDB COMMENT='租户鹅群表';

-- 平台使用统计表
CREATE TABLE IF NOT EXISTS platform_usage_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantId INT NOT NULL COMMENT '租户ID',
    statDate DATE NOT NULL COMMENT '统计日期',
    activeUsers INT DEFAULT 0 COMMENT '活跃用户数',
    totalFlocks INT DEFAULT 0 COMMENT '总鹅群数',
    totalGeese INT DEFAULT 0 COMMENT '总鹅数量',
    apiCalls INT DEFAULT 0 COMMENT 'API调用次数',
    storageUsed BIGINT DEFAULT 0 COMMENT '存储使用量(字节)',
    loginCount INT DEFAULT 0 COMMENT '登录次数',
    dataOperations INT DEFAULT 0 COMMENT '数据操作次数',
    revenue DECIMAL(10,2) DEFAULT 0 COMMENT '当日收入',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_date (tenantId, statDate),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_stat_date (statDate)
) ENGINE=InnoDB COMMENT='平台使用统计表';

-- 订阅计划表
CREATE TABLE IF NOT EXISTS subscription_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    planCode VARCHAR(50) NOT NULL UNIQUE COMMENT '计划代码',
    planName VARCHAR(100) NOT NULL COMMENT '计划名称',
    description TEXT COMMENT '计划描述',
    monthlyPrice DECIMAL(10,2) NOT NULL COMMENT '月费',
    yearlyPrice DECIMAL(10,2) COMMENT '年费',
    maxUsers INT NOT NULL COMMENT '最大用户数',
    maxFlocks INT NOT NULL COMMENT '最大鹅群数',
    storageLimit BIGINT NOT NULL COMMENT '存储限制(字节)',
    apiCallsLimit INT NOT NULL COMMENT 'API调用限制/月',
    features JSON COMMENT '功能列表',
    isActive BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sortOrder INT DEFAULT 0 COMMENT '排序',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_plan_code (planCode),
    INDEX idx_is_active (isActive)
) ENGINE=InnoDB COMMENT='订阅计划表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    configKey VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    configValue TEXT COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    category VARCHAR(50) COMMENT '配置分类',
    dataType ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '数据类型',
    isEditable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (configKey),
    INDEX idx_category (category)
) ENGINE=InnoDB COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    adminId INT COMMENT '管理员ID',
    tenantId INT COMMENT '租户ID',
    operation VARCHAR(100) NOT NULL COMMENT '操作类型',
    module VARCHAR(50) NOT NULL COMMENT '模块',
    description TEXT COMMENT '操作描述',
    ipAddress VARCHAR(45) COMMENT 'IP地址',
    userAgent TEXT COMMENT '用户代理',
    requestData JSON COMMENT '请求数据',
    responseData JSON COMMENT '响应数据',
    status ENUM('success', 'failed') DEFAULT 'success' COMMENT '操作状态',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (adminId) REFERENCES platform_admins(id) ON DELETE SET NULL,
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE SET NULL,
    INDEX idx_admin_id (adminId),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_operation (operation),
    INDEX idx_module (module),
    INDEX idx_created_at (createdAt)
) ENGINE=InnoDB COMMENT='操作日志表';

-- 插入默认平台管理员
INSERT INTO platform_admins (username, email, password, name, role, status) VALUES 
('platform_admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', '平台超级管理员', 'super_admin', 'active');
-- 密码是: admin123

-- 插入默认订阅计划
INSERT INTO subscription_plans (planCode, planName, description, monthlyPrice, yearlyPrice, maxUsers, maxFlocks, storageLimit, apiCallsLimit, features) VALUES
('trial', '试用版', '免费试用30天，体验基础功能', 0.00, 0.00, 3, 5, **********, 1000, '["basic_management", "data_export"]'),
('basic', '基础版', '适合小型养殖场', 99.00, 999.00, 10, 20, *********0, 10000, '["basic_management", "data_export", "basic_analytics"]'),
('standard', '标准版', '适合中型养殖场', 299.00, 2999.00, 50, 100, 21474836480, 50000, '["basic_management", "data_export", "advanced_analytics", "api_access"]'),
('premium', '高级版', '适合大型养殖场', 599.00, 5999.00, 200, 500, **********00, 200000, '["basic_management", "data_export", "advanced_analytics", "api_access", "custom_reports", "priority_support"]'),
('enterprise', '企业版', '适合企业级客户', 1999.00, 19999.00, 1000, 2000, 1099511627776, 1000000, '["all_features", "custom_development", "dedicated_support", "sla_guarantee"]');

-- 插入示例租户
INSERT INTO tenants (tenantCode, companyName, contactName, contactPhone, contactEmail, address, status, subscriptionPlan, subscriptionStartDate, subscriptionEndDate, maxUsers, maxFlocks) VALUES
('DEMO001', '绿野生态养殖有限公司', '张经理', '13800138001', '<EMAIL>', '江苏省南京市江宁区生态园区', 'active', 'standard', '2024-01-01', '2024-12-31', 50, 100),
('DEMO002', '金鹅农业科技公司', '李总', '13800138002', '<EMAIL>', '山东省济南市历城区农业园', 'active', 'premium', '2024-01-15', '2024-12-31', 200, 500),
('DEMO003', '新希望养殖合作社', '王主任', '13800138003', '<EMAIL>', '四川省成都市双流区', 'pending', 'trial', '2024-07-01', '2024-07-31', 3, 5);

-- 插入示例租户用户
INSERT INTO tenant_users (tenantId, username, email, password, name, role, status) VALUES
(1, 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', '张经理', 'owner', 'active'),
(2, 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', '李总', 'owner', 'active'),
(3, 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', '王主任', 'owner', 'active');

-- 插入示例鹅群数据
INSERT INTO tenant_flocks (tenantId, userId, name, batchNumber, breed, totalCount, currentCount, maleCount, femaleCount, ageGroup, status, establishedDate, location) VALUES
(1, 1, '绿野白鹅群A', 'LY-2024001', 'white_goose', 200, 198, 40, 158, 'adult', 'active', '2024-01-15', '1号养殖区'),
(1, 1, '绿野灰鹅群B', 'LY-2024002', 'grey_goose', 150, 149, 30, 119, 'breeding', 'active', '2023-12-01', '2号养殖区'),
(2, 2, '金鹅优质群C', 'JE-2024001', 'white_goose', 500, 495, 100, 395, 'adult', 'active', '2024-01-10', 'A区养殖场'),
(2, 2, '金鹅种鹅群D', 'JE-2024002', 'grey_goose', 300, 298, 60, 238, 'breeding', 'active', '2023-11-15', 'B区养殖场'),
(3, 3, '新希望试验群', 'XW-2024001', 'white_goose', 50, 48, 10, 38, 'young', 'active', '2024-07-01', '试验区');

-- 插入租户小程序配置表
CREATE TABLE IF NOT EXISTS tenant_miniprogram_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantId INT NOT NULL COMMENT '租户ID',
    appId VARCHAR(100) NOT NULL COMMENT '小程序AppID',
    appSecret VARCHAR(255) NOT NULL COMMENT '小程序AppSecret',
    version VARCHAR(20) DEFAULT '1.0.0' COMMENT '小程序版本',
    status ENUM('development', 'testing', 'production') DEFAULT 'development' COMMENT '状态',
    customDomain VARCHAR(200) COMMENT '自定义域名',
    themeConfig JSON COMMENT '主题配置',
    featuresConfig JSON COMMENT '功能配置',
    apiEndpoint VARCHAR(500) NOT NULL COMMENT 'API接口地址',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_appid (tenantId, appId),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_app_id (appId),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='租户小程序配置表';

-- 插入租户API配置表
CREATE TABLE IF NOT EXISTS tenant_api_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantId INT NOT NULL COMMENT '租户ID',
    configKey VARCHAR(100) NOT NULL COMMENT '配置键',
    configValue TEXT COMMENT '配置值',
    dataType ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '数据类型',
    description TEXT COMMENT '配置描述',
    isSecret BOOLEAN DEFAULT FALSE COMMENT '是否敏感信息',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_config (tenantId, configKey),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_config_key (configKey)
) ENGINE=InnoDB COMMENT='租户API配置表';

-- 插入小程序页面权限配置表
CREATE TABLE IF NOT EXISTS miniprogram_page_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantId INT NOT NULL COMMENT '租户ID',
    pagePath VARCHAR(200) NOT NULL COMMENT '页面路径',
    pageTitle VARCHAR(100) NOT NULL COMMENT '页面标题',
    isEnabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    accessLevel ENUM('all', 'owner', 'admin', 'manager', 'user') DEFAULT 'all' COMMENT '访问级别',
    customConfig JSON COMMENT '自定义配置',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_page (tenantId, pagePath),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_page_path (pagePath),
    INDEX idx_is_enabled (isEnabled)
) ENGINE=InnoDB COMMENT='小程序页面权限配置表';

-- 插入微信支付配置表
CREATE TABLE IF NOT EXISTS wechat_payment_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenantId INT NOT NULL COMMENT '租户ID',
    merchantId VARCHAR(100) NOT NULL COMMENT '商户号',
    apiKey VARCHAR(255) NOT NULL COMMENT 'API密钥',
    certPath VARCHAR(500) COMMENT '证书路径',
    notifyUrl VARCHAR(500) NOT NULL COMMENT '支付回调地址',
    isEnabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    environment ENUM('sandbox', 'production') DEFAULT 'sandbox' COMMENT '环境',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (tenantId) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_merchant (tenantId, merchantId),
    INDEX idx_tenant_id (tenantId),
    INDEX idx_merchant_id (merchantId),
    INDEX idx_is_enabled (isEnabled)
) ENGINE=InnoDB COMMENT='微信支付配置表';

-- 插入使用统计数据
INSERT INTO platform_usage_stats (tenantId, statDate, activeUsers, totalFlocks, totalGeese, apiCalls, storageUsed, loginCount, dataOperations, revenue) VALUES
(1, '2024-07-30', 8, 2, 347, 1250, 2147483648, 25, 156, 299.00),
(2, '2024-07-30', 15, 2, 793, 3200, *********0, 42, 287, 599.00),
(3, '2024-07-30', 2, 1, 48, 150, *********, 8, 23, 0.00);

-- 创建视图：租户概览统计
CREATE OR REPLACE VIEW tenant_overview_stats AS
SELECT 
    t.id,
    t.tenantCode,
    t.companyName,
    t.status,
    t.subscriptionPlan,
    t.subscriptionEndDate,
    COUNT(DISTINCT tu.id) as totalUsers,
    COUNT(DISTINCT tf.id) as totalFlocks,
    COALESCE(SUM(tf.currentCount), 0) as totalGeese,
    t.totalRevenue,
    t.lastActiveAt,
    t.createdAt
FROM tenants t
LEFT JOIN tenant_users tu ON t.id = tu.tenantId AND tu.status = 'active'
LEFT JOIN tenant_flocks tf ON t.id = tf.tenantId AND tf.status = 'active'
GROUP BY t.id, t.tenantCode, t.companyName, t.status, t.subscriptionPlan, t.subscriptionEndDate, t.totalRevenue, t.lastActiveAt, t.createdAt;

-- 创建视图：平台运营统计
CREATE OR REPLACE VIEW platform_operation_stats AS
SELECT 
    DATE(pus.statDate) as date,
    COUNT(DISTINCT pus.tenantId) as activeTenants,
    SUM(pus.activeUsers) as totalActiveUsers,
    SUM(pus.totalFlocks) as totalFlocks,
    SUM(pus.totalGeese) as totalGeese,
    SUM(pus.apiCalls) as totalApiCalls,
    SUM(pus.revenue) as dailyRevenue
FROM platform_usage_stats pus
GROUP BY DATE(pus.statDate)
ORDER BY date DESC;

COMMIT;
