/**
 * 批次财务统计组件
 * 显示批次的成本、收入、利润等财务信息
 */

Component({
  properties: {
    // 批次号
    batchNumber: {
      type: String,
      value: ''
    },
    // 是否显示详细记录
    showDetails: {
      type: Boolean,
      value: true
    },
    // 组件样式类型
    styleType: {
      type: String,
      value: 'card' // card, simple, compact
    }
  },

  data: {
    loading: true,
    stats: {
      batchNumber: '',
      totalCost: 0,
      totalRevenue: 0,
      netProfit: 0,
      profitMargin: 0,
      recordCount: 0,
      firstRecordDate: '',
      lastRecordDate: '',
      cycleDays: 0
    },
    records: [],
    error: null
  },

  lifetimes: {
    attached() {
      if (this.data.batchNumber) {
        this.loadBatchStats();
      }
    }
  },

  observers: {
    'batchNumber': function(newBatchNumber) {
      if (newBatchNumber) {
        this.loadBatchStats();
      }
    }
  },

  methods: {
    /**
     * 加载批次财务统计
     */
    async loadBatchStats() {
      if (!this.data.batchNumber) {
        return;
      }

      this.setData({ loading: true, error: null });

      try {
        const app = getApp();
        const response = await wx.request({
          url: `${app.globalData.apiBase}/finance/batch-stats/${this.data.batchNumber}`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.data.success) {
          this.setData({
            stats: response.data.data,
            records: response.data.data.records || [],
            loading: false
          });

          // 触发数据加载完成事件
          this.triggerEvent('statsLoaded', response.data.data);
        } else {
          throw new Error(response.data.message || '获取财务统计失败');
        }

      } catch (error) {
        console.error('加载批次财务统计失败:', error);
        this.setData({
          error: error.message || '加载失败',
          loading: false
        });

        wx.showToast({
          title: '加载财务统计失败',
          icon: 'none'
        });
      }
    },

    /**
     * 刷新数据
     */
    onRefresh() {
      this.loadBatchStats();
    },

    /**
     * 查看财务记录详情
     */
    onViewRecord(e) {
      const record = e.currentTarget.dataset.record;
      this.triggerEvent('recordTap', record);
    },

    /**
     * 格式化金额显示
     */
    formatAmount(amount) {
      if (typeof amount !== 'number') {
        return '0.00';
      }
      return amount.toFixed(2);
    },

    /**
     * 格式化百分比显示
     */
    formatPercent(percent) {
      if (typeof percent !== 'number') {
        return '0.00%';
      }
      return `${percent.toFixed(2)}%`;
    },

    /**
     * 获取利润状态样式类
     */
    getProfitStatusClass(profit) {
      if (profit > 0) return 'profit-positive';
      if (profit < 0) return 'profit-negative';
      return 'profit-zero';
    },

    /**
     * 获取记录类型显示名称
     */
    getRecordTypeName(type) {
      const typeMap = {
        'income': '收入',
        'expense': '支出'
      };
      return typeMap[type] || type;
    },

    /**
     * 获取记录类型样式类
     */
    getRecordTypeClass(type) {
      return type === 'income' ? 'record-income' : 'record-expense';
    },

    /**
     * 格式化日期显示
     */
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    },

    /**
     * 导出财务数据
     */
    onExportData() {
      this.triggerEvent('exportData', {
        batchNumber: this.data.batchNumber,
        stats: this.data.stats,
        records: this.data.records
      });
    },

    /**
     * 查看趋势分析
     */
    onViewTrends() {
      this.triggerEvent('viewTrends', {
        batchNumber: this.data.batchNumber
      });
    }
  }
});
