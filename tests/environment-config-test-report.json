{"timestamp": "2025-08-06T01:55:04.333Z", "summary": {"total": 26, "passed": 25, "failed": 1, "successRate": 96.15384615384616}, "environment": {"current": "development", "name": "开发环境", "baseUrl": "http://localhost:3000", "isDevelopment": true, "isProduction": false}, "tests": [{"name": "环境检测", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.325Z"}, {"name": "环境名称获取", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.329Z"}, {"name": "开发环境判断", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.329Z"}, {"name": "生产环境判断", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.329Z"}, {"name": "基础API地址", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.330Z"}, {"name": "端点URL生成", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.330Z"}, {"name": "上传URL生成", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.330Z"}, {"name": "下载URL生成", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.330Z"}, {"name": "域名白名单获取", "passed": ["http://localhost:3000", "https://dev-api.zhihuiyange.com"], "error": null, "timestamp": "2025-08-06T01:55:04.330Z"}, {"name": "request域名列表", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.330Z"}, {"name": "配置指南生成", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "请求配置获取", "passed": 10000, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "功能特性配置", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "API客户端实例", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "API客户端基础URL", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "API客户端配置", "passed": 10000, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "相对路径处理", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "API路径处理", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "完整URL处理", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "认证头生成", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "Authorization头", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "Tenant头", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "API统计信息", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "环境信息收集", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.331Z"}, {"name": "无效环境处理", "passed": true, "error": null, "timestamp": "2025-08-06T01:55:04.333Z"}, {"name": "日志功能", "passed": false, "error": null, "timestamp": "2025-08-06T01:55:04.333Z"}], "recommendations": ["修复失败的测试用例", "检查环境配置是否完整", "在开发环境中启用详细日志", "配置HTTPS代理服务器", "准备生产环境配置"]}