/**
 * UI设计系统常量
 * 统一管理颜色、字体、尺寸、动画等设计规范
 */

// ===== 主题色彩系统 =====
const COLORS = {
  // 主色调 - 智慧养鹅主题绿色
  PRIMARY: {
    DEFAULT: '#2E8B57',      // 海绿色 - 主色
    LIGHT: '#90EE90',        // 浅绿色 - 浅色变体
    DARK: '#006400',         // 深绿色 - 深色变体
    GRADIENT: 'linear-gradient(135deg, #2E8B57 0%, #90EE90 100%)'
  },
  
  // 辅助色
  SECONDARY: {
    DEFAULT: '#4682B4',      // 钢蓝色
    LIGHT: '#87CEEB',        // 天蓝色
    DARK: '#191970'          // 深蓝色
  },
  
  // 功能色彩
  SUCCESS: {
    DEFAULT: '#28A745',      // 成功绿
    LIGHT: '#D4EDDA',        // 浅成功绿
    DARK: '#155724',         // 深成功绿
    TEXT: '#155724'          // 成功文字色
  },
  
  WARNING: {
    DEFAULT: '#FFC107',      // 警告黄
    LIGHT: '#FFF3CD',        // 浅警告黄
    DARK: '#856404',         // 深警告黄
    TEXT: '#856404'          // 警告文字色
  },
  
  ERROR: {
    DEFAULT: '#DC3545',      // 错误红
    LIGHT: '#F8D7DA',        // 浅错误红
    DARK: '#721C24',         // 深错误红
    TEXT: '#721C24'          // 错误文字色
  },
  
  INFO: {
    DEFAULT: '#17A2B8',      // 信息蓝
    LIGHT: '#D6F1F5',        // 浅信息蓝
    DARK: '#0C5460',         // 深信息蓝
    TEXT: '#0C5460'          // 信息文字色
  },
  
  // 中性色彩
  NEUTRAL: {
    WHITE: '#FFFFFF',
    BLACK: '#000000',
    GRAY_50: '#F9FAFB',      // 最浅灰
    GRAY_100: '#F3F4F6',     // 极浅灰
    GRAY_200: '#E5E7EB',     // 浅灰
    GRAY_300: '#D1D5DB',     // 中浅灰
    GRAY_400: '#9CA3AF',     // 中灰
    GRAY_500: '#6B7280',     // 标准灰
    GRAY_600: '#4B5563',     // 中深灰
    GRAY_700: '#374151',     // 深灰
    GRAY_800: '#1F2937',     // 极深灰
    GRAY_900: '#111827'      // 最深灰
  },
  
  // 背景色彩
  BACKGROUND: {
    PRIMARY: '#FFFFFF',      // 主背景色
    SECONDARY: '#F5F6F8',    // 次背景色
    TERTIARY: '#F9FAFB',     // 第三背景色
    OVERLAY: 'rgba(0, 0, 0, 0.5)',  // 遮罩背景
    MODAL: 'rgba(0, 0, 0, 0.3)'     // 模态框背景
  },
  
  // 文字色彩
  TEXT: {
    PRIMARY: '#333333',      // 主文字色
    SECONDARY: '#666666',    // 次文字色
    TERTIARY: '#999999',     // 第三文字色
    PLACEHOLDER: '#CCCCCC',  // 占位符文字
    DISABLED: '#CCCCCC',     // 禁用文字
    INVERSE: '#FFFFFF',      // 反色文字
    LINK: '#0066CC',         // 链接文字
    LINK_HOVER: '#0052A3'    // 链接悬停
  },
  
  // 边框色彩
  BORDER: {
    DEFAULT: '#E5E7EB',      // 默认边框
    LIGHT: '#F3F4F6',        // 浅边框
    DARK: '#D1D5DB',         // 深边框
    FOCUS: '#2E8B57',        // 聚焦边框
    ERROR: '#DC3545'         // 错误边框
  },
  
  // 业务相关色彩
  BUSINESS: {
    HEALTHY: '#28A745',      // 健康状态
    SICK: '#DC3545',         // 生病状态
    WARNING: '#FFC107',      // 预警状态
    PRODUCTION: '#17A2B8',   // 生产相关
    INVENTORY: '#6F42C1',    // 库存相关
    FINANCE: '#FD7E14'       // 财务相关
  }
};

// ===== 字体系统 =====
const TYPOGRAPHY = {
  // 字体族
  FONT_FAMILY: {
    DEFAULT: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    MONO: 'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
  },
  
  // 字体大小 (rpx)
  FONT_SIZE: {
    XS: 20,          // 极小字体
    SM: 24,          // 小字体
    BASE: 28,        // 基础字体
    LG: 32,          // 大字体
    XL: 36,          // 极大字体
    XXL: 40,         // 特大字体
    XXXL: 48,        // 超大字体
    DISPLAY: 60      // 展示字体
  },
  
  // 字体重量
  FONT_WEIGHT: {
    THIN: 100,
    LIGHT: 300,
    NORMAL: 400,
    MEDIUM: 500,
    SEMIBOLD: 600,
    BOLD: 700,
    EXTRABOLD: 800,
    BLACK: 900
  },
  
  // 行高
  LINE_HEIGHT: {
    NONE: 1,
    TIGHT: 1.25,
    SNUG: 1.375,
    NORMAL: 1.5,
    RELAXED: 1.625,
    LOOSE: 2
  }
};

// ===== 间距系统 =====
const SPACING = {
  // 基础间距单位 (rpx)
  XS: 10,       // 2.5 * 4
  SM: 20,       // 5 * 4  
  BASE: 30,     // 7.5 * 4
  MD: 40,       // 10 * 4
  LG: 60,       // 15 * 4
  XL: 80,       // 20 * 4
  XXL: 120,     // 30 * 4
  XXXL: 160,    // 40 * 4
  
  // 页面间距
  PAGE: {
    HORIZONTAL: 30,   // 页面水平边距
    VERTICAL: 40,     // 页面垂直边距
    SECTION: 60       // 区块间距
  },
  
  // 组件间距
  COMPONENT: {
    INNER: 20,        // 组件内间距
    OUTER: 30,        // 组件外间距
    GAP: 20           // 组件间隙
  }
};

// ===== 尺寸系统 =====
const SIZES = {
  // 图标尺寸 (rpx)
  ICON: {
    XS: 24,
    SM: 32,
    BASE: 40,
    LG: 48,
    XL: 60,
    XXL: 80
  },
  
  // 头像尺寸 (rpx)
  AVATAR: {
    SM: 60,
    BASE: 80,
    LG: 120,
    XL: 160
  },
  
  // 按钮高度 (rpx)
  BUTTON: {
    SM: 60,
    BASE: 80,
    LG: 100
  },
  
  // 输入框高度 (rpx)
  INPUT: {
    SM: 60,
    BASE: 80,
    LG: 100
  },
  
  // 容器最大宽度 (rpx)
  CONTAINER: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280
  }
};

// ===== 圆角系统 =====
const BORDER_RADIUS = {
  NONE: 0,
  SM: 4,          // 小圆角
  BASE: 8,        // 基础圆角
  MD: 12,         // 中圆角
  LG: 16,         // 大圆角
  XL: 24,         // 极大圆角
  FULL: 9999,     // 完全圆角
  
  // 组件专用圆角
  BUTTON: 8,
  CARD: 12,
  INPUT: 8,
  MODAL: 16,
  AVATAR: 9999
};

// ===== 阴影系统 =====
const SHADOWS = {
  NONE: 'none',
  SM: '0 2rpx 4rpx rgba(0, 0, 0, 0.05)',
  BASE: '0 4rpx 8rpx rgba(0, 0, 0, 0.1)',
  MD: '0 8rpx 16rpx rgba(0, 0, 0, 0.1)',
  LG: '0 16rpx 32rpx rgba(0, 0, 0, 0.15)',
  XL: '0 24rpx 48rpx rgba(0, 0, 0, 0.2)',
  
  // 组件专用阴影
  CARD: '0 4rpx 8rpx rgba(0, 0, 0, 0.1)',
  MODAL: '0 16rpx 32rpx rgba(0, 0, 0, 0.15)',
  BUTTON: '0 2rpx 4rpx rgba(0, 0, 0, 0.05)',
  FLOATING: '0 8rpx 16rpx rgba(0, 0, 0, 0.15)'
};

// ===== 动画系统 =====
const ANIMATIONS = {
  // 动画时长 (ms)
  DURATION: {
    FAST: 150,
    BASE: 300,
    SLOW: 500,
    SLOWER: 750,
    SLOWEST: 1000
  },
  
  // 缓动函数
  EASING: {
    LINEAR: 'linear',
    EASE: 'ease',
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
    CUBIC_BEZIER: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },
  
  // 预设动画
  PRESETS: {
    FADE_IN: {
      duration: 300,
      timingFunction: 'ease-out'
    },
    SLIDE_UP: {
      duration: 300,
      timingFunction: 'ease-out'
    },
    SCALE_IN: {
      duration: 200,
      timingFunction: 'ease-out'
    }
  }
};

// ===== Z-Index层级 =====
const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
  LOADING: 1090
};

// ===== 断点系统 =====
const BREAKPOINTS = {
  SM: 576,    // 小屏
  MD: 768,    // 中屏
  LG: 992,    // 大屏
  XL: 1200,   // 超大屏
  XXL: 1400   // 极大屏
};

// ===== 工具函数 =====
const UI_UTILS = {
  /**
   * 根据类型获取颜色
   * @param {string} type 类型 (primary, success, warning, error, info)
   * @param {string} variant 变体 (default, light, dark)
   * @returns {string} 颜色值
   */
  getColor: (type, variant = 'DEFAULT') => {
    const colorMap = {
      primary: COLORS.PRIMARY,
      success: COLORS.SUCCESS,
      warning: COLORS.WARNING,
      error: COLORS.ERROR,
      info: COLORS.INFO
    };
    return colorMap[type]?.[variant.toUpperCase()] || COLORS.NEUTRAL.GRAY_500;
  },
  
  /**
   * 根据尺寸获取间距
   * @param {string} size 尺寸 (xs, sm, base, md, lg, xl, xxl, xxxl)
   * @returns {number} 间距值 (rpx)
   */
  getSpacing: (size) => {
    return SPACING[size.toUpperCase()] || SPACING.BASE;
  },
  
  /**
   * 根据设备类型调整尺寸
   * @param {number} size 基础尺寸
   * @param {string} device 设备类型 (phone, tablet)
   * @returns {number} 调整后尺寸
   */
  responsiveSize: (size, device = 'phone') => {
    const scaleFactor = device === 'tablet' ? 1.2 : 1;
    return Math.round(size * scaleFactor);
  }
};

// 导出所有UI常量
module.exports = {
  COLORS,
  TYPOGRAPHY,
  SPACING,
  SIZES,
  BORDER_RADIUS,
  SHADOWS,
  ANIMATIONS,
  Z_INDEX,
  BREAKPOINTS,
  UI_UTILS
};