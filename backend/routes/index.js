/**
 * 主路由配置文件
 * 统一管理所有API路由
 */

const express = require('express');
const router = express.Router();

// 导入各个模块的路由
const financeRoutes = require('./finance.routes');
const productionIntegratedRoutes = require('./production-integrated.routes');

// 注册路由
router.use('/finance', financeRoutes);
router.use('/production', productionIntegratedRoutes);

// API根路径信息
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '智慧养鹅财务集成API',
    version: '1.0.0',
    modules: {
      finance: {
        description: '财务记录管理',
        endpoints: [
          'GET /finance/records - 获取财务记录列表',
          'POST /finance/records - 创建财务记录',
          'PUT /finance/records/:id - 更新财务记录',
          'DELETE /finance/records/:id - 删除财务记录',
          'GET /finance/batch-stats/:batchNumber - 获取批次财务统计',
          'GET /finance/batch-overview - 获取所有批次财务概览',
          'GET /finance/trends - 获取财务趋势数据',
          'GET /finance/category-stats - 获取分类统计',
          'GET /finance/export - 导出财务数据'
        ]
      },
      production: {
        description: '生产记录管理（集成财务功能）',
        endpoints: [
          'POST /production/entry-records - 创建入栏记录（自动生成支出）',
          'POST /production/sale-records - 创建出栏记录（自动生成收入）',
          'POST /production/weight-records - 创建称重记录',
          'GET /production/records - 获取生产记录列表',
          'GET /production/batch-details/:batchNumber - 获取批次详细信息',
          'GET /production/active-batches - 获取活跃批次列表',
          'GET /production/generate-batch-number - 生成新的批次号',
          'GET /production/batch-overview - 获取批次统计概览',
          'POST /production/validate-batch-operation - 验证批次操作有效性'
        ]
      }
    },
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
