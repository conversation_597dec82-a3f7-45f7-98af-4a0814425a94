/* FAQ常见问题页面样式 */
.faq-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 40rpx;
}

/* 页面头部 */
.faq-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
  color: #ffffff;
}

.header-content {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.header-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.header-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
  display: block;
}

.header-stats {
  text-align: center;
}

.stats-text {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 搜索区域 */
.search-section {
  padding: 20rpx;
}

.search-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 0 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 68rpx;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  font-size: 32rpx;
  color: #999;
  padding: 8rpx;
  margin-left: 8rpx;
}

/* 标签区域 */
.tags-section {
  padding: 0 20rpx 20rpx;
}

.tags-header {
  margin-bottom: 16rpx;
}

.tags-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  padding: 8rpx 16rpx;
  background: #ffffff;
  color: #666;
  border-radius: 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.tag-item:active,
.tag-item.active {
  background: #007AFF;
  color: #ffffff;
  transform: scale(0.95);
  border-color: #007AFF;
}

/* 筛选区域 */
.filter-section {
  padding: 0 20rpx 20rpx;
}

.filter-tabs {
  display: flex;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx 16rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #007AFF;
  color: #ffffff;
  font-weight: 500;
}

/* FAQ列表区域 */
.faq-list-section {
  background: #ffffff;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.result-count {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* FAQ列表 */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.faq-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-question {
  display: flex;
  align-items: center;
  padding: 24rpx;
  transition: background 0.3s ease;
}

.faq-question:active {
  background: #e9ecef;
}

.question-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.question-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.question-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.category-tag {
  padding: 4rpx 12rpx;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.helpful-count {
  font-size: 22rpx;
  color: #999;
}

.question-actions {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.arrow-icon.expanded {
  transform: rotate(180deg);
}

/* FAQ答案 */
.faq-answer {
  padding: 0 24rpx 24rpx;
  border-top: 1rpx solid #e9ecef;
  background: #ffffff;
}

.answer-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  white-space: pre-line;
  display: block;
}

/* 答案图片 */
.answer-images {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.answer-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

/* 相关链接 */
.related-links {
  margin-bottom: 20rpx;
}

.links-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.links-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.link-item {
  font-size: 24rpx;
  color: #007AFF;
  padding: 8rpx 12rpx;
  background: #f0f8ff;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.link-item:active {
  background: #007AFF;
  color: #ffffff;
}

/* 答案操作 */
.answer-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
}

.helpful-btn,
.unhelpful-btn {
  flex: 1;
  height: 60rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  transition: all 0.3s ease;
  border: 2rpx solid #e9ecef;
  background: #ffffff;
  color: #666;
}

.helpful-btn:active,
.helpful-btn.active {
  background: #d4edda;
  color: #155724;
  border-color: #28a745;
}

.unhelpful-btn:active,
.unhelpful-btn.active {
  background: #f8d7da;
  color: #721c24;
  border-color: #dc3545;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.empty-tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 32rpx;
  display: block;
}

.ask-btn {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #ffffff;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.ask-btn:active {
  transform: scale(0.95);
}

.ask-icon {
  font-size: 26rpx;
}

.ask-text {
  font-size: 26rpx;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin: 0 20rpx 20rpx;
}

.action-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.action-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.action-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.action-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.action-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.action-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 统计区域 */
.stats-section {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  margin: 0 20rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 154, 158, 0.3);
}

.stats-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .filter-tabs {
    flex-wrap: wrap;
    gap: 8rpx;
  }

  .filter-tab {
    flex: none;
    min-width: 80rpx;
  }

  .tags-list {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .answer-actions {
    flex-direction: column;
  }
}

/* 动画效果 */
.faq-header {
  animation: slideInDown 0.6s ease-out;
}

.search-section,
.tags-section,
.filter-section,
.faq-list-section,
.quick-actions,
.stats-section {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}