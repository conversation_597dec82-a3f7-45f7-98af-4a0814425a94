const express = require("express");
const router = express.Router();
const authController = require("../controllers/auth.controller");
const { verifyToken } = require("../middleware/auth.middleware");

/**
 * 认证相关路由
 * 遵循RESTful API设计规范
 */

// 用户注册
router.post("/register", authController.register);

// 用户登录
router.post("/login", authController.login);

// 用户登出（需要认证）
router.post("/logout", verifyToken, authController.logout);

// 刷新访问令牌
router.post("/refresh", authController.refreshToken);

// 忘记密码
router.post("/forgot-password", authController.forgotPassword);

// 重置密码
router.post("/reset-password", authController.resetPassword);

// 获取当前用户信息（需要认证）
router.get("/me", verifyToken, authController.getCurrentUser);

// 更新当前用户信息（需要认证）
router.put("/me", verifyToken, authController.updateCurrentUser);

// 修改密码（需要认证）
router.put("/me/password", verifyToken, authController.changePassword);

// 验证令牌有效性
router.get("/verify", verifyToken, authController.verifyToken);

module.exports = router;
