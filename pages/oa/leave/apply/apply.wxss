@import '/styles/oa-common.wxss';
/* pages/oa/leave/apply/apply.wxss */

.apply-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx 0 40rpx 0;
}

/* ==================== 页面标题和智能提示已移除 ==================== */

/* ==================== 通用区域样式 ==================== */
.applicant-section,
.form-section {
  margin: 20rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-section:first-child {
  margin-top: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* ==================== 申请人信息 ==================== */
.applicant-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* ==================== 表单样式 ==================== */
.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required {
  color: #FF3B30;
  font-weight: bold;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #0066CC;
  background: #f8fbff;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #0066CC;
  background: #f8fbff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 选择器 */
.form-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fff;
  transition: all 0.3s ease;
}

.picker-content:active {
  border-color: #0066CC;
  background: #f8fbff;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #999;
}

.picker-arrow,
.picker-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 时间日期选择器 */
.datetime-row {
  display: flex;
  gap: 20rpx;
}

.date-picker {
  flex: 2;
}

.time-picker {
  flex: 1;
}

/* 请假天数显示 */
.days-display {
  display: flex;
  align-items: baseline;
  padding: 20rpx;
  background: linear-gradient(135deg, #0066CC15 0%, #0066CC05 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #0066CC;
}

.days-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #0066CC;
  margin-right: 8rpx;
}

.days-unit {
  font-size: 28rpx;
  color: #666;
}

/* ==================== 附件上传 ==================== */
.attachment-section {
  margin-top: 12rpx;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.attachment-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
}

.attachment-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2rpx solid #e8e8e8;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #FF3B30;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}

.upload-btn {
  width: 150rpx;
  height: 150rpx;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.upload-btn:active {
  border-color: #0066CC;
  background: #f8fbff;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 24rpx;
  color: #666;
}

.upload-tips {
  margin-top: 12rpx;
}

.upload-tips text {
  font-size: 24rpx;
  color: #999;
}

/* ==================== 操作按钮 ==================== */
.action-section {
  margin: 40rpx 20rpx 0 20rpx;
  display: flex;
  gap: 20rpx;
}

.reset-btn {
  flex: 1;
  height: 88rpx;
  border: 2rpx solid #ddd;
  border-radius: var(--radius-xl);
  background: white;
  color: #666;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-btn:disabled {
  opacity: 0.5;
}

.submit-btn {
  flex: 2;
  height: 88rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  border: none;
  border-radius: var(--radius-xl);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 102, 204, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:disabled {
  opacity: 0.7;
  box-shadow: none;
}

.submit-btn:not(:disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 102, 204, 0.3);
}

/* ==================== 底部安全距离 ==================== */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  min-height: 20rpx;
}