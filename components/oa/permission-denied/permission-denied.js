// components/oa/permission-denied/permission-denied.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 页面标题
    title: {
      type: String,
      value: '访问受限'
    },
    // 描述文本
    description: {
      type: String,
      value: '抱歉，您当前没有权限访问此功能。如需使用，请联系管理员申请相应权限。'
    },
    // 是否显示申请权限按钮
    showRequestBtn: {
      type: Boolean,
      value: true
    },
    // 申请权限按钮文本
    requestBtnText: {
      type: String,
      value: '申请权限'
    },
    // 是否显示返回按钮
    showBackBtn: {
      type: Boolean,
      value: true
    },
    // 返回按钮文本
    backBtnText: {
      type: String,
      value: '返回'
    },
    // 是否显示联系管理员按钮
    showContactBtn: {
      type: Boolean,
      value: false
    },
    // 联系管理员按钮文本
    contactBtnText: {
      type: String,
      value: '联系管理员'
    },
    // 是否显示权限详情
    showDetails: {
      type: Boolean,
      value: false
    },
    // 权限列表
    permissionList: {
      type: Array,
      value: []
    },
    // 当前用户权限级别
    userLevel: {
      type: String,
      value: 'guest'
    },
    // 需要的权限级别
    requiredLevel: {
      type: String,
      value: 'user'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    levelTextMap: {
      admin: '管理员',
      manager: '经理',
      user: '用户',
      guest: '访客'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 申请权限
     */
    onRequestPermission() {
      this.triggerEvent('request', {
        userLevel: this.properties.userLevel,
        requiredLevel: this.properties.requiredLevel,
        timestamp: Date.now()
      });
    },

    /**
     * 返回上一页
     */
    onGoBack() {
      this.triggerEvent('back', {
        action: 'goBack'
      });
      
      // 默认行为：返回上一页
      wx.navigateBack({
        delta: 1
      });
    },

    /**
     * 联系管理员
     */
    onContactAdmin() {
      this.triggerEvent('contact', {
        userLevel: this.properties.userLevel,
        requiredLevel: this.properties.requiredLevel
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 可以在这里记录访问被拒绝的日志
      try { const logger = require('../../../utils/logger.js'); logger.warn && logger.warn('Permission denied page shown', {
        userLevel: this.properties.userLevel,
        requiredLevel: this.properties.requiredLevel,
        timestamp: new Date().toISOString()
      }); } catch(_) {}
    }
  }
});