/**
 * SAAS平台数据模型定义
 * SAAS Platform Data Models Definition
 */

const { DataTypes } = require("sequelize");
const TenantDatabaseManager = require("../../models/tenant-database.model");

let models = {};

/**
 * 初始化SAAS平台模型
 */
async function initSaasModels() {
  try {
    const saasDb = await TenantDatabaseManager.initSaasConnection();

    // 平台管理员模型
    models.PlatformAdmin = saasDb.define(
      "PlatformAdmin",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        username: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true,
          comment: "用户名",
        },
        email: {
          type: DataTypes.STRING(100),
          unique: true,
          comment: "邮箱",
        },
        password: {
          type: DataTypes.STRING(255),
          allowNull: false,
          comment: "密码",
        },
        name: {
          type: DataTypes.STRING(100),
          comment: "真实姓名",
        },
        phone: {
          type: DataTypes.STRING(20),
          comment: "手机号",
        },
        role: {
          type: DataTypes.ENUM("super_admin", "admin", "operator", "support"),
          defaultValue: "admin",
          comment: "角色",
        },
        status: {
          type: DataTypes.ENUM("active", "inactive", "suspended"),
          defaultValue: "active",
          comment: "状态",
        },
        avatar: {
          type: DataTypes.STRING(500),
          comment: "头像URL",
        },
        lastLoginAt: {
          type: DataTypes.DATE,
          comment: "最后登录时间",
        },
        permissions: {
          type: DataTypes.JSON,
          comment: "权限列表",
        },
      },
      {
        tableName: "platform_admins",
        timestamps: true,
        createdAt: "createdAt",
        updatedAt: "updatedAt",
        comment: "平台管理员表",
        indexes: [
          { fields: ["username"] },
          { fields: ["email"] },
          { fields: ["role"] },
          { fields: ["status"] },
        ],
      },
    );

    // 租户模型
    models.Tenant = saasDb.define(
      "Tenant",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        tenantCode: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true,
          comment: "租户代码",
        },
        companyName: {
          type: DataTypes.STRING(200),
          allowNull: false,
          comment: "公司名称",
        },
        contactName: {
          type: DataTypes.STRING(100),
          allowNull: false,
          comment: "联系人姓名",
        },
        contactPhone: {
          type: DataTypes.STRING(20),
          allowNull: false,
          comment: "联系电话",
        },
        contactEmail: {
          type: DataTypes.STRING(100),
          allowNull: false,
          comment: "联系邮箱",
        },
        address: {
          type: DataTypes.TEXT,
          comment: "公司地址",
        },
        businessLicense: {
          type: DataTypes.STRING(100),
          comment: "营业执照号",
        },
        industry: {
          type: DataTypes.STRING(100),
          comment: "行业类型",
        },
        scale: {
          type: DataTypes.ENUM("small", "medium", "large", "enterprise"),
          defaultValue: "small",
          comment: "企业规模",
        },
        status: {
          type: DataTypes.ENUM(
            "pending",
            "active",
            "suspended",
            "expired",
            "cancelled",
          ),
          defaultValue: "pending",
          comment: "状态",
        },
        subscriptionPlan: {
          type: DataTypes.ENUM(
            "trial",
            "basic",
            "standard",
            "premium",
            "enterprise",
          ),
          defaultValue: "trial",
          comment: "订阅计划",
        },
        subscriptionStartDate: {
          type: DataTypes.DATEONLY,
          comment: "订阅开始日期",
        },
        subscriptionEndDate: {
          type: DataTypes.DATEONLY,
          comment: "订阅结束日期",
        },
        maxUsers: {
          type: DataTypes.INTEGER,
          defaultValue: 5,
          comment: "最大用户数",
        },
        maxFlocks: {
          type: DataTypes.INTEGER,
          defaultValue: 10,
          comment: "最大鹅群数",
        },
        storageLimit: {
          type: DataTypes.BIGINT,
          defaultValue: 1073741824,
          comment: "存储限制(字节)",
        },
        apiCallsLimit: {
          type: DataTypes.INTEGER,
          defaultValue: 10000,
          comment: "API调用限制/月",
        },
        customDomain: {
          type: DataTypes.STRING(200),
          comment: "自定义域名",
        },
        logo: {
          type: DataTypes.STRING(500),
          comment: "企业Logo",
        },
        theme: {
          type: DataTypes.JSON,
          comment: "主题配置",
        },
        features: {
          type: DataTypes.JSON,
          comment: "功能权限配置",
        },
        settings: {
          type: DataTypes.JSON,
          comment: "租户设置",
        },
        monthlyFee: {
          type: DataTypes.DECIMAL(10, 2),
          defaultValue: 0,
          comment: "月费",
        },
        totalRevenue: {
          type: DataTypes.DECIMAL(12, 2),
          defaultValue: 0,
          comment: "总收入",
        },
        lastActiveAt: {
          type: DataTypes.DATE,
          comment: "最后活跃时间",
        },
      },
      {
        tableName: "tenants",
        timestamps: true,
        createdAt: "createdAt",
        updatedAt: "updatedAt",
        comment: "租户表",
        indexes: [
          { fields: ["tenantCode"] },
          { fields: ["status"] },
          { fields: ["subscriptionPlan"] },
          { fields: ["subscriptionEndDate"] },
          { fields: ["createdAt"] },
        ],
      },
    );

    // 租户用户模型
    models.TenantUser = saasDb.define(
      "TenantUser",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        tenantId: {
          type: DataTypes.INTEGER,
          allowNull: false,
          comment: "租户ID",
        },
        username: {
          type: DataTypes.STRING(50),
          allowNull: false,
          comment: "用户名",
        },
        email: {
          type: DataTypes.STRING(100),
          comment: "邮箱",
        },
        password: {
          type: DataTypes.STRING(255),
          allowNull: false,
          comment: "密码",
        },
        name: {
          type: DataTypes.STRING(100),
          comment: "真实姓名",
        },
        phone: {
          type: DataTypes.STRING(20),
          comment: "手机号",
        },
        role: {
          type: DataTypes.ENUM("owner", "admin", "manager", "user"),
          defaultValue: "user",
          comment: "角色",
        },
        status: {
          type: DataTypes.ENUM("active", "inactive", "suspended"),
          defaultValue: "active",
          comment: "状态",
        },
        avatar: {
          type: DataTypes.STRING(500),
          comment: "头像URL",
        },
        lastLoginAt: {
          type: DataTypes.DATE,
          comment: "最后登录时间",
        },
        permissions: {
          type: DataTypes.JSON,
          comment: "权限列表",
        },
      },
      {
        tableName: "tenant_users",
        timestamps: true,
        createdAt: "createdAt",
        updatedAt: "updatedAt",
        comment: "租户用户表",
        indexes: [
          { fields: ["tenantId"] },
          { fields: ["username"] },
          { fields: ["email"] },
          { fields: ["role"] },
          { fields: ["status"] },
          {
            fields: ["tenantId", "username"],
            unique: true,
            name: "uk_tenant_username",
          },
        ],
      },
    );

    // 平台使用统计模型
    models.PlatformUsageStats = saasDb.define(
      "PlatformUsageStats",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        tenantId: {
          type: DataTypes.INTEGER,
          allowNull: false,
          comment: "租户ID",
        },
        statDate: {
          type: DataTypes.DATEONLY,
          allowNull: false,
          comment: "统计日期",
        },
        apiCalls: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
          comment: "API调用次数",
        },
        dataOperations: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
          comment: "数据操作次数",
        },
        storageUsed: {
          type: DataTypes.BIGINT,
          defaultValue: 0,
          comment: "存储使用量(字节)",
        },
        bandwidth: {
          type: DataTypes.BIGINT,
          defaultValue: 0,
          comment: "带宽使用量(字节)",
        },
        activeUsers: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
          comment: "活跃用户数",
        },
      },
      {
        tableName: "platform_usage_stats",
        timestamps: true,
        createdAt: "createdAt",
        updatedAt: "updatedAt",
        comment: "平台使用统计表",
        indexes: [
          { fields: ["tenantId"] },
          { fields: ["statDate"] },
          {
            fields: ["tenantId", "statDate"],
            unique: true,
            name: "uk_tenant_stat_date",
          },
        ],
      },
    );

    // 设置关联关系
    models.Tenant.hasMany(models.TenantUser, {
      foreignKey: "tenantId",
      as: "users",
    });
    models.TenantUser.belongsTo(models.Tenant, {
      foreignKey: "tenantId",
      as: "tenant",
    });

    models.Tenant.hasMany(models.PlatformUsageStats, {
      foreignKey: "tenantId",
      as: "usageStats",
    });
    models.PlatformUsageStats.belongsTo(models.Tenant, {
      foreignKey: "tenantId",
      as: "tenant",
    });

    try { const { Logger } = require('../../middleware/errorHandler'); Logger.info("✅ SAAS平台模型初始化完成"); } catch(_) {}

    return models;
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("❌ SAAS平台模型初始化失败:", error); } catch(_) {}

    throw error;
  }
}

// 获取模型实例
function getModels() {
  return models;
}

module.exports = {
  initSaasModels,
  getModels,
  models,
};
