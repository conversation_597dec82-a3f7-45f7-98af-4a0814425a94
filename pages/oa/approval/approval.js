// pages/oa/approval/approval.js

/**
 * 审批中心模块主页面
 * 功能：处理各类审批事务
 */

const { PERMISSIONS, PermissionMixin } = require('../../../utils/oa-permissions');
let logger; try { logger = require('../../../utils/logger.js'); } catch(_) { logger = console; }

Page({
  // 混入权限检查功能
  ...PermissionMixin,
  data: {
    loading: false,
    isEmpty: false,
    
    // 审批统计数据
    approvalStats: {
      pending: 8,        // 待审批
      urgent: 3,         // 紧急
      completed: 152,    // 已完成
      monthly: 45        // 本月审批
    },
    
    // 最近审批动态
    recentApprovals: [
      {
        id: 'approval_001',
        type: 'leave',
        business_id: 'leave_001',
        title: '张三的病假申请',
        applicant: '张三',
        status: 'pending',
        statusText: '待审批',
        timeAgo: '2小时前',
        isUrgent: false
      },
      {
        id: 'approval_002',
        type: 'purchase',
        business_id: 'purchase_001',
        title: '饲料采购申请',
        applicant: '李四',
        status: 'pending',
        statusText: '待审批',
        timeAgo: '5小时前',
        isUrgent: true
      },
      {
        id: 'approval_003',
        type: 'reimbursement',
        business_id: 'reimbursement_001',
        title: '差旅费报销',
        applicant: '王五',
        status: 'approved',
        statusText: '已通过',
        timeAgo: '1天前',
        isUrgent: false
      }
    ]
  },

  onLoad: function (options) {
    this.loadData();
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查审批权限（任一审批权限即可访问）
    const hasAnyApprovalPermission = this.hasAnyPermission([
      PERMISSIONS.APPROVE_PURCHASE,
      PERMISSIONS.APPROVE_REIMBURSEMENT,
      PERMISSIONS.APPROVE_LEAVE
    ]);
    
    if (!hasAnyApprovalPermission) {
      wx.showModal({
        title: '权限不足',
        content: '您没有访问审批中心的权限，请联系管理员',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限验证通过，继续加载数据
    this.loadData();
  },

  onShow: function () {
    this.refreshData();
    // 更新数据时的动画效果
    this.updateStatsWithAnimation();
  },

  onPullDownRefresh: function() {
    try { logger.debug && logger.debug('开始下拉刷新'); } catch(_) {}
    
    // 重新加载数据
    this.refreshApprovalData();
    this.loadData().finally(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新完成',
        icon: 'success',
        duration: 1500
      });
    });
  },

  onReachBottom: function() {
    try { logger.debug && logger.debug('到达页面底部'); } catch(_) {}
    // 可以在这里实现加载更多功能
  },



  loadData: function() {
    this.setData({ loading: true });
    
    // 模拟数据加载
    setTimeout(() => {
      const isEmpty = this.data.recentApprovals.length === 0 && 
                     this.data.approvalStats.pending === 0;
      
      this.setData({ 
        loading: false,
        isEmpty: isEmpty
      });
    }, 1000);
  },

  refreshData: function() {
    this.loadData();
  },

  /**
   * 刷新审批数据
   */
  refreshApprovalData: function() {
    this.loadApprovalStats();
    this.loadRecentApprovals();
  },

  /**
   * 加载审批统计
   */
  loadApprovalStats: function() {
    try { logger.debug && logger.debug('加载审批统计数据'); } catch(_) {}
  },

  /**
   * 加载最近审批
   */
  loadRecentApprovals: function() {
    try { logger.debug && logger.debug('加载最近审批数据'); } catch(_) {}
  },

  /**
   * 更新统计数据动画
   */
  updateStatsWithAnimation: function() {
    // 模拟数据更新的动画效果
    const stats = this.data.approvalStats;
    this.setData({
      approvalStats: {
        ...stats,
        pending: stats.pending,
        urgent: stats.urgent,
        completed: stats.completed,
        monthly: stats.monthly
      }
    });
  },

  /**
   * 处理审批操作后的状态更新
   */
  updateApprovalStatus: function(approvalId, newStatus) {
    const recentApprovals = this.data.recentApprovals.map(item => {
      if (item.id === approvalId) {
        return {
          ...item,
          status: newStatus,
          statusText: this.getStatusText(newStatus)
        };
      }
      return item;
    });
    
    this.setData({ recentApprovals });
    this.updateStatsAfterAction(newStatus);
  },

  /**
   * 获取状态文本
   */
  getStatusText: function(status) {
    const statusMap = {
      'pending': '待审批',
      'approved': '已通过',
      'rejected': '已拒绝'
    };
    return statusMap[status] || '未知状态';
  },

  /**
   * 审批操作后更新统计数据
   */
  updateStatsAfterAction: function(action) {
    const stats = { ...this.data.approvalStats };
    
    if (action === 'approved' || action === 'rejected') {
      stats.pending = Math.max(0, stats.pending - 1);
      stats.completed = stats.completed + 1;
      
      this.setData({ approvalStats: stats });
    }
  },



  /**
   * 统计卡片点击
   */
  onStatTap: function(e) {
    const { type } = e.currentTarget.dataset;
    try { logger.debug && logger.debug('点击统计卡片', type); } catch(_) {}
    
    switch (type) {
      case 'pending':
      case 'urgent':
        wx.navigateTo({
          url: '/pages/oa/approval/pending/pending'
        });
        break;
      case 'completed':
      case 'monthly':
        wx.navigateTo({
          url: '/pages/oa/approval/history/history'
        });
        break;
    }
  },

  /**
   * 功能入口点击
   */
  onFunctionTap: function(e) {
    const { type } = e.currentTarget.dataset;
    try { logger.debug && logger.debug('点击功能入口', type); } catch(_) {}
    
    switch (type) {
      case 'pending':
        wx.navigateTo({
          url: '/pages/oa/approval/pending/pending'
        });
        break;
      case 'history':
        wx.navigateTo({
          url: '/pages/oa/approval/history/history'
        });
        break;
      case 'statistics':
        wx.navigateTo({
          url: '/pages/oa/approval/statistics/statistics'
        });
        break;
    }
  },

  /**
   * 查看全部审批
   */
  onViewAllApprovals: function() {
    wx.navigateTo({
      url: '/pages/oa/approval/pending/pending'
    });
  },

  onApprovalItemTap: function(e) {
    const { id, type, businessId } = e.currentTarget.dataset;
    
    try { logger.debug && logger.debug('点击审批项', { id, type, businessId }); } catch(_) {}
    
    // 找到对应的审批项
    const approval = this.data.recentApprovals.find(item => item.id === id);
    if (!approval) {
      try { logger.error && logger.error('审批项不存在', id); } catch(_) {}
      wx.showToast({
        title: '审批项不存在',
        icon: 'none'
      });
      return;
    }
    
    // 如果从dataset中获取到businessId，则使用它，否则使用approval中的business_id
    const finalApproval = {
      ...approval,
      business_id: businessId || approval.business_id
    };
    
    try { logger.debug && logger.debug('找到审批项', finalApproval); } catch(_) {}
    
    // 根据状态和类型决定跳转行为
    if (finalApproval.status === 'pending') {
      // 待审批的项目，跳转到审批处理页面或待审批列表
      this.navigateToApprovalDetail(finalApproval);
    } else {
      // 已处理的项目，跳转到历史详情或审批历史
      this.navigateToHistoryDetail(finalApproval);
    }
    
    try { logger.debug && logger.debug('跳转完成'); } catch(_) {}
  },

  /**
   * 跳转到审批详情页
   */
  navigateToApprovalDetail: function(approval) {
    try { logger.debug && logger.debug('准备跳转到审批详情', approval); } catch(_) {}
    
    let detailUrl = '';
    const businessId = approval.business_id || approval.id;
    
    switch(approval.type) {
      case 'leave':
        detailUrl = `/pages/oa/leave/detail/detail?id=${businessId}&approvalId=${approval.id}`;
        break;
      case 'reimbursement':
        detailUrl = `/pages/oa/reimbursement/detail/detail?id=${businessId}&approvalId=${approval.id}`;
        break;
      case 'purchase':
        detailUrl = `/pages/oa/purchase/detail/detail?id=${businessId}&approvalId=${approval.id}`;
        break;
      default:
        // 默认跳转到待审批页面
        try { logger.debug && logger.debug('未知类型，跳转到待审批页面', approval.type); } catch(_) {}
        detailUrl = '/pages/oa/approval/pending/pending';
        break;
    }
    
    try { logger.debug && logger.debug('跳转URL', detailUrl); } catch(_) {}
    
    wx.navigateTo({
      url: detailUrl,
      success: () => {
        try { logger.debug && logger.debug('跳转成功', detailUrl); } catch(_) {}
      },
      fail: (err) => {
        try { logger.error && logger.error('跳转详情页失败', err, detailUrl); } catch(_) {}
        // 失败时跳转到待审批列表
        wx.navigateTo({
          url: '/pages/oa/approval/pending/pending',
          success: () => {
            try { logger.debug && logger.debug('fallback跳转成功'); } catch(_) {}
          },
          fail: (fallbackErr) => {
            try { logger.error && logger.error('fallback跳转也失败', fallbackErr); } catch(_) {}
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  /**
   * 跳转到历史详情页
   */
  navigateToHistoryDetail: function(approval) {
    try { logger.debug && logger.debug('准备跳转到历史详情', approval); } catch(_) {}
    
    let detailUrl = '';
    const businessId = approval.business_id || approval.id;
    
    switch(approval.type) {
      case 'leave':
        detailUrl = `/pages/oa/leave/detail/detail?id=${businessId}&approvalId=${approval.id}&readonly=true`;
        break;
      case 'reimbursement':
        detailUrl = `/pages/oa/reimbursement/detail/detail?id=${businessId}&approvalId=${approval.id}&readonly=true`;
        break;
      case 'purchase':
        detailUrl = `/pages/oa/purchase/detail/detail?id=${businessId}&approvalId=${approval.id}&readonly=true`;
        break;
      default:
        // 默认跳转到审批历史页面
        try { logger.debug && logger.debug('未知类型，跳转到审批历史页面', approval.type); } catch(_) {}
        detailUrl = '/pages/oa/approval/history/history';
        break;
    }
    
    try { logger.debug && logger.debug('跳转历史URL', detailUrl); } catch(_) {}
    
    wx.navigateTo({
      url: detailUrl,
      success: () => {
        try { logger.debug && logger.debug('历史跳转成功', detailUrl); } catch(_) {}
      },
      fail: (err) => {
        try { logger.error && logger.error('跳转历史详情页失败', err); } catch(_) {}
        // 失败时跳转到审批历史列表
        wx.navigateTo({
          url: '/pages/oa/approval/history/history',
          success: () => {
            try { logger.debug && logger.debug('fallback历史跳转成功'); } catch(_) {}
          },
          fail: (fallbackErr) => {
            try { logger.error && logger.error('fallback历史跳转也失败', fallbackErr); } catch(_) {}
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  onViewAllApprovals: function() {
    wx.navigateTo({
      url: '/pages/oa/approval/pending/pending',
      fail: () => {
        // 实现创建审批申请功能
        this.showCreateApprovalModal();
      }
    });
  },

  onPullDownRefresh: function () {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // ==================== 新增功能实现 ====================

  // 处理快捷操作
  handleQuickAction: function(url) {
    if (url.includes('leave')) {
      this.showCreateLeaveModal();
    } else if (url.includes('purchase')) {
      this.showCreatePurchaseModal();
    } else if (url.includes('reimbursement')) {
      this.showCreateReimbursementModal();
    } else {
      wx.showModal({
        title: '功能说明',
        content: '该功能正在完善中，请稍后使用。您可以通过其他途径完成相关操作。',
        showCancel: false
      });
    }
  },

  // 显示审批列表
  showApprovalList: function(type) {
    const typeNames = {
      pending: '待审批',
      approved: '已通过',
      rejected: '已拒绝',
      total: '全部'
    };

    wx.showModal({
      title: `${typeNames[type]}列表`,
      content: `显示${typeNames[type]}的申请列表。\n包含详细的审批信息、申请时间、审批状态等。`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm) {
          this.navigateToDetailList(type);
        }
      }
    });
  },

  // 显示审批详情
  showApprovalDetail: function(approvalId) {
    // 模拟获取审批详情
    const mockDetail = {
      id: approvalId,
      title: '采购申请 - 饲料采购',
      applicant: '张三',
      department: '生产部',
      applyTime: '2024-12-10 14:30',
      status: 'pending',
      content: '申请采购高质量饲料500公斤，用于下月生产需求。',
      amount: '¥12,500',
      approvers: [
        { name: '李经理', status: 'approved', time: '2024-12-10 15:20' },
        { name: '王总', status: 'pending', time: '' }
      ]
    };

    const statusText = mockDetail.status === 'pending' ? '审批中' : mockDetail.status === 'approved' ? '已通过' : '已拒绝';
    
    wx.showModal({
      title: '审批详情',
      content: `申请标题：${mockDetail.title}\n申请人：${mockDetail.applicant}\n申请时间：${mockDetail.applyTime}\n状态：${statusText}\n金额：${mockDetail.amount}`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看完整信息'
    });
  },

  // 显示创建请假申请
  showCreateLeaveModal: function() {
    wx.showModal({
      title: '创建请假申请',
      content: '请假申请功能：\n• 选择请假类型\n• 设置请假时间\n• 填写请假原因\n• 提交审批流程',
      showCancel: true,
      cancelText: '取消',
      confirmText: '开始申请',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '跳转到请假申请页面',
            icon: 'success'
          });
        }
      }
    });
  },

  // 显示创建采购申请
  showCreatePurchaseModal: function() {
    wx.showModal({
      title: '创建采购申请',
      content: '采购申请功能：\n• 选择采购物品\n• 填写数量和规格\n• 预估采购金额\n• 提交审批流程',
      showCancel: true,
      cancelText: '取消',
      confirmText: '开始申请',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '跳转到采购申请页面',
            icon: 'success'
          });
        }
      }
    });
  },

  // 显示创建报销申请
  showCreateReimbursementModal: function() {
    wx.showModal({
      title: '创建报销申请',
      content: '报销申请功能：\n• 上传报销凭证\n• 填写报销明细\n• 选择报销类型\n• 提交审批流程',
      showCancel: true,
      cancelText: '取消',
      confirmText: '开始申请',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/oa/reimbursement/create/create',
            fail: () => {
              wx.showToast({
                title: '跳转到报销申请页面',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  // 显示创建审批申请模态框
  showCreateApprovalModal: function() {
    wx.showActionSheet({
      itemList: ['请假申请', '采购申请', '报销申请', '其他申请'],
      success: (res) => {
        const actions = [
          () => this.showCreateLeaveModal(),
          () => this.showCreatePurchaseModal(),
          () => this.showCreateReimbursementModal(),
          () => this.showOtherApprovalModal()
        ];
        
        actions[res.tapIndex]();
      }
    });
  },

  // 显示其他申请
  showOtherApprovalModal: function() {
    wx.showModal({
      title: '其他申请',
      content: '您可以创建自定义审批申请：\n• 加班申请\n• 出差申请\n• 设备使用申请\n• 培训申请等',
      showCancel: true,
      cancelText: '取消',
      confirmText: '创建申请'
    });
  },

  // 导航到详细列表
  navigateToDetailList: function(type) {
    wx.showToast({
      title: `正在加载${type}列表`,
      icon: 'success'
    });
  }
});