/**
 * 统一响应工具类
 * Unified Response Helper for API responses
 */

class ResponseHelper {
  /**
   * 成功响应
   * @param {Object} res Express响应对象
   * @param {*} data 响应数据
   * @param {string} message 响应消息
   * @param {number} code HTTP状态码
   */
  static success(res, data = null, message = "操作成功", code = 200) {
    return res.status(code).json({
      success: true,
      code,
      message,
      data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 错误响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   * @param {number} code HTTP状态码
   * @param {*} error 错误详情（开发环境）
   */
  static error(res, message = "操作失败", code = 500, error = null) {
    return res.status(code).json({
      success: false,
      code,
      message,
      error: process.env.NODE_ENV === "development" ? error : null,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 分页响应
   * @param {Array} data 数据列表
   * @param {number} page 当前页码
   * @param {number} limit 每页数量
   * @param {number} total 总数量
   */
  static paginate(data, page, limit, total) {
    return {
      items: data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(total),
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * 分页成功响应
   * @param {Object} res Express响应对象
   * @param {Array} data 数据列表
   * @param {number} page 当前页码
   * @param {number} limit 每页数量
   * @param {number} total 总数量
   * @param {string} message 响应消息
   */
  static paginatedSuccess(
    res,
    data,
    page,
    limit,
    total,
    message = "获取数据成功",
  ) {
    const paginatedData = this.paginate(data, page, limit, total);
    return this.success(res, paginatedData, message);
  }

  /**
   * 验证错误响应
   * @param {Object} res Express响应对象
   * @param {Array|string} errors 验证错误
   */
  static validationError(res, errors) {
    return res.status(400).json({
      success: false,
      code: 400,
      message: "数据验证失败",
      errors: Array.isArray(errors) ? errors : [errors],
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 未找到资源响应
   * @param {Object} res Express响应对象
   * @param {string} resource 资源名称
   */
  static notFound(res, resource = "资源") {
    return res.status(404).json({
      success: false,
      code: 404,
      message: `${resource}不存在`,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 未授权响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   */
  static unauthorized(res, message = "未授权访问") {
    return res.status(401).json({
      success: false,
      code: 401,
      message,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 禁止访问响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   */
  static forbidden(res, message = "权限不足") {
    return res.status(403).json({
      success: false,
      code: 403,
      message,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 冲突响应（如重复数据）
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   */
  static conflict(res, message = "数据冲突") {
    return res.status(409).json({
      success: false,
      code: 409,
      message,
      timestamp: new Date().toISOString(),
    });
  }
}

module.exports = ResponseHelper;
