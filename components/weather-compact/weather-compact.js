// components/weather-compact/weather-compact.js
Component({
  properties: {
    autoLocation: {
      type: Boolean,
      value: true
    }
  },

  data: {
    currentTemp: '28',
    weatherDesc: '晴',
    weatherIcon: '/assets/icons/weather/sunny.png',
    loading: false
  },

  lifetimes: {
    attached() {
      this.getWeatherData();
    }
  },

  methods: {
    // 获取天气数据
    getWeatherData() {
      // 立即设置默认数据，避免空白
      const defaultData = this.getMockWeatherData();
      this.setData({
        ...defaultData,
        loading: false
      });

      // 模拟API调用获取真实数据
      setTimeout(() => {
        const weatherData = this.getMockWeatherData();
        this.setData({
          ...weatherData,
          loading: false
        });
      }, 1000);
    },

    // 获取模拟天气数据 - 固定显示与截图一致的数据
    getMockWeatherData() {
      return {
        currentTemp: '28',
        weatherDesc: '晴',
        weatherIcon: '/assets/icons/weather/sunny.png'
      };
    },

    // 点击天气组件跳转到详细页面
    onWeatherTap() {
      wx.navigateTo({
        url: '/pages/weather/weather-detail/weather-detail'
      });
    }
  }
});
