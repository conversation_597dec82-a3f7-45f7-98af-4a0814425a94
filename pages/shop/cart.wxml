<!-- pages/shop/cart.wxml -->
<view class="container">
  <view class="cart-header">
    <text class="header-title">购物车</text>
  </view>
  
  <view class="cart-content" wx:if="{{cartItems.length > 0}}">
    <!-- 全选 -->
    <view class="select-all">
      <view class="checkbox {{isAllSelected ? 'checked' : ''}}" bindtap="onSelectAll">
        <text wx:if="{{isAllSelected}}" class="checkmark">✓</text>
      </view>
      <text>全选</text>
    </view>
    
    <!-- 购物车商品列表 -->
    <view class="cart-items">
      <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
        <!-- 选择框 -->
        <view class="checkbox {{item.selected ? 'checked' : ''}}" bindtap="onSelectItem" data-id="{{item.id}}">
          <text wx:if="{{item.selected}}" class="checkmark">✓</text>
        </view>
        
        <!-- 商品信息 -->
        <view class="item-info">
          <image class="item-image" src="{{item.image || '/images/icons/goods1.png'}}" mode="aspectFill"></image>
          <view class="item-details">
            <view class="item-name">{{item.name}}</view>
            <view class="item-price">¥{{item.price}}</view>
          </view>
        </view>
        
        <!-- 数量控制 -->
        <view class="quantity-controls">
          <view class="quantity-btn minus" bindtap="onQuantityMinus" data-id="{{item.id}}">-</view>
          <input class="quantity-input" value="{{item.quantity}}" bindinput="onQuantityChange" data-id="{{item.id}}" type="number" />
          <view class="quantity-btn plus" bindtap="onQuantityPlus" data-id="{{item.id}}">+</view>
        </view>
        
        <!-- 删除按钮 -->
        <view class="delete-btn" bindtap="onDeleteItem" data-id="{{item.id}}">删除</view>
      </view>
    </view>
  </view>
  
  <!-- 空购物车 -->
  <view class="empty-cart" wx:else>
    <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
    <text class="empty-text">购物车为空</text>
    <navigator class="go-shopping" url="/pages/shop/shop">去逛逛</navigator>
  </view>
  
  <!-- 结算栏 -->
  <view class="checkout-bar" wx:if="{{cartItems.length > 0}}">
    <view class="total-price">
      <text>合计: </text>
      <text class="price">¥{{totalPrice}}</text>
    </view>
    <view class="checkout-btn" bindtap="onCheckout">
      <text>结算({{selectedCount}})</text>
    </view>
  </view>
</view>