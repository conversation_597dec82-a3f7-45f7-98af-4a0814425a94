// pages/logistics/logistics.js
Page({
  data: {
    orderId: '',
    logisticsInfo: {},
    orderInfo: {}
  },

  onLoad(options) {
    this.setData({
      orderId: options.orderId || ''
    });
    this.loadLogisticsInfo();
  },

  // 加载物流信息
  loadLogisticsInfo() {
    // 模拟物流数据
    const logisticsInfo = {
      status: '运输中',
      statusDesc: '您的包裹正在运输途中，预计明天送达',
      company: '顺丰速运',
      trackingNumber: 'SF1234567890123',
      progress: [
        {
          id: '1',
          time: '2024-01-16 14:30',
          description: '快件已到达【北京朝阳区营业点】',
          location: '北京朝阳区'
        },
        {
          id: '2',
          time: '2024-01-16 10:15',
          description: '快件已从【北京转运中心】发出，下一站【北京朝阳区营业点】',
          location: '北京转运中心'
        },
        {
          id: '3',
          time: '2024-01-15 22:45',
          description: '快件已到达【北京转运中心】',
          location: '北京转运中心'
        },
        {
          id: '4',
          time: '2024-01-15 18:20',
          description: '快件已从【上海转运中心】发出，下一站【北京转运中心】',
          location: '上海转运中心'
        },
        {
          id: '5',
          time: '2024-01-15 15:30',
          description: '快件已到达【上海转运中心】',
          location: '上海转运中心'
        },
        {
          id: '6',
          time: '2024-01-15 12:00',
          description: '商家已发货',
          location: '上海市'
        }
      ]
    };

    const orderInfo = {
      orderNumber: 'GO202401150001',
      createTime: '2024-01-15 10:30',
      address: '北京市朝阳区建国路88号SOHO现代城A座1001室',
      receiver: '张三',
      phone: '138****8888',
      goods: [
        {
          id: '1',
          name: '优质鹅饲料 营养配方',
          price: '99.99',
          quantity: 2,
          image: '/images/icons/goods1.png'
        },
        {
          id: '2',
          name: '疫苗套装 预防疾病',
          price: '199.99',
          quantity: 1,
          image: '/images/icons/goods2.png'
        }
      ]
    };

    this.setData({
      logisticsInfo,
      orderInfo
    });
  },

  // 复制运单号
  onCopyTrackingNumber() {
    const trackingNumber = this.data.logisticsInfo.trackingNumber;
    wx.setClipboardData({
      data: trackingNumber,
      success: () => {
        wx.showToast({
          title: '运单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  }
});
