const ProductionRecord = require("../models/production-record.model");
const UnifiedInventory = require("../models/unified-inventory.model");
const { Op } = require("sequelize");
const ExcelJS = require("exceljs");
const moment = require("moment");
const ResponseHelper = require("../utils/response-helper");
const ValidationHelper = require("../utils/validation-helper");

// 数据库配置（保留用于生产记录等其他功能）
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USERNAME || "zhihuiyange",
  password: process.env.DB_PASSWORD || "zhihuiyange123",
  database: process.env.DB_NAME || "zhihuiyange_local",
  charset: "utf8mb4",
};

/**
 * 生产记录控制器 - 清理版本
 * 移除了重复的materials相关方法，这些功能现在由inventory.controller.js处理
 * 只保留生产记录相关的核心功能
 */

// ================================
// 生产记录基础CRUD操作
// ================================

// 获取生产记录列表
exports.getRecords = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = "",
      startDate = "",
      endDate = "",
      sortBy = "created_at",
      sortOrder = "DESC",
      batchNumber = "",
      status = "",
    } = req.query;

    // 验证分页参数
    if (
      !ValidationHelper.validateNumber(page, 1) ||
      !ValidationHelper.validateNumber(limit, 1, 100)
    ) {
      return ResponseHelper.error(res, "分页参数无效", 400);
    }

    // 验证日期范围
    if (!ValidationHelper.validateDateRange(startDate, endDate)) {
      return ResponseHelper.error(res, "日期范围无效", 400);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    const whereConditions = { userId: req.user.id };

    // 搜索条件
    if (search) {
      whereConditions[Op.or] = [
        { notes: { [Op.like]: `%${search}%` } },
        { batchNumber: { [Op.like]: `%${search}%` } },
      ];
    }

    // 日期范围筛选
    if (startDate && endDate) {
      whereConditions.date = {
        [Op.between]: [startDate, endDate],
      };
    }

    // 批次号筛选
    if (batchNumber) {
      whereConditions.batchNumber = { [Op.like]: `%${batchNumber}%` };
    }

    // 状态筛选
    if (status) {
      whereConditions.status = status;
    }

    // 查询记录
    const { count, rows: records } = await ProductionRecord.findAndCountAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: offset,
      order: [[sortBy, sortOrder]],
    });

    res.json({
      success: true,
      data: {
        records,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / parseInt(limit)),
        },
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取生产记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取生产记录失败",
      error: error.message,
    });
  }
};

// 创建生产记录
exports.createRecord = async (req, res) => {
  try {
    const recordData = {
      ...req.body,
      userId: req.user.id,
    };

    const record = await ProductionRecord.create(recordData);

    res.status(201).json({
      success: true,
      data: { record },
      message: "生产记录创建成功",
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('创建生产记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "创建生产记录失败",
      error: error.message,
    });
  }
};

// 获取生产记录详情
exports.getRecordById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const record = await ProductionRecord.findOne({
      where: { id, userId },
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: "生产记录不存在",
      });
    }

    res.json({
      success: true,
      data: { record },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取生产记录详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取生产记录详情失败",
      error: error.message,
    });
  }
};

// 更新生产记录
exports.updateRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const [updateCount] = await ProductionRecord.update(req.body, {
      where: { id, userId },
    });

    if (updateCount === 0) {
      return res.status(404).json({
        success: false,
        message: "生产记录不存在或无权限更新",
      });
    }

    const record = await ProductionRecord.findOne({
      where: { id, userId },
    });

    res.json({
      success: true,
      data: { record },
      message: "生产记录更新成功",
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('更新生产记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "更新生产记录失败",
      error: error.message,
    });
  }
};

// 删除生产记录
exports.deleteRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const deleteCount = await ProductionRecord.destroy({
      where: { id, userId },
    });

    if (deleteCount === 0) {
      return res.status(404).json({
        success: false,
        message: "生产记录不存在或无权限删除",
      });
    }

    res.json({
      success: true,
      message: "生产记录删除成功",
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('删除生产记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "删除生产记录失败",
      error: error.message,
    });
  }
};

// ================================
// 生产记录关联功能
// ================================

// 获取记录关联的物料使用情况
exports.getRecordMaterials = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 验证记录存在
    const record = await ProductionRecord.findOne({
      where: { id, userId },
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: "生产记录不存在",
      });
    }

    // 这里应该查询记录关联的物料使用情况
    // 根据业务逻辑查询相关物料
    const materials = [
      {
        id: 1,
        name: "饲料A",
        category: "饲料",
        quantity: 100,
        unit: "kg",
        usedAt: record.date,
      },
      {
        id: 2,
        name: "药品B",
        category: "药品",
        quantity: 2,
        unit: "ml",
        usedAt: record.date,
      },
    ];

    res.json({
      success: true,
      data: {
        recordId: id,
        materials: materials,
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取记录物料失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: process.env.NODE_ENV === "development" ? error.message : {},
    });
  }
};

// ================================
// 统计和分析功能
// ================================

// 获取生产记录统计
exports.getRecordStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;

    // 构建查询条件
    const whereConditions = { userId };
    if (startDate && endDate) {
      whereConditions.date = {
        [Op.between]: [startDate, endDate],
      };
    }

    // 统计数据
    const totalRecords = await ProductionRecord.count({
      where: whereConditions,
    });

    const stats = {
      totalRecords,
      recordsByType: {},
      recordsByStatus: {},
      period: { startDate, endDate },
    };

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取统计数据失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取统计数据失败",
      error: error.message,
    });
  }
};

// ================================
// 数据导出功能
// ================================

// 导出记录为Excel
exports.exportRecordsExcel = async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;

    // 构建查询条件
    const whereConditions = { userId };
    if (startDate && endDate) {
      whereConditions.date = {
        [Op.between]: [startDate, endDate],
      };
    }

    const records = await ProductionRecord.findAll({
      where: whereConditions,
      order: [["date", "DESC"]],
    });

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("生产记录");

    // 设置列头
    worksheet.columns = [
      { header: "ID", key: "id", width: 10 },
      { header: "日期", key: "date", width: 15 },
      { header: "类型", key: "type", width: 15 },
      { header: "批次号", key: "batchNumber", width: 20 },
      { header: "状态", key: "status", width: 15 },
      { header: "备注", key: "notes", width: 30 },
    ];

    // 添加数据
    records.forEach((record) => {
      worksheet.addRow({
        id: record.id,
        date: record.date,
        type: record.type,
        batchNumber: record.batchNumber,
        status: record.status,
        notes: record.notes,
      });
    });

    // 设置响应头
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    );
    res.setHeader(
      "Content-Disposition",
      "attachment; filename=production-records.xlsx",
    );

    // 发送文件
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('导出Excel失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "导出Excel失败",
      error: error.message,
    });
  }
};

// 部分更新生产记录
exports.patchRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 验证ID
    if (!ValidationHelper.validateNumber(id, 1)) {
      return ResponseHelper.error(res, "记录ID无效", 400);
    }

    // 查找记录
    const record = await ProductionRecord.findByPk(id);
    if (!record) {
      return ResponseHelper.error(res, "生产记录不存在", 404);
    }

    // 更新记录（只更新提供的字段）
    await record.update(updateData);

    return ResponseHelper.success(res, record, "生产记录更新成功");
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('更新生产记录错误', { error: error.message, stack: error.stack }); } catch(_) {}
    return ResponseHelper.error(res, "更新生产记录失败: " + error.message, 500);
  }
};

// ================================
// V2 版本的生产记录方法
// ================================

// 获取V2生产记录列表 (兼容前端的新格式)
exports.getRecordsV2 = async (req, res) => {
  try {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.debug('getRecordsV2 被调用', { query: req.query }); } catch(_) {}

    // 暂时返回基础的成功响应
    const records = await ProductionRecord.findAll({
      limit: parseInt(req.query.limit) || 20,
      offset:
        ((parseInt(req.query.page) || 1) - 1) *
        (parseInt(req.query.limit) || 20),
      order: [["created_at", "DESC"]],
    });

    return ResponseHelper.success(
      res,
      {
        records: records || [],
        total: await ProductionRecord.count(),
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
      },
      "获取生产记录成功",
    );
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('getRecordsV2 错误', { error: error.message, stack: error.stack }); } catch(_) {}
    return ResponseHelper.error(res, "获取生产记录失败: " + error.message, 500);
  }
};

// 创建V2生产记录 (兼容前端的新格式)
exports.createRecordV2 = async (req, res) => {
  try {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.debug('createRecordV2 被调用', { body: req.body }); } catch(_) {}

    // 基础验证
    if (!req.body.userId) {
      return ResponseHelper.error(res, "用户ID是必需的", 400);
    }

    // 创建记录
    const record = await ProductionRecord.create({
      userId: req.body.userId,
      recordDate: req.body.recordDate || new Date(),
      recordType: req.body.recordType || "daily",
      batchNumber: req.body.batchNumber || `BATCH_${Date.now()}`,
      description: req.body.description || "",
      status: "active",
    });

    return ResponseHelper.success(res, record, "创建生产记录成功");
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('createRecordV2 错误', { error: error.message, stack: error.stack }); } catch(_) {}
    return ResponseHelper.error(res, "创建生产记录失败: " + error.message, 500);
  }
};

module.exports = {
  // 导出所有方法以便其他文件引用
  getRecords: exports.getRecords,
  createRecord: exports.createRecord,
  getRecordById: exports.getRecordById,
  updateRecord: exports.updateRecord,
  patchRecord: exports.patchRecord,
  deleteRecord: exports.deleteRecord,
  getRecordMaterials: exports.getRecordMaterials,
  getRecordStats: exports.getRecordStats,
  exportRecordsExcel: exports.exportRecordsExcel,
  // V2 方法
  getRecordsV2: exports.getRecordsV2,
  createRecordV2: exports.createRecordV2,
};
