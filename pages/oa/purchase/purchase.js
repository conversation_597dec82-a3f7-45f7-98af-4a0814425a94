// pages/oa/purchase/purchase.js

/**
 * 采购申请模块主页面
 * 功能：采购申请管理、查看采购记录
 */

const request = require('../../../utils/request.js');
const { API } = require('../../../constants/index.js');

Page({
  data: {
    // 页面状态
    loading: false,
    
    // 快捷操作
    quickActions: [
      {
        id: 'apply-purchase',
        title: '申请采购',
        description: '提交新的采购申请',
        iconText: '➕',
        url: '/pages/oa/purchase/apply/apply',
        color: '#0066CC'
      },
      {
        id: 'my-purchases',
        title: '我的申请',
        description: '查看我的采购记录',
        iconText: '📋',
        url: '/pages/oa/purchase/list/list?type=my',
        color: '#00A86B'
      }
    ],
    
    // 统计数据
    statistics: {
      pending: 3,      // 待审批
      approved: 12,    // 已批准
      rejected: 1,     // 已拒绝
      total: 16        // 总数
    },
    
    // 最近采购记录
    recentPurchases: [
      {
        id: 'purchase_001',
        category: '饲料用品',
        items: '优质鹅饲料 50袋',
        amount: 2500.00,
        status: 'pending',
        statusText: '待审批',
        statusColor: '#FF9500',
        submitTime: '2024-01-10 14:30',
        urgency: 'normal'
      },
      {
        id: 'purchase_002',
        category: '医疗用品',
        items: '疫苗、消毒液等',
        amount: 800.00,
        status: 'approved',
        statusText: '已批准',
        statusColor: '#00A86B',
        submitTime: '2024-01-05 09:15',
        urgency: 'urgent'
      },
      {
        id: 'purchase_003',
        category: '设备维护',
        items: '饮水器配件',
        amount: 350.00,
        status: 'approved',
        statusText: '已批准',
        statusColor: '#00A86B',
        submitTime: '2024-01-02 16:45',
        urgency: 'normal'
      }
    ]
  },

  onLoad: function (options) {
    this.loadData();
  },

  onShow: function () {
    this.refreshData();
  },

  loadData: async function() {
    this.setData({ loading: true });
    try {
      // 拉取统计与最近记录（若接口不可用，保持静默降级）
      const [statsRes, recentRes] = await Promise.allSettled([
        request.get(API.ENDPOINTS.OA.PURCHASE.REQUESTS, { page: 1, limit: 1 }),
        request.get(API.ENDPOINTS.OA.PURCHASE.REQUESTS, { page: 1, limit: 5 })
      ]);

      if (recentRes.status === 'fulfilled' && recentRes.value && recentRes.value.success !== false) {
        const payload = recentRes.value.data || recentRes.value;
        const list = payload.list || (Array.isArray(payload) ? payload : []);
        // 映射最近记录到页面结构（兼容展示字段）
        const mapped = list.map(item => ({
          id: item.id,
          category: item.category || '-',
          items: item.itemsSummary || item.title || '-',
          amount: item.totalAmount || 0,
          status: item.status || 'pending',
          statusText: item.statusText || '',
          statusColor: '#0066CC',
          submitTime: item.submitTime || item.createdAt || '',
          urgency: item.urgency || 'normal'
        }));
        this.setData({ recentPurchases: mapped });
      }
    } finally {
      this.setData({ loading: false });
    }
  },

  refreshData: function() {
    this.loadData();
  },

  onQuickActionTap: function(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.navigateTo({
        url: url,
        fail: () => {
          wx.showToast({
            title: '页面暂未开放',
            icon: 'none'
          });
        }
      });
    }
  },

  onStatTap: function(e) {
    const { type } = e.currentTarget.dataset;
    let url = '/pages/oa/purchase/list/list';
    
    switch(type) {
      case 'pending':
        url += '?status=pending';
        break;
      case 'approved':
        url += '?status=approved';
        break;
      case 'rejected':
        url += '?status=rejected';
        break;
      default:
        url += '?status=all';
        break;
    }
    
    wx.navigateTo({
      url: url,
      fail: () => {
        wx.showToast({
          title: '页面暂未开放',
          icon: 'none'
        });
      }
    });
  },

  onPurchaseItemTap: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/oa/purchase/detail/detail?id=${id}`,
      fail: () => {
        wx.showToast({
          title: '页面暂未开放',
          icon: 'none'
        });
      }
    });
  },

  onViewAllPurchases: function() {
    wx.navigateTo({
      url: '/pages/oa/purchase/list/list',
      fail: () => {
        wx.showToast({
          title: '页面暂未开放',
          icon: 'none'
        });
      }
    });
  },

  onPullDownRefresh: function () {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  onShareAppMessage: function () {
    return {
      title: '采购申请管理',
      path: '/pages/oa/purchase/purchase'
    };
  }
});