/**
 * 智慧养鹅企业级SaaS管理系统启动脚本
 * Smart Goose Enterprise SaaS Management System Startup Script
 */

require("dotenv").config();
const path = require("path");
const express = require("express");
const http = require("http");
const { sequelize, testConnection } = require("./config/database");
// 简单的颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

// 创建API服务器
const apiApp = express();
const apiPort = process.env.PORT || 3000;

// 创建管理后台服务器
const adminApp = express();
const adminPort = process.env.ADMIN_PORT || 3001;

// 启动API服务器
const startApiServer = async () => {
  try {
    // 测试数据库连接
    await testConnection();

    // 配置API服务器
    require("./app")(apiApp);

    // 启动HTTP服务器
    const apiServer = http.createServer(apiApp);

    apiServer.listen(apiPort, () => {
      console.log(
        colors.green(`✅ API服务器已启动: http://localhost:${apiPort}`),
      );
      console.log(
        colors.blue(`📚 API文档: http://localhost:${apiPort}/api-docs`),
      );
    });

    // 处理未捕获的异常
    process.on("uncaughtException", (error) => {
      console.error(colors.red("❌ 未捕获的异常:"), error);
    });

    // 处理未处理的Promise拒绝
    process.on("unhandledRejection", (reason, promise) => {
      console.error(colors.red("❌ 未处理的Promise拒绝:"), reason);
    });

    return apiServer;
  } catch (error) {
    console.error(colors.red("❌ API服务器启动失败:"), error);
    process.exit(1);
  }
};

// 启动管理后台服务器
const startAdminServer = async () => {
  try {
    // 配置管理后台服务器
    adminApp.set("views", path.join(__dirname, "admin/views"));
    adminApp.set("view engine", "ejs");
    adminApp.use(express.static(path.join(__dirname, "admin/public")));

    // 加载管理后台路由
    require("./admin/app")(adminApp);

    // 启动HTTP服务器
    const adminServer = http.createServer(adminApp);

    adminServer.listen(adminPort, () => {
      console.log(
        colors.green(`✅ 管理后台已启动: http://localhost:${adminPort}`),
      );
      console.log(colors.yellow("👤 默认管理员账户:"));
      console.log(colors.yellow("   用户名: admin"));
      console.log(colors.yellow("   密码: admin123"));
    });

    return adminServer;
  } catch (error) {
    console.error(colors.red("❌ 管理后台启动失败:"), error);
    process.exit(1);
  }
};

// 主函数
const main = async () => {
  console.log(colors.cyan("🚀 正在启动智慧养鹅企业级SaaS管理系统..."));

  try {
    // 启动API服务器
    await startApiServer();

    // 启动管理后台服务器
    await startAdminServer();

    console.log(colors.green("✨ 系统启动成功!"));
    console.log(colors.cyan("=============================================="));
  } catch (error) {
    console.error(colors.red("❌ 系统启动失败:"), error);
    process.exit(1);
  }
};

// 执行主函数
main();
