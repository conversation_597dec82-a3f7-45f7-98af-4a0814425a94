# 智慧养鹅小程序主包尺寸优化指南

## 优化措施

### 1. 启用按需注入
在 `app.json` 中启用了 `lazyCodeLoading: "requiredComponents"`，实现组件按需加载：

```json
{
  "lazyCodeLoading": "requiredComponents"
}
```

**效果**: 只有当前页面使用的组件才会被注入，减少启动时的代码量。

### 2. 移除未使用的组件声明

已移除以下未实际使用的组件：
- `pages/production/production.json` 中的 `c-form-modal`
- `pages/home/<USER>

**验证方法**: 检查WXML文件中是否实际使用了这些组件标签。

### 3. 优化Constants系统

#### 问题分析
- `constants/api.constants.js` (16KB) 
- `constants/business.constants.js` (12KB)
- 总共约28KB的常量文件在主包中

#### 解决方案
1. **创建核心常量文件** (`constants/core.constants.js`)
   - 只包含启动和认证必需的常量
   - 大小压缩到约3KB

2. **创建分包常量** (`constants-data/api-extended.js`)
   - 将OA系统等扩展功能的API常量移至分包
   - 按需加载，不影响主包启动

3. **提供精简入口** (`constants/lite.index.js`)
   - 兼容现有代码
   - 提供按需加载函数

#### 使用示例

```javascript
// 核心功能（主包）
const { ENDPOINTS, USER, UI } = require('../../constants/core.constants.js');

// 扩展功能（按需加载）
const { loadConstants } = require('../../constants/lite.index.js');
const { EXTENDED_ENDPOINTS } = loadConstants.loadExtendedAPI();
```

### 4. 主包大文件分析

主要大文件列表：
- `pages/production/production.js` (83KB) - 需要考虑拆分
- `pages/production/finance/finance.js` (42KB)
- `pages/health/health.js` (37KB)
- `styles/design-system.wxss` (33KB)

**建议**: 考虑将复杂页面的业务逻辑拆分为多个模块。

## 优化效果

### 预期减少的主包尺寸
- 组件按需注入: 减少约10-20%的初始代码量
- 移除未使用组件: 减少约5-10KB
- 优化Constants系统: 减少约20-25KB
- **总计预期减少**: 30-40KB

### 性能提升
1. **启动速度**: 按需注入减少初始化时间
2. **内存占用**: 只加载必需的组件和常量
3. **网络传输**: 主包体积更小，下载更快

## 最佳实践

### 1. 组件声明
```json
{
  "usingComponents": {
    // ❌ 错误：声明了但未使用
    "c-unused-component": "/components/unused/unused",
    
    // ✅ 正确：只声明实际使用的组件
    "c-used-component": "/components/used/used"
  }
}
```

### 2. 常量导入
```javascript
// ❌ 避免：导入全部常量
const { API, IMAGES, UI, BUSINESS, CONFIG } = require('../../constants/index.js');

// ✅ 推荐：只导入需要的核心常量
const { ENDPOINTS, USER } = require('../../constants/core.constants.js');

// ✅ 推荐：按需加载扩展常量
const { loadConstants } = require('../../constants/lite.index.js');
if (needOAFeature) {
  const { EXTENDED_ENDPOINTS } = loadConstants.loadExtendedAPI();
}
```

### 3. 分包策略
- **主包**: 核心功能、首页、认证
- **分包**: OA系统、管理功能、扩展功能
- **常量分包**: 大型配置和扩展API

## 验证优化结果

1. **使用开发者工具查看包大小**
   - 详情 → 基本信息 → 代码包信息

2. **启动性能测试**
   - 控制台 → Performance → 启动性能

3. **内存使用监控**
   - 控制台 → Memory → 内存占用

## 注意事项

1. **兼容性**: 现有代码可以继续使用，已提供兼容性支持
2. **分包限制**: 单个分包不能超过2MB
3. **网络策略**: 分包内容会在需要时自动下载

## 后续优化方向

1. **代码分割**: 考虑将大型页面JS文件进一步拆分
2. **图片优化**: 压缩图片资源，使用webp格式
3. **依赖清理**: 移除未使用的工具函数和组件
4. **懒加载**: 对非首屏内容实现懒加载
