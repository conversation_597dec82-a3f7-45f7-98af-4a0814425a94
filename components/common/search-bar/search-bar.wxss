/* 通用搜索栏组件样式 */

:host {
  --c-search-bar-bg-color: #F5F6F8;
  --c-search-bar-border-color: #E5E7EB;
  --c-search-bar-text-color: #333333;
  --c-search-bar-placeholder-color: #9CA3AF;
  --c-search-bar-icon-color: #6B7280;
  --c-search-bar-cancel-color: #2E8B57;
  --c-search-bar-history-bg: #FFFFFF;
  --c-search-bar-history-border: #F3F4F6;
  
  --c-search-bar-height-small: 60rpx;
  --c-search-bar-height-medium: 70rpx;
  --c-search-bar-height-large: 80rpx;
  
  --c-search-bar-padding-small: 16rpx;
  --c-search-bar-padding-medium: 20rpx;
  --c-search-bar-padding-large: 24rpx;
  
  --c-search-bar-border-radius: 8rpx;
  --c-search-bar-border-radius-round: 1000rpx;
  
  --c-search-bar-transition: all 0.2s ease;
}

/* 基础搜索栏样式 */
.c-search-bar {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  transition: var(--c-search-bar-transition);
}

/* ===== 搜索栏形状 ===== */
.c-search-bar--square .c-search-bar__input-wrapper {
  border-radius: var(--c-search-bar-border-radius);
}

.c-search-bar--round .c-search-bar__input-wrapper {
  border-radius: var(--c-search-bar-border-radius-round);
}

/* ===== 搜索栏尺寸 ===== */
.c-search-bar--small .c-search-bar__input-wrapper {
  height: var(--c-search-bar-height-small);
  padding: 0 var(--c-search-bar-padding-small);
}

.c-search-bar--medium .c-search-bar__input-wrapper {
  height: var(--c-search-bar-height-medium);
  padding: 0 var(--c-search-bar-padding-medium);
}

.c-search-bar--large .c-search-bar__input-wrapper {
  height: var(--c-search-bar-height-large);
  padding: 0 var(--c-search-bar-padding-large);
}

/* ===== 搜索栏状态 ===== */
.c-search-bar--focused .c-search-bar__input-wrapper {
  background-color: #FFFFFF;
  border-color: var(--c-search-bar-cancel-color);
  box-shadow: 0 0 0 4rpx rgba(46, 139, 87, 0.1);
}

.c-search-bar--disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* ===== 输入区域 ===== */
.c-search-bar__input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  background-color: var(--c-search-bar-bg-color);
  border: 2rpx solid transparent;
  transition: var(--c-search-bar-transition);
  box-sizing: border-box;
}

/* 搜索图标 */
.c-search-bar__search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.c-search-bar__icon-img {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

/* 输入框 */
.c-search-bar__input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: var(--c-search-bar-text-color);
  background-color: transparent;
  border: none;
  outline: none;
  min-width: 0;
}

.c-search-bar__input::placeholder {
  color: var(--c-search-bar-placeholder-color);
}

/* 清除按钮 */
.c-search-bar__clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
  cursor: pointer;
}

.c-search-bar__clear-btn .c-search-bar__icon-img {
  opacity: 0.5;
}

.c-search-bar__clear-btn:active .c-search-bar__icon-img {
  opacity: 0.8;
}

/* ===== 取消按钮 ===== */
.c-search-bar__cancel-btn {
  margin-left: 20rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: var(--c-search-bar-cancel-color);
  white-space: nowrap;
  cursor: pointer;
  opacity: 0;
  transform: translateX(20rpx);
  transition: var(--c-search-bar-transition);
}

.c-search-bar__cancel-btn--show {
  opacity: 1;
  transform: translateX(0);
}

.c-search-bar__cancel-btn:active {
  opacity: 0.7;
}

/* ===== 搜索历史面板 ===== */
.c-search-bar__history-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--c-search-bar-history-bg);
  border: 2rpx solid var(--c-search-bar-history-border);
  border-radius: var(--c-search-bar-border-radius);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 8rpx;
  overflow: hidden;
}

/* 历史记录头部 */
.c-search-bar__history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 2rpx solid var(--c-search-bar-history-border);
}

.c-search-bar__history-title {
  font-size: 26rpx;
  font-weight: 600;
  color: var(--c-search-bar-text-color);
}

.c-search-bar__history-clear {
  font-size: 24rpx;
  color: var(--c-search-bar-icon-color);
  cursor: pointer;
}

.c-search-bar__history-clear:active {
  opacity: 0.7;
}

/* 历史记录列表 */
.c-search-bar__history-list {
  max-height: 400rpx;
  overflow-y: auto;
}

/* 历史记录项 */
.c-search-bar__history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 2rpx solid var(--c-search-bar-history-border);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.c-search-bar__history-item:last-child {
  border-bottom: none;
}

.c-search-bar__history-item:active {
  background-color: #F9FAFB;
}

.c-search-bar__history-text {
  flex: 1;
  font-size: 28rpx;
  color: var(--c-search-bar-text-color);
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.c-search-bar__history-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
  cursor: pointer;
}

.c-search-bar__history-delete .c-search-bar__icon-img {
  opacity: 0.4;
}

.c-search-bar__history-delete:active .c-search-bar__icon-img {
  opacity: 0.7;
}

/* ===== 尺寸特定样式 ===== */
.c-search-bar--small .c-search-bar__input {
  font-size: 26rpx;
}

.c-search-bar--small .c-search-bar__search-icon,
.c-search-bar--small .c-search-bar__clear-btn {
  width: 28rpx;
  height: 28rpx;
}

.c-search-bar--small .c-search-bar__cancel-btn {
  font-size: 26rpx;
}

.c-search-bar--large .c-search-bar__input {
  font-size: 30rpx;
}

.c-search-bar--large .c-search-bar__search-icon,
.c-search-bar--large .c-search-bar__clear-btn {
  width: 36rpx;
  height: 36rpx;
}

.c-search-bar--large .c-search-bar__cancel-btn {
  font-size: 30rpx;
}

/* ===== 响应式设计 ===== */

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  :host {
    --c-search-bar-bg-color: #374151;
    --c-search-bar-border-color: #4B5563;
    --c-search-bar-text-color: #F9FAFB;
    --c-search-bar-placeholder-color: #9CA3AF;
    --c-search-bar-icon-color: #D1D5DB;
    --c-search-bar-history-bg: #1F2937;
    --c-search-bar-history-border: #374151;
  }
  
  .c-search-bar__history-item:active {
    background-color: #374151;
  }
}