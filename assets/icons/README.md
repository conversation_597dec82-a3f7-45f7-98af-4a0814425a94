# TabBar 图标说明

## 当前状态
当前的tabbar图标是占位符图标，需要替换为高质量的PNG图标。

## 需要的图标

### 1. 首页图标 (home)
- 普通状态：home.png (灰色 #7A7E83)
- 选中状态：home_selected.png (蓝色 #0066CC)
- 尺寸：81px × 81px (推荐)
- 设计：房子图标

### 2. 健康图标 (health)
- 普通状态：health.png (灰色 #7A7E83)
- 选中状态：health_selected.png (蓝色 #0066CC)
- 尺寸：81px × 81px (推荐)
- 设计：心形图标

### 3. 生产图标 (production)
- 普通状态：production.png (灰色 #7A7E83)
- 选中状态：production_selected.png (蓝色 #0066CC)
- 尺寸：81px × 81px (推荐)
- 设计：工具图标

### 4. 商城图标 (shop)
- 普通状态：shop.png (灰色 #7A7E83)
- 选中状态：shop_selected.png (蓝色 #0066CC)
- 尺寸：81px × 81px (推荐)
- 设计：商店图标

### 5. 我的图标 (profile)
- 普通状态：profile.png (灰色 #7A7E83)
- 选中状态：profile_selected.png (蓝色 #0066CC)
- 尺寸：81px × 81px (推荐)
- 设计：用户图标

## SVG源文件
已创建的SVG源文件位于：
- home_new.svg
- health_new.svg
- production_new.svg
- shop_new.svg
- profile_new.svg

## 转换方法
可以使用以下工具将SVG转换为PNG：

1. **在线工具**
   - https://convertio.co/svg-png/
   - https://cloudconvert.com/svg-to-png

2. **命令行工具**
   ```bash
   # 使用 Inkscape
   inkscape --export-png=output.png --export-width=81 --export-height=81 input.svg
   
   # 使用 ImageMagick
   convert -background transparent -size 81x81 input.svg output.png
   ```

3. **设计软件**
   - Figma：导入SVG，导出为PNG
   - Sketch：导入SVG，导出为PNG
   - Adobe Illustrator：打开SVG，导出为PNG

## 注意事项
- 确保图标在小尺寸下清晰可见
- 保持一致的视觉风格
- 使用透明背景
- 确保颜色符合设计规范
