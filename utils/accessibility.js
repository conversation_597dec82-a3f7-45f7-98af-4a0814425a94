// 智慧养鹅小程序 - 无障碍工具类 V1.0
// 提供完整的无障碍功能支持

/**
 * 无障碍工具类
 */
class AccessibilityHelper {
  constructor() {
    this.isKeyboardNavigation = false;
    this.focusTraps = [];
    this.announcements = [];
    this.init();
  }

  /**
   * 初始化无障碍功能
   */
  init() {
    // 检测键盘导航
    this.detectKeyboardNavigation();
    
    // 初始化焦点管理
    this.initFocusManagement();
    
    // 初始化实时通知
    this.initLiveRegions();
  }

  /**
   * 检测键盘导航模式
   */
  detectKeyboardNavigation() {
    // 监听键盘事件
    wx.onKeyboardHeightChange && wx.onKeyboardHeightChange((res) => {
      if (res.height > 0) {
        this.isKeyboardNavigation = true;
        this.enableKeyboardNavigation();
      } else {
        this.isKeyboardNavigation = false;
        this.disableKeyboardNavigation();
      }
    });
  }

  /**
   * 启用键盘导航模式
   */
  enableKeyboardNavigation() {
    try {
      // 添加键盘导航样式类
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.setData) {
          currentPage.setData({
            keyboardNavigationActive: true
          });
        }
      }
    } catch (error) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('启用键盘导航失败', error); } catch(_) {}
    }
  }

  /**
   * 禁用键盘导航模式
   */
  disableKeyboardNavigation() {
    try {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.setData) {
          currentPage.setData({
            keyboardNavigationActive: false
          });
        }
      }
    } catch (error) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('禁用键盘导航失败', error); } catch(_) {}
    }
  }

  /**
   * 初始化焦点管理
   */
  initFocusManagement() {
    // 在小程序中，焦点管理主要通过data-属性和事件处理
    try { const logger = require('./logger.js'); logger.debug && logger.debug('焦点管理已初始化'); } catch(_) {}
  }

  /**
   * 设置焦点到指定元素
   * @param {string} selector - 元素选择器或ID
   * @param {Object} context - 页面上下文，默认为当前页面
   */
  setFocus(selector, context = null) {
    try {
      const currentContext = context || this.getCurrentPageContext();
      if (!currentContext) return false;

      // 在小程序中使用 createSelectorQuery 来获取元素
      const query = currentContext.createSelectorQuery();
      query.select(selector).boundingClientRect((rect) => {
        if (rect) {
          // 滚动到元素位置
          wx.pageScrollTo({
            scrollTop: Math.max(0, rect.top - 100),
            duration: 300
          });
          
          // 触发焦点事件（通过setData模拟）
          currentContext.setData({
            [`focusedElement`]: selector,
            [`focusTime`]: Date.now()
          });
        }
      }).exec();

      return true;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('设置焦点失败', error); } catch(_) {}
      return false;
    }
  }

  /**
   * 获取当前页面上下文
   */
  getCurrentPageContext() {
    try {
      const pages = getCurrentPages();
      return pages.length > 0 ? pages[pages.length - 1] : null;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('获取页面上下文失败', error); } catch(_) {}
      return null;
    }
  }

  /**
   * 创建焦点陷阱
   * @param {string} containerId - 容器ID
   * @param {Array} focusableSelectors - 可聚焦元素选择器数组
   */
  createFocusTrap(containerId, focusableSelectors = []) {
    const trap = {
      id: containerId,
      selectors: focusableSelectors.length > 0 ? focusableSelectors : [
        'button', '.btn', 'input', '.input', '.nav-item', '.tab-item'
      ],
      active: false
    };

    this.focusTraps.push(trap);
    return trap;
  }

  /**
   * 激活焦点陷阱
   * @param {string} containerId - 容器ID
   */
  activateFocusTrap(containerId) {
    const trap = this.focusTraps.find(t => t.id === containerId);
    if (trap) {
      trap.active = true;
      // 设置焦点到第一个可聚焦元素
      this.setFocus(`#${containerId} ${trap.selectors[0]}`);
    }
  }

  /**
   * 释放焦点陷阱
   * @param {string} containerId - 容器ID
   */
  releaseFocusTrap(containerId) {
    const trap = this.focusTraps.find(t => t.id === containerId);
    if (trap) {
      trap.active = false;
    }
  }

  /**
   * 初始化实时通知区域
   */
  initLiveRegions() {
    // 在小程序中，主要通过页面data的变化来实现实时通知
    try { const logger = require('./logger.js'); logger.debug && logger.debug('实时通知区域已初始化'); } catch(_) {}
  }

  /**
   * 发布实时通知
   * @param {string} message - 通知消息
   * @param {string} priority - 优先级 ('polite' | 'assertive')
   * @param {number} duration - 显示时长(毫秒)，0表示不自动消失
   */
  announce(message, priority = 'polite', duration = 3000) {
    try {
      const announcement = {
        id: Date.now(),
        message,
        priority,
        timestamp: new Date().toISOString()
      };

      this.announcements.push(announcement);

      // 通过页面数据更新触发通知
      const currentContext = this.getCurrentPageContext();
      if (currentContext && currentContext.setData) {
        currentContext.setData({
          [`announcement_${priority}`]: message,
          [`announcementTime_${priority}`]: Date.now()
        });

        // 自动清除通知
        if (duration > 0) {
          setTimeout(() => {
            if (currentContext.setData) {
              currentContext.setData({
                [`announcement_${priority}`]: '',
                [`announcementTime_${priority}`]: 0
              });
            }
          }, duration);
        }
      }

      return announcement;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('发布通知失败', error); } catch(_) {}
      return null;
    }
  }

  /**
   * 检查颜色对比度
   * @param {string} foreground - 前景色 (hex格式)
   * @param {string} background - 背景色 (hex格式)
   * @returns {Object} 对比度信息
   */
  checkColorContrast(foreground, background) {
    try {
      const fgLuminance = this.calculateLuminance(foreground);
      const bgLuminance = this.calculateLuminance(background);
      
      const contrast = (Math.max(fgLuminance, bgLuminance) + 0.05) / 
                      (Math.min(fgLuminance, bgLuminance) + 0.05);

      return {
        ratio: Math.round(contrast * 100) / 100,
        AA: contrast >= 4.5,
        AAA: contrast >= 7,
        AALarge: contrast >= 3,
        AAALarge: contrast >= 4.5
      };
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('颜色对比度检查失败', error); } catch(_) {}
      return null;
    }
  }

  /**
   * 计算颜色亮度
   * @param {string} color - 颜色值 (hex格式)
   * @returns {number} 亮度值
   */
  calculateLuminance(color) {
    // 转换hex颜色为RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // 应用gamma校正
    const rs = r <= 0.03928 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
    const gs = g <= 0.03928 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
    const bs = b <= 0.03928 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);

    // 计算相对亮度
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * 验证表单无障碍性
   * @param {Object} formData - 表单数据
   * @param {Object} rules - 验证规则
   * @returns {Object} 验证结果
   */
  validateFormAccessibility(formData, rules) {
    const errors = [];
    const warnings = [];

    try {
      Object.keys(rules).forEach(field => {
        const rule = rules[field];
        const value = formData[field];

        // 检查必填字段
        if (rule.required && (!value || value.trim() === '')) {
          errors.push({
            field,
            type: 'required',
            message: rule.requiredMessage || `${field}为必填项`
          });
        }

        // 检查字段长度
        if (value && rule.minLength && value.length < rule.minLength) {
          errors.push({
            field,
            type: 'minLength',
            message: rule.minLengthMessage || `${field}最少需要${rule.minLength}个字符`
          });
        }

        if (value && rule.maxLength && value.length > rule.maxLength) {
          errors.push({
            field,
            type: 'maxLength',
            message: rule.maxLengthMessage || `${field}最多允许${rule.maxLength}个字符`
          });
        }

        // 检查格式
        if (value && rule.pattern && !rule.pattern.test(value)) {
          errors.push({
            field,
            type: 'pattern',
            message: rule.patternMessage || `${field}格式不正确`
          });
        }
      });

      return {
        valid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('表单验证失败', error); } catch(_) {}
      return {
        valid: false,
        errors: [{ type: 'system', message: '表单验证出现系统错误' }],
        warnings: []
      };
    }
  }

  /**
   * 格式化错误消息为无障碍友好的形式
   * @param {Array} errors - 错误数组
   * @returns {string} 格式化后的错误消息
   */
  formatErrorsForScreenReader(errors) {
    if (!errors || errors.length === 0) {
      return '';
    }

    if (errors.length === 1) {
      return `表单错误：${errors[0].message}`;
    }

    const messages = errors.map((error, index) => `${index + 1}. ${error.message}`);
    return `表单包含${errors.length}个错误：${messages.join('，')}`;
  }

  /**
   * 获取用户无障碍偏好设置
   * @returns {Object} 偏好设置
   */
  getUserPreferences() {
    try {
      return {
        reducedMotion: wx.getStorageSync('accessibility_reduced_motion') || false,
        highContrast: wx.getStorageSync('accessibility_high_contrast') || false,
        largeFont: wx.getStorageSync('accessibility_large_font') || false,
        screenReader: wx.getStorageSync('accessibility_screen_reader') || false
      };
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('获取用户偏好失败', error); } catch(_) {}
      return {
        reducedMotion: false,
        highContrast: false,
        largeFont: false,
        screenReader: false
      };
    }
  }

  /**
   * 设置用户无障碍偏好
   * @param {Object} preferences - 偏好设置
   */
  setUserPreferences(preferences) {
    try {
      Object.keys(preferences).forEach(key => {
        wx.setStorageSync(`accessibility_${key}`, preferences[key]);
      });

      // 应用偏好设置
      this.applyPreferences(preferences);
      
      return true;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('设置用户偏好失败', error); } catch(_) {}
      return false;
    }
  }

  /**
   * 应用无障碍偏好设置
   * @param {Object} preferences - 偏好设置
   */
  applyPreferences(preferences) {
    try {
      const currentContext = this.getCurrentPageContext();
      if (!currentContext) return;

      currentContext.setData({
        accessibilityPreferences: preferences,
        reducedMotionMode: preferences.reducedMotion,
        highContrastMode: preferences.highContrast,
        largeFontMode: preferences.largeFont,
        screenReaderMode: preferences.screenReader
      });
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('应用偏好设置失败', error); } catch(_) {}
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.focusTraps = [];
    this.announcements = [];
  }
}

// 创建全局实例
const accessibilityHelper = new AccessibilityHelper();

// 页面混入对象，为页面添加无障碍功能
const AccessibilityMixin = {
  data: {
    keyboardNavigationActive: false,
    announcement_polite: '',
    announcement_assertive: '',
    announcementTime_polite: 0,
    announcementTime_assertive: 0,
    focusedElement: '',
    focusTime: 0,
    accessibilityPreferences: {}
  },

  onLoad() {
    // 加载用户偏好设置
    const preferences = accessibilityHelper.getUserPreferences();
    accessibilityHelper.applyPreferences(preferences);
  },

  onUnload() {
    // 清理焦点陷阱
    this.releaseFocusTraps();
  },

  // 无障碍相关方法
  setFocus(selector) {
    return accessibilityHelper.setFocus(selector, this);
  },

  announce(message, priority = 'polite', duration = 3000) {
    return accessibilityHelper.announce(message, priority, duration);
  },

  createFocusTrap(containerId, selectors) {
    return accessibilityHelper.createFocusTrap(containerId, selectors);
  },

  activateFocusTrap(containerId) {
    return accessibilityHelper.activateFocusTrap(containerId);
  },

  releaseFocusTrap(containerId) {
    return accessibilityHelper.releaseFocusTrap(containerId);
  },

  releaseFocusTraps() {
    accessibilityHelper.focusTraps.forEach(trap => {
      if (trap.active) {
        accessibilityHelper.releaseFocusTrap(trap.id);
      }
    });
  },

  validateFormAccessibility(formData, rules) {
    return accessibilityHelper.validateFormAccessibility(formData, rules);
  },

  setAccessibilityPreferences(preferences) {
    return accessibilityHelper.setUserPreferences(preferences);
  }
};

module.exports = {
  AccessibilityHelper,
  accessibilityHelper,
  AccessibilityMixin
};