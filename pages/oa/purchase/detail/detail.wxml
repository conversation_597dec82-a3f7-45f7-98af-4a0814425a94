<!--pages/oa/purchase/detail/detail.wxml-->
<view class="container" wx:if="{{purchaseDetail}}">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="header-info">
        <text class="request-number">#{{purchaseDetail.requestNumber}}</text>
        <text class="request-title">{{purchaseDetail.title}}</text>
        <view class="status-info">
          <view 
            class="status-badge" 
            style="background: {{statusConfig[purchaseDetail.status].bgColor}}; color: {{statusConfig[purchaseDetail.status].color}}"
          >
            <text class="status-text">{{statusConfig[purchaseDetail.status].label}}</text>
          </view>
          <view 
            class="urgency-badge" 
            style="color: {{urgencyConfig[purchaseDetail.urgencyLevel].color}}"
          >
            <text class="urgency-text">{{urgencyConfig[purchaseDetail.urgencyLevel].label}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 申请人信息 -->
  <view class="section applicant-section">
    <view class="section-header">
      <view class="section-title">申请人信息</view>
      <text class="auto-fill-tag">自动填充</text>
    </view>
    
    <view class="applicant-card">
      <view class="applicant-avatar">
        <text class="avatar-text">{{purchaseDetail.applicantName.charAt(0)}}</text>
      </view>
      <view class="applicant-info">
        <view class="info-row">
          <text class="info-label">申请人</text>
          <text class="info-value">{{purchaseDetail.applicantName}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">工号</text>
          <text class="info-value">{{purchaseDetail.applicantEmployeeId}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">部门</text>
          <text class="info-value">{{purchaseDetail.department}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">职位</text>
          <text class="info-value">{{purchaseDetail.position}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{purchaseDetail.phone}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="section">
    <view class="section-title">基本信息</view>
    
    <view class="info-grid">
      <view class="info-item">
        <text class="label">采购类别</text>
        <text class="value">{{categoryConfig[purchaseDetail.category]}}</text>
      </view>
      <view class="info-item">
        <text class="label">申请时间</text>
        <text class="value">{{formatDate(purchaseDetail.createdAt, 'YYYY-MM-DD HH:mm')}}</text>
      </view>
      <view class="info-item">
        <text class="label">预期到货</text>
        <text class="value">{{formatDate(purchaseDetail.expectedDate, 'YYYY-MM-DD')}}</text>
      </view>
      <view class="info-item">
        <text class="label">总金额</text>
        <text class="value amount">{{formatCurrency(purchaseDetail.totalAmount)}}</text>
      </view>
    </view>
    
    <view class="description-section" wx:if="{{purchaseDetail.description}}">
      <text class="label">采购说明</text>
      <text class="description">{{purchaseDetail.description}}</text>
    </view>
    
    <view class="description-section">
      <text class="label">业务目的</text>
      <text class="description">{{purchaseDetail.businessPurpose}}</text>
    </view>
  </view>

  <!-- 采购物品清单 -->
  <view class="section">
    <view class="section-title">采购物品清单</view>
    
    <view class="items-list">
      <view 
        wx:for="{{purchaseDetail.items}}" 
        wx:key="index" 
        class="item-card"
        bindtap="onItemTap"
        data-index="{{index}}"
      >
        <view class="item-header">
          <text class="item-name">{{item.itemName}}</text>
          <text class="item-price">{{formatCurrency(item.totalPrice)}}</text>
        </view>
        
        <view class="item-details">
          <view class="detail-row" wx:if="{{item.specification}}">
            <text class="detail-label">规格</text>
            <text class="detail-value">{{item.specification}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">数量</text>
            <text class="detail-value">{{item.quantity}} {{item.unit}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">单价</text>
            <text class="detail-value">{{formatCurrency(item.unitPrice)}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.brand}}">
            <text class="detail-label">品牌</text>
            <text class="detail-value">{{item.brand}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.supplierSuggestion}}">
            <text class="detail-label">建议供应商</text>
            <text class="detail-value">{{item.supplierSuggestion}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.remarks}}">
            <text class="detail-label">备注</text>
            <text class="detail-value">{{item.remarks}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="total-summary">
      <view class="summary-row">
        <text class="summary-label">物品总数</text>
        <text class="summary-value">{{purchaseDetail.items.length}} 项</text>
      </view>
      <view class="summary-row total">
        <text class="summary-label">采购总金额</text>
        <text class="summary-value">{{formatCurrency(purchaseDetail.totalAmount)}}</text>
      </view>
    </view>
  </view>

  <!-- 审批流程 -->
  <view class="section" wx:if="{{approvalFlow.length > 0}}">
    <view class="section-header">
      <view class="section-title">审批流程</view>
      <button class="view-flow-btn" bindtap="onViewApprovalFlow">
        <text>查看详情</text>
        <text class="arrow">→</text>
      </button>
    </view>
    
    <view class="approval-timeline">
      <view 
        wx:for="{{approvalFlow}}" 
        wx:key="id" 
        class="timeline-item {{item.status}}"
      >
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="approval-header">
            <text class="approver-name">{{item.approverName}}</text>
            <text class="approval-time">{{formatTimeAgo(item.createdAt)}}</text>
          </view>
          <view class="approval-action">
            <text class="action-text">{{item.actionText}}</text>
          </view>
          <view class="approval-comment" wx:if="{{item.comment}}">
            <text class="comment-text">{{item.comment}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 附件 -->
  <view class="section" wx:if="{{attachments.length > 0}}">
    <view class="section-title">相关附件</view>
    
    <view class="attachments-list">
      <view 
        wx:for="{{attachments}}" 
        wx:key="id" 
        class="attachment-item"
        bindtap="onViewAttachment"
        data-index="{{index}}"
      >
        <view class="attachment-icon">附件</view>
        <view class="attachment-info">
          <text class="attachment-name">{{item.fileName}}</text>
          <text class="attachment-size">{{item.fileSize}}</text>
        </view>
        <view class="attachment-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 评论记录 -->
  <view class="section" wx:if="{{comments.length > 0}}">
    <view class="section-header">
      <view class="section-title">评论记录</view>
      <button class="add-comment-btn" bindtap="onAddComment" wx:if="{{canComment}}">
        <text>添加评论</text>
        <text class="add-icon">+</text>
      </button>
    </view>
    
    <view class="comments-list">
      <view 
        wx:for="{{comments}}" 
        wx:key="id" 
        class="comment-item"
        bindtap="onCommentTap"
        data-index="{{index}}"
      >
        <view class="comment-avatar">
          <text class="avatar-text">{{item.authorName.charAt(0)}}</text>
        </view>
        <view class="comment-content">
          <view class="comment-header">
            <text class="comment-author">{{item.authorName}}</text>
            <text class="comment-time">{{formatTimeAgo(item.createdAt)}}</text>
          </view>
          <text class="comment-text">{{item.content}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="action-buttons">
      <button 
        wx:if="{{canEdit}}" 
        class="action-btn primary" 
        bindtap="onEdit"
        disabled="{{processing}}"
      >
        编辑申请
      </button>
      
      <button 
        wx:if="{{canApprove}}" 
        class="action-btn success" 
        bindtap="onApprove"
        disabled="{{processing}}"
      >
        批准申请
      </button>
      
      <button 
        wx:if="{{canReject}}" 
        class="action-btn danger" 
        bindtap="onReject"
        disabled="{{processing}}"
      >
        拒绝申请
      </button>
      
      <button 
        wx:if="{{canCancel}}" 
        class="action-btn warning" 
        bindtap="onCancel"
        disabled="{{processing}}"
      >
        {{processing ? '取消中...' : '取消申请'}}
      </button>
      
      <button 
        class="action-btn secondary" 
        bindtap="onCopy"
        disabled="{{processing}}"
      >
        复制申请
      </button>
    </view>
  </view>

  <!-- 加载覆盖层 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载...</text>
    </view>
  </view>

  <!-- 刷新覆盖层 -->
  <view class="refresh-overlay" wx:if="{{refreshing}}">
    <view class="refresh-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在刷新...</text>
    </view>
  </view>
</view>

<!-- 空状态 -->
<view class="empty-state" wx:if="{{!loading && !purchaseDetail}}">
  <view class="empty-icon">📋</view>
  <text class="empty-title">加载失败</text>
  <text class="empty-desc">无法加载采购申请详情，请稍后重试</text>
  <button class="retry-btn" bindtap="onPullDownRefresh">重新加载</button>
</view>