<!-- pages/address/address.wxml - 重新设计的现代化收货地址页面 -->
<view class="address-page">
  <!-- 地址列表容器 -->
  <scroll-view class="address-container" scroll-y="true" enable-back-to-top="true">
    
    <!-- 地址卡片列表 -->
    <view class="address-list" wx:if="{{addressList.length > 0}}">
      <view class="address-card {{item.isDefault ? 'is-default' : ''}}" 
            wx:for="{{addressList}}" 
            wx:key="id"
            bindtap="onSelectAddress" 
            data-id="{{item.id}}">
        
        <!-- 默认标识 -->
        <view class="default-badge" wx:if="{{item.isDefault}}">
          <text class="badge-text">默认</text>
        </view>
        
        <!-- 地址标签 -->
        <view class="address-tag" wx:if="{{item.tag}}">
          <text class="tag-text">{{item.tag}}</text>
        </view>
        
        <!-- 地址主要内容 -->
        <view class="address-main">
          <!-- 收件人信息 -->
          <view class="recipient-section">
            <view class="recipient-name">{{item.name}}</view>
            <view class="recipient-phone">{{item.phone}}</view>
          </view>
          
          <!-- 地址信息 -->
          <view class="address-section">
            <view class="address-text">
              <text class="region">{{item.province}} {{item.city}} {{item.district}}</text>
              <text class="detail">{{item.detail}}</text>
            </view>
          </view>
        </view>
        
        <!-- 操作按钮区域 -->
        <view class="action-section">
          <view class="action-btn edit-action" 
                bindtap="onEditAddress" 
                data-id="{{item.id}}"
                hover-class="action-hover">
            <text class="action-text">编辑</text>
          </view>
          
          <view class="action-btn star-action {{item.isDefault ? 'is-active' : ''}}" 
                bindtap="onSetDefault" 
                data-id="{{item.id}}"
                hover-class="action-hover">
            <text class="action-text">{{item.isDefault ? '默认' : '设为默认'}}</text>
          </view>
          
          <view class="action-btn delete-action" 
                bindtap="onDeleteAddress" 
                data-id="{{item.id}}"
                hover-class="action-hover">
            <text class="action-text">删除</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{addressList.length === 0}}">
      <view class="empty-icon">📍</view>
      <view class="empty-title">暂无收货地址</view>
      <view class="empty-desc">添加收货地址，让购物更便捷</view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="bottom-safe-area"></view>
  </scroll-view>
  
  <!-- 添加地址按钮 -->
  <view class="add-address-section">
    <view class="add-btn" bindtap="onAddAddress" hover-class="add-btn-hover">
      <text class="add-icon">+</text>
      <text class="add-text">添加新地址</text>
    </view>
  </view>
</view>

<!-- 删除确认弹窗 -->
<view class="delete-modal {{showDeleteModal ? 'show' : ''}}" wx:if="{{showDeleteModal}}">
  <view class="modal-mask" bindtap="onCancelDelete"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">确认删除</text>
    </view>
    <view class="modal-body">
      <text class="modal-message">确定要删除这个地址吗？</text>
      <view class="address-preview" wx:if="{{deleteTarget}}">
        <text class="preview-name">{{deleteTarget.name}}</text>
        <text class="preview-address">{{deleteTarget.province}}{{deleteTarget.city}}{{deleteTarget.district}}{{deleteTarget.detail}}</text>
      </view>
    </view>
    <view class="modal-footer">
      <view class="modal-btn cancel-btn" bindtap="onCancelDelete">取消</view>
      <view class="modal-btn confirm-btn" bindtap="onConfirmDelete">删除</view>
    </view>
  </view>
</view>