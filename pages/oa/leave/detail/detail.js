// pages/oa/leave/detail/detail.js

/**
 * 请假详情页面
 * 功能：查看请假详情、审批操作、状态跟踪
 */

Page({
  data: {
    // 页面状态
    loading: true,
    submitting: false,
    
    // 请假详情数据
    leaveDetail: null,
    
    // 审批流程数据
    approvalFlow: [],
    
    // 当前用户信息
    currentUser: {
      id: 'user_001',
      name: '张三',
      role: 'admin', // admin, manager, user
      canApprove: false
    },
    
    // 审批表单数据
    approvalForm: {
      action: '', // approve, reject
      comment: '',
      visible: false
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const { id } = options;
    if (id) {
      this.loadLeaveDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载请假详情
   */
  loadLeaveDetail: function(id) {
    this.setData({ loading: true });
    
    // 模拟API调用
    setTimeout(() => {
      const mockDetail = this.generateMockDetail(id);
      const mockFlow = this.generateMockFlow(mockDetail.status);
      
      // 判断当前用户是否可以审批
      const canApprove = this.checkApprovalPermission(mockDetail);
      
      this.setData({
        leaveDetail: mockDetail,
        approvalFlow: mockFlow,
        'currentUser.canApprove': canApprove,
        loading: false
      });
    }, 800);
  },

  /**
   * 生成模拟详情数据
   */
  generateMockDetail: function(id) {
    const detailMap = {
      'leave_001': {
        id: 'leave_001',
        type: 'sick',
        typeLabel: '病假',
        typeColor: '#FF9500',
        reason: '感冒发烧需要休息，医生建议在家休息2天',
        startDate: '2024-01-15',
        endDate: '2024-01-16',
        startTime: '09:00',
        endTime: '18:00',
        days: 2,
        status: 'pending',
        statusText: '待审批',
        statusColor: '#FF9500',
        applicant: {
          id: 'user_001',
          name: '张三',
          avatar: '', // 移除图片引用
          department: '技术部',
          position: '高级工程师',
          phone: '138****8888',
          email: '<EMAIL>'
        },
        contact: '13800138000',
        backup: {
          id: 'user_002',
          name: '李四',
          department: '技术部',
          position: '工程师'
        },
        emergency: '家属：13900139000',
        attachment: [
          '/temp/images/medical_certificate_1.jpg',
          '/temp/images/medical_certificate_2.jpg'
        ],
        submitTime: '2024-01-10 09:30:00',
        approver: null,
        approveTime: null,
        rejectReason: null,
        approvalComment: []
      }
    };
    
    return detailMap[id] || detailMap['leave_001'];
  },

  /**
   * 生成模拟审批流程
   */
  generateMockFlow: function(status) {
    const baseFlow = [
      {
        step: 1,
        title: '提交申请',
        status: 'completed',
        time: '2024-01-10 09:30',
        user: '张三',
        description: '提交请假申请'
      },
      {
        step: 2,
        title: '部门审批',
        status: status === 'pending' ? 'pending' : (status === 'approved' ? 'completed' : 'rejected'),
        time: status !== 'pending' ? '2024-01-11 14:20' : null,
        user: status !== 'pending' ? '李经理' : '待审批',
        description: '部门经理审批'
      }
    ];
    
    if (status === 'approved') {
      baseFlow.push({
        step: 3,
        title: '审批完成',
        status: 'completed',
        time: '2024-01-11 14:21',
        user: '系统',
        description: '请假申请已通过'
      });
    } else if (status === 'rejected') {
      baseFlow.push({
        step: 3,
        title: '审批完成',
        status: 'rejected',
        time: '2024-01-11 14:21',
        user: '系统',
        description: '请假申请已拒绝'
      });
    }
    
    return baseFlow;
  },

  /**
   * 检查审批权限
   */
  checkApprovalPermission: function(detail) {
    const { currentUser } = this.data;
    
    // 如果是申请人自己，不能审批
    if (detail.applicant.id === currentUser.id) {
      return false;
    }
    
    // 如果状态不是待审批，不能审批
    if (detail.status !== 'pending') {
      return false;
    }
    
    // 只有管理员和经理可以审批
    return ['admin', 'manager'].includes(currentUser.role);
  },

  /**
   * 预览附件
   */
  onPreviewAttachment: function(e) {
    const { url } = e.currentTarget.dataset;
    const urls = this.data.leaveDetail.attachment;
    
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  /**
   * 拨打电话
   */
  onMakeCall: function(e) {
    const { phone } = e.currentTarget.dataset;
    
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示审批表单
   */
  showApprovalForm: function(e) {
    const { action } = e.currentTarget.dataset;
    
    this.setData({
      'approvalForm.action': action,
      'approvalForm.comment': '',
      'approvalForm.visible': true
    });
  },

  /**
   * 隐藏审批表单
   */
  hideApprovalForm: function() {
    this.setData({
      'approvalForm.visible': false
    });
  },

  /**
   * 审批意见输入
   */
  onCommentInput: function(e) {
    this.setData({
      'approvalForm.comment': e.detail.value
    });
  },

  /**
   * 确认审批
   */
  confirmApproval: function() {
    const { approvalForm, leaveDetail } = this.data;
    
    if (!approvalForm.comment.trim()) {
      wx.showToast({
        title: '请填写审批意见',
        icon: 'none'
      });
      return;
    }
    
    const actionText = approvalForm.action === 'approve' ? '通过' : '拒绝';
    
    wx.showModal({
      title: '确认审批',
      content: `确定要${actionText}这份请假申请吗？`,
      success: (res) => {
        if (res.confirm) {
          this.submitApproval();
        }
      }
    });
  },

  /**
   * 提交审批
   */
  submitApproval: function() {
    const { approvalForm, leaveDetail, currentUser } = this.data;
    
    this.setData({ submitting: true });
    
    const approvalData = {
      leaveId: leaveDetail.id,
      action: approvalForm.action,
      comment: approvalForm.comment,
      approver: currentUser.name,
      approveTime: new Date().toISOString()
    };
    
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('提交审批', approvalData); } catch(_) {}
    
    // 模拟API调用
    setTimeout(() => {
      const newStatus = approvalForm.action === 'approve' ? 'approved' : 'rejected';
      const newStatusText = approvalForm.action === 'approve' ? '已通过' : '已拒绝';
      const newStatusColor = approvalForm.action === 'approve' ? '#00A86B' : '#FF3B30';
      
      // 更新请假详情
      this.setData({
        'leaveDetail.status': newStatus,
        'leaveDetail.statusText': newStatusText,
        'leaveDetail.statusColor': newStatusColor,
        'leaveDetail.approver': currentUser.name,
        'leaveDetail.approveTime': new Date().toLocaleString(),
        'leaveDetail.rejectReason': approvalForm.action === 'reject' ? approvalForm.comment : null,
        'currentUser.canApprove': false,
        'approvalForm.visible': false,
        submitting: false
      });
      
      // 更新审批流程
      const newFlow = this.generateMockFlow(newStatus);
      this.setData({
        approvalFlow: newFlow
      });
      
      wx.showToast({
        title: '审批成功',
        icon: 'success'
      });
      
    }, 2000);
  },

  /**
   * 分享
   */
  onShareAppMessage: function () {
    const { leaveDetail } = this.data;
    
    return {
      title: `请假申请详情 - ${leaveDetail ? leaveDetail.applicant.name : ''}`,
      path: `/pages/oa/leave/detail/detail?id=${leaveDetail ? leaveDetail.id : ''}`
    };
  }
});