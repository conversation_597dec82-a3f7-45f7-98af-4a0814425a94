import { z } from "zod";
import { <PERSON>sult<PERSON><PERSON><PERSON> } from "express-zod-api";
import type { Request, Response } from "express";

// 定义统一的成功响应格式
const successResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.literal(true),
    data: dataSchema,
    timestamp: z.string(),
    requestId: z.string().optional(),
  });

// 定义统一的错误响应格式
const errorResponseSchema = z.object({
  success: z.literal(false),
  error: z.object({
    message: z.string(),
    code: z.string(),
    field: z.string().optional(),
    details: z.any().optional(),
  }),
  timestamp: z.string(),
  requestId: z.string().optional(),
});

// 错误代码映射
const getErrorCode = (error: Error): string => {
  if (error.name === "ValidationError") return "VALIDATION_ERROR";
  if (error.name === "UnauthorizedError") return "UNAUTHORIZED";
  if (error.name === "ForbiddenError") return "FORBIDDEN";
  if (error.name === "NotFoundError") return "NOT_FOUND";
  if (error.name === "ConflictError") return "CONFLICT";
  return "INTERNAL_ERROR";
};

// 状态码映射
const getStatusCode = (error: Error): number => {
  if (error.name === "ValidationError") return 400;
  if (error.name === "UnauthorizedError") return 401;
  if (error.name === "ForbiddenError") return 403;
  if (error.name === "NotFoundError") return 404;
  if (error.name === "ConflictError") return 409;
  return 500;
};

// 提取字段信息（用于验证错误）
const getErrorField = (error: any): string | undefined => {
  if (error.issues && Array.isArray(error.issues) && error.issues.length > 0) {
    return error.issues[0].path?.join(".") || undefined;
  }
  return undefined;
};

// 创建自定义结果处理器
export const customResultHandler = new ResultHandler({
  positive: (data: any) => ({
    schema: successResponseSchema(z.any()),
    mimeType: "application/json",
  }),

  negative: errorResponseSchema,

  handler: ({
    error,
    input,
    output,
    request,
    response,
    logger,
  }: {
    error: Error | null;
    input: any;
    output: any;
    request: Request;
    response: Response;
    logger: any;
  }) => {
    const timestamp = new Date().toISOString();
    const requestId = request.headers["x-request-id"] as string;

    if (error) {
      const statusCode = getStatusCode(error);
      const errorCode = getErrorCode(error);
      const field = getErrorField(error);

      // 记录错误日志
      console.error("API Error:", {
        message: error.message,
        statusCode,
        errorCode,
        field,
        url: request.originalUrl,
        method: request.method,
        userAgent: request.headers["user-agent"],
        ip: request.ip,
        timestamp,
        requestId,
      });

      const errorResponse = {
        success: false as const,
        error: {
          message: error.message,
          code: errorCode,
          field,
          ...(process.env.NODE_ENV === "development" && {
            details: error.stack,
          }),
        },
        timestamp,
        requestId,
      };

      response.status(statusCode).json(errorResponse);
    } else {
      // 记录成功日志
      console.info("API Success:", {
        url: request.originalUrl,
        method: request.method,
        statusCode: 200,
        timestamp,
        requestId,
      });

      const successResponse = {
        success: true as const,
        data: output,
        timestamp,
        requestId,
      };

      response.status(200).json(successResponse);
    }
  },
});
