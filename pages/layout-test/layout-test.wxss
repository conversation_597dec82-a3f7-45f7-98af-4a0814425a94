/* 布局测试页面样式 */

.layout-test-page {
  min-height: 100vh;
  background: #f8fffe;
  padding: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #262626;
  text-align: center;
  margin: 40rpx 0 60rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

.test-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16rpx;
  padding: 12rpx 20rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border-left: 4rpx solid #52c41a;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 布局说明 */
.layout-description {
  margin-top: 60rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

.desc-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #262626;
  margin-bottom: 20rpx;
  text-align: center;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.desc-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.desc-item {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.6;
  padding: 8rpx 0;
  display: flex;
  align-items: center;
}

.desc-item::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  background: #52c41a;
  border-radius: 50%;
  margin-right: 12rpx;
  flex-shrink: 0;
}

/* 优化提示 */
.optimization-tips {
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  border: 1rpx solid #91d5ff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 30rpx;
}

.tips-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.tips-title::before {
  content: '💡';
  font-size: 20rpx;
}

.tips-content {
  font-size: 22rpx;
  color: #1890ff;
  line-height: 1.5;
}

/* 对比展示 */
.comparison-section {
  margin-top: 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.comparison-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #262626;
  text-align: center;
  margin-bottom: 30rpx;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.comparison-item {
  text-align: center;
}

.comparison-label {
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.before-label {
  background: #fff2e8;
  color: #fa8c16;
}

.after-label {
  background: #f6ffed;
  color: #52c41a;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .comparison-grid {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: 32rpx;
    margin: 30rpx 0 40rpx;
  }
  
  .section-title {
    font-size: 26rpx;
  }
}
