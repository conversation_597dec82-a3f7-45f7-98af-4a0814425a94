/**
 * 日志轮转脚本
 * 定期清理和压缩日志文件
 */

const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

class LogRotation {
  constructor(logDir = path.join(__dirname, '../logs')) {
    this.logDir = logDir;
    this.maxAge = 30; // 保留30天的日志
    this.compressionAge = 7; // 7天后压缩日志
  }

  /**
   * 执行日志轮转
   */
  async rotate() {
    console.log('🔄 开始日志轮转...');
    
    try {
      // 确保日志目录存在
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
        console.log('📁 创建日志目录:', this.logDir);
        return;
      }

      const files = fs.readdirSync(this.logDir);
      const now = new Date();
      
      let compressedCount = 0;
      let deletedCount = 0;

      for (const file of files) {
        if (!file.endsWith('.log')) continue;

        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        const daysDiff = Math.floor((now - stats.mtime) / (1000 * 60 * 60 * 24));

        // 删除过期文件
        if (daysDiff > this.maxAge) {
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`🗑️ 删除过期日志: ${file} (${daysDiff}天前)`);
        }
        // 压缩旧文件
        else if (daysDiff > this.compressionAge && !file.endsWith('.gz')) {
          await this.compressFile(filePath);
          compressedCount++;
          console.log(`🗜️ 压缩日志文件: ${file} (${daysDiff}天前)`);
        }
      }

      console.log(`✅ 日志轮转完成: 压缩 ${compressedCount} 个文件, 删除 ${deletedCount} 个文件`);
      
      // 记录轮转信息
      this.logRotationInfo(compressedCount, deletedCount);
      
    } catch (error) {
      console.error('❌ 日志轮转失败:', error);
    }
  }

  /**
   * 压缩文件
   */
  async compressFile(filePath) {
    return new Promise((resolve, reject) => {
      const input = fs.createReadStream(filePath);
      const output = fs.createWriteStream(filePath + '.gz');
      const gzip = zlib.createGzip();

      input.pipe(gzip).pipe(output);

      output.on('close', () => {
        // 压缩完成后删除原文件
        fs.unlinkSync(filePath);
        resolve();
      });

      output.on('error', reject);
    });
  }

  /**
   * 记录轮转信息
   */
  logRotationInfo(compressed, deleted) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      event: 'log_rotation',
      compressed_files: compressed,
      deleted_files: deleted,
      retention_days: this.maxAge,
      compression_days: this.compressionAge
    };

    const logFile = path.join(this.logDir, 'rotation.log');
    fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
  }

  /**
   * 获取日志目录状态
   */
  getLogStats() {
    if (!fs.existsSync(this.logDir)) {
      return { totalFiles: 0, totalSize: 0, oldestFile: null, newestFile: null };
    }

    const files = fs.readdirSync(this.logDir)
      .filter(file => file.endsWith('.log') || file.endsWith('.gz'))
      .map(file => {
        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          mtime: stats.mtime
        };
      });

    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const oldestFile = files.reduce((oldest, file) => 
      !oldest || file.mtime < oldest.mtime ? file : oldest, null);
    const newestFile = files.reduce((newest, file) => 
      !newest || file.mtime > newest.mtime ? file : newest, null);

    return {
      totalFiles: files.length,
      totalSize,
      oldestFile: oldestFile ? oldestFile.name : null,
      newestFile: newestFile ? newestFile.name : null,
      files: files.sort((a, b) => b.mtime - a.mtime)
    };
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const logRotation = new LogRotation();
  
  // 显示当前状态
  console.log('📊 当前日志状态:');
  const stats = logRotation.getLogStats();
  console.log(`总文件数: ${stats.totalFiles}`);
  console.log(`总大小: ${logRotation.formatSize(stats.totalSize)}`);
  if (stats.oldestFile) console.log(`最旧文件: ${stats.oldestFile}`);
  if (stats.newestFile) console.log(`最新文件: ${stats.newestFile}`);
  console.log('');
  
  // 执行轮转
  logRotation.rotate();
}

module.exports = LogRotation;