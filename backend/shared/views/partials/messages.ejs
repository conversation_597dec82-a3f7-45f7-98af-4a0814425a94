<!-- 统一消息提示组件 -->
<% if (locals.messages) { %>
    <div class="messages-container">
        <% Object.keys(messages).forEach(function(type) { %>
            <% if (Array.isArray(messages[type])) { %>
                <% messages[type].forEach(function(message) { %>
                    <div class="alert alert-<%= type === 'error' ? 'danger' : type %> alert-dismissible fade show modern-alert" role="alert">
                        <div class="alert-content">
                            <div class="alert-icon">
                                <% if (type === 'success') { %>
                                    <i class="bi bi-check-circle"></i>
                                <% } else if (type === 'error' || type === 'danger') { %>
                                    <i class="bi bi-exclamation-triangle"></i>
                                <% } else if (type === 'warning') { %>
                                    <i class="bi bi-exclamation-circle"></i>
                                <% } else if (type === 'info') { %>
                                    <i class="bi bi-info-circle"></i>
                                <% } else { %>
                                    <i class="bi bi-chat-square-text"></i>
                                <% } %>
                            </div>
                            <div class="alert-body">
                                <% if (typeof message === 'object' && message.title) { %>
                                    <div class="alert-title"><%= message.title %></div>
                                    <div class="alert-text"><%= message.text %></div>
                                <% } else { %>
                                    <div class="alert-text"><%= message %></div>
                                <% } %>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
                    </div>
                <% }); %>
            <% } else if (messages[type]) { %>
                <div class="alert alert-<%= type === 'error' ? 'danger' : type %> alert-dismissible fade show modern-alert" role="alert">
                    <div class="alert-content">
                        <div class="alert-icon">
                            <% if (type === 'success') { %>
                                <i class="bi bi-check-circle"></i>
                            <% } else if (type === 'error' || type === 'danger') { %>
                                <i class="bi bi-exclamation-triangle"></i>
                            <% } else if (type === 'warning') { %>
                                <i class="bi bi-exclamation-circle"></i>
                            <% } else if (type === 'info') { %>
                                <i class="bi bi-info-circle"></i>
                            <% } else { %>
                                <i class="bi bi-chat-square-text"></i>
                            <% } %>
                        </div>
                        <div class="alert-body">
                            <% if (typeof messages[type] === 'object' && messages[type].title) { %>
                                <div class="alert-title"><%= messages[type].title %></div>
                                <div class="alert-text"><%= messages[type].text %></div>
                            <% } else { %>
                                <div class="alert-text"><%= messages[type] %></div>
                            <% } %>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
                </div>
            <% } %>
        <% }); %>
    </div>
<% } %>

<style>
    /* 消息提示样式 */
    .messages-container {
        margin-bottom: 20px;
    }
    
    .modern-alert {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        margin-bottom: 16px;
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.4s ease-out;
    }
    
    .modern-alert::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
    }
    
    .modern-alert.alert-success::before {
        background: var(--success-color);
    }
    
    .modern-alert.alert-danger::before {
        background: var(--danger-color);
    }
    
    .modern-alert.alert-warning::before {
        background: var(--warning-color);
    }
    
    .modern-alert.alert-info::before {
        background: var(--info-color);
    }
    
    .modern-alert.alert-primary::before {
        background: var(--primary-color);
    }
    
    .alert-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px 20px;
        padding-right: 50px; /* 为关闭按钮留空间 */
    }
    
    .alert-icon {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 14px;
        margin-top: 2px;
    }
    
    .alert-success .alert-icon {
        background: rgba(40, 167, 69, 0.15);
        color: var(--success-color);
    }
    
    .alert-danger .alert-icon {
        background: rgba(220, 53, 69, 0.15);
        color: var(--danger-color);
    }
    
    .alert-warning .alert-icon {
        background: rgba(255, 193, 7, 0.15);
        color: var(--warning-color);
    }
    
    .alert-info .alert-icon {
        background: rgba(23, 162, 184, 0.15);
        color: var(--info-color);
    }
    
    .alert-primary .alert-icon {
        background: rgba(40, 167, 69, 0.15);
        color: var(--primary-color);
    }
    
    .alert-body {
        flex: 1;
        min-width: 0;
    }
    
    .alert-title {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 4px;
        line-height: 1.3;
    }
    
    .alert-text {
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
    }
    
    .modern-alert .btn-close {
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 12px;
        opacity: 0.6;
        transition: opacity 0.2s ease;
    }
    
    .modern-alert .btn-close:hover {
        opacity: 1;
    }
    
    /* 不同类型的背景色 */
    .modern-alert.alert-success {
        background: rgba(40, 167, 69, 0.05);
        color: #0f5132;
    }
    
    .modern-alert.alert-danger {
        background: rgba(220, 53, 69, 0.05);
        color: #842029;
    }
    
    .modern-alert.alert-warning {
        background: rgba(255, 193, 7, 0.05);
        color: #664d03;
    }
    
    .modern-alert.alert-info {
        background: rgba(23, 162, 184, 0.05);
        color: #055160;
    }
    
    .modern-alert.alert-primary {
        background: rgba(40, 167, 69, 0.05);
        color: #0f5132;
    }
    
    /* 动画效果 */
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .modern-alert.fade {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    .modern-alert.fade:not(.show) {
        opacity: 0;
        transform: translateY(-10px);
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .alert-content {
            padding: 14px 16px;
            padding-right: 45px;
            gap: 10px;
        }
        
        .alert-icon {
            width: 20px;
            height: 20px;
            font-size: 12px;
        }
        
        .alert-title {
            font-size: 15px;
        }
        
        .alert-text {
            font-size: 13px;
        }
        
        .modern-alert .btn-close {
            right: 12px;
            top: 12px;
        }
    }
    
    /* 堆叠效果 */
    .messages-container .modern-alert:nth-child(1) {
        animation-delay: 0s;
    }
    
    .messages-container .modern-alert:nth-child(2) {
        animation-delay: 0.1s;
    }
    
    .messages-container .modern-alert:nth-child(3) {
        animation-delay: 0.2s;
    }
    
    .messages-container .modern-alert:nth-child(4) {
        animation-delay: 0.3s;
    }
    
    .messages-container .modern-alert:nth-child(5) {
        animation-delay: 0.4s;
    }
    
    /* 特殊样式：重要消息 */
    .modern-alert.alert-important {
        border: 2px solid var(--warning-color);
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    }
    
    .modern-alert.alert-important::before {
        background: linear-gradient(180deg, var(--warning-color) 0%, var(--danger-color) 100%);
        width: 6px;
    }
    
    .modern-alert.alert-important .alert-icon {
        background: var(--warning-color);
        color: white;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    /* 特殊样式：永久消息 */
    .modern-alert.alert-permanent {
        border: 2px solid var(--info-color);
        background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    }
    
    .modern-alert.alert-permanent .btn-close {
        display: none;
    }
    
    /* 特殊样式：加载中消息 */
    .modern-alert.alert-loading .alert-icon {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>

<script>
    // 消息提示增强功能
    document.addEventListener('DOMContentLoaded', function() {
        // 为所有消息添加进度条（自动消失）
        const alerts = document.querySelectorAll('.modern-alert:not(.alert-permanent)');
        
        alerts.forEach(function(alert, index) {
            // 添加进度条
            if (!alert.querySelector('.alert-progress')) {
                const progressBar = document.createElement('div');
                progressBar.className = 'alert-progress';
                alert.appendChild(progressBar);
                
                // 5秒后自动关闭（除了永久消息）
                setTimeout(function() {
                    if (alert && alert.parentNode) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000 + (index * 500)); // 错开关闭时间
            }
        });
        
        // 添加进度条样式
        if (!document.getElementById('alert-progress-styles')) {
            const style = document.createElement('style');
            style.id = 'alert-progress-styles';
            style.textContent = `
                .alert-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 3px;
                    background: rgba(255,255,255,0.3);
                    animation: progressBar 5s linear forwards;
                }
                
                @keyframes progressBar {
                    from { width: 100%; }
                    to { width: 0%; }
                }
                
                .modern-alert.alert-permanent .alert-progress {
                    display: none;
                }
            `;
            document.head.appendChild(style);
        }
    });
    
    // 全局消息显示函数
    window.showMessage = function(message, type = 'info', options = {}) {
        const {
            title = null,
            permanent = false,
            important = false,
            duration = 5000
        } = options;
        
        const messagesContainer = document.querySelector('.messages-container') || 
                                 createMessagesContainer();
        
        const alertDiv = document.createElement('div');
        const alertClass = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show modern-alert`;
        alertDiv.className = alertClass + 
                           (permanent ? ' alert-permanent' : '') +
                           (important ? ' alert-important' : '');
        
        alertDiv.innerHTML = `
            <div class="alert-content">
                <div class="alert-icon">
                    ${getAlertIcon(type)}
                </div>
                <div class="alert-body">
                    ${title ? `<div class="alert-title">${title}</div>` : ''}
                    <div class="alert-text">${message}</div>
                </div>
            </div>
            ${!permanent ? '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>' : ''}
            ${!permanent ? '<div class="alert-progress"></div>' : ''}
        `;
        
        messagesContainer.appendChild(alertDiv);
        
        // 自动关闭
        if (!permanent) {
            setTimeout(() => {
                if (alertDiv && alertDiv.parentNode) {
                    const bsAlert = new bootstrap.Alert(alertDiv);
                    bsAlert.close();
                }
            }, duration);
        }
        
        return alertDiv;
    };
    
    // 创建消息容器
    function createMessagesContainer() {
        const container = document.createElement('div');
        container.className = 'messages-container';
        
        const mainContent = document.querySelector('.container-fluid');
        if (mainContent) {
            mainContent.insertBefore(container, mainContent.firstChild);
        } else {
            document.body.appendChild(container);
        }
        
        return container;
    }
    
    // 获取图标
    function getAlertIcon(type) {
        const icons = {
            success: 'bi bi-check-circle',
            error: 'bi bi-exclamation-triangle',
            danger: 'bi bi-exclamation-triangle',
            warning: 'bi bi-exclamation-circle',
            info: 'bi bi-info-circle',
            primary: 'bi bi-info-circle'
        };
        
        return `<i class="${icons[type] || 'bi bi-chat-square-text'}"></i>`;
    }
    
    // 便捷方法
    window.showSuccess = function(message, options = {}) {
        return window.showMessage(message, 'success', options);
    };
    
    window.showError = function(message, options = {}) {
        return window.showMessage(message, 'error', options);
    };
    
    window.showWarning = function(message, options = {}) {
        return window.showMessage(message, 'warning', options);
    };
    
    window.showInfo = function(message, options = {}) {
        return window.showMessage(message, 'info', options);
    };
</script>