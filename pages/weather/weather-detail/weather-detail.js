// pages/weather/weather-detail/weather-detail.js
Page({
  data: {
    location: '北京市',
    currentTemp: 25,
    weatherDesc: '多云',
    weatherIcon: '/assets/icons/weather/cloudy.png',
    humidity: 76,
    windLevel: 2,
    airQuality: '良',
    updateTime: '14:16',
    forecast: [
      {
        date: '7/29 周一',
        icon: '/assets/icons/weather/sunny.png',
        minTemp: 21,
        maxTemp: 26
      },
      {
        date: '7/30 周二',
        icon: '/assets/icons/weather/rainy.png',
        minTemp: 17,
        maxTemp: 26
      },
      {
        date: '7/31 周三',
        icon: '/assets/icons/weather/cloudy.png',
        minTemp: 18,
        maxTemp: 23
      }
    ]
  },

  onLoad: function (options) {
    this.getWeatherData();
  },

  // 获取天气数据
  getWeatherData() {
    // 模拟API调用
    setTimeout(() => {
      const now = new Date();
      const timeStr = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
      
      this.setData({
        updateTime: timeStr
      });
    }, 500);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.getWeatherData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
