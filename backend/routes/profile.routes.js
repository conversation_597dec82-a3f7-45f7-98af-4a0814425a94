/**
 * 个人中心相关路由
 * Profile Routes
 */

const express = require("express");
const router = express.Router();
const { verifyToken } = require("../middleware/auth.middleware");
const {
  identifyTenant,
  authenticateTenantUser,
  recordTenantUsage,
} = require("../middleware/tenant.middleware");

// 应用中间件
router.use(verifyToken);
router.use(identifyTenant);
router.use(authenticateTenantUser);
router.use(recordTenantUsage);

/**
 * @route   GET /profile/settings
 * @desc    获取用户设置
 * @access  Private
 */
router.get("/settings", async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const userId = req.user.id;

    // 获取用户基本信息和设置
    const [userResult] = await tenantDb.query(
      `
      SELECT 
        id, username, name, email, phone, avatar, role, status,
        settings, preferences, notifications, privacy
      FROM users 
      WHERE id = ?
    `,
      { replacements: [userId] },
    );

    if (userResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: "用户不存在",
      });
    }

    const user = userResult[0];

    // 解析JSON字段
    const settings = user.settings ? JSON.parse(user.settings) : {};
    const preferences = user.preferences ? JSON.parse(user.preferences) : {};
    const notifications = user.notifications
      ? JSON.parse(user.notifications)
      : {};
    const privacy = user.privacy ? JSON.parse(user.privacy) : {};

    // 默认设置
    const defaultSettings = {
      theme: "light",
      language: "zh-CN",
      timezone: "Asia/Shanghai",
      autoSave: true,
      soundEnabled: true,
      vibrationEnabled: true,
    };

    const defaultNotifications = {
      healthAlerts: true,
      productionReminders: true,
      systemUpdates: true,
      emailNotifications: false,
      smsNotifications: false,
    };

    const defaultPrivacy = {
      profileVisibility: "private",
      dataSharing: false,
      analytics: true,
    };

    res.json({
      success: true,
      message: "获取用户设置成功",
      data: {
        userInfo: {
          id: user.id,
          username: user.username,
          name: user.name,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar,
          role: user.role,
          status: user.status,
        },
        settings: { ...defaultSettings, ...settings },
        preferences: preferences,
        notifications: { ...defaultNotifications, ...notifications },
        privacy: { ...defaultPrivacy, ...privacy },
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取用户设置失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取用户设置失败",
    });
  }
});

/**
 * @route   PUT /profile/settings
 * @desc    更新用户设置
 * @access  Private
 */
router.put("/settings", async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const userId = req.user.id;
    const { userInfo, settings, preferences, notifications, privacy } =
      req.body;

    // 构建更新字段
    const updateFields = [];
    const updateValues = [];

    if (userInfo) {
      if (userInfo.name) {
        updateFields.push("name = ?");
        updateValues.push(userInfo.name);
      }
      if (userInfo.email) {
        updateFields.push("email = ?");
        updateValues.push(userInfo.email);
      }
      if (userInfo.phone) {
        updateFields.push("phone = ?");
        updateValues.push(userInfo.phone);
      }
      if (userInfo.avatar) {
        updateFields.push("avatar = ?");
        updateValues.push(userInfo.avatar);
      }
    }

    if (settings) {
      updateFields.push("settings = ?");
      updateValues.push(JSON.stringify(settings));
    }

    if (preferences) {
      updateFields.push("preferences = ?");
      updateValues.push(JSON.stringify(preferences));
    }

    if (notifications) {
      updateFields.push("notifications = ?");
      updateValues.push(JSON.stringify(notifications));
    }

    if (privacy) {
      updateFields.push("privacy = ?");
      updateValues.push(JSON.stringify(privacy));
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: "没有提供更新数据",
      });
    }

    // 添加更新时间
    updateFields.push("updatedAt = NOW()");
    updateValues.push(userId);

    // 执行更新
    await tenantDb.query(
      `
      UPDATE users 
      SET ${updateFields.join(", ")}
      WHERE id = ?
    `,
      { replacements: updateValues },
    );

    res.json({
      success: true,
      message: "用户设置更新成功",
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('更新用户设置失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "更新用户设置失败",
    });
  }
});

/**
 * @route   GET /profile/help
 * @desc    获取帮助列表
 * @access  Private
 */
router.get("/help", async (req, res) => {
  try {
    // 帮助内容（可以存储在数据库或配置文件中）
    const helpCategories = [
      {
        id: 1,
        title: "快速入门",
        icon: "help-circle",
        items: [
          {
            id: 11,
            title: "如何添加鹅群",
            content: "在鹅群管理页面点击添加按钮...",
          },
          { id: 12, title: "如何记录生产数据", content: "进入生产管理页面..." },
          { id: 13, title: "如何使用AI诊断", content: "在健康管理页面..." },
        ],
      },
      {
        id: 2,
        title: "功能说明",
        icon: "book",
        items: [
          { id: 21, title: "健康管理功能", content: "健康管理模块包括..." },
          { id: 22, title: "生产管理功能", content: "生产管理模块包括..." },
          { id: 23, title: "商城购买指南", content: "在商城页面可以..." },
        ],
      },
      {
        id: 3,
        title: "常见问题",
        icon: "question-circle",
        items: [
          {
            id: 31,
            title: "数据同步问题",
            content: "如果数据不同步，请检查网络连接...",
          },
          { id: 32, title: "登录问题", content: "如果无法登录，请确认..." },
          { id: 33, title: "功能限制", content: "不同订阅版本功能不同..." },
        ],
      },
      {
        id: 4,
        title: "联系支持",
        icon: "phone",
        items: [
          { id: 41, title: "技术支持热线", content: "************" },
          { id: 42, title: "在线客服", content: "工作时间：9:00-18:00" },
          { id: 43, title: "邮件支持", content: "<EMAIL>" },
        ],
      },
    ];

    res.json({
      success: true,
      message: "获取帮助列表成功",
      data: helpCategories,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取帮助列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取帮助列表失败",
    });
  }
});

/**
 * @route   GET /profile/help/:id
 * @desc    获取帮助详情
 * @access  Private
 */
router.get("/help/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // 这里应该从数据库获取具体的帮助内容
    // 暂时返回模拟数据
    const helpDetail = {
      id: parseInt(id),
      title: "帮助详情",
      content: "详细的帮助内容...",
      lastUpdated: new Date(),
      relatedItems: [],
    };

    res.json({
      success: true,
      message: "获取帮助详情成功",
      data: helpDetail,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取帮助详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取帮助详情失败",
    });
  }
});

module.exports = router;
