<!-- 无权限访问页面组件 -->
<view class="permission-denied-page">
  <!-- 权限拒绝图标 -->
  <view class="permission-denied-icon">
    <text>🚫</text>
  </view>
  
  <!-- 标题 -->
  <view class="permission-denied-title">
    <text>{{title || '访问受限'}}</text>
  </view>
  
  <!-- 描述文本 -->
  <view class="permission-denied-description">
    <text>{{description || '抱歉，您当前没有权限访问此功能。如需使用，请联系管理员申请相应权限。'}}</text>
  </view>
  
  <!-- 操作按钮组 -->
  <view class="permission-denied-actions">
    <!-- 申请权限按钮 -->
    <button wx:if="{{showRequestBtn}}" class="permission-request-btn" bindtap="onRequestPermission">
      <text>{{requestBtnText || '申请权限'}}</text>
    </button>
    
    <!-- 返回按钮 -->
    <button wx:if="{{showBackBtn}}" class="btn btn-secondary btn-md" bindtap="onGoBack">
      <text>{{backBtnText || '返回'}}</text>
    </button>
    
    <!-- 联系管理员按钮 -->
    <button wx:if="{{showContactBtn}}" class="btn btn-ghost btn-md" bindtap="onContactAdmin">
      <text>{{contactBtnText || '联系管理员'}}</text>
    </button>
  </view>
  
  <!-- 权限详情 (可选) -->
  <view wx:if="{{showDetails}}" class="permission-details">
    <view class="permission-details-title">权限详情</view>
    <view class="permission-details-item" wx:for="{{permissionList}}" wx:key="id">
      <view class="permission-icon permission-icon--{{item.status}}"></view>
      <text class="permission-name">{{item.name}}</text>
      <view class="permission-indicator permission-indicator--{{item.level}}">
        <text>{{item.levelText}}</text>
      </view>
    </view>
  </view>
</view>