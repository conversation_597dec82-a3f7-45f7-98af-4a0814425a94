<!-- 财务集成演示页面 -->
<view class="finance-integration-demo">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">财务集成系统</view>
    <view class="header-subtitle">生产记录与财务数据自动关联</view>
  </view>

  <!-- 财务概览卡片 -->
  <view class="overview-card">
    <view class="overview-title">总体财务概览</view>
    <view class="overview-stats">
      <view class="stat-item">
        <view class="stat-value">¥{{financeStats.totalCost || 0}}</view>
        <view class="stat-label">总成本</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">¥{{financeStats.totalRevenue || 0}}</view>
        <view class="stat-label">总收入</view>
      </view>
      <view class="stat-item profit-{{financeStats.totalProfit >= 0 ? 'positive' : 'negative'}}">
        <view class="stat-value">¥{{financeStats.totalProfit || 0}}</view>
        <view class="stat-label">总利润</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{financeStats.overallProfitMargin || 0}}%</view>
        <view class="stat-label">利润率</view>
      </view>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view 
      wx:for="{{tabs}}" 
      wx:key="id" 
      class="tab-item {{activeTab === item.id ? 'active' : ''}}"
      data-id="{{item.id}}"
      bindtap="onTabChange"
    >
      <text class="tab-icon">{{item.icon}}</text>
      <text class="tab-name">{{item.name}}</text>
    </view>
  </view>

  <!-- 标签页内容 -->
  <view class="tab-content">
    <!-- 批次概览 -->
    <view wx:if="{{activeTab === 0}}" class="tab-panel">
      <view class="panel-header">
        <text class="panel-title">批次概览</text>
        <text class="panel-subtitle">共{{batches.length}}个批次</text>
      </view>
      
      <scroll-view class="batch-list" scroll-y>
        <view 
          wx:for="{{batches}}" 
          wx:key="batchNumber" 
          class="batch-item {{selectedBatch === item.batchNumber ? 'selected' : ''}}"
          data-batch="{{item.batchNumber}}"
          bindtap="onBatchSelect"
        >
          <view class="batch-header">
            <view class="batch-number">{{item.batchNumber}}</view>
            <view class="batch-status status-{{item.status}}">{{item.status}}</view>
          </view>
          <view class="batch-info">
            <text>{{item.breedName}} • {{item.currentCount}}/{{item.totalCount}}只</text>
          </view>
          <view class="batch-finance">
            <view class="finance-item">
              <text class="finance-label">成本</text>
              <text class="finance-value">¥{{item.totalCost || 0}}</text>
            </view>
            <view class="finance-item">
              <text class="finance-label">收入</text>
              <text class="finance-value">¥{{item.totalRevenue || 0}}</text>
            </view>
            <view class="finance-item profit-{{item.netProfit >= 0 ? 'positive' : 'negative'}}">
              <text class="finance-label">利润</text>
              <text class="finance-value">¥{{item.netProfit || 0}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 生产记录 -->
    <view wx:elif="{{activeTab === 1}}" class="tab-panel">
      <view class="panel-header">
        <text class="panel-title">生产记录</text>
        <view class="add-buttons">
          <view class="add-btn" data-type="entry" bindtap="onShowAddModal">入栏</view>
          <view class="add-btn" data-type="weight" bindtap="onShowAddModal">称重</view>
          <view class="add-btn" data-type="sale" bindtap="onShowAddModal">出栏</view>
        </view>
      </view>

      <!-- 记录类型筛选 -->
      <view class="filter-tabs">
        <view 
          wx:for="{{productionRecordTypes}}" 
          wx:key="value" 
          class="filter-tab {{selectedRecordType === item.value ? 'active' : ''}}"
          data-type="{{item.value}}"
          bindtap="onRecordTypeChange"
        >
          {{item.label}}
        </view>
      </view>

      <!-- 生产记录列表 -->
      <scroll-view class="record-list" scroll-y>
        <view wx:for="{{productionRecords}}" wx:key="id" class="record-item record-{{item.type}}">
          <view class="record-header">
            <view class="record-type">{{item.type_name}}</view>
            <view class="record-date">{{item.date}}</view>
          </view>
          <view class="record-content">
            <view wx:if="{{item.type === 'entry'}}" class="entry-info">
              <text>入栏{{item.count}}只 • {{item.breed}} • ¥{{item.total_cost}}</text>
              <text class="supplier">供应商：{{item.supplier}}</text>
            </view>
            <view wx:elif="{{item.type === 'sale'}}" class="sale-info">
              <text>出栏{{item.sale_count}}只 • ¥{{item.total_income}}</text>
              <text class="buyer">买家：{{item.buyer}}</text>
            </view>
            <view wx:elif="{{item.type === 'weight'}}" class="weight-info">
              <text>称重{{item.weight_count}}只 • 平均{{item.average_weight}}kg</text>
            </view>
          </view>
          <view wx:if="{{item.finance_record_id}}" class="finance-link">
            <text class="link-icon">🔗</text>
            <text class="link-text">已关联财务记录</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 财务记录 -->
    <view wx:elif="{{activeTab === 2}}" class="tab-panel">
      <view class="panel-header">
        <text class="panel-title">财务记录</text>
        <text class="panel-subtitle">批次：{{selectedBatch}}</text>
      </view>

      <!-- 使用批次财务统计组件 -->
      <batch-finance-stats 
        batch-number="{{selectedBatch}}"
        show-details="{{true}}"
        style-type="card"
        bind:statsLoaded="onStatsLoaded"
        bind:recordTap="onRecordTap"
        bind:exportData="onExportData"
        bind:viewTrends="onViewTrends"
      />
    </view>

    <!-- 数据分析 -->
    <view wx:elif="{{activeTab === 3}}" class="tab-panel">
      <view class="panel-header">
        <text class="panel-title">数据分析</text>
        <text class="panel-subtitle">趋势与统计</text>
      </view>

      <!-- 趋势图表 -->
      <view class="chart-section">
        <view class="chart-title">财务趋势</view>
        <view class="chart-placeholder">
          <text>📈 趋势图表区域</text>
          <text class="chart-note">显示收入、支出、利润趋势</text>
        </view>
      </view>

      <!-- 分类统计 -->
      <view class="category-section">
        <view class="category-title">分类统计</view>
        <view wx:for="{{categoryStats}}" wx:key="category" class="category-item">
          <view class="category-info">
            <text class="category-name">{{item.category}}</text>
            <text class="category-type type-{{item.type}}">{{item.type === 'income' ? '收入' : '支出'}}</text>
          </view>
          <view class="category-amount">¥{{item.totalAmount}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加记录弹窗 -->
  <view wx:if="{{showAddModal}}" class="modal-overlay" bindtap="onCloseAddModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">
          {{addModalType === 'entry' ? '添加入栏记录' : 
            addModalType === 'sale' ? '添加出栏记录' : '添加称重记录'}}
        </text>
        <view class="modal-close" bindtap="onCloseAddModal">×</view>
      </view>

      <scroll-view class="modal-body" scroll-y>
        <!-- 通用字段 -->
        <view class="form-group">
          <text class="form-label">批次号</text>
          <input 
            class="form-input" 
            value="{{newRecord.batchNumber}}" 
            data-field="batchNumber"
            bindinput="onRecordInput"
            placeholder="请输入批次号"
          />
        </view>

        <view class="form-group">
          <text class="form-label">记录日期</text>
          <picker 
            mode="date" 
            value="{{newRecord.recordDate}}" 
            data-field="recordDate"
            bindchange="onRecordInput"
          >
            <view class="form-input">{{newRecord.recordDate || '请选择日期'}}</view>
          </picker>
        </view>

        <!-- 入栏记录特有字段 -->
        <block wx:if="{{addModalType === 'entry'}}">
          <view class="form-group">
            <text class="form-label">数量（只）</text>
            <input 
              class="form-input" 
              type="number" 
              value="{{newRecord.count}}" 
              data-field="count"
              bindinput="onRecordInput"
              placeholder="请输入数量"
            />
          </view>
          <view class="form-group">
            <text class="form-label">单价（元/只）</text>
            <input 
              class="form-input" 
              type="digit" 
              value="{{newRecord.unitCost}}" 
              data-field="unitCost"
              bindinput="onRecordInput"
              placeholder="请输入单价"
            />
          </view>
          <view class="form-group">
            <text class="form-label">总成本（元）</text>
            <input 
              class="form-input" 
              type="digit" 
              value="{{newRecord.totalCost}}" 
              data-field="totalCost"
              bindinput="onRecordInput"
              placeholder="自动计算或手动输入"
            />
          </view>
          <view class="form-group">
            <text class="form-label">供应商</text>
            <input 
              class="form-input" 
              value="{{newRecord.supplier}}" 
              data-field="supplier"
              bindinput="onRecordInput"
              placeholder="请输入供应商名称"
            />
          </view>
        </block>

        <!-- 出栏记录特有字段 -->
        <block wx:elif="{{addModalType === 'sale'}}">
          <view class="form-group">
            <text class="form-label">出栏数量（只）</text>
            <input 
              class="form-input" 
              type="number" 
              value="{{newRecord.count}}" 
              data-field="count"
              bindinput="onRecordInput"
              placeholder="请输入出栏数量"
            />
          </view>
          <view class="form-group">
            <text class="form-label">单价（元/只）</text>
            <input 
              class="form-input" 
              type="digit" 
              value="{{newRecord.unitPrice}}" 
              data-field="unitPrice"
              bindinput="onRecordInput"
              placeholder="请输入单价"
            />
          </view>
          <view class="form-group">
            <text class="form-label">总收入（元）</text>
            <input 
              class="form-input" 
              type="digit" 
              value="{{newRecord.totalIncome}}" 
              data-field="totalIncome"
              bindinput="onRecordInput"
              placeholder="自动计算或手动输入"
            />
          </view>
          <view class="form-group">
            <text class="form-label">买家</text>
            <input 
              class="form-input" 
              value="{{newRecord.buyer}}" 
              data-field="buyer"
              bindinput="onRecordInput"
              placeholder="请输入买家名称"
            />
          </view>
        </block>

        <!-- 称重记录特有字段 -->
        <block wx:elif="{{addModalType === 'weight'}}">
          <view class="form-group">
            <text class="form-label">称重数量（只）</text>
            <input 
              class="form-input" 
              type="number" 
              value="{{newRecord.count}}" 
              data-field="count"
              bindinput="onRecordInput"
              placeholder="请输入称重数量"
            />
          </view>
          <view class="form-group">
            <text class="form-label">平均体重（kg）</text>
            <input 
              class="form-input" 
              type="digit" 
              value="{{newRecord.averageWeight}}" 
              data-field="averageWeight"
              bindinput="onRecordInput"
              placeholder="请输入平均体重"
            />
          </view>
          <view class="form-group">
            <text class="form-label">总重量（kg）</text>
            <input 
              class="form-input" 
              type="digit" 
              value="{{newRecord.totalWeight}}" 
              data-field="totalWeight"
              bindinput="onRecordInput"
              placeholder="自动计算或手动输入"
            />
          </view>
        </block>

        <view class="form-group">
          <text class="form-label">备注</text>
          <textarea 
            class="form-textarea" 
            value="{{newRecord.notes}}" 
            data-field="notes"
            bindinput="onRecordInput"
            placeholder="请输入备注信息"
          />
        </view>
      </scroll-view>

      <view class="modal-footer">
        <view class="modal-btn cancel" bindtap="onCloseAddModal">取消</view>
        <view class="modal-btn confirm" bindtap="onSubmitRecord">确定</view>
      </view>
    </view>
  </view>
</view>
