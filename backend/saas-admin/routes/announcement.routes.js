const express = require("express");
const router = express.Router();

// 公告列表
router.get("/", async (req, res) => {
  try {
    // 模拟数据 - 实际应该从数据库获取
    const announcements = [
      {
        id: 1,
        title: "春节期间休假安排",
        content: "春节期间平台服务安排通知",
        type: "notice",
        status: "published",
        publishTime: "2024-01-10T09:00:00Z",
        expireTime: "2024-02-10T09:00:00Z",
        viewCount: 1250,
        createdAt: "2024-01-10T09:00:00Z",
      },
      {
        id: 2,
        title: "系统升级通知",
        content: "平台将于1月20日晚进行系统升级",
        type: "important",
        status: "published",
        publishTime: "2024-01-15T14:00:00Z",
        expireTime: "2024-01-25T14:00:00Z",
        viewCount: 980,
        createdAt: "2024-01-15T14:00:00Z",
      },
    ];

    res.json({
      success: true,
      message: "获取公告列表成功",
      data: announcements,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取公告列表失败",
      error: error.message,
    });
  }
});

// 公告详情
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    // 模拟数据 - 实际应该从数据库获取
    const announcement = {
      id: parseInt(id),
      title: "春节期间休假安排",
      content: "春节期间平台服务安排通知",
      type: "notice",
      status: "published",
      publishTime: "2024-01-10T09:00:00Z",
      expireTime: "2024-02-10T09:00:00Z",
      viewCount: 1250,
      createdAt: "2024-01-10T09:00:00Z",
      updatedAt: "2024-01-10T09:00:00Z",
    };

    res.json({
      success: true,
      message: "获取公告详情成功",
      data: announcement,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取公告详情失败",
      error: error.message,
    });
  }
});

// 创建公告
router.post("/", async (req, res) => {
  try {
    const { title, content, type, status, publishTime, expireTime } = req.body;

    // 模拟创建公告 - 实际应该保存到数据库
    const newAnnouncement = {
      id: Math.floor(Math.random() * 1000) + 3,
      title,
      content,
      type,
      status,
      publishTime,
      expireTime,
      viewCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "创建公告成功",
      data: newAnnouncement,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "创建公告失败",
      error: error.message,
    });
  }
});

// 更新公告
router.put("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, type, status, publishTime, expireTime } = req.body;

    // 模拟更新公告 - 实际应该更新数据库记录
    const updatedAnnouncement = {
      id: parseInt(id),
      title,
      content,
      type,
      status,
      publishTime,
      expireTime,
      viewCount: 1250,
      createdAt: "2024-01-10T09:00:00Z",
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "更新公告成功",
      data: updatedAnnouncement,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "更新公告失败",
      error: error.message,
    });
  }
});

// 删除公告
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    // 模拟删除公告 - 实际应该从数据库删除

    res.json({
      success: true,
      message: "删除公告成功",
      data: { id: parseInt(id) },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "删除公告失败",
      error: error.message,
    });
  }
});

module.exports = router;
