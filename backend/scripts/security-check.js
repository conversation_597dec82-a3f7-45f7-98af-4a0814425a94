/**
 * 安全检查脚本
 * 检查项目中的潜在安全问题
 */

const fs = require('fs');
const path = require('path');

class SecurityCheck {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.suggestions = [];
  }

  /**
   * 运行安全检查
   */
  async runCheck() {
    console.log('🔒 开始安全检查...\n');

    // 检查环境变量
    this.checkEnvironmentVariables();
    
    // 检查硬编码密钥
    await this.checkHardcodedSecrets();
    
    // 检查文件权限
    this.checkFilePermissions();
    
    // 检查依赖安全
    await this.checkDependencySecurity();
    
    // 生成报告
    this.generateReport();
  }

  /**
   * 检查环境变量
   */
  checkEnvironmentVariables() {
    console.log('🔍 检查环境变量配置...');
    
    const requiredEnvVars = [
      'JWT_SECRET',
      'DB_PASSWORD',
      'SESSION_SECRET'
    ];

    const recommendedEnvVars = [
      'DB_HOST',
      'DB_USERNAME',
      'DB_NAME',
      'NODE_ENV'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        this.issues.push({
          type: 'MISSING_ENV_VAR',
          severity: 'HIGH',
          message: `缺少关键环境变量: ${envVar}`,
          suggestion: `请在.env文件中设置 ${envVar}=your_secure_value`
        });
      } else if (process.env[envVar].length < 8) {
        this.warnings.push({
          type: 'WEAK_SECRET',
          severity: 'MEDIUM',
          message: `环境变量 ${envVar} 值过短 (当前: ${process.env[envVar].length} 字符)`,
          suggestion: `建议使用至少32字符的强密钥`
        });
      }
    }

    for (const envVar of recommendedEnvVars) {
      if (!process.env[envVar]) {
        this.suggestions.push({
          type: 'RECOMMENDED_ENV_VAR',
          severity: 'LOW',
          message: `建议设置环境变量: ${envVar}`,
          suggestion: `设置 ${envVar} 以提高安全性和可配置性`
        });
      }
    }
  }

  /**
   * 检查硬编码密钥
   */
  async checkHardcodedSecrets() {
    console.log('🔍 检查硬编码密钥...');
    
    const patterns = [
      { regex: /["']default_secret["']/g, name: 'Default Secret' },
      { regex: /password\s*[:=]\s*["'][^"']{1,8}["']/gi, name: 'Weak Password' },
      { regex: /api_key\s*[:=]\s*["'][^"']+["']/gi, name: 'API Key' },
      { regex: /secret\s*[:=]\s*["'][^"']+["']/gi, name: 'Secret Key' }
    ];

    const checkFile = (filePath) => {
      if (!fs.existsSync(filePath) || !filePath.endsWith('.js')) return;
      
      const content = fs.readFileSync(filePath, 'utf8');
      
      patterns.forEach(pattern => {
        const matches = content.match(pattern.regex);
        if (matches) {
          matches.forEach(match => {
            // 跳过一些已知的安全例外
            if (match.includes('process.env') || match.includes('example') || match.includes('test')) {
              return;
            }
            
            this.issues.push({
              type: 'HARDCODED_SECRET',
              severity: 'HIGH',
              message: `发现硬编码的${pattern.name}: ${match}`,
              file: filePath,
              suggestion: '使用环境变量替代硬编码的密钥'
            });
          });
        }
      });
    };

    // 递归检查所有JS文件
    const checkDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
          checkDirectory(filePath);
        } else if (stat.isFile()) {
          checkFile(filePath);
        }
      });
    };

    checkDirectory(process.cwd());
  }

  /**
   * 检查文件权限
   */
  checkFilePermissions() {
    console.log('🔍 检查文件权限...');
    
    const sensitiveFiles = [
      '.env',
      '.env.local', 
      '.env.production',
      'config/database.js',
      'package.json'
    ];

    sensitiveFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        const mode = stats.mode.toString(8);
        
        // 检查是否对其他用户可读
        if (mode.endsWith('4') || mode.endsWith('6') || mode.endsWith('7')) {
          this.warnings.push({
            type: 'FILE_PERMISSION',
            severity: 'MEDIUM',
            message: `敏感文件 ${file} 对其他用户可读 (权限: ${mode})`,
            suggestion: `运行: chmod 600 ${file}`
          });
        }
      }
    });
  }

  /**
   * 检查依赖安全
   */
  async checkDependencySecurity() {
    console.log('🔍 检查依赖安全...');
    
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      // 检查已知的有问题的包版本
      const vulnerablePackages = {
        'jsonwebtoken': ['8.5.1', '8.5.0'],
        'mysql2': ['3.9.7', '3.9.6'],
        'multer': ['2.0.1', '2.0.0']
      };

      Object.entries(dependencies).forEach(([pkg, version]) => {
        if (vulnerablePackages[pkg]) {
          const cleanVersion = version.replace(/[\^~]/, '');
          if (vulnerablePackages[pkg].includes(cleanVersion)) {
            this.issues.push({
              type: 'VULNERABLE_DEPENDENCY',
              severity: 'HIGH',
              message: `依赖包 ${pkg}@${version} 存在已知漏洞`,
              suggestion: `运行 npm audit fix 更新到安全版本`
            });
          }
        }
      });

    } catch (error) {
      this.warnings.push({
        type: 'PACKAGE_CHECK_FAILED',
        severity: 'LOW',
        message: '无法读取package.json文件',
        suggestion: '请确保package.json文件存在且格式正确'
      });
    }
  }

  /**
   * 生成安全报告
   */
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🔒 安全检查报告');
    console.log('='.repeat(60));

    console.log(`\n📊 检查统计:`);
    console.log(`   🔴 严重问题: ${this.issues.length}`);
    console.log(`   🟡 警告: ${this.warnings.length}`);
    console.log(`   💡 建议: ${this.suggestions.length}`);

    if (this.issues.length > 0) {
      console.log(`\n🔴 严重问题:`);
      this.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. [${issue.severity}] ${issue.message}`);
        if (issue.file) console.log(`      文件: ${issue.file}`);
        console.log(`      建议: ${issue.suggestion}\n`);
      });
    }

    if (this.warnings.length > 0) {
      console.log(`\n🟡 警告:`);
      this.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. [${warning.severity}] ${warning.message}`);
        console.log(`      建议: ${warning.suggestion}\n`);
      });
    }

    if (this.suggestions.length > 0) {
      console.log(`\n💡 建议:`);
      this.suggestions.forEach((suggestion, index) => {
        console.log(`   ${index + 1}. ${suggestion.message}`);
        console.log(`      建议: ${suggestion.suggestion}\n`);
      });
    }

    // 总体评分
    const totalIssues = this.issues.length + this.warnings.length;
    let score = Math.max(0, 100 - (this.issues.length * 20) - (this.warnings.length * 5));
    let grade = 'A';
    
    if (score < 60) grade = 'F';
    else if (score < 70) grade = 'D';
    else if (score < 80) grade = 'C';
    else if (score < 90) grade = 'B';

    console.log(`\n📈 安全评分: ${score}/100 (${grade}级)`);
    
    if (totalIssues === 0) {
      console.log('🎉 恭喜！没有发现安全问题');
    } else {
      console.log('⚠️ 请解决上述安全问题以提高项目安全性');
    }

    console.log('\n' + '='.repeat(60));

    // 保存报告到文件
    this.saveReport();
  }

  /**
   * 保存报告到文件
   */
  saveReport() {
    const report = {
      timestamp: new Date().toISOString(),
      issues: this.issues,
      warnings: this.warnings,
      suggestions: this.suggestions,
      summary: {
        total_issues: this.issues.length,
        total_warnings: this.warnings.length,
        total_suggestions: this.suggestions.length
      }
    };

    const reportPath = path.join(__dirname, `../logs/security-report-${Date.now()}.json`);
    
    try {
      // 确保logs目录存在
      const logsDir = path.dirname(reportPath);
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
      
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`📄 详细报告已保存到: ${reportPath}`);
    } catch (error) {
      console.error('❌ 保存报告失败:', error.message);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const securityCheck = new SecurityCheck();
  securityCheck.runCheck();
}

module.exports = SecurityCheck;