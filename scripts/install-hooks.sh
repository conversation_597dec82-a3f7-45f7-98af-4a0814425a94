#!/bin/bash

# 智慧养鹅SAAS平台 - Git Hooks 安装脚本
# 用于安装pre-commit hooks以确保代码质量

echo "🔧 安装Git Hooks..."

# 检查是否在Git仓库中
if [ ! -d ".git" ]; then
    echo "❌ 错误: 当前目录不是Git仓库"
    exit 1
fi

# 创建pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

echo "🔍 运行pre-commit检查..."

# 运行代码格式化
echo "📝 格式化代码..."
npm run format

# 运行代码检查
echo "🔍 检查代码质量..."
npm run lint
if [ $? -ne 0 ]; then
    echo "❌ 代码质量检查失败，请修复后重新提交"
    exit 1
fi

# 运行一致性检查
echo "🔄 检查一致性..."
npm run check:consistency
if [ $? -ne 0 ]; then
    echo "❌ 一致性检查失败，请修复后重新提交"
    exit 1
fi

# 运行标准检查
echo "📏 检查开发标准..."
npm run check:standards
if [ $? -ne 0 ]; then
    echo "❌ 开发标准检查失败，请修复后重新提交"
    exit 1
fi

echo "✅ Pre-commit检查通过！"
EOF

# 设置可执行权限
chmod +x .git/hooks/pre-commit

echo "✅ Git Hooks安装完成！"
echo ""
echo "📋 已安装的Hooks:"
echo "   - pre-commit: 代码质量和一致性检查"
echo ""
echo "💡 使用说明:"
echo "   - 每次git commit时会自动运行检查"
echo "   - 如需跳过检查，使用: git commit --no-verify"
echo "   - 手动运行检查: npm run pre-commit"