/**
 * 业务逻辑常量
 * 统一管理业务状态、枚举值、限制条件等
 */

// ===== 用户相关 =====
const USER = {
  // 角色类型
  ROLES: {
    ADMIN: 'admin',
    MANAGER: 'manager',
    USER: 'user',
    GUEST: 'guest'
  },
  
  // 角色显示名称
  ROLE_NAMES: {
    admin: '管理员',
    manager: '管理者',
    user: '用户',
    guest: '访客'
  },
  
  // 用户状态
  STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    PENDING: 'pending',
    BANNED: 'banned'
  },
  
  // 用户状态显示名称
  STATUS_NAMES: {
    active: '活跃',
    inactive: '非活跃',
    pending: '待激活',
    banned: '已禁用'
  },
  
  // 默认设置
  DEFAULTS: {
    AVATAR: '/images/default_avatar.png',
    NAME: '用户',
    FARM_NAME: '我的养殖场'
  }
};

// ===== 鹅群管理 =====
const FLOCK = {
  // 鹅的品种
  BREEDS: {
    WHITE_GOOSE: 'white_goose',
    GRAY_GOOSE: 'gray_goose',
    BROWN_GOOSE: 'brown_goose',
    MIXED: 'mixed'
  },
  
  // 品种显示名称
  BREED_NAMES: {
    white_goose: '白鹅',
    gray_goose: '灰鹅',
    brown_goose: '棕鹅',
    mixed: '混合品种'
  },
  
  // 年龄组
  AGE_GROUPS: {
    YOUNG: 'young',        // 幼鹅 (0-3个月)
    ADULT: 'adult',        // 成鹅 (3-12个月)
    BREEDING: 'breeding',  // 种鹅 (12个月以上)
    RETIRED: 'retired'     // 淘汰鹅
  },
  
  // 年龄组显示名称
  AGE_GROUP_NAMES: {
    young: '幼鹅',
    adult: '成鹅',
    breeding: '种鹅',
    retired: '淘汰鹅'
  },
  
  // 鹅群状态
  STATUS: {
    ACTIVE: 'active',      // 活跃
    INACTIVE: 'inactive',  // 非活跃
    SOLD: 'sold',          // 已售出
    DECEASED: 'deceased'   // 已死亡
  },
  
  // 状态显示名称
  STATUS_NAMES: {
    active: '活跃',
    inactive: '非活跃',
    sold: '已售出',
    deceased: '已死亡'
  },
  
  // 数量限制
  LIMITS: {
    MIN_COUNT: 1,          // 最小数量
    MAX_COUNT: 100000,     // 最大数量
    MAX_NAME_LENGTH: 100,  // 名称最大长度
    MAX_DESCRIPTION_LENGTH: 500  // 描述最大长度
  }
};

// ===== 健康管理 =====
const HEALTH = {
  // 检查类型
  CHECK_TYPES: {
    ROUTINE: 'routine',        // 常规检查
    VACCINATION: 'vaccination', // 疫苗接种
    TREATMENT: 'treatment',    // 治疗
    EMERGENCY: 'emergency'     // 紧急处理
  },
  
  // 检查类型显示名称
  CHECK_TYPE_NAMES: {
    routine: '常规检查',
    vaccination: '疫苗接种',
    treatment: '治疗',
    emergency: '紧急处理'
  },
  
  // 健康结果
  RESULTS: {
    HEALTHY: 'healthy',    // 健康
    SICK: 'sick',          // 生病
    RECOVERING: 'recovering', // 恢复中
    CRITICAL: 'critical'   // 危重
  },
  
  // 健康结果显示名称
  RESULT_NAMES: {
    healthy: '健康',
    sick: '生病',
    recovering: '恢复中',
    critical: '危重'
  },
  
  // 健康状态等级
  STATUS_LEVELS: {
    EXCELLENT: 'excellent', // 优秀
    GOOD: 'good',          // 良好
    FAIR: 'fair',          // 一般
    POOR: 'poor'           // 较差
  },
  
  // 状态等级显示名称
  STATUS_LEVEL_NAMES: {
    excellent: '优秀',
    good: '良好',
    fair: '一般',
    poor: '较差'
  },
  
  // 疫苗类型
  VACCINE_TYPES: {
    AVIAN_INFLUENZA: 'avian_influenza',  // 禽流感
    NEWCASTLE: 'newcastle',               // 新城疫
    HEPATITIS: 'hepatitis',               // 肝炎
    OTHER: 'other'                        // 其他
  },
  
  // 疫苗类型显示名称
  VACCINE_TYPE_NAMES: {
    avian_influenza: '禽流感疫苗',
    newcastle: '新城疫疫苗',
    hepatitis: '肝炎疫苗',
    other: '其他疫苗'
  }
};

// ===== 生产管理 =====
const PRODUCTION = {
  // 记录类型
  RECORD_TYPES: {
    GROWTH: 'growth',      // 生长记录
    LAYING: 'laying',      // 产蛋记录
    FEEDING: 'feeding',    // 喂养记录
    WEIGHING: 'weighing',  // 称重记录
    SLAUGHTER: 'slaughter' // 出栏记录
  },
  
  // 记录类型显示名称
  RECORD_TYPE_NAMES: {
    growth: '生长记录',
    laying: '产蛋记录',
    feeding: '喂养记录',
    weighing: '称重记录',
    slaughter: '出栏记录'
  },
  
  // 天气条件
  WEATHER_CONDITIONS: {
    SUNNY: 'sunny',        // 晴天
    CLOUDY: 'cloudy',      // 多云
    RAINY: 'rainy',        // 雨天
    SNOWY: 'snowy',        // 雪天
    WINDY: 'windy',        // 大风
    FOGGY: 'foggy'         // 雾天
  },
  
  // 天气条件显示名称
  WEATHER_NAMES: {
    sunny: '晴天',
    cloudy: '多云',
    rainy: '雨天',
    snowy: '雪天',
    windy: '大风',
    foggy: '雾天'
  },
  
  // 饲料类型
  FEED_TYPES: {
    STARTER: 'starter',    // 雏鹅料
    GROWER: 'grower',      // 生长料
    FINISHER: 'finisher',  // 育肥料
    BREEDER: 'breeder'     // 种鹅料
  },
  
  // 饲料类型显示名称
  FEED_TYPE_NAMES: {
    starter: '雏鹅专用饲料',
    grower: '生长期饲料',
    finisher: '育肥期饲料',
    breeder: '种鹅饲料'
  },
  
  // 生产指标限制
  LIMITS: {
    MAX_DAILY_EGG_PRODUCTION: 10,    // 每只鹅日最大产蛋数
    MAX_WEIGHT: 20,                  // 最大重量(kg)
    MAX_FEED_CONSUMPTION: 500,       // 最大日饲料消耗(g)
    MIN_TEMPERATURE: -20,            // 最低温度(°C)
    MAX_TEMPERATURE: 50,             // 最高温度(°C)
    MIN_HUMIDITY: 0,                 // 最低湿度(%)
    MAX_HUMIDITY: 100                // 最高湿度(%)
  }
};

// ===== 库存管理 =====
const INVENTORY = {
  // 物料类别
  CATEGORIES: {
    FEED: 'feed',          // 饲料
    MEDICINE: 'medicine',  // 药品
    EQUIPMENT: 'equipment', // 设备
    MATERIALS: 'materials', // 物料
    OTHER: 'other'         // 其他
  },
  
  // 类别显示名称
  CATEGORY_NAMES: {
    feed: '饲料',
    medicine: '药品',
    equipment: '设备',
    materials: '物料',
    other: '其他'
  },
  
  // 库存状态
  STATUS: {
    NORMAL: 'normal',          // 正常
    LOW_STOCK: 'low_stock',    // 库存不足
    OUT_OF_STOCK: 'out_of_stock', // 缺货
    EXPIRED: 'expired',        // 过期
    WARNING: 'warning',        // 预警
    INACTIVE: 'inactive'       // 停用
  },
  
  // 状态显示名称
  STATUS_NAMES: {
    normal: '正常',
    low_stock: '库存不足',
    out_of_stock: '缺货',
    expired: '过期',
    warning: '预警',
    inactive: '停用'
  },
  
  // 计量单位
  UNITS: {
    KG: '千克',
    G: '克',
    L: '升',
    ML: '毫升',
    BAG: '袋',
    BOX: '箱',
    BOTTLE: '瓶',
    PIECE: '个',
    SET: '套'
  },
  
  // 库存限制
  LIMITS: {
    MIN_STOCK: 0,              // 最小库存
    MAX_STOCK: 999999999,      // 最大库存
    MAX_NAME_LENGTH: 100,      // 名称最大长度
    MAX_DESCRIPTION_LENGTH: 1000, // 描述最大长度
    LOW_STOCK_THRESHOLD: 10    // 低库存预警阈值
  }
};

// ===== 财务管理 =====
const FINANCE = {
  // 交易类型
  TRANSACTION_TYPES: {
    INCOME: 'income',      // 收入
    EXPENSE: 'expense',    // 支出
    INVESTMENT: 'investment', // 投资
    LOAN: 'loan'           // 贷款
  },
  
  // 交易类型显示名称
  TRANSACTION_TYPE_NAMES: {
    income: '收入',
    expense: '支出',
    investment: '投资',
    loan: '贷款'
  },
  
  // 支付方式
  PAYMENT_METHODS: {
    CASH: 'cash',          // 现金
    BANK_TRANSFER: 'bank_transfer', // 银行转账
    ALIPAY: 'alipay',      // 支付宝
    WECHAT: 'wechat',      // 微信支付
    CREDIT_CARD: 'credit_card', // 信用卡
    OTHER: 'other'         // 其他
  },
  
  // 支付方式显示名称
  PAYMENT_METHOD_NAMES: {
    cash: '现金',
    bank_transfer: '银行转账',
    alipay: '支付宝',
    wechat: '微信支付',
    credit_card: '信用卡',
    other: '其他'
  },
  
  // 费用类别
  EXPENSE_CATEGORIES: {
    FEED: 'feed',          // 饲料费
    MEDICINE: 'medicine',  // 药品费
    LABOR: 'labor',        // 人工费
    UTILITIES: 'utilities', // 水电费
    EQUIPMENT: 'equipment', // 设备费
    MAINTENANCE: 'maintenance', // 维护费
    OTHER: 'other'         // 其他
  },
  
  // 费用类别显示名称
  EXPENSE_CATEGORY_NAMES: {
    feed: '饲料费',
    medicine: '药品费',
    labor: '人工费',
    utilities: '水电费',
    equipment: '设备费',
    maintenance: '维护费',
    other: '其他'
  }
};

// ===== 订单管理 =====
const ORDER = {
  // 订单状态
  STATUS: {
    PENDING: 'pending',        // 待付款
    PAID: 'paid',              // 已付款
    SHIPPED: 'shipped',        // 已发货
    DELIVERED: 'delivered',    // 已送达
    COMPLETED: 'completed',    // 已完成
    CANCELLED: 'cancelled',    // 已取消
    REFUNDED: 'refunded'       // 已退款
  },
  
  // 订单状态显示名称
  STATUS_NAMES: {
    pending: '待付款',
    paid: '已付款',
    shipped: '已发货',
    delivered: '已送达',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  },
  
  // 订单类型
  TYPES: {
    PURCHASE: 'purchase',    // 采购订单
    SALE: 'sale',           // 销售订单
    SERVICE: 'service'      // 服务订单
  },
  
  // 订单类型显示名称
  TYPE_NAMES: {
    purchase: '采购订单',
    sale: '销售订单',
    service: '服务订单'
  }
};

// ===== AI相关 =====
const AI = {
  // AI服务类型
  SERVICE_TYPES: {
    CHAT: 'chat',                    // AI对话
    IMAGE_RECOGNITION: 'image_recognition', // 图像识别
    HEALTH_DIAGNOSIS: 'health_diagnosis',   // 健康诊断
    INVENTORY_COUNT: 'inventory_count',     // 库存盘点
    FINANCE_ANALYSIS: 'finance_analysis'    // 财务分析
  },
  
  // AI服务显示名称
  SERVICE_TYPE_NAMES: {
    chat: 'AI对话',
    image_recognition: '图像识别',
    health_diagnosis: '健康诊断',
    inventory_count: '库存盘点',
    finance_analysis: '财务分析'
  },
  
  // 置信度等级
  CONFIDENCE_LEVELS: {
    VERY_HIGH: 'very_high',  // 很高 (>90%)
    HIGH: 'high',            // 高 (70-90%)
    MEDIUM: 'medium',        // 中等 (50-70%)
    LOW: 'low',              // 低 (30-50%)
    VERY_LOW: 'very_low'     // 很低 (<30%)
  },
  
  // 置信度等级显示名称
  CONFIDENCE_LEVEL_NAMES: {
    very_high: '很高',
    high: '高',
    medium: '中等',
    low: '低',
    very_low: '很低'
  }
};

// ===== 通知相关 =====
const NOTIFICATION = {
  // 通知类型
  TYPES: {
    INFO: 'info',          // 信息
    SUCCESS: 'success',    // 成功
    WARNING: 'warning',    // 警告
    ERROR: 'error',        // 错误
    SYSTEM: 'system'       // 系统
  },
  
  // 通知优先级
  PRIORITIES: {
    LOW: 'low',           // 低
    NORMAL: 'normal',     // 普通
    HIGH: 'high',         // 高
    URGENT: 'urgent'      // 紧急
  },
  
  // 通知优先级显示名称
  PRIORITY_NAMES: {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
};

// ===== 系统设置 =====
const SYSTEM = {
  // 语言选项
  LANGUAGES: {
    ZH_CN: 'zh-CN',       // 简体中文
    ZH_TW: 'zh-TW',       // 繁体中文
    EN_US: 'en-US'        // 英语
  },
  
  // 语言显示名称
  LANGUAGE_NAMES: {
    'zh-CN': '简体中文',
    'zh-TW': '繁體中文',
    'en-US': 'English'
  },
  
  // 主题模式
  THEMES: {
    LIGHT: 'light',       // 浅色主题
    DARK: 'dark',         // 深色主题
    AUTO: 'auto'          // 自动
  },
  
  // 主题显示名称
  THEME_NAMES: {
    light: '浅色主题',
    dark: '深色主题',
    auto: '自动'
  }
};

// ===== 数据验证规则 =====
const VALIDATION = {
  // 手机号正则
  PHONE_REGEX: /^1[3-9]\d{9}$/,
  
  // 邮箱正则
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  // 密码正则 (至少包含字母和数字，6-20位)
  PASSWORD_REGEX: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,20}$/,
  
  // 中文姓名正则
  CHINESE_NAME_REGEX: /^[\u4e00-\u9fa5]{2,8}$/,
  
  // 身份证号正则
  ID_CARD_REGEX: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  
  // 统一社会信用代码正则
  CREDIT_CODE_REGEX: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
};

// 导出所有业务常量
module.exports = {
  USER,
  FLOCK,
  HEALTH,
  PRODUCTION,
  INVENTORY,
  FINANCE,
  ORDER,
  AI,
  NOTIFICATION,
  SYSTEM,
  VALIDATION
};