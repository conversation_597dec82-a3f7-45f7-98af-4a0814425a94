<!--pages/oa/approval/statistics/statistics.wxml-->
<view class="page-container">
  
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">审批统计概览</text>
      <text class="header-subtitle">数据驱动，高效管理</text>
    </view>
    
    <!-- 时间筛选 -->
    <view class="time-filter">
      <picker mode="selector" range="{{timeRangeOptions}}" range-key="label" value="{{filters.timeRangeIndex || 0}}" bindchange="onTimeRangeChange">
        <view class="filter-btn">
          <text class="filter-text">{{timeRangeOptions[filters.timeRangeIndex || 0].label}}</text>
          <text class="filter-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="overview-section">
    <view class="section-title">统计概览</view>
    
    <view class="overview-grid">
      <view class="overview-card primary">
        <view class="card-icon">📊</view>
        <view class="card-content">
          <text class="card-number">{{overview.totalApprovals}}</text>
          <text class="card-label">总审批数</text>
        </view>
      </view>
      
      <view class="overview-card warning">
        <view class="card-icon">⏳</view>
        <view class="card-content">
          <text class="card-number">{{overview.pending}}</text>
          <text class="card-label">待审批</text>
        </view>
      </view>
      
      <view class="overview-card success">
        <view class="card-icon">✅</view>
        <view class="card-content">
          <text class="card-number">{{overview.approved}}</text>
          <text class="card-label">已通过</text>
        </view>
      </view>
      
      <view class="overview-card danger">
        <view class="card-icon">❌</view>
        <view class="card-content">
          <text class="card-number">{{overview.rejected}}</text>
          <text class="card-label">已拒绝</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 按类型统计 -->
  <view class="stats-section">
    <view class="section-header">
      <text class="section-title">按类型统计</text>
      <view class="view-detail-btn" bindtap="onViewDetail" data-type="type">
        <text class="detail-text">详情</text>
        <text class="detail-arrow">〉</text>
      </view>
    </view>
    
    <view class="type-stats-container">
      <view wx:for="{{typeStats}}" wx:key="type" class="type-stat-item">
        <view class="type-info">
          <view class="type-dot" style="background-color: {{item.color}};"></view>
          <view class="type-details">
            <text class="type-label">{{item.label}}</text>
            <text class="type-count">{{item.count}}件</text>
          </view>
        </view>
        
        <view class="type-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.percentage}}%; background-color: {{item.color}};"></view>
          </view>
          <text class="progress-text">{{item.percentage}}%</text>
        </view>
      </view>
    </view>
  </view>



  <!-- 效率统计 -->
  <view class="stats-section">
    <view class="section-header">
      <text class="section-title">审批效率</text>
      <view class="view-detail-btn" bindtap="onViewDetail" data-type="efficiency">
        <text class="detail-text">详情</text>
        <text class="detail-arrow">〉</text>
      </view>
    </view>
    
    <view class="efficiency-grid">
      <view class="efficiency-item">
        <text class="efficiency-label">平均时长</text>
        <text class="efficiency-value primary">{{efficiencyStats.avgApprovalTime}}</text>
      </view>
      
      <view class="efficiency-item">
        <text class="efficiency-label">通过率</text>
        <text class="efficiency-value success">{{efficiencyStats.approvalRate}}</text>
      </view>
      
      <view class="efficiency-item">
        <text class="efficiency-label">最快审批</text>
        <text class="efficiency-value info">{{efficiencyStats.quickestApproval}}</text>
      </view>
      
      <view class="efficiency-item">
        <text class="efficiency-label">按时完成率</text>
        <text class="efficiency-value warning">{{efficiencyStats.onTimeRate || '92.8%'}}</text>
      </view>
    </view>
  </view>

  <!-- 我的审批统计 (如果有审批权限) -->
  <view wx:if="{{currentUser.role === 'admin' || currentUser.role === 'manager'}}" class="stats-section">
    <view class="section-title">我的审批统计</view>
    
    <view class="my-stats-container">
      <view class="my-stats-card">
        <view class="stats-row">
          <view class="stat-item">
            <text class="stat-number">{{myStats.processed}}</text>
            <text class="stat-label">已处理</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number success">{{myStats.approved}}</text>
            <text class="stat-label">已通过</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number danger">{{myStats.rejected}}</text>
            <text class="stat-label">已拒绝</text>
          </view>
        </view>
        
        <view class="stats-footer">
          <view class="footer-item">
            <text class="footer-label">平均处理时间</text>
            <text class="footer-value">{{myStats.avgTime}}</text>
          </view>
          <view class="footer-item">
            <text class="footer-label">处理效率</text>
            <text class="footer-value">{{myStats.efficiency}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>



  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 刷新状态 -->
  <view wx:if="{{refreshing}}" class="refresh-indicator">
    <view class="refresh-content">
      <view class="loading-spinner small"></view>
      <text class="refresh-text">刷新中...</text>
    </view>
  </view>

</view>