const express = require("express");
const router = express.Router();
const apiService = require("../utils/apiService");

/**
 * API路由 - 用于AJAX请求
 */

/**
 * 创建用户
 */
router.post("/users", async (req, res) => {
  try {
    if (!req.session.token) {
      return res.status(401).json({
        success: false,
        message: "请先登录",
      });
    }

    const result = await apiService.createUser(req.session.token, req.body);
    res.json(result);
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建用户API错误:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "创建用户失败",
    });
  }
});

/**
 * 创建生产记录
 */
router.post("/production", async (req, res) => {
  try {
    if (!req.session.token) {
      return res.status(401).json({
        success: false,
        message: "请先登录",
      });
    }

    const result = await apiService.createProductionRecord(
      req.session.token,
      req.body,
    );
    res.json(result);
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建生产记录API错误:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "创建生产记录失败",
    });
  }
});

/**
 * 创建健康记录
 */
router.post("/health", async (req, res) => {
  try {
    if (!req.session.token) {
      return res.status(401).json({
        success: false,
        message: "请先登录",
      });
    }

    const result = await apiService.createHealthRecord(
      req.session.token,
      req.body,
    );
    res.json(result);
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建健康记录API错误:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "创建健康记录失败",
    });
  }
});

/**
 * 删除生产记录
 */
router.delete("/production/:id", async (req, res) => {
  try {
    if (!req.session.token) {
      return res.status(401).json({
        success: false,
        message: "请先登录",
      });
    }

    const result = await apiService.deleteProductionRecord(
      req.session.token,
      req.params.id,
    );
    res.json(result);
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("删除生产记录API错误:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "删除生产记录失败",
    });
  }
});

module.exports = router;
