/**
 * HTTP请求封装
 * 支持自动loading、错误处理、重试机制、类型安全的API客户端
 * 
 * 📋 更新日志:
 * - v2.6.0: 集成类型安全API客户端，支持新的响应格式
 * - v2.6.0: 添加向下兼容支持，无缝迁移现有代码
 * - v2.6.0: 集成常量管理系统，统一配置管理
 */

// 引入常量系统
const { API, CONFIG } = require('../constants/index.js');
// 引入统一日志
let logger;
try {
  logger = require('./logger.js');
  } catch (e) {
  logger = console;
  }

// 引入租户配置工具
const tenantConfig = require('./tenant-config.js');

// 引入统一API客户端
let apiClientV2;
try {
  const { apiClient } = require('./api-client-unified.js');
  apiClientV2 = { apiClient };
} catch (e) {
  try { const fallbackLogger = require('./logger.js'); fallbackLogger.warn && fallbackLogger.warn('统一API客户端未加载，使用传统模式'); } catch (_) { logger.warn && logger.warn('统一API客户端未加载，使用传统模式'); }
  apiClientV2 = null;
}

/**
 * 获取基础URL
 */
function getBaseUrl() {
  try {
    // 首先尝试从租户配置获取
    const config = tenantConfig.getTenantConfig();
    if (config && config.apiBaseUrl) {
      return config.apiBaseUrl;
    }
    
    // 其次从app实例获取
    const app = getApp();
    if (app && app.globalData && app.globalData.baseUrl) {
      return app.globalData.baseUrl;
    }
    
    // 最后使用常量系统中的默认值
    return API.BASE_URL + API.API_VERSIONS.TENANT;
  } catch (e) {
    logger.warn('获取baseUrl失败，使用默认值');
    return API.BASE_URL + API.API_VERSIONS.TENANT;
  }
}

/**
 * 显示Loading
 * @param {string} title 加载提示文字
 * @param {boolean} mask 是否显示透明蒙层
 */
function showLoading(title = '加载中...', mask = true) {
  wx.showLoading({
    title,
    mask
  });
}

/**
 * 隐藏Loading
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 显示错误提示
 * @param {string} message 错误消息
 * @param {number} duration 显示时长
 */
function showError(message, duration = 2000) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration
  });
}

/**
 * 显示成功提示
 * @param {string} message 成功消息
 * @param {number} duration 显示时长
 */
function showSuccess(message, duration = 1500) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration
  });
}

/**
 * 处理网络错误
 * @param {Object} error 错误对象
 * @param {boolean} showToast 是否显示错误提示
 * @returns {Object} 格式化的错误对象
 */
function handleNetworkError(error, showToast = true) {
  let errorType = API.ERROR_TYPES.NETWORK_ERROR;
  let errorMessage = API.ERROR_MESSAGES[API.ERROR_TYPES.NETWORK_ERROR];

  // 根据错误信息判断错误类型
  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      errorType = API.ERROR_TYPES.TIMEOUT_ERROR;
      errorMessage = API.ERROR_MESSAGES[API.ERROR_TYPES.TIMEOUT_ERROR];
    } else if (error.errMsg.includes('fail')) {
      errorType = API.ERROR_TYPES.NETWORK_ERROR;
      errorMessage = API.ERROR_MESSAGES[API.ERROR_TYPES.NETWORK_ERROR];
    }
  }

  const formattedError = {
    type: errorType,
    message: errorMessage,
    originalError: error,
    timestamp: new Date().toISOString()
  };

  // 显示错误提示
  if (showToast) {
    showError(errorMessage);
  }

  // 记录错误日志
  logger.error('网络请求错误:', formattedError);

  return formattedError;
}

/**
 * 重试请求
 * @param {Function} requestFn 请求函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} delay 重试延迟时间(ms)
 * @returns {Promise}
 */
function retryRequest(requestFn, maxRetries = 2, delay = 1000) {
  return new Promise((resolve, reject) => {
    let retryCount = 0;

    function attemptRequest() {
      requestFn()
        .then(resolve)
        .catch(error => {
          retryCount++;
          if (retryCount <= maxRetries &&
              (error.type === API.ERROR_TYPES.NETWORK_ERROR || error.type === API.ERROR_TYPES.TIMEOUT_ERROR)) {
            logger.debug(`请求失败，${delay}ms后进行第${retryCount}次重试...`);
            setTimeout(attemptRequest, delay);
          } else {
            reject(error);
          }
        });
    }

    attemptRequest();
  });
}

/**
 * 封装wx.request请求
 * @param {Object} options 请求参数
 * @param {boolean} options.showLoading 是否显示loading
 * @param {string} options.loadingText loading文字
 * @param {boolean} options.showError 是否显示错误提示
 * @param {boolean} options.enableRetry 是否启用重试
 * @param {number} options.maxRetries 最大重试次数
 */
function request(options = {}) {
  // 默认参数
  const defaultOptions = {
    url: '',
    method: API.HTTP_METHODS.GET,
    data: {},
    header: {
      'Content-Type': 'application/json'
    },
    timeout: API.REQUEST_CONFIG.TIMEOUT,
    showLoading: false,        // 是否显示loading
    loadingText: '加载中...',  // loading文字
    showError: true,           // 是否显示错误提示
    enableRetry: false,        // 是否启用重试
    maxRetries: API.REQUEST_CONFIG.RETRY_COUNT              // 最大重试次数
  };

  // 合并参数
  options = Object.assign({}, defaultOptions, options);

  // 添加认证头
  const token = wx.getStorageSync('access_token');
  if (token && !options.header.Authorization) {
    options.header.Authorization = 'Bearer ' + token;
  }
  
  // 添加租户标识头
  try {
    const config = tenantConfig.getTenantConfig();
    if (config && config.tenantCode && !options.header['X-Tenant-Code']) {
      options.header['X-Tenant-Code'] = config.tenantCode;
    }
    // 兜底：若未从租户配置获取，尝试从 storage 读取
    if (!options.header['X-Tenant-Code']) {
      const code = wx.getStorageSync('tenant_code');
      if (code) options.header['X-Tenant-Code'] = code;
    }
    
    // 添加客户端信息
    options.header['X-Client-Version'] = '1.0.0';
    options.header['X-Platform'] = 'wechat-miniprogram';
  } catch (e) {
    logger.warn('添加租户头信息失败:', e);
  }

  // 完整URL
  if (options.url.startsWith('/')) {
    options.url = getBaseUrl() + options.url;
  }

  // 显示loading
  if (options.showLoading) {
    showLoading(options.loadingText);
  }

  // 创建请求函数
  const makeRequest = () => {
    return new Promise((resolve, reject) => {
      wx.request({
        url: options.url,
        method: options.method,
        data: options.data,
        header: options.header,
        timeout: options.timeout,
        success: function(res) {
          // 隐藏loading
          if (options.showLoading) {
            hideLoading();
          }

          // 处理HTTP状态码
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 成功响应 - 支持新的API响应格式
            const responseData = res.data;
            
            // 检查新版API格式 (express-zod-api)
            if (responseData && typeof responseData === 'object' && 'status' in responseData) {
              if (responseData.status === 'success') {
                // 新版成功响应格式
                resolve(responseData.data || responseData);
              } else if (responseData.status === 'error') {
                // 新版错误响应格式
                const error = {
                  type: API.ERROR_TYPES.BUSINESS_ERROR,
                  message: responseData.message || API.ERROR_MESSAGES[API.ERROR_TYPES.BUSINESS_ERROR],
                  data: responseData,
                  statusCode: res.statusCode,
                  code: responseData.code,
                  issues: responseData.issues || []
                };
                if (options.showError) {
                  showError(error.message);
                }
                reject(error);
              } else {
                // 未知状态，当作成功处理
                resolve(responseData);
              }
            }
            // 检查旧版API格式 (向下兼容)
            else if (responseData && responseData.success === false) {
              // 旧版业务错误格式
              const error = {
                type: API.ERROR_TYPES.BUSINESS_ERROR,
                message: responseData.message || API.ERROR_MESSAGES[API.ERROR_TYPES.BUSINESS_ERROR],
                data: responseData,
                statusCode: res.statusCode
              };
              if (options.showError) {
                showError(error.message);
              }
              reject(error);
            } else {
              // 成功响应或未知格式，直接返回数据
              resolve(responseData);
            }
          } else if (res.statusCode === API.HTTP_STATUS.UNAUTHORIZED) {
            // 认证错误
            const error = {
              type: API.ERROR_TYPES.AUTH_ERROR,
              message: API.ERROR_MESSAGES[API.ERROR_TYPES.AUTH_ERROR],
              statusCode: res.statusCode
            };

            // 清除token和租户配置，跳转登录页
            wx.removeStorageSync('access_token');
            wx.removeStorageSync('refresh_token');
            tenantConfig.clearTenantConfig();
            
            wx.redirectTo({
              url: '/pages/login/login'
            });

            if (options.showError) {
              showError(error.message);
            }
            reject(error);
          } else {
            // 服务器错误
            const error = {
              type: API.ERROR_TYPES.SERVER_ERROR,
              message: res.data?.message || API.ERROR_MESSAGES[API.ERROR_TYPES.SERVER_ERROR],
              statusCode: res.statusCode,
              data: res.data
            };
            if (options.showError) {
              showError(error.message);
            }
            reject(error);
          }
        },
        fail: function(err) {
          // 隐藏loading
          if (options.showLoading) {
            hideLoading();
          }

          // 处理网络错误
          const error = handleNetworkError(err, options.showError);
          reject(error);
        }
      });
    });
  };

  // 是否启用重试
  if (options.enableRetry) {
    return retryRequest(makeRequest, options.maxRetries);
  } else {
    return makeRequest();
  }
}

/**
 * GET请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 */
function get(url, data = {}) {
  return request({
    url: url,
    method: API.HTTP_METHODS.GET,
    data: data
  });
}

/**
 * POST请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 */
function post(url, data = {}) {
  return request({
    url: url,
    method: API.HTTP_METHODS.POST,
    data: data
  });
}

/**
 * PUT请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 */
function put(url, data = {}) {
  return request({
    url: url,
    method: API.HTTP_METHODS.PUT,
    data: data
  });
}

/**
 * DELETE请求
 * @param {string} url 请求地址
 */
function del(url) {
  return request({
    url: url,
    method: API.HTTP_METHODS.DELETE
  });
}

/**
 * 智能请求函数 - 自动选择最佳的请求方式
 * @param {string|Object} urlOrOptions URL字符串或完整选项对象
 * @param {Object} options 请求选项（当第一个参数是URL时使用）
 */
function smartRequest(urlOrOptions, options = {}) {
  // 如果新版API客户端可用，且请求是API端点，优先使用新版
  if (apiClientV2 && typeof urlOrOptions === 'string') {
    // 检查是否为预定义的API端点
    const endpointKey = Object.keys(API.ENDPOINTS).find(module => 
      Object.values(API.ENDPOINTS[module]).includes(urlOrOptions)
    );
    
    if (endpointKey) {
      try {
        // 使用新版API客户端
        logger.debug('使用新版API客户端处理请求:', urlOrOptions);
        return apiClientV2.requestV2.request(urlOrOptions, options);
      } catch (error) {
        logger.warn('新版API客户端调用失败，回退到传统模式:', error);
        // 回退到传统请求
      }
    }
  }
  
  // 使用传统请求方式
  if (typeof urlOrOptions === 'string') {
    return request({ url: urlOrOptions, ...options });
  } else {
    return request(urlOrOptions);
  }
}

/**
 * 获取API客户端实例（如果可用）
 */
function getApiClient() {
  return apiClientV2 ? apiClientV2.apiClient : null;
}

/**
 * 检查是否启用了新版API客户端
 */
function isNewApiClientEnabled() {
  return !!apiClientV2;
}

/**
 * 统一错误处理函数 - 兼容新旧格式
 */
function handleUnifiedError(error, showToast = true) {
  if (apiClientV2 && apiClientV2.handleApiError) {
    return apiClientV2.handleApiError(error, showToast);
  } else {
    // 使用传统错误处理
    if (showToast) {
      showError(error.message || '操作失败');
    }
    return error.message || '操作失败';
  }
}

/**
 * 创建带有统一错误处理的请求函数
 */
function createSafeRequest(options = {}) {
  const { showToast = true, throwError = true } = options;
  
  return async function(urlOrOptions, requestOptions = {}) {
    try {
      return await smartRequest(urlOrOptions, requestOptions);
    } catch (error) {
      const message = handleUnifiedError(error, showToast);
      
      if (throwError) {
        throw error;
      }
      
      return { error: true, message };
    }
  };
}

module.exports = {
  // 基础请求方法
  request,
  get,
  post,
  put,
  del,

  // 智能请求方法
  smartRequest,
  createSafeRequest,

  // 工具函数
  showLoading,
  hideLoading,
  showError,
  showSuccess,
  handleNetworkError,
  handleUnifiedError,

  // API客户端相关
  getApiClient,
  isNewApiClientEnabled,

  // 常量（为了向下兼容）
  ERROR_TYPES: API.ERROR_TYPES,
  ERROR_MESSAGES: API.ERROR_MESSAGES,

  // 新版API客户端访问（如果可用）
  get apiClientV2() {
    return apiClientV2;
  }
};