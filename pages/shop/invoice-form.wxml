<!-- pages/shop/invoice-form.wxml -->
<view class="invoice-form-container">
  <!-- 个人发票表单 -->
  <view wx:if="{{type === 'personal'}}" class="form-section">
    <view class="section-header">
      <text class="section-title">个人发票信息</text>
    </view>
    
    <view class="form-group">
      <view class="form-item">
        <text class="label required">邮箱地址</text>
        <input 
          class="input" 
          placeholder="请输入邮箱地址，用于接收电子发票"
          value="{{formData.personal.email}}"
          data-field="email"
          bindinput="onInputChange"
          type="email"
        />
      </view>
    </view>
    
    <view class="tips">
      <text class="tips-text">• 电子发票将发送至您填写的邮箱</text>
      <text class="tips-text">• 电子发票与纸质发票具有同等法律效力</text>
    </view>
  </view>

  <!-- 企业发票表单 -->
  <view wx:if="{{type === 'company'}}" class="form-section">
    <view class="section-header">
      <text class="section-title">企业发票信息</text>
    </view>
    
    <view class="form-group">
      <view class="form-item">
        <text class="label required">公司名称</text>
        <input 
          class="input" 
          placeholder="请输入完整的公司名称"
          value="{{formData.company.title}}"
          data-field="title"
          bindinput="onInputChange"
        />
      </view>
      
      <view class="form-item">
        <text class="label required">税号</text>
        <input 
          class="input" 
          placeholder="请输入纳税人识别号"
          value="{{formData.company.taxNumber}}"
          data-field="taxNumber"
          bindinput="onInputChange"
        />
      </view>
      
      <view class="form-item">
        <text class="label">公司地址</text>
        <input 
          class="input" 
          placeholder="请输入公司注册地址（可选）"
          value="{{formData.company.address}}"
          data-field="address"
          bindinput="onInputChange"
        />
      </view>
      
      <view class="form-item">
        <text class="label">公司电话</text>
        <input 
          class="input" 
          placeholder="请输入公司电话（可选）"
          value="{{formData.company.phone}}"
          data-field="phone"
          bindinput="onInputChange"
          type="number"
        />
      </view>
      
      <view class="form-item">
        <text class="label">开户银行</text>
        <input 
          class="input" 
          placeholder="请输入开户银行（可选）"
          value="{{formData.company.bank}}"
          data-field="bank"
          bindinput="onInputChange"
        />
      </view>
      
      <view class="form-item">
        <text class="label">银行账户</text>
        <input 
          class="input" 
          placeholder="请输入银行账户（可选）"
          value="{{formData.company.account}}"
          data-field="account"
          bindinput="onInputChange"
          type="number"
        />
      </view>
      
      <view class="form-item">
        <text class="label required">邮箱地址</text>
        <input 
          class="input" 
          placeholder="请输入邮箱地址，用于接收电子发票"
          value="{{formData.company.email}}"
          data-field="email"
          bindinput="onInputChange"
          type="email"
        />
      </view>
    </view>
    
    <view class="tips">
      <text class="tips-text">• 请确保发票信息准确无误，开具后不可修改</text>
      <text class="tips-text">• 电子发票将发送至您填写的邮箱</text>
      <text class="tips-text">• 带*号的为必填项</text>
    </view>
  </view>
</view>

<!-- 底部保存按钮 -->
<view class="bottom-section">
  <button class="save-btn" bindtap="onSave">保存发票信息</button>
</view>