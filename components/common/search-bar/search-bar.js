/**
 * 通用搜索栏组件
 * 支持搜索、清除、历史记录等功能
 */
const { UI, IMAGES } = require('../../../constants/index.js');

Component({
  options: {
    virtualHost: true
  },

  properties: {
    // 搜索值
    value: {
      type: String,
      value: ''
    },
    
    // 占位符文本
    placeholder: {
      type: String,
      value: '请输入搜索关键词'
    },
    
    // 是否显示搜索图标
    showSearchIcon: {
      type: Boolean,
      value: true
    },
    
    // 是否显示清除按钮
    showClearButton: {
      type: Boolean,
      value: true
    },
    
    // 是否显示取消按钮
    showCancelButton: {
      type: Boolean,
      value: false
    },
    
    // 取消按钮文本
    cancelText: {
      type: String,
      value: '取消'
    },
    
    // 搜索栏形状
    shape: {
      type: String,
      value: 'round' // square, round
    },
    
    // 搜索栏尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    
    // 是否只读
    readonly: {
      type: Boolean,
      value: false
    },
    
    // 是否自动聚焦
    autofocus: {
      type: Boolean,
      value: false
    },
    
    // 输入类型
    inputType: {
      type: String,
      value: 'text' // text, number, idcard, digit
    },
    
    // 最大长度
    maxlength: {
      type: Number,
      value: 140
    },
    
    // 键盘弹起时，是否自动上推页面
    adjustPosition: {
      type: Boolean,
      value: true
    },
    
    // 聚焦时光标位置
    cursorSpacing: {
      type: Number,
      value: 0
    },
    
    // 是否显示历史记录
    showHistory: {
      type: Boolean,
      value: false
    },
    
    // 历史记录最大数量
    maxHistory: {
      type: Number,
      value: 10
    },
    
    // 历史记录存储键
    historyKey: {
      type: String,
      value: 'search_history'
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    
    // 无障碍标签
    ariaLabel: {
      type: String,
      value: ''
    }
  },

  data: {
    // 内部搜索值
    inputValue: '',
    
    // 是否聚焦
    focused: false,
    
    // 历史记录
    historyList: [],
    
    // 是否显示历史记录面板
    showHistoryPanel: false,
    
    // UI常量
    colors: UI.COLORS,
    spacing: UI.SPACING,
    
    // 图标
    searchIcon: IMAGES.ICONS.SEARCH,
    clearIcon: IMAGES.ICONS.CLOSE
  },

  observers: {
    'value'(newVal) {
      this.setData({
        inputValue: newVal || ''
      });
    }
  },

  lifetimes: {
    attached() {
      // 初始化搜索值
      this.setData({
        inputValue: this.data.value || ''
      });
      
      // 加载搜索历史
      if (this.data.showHistory) {
        this.loadSearchHistory();
      }
    }
  },

  methods: {
    /**
     * 输入事件
     */
    onInput(e) {
      const value = e.detail.value;
      
      this.setData({
        inputValue: value
      });
      
      this.triggerEvent('input', {
        value: value
      });
      
      // 实时搜索
      this.triggerEvent('search', {
        value: value,
        type: 'input'
      });
    },
    
    /**
     * 确认搜索
     */
    onConfirm(e) {
      const value = e.detail.value.trim();
      
      if (value) {
        // 保存到历史记录
        if (this.data.showHistory) {
          this.saveToHistory(value);
        }
        
        // 隐藏历史记录面板
        this.setData({
          showHistoryPanel: false
        });
        
        this.triggerEvent('search', {
          value: value,
          type: 'confirm'
        });
      }
      
      this.triggerEvent('confirm', {
        value: value
      });
    },
    
    /**
     * 聚焦事件
     */
    onFocus(e) {
      this.setData({
        focused: true
      });
      
      // 显示历史记录
      if (this.data.showHistory && this.data.historyList.length > 0) {
        this.setData({
          showHistoryPanel: true
        });
      }
      
      this.triggerEvent('focus', e.detail);
    },
    
    /**
     * 失焦事件
     */
    onBlur(e) {
      this.setData({
        focused: false
      });
      
      // 延迟隐藏历史记录面板，以便点击历史项
      setTimeout(() => {
        this.setData({
          showHistoryPanel: false
        });
      }, 150);
      
      this.triggerEvent('blur', e.detail);
    },
    
    /**
     * 清除搜索
     */
    onClear() {
      this.setData({
        inputValue: '',
        showHistoryPanel: false
      });
      
      this.triggerEvent('input', {
        value: ''
      });
      
      this.triggerEvent('clear');
      
      // 清除搜索结果
      this.triggerEvent('search', {
        value: '',
        type: 'clear'
      });
    },
    
    /**
     * 取消搜索
     */
    onCancel() {
      this.setData({
        inputValue: '',
        focused: false,
        showHistoryPanel: false
      });
      
      this.triggerEvent('cancel');
    },
    
    /**
     * 点击历史记录项
     */
    onHistoryItemTap(e) {
      const { value } = e.currentTarget.dataset;
      
      this.setData({
        inputValue: value,
        showHistoryPanel: false
      });
      
      this.triggerEvent('input', {
        value: value
      });
      
      this.triggerEvent('search', {
        value: value,
        type: 'history'
      });
    },
    
    /**
     * 清除历史记录
     */
    onClearHistory() {
      wx.showModal({
        title: '确认清除',
        content: '确定要清除所有搜索历史吗？',
        confirmColor: this.data.colors.PRIMARY.DEFAULT,
        success: (res) => {
          if (res.confirm) {
            this.setData({
              historyList: [],
              showHistoryPanel: false
            });
            
            wx.removeStorageSync(this.data.historyKey);
            
            this.triggerEvent('clearhistory');
          }
        }
      });
    },
    
    /**
     * 删除单个历史记录
     */
    onDeleteHistoryItem(e) {
      const { index } = e.currentTarget.dataset;
      const historyList = [...this.data.historyList];
      
      historyList.splice(index, 1);
      
      this.setData({
        historyList: historyList
      });
      
      // 保存到本地存储
      wx.setStorageSync(this.data.historyKey, historyList);
      
      this.triggerEvent('deletehistory', {
        index: index
      });
    },
    
    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
      try {
        const history = wx.getStorageSync(this.data.historyKey) || [];
        this.setData({
          historyList: history
        });
      } catch (e) {
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('加载搜索历史失败', e); } catch(_) {}
      }
    },
    
    /**
     * 保存到搜索历史
     */
    saveToHistory(value) {
      if (!value.trim()) return;
      
      let historyList = [...this.data.historyList];
      
      // 移除重复项
      const existIndex = historyList.indexOf(value);
      if (existIndex > -1) {
        historyList.splice(existIndex, 1);
      }
      
      // 添加到开头
      historyList.unshift(value);
      
      // 限制数量
      if (historyList.length > this.data.maxHistory) {
        historyList = historyList.slice(0, this.data.maxHistory);
      }
      
      this.setData({
        historyList: historyList
      });
      
      // 保存到本地存储
      try {
        wx.setStorageSync(this.data.historyKey, historyList);
      } catch (e) {
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('保存搜索历史失败', e); } catch(_) {}
      }
    },

    /**
     * 阻止事件冒泡的通用方法
     */
    stopPropagation(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
    }
  }
});