// pages/production/reimbursement/detail/detail.js
const app = getApp();

Page({
  data: {
    reimbursementId: '',
    reimbursement: {},
    approvalRecords: [],
    userRole: 'user',
    currentUser: '',
    showActions: false,
    canApprove: false,
    canReject: false,
    canEdit: false,
    canCancel: false
  },

  onLoad: function (options) {
    const id = options.id;
    this.setData({
      reimbursementId: id,
      userRole: this.getUserRole(),
      currentUser: this.getCurrentUser()
    });
    
    this.loadReimbursementDetail();
  },

  // 获取用户角色
  getUserRole: function() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('user_info') || {};
    return userInfo.role || 'user';
  },

  // 获取当前用户
  getCurrentUser: function() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('user_info') || {};
    return userInfo.name || '当前用户';
  },

  // 加载报销详情
  loadReimbursementDetail: function() {
    wx.showLoading({ title: '加载中...' });

    // 模拟API调用
    setTimeout(() => {
      const mockData = this.getMockReimbursementDetail();
      
      this.setData({
        reimbursement: mockData.reimbursement,
        approvalRecords: mockData.approvalRecords
      });

      this.updateActionButtons();
      wx.hideLoading();
    }, 800);
  },

  // 获取模拟数据
  getMockReimbursementDetail: function() {
    const reimbursement = {
      id: this.data.reimbursementId,
      title: '差旅费报销',
      category: '差旅费',
      amount: 500,
      applicant: '张三',
      createTime: '2023-12-15 10:30',
      status: 'pending',
      statusText: '待审批',
      description: '出差北京参加培训产生的交通费和住宿费',
      images: [
        '/assets/images/receipt1.jpg',
        '/assets/images/receipt2.jpg'
      ]
    };

    const approvalRecords = [
      {
        id: 1,
        approver: '李经理',
        status: 'pending',
        statusText: '待审批',
        time: '2023-12-15 10:30',
        remark: ''
      }
    ];

    return { reimbursement, approvalRecords };
  },

  // 更新操作按钮状态
  updateActionButtons: function() {
    const { reimbursement, userRole, currentUser } = this.data;
    const isApprover = userRole === 'admin' || userRole === 'manager';
    const isApplicant = reimbursement.applicant === currentUser;
    const isPending = reimbursement.status === 'pending';

    this.setData({
      showActions: isApprover || isApplicant,
      canApprove: isApprover && isPending,
      canReject: isApprover && isPending,
      canEdit: isApplicant && isPending,
      canCancel: isApplicant && isPending
    });
  },

  // 预览图片
  onPreviewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.reimbursement.images
    });
  },

  // 审批通过
  onApprove: function() {
    this.showApprovalModal('approved', '通过');
  },

  // 审批拒绝
  onReject: function() {
    this.showApprovalModal('rejected', '拒绝');
  },

  // 显示审批弹窗
  showApprovalModal: function(status, action) {
    wx.showModal({
      title: '审批确认',
      content: `确定要${action}这个报销申请吗？`,
      success: (res) => {
        if (res.confirm) {
          this.handleApproval(status);
        }
      }
    });
  },

  // 处理审批
  handleApproval: function(status) {
    wx.showLoading({ title: '处理中...' });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: status === 'approved' ? '审批通过' : '已拒绝',
        icon: 'success'
      });

      // 更新状态并返回
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  // 编辑报销
  onEdit: function() {
    wx.navigateTo({
      url: `/pages/production/reimbursement/add/add?id=${this.data.reimbursementId}&mode=edit`
    });
  },

  // 撤销报销
  onCancel: function() {
    wx.showModal({
      title: '撤销确认',
      content: '确定要撤销这个报销申请吗？',
      success: (res) => {
        if (res.confirm) {
          this.handleCancel();
        }
      }
    });
  },

  // 处理撤销
  handleCancel: function() {
    wx.showLoading({ title: '处理中...' });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '已撤销',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  }
});
