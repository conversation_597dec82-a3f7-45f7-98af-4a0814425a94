/* pages/oa/approval/pending/pending.wxss */

/* 页面容器 */
.pending-container {
  min-height: 100vh;
  background: #f2f2f7;
  padding-bottom: 120rpx;
}

/* ==================== 页面头部 ==================== */
.page-header {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  padding: 32rpx 24rpx 40rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.2;
}

.header-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.summary-text {
  font-size: 24rpx;
  opacity: 0.8;
}

.urgent-tip {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.urgent-count {
  font-size: 20rpx;
  font-weight: 600;
}

.urgent-label {
  font-size: 20rpx;
}

/* ==================== 快速统计 ==================== */
.quick-stats {
  background: white;
  margin: -20rpx 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.1);
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.stat-item:active {
  background: #f8f9fa;
  transform: scale(0.95);
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.stat-number.primary {
  color: #007aff;
}

.stat-number.urgent {
  color: #ff3b30;
}

.stat-number.today {
  color: #ff9500;
}

.stat-label {
  font-size: 24rpx;
  color: #8e8e93;
  font-weight: 500;
}

/* ==================== 搜索区域 ==================== */
.search-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin: 0 24rpx 24rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.search-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #8e8e93;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #1d1d1f;
}

.clear-search {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #8e8e93;
  border-radius: 50%;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.filter-toggle {
  width: 72rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.filter-icon {
  font-size: 28rpx;
}

/* ==================== 筛选面板 ==================== */
.filter-panel {
  background: white;
  margin: 0 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section:last-of-type {
  margin-bottom: 24rpx;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 16rpx;
  display: block;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-option {
  padding: 12rpx 20rpx;
  background: #f2f2f7;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #1d1d1f;
  transition: all 0.2s ease;
}

.filter-option.active {
  background: #007aff;
  color: white;
}

.filter-option:active {
  transform: scale(0.95);
}

.filter-actions {
  display: flex;
  gap: 16rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.reset-btn {
  background: #f2f2f7;
  color: #1d1d1f;
}

.apply-btn {
  background: #007aff;
  color: white;
}

/* ==================== 审批列表 ==================== */
.approval-list {
  margin: 0 24rpx;
}

.approval-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.approval-item:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 24rpx 16rpx;
}

.title-section {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1d1d1f;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.business-tag,
.urgent-tag,
.overdue-tag {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.business-tag {
  background: var(--info-bg);
  color: var(--info);
}

.business-tag.reimbursement {
  background: var(--success-bg);
  color: var(--success);
}

.business-tag.leave {
  background: var(--warning-bg);
  color: var(--warning);
}

.business-tag.purchase {
  background: rgba(88, 86, 214, 0.1);
  color: #5856d6;
}

.urgent-tag {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.overdue-tag {
  background: rgba(255, 45, 85, 0.1);
  color: #ff2d55;
}

.time-info {
  font-size: 24rpx;
  color: #8e8e93;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.item-content {
  padding: 0 24rpx 16rpx;
}

.applicant-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  font-size: 24rpx;
  width: 32rpx;
}

.info-text {
  font-size: 26rpx;
  color: #1d1d1f;
  font-weight: 500;
}

.dept-text {
  font-size: 24rpx;
  color: #8e8e93;
  margin-left: auto;
}

.amount-text {
  font-size: 26rpx;
  color: #34c759;
  font-weight: 600;
}

.deadline-row {
  color: #ff9500;
}

.deadline-text {
  font-size: 24rpx;
  color: #ff9500;
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #f2f2f7;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.reject-btn {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.reject-btn:active {
  background: rgba(255, 59, 48, 0.2);
}

.approve-btn {
  background: #007aff;
  color: white;
}

.approve-btn:active {
  background: #0051d5;
}

/* ==================== 状态组件 ==================== */
.loading-state,
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
  border-width: 2rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #8e8e93;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #1d1d1f;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #8e8e93;
  margin-bottom: 40rpx;
}

.refresh-btn {
  background: #007aff;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #8e8e93;
  font-size: 24rpx;
}

/* ==================== 审批确认弹窗 ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.action-modal {
  width: 100%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f2f2f7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f2f2f7;
  border-radius: 50%;
  font-size: 32rpx;
  color: #8e8e93;
}

.modal-content {
  padding: 32rpx;
}

.approval-summary {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.summary-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d1d1f;
  display: block;
  margin-bottom: 8rpx;
}

.summary-applicant {
  font-size: 24rpx;
  color: #8e8e93;
}

.form-section {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 26rpx;
  color: #1d1d1f;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
}

.remarks-input {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #1d1d1f;
  border: 2rpx solid transparent;
  transition: border-color 0.2s ease;
}

.remarks-input:focus {
  border-color: #007aff;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f2f2f7;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn {
  background: #f2f2f7;
  color: #1d1d1f;
}

.confirm-btn {
  color: white;
}

.confirm-btn.approve {
  background: #34c759;
}

.confirm-btn.reject {
  background: #ff3b30;
}

.confirm-btn:disabled {
  opacity: 0.6;
}

/* ==================== 动画 ==================== */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .stats-row {
    flex-direction: column;
    gap: 16rpx;
  }

  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }

  .filter-options {
    flex-direction: column;
  }

  .filter-option {
    text-align: center;
  }
}

/* ==================== 小程序特有样式 ==================== */
button::after {
  border: none;
}

.action-btn:active,
.filter-option:active,
.stat-item:active {
  opacity: 0.8;
}