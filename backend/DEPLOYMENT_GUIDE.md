# 智慧养鹅系统后端API部署和测试指南

## 📋 概述

本指南将帮助您部署和测试智慧养鹅系统的后端API，确保前端钻取功能页面能够正常工作。

## 🚀 快速部署

### 1. 环境准备

确保您的系统已安装：

- Node.js (v14.0.0 或更高版本)
- MySQL (v8.0 或更高版本)
- npm 或 yarn

### 2. 数据库设置

```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE zhihuiyange_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 创建用户（可选）
CREATE USER 'zhihuiyange'@'localhost' IDENTIFIED BY 'zhihuiyange123';
GRANT ALL PRIVILEGES ON zhihuiyange_local.* TO 'zhihuiyange'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 执行数据库脚本

```bash
# 进入后端目录
cd backend

# 执行数据库表创建脚本
node scripts/setup-missing-tables.js

# 执行性能优化脚本
mysql -u zhihuiyange -p zhihuiyange_local < scripts/optimize-database-performance.sql
```

### 4. 安装依赖和启动服务

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 或启动生产服务器
npm start
```

## 🧪 API测试

### 1. 基础连接测试

```bash
# 测试服务器是否启动
curl http://localhost:3001/

# 预期响应
{
  "message": "智慧养鹅API服务已启动",
  "version": "1.0.0",
  "timestamp": "2024-01-20T10:00:00.000Z"
}
```

### 2. 任务管理API测试

```bash
# 获取任务列表
curl -X GET "http://localhost:3001/api/v1/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 创建任务
curl -X POST "http://localhost:3001/api/v1/tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "测试任务",
    "description": "这是一个测试任务",
    "priority": "high",
    "category": "测试分类"
  }'
```

### 3. 公告管理API测试

```bash
# 获取公告列表
curl -X GET "http://localhost:3001/api/v1/announcements" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 获取公告统计
curl -X GET "http://localhost:3001/api/v1/announcements/statistics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. 库存管理API测试

```bash
# 获取库存总览
curl -X GET "http://localhost:3001/api/v1/inventory/overview" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 获取健康状态分析
curl -X GET "http://localhost:3001/api/v1/inventory/health-analysis" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 5. 价格管理API测试

```bash
# 获取价格趋势
curl -X GET "http://localhost:3001/api/v1/price/trends?breed_name=白鹅&timeRange=30d" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 获取最新价格
curl -X GET "http://localhost:3001/api/v1/price/latest" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 6. 物料管理API测试

```bash
# 获取物料列表
curl -X GET "http://localhost:3001/api/v1/production/materials" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 获取物料库存趋势
curl -X GET "http://localhost:3001/api/v1/production/materials/1/stock-trends" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔧 性能监控

### 1. 运行性能监控

```bash
# 执行性能监控脚本
node scripts/performance-monitor.js

# 查看生成的性能报告
ls scripts/performance-report-*.md
```

### 2. 日志管理

```bash
# 查看日志文件
ls logs/

# 清理过期日志（试运行）
node scripts/cleanup-logs.js --dry-run

# 实际清理过期日志
node scripts/cleanup-logs.js --retention-days 30
```

## 📊 前端集成测试

### 1. 更新前端API配置

确保前端 `utils/api.js` 文件包含所有新增的API端点：

```javascript
const API = {
  // 任务管理
  TASKS: BASE_URL + "/tasks",
  TASK_STATISTICS: BASE_URL + "/tasks/statistics",

  // 公告管理
  ANNOUNCEMENTS: BASE_URL + "/announcements",
  ANNOUNCEMENT_STATISTICS: BASE_URL + "/announcements/statistics",

  // 库存管理
  INVENTORY_OVERVIEW: BASE_URL + "/inventory/overview",
  INVENTORY_HEALTH: BASE_URL + "/inventory/health-analysis",

  // 价格管理
  PRICE_TRENDS: BASE_URL + "/price/trends",
  PRICE_STATISTICS: BASE_URL + "/price/statistics",

  // 物料管理
  MATERIALS: BASE_URL + "/production/materials",
  MATERIAL_TRENDS: BASE_URL + "/production/materials/{id}/stock-trends",
};
```

### 2. 测试钻取功能

1. **首页数据钻取**：
   - 点击存栏总数 → 跳转到库存详情页面
   - 点击健康率 → 显示健康状态分析
   - 点击"查看全部任务" → 跳转到任务列表页面
   - 点击"查看全部公告" → 跳转到公告列表页面

2. **价格组件钻取**：
   - 点击价格项 → 跳转到价格详情页面
   - 查看价格趋势图表
   - 测试价格订阅功能

3. **物料管理钻取**：
   - 点击物料项 → 跳转到物料详情页面
   - 查看库存趋势图表
   - 测试预警设置功能

## 🐛 故障排除

### 1. 数据库连接问题

```bash
# 检查数据库连接
mysql -u zhihuiyange -p zhihuiyange_local

# 检查表是否创建成功
SHOW TABLES;

# 检查表结构
DESCRIBE tasks;
```

### 2. API响应错误

```bash
# 检查服务器日志
tail -f logs/error-$(date +%Y-%m-%d).log

# 检查请求日志
tail -f logs/info-$(date +%Y-%m-%d).log
```

### 3. 权限问题

确保JWT令牌正确配置：

```javascript
// 在前端请求中包含正确的Authorization头
headers: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}
```

## 📈 性能优化建议

### 1. 数据库优化

- 定期执行 `ANALYZE TABLE` 更新统计信息
- 监控慢查询日志
- 根据查询模式调整索引

### 2. 应用优化

- 启用请求缓存
- 使用连接池管理数据库连接
- 实施API限流

### 3. 监控建议

- 设置定时任务清理日志
- 监控API响应时间
- 跟踪错误率和成功率

## 🔒 安全注意事项

1. **环境变量**：确保生产环境中的敏感信息通过环境变量配置
2. **JWT安全**：使用强密钥并设置合适的过期时间
3. **输入验证**：所有API都已包含输入验证中间件
4. **错误处理**：生产环境中不会暴露敏感错误信息

## 📞 支持

如果遇到问题，请：

1. 检查日志文件获取详细错误信息
2. 运行性能监控脚本检查系统状态
3. 参考API文档确认请求格式
4. 检查数据库连接和表结构

---

**部署完成后，您的智慧养鹅系统将支持完整的数据钻取功能，为用户提供深入的数据分析和管理能力。**
