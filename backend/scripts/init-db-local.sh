#!/bin/bash

# 智慧养鹅本地数据库初始化脚本
echo "========================================="
echo "  智慧养鹅本地数据库初始化脚本"
echo "========================================="

# 检查MySQL是否安装
if ! command -v mysql &> /dev/null
then
    echo "错误: 未找到MySQL，请先安装MySQL"
    exit 1
fi

echo "MySQL版本: $(mysql --version)"

# 执行数据库初始化脚本
echo "正在创建数据库和表..."
mysql -u root < backend/scripts/create-db.sql

if [ $? -eq 0 ]; then
    echo "数据库初始化成功完成！"
    echo ""
    echo "数据库信息："
    echo "- 数据库名: zhihuiyange_local"
    echo "- 用户名: zhihuiyange"
    echo "- 密码: zhihuiyange123"
    echo ""
    echo "现在可以启动本地开发服务器了："
    echo "./start-local-dev.sh"
else
    echo "数据库初始化失败！"
    exit 1
fi