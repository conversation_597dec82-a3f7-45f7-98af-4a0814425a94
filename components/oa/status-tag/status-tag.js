// components/oa/status-tag/status-tag.js

/**
 * OA状态标签组件
 * 用于统一显示各种状态信息
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 状态文本
    text: {
      type: String,
      value: ''
    },
    // 状态类型 success/warning/error/info/default
    type: {
      type: String,
      value: 'default'
    },
    // 自定义颜色
    color: {
      type: String,
      value: ''
    },
    // 自定义背景色
    backgroundColor: {
      type: String,
      value: ''
    },
    // 标签大小 small/medium/large
    size: {
      type: String,
      value: 'medium'
    },
    // 是否显示圆点
    dot: {
      type: Boolean,
      value: false
    },
    // 是否可关闭
    closable: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否为空心样式
    plain: {
      type: Boolean,
      value: false
    },
    // 圆角程度 square/round/circle
    shape: {
      type: String,
      value: 'round'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 预定义的状态样式
    typeStyles: {
      success: {
        color: '#00A86B',
        backgroundColor: '#F0F9F5'
      },
      warning: {
        color: '#FF9500',
        backgroundColor: '#FFF8F0'
      },
      error: {
        color: '#FF3B30',
        backgroundColor: '#FFF0F0'
      },
      info: {
        color: '#007AFF',
        backgroundColor: '#F0F7FF'
      },
      default: {
        color: '#8E8E93',
        backgroundColor: '#F2F2F7'
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 获取标签样式
     */
    getTagStyle() {
      const { type, color, backgroundColor, plain } = this.properties;
      const typeStyle = this.data.typeStyles[type] || this.data.typeStyles.default;
      
      let style = '';
      
      if (color) {
        style += `color: ${color};`;
        if (plain) {
          style += `border-color: ${color};`;
        }
      } else {
        style += `color: ${typeStyle.color};`;
        if (plain) {
          style += `border-color: ${typeStyle.color};`;
        }
      }
      
      if (backgroundColor) {
        style += `background-color: ${backgroundColor};`;
      } else if (!plain) {
        style += `background-color: ${typeStyle.backgroundColor};`;
      }
      
      return style;
    },

    /**
     * 获取圆点样式
     */
    getDotStyle() {
      const { type, color } = this.properties;
      const typeStyle = this.data.typeStyles[type] || this.data.typeStyles.default;
      
      const dotColor = color || typeStyle.color;
      return `background-color: ${dotColor};`;
    },

    /**
     * 标签点击事件
     */
    onTagTap() {
      if (!this.properties.disabled) {
        this.triggerEvent('tap', {
          text: this.properties.text,
          type: this.properties.type
        });
      }
    },

    /**
     * 关闭按钮点击事件
     */
    onCloseTap(e) {
      e.stopPropagation();
      if (!this.properties.disabled) {
        this.triggerEvent('close', {
          text: this.properties.text,
          type: this.properties.type
        });
      }
    }
  }
});
