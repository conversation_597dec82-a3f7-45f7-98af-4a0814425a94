// pages/order-detail/order-detail.js
const app = getApp();
const logger = require('../../utils/logger.js');

Page({
  data: {
    orderId: '',
    orderInfo: null,
    loading: true,
    cancelLoading: false,
    // 发票相关数据
    invoiceData: {
      type: 'none', // none: 不开发票, personal: 个人发票, company: 企业发票
      personal: {
        email: ''
      },
      company: {
        title: '',
        taxNumber: '',
        address: '',
        phone: '',
        bank: '',
        account: '',
        email: ''
      }
    },
    // 发票选择模态框
    showInvoiceModal: false
  },

  onLoad(options) {
    const orderId = options.id;
    if (orderId) {
      this.setData({
        orderId: orderId
      });
      this.loadOrderDetail();
    } else {
      wx.showToast({
        title: '订单信息错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单详情
  loadOrderDetail() {
    this.setData({
      loading: true
    });

    // TODO: 连接真实API
    this.loadOrderFromAPI();
  },

  // 从API加载订单详情
  loadOrderFromAPI() {
    const request = require('../../utils/request');
    const { API } = require('../../constants/index.js');
    
    request.get(`${API.ENDPOINTS.SHOP.ORDERS}/${this.data.orderId}`)
    .then(result => {
      if (result.success) {
        this.setData({
          orderInfo: result.data,
          loading: false
        });
      } else {
        logger.error('获取订单详情失败:', result.message);
        this.loadMockOrderDetail(); // fallback到模拟数据
      }
    }).catch(error => {
      logger.error('API请求失败，使用模拟数据:', error);
      this.loadMockOrderDetail(); // fallback到模拟数据
    });
  },

  // 模拟订单详情数据（备用）
  loadMockOrderDetail() {
    setTimeout(() => {
      const orderInfo = {
        id: this.data.orderId,
        orderNumber: 'GO202401150001',
        createTime: '2024-01-15 10:30',
        status: 'pending',
        statusText: '待付款',
        payTime: '',
        shipTime: '',
        receiveTime: '',
        totalQuantity: 1,
        totalPrice: '99.99',
        discountAmount: 0,
        shippingFee: '0.00',
        actualAmount: '99.99',
        goods: [
          {
            id: '1',
            name: '优质鹅饲料',
            price: '99.99',
            quantity: 1,
            image: '/images/icons/goods1.png',
            specs: '颜色以质检报告为准，16G+1T，9成新',
            tags: ['1年质保', '7天无理由', '专业质检']
          }
        ],
        address: {
          name: '张三',
          phone: '13800138000',
          detail: '银丰路10号财津银座B座417'
        },
        logistics: {
          company: '顺丰速运',
          trackingNumber: 'SF1234567890',
          status: '已发货'
        }
      };

      this.setData({
        orderInfo: orderInfo,
        loading: false
      });
    }, 500);
  },

  // 取消订单
  onCancelOrder() {
    if (this.data.orderInfo.status !== 'pending') {
      wx.showToast({
        title: '当前状态不能取消',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认取消订单',
      content: '取消后订单无法恢复，确定要取消这个订单吗？',
      confirmText: '确认取消',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder();
        }
      }
    });
  },

  // 执行取消订单
  cancelOrder() {
    this.setData({
      cancelLoading: true
    });

    wx.showLoading({
      title: '正在取消订单...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      
      // 更新订单状态
      const orderInfo = {
        ...this.data.orderInfo,
        status: 'cancelled',
        statusText: '已取消'
      };

      this.setData({
        orderInfo: orderInfo,
        cancelLoading: false
      });

      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      });
    }, 1500);
  },

  // 立即付款
  onPayOrder() {
    if (this.data.orderInfo.status !== 'pending') {
      wx.showToast({
        title: '订单状态异常',
        icon: 'none'
      });
      return;
    }

    // 直接跳转到支付页面，而不是订单详情页面
    wx.navigateTo({
      url: `/pages/payment/payment?orderId=${this.data.orderId}`
    });
  },

  // 查看物流
  onTrackOrder() {
    if (!this.data.orderInfo.logistics) {
      wx.showToast({
        title: '暂无物流信息',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/logistics/logistics?orderId=${this.data.orderId}`
    });
  },

  // 确认收货
  onConfirmOrder() {
    if (this.data.orderInfo.status !== 'shipped') {
      wx.showToast({
        title: '当前状态不能确认收货',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？确认后订单将完成。',
      confirmText: '确认收货',
      success: (res) => {
        if (res.confirm) {
          this.confirmReceive();
        }
      }
    });
  },

  // 执行确认收货
  confirmReceive() {
    wx.showLoading({
      title: '正在确认收货...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      
      // 更新订单状态
      const orderInfo = {
        ...this.data.orderInfo,
        status: 'completed',
        statusText: '已完成',
        receiveTime: new Date().toLocaleString()
      };

      this.setData({
        orderInfo: orderInfo
      });

      wx.showToast({
        title: '确认收货成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 发票选择
  onInvoiceSelect: function() {
    this.setData({
      showInvoiceModal: true
    });
  },

  // 关闭发票模态框
  onCloseInvoiceModal: function() {
    this.setData({
      showInvoiceModal: false
    });
  },

  // 选择发票类型
  onInvoiceTypeSelect: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      'invoiceData.type': type,
      showInvoiceModal: false
    });

    // 如果选择了需要发票，跳转到发票信息填写页面
    if (type !== 'none') {
      wx.navigateTo({
        url: `/pages/shop/invoice-form?type=${type}`
      });
    }
  },

  // 分享订单
  onShareOrder() {
    return {
      title: `订单详情 - ${this.data.orderInfo.orderNumber}`,
      path: `/pages/order-detail/order-detail?id=${this.data.orderId}`,
      imageUrl: this.data.orderInfo.goods[0]?.image || '/images/default-share.png'
    };
  },

  // 再次购买
  onRebuyOrder() {
    const orderInfo = this.data.orderInfo;
    if (!orderInfo || !orderInfo.goods || orderInfo.goods.length === 0) {
      wx.showToast({
        title: '订单信息异常',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '再次购买',
      content: `确定要将订单中的${orderInfo.goods.length}件商品加入购物车吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已加入购物车',
            icon: 'success'
          });

          setTimeout(() => {
            wx.switchTab({
              url: '/pages/shop/cart'
            });
          }, 1500);
        }
      }
    });
  },

  // 评价订单
  onReviewOrder() {
    wx.navigateTo({
      url: `/pages/shop/review?orderId=${this.data.orderId}`
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 复制订单号
  onCopyOrderNumber() {
    wx.setClipboardData({
      data: this.data.orderInfo.orderNumber,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 删除订单
  onDeleteOrder() {
    if (this.data.orderInfo.status !== 'cancelled' && this.data.orderInfo.status !== 'completed') {
      wx.showToast({
        title: '当前状态不能删除',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '删除订单',
      content: '删除后订单记录将无法找回，确定要删除吗？',
      confirmText: '确认删除',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '正在删除...'
          });

          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '订单已删除',
              icon: 'success'
            });

            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }, 1000);
        }
      }
    });
  }
});
