@import '/styles/oa-common.wxss';
/* pages/oa/reimbursement/detail/detail.wxss */

.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #E5E5E7;
  border-top: 6rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 详情内容 */
.detail-content {
  padding: 24rpx 32rpx;
}

/* 基本信息卡片 */
.info-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.title-area {
  flex: 1;
  margin-right: 24rpx;
}

.main-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1D1D1F;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.reimbursement-number {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
}

.amount-section {
  text-align: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 12rpx;
  color: #FFFFFF;
}

.amount-label {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.amount-value {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
}

/* 信息区块 */
.info-section {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
}

/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
}

.row-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #8E8E93;
  flex-shrink: 0;
}

.row-value {
  flex: 1;
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.5;
}

/* 费用明细 */
.expense-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.expense-item {
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  padding: 24rpx;
  background-color: #FAFAFA;
  transition: background-color 0.2s ease;
}

.expense-item:active {
  background-color: #F0F0F0;
}

.expense-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.expense-description {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-right: 16rpx;
}

.expense-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #007AFF;
}

.expense-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.expense-detail {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.detail-label {
  color: #8E8E93;
  margin-right: 8rpx;
  min-width: 80rpx;
}

.detail-value {
  color: #1D1D1F;
  flex: 1;
}

/* 费用汇总 */
.expense-summary {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #E5E5E7;
}

.summary-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.summary-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #007AFF;
}

/* 审批流程区域 */
.approval-section {
  margin-bottom: 24rpx;
}

/* 操作按钮区域 */
.action-section {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.owner-actions,
.approver-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background-color: #007AFF;
  color: #FFFFFF;
}

.action-btn.secondary {
  background-color: #F2F2F7;
  color: #007AFF;
  border: 1rpx solid #E5E5E7;
}

.action-btn.danger {
  background-color: #FF3B30;
  color: #FFFFFF;
}

.action-btn[disabled] {
  background-color: #C7C7CC;
  color: #FFFFFF;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.approval-modal {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5E7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 32rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.modal-close::after {
  border: none;
}

.modal-content {
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 28rpx;
  color: #1D1D1F;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-textarea {
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background-color: #FAFAFA;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #007AFF;
  background-color: #FFFFFF;
}

.modal-actions {
  display: flex;
  padding: 24rpx 32rpx 32rpx;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.modal-btn::after {
  border: none;
}

.modal-btn.cancel {
  background-color: #F2F2F7;
  color: #8E8E93;
}

.modal-btn.confirm {
  background-color: #007AFF;
  color: #FFFFFF;
}

.modal-btn[disabled] {
  background-color: #C7C7CC;
  color: #FFFFFF;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .expense-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .expense-description {
    margin-right: 0;
    margin-bottom: 8rpx;
  }

  .owner-actions,
  .approver-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}