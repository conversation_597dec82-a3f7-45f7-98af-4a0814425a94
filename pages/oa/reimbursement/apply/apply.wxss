/* pages/oa/reimbursement/apply/apply.wxss */
.container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required {
  color: #ff3b30;
  margin-left: 4rpx;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #0066CC;
  background: #fff;
}

.form-input::placeholder {
  color: #999;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  box-sizing: border-box;
  transition: all 0.3s ease;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #0066CC;
  background: #fff;
}

.form-textarea::placeholder {
  color: #999;
}

/* 选择器 */
.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.form-picker:active {
  border-color: #0066CC;
  background: #fff;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

/* 占位符样式 */
.picker-text.placeholder {
  color: #999;
  font-size: 28rpx;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
}

.picker-icon {
  font-size: 32rpx;
  margin-left: 12rpx;
}

/* 行布局 */
.form-row {
  display: flex;
  gap: 20rpx;
}

.form-item.half {
  flex: 1;
}

/* 提交区域 */
.submit-section {
  margin-top: 40rpx;
  display: flex;
  gap: 20rpx;
}

.draft-btn {
  flex: 1;
  height: 96rpx;
  background: #f8f9fa;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  color: #666;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.draft-btn:active {
  background: #e8e8e8;
  transform: translateY(2rpx);
}

.draft-btn:disabled {
  opacity: 0.6;
  transform: none;
}

.submit-btn {
  flex: 2;
  height: 96rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0052A3 100%);
  border: none;
  border-radius: 16rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 102, 204, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.4);
}

.submit-btn:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.2);
}

/* 响应式适配 */
@media (max-width: 480px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-item.half {
    margin-bottom: 32rpx;
  }

  .container {
    padding: 12rpx;
  }

  .form-section {
    padding: 24rpx;
  }

  .submit-section {
    flex-direction: column;
  }

  .draft-btn,
  .submit-btn {
    flex: 1;
  }
}

/* 动画效果 */
.form-section {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}