<!-- 统一侧边栏组件 -->
<nav class="sidebar-modern" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="bi bi-egg-fried brand-icon"></i>
            <span class="brand-text">
                智慧养鹅
                <% if (platformType === 'saas') { %>
                    <small class="platform-label">SaaS平台</small>
                <% } else { %>
                    <small class="platform-label">管理系统</small>
                <% } %>
            </span>
        </div>
        
        <!-- 移动端关闭按钮 -->
        <button class="sidebar-close d-md-none" onclick="toggleSidebar()">
            <i class="bi bi-x"></i>
        </button>
    </div>
    
    <div class="sidebar-content">
        <!-- 用户信息 -->
        <% if (locals.currentUser) { %>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <% if (currentUser.avatar) { %>
                        <img src="<%= currentUser.avatar %>" alt="用户头像">
                    <% } else { %>
                        <i class="bi bi-person-circle"></i>
                    <% } %>
                </div>
                <div class="user-info">
                    <div class="user-name"><%= currentUser.name || '管理员' %></div>
                    <div class="user-role"><%= currentUser.role || '系统管理员' %></div>
                </div>
            </div>
        <% } %>
        
        <!-- 导航菜单 -->
        <ul class="sidebar-nav">
            <% 
                // 根据平台类型定义菜单项
                let menuItems = [];
                
                if (platformType === 'saas') {
                    // SaaS平台菜单
                    menuItems = [
                        { id: 'dashboard', text: '仪表板', icon: 'bi-speedometer2', url: '/dashboard' },
                        { divider: true, text: '租户管理' },
                        { id: 'tenants', text: '租户列表', icon: 'bi-building', url: '/tenants' },
                        { id: 'tenant-stats', text: '租户统计', icon: 'bi-graph-up', url: '/tenant-stats' },
                        { divider: true, text: '平台设置' },
                        { id: 'ai-config', text: 'AI配置', icon: 'bi-cpu', url: '/ai-config' },
                        { id: 'announcements', text: '公告管理', icon: 'bi-megaphone', url: '/announcements' },
                        { id: 'pricing', text: '价格管理', icon: 'bi-currency-dollar', url: '/pricing' },
                        { id: 'plans', text: '套餐管理', icon: 'bi-layers', url: '/plans' },
                        { divider: true, text: '系统监控' },
                        { id: 'monitoring', text: '性能监控', icon: 'bi-activity', url: '/monitoring' },
                        { id: 'logs', text: '系统日志', icon: 'bi-journal-text', url: '/logs' },
                        { id: 'users', text: '用户管理', icon: 'bi-people', url: '/users' },
                        { divider: true, text: '帮助支持' },
                        { id: 'knowledge', text: '知识库', icon: 'bi-book', url: '/knowledge' },
                        { id: 'ai', text: 'AI助手', icon: 'bi-robot', url: '/ai' }
                    ];
                } else {
                    // 普通管理系统菜单
                    menuItems = [
                        { id: 'dashboard', text: '仪表板', icon: 'bi-speedometer2', url: '/dashboard' },
                        { divider: true, text: '养殖管理' },
                        { id: 'flocks', text: '鹅群管理', icon: 'bi-egg', url: '/flocks' },
                        { id: 'health', text: '健康管理', icon: 'bi-heart-pulse', url: '/health' },
                        { id: 'production', text: '生产管理', icon: 'bi-clipboard-data', url: '/production' },
                        { id: 'inventory', text: '库存管理', icon: 'bi-box-seam', url: '/inventory' },
                        { divider: true, text: '系统管理' },
                        { id: 'users', text: '用户管理', icon: 'bi-people', url: '/users' },
                        { id: 'settings', text: '系统设置', icon: 'bi-gear', url: '/settings' },
                        { divider: true, text: '数据分析' },
                        { id: 'reports', text: '报表中心', icon: 'bi-bar-chart', url: '/reports' },
                        { id: 'analytics', text: '数据分析', icon: 'bi-graph-up-arrow', url: '/analytics' },
                        { divider: true, text: '工具' },
                        { id: 'api-docs', text: 'API文档', icon: 'bi-code-square', url: '/api-docs' },
                        { id: 'icons', text: '图标库', icon: 'bi-palette', url: '/icons' }
                    ];
                }
            %>
            
            <% menuItems.forEach(function(item) { %>
                <% if (item.divider) { %>
                    <li class="nav-divider">
                        <span class="divider-text"><%= item.text %></span>
                    </li>
                <% } else { %>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === item.id ? 'active' : '' %>" 
                           href="<%= item.url %>" data-page="<%= item.id %>">
                            <i class="<%= item.icon %> nav-icon"></i>
                            <span class="nav-text"><%= item.text %></span>
                            <% if (item.badge) { %>
                                <span class="nav-badge"><%= item.badge %></span>
                            <% } %>
                        </a>
                    </li>
                <% } %>
            <% }); %>
        </ul>
    </div>
    
    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
        <div class="sidebar-actions">
            <button class="action-btn" onclick="toggleTheme()" title="切换主题">
                <i class="bi bi-moon"></i>
            </button>
            <button class="action-btn" onclick="showHelp()" title="帮助">
                <i class="bi bi-question-circle"></i>
            </button>
            <button class="action-btn" onclick="logout()" title="退出登录">
                <i class="bi bi-box-arrow-right"></i>
            </button>
        </div>
        
        <div class="sidebar-version">
            <small>v2.6.0</small>
        </div>
    </div>
</nav>

<!-- 移动端遮罩 -->
<div class="sidebar-overlay d-md-none" onclick="toggleSidebar()"></div>

<style>
    /* 侧边栏样式 */
    .sidebar-modern {
        width: 280px;
        min-height: 100vh;
        background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
        color: white;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .sidebar-header {
        padding: 20px 24px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .sidebar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: white;
    }
    
    .brand-icon {
        font-size: 28px;
        color: var(--primary-color);
        margin-right: 12px;
    }
    
    .brand-text {
        font-size: 20px;
        font-weight: 700;
        line-height: 1.2;
    }
    
    .platform-label {
        display: block;
        font-size: 11px;
        font-weight: 400;
        color: rgba(255,255,255,0.7);
        margin-top: 2px;
    }
    
    .sidebar-close {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        padding: 5px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }
    
    .sidebar-close:hover {
        background-color: rgba(255,255,255,0.1);
    }
    
    .sidebar-content {
        flex: 1;
        overflow-y: auto;
        padding: 20px 0;
    }
    
    .sidebar-user {
        display: flex;
        align-items: center;
        padding: 0 24px 20px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        margin-bottom: 20px;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255,255,255,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        overflow: hidden;
    }
    
    .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .user-avatar i {
        font-size: 24px;
        color: rgba(255,255,255,0.7);
    }
    
    .user-info {
        flex: 1;
        min-width: 0;
    }
    
    .user-name {
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .user-role {
        font-size: 12px;
        color: rgba(255,255,255,0.7);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .sidebar-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .nav-divider {
        padding: 16px 24px 8px;
        margin: 8px 0;
    }
    
    .divider-text {
        font-size: 11px;
        font-weight: 600;
        color: rgba(255,255,255,0.5);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .nav-item {
        margin: 2px 12px;
    }
    
    .nav-link {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .nav-link:hover {
        color: white;
        background: rgba(255,255,255,0.1);
        transform: translateX(4px);
    }
    
    .nav-link.active {
        color: white;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
    
    .nav-link.active::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: rgba(255,255,255,0.3);
    }
    
    .nav-icon {
        font-size: 16px;
        margin-right: 12px;
        width: 20px;
        text-align: center;
        flex-shrink: 0;
    }
    
    .nav-text {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
    }
    
    .nav-badge {
        background: var(--danger-color);
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: 600;
        margin-left: 8px;
    }
    
    .sidebar-footer {
        padding: 20px 24px;
        border-top: 1px solid rgba(255,255,255,0.1);
    }
    
    .sidebar-actions {
        display: flex;
        justify-content: space-around;
        margin-bottom: 12px;
    }
    
    .action-btn {
        background: none;
        border: none;
        color: rgba(255,255,255,0.7);
        font-size: 18px;
        padding: 8px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s ease;
    }
    
    .action-btn:hover {
        color: white;
        background: rgba(255,255,255,0.1);
        transform: scale(1.1);
    }
    
    .sidebar-version {
        text-align: center;
        color: rgba(255,255,255,0.5);
        font-size: 11px;
    }
    
    /* 移动端遮罩 */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .sidebar-modern {
            transform: translateX(-100%);
        }
        
        .sidebar-modern.show {
            transform: translateX(0);
        }
        
        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .main-content {
            margin-left: 0 !important;
        }
    }
    
    @media (min-width: 769px) {
        .main-content {
            margin-left: 280px;
        }
    }
    
    /* 滚动条样式 */
    .sidebar-content::-webkit-scrollbar {
        width: 6px;
    }
    
    .sidebar-content::-webkit-scrollbar-track {
        background: rgba(255,255,255,0.1);
        border-radius: 3px;
    }
    
    .sidebar-content::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
    }
    
    .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }
    
    /* 动画效果 */
    .nav-item {
        animation: slideInLeft 0.4s ease-out backwards;
    }
    
    .nav-item:nth-child(1) { animation-delay: 0.1s; }
    .nav-item:nth-child(2) { animation-delay: 0.15s; }
    .nav-item:nth-child(3) { animation-delay: 0.2s; }
    .nav-item:nth-child(4) { animation-delay: 0.25s; }
    .nav-item:nth-child(5) { animation-delay: 0.3s; }
    .nav-item:nth-child(6) { animation-delay: 0.35s; }
    .nav-item:nth-child(7) { animation-delay: 0.4s; }
    .nav-item:nth-child(8) { animation-delay: 0.45s; }
    .nav-item:nth-child(9) { animation-delay: 0.5s; }
    .nav-item:nth-child(10) { animation-delay: 0.55s; }
    
    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
</style>

<script>
    // 侧边栏功能
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
    }
    
    // 主题切换
    function toggleTheme() {
        const body = document.body;
        const isDark = body.classList.contains('dark-theme');
        
        if (isDark) {
            body.classList.remove('dark-theme');
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-theme');
            localStorage.setItem('theme', 'dark');
        }
        
        // 更新图标
        const themeIcon = document.querySelector('.action-btn i.bi-moon, .action-btn i.bi-sun');
        if (themeIcon) {
            themeIcon.className = isDark ? 'bi bi-moon' : 'bi bi-sun';
        }
    }
    
    // 显示帮助
    function showHelp() {
        const helpUrl = window.AppConfig.platformType === 'saas' ? '/help/saas' : '/help/admin';
        window.open(helpUrl, '_blank');
    }
    
    // 退出登录
    function logout() {
        if (confirm('确定要退出登录吗？')) {
            window.location.href = '/logout';
        }
    }
    
    // 页面加载时恢复主题
    document.addEventListener('DOMContentLoaded', function() {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            const themeIcon = document.querySelector('.action-btn i.bi-moon');
            if (themeIcon) {
                themeIcon.className = 'bi bi-sun';
            }
        }
        
        // 高亮当前页面
        const currentPage = '<%= locals.currentPage || "" %>';
        if (currentPage) {
            const navLink = document.querySelector(`[data-page="${currentPage}"]`);
            if (navLink) {
                navLink.classList.add('active');
            }
        }
    });
</script>