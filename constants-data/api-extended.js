/**
 * 扩展API常量 - 移至分包以减少主包尺寸
 * 包含非核心的API端点定义
 */

// 引入基础配置
const { BASE_URL, API_VERSIONS } = require('../constants/api.constants.js');

// 构建完整API路径的辅助函数
const buildApiUrl = (version, path) => `${BASE_URL}${version}${path}`;

// 扩展的API端点（非核心）
const EXTENDED_ENDPOINTS = {
  // ===== OA系统API (移至分包) =====
  OA: {
    // 审批系统
    APPROVAL: {
      LIST: buildApiUrl(API_VERSIONS.V1, '/oa/approvals'),
      CREATE: buildApiUrl(API_VERSIONS.V1, '/oa/approvals'),
      DETAIL: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/approvals/${id}`),
      APPROVE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/approvals/${id}/approve`),
      REJECT: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/approvals/${id}/reject`),
      STATISTICS: buildApiUrl(API_VERSIONS.V1, '/oa/approvals/statistics')
    },

    // 采购申请
    PURCHASE_REQUESTS: {
      LIST: buildApiUrl(API_VERSIONS.V1, '/oa/purchase-requests'),
      CREATE: buildApiUrl(API_VERSIONS.V1, '/oa/purchase-requests'),
      DETAIL: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/purchase-requests/${id}`),
      UPDATE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/purchase-requests/${id}`),
      DELETE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/purchase-requests/${id}`),
      APPROVE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/purchase-requests/${id}/approve`)
    },

    // 费用报销
    REIMBURSEMENT_REQUESTS: {
      LIST: buildApiUrl(API_VERSIONS.V1, '/oa/reimbursement-requests'),
      CREATE: buildApiUrl(API_VERSIONS.V1, '/oa/reimbursement-requests'),
      DETAIL: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursement-requests/${id}`),
      UPDATE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursement-requests/${id}`),
      DELETE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursement-requests/${id}`),
      APPROVE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursement-requests/${id}/approve`)
    },

    // 财务管理
    FINANCE: {
      STATS: buildApiUrl(API_VERSIONS.V1, '/oa/finance/stats'),
      RECORDS: buildApiUrl(API_VERSIONS.V1, '/oa/finance/records'),
      REPORTS: buildApiUrl(API_VERSIONS.V1, '/oa/finance/reports'),
      BUDGET: buildApiUrl(API_VERSIONS.V1, '/oa/finance/budget')
    }
  }
};

// 导出扩展API常量
module.exports = {
  EXTENDED_ENDPOINTS,
  buildApiUrl
};
