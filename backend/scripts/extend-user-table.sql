-- 扩展用户表，添加企业员工信息
-- 优化OA系统用户信息复用

USE smart_goose;

-- 添加企业员工必要字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS employee_id VARCHAR(20) COMMENT '工号',
ADD COLUMN IF NOT EXISTS department VARCHAR(100) COMMENT '部门',
ADD COLUMN IF NOT EXISTS position VARCHAR(100) COMMENT '职位',
ADD COLUMN IF NOT EXISTS manager_id INT COMMENT '直属上级ID',
ADD COLUMN IF NOT EXISTS emergency_contact VARCHAR(100) COMMENT '紧急联系人',
ADD COLUMN IF NOT EXISTS emergency_phone VARCHAR(20) COMMENT '紧急联系人电话',
ADD COLUMN IF NOT EXISTS hire_date DATE COMMENT '入职日期',
ADD COLUMN IF NOT EXISTS office_location VARCHAR(200) COMMENT '办公地点',
ADD COLUMN IF NOT EXISTS job_level VARCHAR(50) COMMENT '职级',
ADD COLUMN IF NOT EXISTS salary_grade VARCHAR(20) COMMENT '薪资等级';

-- 添加索引
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_employee_id (employee_id),
ADD INDEX IF NOT EXISTS idx_department (department),
ADD INDEX IF NOT EXISTS idx_manager_id (manager_id);

-- 添加外键约束（直属上级）
ALTER TABLE users 
ADD CONSTRAINT fk_users_manager 
FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;

-- 创建部门表
CREATE TABLE IF NOT EXISTS departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    code VARCHAR(20) UNIQUE COMMENT '部门编码',
    parent_id INT COMMENT '上级部门ID',
    manager_id INT COMMENT '部门经理ID',
    description TEXT COMMENT '部门描述',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_code (code),
    INDEX idx_parent_id (parent_id)
) ENGINE=InnoDB COMMENT='部门表';

-- 创建职位表
CREATE TABLE IF NOT EXISTS positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '职位名称',
    code VARCHAR(20) UNIQUE COMMENT '职位编码',
    department_id INT COMMENT '所属部门ID',
    level INT DEFAULT 1 COMMENT '职位级别',
    description TEXT COMMENT '职位描述',
    requirements TEXT COMMENT '任职要求',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_department_id (department_id)
) ENGINE=InnoDB COMMENT='职位表';

-- 插入示例部门数据
INSERT IGNORE INTO departments (name, code, description) VALUES
('总经理办公室', 'CEO', '公司最高管理层'),
('人力资源部', 'HR', '负责人力资源管理'),
('财务部', 'FIN', '负责财务管理和会计核算'),
('生产部', 'PROD', '负责养殖生产管理'),
('销售部', 'SALES', '负责产品销售和市场推广'),
('技术部', 'TECH', '负责技术研发和系统维护'),
('采购部', 'PUR', '负责采购管理'),
('品质部', 'QC', '负责质量控制和检验');

-- 插入示例职位数据
INSERT IGNORE INTO positions (name, code, department_id, level, description) VALUES
('总经理', 'CEO', 1, 1, '公司最高管理者'),
('副总经理', 'VP', 1, 2, '协助总经理管理公司'),
('人力资源经理', 'HR_MGR', 2, 3, '负责人力资源部门管理'),
('人力资源专员', 'HR_SPEC', 2, 4, '负责招聘、培训等具体工作'),
('财务经理', 'FIN_MGR', 3, 3, '负责财务部门管理'),
('会计', 'ACCOUNTANT', 3, 4, '负责会计核算工作'),
('出纳', 'CASHIER', 3, 4, '负责现金管理'),
('生产经理', 'PROD_MGR', 4, 3, '负责生产部门管理'),
('养殖技术员', 'TECH_SPEC', 4, 4, '负责养殖技术指导'),
('销售经理', 'SALES_MGR', 5, 3, '负责销售部门管理'),
('销售代表', 'SALES_REP', 5, 4, '负责客户开发和维护');

-- 更新现有用户的部门和职位信息（示例）
UPDATE users SET 
    department = '技术部',
    position = '系统管理员',
    employee_id = CONCAT('EMP', LPAD(id, 4, '0'))
WHERE role IN ('admin', 'platform_admin');

UPDATE users SET 
    department = '生产部',
    position = '养殖技术员',
    employee_id = CONCAT('EMP', LPAD(id, 4, '0'))
WHERE role = 'staff' AND department IS NULL;

-- 创建用户扩展信息视图
CREATE OR REPLACE VIEW user_profile_view AS
SELECT 
    u.id,
    u.username,
    u.name,
    u.employee_id,
    u.email,
    u.phone,
    u.department,
    u.position,
    u.role,
    u.status,
    u.hire_date,
    u.office_location,
    u.emergency_contact,
    u.emergency_phone,
    u.created_at,
    d.name as department_name,
    p.name as position_name,
    m.name as manager_name,
    m.phone as manager_phone
FROM users u
LEFT JOIN departments d ON u.department = d.code
LEFT JOIN positions p ON u.position = p.code  
LEFT JOIN users m ON u.manager_id = m.id
WHERE u.status = 'active';