-- 智慧养鹅数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS zhihuiyange_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'zhihuiyange'@'localhost' IDENTIFIED BY 'zhihuiyange123';
GRANT ALL PRIVILEGES ON zhihuiyange_local.* TO 'zhihuiyange'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 使用数据库
USE zhihuiyange_local;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(100),
  farm_name VARCHAR(100),
  phone VARCHAR(20),
  email VARCHAR(100) UNIQUE NOT NULL,
  role ENUM('admin', 'user', 'manager') DEFAULT 'user',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建健康记录表
CREATE TABLE IF NOT EXISTS health_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  goose_id VARCHAR(50) NOT NULL,
  symptoms TEXT,
  diagnosis TEXT,
  treatment TEXT,
  status ENUM('pending', 'processing', 'completed') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建生产记录表
CREATE TABLE IF NOT EXISTS production_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  egg_count INT DEFAULT 0,
  feed_consumption DECIMAL(10,2) DEFAULT 0,
  temperature DECIMAL(5,2),
  humidity DECIMAL(5,2),
  notes TEXT,
  recorded_date DATE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 插入默认管理员用户
INSERT IGNORE INTO users (username, password, email, role) VALUES 
('admin', '$2a$10$8K1p/a0dhrxiowP.dnkgNORTWgdEDHn5L2/xjpEWuC.QQv4rKO9jO', '<EMAIL>', 'admin');

-- 插入测试数据
INSERT IGNORE INTO users (username, password, email, role) VALUES 
('testuser', '$2a$10$8K1p/a0dhrxiowP.dnkgNORTWgdEDHn5L2/xjpEWuC.QQv4rKO9jO', '<EMAIL>', 'user');