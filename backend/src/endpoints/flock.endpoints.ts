import { z } from "zod";
import { defaultEndpointsFactory } from "express-zod-api";
import { authMiddleware } from "../middleware/auth.middleware";
import {
  CreateFlockSchema,
  UpdateFlockSchema,
  FlockResponseSchema,
  FlockQuerySchema,
  FlockListResponseSchema,
  FlockParamsSchema,
  type CreateFlockRequest,
  type UpdateFlockRequest,
  type FlockQuery,
  type FlockParams,
} from "../types/flock.types";

// 模拟数据库操作 - 实际应该连接到真实数据库
const mockFlocks: any[] = [
  {
    id: 1,
    userId: 1,
    name: "一号鹅群",
    batchNumber: "FLOCK-1704067200000",
    breed: "white_goose",
    totalCount: 100,
    currentCount: 95,
    maleCount: 45,
    femaleCount: 50,
    ageGroup: "adult",
    status: "active",
    establishedDate: "2024-01-01T00:00:00.000Z",
    location: "A区养殖场",
    description: "优质白鹅种群",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
  {
    id: 2,
    userId: 1,
    name: "二号鹅群",
    batchNumber: "FLOCK-1704153600000",
    breed: "grey_goose",
    totalCount: 80,
    currentCount: 78,
    maleCount: 35,
    femaleCount: 43,
    ageGroup: "young",
    status: "active",
    establishedDate: "2024-01-02T00:00:00.000Z",
    location: "B区养殖场",
    description: "灰鹅幼群",
    createdAt: "2024-01-02T00:00:00.000Z",
    updatedAt: "2024-01-02T00:00:00.000Z",
  },
];

// 错误类定义
class NotFoundError extends Error {
  name = "NotFoundError";
  constructor(message: string = "资源不存在") {
    super(message);
  }
}

class ConflictError extends Error {
  name = "ConflictError";
  constructor(message: string = "资源冲突") {
    super(message);
  }
}

// 生成批次号
const generateBatchNumber = (): string => {
  return `FLOCK-${Date.now()}`;
};

// 获取鹅群列表端点
export const getFlockListEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .build({
    method: "get",
    input: FlockQuerySchema,
    output: FlockListResponseSchema,
    shortDescription: "获取鹅群列表",
    description: "获取当前用户的鹅群列表，支持分页、搜索和排序",
    tag: "flocks",
    handler: async ({ input, options, logger }) => {
      const { user } = options;
      const {
        page,
        limit,
        search,
        status,
        breed,
        ageGroup,
        sortBy,
        sortOrder,
      } = input;

      logger.info(`用户 ${user.id} 查询鹅群列表`, { input });

      // 模拟数据库查询
      let filteredFlocks = mockFlocks.filter(
        (flock) => flock.userId === user.id,
      );

      // 搜索过滤
      if (search) {
        const searchLower = search.toLowerCase();
        filteredFlocks = filteredFlocks.filter(
          (flock) =>
            flock.name.toLowerCase().includes(searchLower) ||
            flock.batchNumber.toLowerCase().includes(searchLower) ||
            (flock.description &&
              flock.description.toLowerCase().includes(searchLower)),
        );
      }

      // 状态过滤
      if (status) {
        filteredFlocks = filteredFlocks.filter(
          (flock) => flock.status === status,
        );
      }

      // 品种过滤
      if (breed) {
        filteredFlocks = filteredFlocks.filter(
          (flock) => flock.breed === breed,
        );
      }

      // 年龄组过滤
      if (ageGroup) {
        filteredFlocks = filteredFlocks.filter(
          (flock) => flock.ageGroup === ageGroup,
        );
      }

      // 排序
      filteredFlocks.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        const factor = sortOrder === "ASC" ? 1 : -1;

        if (typeof aValue === "string") {
          return aValue.localeCompare(bValue) * factor;
        }

        return (aValue - bValue) * factor;
      });

      // 分页
      const total = filteredFlocks.length;
      const offset = (page - 1) * limit;
      const items = filteredFlocks.slice(offset, offset + limit);

      const pagination = {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      };

      return { items, pagination };
    },
  });

// 创建鹅群端点
export const createFlockEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .build({
    method: "post",
    input: CreateFlockSchema,
    output: FlockResponseSchema,
    shortDescription: "创建鹅群",
    description: "创建新的鹅群记录",
    tag: "flocks",
    handler: async ({ input, options, logger }) => {
      const { user } = options;

      logger.info(`用户 ${user.id} 创建鹅群`, { input });

      // 检查名称是否重复
      const existingFlock = mockFlocks.find(
        (flock) => flock.userId === user.id && flock.name === input.name,
      );

      if (existingFlock) {
        throw new ConflictError("鹅群名称已存在");
      }

      // 创建新鹅群
      const newFlock = {
        id: Math.max(...mockFlocks.map((f) => f.id), 0) + 1,
        userId: user.id,
        batchNumber: generateBatchNumber(),
        currentCount: input.totalCount,
        status: "active" as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...input,
        location: input.location || null,
        description: input.description || null,
      };

      mockFlocks.push(newFlock);

      logger.info(`鹅群创建成功`, { flockId: newFlock.id });

      return newFlock;
    },
  });

// 获取单个鹅群端点
export const getFlockByIdEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .build({
    method: "get",
    input: FlockParamsSchema,
    output: FlockResponseSchema,
    shortDescription: "获取鹅群详情",
    description: "根据ID获取鹅群的详细信息",
    tag: "flocks",
    handler: async ({ input, options, logger }) => {
      const { user } = options;
      const { id } = input;

      logger.info(`用户 ${user.id} 查询鹅群详情`, { flockId: id });

      const flock = mockFlocks.find((f) => f.id === id && f.userId === user.id);

      if (!flock) {
        throw new NotFoundError("鹅群不存在或无权限访问");
      }

      return flock;
    },
  });

// 更新鹅群端点
export const updateFlockEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .build({
    method: "patch",
    input: UpdateFlockSchema.extend(FlockParamsSchema.shape),
    output: FlockResponseSchema,
    shortDescription: "更新鹅群",
    description: "更新鹅群的信息",
    tag: "flocks",
    handler: async ({ input, options, logger }) => {
      const { user } = options;
      const { id, ...updateData } = input;

      logger.info(`用户 ${user.id} 更新鹅群`, { flockId: id, updateData });

      const flockIndex = mockFlocks.findIndex(
        (f) => f.id === id && f.userId === user.id,
      );

      if (flockIndex === -1) {
        throw new NotFoundError("鹅群不存在或无权限访问");
      }

      // 如果更新名称，检查重复
      if (updateData.name) {
        const existingFlock = mockFlocks.find(
          (flock) =>
            flock.userId === user.id &&
            flock.name === updateData.name &&
            flock.id !== id,
        );

        if (existingFlock) {
          throw new ConflictError("鹅群名称已存在");
        }
      }

      // 验证数量关系
      if (
        updateData.totalCount !== undefined ||
        updateData.maleCount !== undefined ||
        updateData.femaleCount !== undefined
      ) {
        const currentFlock = mockFlocks[flockIndex];
        const totalCount = updateData.totalCount ?? currentFlock.totalCount;
        const maleCount = updateData.maleCount ?? currentFlock.maleCount;
        const femaleCount = updateData.femaleCount ?? currentFlock.femaleCount;

        if (maleCount + femaleCount !== totalCount) {
          throw new Error("雄性和雌性数量之和必须等于总数量");
        }
      }

      // 更新鹅群
      const updatedFlock = {
        ...mockFlocks[flockIndex],
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      mockFlocks[flockIndex] = updatedFlock;

      logger.info(`鹅群更新成功`, { flockId: id });

      return updatedFlock;
    },
  });

// 删除鹅群端点
export const deleteFlockEndpoint = defaultEndpointsFactory
  .addMiddleware(authMiddleware)
  .build({
    method: "delete",
    input: FlockParamsSchema,
    output: z.object({
      success: z.literal(true),
      message: z.string(),
    }),
    shortDescription: "删除鹅群",
    description: "删除指定的鹅群记录",
    tag: "flocks",
    handler: async ({ input, options, logger }) => {
      const { user } = options;
      const { id } = input;

      logger.info(`用户 ${user.id} 删除鹅群`, { flockId: id });

      const flockIndex = mockFlocks.findIndex(
        (f) => f.id === id && f.userId === user.id,
      );

      if (flockIndex === -1) {
        throw new NotFoundError("鹅群不存在或无权限访问");
      }

      mockFlocks.splice(flockIndex, 1);

      logger.info(`鹅群删除成功`, { flockId: id });

      return {
        success: true as const,
        message: "鹅群删除成功",
      };
    },
  });
