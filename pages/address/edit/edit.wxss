/* pages/address/edit/edit.wxss - 地址编辑页面样式 */
@import '/styles/design-system.wxss';

.address-edit-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 编辑内容 */
.edit-content {
  height: calc(100vh - 120rpx);
  padding: var(--space-lg);
}

/* 表单区域 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

/* 表单组 */
.form-group {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-xl) var(--space-2xl);
  background: var(--bg-subtle);
  border-bottom: 1rpx solid var(--border-subtle);
}

.group-icon {
  font-size: var(--text-lg);
}

.group-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.group-desc {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-left: auto;
}

/* 表单项 */
.form-item {
  position: relative;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  align-items: center;
  gap: 16rpx;
  min-height: 88rpx;
  box-sizing: border-box;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 28rpx !important;
  color: #8E8E93 !important;
  font-weight: 500 !important;
  min-width: 120rpx !important;
  flex-shrink: 0 !important;
  line-height: 44rpx !important;
  display: flex !important;
  align-items: center !important;
  height: 44rpx !important;
}

.item-input {
  flex: 1 !important;
  font-size: 28rpx !important;
  color: #1D1D1F !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  line-height: 44rpx !important;
  height: 44rpx !important;
  /* 确保输入框垂直居中 */
  display: flex !important;
  align-items: center !important;
}

.item-input::placeholder {
  color: var(--text-placeholder);
}

.required-mark {
  color: var(--error);
  font-size: var(--text-base);
  font-weight: var(--font-bold);
  margin-left: var(--space-xs);
}

/* 地区选择器 - 直接复制OA页面的成功实现并适配 */
.region-picker-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  /* 完全模拟item-input的样式 */
  line-height: 44rpx;
  background: transparent;
  transition: all 0.2s ease;
}

.region-picker-display:active {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}

.region-picker-text {
  flex: 1;
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 44rpx;
  height: 44rpx;
  /* 关键：确保与item-input对齐 */
  display: flex;
  align-items: center;
}

.region-picker-text.placeholder {
  color: #C7C7CC;
}

.region-picker-arrow {
  font-size: 28rpx;
  color: #8E8E93;
  margin-left: 8rpx;
  line-height: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  transition: transform 0.15s ease, color 0.15s ease;
}

.region-picker-display:active .region-picker-arrow {
  transform: translateX(2rpx);
  color: #007AFF;
}

/* 文本域项 */
.textarea-item {
  align-items: flex-start;
  padding-top: var(--space-xl);
  padding-bottom: var(--space-xl);
}

.item-textarea {
  flex: 1;
  font-size: var(--text-base);
  color: var(--text-primary);
  background: transparent;
  border: none;
  outline: none;
  min-height: 100rpx;
  line-height: var(--leading-relaxed);
}

.item-textarea::placeholder {
  color: var(--text-placeholder);
}

/* 标签选择器 */
.tag-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 24rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  border: 2rpx solid #E5E5EA;
  background: #F8F9FA;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 60rpx;
  box-sizing: border-box;
  flex-shrink: 0;
}

.tag-item:active {
  transform: scale(0.98);
}

.tag-item.active {
  border-color: #007AFF;
  background: #E3F2FD;
  color: #007AFF;
}

.tag-icon {
  font-size: 28rpx;
  flex-shrink: 0;
}

.tag-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #8E8E93;
  white-space: nowrap;
}

.tag-item.active .tag-text {
  color: #007AFF;
  font-weight: 600;
}

/* 自定义标签 - 与其他标签保持一致的样式 */
.custom-tag {
  border: 2rpx dashed #C7C7CC;
  background: #F8F9FA;
  padding: 12rpx 20rpx;
  min-height: 60rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-input {
  font-size: 28rpx;
  color: #8E8E93;
  background: transparent;
  border: none;
  outline: none;
  text-align: center;
  width: 100rpx;
  min-width: 80rpx;
}

.custom-input::placeholder {
  color: #C7C7CC;
}

/* 开关项 */
.switch-item {
  align-items: flex-start;
  padding: var(--space-xl) var(--space-2xl);
}

.switch-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.switch-label {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.switch-desc {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
}

.switch-control {
  margin-left: var(--space-lg);
  flex-shrink: 0;
}

/* 底部间距 */
.form-bottom-space {
  height: var(--space-4xl);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16rpx 24rpx calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #F2F2F7;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.cancel-btn {
  background: #F8F9FA;
  color: #8E8E93;
  border: 1rpx solid #E5E5EA;
}

.cancel-btn:active {
  background: #E5E5EA;
  transform: scale(0.98);
}

.save-btn {
  background: #007AFF;
  color: white;
}

.save-btn:active {
  background: #0056CC;
  transform: scale(0.98);
}

.save-btn.disabled {
  background: #C7C7CC;
  color: #8E8E93;
  cursor: not-allowed;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* 隐藏的选择器 */
.hidden-picker {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  opacity: 0;
  pointer-events: none;
}

/* 响应式适配 */
@media (max-width: 480rpx) {
  .edit-content {
    padding: var(--space-sm);
  }

  .form-item {
    padding: var(--space-lg) var(--space-xl);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }

  .item-label {
    min-width: auto;
  }

  .picker-content {
    width: 100%;
  }

  .tag-selector {
    padding: var(--space-lg) var(--space-xl);
  }

  .switch-item {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .switch-control {
    margin-left: 0;
    align-self: flex-start;
  }

  .bottom-actions {
    padding: var(--space-sm) var(--space-lg) calc(var(--space-sm) + env(safe-area-inset-bottom));
  }
}

/* 可访问性支持 */
@media (prefers-reduced-motion: reduce) {

  .form-group,
  .tag-item,
  .action-btn {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {

  .form-group,
  .tag-item,
  .action-btn {
    border-width: 3rpx;
  }

  .form-item {
    border-bottom-width: 2rpx;
  }
}