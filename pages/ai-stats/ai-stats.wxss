/* pages/ai-stats/ai-stats.wxss */
.ai-stats-container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding: 20rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 时间范围选择器 */
.time-range-selector {
  display: flex;
  justify-content: center;
  background-color: var(--text-inverse);
  border-radius: var(--radius-xl);
  padding: 10rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.range-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: var(--text-secondary);
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.range-item.active {
  background-color: var(--primary);
  color: var(--text-inverse);
  font-weight: bold;
}

/* 通用区块样式 */
.section {
  background-color: var(--text-inverse);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  flex: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 15rpx 20rpx;
  background-color: #f8f9fa;
  color: var(--text-secondary);
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background-color: #e9ecef;
}

.action-icon {
  font-size: 28rpx;
}

.action-text {
  font-size: 24rpx;
}

/* 统计概览网格 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.overview-item {
  text-align: center;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.overview-item.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-item.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-item.info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-item.warning {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-item.secondary {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.overview-item.accent {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.item-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 10rpx;
}

.item-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-inverse);
  margin-bottom: 5rpx;
}

.item-unit {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 服务提供商列表 */
.provider-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.provider-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.provider-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2rpx);
}

.provider-info {
  flex: 1;
}

.provider-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 10rpx;
}

.provider-metrics {
  display: flex;
  gap: 20rpx;
}

.metric {
  font-size: 24rpx;
  color: var(--text-secondary);
  background-color: var(--text-inverse);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.provider-success {
  text-align: center;
}

.success-rate {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #28a745;
}

.success-label {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 使用场景列表 */
.scenario-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.scenario-item {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.scenario-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2rpx);
}

.scenario-info {
  margin-bottom: 20rpx;
}

.scenario-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 15rpx;
}

.scenario-metrics {
  display: flex;
  justify-content: space-between;
}

.metric-item {
  text-align: center;
  flex: 1;
}

.metric-label {
  display: block;
  font-size: 22rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.metric-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.scenario-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0066cc, #4facfe);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  min-width: 60rpx;
  text-align: right;
}

/* 模型网格 */
.model-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.model-card {
  padding: 25rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: var(--text-inverse);
  transition: all 0.3s ease;
}

.model-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.model-name {
  font-size: 28rpx;
  font-weight: bold;
}

.model-cost {
  font-size: 24rpx;
  opacity: 0.9;
}

.model-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-requests {
  font-size: 24rpx;
  opacity: 0.9;
}

.model-percentage {
  font-size: 32rpx;
  font-weight: bold;
}

/* 图表区域 */
.chart-tabs {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.chart-tab {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.chart-tab.active {
  background-color: var(--primary);
  color: var(--text-inverse);
  font-weight: bold;
}

.chart-container {
  text-align: center;
  padding: 40rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.chart-placeholder {
  display: block;
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 20rpx;
}

.chart-data {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 10rpx;
}

.chart-point {
  text-align: center;
  padding: 10rpx;
}

.point-date {
  display: block;
  font-size: 20rpx;
  color: var(--text-secondary);
}

.point-value {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: var(--text-primary);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  background-color: var(--text-inverse);
  padding: 40rpx;
  border-radius: 20rpx;
}

.loading-spinner {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 刷新指示器 */
.refresh-indicator {
  position: fixed;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary);
  color: var(--text-inverse);
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  z-index: 9998;
}

/* 详情模态框 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background-color: var(--text-inverse);
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: var(--text-secondary);
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-content {
  text-align: center;
  padding: 40rpx;
}

.detail-placeholder {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.modal-footer {
  display: flex;
  justify-content: center;
  padding: 20rpx 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  padding: 15rpx 40rpx;
  background-color: var(--primary);
  color: var(--text-inverse);
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}