<!-- pages/oa/purchase/purchase.wxml -->
<view class="purchase-container">
  


  <!-- 快捷操作 -->
  <view class="quick-actions-section">
    <view class="section-title">
      <text>快捷操作</text>
    </view>
    <view class="quick-actions-grid">
      <block wx:for="{{quickActions}}" wx:key="id">
        <view class="quick-action-item" 
              bindtap="onQuickActionTap" 
              data-url="{{item.url}}"
              style="background: linear-gradient(135deg, {{item.color}}15 0%, {{item.color}}05 100%); border-left: 4rpx solid {{item.color}};">
          <view class="action-icon">
            <text wx:if="{{item.iconText}}" class="icon-text">{{item.iconText}}</text>
            <image wx:else src="{{item.icon}}" mode="aspectFit"></image>
          </view>
          <view class="action-content">
            <text class="action-title">{{item.title}}</text>
            <text class="action-description">{{item.description}}</text>
          </view>
          <view class="action-arrow">
            <text>></text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="statistics-section">
    <view class="section-title">
      <text>统计概览</text>
    </view>
    <view class="statistics-grid">
      <view class="stat-item" bindtap="onStatTap" data-type="pending">
        <view class="stat-number pending">{{statistics.pending}}</view>
        <text class="stat-label">待审批</text>
      </view>
      <view class="stat-item" bindtap="onStatTap" data-type="approved">
        <view class="stat-number approved">{{statistics.approved}}</view>
        <text class="stat-label">已批准</text>
      </view>
      <view class="stat-item" bindtap="onStatTap" data-type="rejected">
        <view class="stat-number rejected">{{statistics.rejected}}</view>
        <text class="stat-label">已拒绝</text>
      </view>
      <view class="stat-item" bindtap="onStatTap" data-type="total">
        <view class="stat-number total">{{statistics.total}}</view>
        <text class="stat-label">总申请</text>
      </view>
    </view>
  </view>

  <!-- 最近记录 -->
  <view class="recent-section">
    <view class="section-header">
      <text class="section-title">最近记录</text>
      <text class="view-all-btn" bindtap="onViewAllPurchases">查看全部</text>
    </view>
    
    <view class="recent-list">
      <block wx:for="{{recentPurchases}}" wx:key="id">
        <view class="purchase-item" bindtap="onPurchaseItemTap" data-id="{{item.id}}">
          <view class="purchase-header">
            <view class="purchase-category">{{item.category}}</view>
            <view class="purchase-status" style="color: {{item.statusColor}};">{{item.statusText}}</view>
            <view wx:if="{{item.urgency === 'urgent'}}" class="urgency-badge">急</view>
          </view>
          
          <view class="purchase-content">
            <text class="purchase-items">{{item.items}}</text>
            <view class="purchase-amount">￥{{item.amount}}</view>
          </view>
          
          <view class="purchase-footer">
            <text class="submit-time">提交时间：{{item.submitTime}}</text>
            <view class="arrow-icon">></view>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 空状态 -->
    <view wx:if="{{recentPurchases.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无采购记录</text>
      <button class="empty-btn" bindtap="onQuickActionTap" data-url="/pages/oa/purchase/apply/apply">
        立即申请
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

</view>