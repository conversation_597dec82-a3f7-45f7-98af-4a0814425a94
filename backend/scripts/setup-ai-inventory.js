#!/usr/bin/env node

const mysql = require("mysql2/promise");
const fs = require("fs");
const path = require("path");

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USERNAME || "zhihuiyange",
  password: process.env.DB_PASSWORD || "zhihuiyange123",
  database: process.env.DB_NAME || "zhihuiyange_local",
  charset: "utf8mb4",
};

async function setupAIInventoryTable() {
  let connection;

  try {
    console.log("连接数据库...");
    connection = await mysql.createConnection(dbConfig);

    console.log("读取SQL文件...");
    const sqlFile = path.join(__dirname, "create-ai-inventory-table.sql");
    const sqlContent = fs.readFileSync(sqlFile, "utf8");

    // 分割SQL语句
    const statements = sqlContent
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0);

    console.log("执行SQL语句...");
    for (const statement of statements) {
      if (statement.trim()) {
        await connection.execute(statement);
        console.log("✅ 执行成功:", statement.substring(0, 50) + "...");
      }
    }

    console.log("🎉 AI盘点表创建完成！");

    // 验证表是否创建成功
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'ai_inventory_records'",
    );

    if (tables.length > 0) {
      console.log("✅ 表创建验证成功");

      // 查看表结构
      const [columns] = await connection.execute(
        "DESCRIBE ai_inventory_records",
      );

      console.log("\n📋 表结构:");
      columns.forEach((col) => {
        console.log(
          `  ${col.Field}: ${col.Type} ${col.Null === "NO" ? "NOT NULL" : ""} ${col.Key ? `(${col.Key})` : ""}`,
        );
      });

      // 查看示例数据
      const [records] = await connection.execute(
        "SELECT COUNT(*) as count FROM ai_inventory_records",
      );

      console.log(`\n📊 示例数据: ${records[0].count} 条记录`);
    } else {
      console.log("❌ 表创建验证失败");
    }
  } catch (error) {
    console.error("❌ 设置失败:", error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log("数据库连接已关闭");
    }
  }
}

// 执行设置
if (require.main === module) {
  setupAIInventoryTable();
}

module.exports = setupAIInventoryTable;
