// pages/ai-stats/ai-stats.js
// AI使用统计页面

const { backendAIConfigManager } = require('../../utils/backend-ai-config.js');

Page({
  data: {
    // 时间范围选择
    timeRanges: [
      { id: 'today', name: '今天' },
      { id: 'week', name: '本周' },
      { id: 'month', name: '本月' },
      { id: 'quarter', name: '本季度' },
      { id: 'year', name: '本年' }
    ],
    activeTimeRange: 'month',

    // 统计概览
    statsOverview: {
      totalRequests: 0,
      successfulRequests: 0,
      totalTokens: 0,
      totalCost: 0,
      averageResponseTime: 0,
      topProvider: ''
    },

    // 详细统计数据
    detailedStats: {
      byProvider: [],
      byScenario: [],
      byDate: [],
      byModel: []
    },

    // 图表数据
    chartData: {
      usage: [],
      cost: [],
      success: []
    },

    // 服务提供商列表
    providers: [],

    // 加载状态
    loading: true,
    refreshing: false,

    // 显示详情模态框
    showDetailModal: false,
    detailData: {}
  },

  onLoad: function (options) {
    this.loadAIStats();
    this.loadProviders();
  },

  onShow: function () {
    // 页面显示时刷新数据
    this.refreshData();
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshData();
  },

  // 时间范围切换
  onTimeRangeChange: function (e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      activeTimeRange: range
    });
    this.loadAIStats();
  },

  // 刷新数据
  refreshData: function () {
    this.setData({ refreshing: true });
    this.loadAIStats();
    this.loadProviders();
  },

  // 加载AI使用统计
  loadAIStats: async function () {
    try {
      this.setData({ loading: true });
      
      // 模拟API调用获取统计数据
      const stats = await this.fetchAIUsageStats();
      
      // 计算格式化的显示数据
      const formattedStats = this.calculateDisplayStats(stats.overview, stats.detailed);
      
      // 将格式化数据合并到详细统计中
      const enhancedDetailed = {
        ...stats.detailed,
        byProvider: formattedStats.providers,
        byScenario: formattedStats.scenarios,
        byModel: formattedStats.models
      };
      
      this.setData({
        statsOverview: stats.overview,
        detailedStats: enhancedDetailed,
        chartData: stats.charts,
        displayStats: formattedStats,
        loading: false,
        refreshing: false
      });

      // 停止下拉刷新
      wx.stopPullDownRefresh();
      
    } catch (error) {
    try { const logger = require('../../utils/logger.js'); logger.error && logger.error('加载AI统计失败', error); } catch(_) {}
      this.setData({ 
        loading: false,
        refreshing: false 
      });
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      
      wx.stopPullDownRefresh();
    }
  },

  // 加载服务提供商列表
  loadProviders: async function () {
    try {
      const providers = await backendAIConfigManager.getAvailableProviders();
      this.setData({ providers: providers || [] });
    } catch (error) {
    try { const logger = require('../../utils/logger.js'); logger.error && logger.error('加载提供商列表失败', error); } catch(_) {}
    }
  },

  // 模拟获取AI使用统计数据
  fetchAIUsageStats: function () {
    return new Promise((resolve) => {
      setTimeout(() => {
        const timeRange = this.data.activeTimeRange;
        const mockData = this.generateMockStatsData(timeRange);
        resolve(mockData);
      }, 1000);
    });
  },

  // 计算显示用的格式化数据
  calculateDisplayStats: function (overview, detailed) {
    const successRate = overview.totalRequests > 0 
      ? ((overview.successfulRequests / overview.totalRequests) * 100).toFixed(1)
      : '0.0';
    
    const providerStats = detailed.byProvider.map(item => ({
      ...item,
      successRateText: (item.success * 100).toFixed(1),
      percentageText: overview.totalRequests > 0 
        ? ((item.requests / overview.totalRequests) * 100).toFixed(1)
        : '0.0'
    }));
    
    const scenarioStats = detailed.byScenario.map(item => ({
      ...item,
      percentageText: overview.totalRequests > 0 
        ? ((item.requests / overview.totalRequests) * 100).toFixed(1)
        : '0.0'
    }));
    
    const modelStats = detailed.byModel.map(item => ({
      ...item,
      percentageText: overview.totalRequests > 0 
        ? ((item.requests / overview.totalRequests) * 100).toFixed(1)
        : '0.0'
    }));
    
    return {
      successRate: successRate,
      providers: providerStats,
      scenarios: scenarioStats,
      models: modelStats
    };
  },

  // 生成模拟统计数据
  generateMockStatsData: function (timeRange) {
    const now = new Date();
    const ranges = {
      today: 1,
      week: 7,
      month: 30,
      quarter: 90,
      year: 365
    };
    
    const days = ranges[timeRange] || 30;
    const baseRequests = Math.floor(Math.random() * 100) + 50;
    
    return {
      overview: {
        totalRequests: baseRequests * days,
        successfulRequests: Math.floor(baseRequests * days * 0.95),
        totalTokens: Math.floor(baseRequests * days * 1500),
        totalCost: (baseRequests * days * 0.02).toFixed(2),
        averageResponseTime: (Math.random() * 2000 + 500).toFixed(0),
        topProvider: 'OpenAI'
      },
      detailed: {
        byProvider: [
          { name: 'OpenAI', requests: Math.floor(baseRequests * 0.6), tokens: Math.floor(baseRequests * 900), cost: (baseRequests * 0.012).toFixed(2), success: 0.98 },
          { name: 'Claude', requests: Math.floor(baseRequests * 0.25), tokens: Math.floor(baseRequests * 375), cost: (baseRequests * 0.005).toFixed(2), success: 0.96 },
          { name: 'Gemini', requests: Math.floor(baseRequests * 0.15), tokens: Math.floor(baseRequests * 225), cost: (baseRequests * 0.003).toFixed(2), success: 0.94 }
        ],
        byScenario: [
          { name: '财务分析', requests: Math.floor(baseRequests * 0.4), tokens: Math.floor(baseRequests * 600), avgTime: 1200 },
          { name: '健康诊断', requests: Math.floor(baseRequests * 0.3), tokens: Math.floor(baseRequests * 450), avgTime: 1500 },
          { name: '智能盘点', requests: Math.floor(baseRequests * 0.2), tokens: Math.floor(baseRequests * 300), avgTime: 2000 },
          { name: '其他分析', requests: Math.floor(baseRequests * 0.1), tokens: Math.floor(baseRequests * 150), avgTime: 800 }
        ],
        byDate: this.generateDateStats(days, baseRequests),
        byModel: [
          { name: 'GPT-4', requests: Math.floor(baseRequests * 0.4), cost: (baseRequests * 0.008).toFixed(2) },
          { name: 'GPT-3.5', requests: Math.floor(baseRequests * 0.2), cost: (baseRequests * 0.004).toFixed(2) },
          { name: 'Claude-3', requests: Math.floor(baseRequests * 0.25), cost: (baseRequests * 0.005).toFixed(2) },
          { name: 'Gemini-Pro', requests: Math.floor(baseRequests * 0.15), cost: (baseRequests * 0.003).toFixed(2) }
        ]
      },
      charts: {
        usage: this.generateChartData(days, 'usage'),
        cost: this.generateChartData(days, 'cost'),
        success: this.generateChartData(days, 'success')
      }
    };
  },

  // 生成日期统计数据
  generateDateStats: function (days, baseRequests) {
    const stats = [];
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      const requests = Math.floor(baseRequests * (0.7 + Math.random() * 0.6));
      const successRate = 0.92 + Math.random() * 0.06;
      
      stats.push({
        date: date.toISOString().split('T')[0],
        requests: requests,
        successful: Math.floor(requests * successRate),
        tokens: Math.floor(requests * 1500 * (0.8 + Math.random() * 0.4)),
        cost: (requests * 0.02 * (0.8 + Math.random() * 0.4)).toFixed(2),
        avgResponseTime: Math.floor(800 + Math.random() * 1200)
      });
    }
    
    return stats;
  },

  // 生成图表数据
  generateChartData: function (days, type) {
    const data = [];
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      let value;
      switch (type) {
        case 'usage':
          value = Math.floor(50 + Math.random() * 100);
          break;
        case 'cost':
          value = (Math.random() * 2 + 0.5).toFixed(2);
          break;
        case 'success':
          value = (0.90 + Math.random() * 0.08).toFixed(3);
          break;
        default:
          value = Math.floor(Math.random() * 100);
      }
      
      data.push({
        date: `${date.getMonth() + 1}/${date.getDate()}`,
        value: value
      });
    }
    
    return data;
  },

  // 查看详情
  onViewDetail: function (e) {
    const { type, data } = e.currentTarget.dataset;
    
    this.setData({
      showDetailModal: true,
      detailData: {
        type: type,
        title: this.getDetailTitle(type),
        content: data
      }
    });
  },

  // 关闭详情模态框
  onCloseDetailModal: function () {
    this.setData({
      showDetailModal: false,
      detailData: {}
    });
  },

  // 获取详情标题
  getDetailTitle: function (type) {
    const titles = {
      provider: '服务提供商详情',
      scenario: '使用场景详情',
      model: '模型使用详情',
      date: '日期统计详情'
    };
    return titles[type] || '详情';
  },

  // 导出统计报告
  onExportReport: function () {
    wx.showActionSheet({
      itemList: ['导出Excel报告', '导出PDF报告', '发送邮件报告'],
      success: (res) => {
        const exportTypes = ['excel', 'pdf', 'email'];
        const selectedType = exportTypes[res.tapIndex];
        this.executeExport(selectedType);
      }
    });
  },

  // 执行导出
  executeExport: function (type) {
    wx.showLoading({
      title: '生成报告中...'
    });

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      
      const messages = {
        excel: 'Excel报告已生成完成',
        pdf: 'PDF报告已生成完成',
        email: '邮件报告已发送'
      };
      
      wx.showModal({
        title: '导出成功',
        content: messages[type] || '报告已生成',
        showCancel: false
      });
    }, 2000);
  },

  // 清空统计数据
  onClearStats: function () {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有AI使用统计数据吗？此操作不可撤销。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清空中...'
          });
          
          // 模拟清空过程
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '清空成功',
              icon: 'success'
            });
            
            // 重新加载数据
            this.loadAIStats();
          }, 1000);
        }
      }
    });
  },

  // 分享统计报告
  onShareStats: function () {
    const overview = this.data.statsOverview;
    return {
      title: `AI使用统计报告`,
      desc: `总请求${overview.totalRequests}次，成功率${Math.round(overview.successfulRequests/overview.totalRequests*100)}%`,
      path: '/pages/ai-stats/ai-stats'
    };
  }
});