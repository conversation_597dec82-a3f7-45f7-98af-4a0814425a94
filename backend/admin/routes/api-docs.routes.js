/**
 * API文档路由
 * 提供Swagger UI界面和API文档相关功能
 */

const express = require("express");
const path = require("path");
const swaggerUi = require("swagger-ui-express");
const { requireAuth, requireRole } = require("../middleware/common");
const {
  renderPage,
  successResponse,
  errorResponse,
  asyncHandler,
} = require("../utils/response-helper");
const { swaggerSpec } = require("../../docs/openapi-config");

const router = express.Router();

// 应用认证中间件（文档访问需要登录）
router.use(requireAuth);

/**
 * API文档首页
 */
router.get(
  "/",
  asyncHandler(async (req, res) => {
    renderPage(res, "api-docs/index", {
      title: "API文档 - 智慧养鹅管理系统",
      user: req.session.user,
      currentPage: "api-docs",
      data: {
        totalEndpoints: countEndpoints(swaggerSpec),
        apiVersion: swaggerSpec.info.version,
        lastUpdated: new Date().toISOString(),
      },
    });
  }),
);

/**
 * Swagger UI文档页面
 */
router.use("/swagger", swaggerUi.serve);
router.get(
  "/swagger",
  swaggerUi.setup(swaggerSpec, {
    customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info { margin: 20px 0 }
    .swagger-ui .info .title { color: #1890ff }
    .swagger-ui .scheme-container { 
      background: #f8f9fa; 
      padding: 10px; 
      border-radius: 4px; 
      margin: 10px 0; 
    }
  `,
    customSiteTitle: "智慧养鹅API文档",
    customfavIcon: "/favicon.ico",
    swaggerOptions: {
      docExpansion: "none",
      tagsSorter: "alpha",
      operationsSorter: "alpha",
      defaultModelsExpandDepth: 2,
      defaultModelExpandDepth: 2,
      displayRequestDuration: true,
      filter: true,
      tryItOutEnabled: true,
    },
  }),
);

/**
 * 获取OpenAPI规范JSON
 */
router.get(
  "/openapi.json",
  asyncHandler(async (req, res) => {
    res.json(swaggerSpec);
  }),
);

/**
 * 获取OpenAPI规范YAML
 */
router.get(
  "/openapi.yaml",
  asyncHandler(async (req, res) => {
    const yaml = require("js-yaml");
    const yamlStr = yaml.dump(swaggerSpec);
    res.set("Content-Type", "application/x-yaml");
    res.send(yamlStr);
  }),
);

/**
 * API统计信息
 */
router.get(
  "/api/stats",
  asyncHandler(async (req, res) => {
    const stats = {
      totalEndpoints: countEndpoints(swaggerSpec),
      endpointsByTag: getEndpointsByTag(swaggerSpec),
      endpointsByMethod: getEndpointsByMethod(swaggerSpec),
      schemas: Object.keys(swaggerSpec.components?.schemas || {}).length,
      securitySchemes: Object.keys(
        swaggerSpec.components?.securitySchemes || {},
      ).length,
      apiVersion: swaggerSpec.info.version,
      openApiVersion: swaggerSpec.openapi,
      lastUpdated: new Date().toISOString(),
    };

    successResponse(res, stats, "获取API统计信息成功");
  }),
);

/**
 * 生成Postman集合
 */
router.get(
  "/api/postman-collection",
  requireRole(["admin", "manager"]),
  asyncHandler(async (req, res) => {
    try {
      const postmanCollection = generatePostmanCollection(swaggerSpec);

      res.set({
        "Content-Type": "application/json",
        "Content-Disposition":
          'attachment; filename="zhihuiyange-api-collection.json"',
      });

      res.json(postmanCollection);
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("生成Postman集合失败:", error); } catch(_) {}

      errorResponse(res, "生成Postman集合失败", 500);
    }
  }),
);

/**
 * 验证API规范
 */
router.post(
  "/api/validate",
  requireRole(["admin"]),
  asyncHandler(async (req, res) => {
    try {
      // 这里可以添加API规范验证逻辑
      const validation = {
        valid: true,
        errors: [],
        warnings: [],
        info: {
          endpoints: countEndpoints(swaggerSpec),
          schemas: Object.keys(swaggerSpec.components?.schemas || {}).length,
          tags: swaggerSpec.tags?.length || 0,
        },
      };

      successResponse(res, validation, "API规范验证完成");
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("API规范验证失败:", error); } catch(_) {}

      errorResponse(res, "API规范验证失败", 500);
    }
  }),
);

/**
 * API端点搜索
 */
router.get(
  "/api/search",
  asyncHandler(async (req, res) => {
    const { q, tag, method } = req.query;

    if (!q || q.trim().length === 0) {
      return errorResponse(res, "搜索关键词不能为空", 400);
    }

    try {
      const results = searchEndpoints(swaggerSpec, q.trim(), { tag, method });
      successResponse(res, results, "搜索完成");
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("搜索API端点失败:", error); } catch(_) {}

      errorResponse(res, "搜索失败", 500);
    }
  }),
);

/**
 * 获取API变更历史（模拟数据）
 */
router.get(
  "/api/changelog",
  asyncHandler(async (req, res) => {
    const changelog = [
      {
        version: "2.1.0",
        date: "2024-01-20",
        changes: [
          { type: "added", description: "新增AI诊断API端点" },
          { type: "updated", description: "优化鹅群管理API响应格式" },
          { type: "fixed", description: "修复健康记录查询分页问题" },
        ],
      },
      {
        version: "2.0.0",
        date: "2024-01-15",
        changes: [
          { type: "added", description: "引入express-zod-api框架" },
          { type: "added", description: "新增类型安全的API端点" },
          { type: "breaking", description: "更新API响应格式标准" },
        ],
      },
      {
        version: "1.5.0",
        date: "2024-01-10",
        changes: [
          { type: "added", description: "新增生产统计API" },
          { type: "updated", description: "完善认证中间件" },
          { type: "deprecated", description: "标记部分v1端点为废弃" },
        ],
      },
    ];

    successResponse(res, changelog, "获取API变更历史成功");
  }),
);

// 工具函数

/**
 * 统计API端点数量
 */
function countEndpoints(spec) {
  let count = 0;
  if (spec.paths) {
    Object.values(spec.paths).forEach((pathItem) => {
      count += Object.keys(pathItem).filter((key) =>
        ["get", "post", "put", "patch", "delete", "options", "head"].includes(
          key,
        ),
      ).length;
    });
  }
  return count;
}

/**
 * 按标签分组统计端点
 */
function getEndpointsByTag(spec) {
  const tagStats = {};

  if (spec.paths) {
    Object.values(spec.paths).forEach((pathItem) => {
      Object.values(pathItem).forEach((operation) => {
        if (operation.tags) {
          operation.tags.forEach((tag) => {
            tagStats[tag] = (tagStats[tag] || 0) + 1;
          });
        }
      });
    });
  }

  return tagStats;
}

/**
 * 按HTTP方法统计端点
 */
function getEndpointsByMethod(spec) {
  const methodStats = {};

  if (spec.paths) {
    Object.values(spec.paths).forEach((pathItem) => {
      Object.keys(pathItem).forEach((method) => {
        if (
          ["get", "post", "put", "patch", "delete", "options", "head"].includes(
            method,
          )
        ) {
          methodStats[method.toUpperCase()] =
            (methodStats[method.toUpperCase()] || 0) + 1;
        }
      });
    });
  }

  return methodStats;
}

/**
 * 搜索API端点
 */
function searchEndpoints(spec, query, filters = {}) {
  const results = [];
  const queryLower = query.toLowerCase();

  if (spec.paths) {
    Object.entries(spec.paths).forEach(([path, pathItem]) => {
      Object.entries(pathItem).forEach(([method, operation]) => {
        if (
          ![
            "get",
            "post",
            "put",
            "patch",
            "delete",
            "options",
            "head",
          ].includes(method)
        ) {
          return;
        }

        // 标签过滤
        if (
          filters.tag &&
          (!operation.tags || !operation.tags.includes(filters.tag))
        ) {
          return;
        }

        // HTTP方法过滤
        if (
          filters.method &&
          method.toLowerCase() !== filters.method.toLowerCase()
        ) {
          return;
        }

        // 搜索匹配
        const searchText = [
          path,
          operation.summary || "",
          operation.description || "",
          ...(operation.tags || []),
        ]
          .join(" ")
          .toLowerCase();

        if (searchText.includes(queryLower)) {
          results.push({
            path,
            method: method.toUpperCase(),
            summary: operation.summary,
            description: operation.description,
            tags: operation.tags || [],
            operationId: operation.operationId,
          });
        }
      });
    });
  }

  return results;
}

/**
 * 生成Postman集合
 */
function generatePostmanCollection(spec) {
  const collection = {
    info: {
      name: spec.info.title,
      description: spec.info.description,
      version: spec.info.version,
      schema:
        "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
    },
    auth: {
      type: "bearer",
      bearer: [
        {
          key: "token",
          value: "{{access_token}}",
          type: "string",
        },
      ],
    },
    variable: [
      {
        key: "base_url",
        value: spec.servers?.[0]?.url || "http://localhost:3000",
        type: "string",
      },
      {
        key: "access_token",
        value: "",
        type: "string",
      },
    ],
    item: [],
  };

  // 按标签分组
  const tagGroups = {};

  if (spec.paths) {
    Object.entries(spec.paths).forEach(([path, pathItem]) => {
      Object.entries(pathItem).forEach(([method, operation]) => {
        if (!["get", "post", "put", "patch", "delete"].includes(method)) {
          return;
        }

        const tags = operation.tags || ["未分类"];
        tags.forEach((tag) => {
          if (!tagGroups[tag]) {
            tagGroups[tag] = [];
          }

          const request = {
            name: operation.summary || `${method.toUpperCase()} ${path}`,
            request: {
              method: method.toUpperCase(),
              header: [],
              url: {
                raw: `{{base_url}}${path}`,
                host: ["{{base_url}}"],
                path: path.split("/").filter((p) => p),
              },
            },
          };

          // 添加请求体示例
          if (operation.requestBody?.content?.["application/json"]?.schema) {
            request.request.body = {
              mode: "raw",
              raw: JSON.stringify(
                generateExampleFromSchema(
                  operation.requestBody.content["application/json"].schema,
                ),
                null,
                2,
              ),
              options: {
                raw: {
                  language: "json",
                },
              },
            };
            request.request.header.push({
              key: "Content-Type",
              value: "application/json",
            });
          }

          tagGroups[tag].push(request);
        });
      });
    });
  }

  // 转换为Postman文件夹结构
  collection.item = Object.entries(tagGroups).map(([tag, requests]) => ({
    name: tag,
    item: requests,
  }));

  return collection;
}

/**
 * 从Schema生成示例数据
 */
function generateExampleFromSchema(schema) {
  if (schema.example) {
    return schema.example;
  }

  if (schema.type === "object" && schema.properties) {
    const example = {};
    Object.entries(schema.properties).forEach(([key, prop]) => {
      if (prop.example !== undefined) {
        example[key] = prop.example;
      } else if (prop.type === "string") {
        example[key] = prop.enum ? prop.enum[0] : "string";
      } else if (prop.type === "integer") {
        example[key] = prop.minimum || 1;
      } else if (prop.type === "number") {
        example[key] = prop.minimum || 1.0;
      } else if (prop.type === "boolean") {
        example[key] = false;
      }
    });
    return example;
  }

  return {};
}

module.exports = router;
