// pages/payment/payment.js
const app = getApp();
const logger = require('../../utils/logger.js');

Page({
  data: {
    orderId: '',
    orderInfo: null,
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: '/images/icons/wechat-pay.png',
        desc: '安全便捷的微信支付',
        selected: true
      }
    ],
    selectedPayment: 'wechat',
    loading: false,
    paymentLoading: false
  },

  onLoad(options) {
    const orderId = options.orderId;
    if (orderId) {
      this.setData({
        orderId: orderId
      });
      this.loadOrderInfo();
    } else {
      wx.showToast({
        title: '订单信息错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单信息
  loadOrderInfo() {
    this.setData({
      loading: true
    });

    // 模拟API调用
    setTimeout(() => {
      const orderInfo = {
        id: this.data.orderId,
        orderNumber: 'GO202401150001',
        createTime: '2024-01-15 10:30',
        totalPrice: '299.98',
        discountAmount: '0.00',
        shippingFee: '0.00',
        actualAmount: '299.98',
        goods: [
          {
            id: '1',
            name: '优质鹅饲料',
            price: '99.99',
            quantity: 1,
            image: '/images/icons/goods1.png'
          },
          {
            id: '2',
            name: '疫苗套装',
            price: '199.99',
            quantity: 1,
            image: '/images/icons/goods2.png'
          }
        ],
        address: {
          name: '张三',
          phone: '138****8888',
          detail: '北京市朝阳区xxx街道xxx号'
        }
      };

      this.setData({
        orderInfo: orderInfo,
        loading: false
      });
    }, 500);
  },

  // 支付方式固定为微信支付，无需选择
  // onSelectPayment 方法已移除，因为只支持微信支付

  // 立即支付
  onPay() {
    if (!this.data.orderInfo) {
      wx.showToast({
        title: '订单信息加载中',
        icon: 'none'
      });
      return;
    }

    this.setData({
      paymentLoading: true
    });

    // 调用微信支付
    this.wechatPay();
  },

  // 微信支付
  wechatPay() {
    wx.showLoading({
      title: '正在调起支付...'
    });

    // 模拟获取支付参数的API调用
    setTimeout(() => {
      // 在实际项目中，这里应该调用后端API获取支付参数
      // const paymentParams = await this.getPaymentParams(this.data.orderId);

      wx.hideLoading();

      // 调用微信支付API
      wx.requestPayment({
        timeStamp: String(Date.now()),
        nonceStr: this.generateNonceStr(),
        package: 'prepay_id=mock_prepay_id_' + Date.now(),
        signType: 'RSA',
        paySign: 'mock_pay_sign_' + Date.now(),
        success: (res) => {
          logger.info && logger.info('支付成功', res);
          this.paymentSuccess();
        },
        fail: (err) => {
          logger.warn && logger.warn('支付失败', err);
          if (err.errMsg === 'requestPayment:fail cancel') {
            this.paymentFailed('支付已取消');
          } else {
            this.paymentFailed('支付失败，请重试');
          }
        },
        complete: () => {
          this.setData({
            paymentLoading: false
          });
        }
      });
    }, 1000);
  },

  // 生成随机字符串
  generateNonceStr() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // 支付成功
  paymentSuccess() {
    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000
    });

    // 跳转到支付成功页面
    setTimeout(() => {
      wx.redirectTo({
        url: `/pages/shop/order-success?orderId=${this.data.orderId}&type=payment`
      });
    }, 2000);
  },

  // 支付失败
  paymentFailed(message) {
    wx.showModal({
      title: '支付失败',
      content: message || '支付过程中出现错误，请重试',
      showCancel: false,
      confirmText: '确定'
    });

    this.setData({
      paymentLoading: false
    });
  },

  // 返回订单列表
  onBackToOrders() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  }
});
