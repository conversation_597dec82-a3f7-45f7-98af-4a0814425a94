// backend/utils/error-handler.js
// 统一错误处理工具

/**
 * 自定义应用错误类
 */
class AppError extends Error {
  constructor(message, statusCode, errorCode = null, details = null) {
    super(message);

    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 错误类型枚举
 */
const ERROR_TYPES = {
  // 认证相关错误
  AUTH_INVALID_CREDENTIALS: "AUTH_INVALID_CREDENTIALS",
  AUTH_TOKEN_EXPIRED: "AUTH_TOKEN_EXPIRED",
  AUTH_TOKEN_INVALID: "AUTH_TOKEN_INVALID",
  AUTH_INSUFFICIENT_PERMISSIONS: "AUTH_INSUFFICIENT_PERMISSIONS",

  // 验证相关错误
  VALIDATION_FAILED: "VALIDATION_FAILED",
  VALIDATION_REQUIRED_FIELD: "VALIDATION_REQUIRED_FIELD",
  VALIDATION_INVALID_FORMAT: "VALIDATION_INVALID_FORMAT",

  // 数据库相关错误
  DB_CONNECTION_FAILED: "DB_CONNECTION_FAILED",
  DB_QUERY_FAILED: "DB_QUERY_FAILED",
  DB_DUPLICATE_ENTRY: "DB_DUPLICATE_ENTRY",
  DB_RECORD_NOT_FOUND: "DB_RECORD_NOT_FOUND",

  // 业务逻辑错误
  BUSINESS_LOGIC_ERROR: "BUSINESS_LOGIC_ERROR",
  RESOURCE_NOT_FOUND: "RESOURCE_NOT_FOUND",
  RESOURCE_ALREADY_EXISTS: "RESOURCE_ALREADY_EXISTS",

  // 外部服务错误
  EXTERNAL_SERVICE_ERROR: "EXTERNAL_SERVICE_ERROR",
  AI_SERVICE_ERROR: "AI_SERVICE_ERROR",

  // 系统错误
  INTERNAL_SERVER_ERROR: "INTERNAL_SERVER_ERROR",
  SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE",
};

/**
 * 错误消息映射
 */
const ERROR_MESSAGES = {
  [ERROR_TYPES.AUTH_INVALID_CREDENTIALS]: "用户名或密码错误",
  [ERROR_TYPES.AUTH_TOKEN_EXPIRED]: "登录已过期，请重新登录",
  [ERROR_TYPES.AUTH_TOKEN_INVALID]: "无效的访问令牌",
  [ERROR_TYPES.AUTH_INSUFFICIENT_PERMISSIONS]: "权限不足",

  [ERROR_TYPES.VALIDATION_FAILED]: "数据验证失败",
  [ERROR_TYPES.VALIDATION_REQUIRED_FIELD]: "必填字段不能为空",
  [ERROR_TYPES.VALIDATION_INVALID_FORMAT]: "数据格式不正确",

  [ERROR_TYPES.DB_CONNECTION_FAILED]: "数据库连接失败",
  [ERROR_TYPES.DB_QUERY_FAILED]: "数据库查询失败",
  [ERROR_TYPES.DB_DUPLICATE_ENTRY]: "数据已存在",
  [ERROR_TYPES.DB_RECORD_NOT_FOUND]: "记录不存在",

  [ERROR_TYPES.BUSINESS_LOGIC_ERROR]: "业务逻辑错误",
  [ERROR_TYPES.RESOURCE_NOT_FOUND]: "资源不存在",
  [ERROR_TYPES.RESOURCE_ALREADY_EXISTS]: "资源已存在",

  [ERROR_TYPES.EXTERNAL_SERVICE_ERROR]: "外部服务错误",
  [ERROR_TYPES.AI_SERVICE_ERROR]: "AI服务错误",

  [ERROR_TYPES.INTERNAL_SERVER_ERROR]: "服务器内部错误",
  [ERROR_TYPES.SERVICE_UNAVAILABLE]: "服务暂时不可用",
};

/**
 * 错误工厂函数
 */
const createError = {
  // 认证错误
  invalidCredentials: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.AUTH_INVALID_CREDENTIALS],
      401,
      ERROR_TYPES.AUTH_INVALID_CREDENTIALS,
      details,
    ),

  tokenExpired: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.AUTH_TOKEN_EXPIRED],
      401,
      ERROR_TYPES.AUTH_TOKEN_EXPIRED,
      details,
    ),

  tokenInvalid: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.AUTH_TOKEN_INVALID],
      401,
      ERROR_TYPES.AUTH_TOKEN_INVALID,
      details,
    ),

  insufficientPermissions: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.AUTH_INSUFFICIENT_PERMISSIONS],
      403,
      ERROR_TYPES.AUTH_INSUFFICIENT_PERMISSIONS,
      details,
    ),

  // 验证错误
  validationFailed: (message, details = null) =>
    new AppError(
      message || ERROR_MESSAGES[ERROR_TYPES.VALIDATION_FAILED],
      400,
      ERROR_TYPES.VALIDATION_FAILED,
      details,
    ),

  requiredField: (fieldName, details = null) =>
    new AppError(
      `${fieldName}为必填项`,
      400,
      ERROR_TYPES.VALIDATION_REQUIRED_FIELD,
      details,
    ),

  invalidFormat: (fieldName, details = null) =>
    new AppError(
      `${fieldName}格式不正确`,
      400,
      ERROR_TYPES.VALIDATION_INVALID_FORMAT,
      details,
    ),

  // 数据库错误
  dbConnectionFailed: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.DB_CONNECTION_FAILED],
      500,
      ERROR_TYPES.DB_CONNECTION_FAILED,
      details,
    ),

  dbQueryFailed: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.DB_QUERY_FAILED],
      500,
      ERROR_TYPES.DB_QUERY_FAILED,
      details,
    ),

  duplicateEntry: (resource, details = null) =>
    new AppError(
      `${resource}已存在`,
      409,
      ERROR_TYPES.DB_DUPLICATE_ENTRY,
      details,
    ),

  recordNotFound: (resource, details = null) =>
    new AppError(
      `${resource}不存在`,
      404,
      ERROR_TYPES.DB_RECORD_NOT_FOUND,
      details,
    ),

  // 业务逻辑错误
  businessLogicError: (message, details = null) =>
    new AppError(message, 400, ERROR_TYPES.BUSINESS_LOGIC_ERROR, details),

  resourceNotFound: (resource, details = null) =>
    new AppError(
      `${resource}不存在`,
      404,
      ERROR_TYPES.RESOURCE_NOT_FOUND,
      details,
    ),

  resourceAlreadyExists: (resource, details = null) =>
    new AppError(
      `${resource}已存在`,
      409,
      ERROR_TYPES.RESOURCE_ALREADY_EXISTS,
      details,
    ),

  // 外部服务错误
  externalServiceError: (service, details = null) =>
    new AppError(
      `${service}服务错误`,
      502,
      ERROR_TYPES.EXTERNAL_SERVICE_ERROR,
      details,
    ),

  aiServiceError: (message, details = null) =>
    new AppError(
      message || ERROR_MESSAGES[ERROR_TYPES.AI_SERVICE_ERROR],
      502,
      ERROR_TYPES.AI_SERVICE_ERROR,
      details,
    ),

  // 系统错误
  internalServerError: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.INTERNAL_SERVER_ERROR],
      500,
      ERROR_TYPES.INTERNAL_SERVER_ERROR,
      details,
    ),

  serviceUnavailable: (details = null) =>
    new AppError(
      ERROR_MESSAGES[ERROR_TYPES.SERVICE_UNAVAILABLE],
      503,
      ERROR_TYPES.SERVICE_UNAVAILABLE,
      details,
    ),
};

/**
 * 异步错误处理装饰器
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 全局错误处理中间件
 * @param {Error} err - 错误对象
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
const globalErrorHandler = (err, req, res, next) => {
  // 设置默认错误信息
  let error = { ...err };
  error.message = err.message;

  // 记录错误日志
  console.error("Error:", {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get("User-Agent"),
    timestamp: new Date().toISOString(),
  });

  // 处理特定类型的错误
  if (err.name === "SequelizeValidationError") {
    const message = "数据验证失败";
    const details = err.errors.map((e) => ({
      field: e.path,
      message: e.message,
      value: e.value,
    }));
    error = createError.validationFailed(message, details);
  }

  if (err.name === "SequelizeUniqueConstraintError") {
    const message = "数据已存在";
    error = createError.duplicateEntry(message);
  }

  if (err.name === "SequelizeDatabaseError") {
    error = createError.dbQueryFailed(err.message);
  }

  if (err.name === "JsonWebTokenError") {
    error = createError.tokenInvalid();
  }

  if (err.name === "TokenExpiredError") {
    error = createError.tokenExpired();
  }

  // 如果不是操作性错误，设置为内部服务器错误
  if (!error.isOperational) {
    error = createError.internalServerError(
      process.env.NODE_ENV === "development" ? err.message : null,
    );
  }

  // 构建响应
  const response = {
    success: false,
    message: error.message,
    errorCode: error.errorCode || "UNKNOWN_ERROR",
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
  };

  // 开发环境下包含更多错误信息
  if (process.env.NODE_ENV === "development") {
    response.stack = err.stack;
    response.details = error.details;
  }

  // 生产环境下只包含必要信息
  if (process.env.NODE_ENV === "production" && error.details) {
    response.details = error.details;
  }

  res.status(error.statusCode || 500).json(response);
};

/**
 * 404错误处理中间件
 */
const notFoundHandler = (req, res, next) => {
  const error = createError.resourceNotFound(`路由 ${req.originalUrl}`);
  next(error);
};

module.exports = {
  AppError,
  ERROR_TYPES,
  ERROR_MESSAGES,
  createError,
  asyncHandler,
  globalErrorHandler,
  notFoundHandler,
};
