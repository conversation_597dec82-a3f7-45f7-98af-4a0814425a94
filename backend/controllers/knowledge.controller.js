const KnowledgeBase = require("../models/knowledge-base.model");
const { Op } = require("sequelize");

// 获取知识库文章列表
exports.getKnowledgeList = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || "";
    const category = req.query.category || "";

    // 构建查询条件
    const whereClause = {
      status: "published",
    };

    // 添加搜索条件
    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { content: { [Op.like]: `%${search}%` } },
        { keywords: { [Op.like]: `%${search}%` } },
      ];
    }

    // 添加分类条件
    if (category) {
      whereClause.category = category;
    }

    const { count, rows } = await KnowledgeBase.findAndCountAll({
      where: whereClause,
      limit: limit,
      offset: offset,
      order: [
        ["publishTime", "DESC"],
        ["createdAt", "DESC"],
      ],
    });

    res.json({
      success: true,
      data: {
        articles: rows,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取知识库文章列表失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: process.env.NODE_ENV === "development" ? error.message : {},
    });
  }
};

// 获取知识详情
exports.getKnowledgeById = async (req, res) => {
  try {
    const { id } = req.params;

    const article = await KnowledgeBase.findByPk(id);

    if (!article) {
      return res.status(404).json({
        success: false,
        message: "知识文章不存在",
      });
    }

    // 增加浏览次数
    await article.increment("viewCount");

    res.json({
      success: true,
      data: article,
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取知识详情失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: process.env.NODE_ENV === "development" ? error.message : {},
    });
  }
};

// 创建知识文章（管理员功能）
exports.createKnowledge = async (req, res) => {
  try {
    const {
      title,
      content,
      category,
      tags,
      keywords,
      difficulty,
      readTime,
      images,
      videos,
      attachments,
      relatedArticles,
      isRecommended,
    } = req.body;

    // 验证必填字段
    if (!title || !content || !category) {
      return res.status(400).json({
        success: false,
        message: "标题、内容和分类为必填项",
      });
    }

    const article = await KnowledgeBase.create({
      title,
      content,
      category,
      tags,
      keywords,
      difficulty: difficulty || "beginner",
      readTime,
      images,
      videos,
      attachments,
      relatedArticles,
      isRecommended: isRecommended || false,
      status: "published",
      publishTime: new Date(),
      createdBy: req.user.id,
      updatedBy: req.user.id,
    });

    res.status(201).json({
      success: true,
      message: "知识文章创建成功",
      data: article,
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("创建知识文章失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: process.env.NODE_ENV === "development" ? error.message : {},
    });
  }
};

// 更新知识文章（管理员功能）
exports.updateKnowledge = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      category,
      tags,
      keywords,
      difficulty,
      readTime,
      images,
      videos,
      attachments,
      relatedArticles,
      isRecommended,
      status,
    } = req.body;

    const article = await KnowledgeBase.findByPk(id);

    if (!article) {
      return res.status(404).json({
        success: false,
        message: "知识文章不存在",
      });
    }

    // 更新文章
    await article.update({
      title: title || article.title,
      content: content || article.content,
      category: category || article.category,
      tags: tags || article.tags,
      keywords: keywords || article.keywords,
      difficulty: difficulty || article.difficulty,
      readTime: readTime || article.readTime,
      images: images || article.images,
      videos: videos || article.videos,
      attachments: attachments || article.attachments,
      relatedArticles: relatedArticles || article.relatedArticles,
      isRecommended:
        isRecommended !== undefined ? isRecommended : article.isRecommended,
      status: status || article.status,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: "知识文章更新成功",
      data: article,
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新知识文章失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: process.env.NODE_ENV === "development" ? error.message : {},
    });
  }
};

// 删除知识文章（管理员功能）
exports.deleteKnowledge = async (req, res) => {
  try {
    const { id } = req.params;

    const article = await KnowledgeBase.findByPk(id);

    if (!article) {
      return res.status(404).json({
        success: false,
        message: "知识文章不存在",
      });
    }

    // 软删除文章
    await article.destroy();

    res.json({
      success: true,
      message: "知识文章删除成功",
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("删除知识文章失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: process.env.NODE_ENV === "development" ? error.message : {},
    });
  }
};

// 搜索知识库
exports.searchKnowledge = async (req, res) => {
  try {
    const { q, category } = req.query;

    if (!q) {
      return res.status(400).json({
        success: false,
        message: "搜索关键词不能为空",
      });
    }

    // 构建查询条件
    const whereClause = {
      status: "published",
      [Op.or]: [
        { title: { [Op.like]: `%${q}%` } },
        { content: { [Op.like]: `%${q}%` } },
        { keywords: { [Op.like]: `%${q}%` } },
      ],
    };

    // 添加分类条件
    if (category) {
      whereClause.category = category;
    }

    const articles = await KnowledgeBase.findAll({
      where: whereClause,
      order: [
        ["publishTime", "DESC"],
        ["createdAt", "DESC"],
      ],
      limit: 20,
    });

    res.json({
      success: true,
      data: {
        articles,
      },
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("搜索知识库失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: process.env.NODE_ENV === "development" ? error.message : {},
    });
  }
};
