import { z } from "zod";

// 鹅群状态枚举
export const FlockStatusSchema = z.enum([
  "active", // 活跃
  "inactive", // 非活跃
  "sold", // 已出售
  "deceased", // 死亡
]);

// 鹅的品种枚举
export const BreedSchema = z.enum([
  "white_goose", // 白鹅
  "grey_goose", // 灰鹅
  "toulouse", // 图卢兹鹅
  "embden", // 埃姆登鹅
  "chinese", // 中国鹅
  "african", // 非洲鹅
  "sebastopol", // 塞瓦斯托波尔鹅
  "other", // 其他
]);

// 年龄组枚举
export const AgeGroupSchema = z.enum([
  "gosling", // 雏鹅 (0-2个月)
  "young", // 青年鹅 (3-6个月)
  "adult", // 成年鹅 (7个月以上)
  "breeder", // 种鹅
]);

// 基础鹅群信息模式
export const FlockBaseSchema = z.object({
  name: z
    .string()
    .min(1, "鹅群名称不能为空")
    .max(100, "鹅群名称不能超过100个字符"),

  breed: BreedSchema,

  totalCount: z
    .number()
    .int("总数量必须是整数")
    .min(1, "总数量必须大于0")
    .max(10000, "总数量不能超过10000"),

  maleCount: z
    .number()
    .int("雄性数量必须是整数")
    .min(0, "雄性数量不能为负数")
    .optional()
    .default(0),

  femaleCount: z
    .number()
    .int("雌性数量必须是整数")
    .min(0, "雌性数量不能为负数")
    .optional()
    .default(0),

  ageGroup: AgeGroupSchema.optional().default("young"),

  establishedDate: z
    .string()
    .datetime("建群日期格式无效")
    .optional()
    .default(() => new Date().toISOString()),

  location: z.string().max(200, "位置信息不能超过200个字符").optional(),

  description: z.string().max(500, "描述不能超过500个字符").optional(),
});

// 创建鹅群请求模式
export const CreateFlockSchema = FlockBaseSchema.refine(
  (data) => {
    const { totalCount, maleCount = 0, femaleCount = 0 } = data;
    return maleCount + femaleCount === totalCount;
  },
  {
    message: "雄性和雌性数量之和必须等于总数量",
    path: ["totalCount"],
  },
);

// 更新鹅群请求模式 (所有字段可选)
export const UpdateFlockSchema = FlockBaseSchema.partial().extend({
  status: FlockStatusSchema.optional(),
  currentCount: z
    .number()
    .int("当前数量必须是整数")
    .min(0, "当前数量不能为负数")
    .optional(),
});

// 鹅群响应模式
export const FlockResponseSchema = z.object({
  id: z.number(),
  userId: z.number(),
  name: z.string(),
  batchNumber: z.string(),
  breed: BreedSchema,
  totalCount: z.number(),
  currentCount: z.number(),
  maleCount: z.number(),
  femaleCount: z.number(),
  ageGroup: AgeGroupSchema,
  status: FlockStatusSchema,
  establishedDate: z.string(),
  location: z.string().nullable(),
  description: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// 鹅群列表查询参数模式
export const FlockQuerySchema = z.object({
  page: z.coerce
    .number()
    .int("页码必须是整数")
    .min(1, "页码必须大于0")
    .optional()
    .default(1),

  limit: z.coerce
    .number()
    .int("每页数量必须是整数")
    .min(1, "每页数量必须大于0")
    .max(100, "每页数量不能超过100")
    .optional()
    .default(10),

  search: z.string().max(100, "搜索关键词不能超过100个字符").optional(),

  status: FlockStatusSchema.optional(),

  breed: BreedSchema.optional(),

  ageGroup: AgeGroupSchema.optional(),

  sortBy: z
    .enum([
      "createdAt",
      "name",
      "totalCount",
      "currentCount",
      "establishedDate",
    ])
    .optional()
    .default("createdAt"),

  sortOrder: z.enum(["ASC", "DESC"]).optional().default("DESC"),
});

// 分页响应模式
export const PaginationSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  pages: z.number(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

// 鹅群列表响应模式
export const FlockListResponseSchema = z.object({
  items: z.array(FlockResponseSchema),
  pagination: PaginationSchema,
});

// 路径参数模式
export const FlockParamsSchema = z.object({
  id: z.coerce.number().int("鹅群ID必须是整数").min(1, "鹅群ID必须大于0"),
});

// 导出类型
export type FlockStatus = z.infer<typeof FlockStatusSchema>;
export type Breed = z.infer<typeof BreedSchema>;
export type AgeGroup = z.infer<typeof AgeGroupSchema>;
export type CreateFlockRequest = z.infer<typeof CreateFlockSchema>;
export type UpdateFlockRequest = z.infer<typeof UpdateFlockSchema>;
export type FlockResponse = z.infer<typeof FlockResponseSchema>;
export type FlockQuery = z.infer<typeof FlockQuerySchema>;
export type FlockListResponse = z.infer<typeof FlockListResponseSchema>;
export type FlockParams = z.infer<typeof FlockParamsSchema>;
