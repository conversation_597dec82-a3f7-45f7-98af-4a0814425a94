<!-- 多租户登录页面 -->
<view class="login-container">
  <!-- 错误提示 -->
  <view class="error-message" wx:if="{{errorMessage && !showLogin}}">
    <image class="error-icon" src="/images/error.png"></image>
    <text class="error-text">{{errorMessage}}</text>
    <view class="error-actions">
      <button class="retry-btn" bindtap="reloadPage">重新加载</button>
      <button class="contact-btn" bindtap="contactService">联系客服</button>
    </view>
  </view>

  <!-- 正常登录界面 -->
  <view class="login-content" wx:if="{{showLogin}}">
    <!-- 租户信息头部 -->
    <view class="tenant-header">
      <image class="tenant-logo" src="{{tenantInfo.logo || '/images/default-logo.png'}}" mode="aspectFit"></image>
      <view class="tenant-info">
        <text class="tenant-name">{{tenantInfo.name || '智慧养鹅管理平台'}}</text>
        <text class="tenant-desc">{{tenantInfo.description || '智能化养鹅管理系统'}}</text>
      </view>
    </view>

    <!-- 租户代码显示 -->
    <view class="tenant-code" wx:if="{{tenantCode}}">
      <text class="code-label">租户代码：</text>
      <text class="code-value">{{tenantCode}}</text>
    </view>

    <!-- 微信登录区域 -->
    <view class="wechat-login-section" wx:if="{{loginMode === 'wechat'}}">
      <view class="login-title">
        <text>欢迎使用</text>
      </view>
      
      <view class="login-subtitle">
        <text>请点击下方按钮进行微信授权登录</text>
      </view>

      <button class="wechat-login-btn {{loading ? 'disabled' : ''}}" 
              bindtap="getUserProfile" 
              disabled="{{loading}}">
        <image class="wechat-icon" src="/images/wechat.png" wx:if="{{!loading}}"></image>
        <text>{{loading ? '登录中...' : '微信授权登录'}}</text>
      </button>

      <!-- 错误提示（仅在有租户代码但加载失败时显示） -->
      <view class="login-error" wx:if="{{errorMessage && tenantCode}}">
        <text class="error-tip">{{errorMessage}}</text>
        <text class="error-action" bindtap="reloadPage">点击重试</text>
      </view>
    </view>

    <!-- 演示模式区域 -->
    <view class="demo-login-section" wx:if="{{loginMode === 'demo'}}">
      <view class="demo-title">
        <text>演示模式</text>
        <text class="demo-info-btn" bindtap="onDemoInfo">说明</text>
      </view>
      
      <view class="demo-description">
        <text>选择不同角色体验相应功能权限</text>
      </view>

      <!-- 角色选择器 -->
      <view class="role-selector">
        <view class="role-title">选择登录角色：</view>
        <view class="role-grid">
          <block wx:for="{{demoRoles}}" wx:key="role">
            <view class="role-item {{selectedRole === item.role ? 'active' : ''}}" 
                  bindtap="selectRole" 
                  data-role="{{item.role}}">
              <view class="role-name">{{item.name}}</view>
              <view class="role-desc">{{item.description}}</view>
            </view>
          </block>
        </view>
      </view>

      <button class="demo-login-btn {{loading ? 'disabled' : ''}}" 
              bindtap="demoLogin" 
              disabled="{{loading}}">
        <text>{{loading ? '登录中...' : '以' + currentRoleName + '身份登录'}}</text>
      </button>
    </view>

    <!-- 模式切换 -->
    <view class="mode-switch">
      <text class="switch-text" wx:if="{{loginMode === 'wechat'}}">
        没有租户代码？
        <text class="switch-link" bindtap="switchLoginMode">试试演示模式</text>
      </text>
      <text class="switch-text" wx:if="{{loginMode === 'demo'}}">
        有租户代码？
        <text class="switch-link" bindtap="switchLoginMode">返回正式登录</text>
      </text>
    </view>

    <!-- 帮助信息 -->
    <view class="help-section">
      <view class="help-item" bindtap="contactService">
        <image class="help-icon" src="/images/service.png"></image>
        <text>联系客服</text>
      </view>
    </view>
  </view>

  <!-- 页面底部 -->
  <view class="login-footer">
    <text class="copyright">© 2024 智慧养鹅SaaS平台</text>
  </view>
</view>