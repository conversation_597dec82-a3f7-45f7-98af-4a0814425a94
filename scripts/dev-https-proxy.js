/**
 * 🔒 开发环境HTTPS代理服务器
 * 为微信小程序开发环境提供HTTPS支持
 * 解决localhost域名无法通过微信小程序域名校验的问题
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const { createProxyMiddleware } = require('http-proxy-middleware');
const express = require('express');
const cors = require('cors');

class DevHttpsProxy {
  constructor(options = {}) {
    this.config = {
      // HTTPS代理服务器配置
      httpsPort: options.httpsPort || 8443,
      httpPort: options.httpPort || 8080,
      
      // 后端服务器配置
      targetHost: options.targetHost || 'localhost',
      targetPort: options.targetPort || 3000,
      targetProtocol: options.targetProtocol || 'http',
      
      // SSL证书配置
      certPath: options.certPath || path.join(__dirname, '../certs'),
      certName: options.certName || 'dev-cert',
      
      // 域名配置
      domain: options.domain || 'dev-api.zhihuiyange.local',
      
      // 代理配置
      changeOrigin: true,
      secure: false,
      logLevel: options.logLevel || 'info'
    };
    
    this.app = express();
    this.setupMiddleware();
  }

  /**
   * 设置Express中间件
   */
  setupMiddleware() {
    // 启用CORS
    this.app.use(cors({
      origin: ['http://localhost:3000', 'https://dev-api.zhihuiyange.local:8443'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Tenant-Code']
    }));

    // 请求日志
    this.app.use((req, res, next) => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.get('User-Agent') || 'Unknown'}`);
      next();
    });

    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        proxy: {
          httpsPort: this.config.httpsPort,
          httpPort: this.config.httpPort,
          target: `${this.config.targetProtocol}://${this.config.targetHost}:${this.config.targetPort}`
        }
      });
    });

    // 代理配置
    const proxyOptions = {
      target: `${this.config.targetProtocol}://${this.config.targetHost}:${this.config.targetPort}`,
      changeOrigin: this.config.changeOrigin,
      secure: this.config.secure,
      logLevel: this.config.logLevel,
      
      // 路径重写
      pathRewrite: {
        '^/api/proxy': '/api', // 移除代理前缀
      },

      // 请求头处理
      onProxyReq: (proxyReq, req, res) => {
        // 添加自定义请求头
        proxyReq.setHeader('X-Forwarded-For', req.ip);
        proxyReq.setHeader('X-Forwarded-Proto', req.protocol);
        proxyReq.setHeader('X-Forwarded-Host', req.get('Host'));
        
        // 处理POST请求体
        if (req.body && (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH')) {
          const bodyData = JSON.stringify(req.body);
          proxyReq.setHeader('Content-Type', 'application/json');
          proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
          proxyReq.write(bodyData);
        }
      },

      // 响应处理
      onProxyRes: (proxyRes, req, res) => {
        // 添加CORS头
        proxyRes.headers['Access-Control-Allow-Origin'] = req.get('Origin') || '*';
        proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,POST,PUT,DELETE,PATCH,OPTIONS';
        proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type,Authorization,X-Requested-With,X-Tenant-Code';
      },

      // 错误处理
      onError: (err, req, res) => {
        console.error('代理错误:', err.message);
        res.status(500).json({
          error: '代理服务器错误',
          message: err.message,
          timestamp: new Date().toISOString()
        });
      }
    };

    // 创建代理中间件
    const apiProxy = createProxyMiddleware('/api', proxyOptions);
    this.app.use('/api', apiProxy);

    // 静态文件代理（如果需要）
    const staticProxy = createProxyMiddleware('/static', {
      ...proxyOptions,
      pathRewrite: {
        '^/static': '/public'
      }
    });
    this.app.use('/static', staticProxy);

    // 处理预检请求
    this.app.options('*', cors());
  }

  /**
   * 生成自签名SSL证书
   */
  async generateSSLCertificate() {
    const certDir = this.config.certPath;
    const keyPath = path.join(certDir, `${this.config.certName}.key`);
    const certPath = path.join(certDir, `${this.config.certName}.crt`);

    // 检查证书是否已存在
    if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
      console.log('✅ SSL证书已存在，跳过生成');
      return { keyPath, certPath };
    }

    // 创建证书目录
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }

    console.log('🔐 生成自签名SSL证书...');

    // 生成证书的命令
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      // 生成私钥
      await execAsync(`openssl genrsa -out "${keyPath}" 2048`);
      
      // 生成证书
      const certCommand = `openssl req -new -x509 -key "${keyPath}" -out "${certPath}" -days 365 -subj "/C=CN/ST=Beijing/L=Beijing/O=DevProxy/OU=Development/CN=${this.config.domain}"`;
      await execAsync(certCommand);

      console.log('✅ SSL证书生成成功');
      console.log(`   私钥: ${keyPath}`);
      console.log(`   证书: ${certPath}`);
      
      return { keyPath, certPath };
    } catch (error) {
      console.error('❌ SSL证书生成失败:', error.message);
      
      // 如果OpenSSL不可用，提供手动生成指南
      console.log('\n📋 手动生成SSL证书指南:');
      console.log('1. 安装OpenSSL (macOS: brew install openssl, Windows: 下载OpenSSL)');
      console.log('2. 运行以下命令:');
      console.log(`   openssl genrsa -out "${keyPath}" 2048`);
      console.log(`   openssl req -new -x509 -key "${keyPath}" -out "${certPath}" -days 365 -subj "/C=CN/ST=Beijing/L=Beijing/O=DevProxy/OU=Development/CN=${this.config.domain}"`);
      
      throw error;
    }
  }

  /**
   * 启动HTTPS服务器
   */
  async startHttpsServer() {
    try {
      const { keyPath, certPath } = await this.generateSSLCertificate();
      
      const httpsOptions = {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath)
      };

      const httpsServer = https.createServer(httpsOptions, this.app);
      
      httpsServer.listen(this.config.httpsPort, () => {
        console.log(`🔒 HTTPS代理服务器启动成功!`);
        console.log(`   URL: https://${this.config.domain}:${this.config.httpsPort}`);
        console.log(`   代理目标: ${this.config.targetProtocol}://${this.config.targetHost}:${this.config.targetPort}`);
        console.log(`   健康检查: https://${this.config.domain}:${this.config.httpsPort}/health`);
      });

      httpsServer.on('error', (error) => {
        console.error('❌ HTTPS服务器启动失败:', error.message);
        if (error.code === 'EADDRINUSE') {
          console.log(`   端口 ${this.config.httpsPort} 已被占用，请尝试其他端口`);
        }
      });

      return httpsServer;
    } catch (error) {
      console.error('❌ HTTPS服务器配置失败:', error.message);
      throw error;
    }
  }

  /**
   * 启动HTTP重定向服务器
   */
  startHttpRedirectServer() {
    const httpApp = express();
    
    // 重定向所有HTTP请求到HTTPS
    httpApp.use((req, res) => {
      const httpsUrl = `https://${this.config.domain}:${this.config.httpsPort}${req.url}`;
      res.redirect(301, httpsUrl);
    });

    const httpServer = http.createServer(httpApp);
    
    httpServer.listen(this.config.httpPort, () => {
      console.log(`🔀 HTTP重定向服务器启动成功!`);
      console.log(`   URL: http://${this.config.domain}:${this.config.httpPort}`);
      console.log(`   重定向到: https://${this.config.domain}:${this.config.httpsPort}`);
    });

    httpServer.on('error', (error) => {
      console.error('❌ HTTP重定向服务器启动失败:', error.message);
      if (error.code === 'EADDRINUSE') {
        console.log(`   端口 ${this.config.httpPort} 已被占用，请尝试其他端口`);
      }
    });

    return httpServer;
  }

  /**
   * 启动代理服务器
   */
  async start() {
    console.log('🚀 启动开发环境HTTPS代理服务器...');
    console.log('配置信息:');
    console.log(`   域名: ${this.config.domain}`);
    console.log(`   HTTPS端口: ${this.config.httpsPort}`);
    console.log(`   HTTP端口: ${this.config.httpPort}`);
    console.log(`   代理目标: ${this.config.targetProtocol}://${this.config.targetHost}:${this.config.targetPort}`);
    console.log('');

    try {
      // 启动HTTPS服务器
      const httpsServer = await this.startHttpsServer();
      
      // 启动HTTP重定向服务器
      const httpServer = this.startHttpRedirectServer();

      // 输出配置指南
      console.log('\n' + '='.repeat(60));
      console.log('📋 微信小程序配置指南');
      console.log('='.repeat(60));
      console.log('1. 在微信开发者工具中添加以下域名到request合法域名:');
      console.log(`   https://${this.config.domain}:${this.config.httpsPort}`);
      console.log('');
      console.log('2. 或者在开发阶段临时关闭域名校验:');
      console.log('   详情 → 本地设置 → 勾选"不校验合法域名..."');
      console.log('');
      console.log('3. 在小程序中使用代理URL:');
      console.log(`   const apiUrl = 'https://${this.config.domain}:${this.config.httpsPort}/api/v1';`);
      console.log('='.repeat(60));

      // 优雅关闭处理
      const gracefulShutdown = () => {
        console.log('\n🛑 正在关闭代理服务器...');
        httpsServer.close(() => {
          console.log('✅ HTTPS服务器已关闭');
        });
        httpServer.close(() => {
          console.log('✅ HTTP重定向服务器已关闭');
          process.exit(0);
        });
      };

      process.on('SIGTERM', gracefulShutdown);
      process.on('SIGINT', gracefulShutdown);

      return { httpsServer, httpServer };
    } catch (error) {
      console.error('❌ 代理服务器启动失败:', error.message);
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const proxy = new DevHttpsProxy({
    httpsPort: process.env.HTTPS_PORT || 8443,
    httpPort: process.env.HTTP_PORT || 8080,
    targetPort: process.env.TARGET_PORT || 3000,
    domain: process.env.PROXY_DOMAIN || 'dev-api.zhihuiyange.local'
  });

  proxy.start().catch(error => {
    console.error('启动失败:', error);
    process.exit(1);
  });
}

module.exports = DevHttpsProxy;