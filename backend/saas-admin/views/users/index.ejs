<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body>
    <%- include('../partials/navbar') %>

    <div class="container-fluid">
        <div class="row">
            <%- include('../partials/sidebar') %>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 顶部导航 -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0"><%= title %></h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-3">在线</span>
                            <i class="bi bi-bell fs-5 me-3"></i>
                            <i class="bi bi-gear fs-5"></i>
                        </div>
                    </div>
                </nav>
                
                <div class="container-fluid mt-4">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><i class="bi bi-people-fill me-2"></i><%= title %></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-success">
                                <i class="bi bi-person-plus"></i> 新增用户
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" placeholder="搜索用户名、邮箱或租户...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col">
                                <select class="form-select">
                                    <option>全部状态</option>
                                    <option>活跃</option>
                                    <option>禁用</option>
                                </select>
                            </div>
                            <div class="col">
                                <select class="form-select">
                                    <option>全部角色</option>
                                    <option>管理员</option>
                                    <option>用户</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th><input type="checkbox" class="form-check-input"></th>
                                        <th>用户名</th>
                                        <th>邮箱</th>
                                        <th>角色</th>
                                        <th>所属租户</th>
                                        <th>状态</th>
                                        <th>最后登录</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% users.forEach(user => { %>
                                    <tr>
                                        <td><input type="checkbox" class="form-check-input"></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar me-2">
                                                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                        <%= user.username.charAt(0).toUpperCase() %>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><%= user.username %></div>
                                                    <small class="text-muted">ID: <%= user.id %></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><%= user.email %></td>
                                        <td>
                                            <% if (user.role === 'admin') { %>
                                                <span class="badge bg-primary">管理员</span>
                                            <% } else { %>
                                                <span class="badge bg-secondary">用户</span>
                                            <% } %>
                                        </td>
                                        <td><%= user.tenantName %></td>
                                        <td>
                                            <% if (user.status === 'active') { %>
                                                <span class="badge bg-success">活跃</span>
                                            <% } else { %>
                                                <span class="badge bg-danger">禁用</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <%= new Date(user.lastLogin).toLocaleDateString('zh-CN') %>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/saas-admin/users/<%= user.id %>" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <% }) %>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav class="mt-3">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <span class="page-link">上一页</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                            </ul>
                        </nav>
                        </nav>
                    </div>
                </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>