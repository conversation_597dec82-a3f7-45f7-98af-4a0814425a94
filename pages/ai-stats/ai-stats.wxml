<!-- pages/ai-stats/ai-stats.wxml -->
<view class="ai-stats-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">AI使用统计</text>
    <text class="page-subtitle">查看AI服务使用情况和费用统计</text>
  </view>

  <!-- 时间范围选择 -->
  <view class="time-range-selector">
    <block wx:for="{{timeRanges}}" wx:key="id">
      <text class="range-item {{activeTimeRange === item.id ? 'active' : ''}}" 
            data-range="{{item.id}}" 
            bindtap="onTimeRangeChange">{{item.name}}</text>
    </block>
  </view>

  <!-- 统计概览 -->
  <view class="section overview-section">
    <view class="section-header">
      <text class="section-title">统计概览</text>
      <view class="action-buttons">
        <button class="action-btn" bindtap="onExportReport">
          <text class="action-icon">📊</text>
          <text class="action-text">导出</text>
        </button>
        <button class="action-btn" bindtap="onClearStats">
          <text class="action-icon">🗑️</text>
          <text class="action-text">清空</text>
        </button>
      </view>
    </view>
    
    <view class="overview-grid">
      <view class="overview-item primary">
        <text class="item-label">总请求数</text>
        <text class="item-value">{{statsOverview.totalRequests}}</text>
        <text class="item-unit">次</text>
      </view>
      <view class="overview-item success">
        <text class="item-label">成功率</text>
        <text class="item-value">{{displayStats.successRate}}%</text>
        <text class="item-unit">{{statsOverview.successfulRequests}}/{{statsOverview.totalRequests}}</text>
      </view>
      <view class="overview-item info">
        <text class="item-label">总Token数</text>
        <text class="item-value">{{statsOverview.totalTokens}}</text>
        <text class="item-unit">tokens</text>
      </view>
      <view class="overview-item warning">
        <text class="item-label">总费用</text>
        <text class="item-value">¥{{statsOverview.totalCost}}</text>
        <text class="item-unit">CNY</text>
      </view>
      <view class="overview-item secondary">
        <text class="item-label">平均响应时间</text>
        <text class="item-value">{{statsOverview.averageResponseTime}}</text>
        <text class="item-unit">ms</text>
      </view>
      <view class="overview-item accent">
        <text class="item-label">主要提供商</text>
        <text class="item-value">{{statsOverview.topProvider}}</text>
        <text class="item-unit">最常用</text>
      </view>
    </view>
  </view>

  <!-- 服务提供商统计 -->
  <view class="section provider-section">
    <view class="section-header">
      <text class="section-title">服务提供商统计</text>
    </view>
    <view class="provider-list">
      <block wx:for="{{detailedStats.byProvider}}" wx:key="name">
        <view class="provider-item" bindtap="onViewDetail" data-type="provider" data-data="{{item}}">
          <view class="provider-info">
            <text class="provider-name">{{item.name}}</text>
            <view class="provider-metrics">
              <text class="metric">{{item.requests}}次请求</text>
              <text class="metric">{{item.tokens}}tokens</text>
              <text class="metric">¥{{item.cost}}</text>
            </view>
          </view>
          <view class="provider-success">
            <text class="success-rate">{{item.successRateText}}%</text>
            <text class="success-label">成功率</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 使用场景统计 -->
  <view class="section scenario-section">
    <view class="section-header">
      <text class="section-title">使用场景统计</text>
    </view>
    <view class="scenario-list">
      <block wx:for="{{detailedStats.byScenario}}" wx:key="name">
        <view class="scenario-item" bindtap="onViewDetail" data-type="scenario" data-data="{{item}}">
          <view class="scenario-info">
            <text class="scenario-name">{{item.name}}</text>
            <view class="scenario-metrics">
              <view class="metric-item">
                <text class="metric-label">请求次数</text>
                <text class="metric-value">{{item.requests}}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">Token数</text>
                <text class="metric-value">{{item.tokens}}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">平均响应时间</text>
                <text class="metric-value">{{item.avgTime}}ms</text>
              </view>
            </view>
          </view>
          <view class="scenario-progress">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.percentageText}}%"></view>
            </view>
            <text class="progress-text">{{item.percentageText}}%</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 模型使用统计 -->
  <view class="section model-section">
    <view class="section-header">
      <text class="section-title">模型使用统计</text>
    </view>
    <view class="model-grid">
      <block wx:for="{{detailedStats.byModel}}" wx:key="name">
        <view class="model-card" bindtap="onViewDetail" data-type="model" data-data="{{item}}">
          <view class="model-header">
            <text class="model-name">{{item.name}}</text>
            <text class="model-cost">¥{{item.cost}}</text>
          </view>
          <view class="model-stats">
            <text class="model-requests">{{item.requests}}次请求</text>
            <text class="model-percentage">{{item.percentageText}}%</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 趋势图表 -->
  <view class="section chart-section">
    <view class="section-header">
      <text class="section-title">使用趋势</text>
    </view>
    <view class="chart-tabs">
      <text class="chart-tab active">使用量</text>
      <text class="chart-tab">费用</text>
      <text class="chart-tab">成功率</text>
    </view>
    <view class="chart-container">
      <text class="chart-placeholder">📈 趋势图表（需要图表组件支持）</text>
      <view class="chart-data">
        <block wx:for="{{chartData.usage}}" wx:key="date">
          <view class="chart-point">
            <text class="point-date">{{item.date}}</text>
            <text class="point-value">{{item.value}}</text>
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <text class="loading-spinner">⏳</text>
      <text class="loading-text">加载统计数据中...</text>
    </view>
  </view>

  <!-- 刷新状态 -->
  <view wx:if="{{refreshing}}" class="refresh-indicator">
    <text class="refresh-text">正在刷新...</text>
  </view>
</view>

<!-- 详情模态框 -->
<modal wx:if="{{showDetailModal}}" class="detail-modal">
  <view class="modal-overlay" bindtap="onCloseDetailModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">{{detailData.title}}</text>
      <button class="modal-close" bindtap="onCloseDetailModal">×</button>
    </view>
    <view class="modal-body">
      <view class="detail-content">
        <text class="detail-placeholder">详细信息将在这里显示</text>
        <!-- 这里可以根据detailData.type显示不同的详细信息 -->
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn" bindtap="onCloseDetailModal">关闭</button>
    </view>
  </view>
</modal>