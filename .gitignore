# Logs
logs
*.log
backend/logs

# Temporary files
temp
.tmp
*.tmp

# Node
node_modules
backend/node_modules

# Build outputs
dist
build
backend/build
backend/dist

# OS
.DS_Store

# Editor
.vscode
.idea

# Mistaken synced paths
javascript:

# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 临时文件
.tmp/
temp/

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 微信小程序相关
project.private.config.json

# 数据库文件
*.sqlite
*.db

# 构建输出
dist/
build/

# 备份文件
*.bak
*.backup

# 测试覆盖率报告
coverage/

# 上传文件
uploads/
