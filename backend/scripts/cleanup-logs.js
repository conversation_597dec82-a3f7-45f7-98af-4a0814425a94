#!/usr/bin/env node

/**
 * 智慧养鹅系统 - 日志清理脚本
 * 定期清理过期的日志文件
 */

const fs = require("fs").promises;
const path = require("path");

class LogCleaner {
  constructor(options = {}) {
    this.logDir = options.logDir || path.join(__dirname, "../logs");
    this.retentionDays = options.retentionDays || 30; // 默认保留30天
    this.dryRun = options.dryRun || false; // 是否为试运行
  }

  /**
   * 获取日志目录中的所有文件
   */
  async getLogFiles() {
    try {
      const files = await fs.readdir(this.logDir);
      return files.filter((file) => file.endsWith(".log"));
    } catch (error) {
      if (error.code === "ENOENT") {
        console.log("📁 日志目录不存在，无需清理");
        return [];
      }
      throw error;
    }
  }

  /**
   * 检查文件是否过期
   */
  async isFileExpired(filePath) {
    const stats = await fs.stat(filePath);
    const fileAge = Date.now() - stats.mtime.getTime();
    const maxAge = this.retentionDays * 24 * 60 * 60 * 1000; // 转换为毫秒

    return fileAge > maxAge;
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(filePath) {
    const stats = await fs.stat(filePath);
    return {
      path: filePath,
      name: path.basename(filePath),
      size: stats.size,
      sizeFormatted: this.formatFileSize(stats.size),
      mtime: stats.mtime,
      age: Math.floor(
        (Date.now() - stats.mtime.getTime()) / (24 * 60 * 60 * 1000),
      ), // 天数
    };
  }

  /**
   * 清理过期日志文件
   */
  async cleanupLogs() {
    console.log("🧹 开始清理日志文件...");
    console.log(`📂 日志目录: ${this.logDir}`);
    console.log(`⏰ 保留天数: ${this.retentionDays} 天`);
    console.log(`🔍 模式: ${this.dryRun ? "试运行" : "实际清理"}`);
    console.log("=".repeat(50));

    const logFiles = await this.getLogFiles();

    if (logFiles.length === 0) {
      console.log("📝 没有找到日志文件");
      return {
        total: 0,
        expired: 0,
        deleted: 0,
        totalSize: 0,
        freedSpace: 0,
      };
    }

    console.log(`📝 找到 ${logFiles.length} 个日志文件`);

    let totalFiles = 0;
    let expiredFiles = 0;
    let deletedFiles = 0;
    let totalSize = 0;
    let freedSpace = 0;

    const fileInfos = [];

    // 收集文件信息
    for (const file of logFiles) {
      const filePath = path.join(this.logDir, file);
      const fileInfo = await this.getFileInfo(filePath);
      const isExpired = await this.isFileExpired(filePath);

      fileInfo.expired = isExpired;
      fileInfos.push(fileInfo);

      totalFiles++;
      totalSize += fileInfo.size;

      if (isExpired) {
        expiredFiles++;
        freedSpace += fileInfo.size;
      }
    }

    // 按修改时间排序（最新的在前）
    fileInfos.sort((a, b) => b.mtime - a.mtime);

    // 显示文件列表
    console.log("\n📋 日志文件列表:");
    console.log(
      "文件名".padEnd(30) + "大小".padEnd(10) + "年龄".padEnd(8) + "状态",
    );
    console.log("-".repeat(60));

    for (const fileInfo of fileInfos) {
      const status = fileInfo.expired ? "🗑️ 过期" : "✅ 保留";
      console.log(
        fileInfo.name.padEnd(30) +
          fileInfo.sizeFormatted.padEnd(10) +
          `${fileInfo.age}天`.padEnd(8) +
          status,
      );
    }

    // 执行删除操作
    if (expiredFiles > 0) {
      console.log(
        `\n🗑️ 发现 ${expiredFiles} 个过期文件，总大小: ${this.formatFileSize(freedSpace)}`,
      );

      if (!this.dryRun) {
        console.log("🔄 开始删除过期文件...");

        for (const fileInfo of fileInfos) {
          if (fileInfo.expired) {
            try {
              await fs.unlink(fileInfo.path);
              console.log(`✅ 已删除: ${fileInfo.name}`);
              deletedFiles++;
            } catch (error) {
              console.error(`❌ 删除失败: ${fileInfo.name} - ${error.message}`);
            }
          }
        }
      } else {
        console.log("🔍 试运行模式，不会实际删除文件");
        deletedFiles = expiredFiles; // 在试运行模式下，假设所有过期文件都会被删除
      }
    } else {
      console.log("\n✅ 没有过期的日志文件需要清理");
    }

    // 显示清理结果
    console.log("\n📊 清理结果:");
    console.log(`总文件数: ${totalFiles}`);
    console.log(`过期文件数: ${expiredFiles}`);
    console.log(`删除文件数: ${deletedFiles}`);
    console.log(`总大小: ${this.formatFileSize(totalSize)}`);
    console.log(`释放空间: ${this.formatFileSize(freedSpace)}`);

    return {
      total: totalFiles,
      expired: expiredFiles,
      deleted: deletedFiles,
      totalSize,
      freedSpace,
    };
  }

  /**
   * 压缩旧日志文件
   */
  async compressOldLogs() {
    console.log("🗜️ 开始压缩旧日志文件...");

    // 实现日志压缩功能
    const zlib = require("zlib");
    const archiveDir = path.join(this.logDir, "archive");

    if (!fs.existsSync(archiveDir)) {
      fs.mkdirSync(archiveDir, { recursive: true });
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 30); // 压缩30天前的日志

    const files = fs.readdirSync(this.logDir);
    files.forEach((file) => {
      const filePath = path.join(this.logDir, file);
      const stats = fs.statSync(filePath);

      if (stats.isFile() && file.endsWith(".log") && stats.mtime < cutoffDate) {
        const sourceStream = fs.createReadStream(filePath);
        const destStream = fs.createWriteStream(
          path.join(archiveDir, `${file}.gz`),
        );
        const gzipStream = zlib.createGzip();

        sourceStream.pipe(gzipStream).pipe(destStream);

        destStream.on("close", () => {
          fs.unlinkSync(filePath); // 删除原文件
          console.log(`日志文件 ${file} 已压缩并归档`);
        });
      }
    });
  }

  /**
   * 生成清理报告
   */
  async generateReport(result) {
    const report = `
# 日志清理报告

**清理时间**: ${new Date().toLocaleString("zh-CN")}
**日志目录**: ${this.logDir}
**保留天数**: ${this.retentionDays} 天
**运行模式**: ${this.dryRun ? "试运行" : "实际清理"}

## 清理结果

- **总文件数**: ${result.total}
- **过期文件数**: ${result.expired}
- **删除文件数**: ${result.deleted}
- **总大小**: ${this.formatFileSize(result.totalSize)}
- **释放空间**: ${this.formatFileSize(result.freedSpace)}

## 建议

${
  result.expired === 0
    ? "✅ 当前日志文件都在保留期内，无需清理。"
    : `🗑️ 建议定期运行此脚本以保持日志目录整洁。`
}

${
  result.totalSize > 100 * 1024 * 1024
    ? "⚠️ 日志文件总大小超过100MB，建议考虑启用日志压缩功能。"
    : ""
}

---
*报告由智慧养鹅系统日志清理工具自动生成*
`;

    const reportPath = path.join(
      this.logDir,
      `cleanup-report-${Date.now()}.md`,
    );

    try {
      await fs.writeFile(reportPath, report, "utf8");
      console.log(`📄 清理报告已保存到: ${reportPath}`);
    } catch (error) {
      console.error("❌ 保存清理报告失败:", error.message);
    }
  }
}

// 主函数
async function main() {
  console.log("🧹 智慧养鹅系统 - 日志清理工具");
  console.log("=".repeat(50));

  // 解析命令行参数
  const args = process.argv.slice(2);
  const options = {
    retentionDays: 30,
    dryRun: false,
  };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case "--retention-days":
        options.retentionDays = parseInt(args[i + 1]) || 30;
        i++;
        break;
      case "--dry-run":
        options.dryRun = true;
        break;
      case "--help":
        console.log(`
使用方法: node cleanup-logs.js [选项]

选项:
  --retention-days <天数>  日志保留天数 (默认: 30)
  --dry-run               试运行模式，不实际删除文件
  --help                  显示帮助信息

示例:
  node cleanup-logs.js --retention-days 7 --dry-run
        `);
        process.exit(0);
    }
  }

  const cleaner = new LogCleaner(options);

  try {
    const result = await cleaner.cleanupLogs();
    await cleaner.generateReport(result);

    console.log("\n🎉 日志清理完成！");
    process.exit(0);
  } catch (error) {
    console.error("\n❌ 日志清理失败:", error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = LogCleaner;
