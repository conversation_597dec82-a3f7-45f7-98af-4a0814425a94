<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-content {
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .detail-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .confidence-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .confidence-high { background-color: #d4edda; color: #155724; }
        .confidence-medium { background-color: #fff3cd; color: #856404; }
        .confidence-low { background-color: #f8d7da; color: #721c24; }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .recommendation-box {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <%- include('../partials/navbar') %>

    <div class="container-fluid">
        <div class="row">
            <%- include('../partials/sidebar') %>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="main-content">
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h2">
                                <i class="bi bi-robot me-2"></i>AI诊断详情
                            </h1>
                            <div>
                                <a href="/saas-admin/ai-config" class="btn btn-outline-secondary me-2">
                                    <i class="bi bi-arrow-left"></i> 返回AI配置
                                </a>
                                <button type="button" class="btn btn-primary">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-card">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="mb-3">基本信息</h4>
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <strong>诊断ID：</strong>
                                    </div>
                                    <div class="col-sm-9">
                                        #<%= record.id %>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <strong>鹅群编号：</strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <%= record.flockCode %>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <strong>症状描述：</strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <%= record.symptoms %>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <strong>创建时间：</strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <%= new Date(record.createdAt).toLocaleString('zh-CN') %>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <strong>更新时间：</strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <%= new Date(record.updatedAt).toLocaleString('zh-CN') %>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="mb-3">
                                        <span class="status-badge <%= record.status === 'completed' ? 'bg-success' : record.status === 'pending' ? 'bg-warning text-dark' : 'bg-secondary' %>">
                                            <%= record.status === 'completed' ? '已完成' : record.status === 'pending' ? '待处理' : '进行中' %>
                                        </span>
                                    </div>
                                    <div class="mb-3">
                                        <span class="confidence-badge <%= record.confidence >= 0.8 ? 'confidence-high' : record.confidence >= 0.6 ? 'confidence-medium' : 'confidence-low' %>">
                                            置信度: <%= Math.round(record.confidence * 100) %>%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-card">
                        <h4 class="mb-3">
                            <i class="bi bi-clipboard-check me-2"></i>诊断结果
                        </h4>
                        <div class="alert alert-info">
                            <h5><i class="bi bi-lightbulb me-2"></i>诊断结论</h5>
                            <p class="mb-0"><%= record.diagnosis %></p>
                        </div>
                        
                        <div class="recommendation-box">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>处理建议</h6>
                            <p class="mb-0"><%= record.recommendations %></p>
                        </div>
                    </div>

                    <% if (record.images && record.images.length > 0) { %>
                    <div class="detail-card">
                        <h4 class="mb-3">
                            <i class="bi bi-images me-2"></i>相关图片
                        </h4>
                        <div class="row">
                            <% record.images.forEach(function(image) { %>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <img src="<%= image %>" alt="诊断图片" class="image-preview img-fluid" data-bs-toggle="modal" data-bs-target="#imageModal" data-bs-image="<%= image %>">
                                </div>
                            <% }) %>
                        </div>
                    </div>
                    <% } %>

                    <div class="detail-card">
                        <h4 class="mb-3">
                            <i class="bi bi-gear me-2"></i>操作记录
                        </h4>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>操作</th>
                                        <th>操作人</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><%= new Date(record.createdAt).toLocaleString('zh-CN') %></td>
                                        <td>创建诊断记录</td>
                                        <td>AI系统</td>
                                        <td>自动创建</td>
                                    </tr>
                                    <% if (record.updatedAt !== record.createdAt) { %>
                                    <tr>
                                        <td><%= new Date(record.updatedAt).toLocaleString('zh-CN') %></td>
                                        <td>更新诊断结果</td>
                                        <td>AI系统</td>
                                        <td>完成AI分析</td>
                                    </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="预览图片" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 图片预览功能
        document.getElementById('imageModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-bs-image');
            const modalImg = document.getElementById('modalImage');
            modalImg.src = imageSrc;
        });
    </script>
</body>
</html>