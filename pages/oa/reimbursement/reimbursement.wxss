@import '/styles/oa-common.wxss';
/* pages/oa/reimbursement/reimbursement.wxss */

.reimbursement-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx 0 40rpx 0;
}

.quick-actions-section,
.statistics-section,
.recent-section {
  margin: 20rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.quick-actions-section:first-child {
  margin-top: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all-btn {
  font-size: 28rpx;
  color: #0066CC;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: #f0f8ff;
}

.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.quick-action-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.1);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
}

.action-icon image {
  width: 48rpx;
  height: 48rpx;
}

.icon-text {
  font-size: 44rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.action-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.action-description {
  font-size: 26rpx;
  color: #666;
}

.action-arrow {
  color: #999;
  font-size: 32rpx;
  font-weight: bold;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  background: #f8fbff;
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.1);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.stat-number.pending {
  color: #FF9500;
}

.stat-number.approved {
  color: #00A86B;
}

.stat-number.rejected {
  color: #FF3B30;
}

.stat-number.total {
  color: #0066CC;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.reimbursement-item {
  background: #f8fbff;
  border: 2rpx solid #e8f3ff;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}

.reimbursement-item:active {
  transform: translateY(-2rpx);
  border-color: #0066CC;
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.1);
}

.reimbursement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.reimbursement-type {
  background: #FF9500;
  color: white;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-weight: 500;
}

.reimbursement-status {
  font-size: 26rpx;
  font-weight: 600;
}

.reimbursement-content {
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reimbursement-description {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.reimbursement-amount {
  font-size: 32rpx;
  color: #FF9500;
  font-weight: 700;
}

.reimbursement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 2rpx solid #f0f0f0;
}

.submit-time {
  font-size: 24rpx;
  color: #999;
}

.arrow-icon {
  color: #ccc;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}