<!-- components/lazy-list/lazy-list.wxml -->
<scroll-view 
  class="lazy-list"
  scroll-y="{{true}}"
  enable-back-to-top="{{true}}"
  bindscroll="onScroll"
  style="height: {{containerHeight}}rpx;"
>
  <!-- 虚拟滚动模式 -->
  <view wx:if="{{virtual}}" class="virtual-container">
    <!-- 上方占位 -->
    <view class="virtual-placeholder" style="height: {{paddingTop}}rpx;"></view>
    
    <!-- 可见项列表 -->
    <view 
      wx:for="{{visibleItems}}" 
      wx:key="index"
      class="list-item virtual-item"
      style="height: {{itemHeight}}rpx;"
      data-index="{{index}}"
      bindtap="onItemTap"
    >
      <slot name="item" item="{{item}}" index="{{startIndex + index}}"></slot>
    </view>
    
    <!-- 下方占位 -->
    <view class="virtual-placeholder" style="height: {{paddingBottom}}rpx;"></view>
  </view>

  <!-- 普通模式 -->
  <view wx:else class="normal-container">
    <view 
      wx:for="{{displayItems}}" 
      wx:key="index"
      class="list-item"
      data-index="{{index}}"
      bindtap="onItemTap"
    >
      <slot name="item" item="{{item}}" index="{{index}}"></slot>
    </view>

    <!-- 加载更多指示器 -->
    <view wx:if="{{loading}}" class="loading-more">
      <c-loading text="加载中..."></c-loading>
    </view>

    <!-- 无更多数据提示 -->
    <view wx:elif="{{!hasMore && displayItems.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多数据了</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{displayItems.length === 0}}" class="empty-container">
      <slot name="empty">
        <c-empty-state title="暂无数据"></c-empty-state>
      </slot>
    </view>
  </view>
</scroll-view>