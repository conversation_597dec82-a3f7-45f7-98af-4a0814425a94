/**
 * 微信小程序 Loading 最终解决方案
 * 
 * 核心思想：完全不调用 wx.hideLoading()
 * 让微信小程序自己管理 loading 的隐藏
 * 
 * 设计原理：
 * 1. 只调用 wx.showLoading()
 * 2. 永远不调用 wx.hideLoading()
 * 3. 依靠页面跳转、网络请求完成等自动隐藏
 * 4. 使用定时器作为备用隐藏机制
 */

let showCallCount = 0;
let lastShowTime = 0;

/**
 * 安全显示 Loading
 * @param {Object} options 选项
 */
function safeShow(options = {}) {
  const now = Date.now();
  showCallCount++;
  
  // 防止过于频繁的调用（100ms内只允许一次）
  if (now - lastShowTime < 100) {
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[FinalSolution] Too frequent show calls, skip', now - lastShowTime); } catch(_) {}
    return;
  }
  
  lastShowTime = now;
  
  try {
    wx.showLoading({
      title: options.title || '加载中...',
      mask: options.mask !== false
    });
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[FinalSolution] wx.showLoading called', { title: options.title || '加载中...', call: showCallCount }); } catch(_) {}
    
    // 设置备用定时器，30秒后自动隐藏（防止loading永久显示）
    setTimeout(() => {
      try {
        wx.hideLoading();
        try { const logger = require('./logger.js'); logger.debug && logger.debug('[FinalSolution] Backup timer hideLoading'); } catch(_) {}
      } catch (error) {
        try { const logger = require('./logger.js'); logger.warn && logger.warn('[FinalSolution] Backup hideLoading failed', error); } catch(_) {}
      }
    }, 30000);
    
  } catch (error) {
    try { const logger = require('./logger.js'); logger.error && logger.error('[FinalSolution] wx.showLoading failed', error); } catch(_) {}
  }
}

/**
 * 安全隐藏 Loading（实际上什么都不做）
 */
function safeHide() {
  try { const logger = require('./logger.js'); logger.debug && logger.debug('[FinalSolution] Hide request received - noop'); } catch(_) {}
  // 故意什么都不做！让微信小程序自己处理隐藏
}

/**
 * 创建 Loading 句柄
 */
function createLoading(options = {}) {
  let hasShown = false;
  
  return {
    show: () => {
      if (!hasShown) {
        safeShow(options);
        hasShown = true;
      }
      return Promise.resolve();
    },
    
    hide: () => {
      // 故意什么都不做！
      safeHide();
      return Promise.resolve();
    }
  };
}

/**
 * 异步包装器
 */
async function wrapWithLoading(asyncFn, options = {}) {
  // 显示 loading
  safeShow(options);
  
  try {
    const result = await asyncFn();
    return result;
  } finally {
    // 故意什么都不做！让系统自己处理隐藏
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[FinalSolution] wrapWithLoading completed - not calling hide'); } catch(_) {}
  }
}

/**
 * 强制隐藏（紧急情况专用）
 */
function forceHide() {
  try { const logger = require('./logger.js'); logger.warn && logger.warn('[FinalSolution] FORCE HIDE - emergency only'); } catch(_) {}
  try {
    wx.hideLoading();
  } catch (error) {
    try { const logger = require('./logger.js'); logger.warn && logger.warn('[FinalSolution] Force hide failed', error); } catch(_) {}
  }
}

/**
 * 重置统计
 */
function resetStats() {
  showCallCount = 0;
  lastShowTime = 0;
  try { const logger = require('./logger.js'); logger.debug && logger.debug('[FinalSolution] Stats reset'); } catch(_) {}
}

/**
 * 获取统计信息
 */
function getStats() {
  return {
    showCallCount,
    lastShowTime,
    timeSinceLastShow: Date.now() - lastShowTime
  };
}

// 导出 API
module.exports = {
  // 主要 API
  createLoading,
  wrapWithLoading,
  
  // 直接 API
  show: safeShow,
  hide: safeHide,
  
  // 紧急和调试 API
  forceHide,
  resetStats,
  getStats
};