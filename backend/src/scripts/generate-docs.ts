import fs from "fs";
import path from "path";
import { Documentation } from "express-zod-api";
import { zodConfig } from "../config/zod-config";
import { routing } from "../config/routing";

// 生成API文档
const generateDocs = async () => {
  try {
    console.log("📚 开始生成API文档...");

    // 创建文档生成器
    const documentation = new Documentation({
      routing,
      config: zodConfig,
      version: "2.0.0",
      title: "智慧养鹅SaaS平台 API v2",
      serverUrl: process.env.API_BASE_URL || "http://localhost:3001",
      composition: "components",

      // 自定义描述生成器
      descriptions: {
        positiveResponse: ({ method, path }) =>
          `${method.toUpperCase()} ${path} 成功响应`,
        negativeResponse: ({ method, path }) =>
          `${method.toUpperCase()} ${path} 错误响应`,
        requestBody: ({ operationId }) => `${operationId} 请求体`,
        requestParameter: () => "请求参数",
      },
    });

    // 生成OpenAPI YAML文档
    const yamlContent = documentation.getSpecAsYaml();
    const yamlPath = path.join(__dirname, "../../docs/api-v2.yaml");

    // 确保目录存在
    const docsDir = path.dirname(yamlPath);
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }

    // 写入YAML文件
    fs.writeFileSync(yamlPath, yamlContent, "utf8");
    console.log(`✅ OpenAPI YAML文档已生成: ${yamlPath}`);

    // 生成JSON格式
    const jsonContent = JSON.stringify(documentation.getSpecAsJson(), null, 2);
    const jsonPath = path.join(__dirname, "../../docs/api-v2.json");
    fs.writeFileSync(jsonPath, jsonContent, "utf8");
    console.log(`✅ OpenAPI JSON文档已生成: ${jsonPath}`);

    // 生成Markdown文档
    try {
      // 这里可以添加Markdown文档生成逻辑
      console.log("📖 可以使用第三方工具将OpenAPI转换为Markdown");
      console.log(
        "   例如: redoc-cli build docs/api-v2.yaml --output docs/api-v2.html",
      );
    } catch (error) {
      console.warn("⚠️ Markdown文档生成跳过:", error);
    }

    console.log("🎉 API文档生成完成！");
  } catch (error) {
    console.error("❌ 文档生成失败:", error);
    process.exit(1);
  }
};

// 运行文档生成
if (require.main === module) {
  generateDocs();
}

export { generateDocs };
