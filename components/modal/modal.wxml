<!-- components/modal/modal.wxml -->
<view 
  wx:if="{{innerVisible}}"
  class="modal-container {{customClass}}"
  style="z-index: {{zIndex}};">
  
  <!-- 遮罩层 -->
  <view 
    class="modal-mask"
    animation="{{maskAnimationData}}"
    bindtap="onMaskTap">
  </view>

  <!-- 弹窗主体 -->
  <view 
    class="modal-wrapper"
    bindtap="onModalTap">
    <view 
      class="modal-content modal-{{type}}"
      style="width: {{width}}; max-width: {{maxWidth}};"
      animation="{{animationData}}">
      
      <!-- 关闭按钮 -->
      <view 
        wx:if="{{showClose}}"
        class="modal-close"
        bindtap="onClose">
        <text class="close-icon">×</text>
      </view>

      <!-- 标题区域 -->
      <view wx:if="{{title}}" class="modal-header">
        <text class="modal-title">{{title}}</text>
      </view>

      <!-- 内容区域 -->
      <view class="modal-body">
        <!-- 预设类型内容 -->
        <view wx:if="{{type !== 'custom'}}" class="modal-text">
          <text>{{content}}</text>
        </view>
        
        <!-- 自定义内容插槽 -->
        <view wx:else class="modal-slot">
          <slot></slot>
        </view>
      </view>

      <!-- 底部按钮区域 （除了alert类型） -->
      <view wx:if="{{type !== 'alert' && type !== 'custom'}}" class="modal-footer">
        <button 
          wx:if="{{showCancel}}"
          class="modal-btn cancel-btn"
          bindtap="onCancel">
          {{cancelText}}
        </button>
        
        <button 
          class="modal-btn confirm-btn"
          bindtap="onConfirm">
          {{confirmText}}
        </button>
      </view>

      <!-- Alert类型只有确认按钮 -->
      <view wx:elif="{{type === 'alert'}}" class="modal-footer single-btn">
        <button 
          class="modal-btn confirm-btn full-width"
          bindtap="onConfirm">
          {{confirmText}}
        </button>
      </view>

      <!-- 自定义类型的底部插槽 -->
      <view wx:elif="{{type === 'custom'}}" class="modal-footer-slot">
        <slot name="footer"></slot>
      </view>
    </view>
  </view>
</view>