// backend/models/audit-log.model.js
// 操作日志模型

const { DataTypes } = require("sequelize");
const { sequelize } = require("../config/database");

const AuditLog = sequelize.define(
  "AuditLog",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "user_id",
      comment: "操作用户ID",
    },
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "flock_id",
      comment: "关联鹅群ID",
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: "操作用户名",
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: "操作类型",
    },
    resource: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "操作资源",
    },
    resourceId: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: "resource_id",
      comment: "资源ID",
    },
    method: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: "HTTP方法",
    },
    url: {
      type: DataTypes.STRING(500),
      allowNull: false,
      comment: "请求URL",
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: "user_agent",
      comment: "用户代理",
    },
    ipAddress: {
      type: DataTypes.STRING(45),
      allowNull: true,
      field: "ip_address",
      comment: "IP地址",
    },
    requestData: {
      type: DataTypes.JSON,
      allowNull: true,
      field: "request_data",
      comment: "请求数据（脱敏后）",
    },
    responseStatus: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "response_status",
      comment: "响应状态码",
    },
    responseData: {
      type: DataTypes.JSON,
      allowNull: true,
      field: "response_data",
      comment: "响应数据（脱敏后）",
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "请求耗时（毫秒）",
    },
    success: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: "操作是否成功",
    },
    errorMessage: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: "error_message",
      comment: "错误信息",
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "操作描述",
    },
    sessionId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: "session_id",
      comment: "会话ID",
    },
    level: {
      type: DataTypes.ENUM("INFO", "WARN", "ERROR", "CRITICAL"),
      allowNull: false,
      defaultValue: "INFO",
      comment: "日志级别",
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: "操作分类",
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: "标签",
    },
    details: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: "详细信息",
    },
  },
  {
    tableName: "audit_logs",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        fields: ["user_id"],
      },
      {
        fields: ["action"],
      },
      {
        fields: ["resource"],
      },
      {
        fields: ["created_at"],
      },
      {
        fields: ["ip_address"],
      },
      {
        fields: ["success"],
      },
      {
        fields: ["level"],
      },
      {
        fields: ["category"],
      },
    ],
  },
);

// 定义操作类型常量
AuditLog.ACTIONS = {
  // 认证相关
  LOGIN: "LOGIN",
  LOGOUT: "LOGOUT",
  REGISTER: "REGISTER",
  CHANGE_PASSWORD: "CHANGE_PASSWORD",

  // CRUD操作
  CREATE: "CREATE",
  READ: "READ",
  UPDATE: "UPDATE",
  DELETE: "DELETE",
  BATCH_DELETE: "BATCH_DELETE",

  // 管理操作
  ADMIN_LOGIN: "ADMIN_LOGIN",
  USER_MANAGEMENT: "USER_MANAGEMENT",
  SYSTEM_CONFIG: "SYSTEM_CONFIG",

  // 业务操作
  HEALTH_RECORD: "HEALTH_RECORD",
  PRODUCTION_RECORD: "PRODUCTION_RECORD",
  AI_DIAGNOSIS: "AI_DIAGNOSIS",

  // 安全操作
  PERMISSION_CHANGE: "PERMISSION_CHANGE",
  ROLE_CHANGE: "ROLE_CHANGE",
  SECURITY_ALERT: "SECURITY_ALERT",
};

// 定义资源类型常量
AuditLog.RESOURCES = {
  USER: "USER",
  HEALTH_RECORD: "HEALTH_RECORD",
  PRODUCTION_RECORD: "PRODUCTION_RECORD",
  INVENTORY: "INVENTORY",
  ANNOUNCEMENT: "ANNOUNCEMENT",
  KNOWLEDGE_BASE: "KNOWLEDGE_BASE",
  AI_CONFIG: "AI_CONFIG",
  SYSTEM: "SYSTEM",
};

// 定义日志级别常量
AuditLog.LEVELS = {
  INFO: "INFO",
  WARN: "WARN",
  ERROR: "ERROR",
  CRITICAL: "CRITICAL",
};

// 静态方法：创建操作日志
AuditLog.createLog = async function (logData) {
  try {
    return await this.create(logData);
  } catch (error) {
    console.error("创建操作日志失败:", error);
    // 日志记录失败不应该影响主业务流程
    return null;
  }
};

// 静态方法：批量创建操作日志
AuditLog.createBatchLogs = async function (logsData) {
  try {
    return await this.bulkCreate(logsData);
  } catch (error) {
    console.error("批量创建操作日志失败:", error);
    return null;
  }
};

// 静态方法：查询操作日志
AuditLog.findLogs = async function (options = {}) {
  const {
    userId,
    action,
    resource,
    startDate,
    endDate,
    level,
    success,
    page = 1,
    limit = 50,
  } = options;

  const where = {};

  if (userId) where.userId = userId;
  if (action) where.action = action;
  if (resource) where.resource = resource;
  if (level) where.level = level;
  if (success !== undefined) where.success = success;

  if (startDate || endDate) {
    where.created_at = {};
    if (startDate) where.created_at[sequelize.Sequelize.Op.gte] = startDate;
    if (endDate) where.created_at[sequelize.Sequelize.Op.lte] = endDate;
  }

  const offset = (page - 1) * limit;

  return await this.findAndCountAll({
    where,
    limit,
    offset,
    order: [["created_at", "DESC"]],
  });
};

// 静态方法：清理过期日志
AuditLog.cleanupOldLogs = async function (daysToKeep = 90) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  try {
    const deletedCount = await this.destroy({
      where: {
        created_at: {
          [sequelize.Sequelize.Op.lt]: cutoffDate,
        },
      },
    });

    console.log(`清理了 ${deletedCount} 条过期操作日志`);
    return deletedCount;
  } catch (error) {
    console.error("清理过期操作日志失败:", error);
    return 0;
  }
};

module.exports = AuditLog;
