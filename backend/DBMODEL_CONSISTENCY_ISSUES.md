# 数据库模型一致性问题报告

## 🚨 发现的主要问题

### 1. 库存管理模型冲突

**问题描述**: 存在三个不同的库存相关模型，操作不同的数据表

| 模型文件                     | 表名                | 用途         | 状态        |
| ---------------------------- | ------------------- | ------------ | ----------- |
| `material.model.js`          | `materials`         | 简化物料管理 | ⚠️ 功能重复 |
| `inventory.model.js`         | `inventory`         | 完整库存管理 | ⚠️ 功能重复 |
| `unified-inventory.model.js` | `unified_inventory` | 统一库存管理 | ✅ 推荐使用 |

**风险影响**:

- 数据分散在多个表中
- API调用时可能操作错误的表
- 数据同步问题
- 维护复杂度增加

### 2. 健康记录模型字段不匹配

**Sequelize模型** (`health-record.model.js`):

```javascript
gooseId: {
  type: DataTypes.STRING,
  allowNull: false,
  field: 'goose_id'
}
```

**数据库表字段**: 需要确认health_records表的具体结构

### 3. 数据库命名规范不一致

**问题示例**:

```sql
-- SQL表定义使用驼峰命名（不规范）
userId INT NOT NULL COMMENT '用户ID',
totalCount INT NOT NULL DEFAULT 0 COMMENT '总数量',

-- 应该使用下划线命名
user_id INT NOT NULL COMMENT '用户ID',
total_count INT NOT NULL DEFAULT 0 COMMENT '总数量',
```

## 🔧 修复方案

### 方案1: 统一库存模型

1. **废弃重复模型**:
   - 删除 `material.model.js`
   - 删除 `inventory.model.js`
   - 保留 `unified-inventory.model.js`

2. **数据迁移**:
   - 将 `materials` 表数据迁移到 `unified_inventory`
   - 将 `inventory` 表数据迁移到 `unified_inventory`
   - 删除重复表

3. **API更新**:
   - 更新所有API路由使用统一模型
   - 修改前端API调用

### 方案2: 数据库命名规范化

1. **字段命名标准化**:
   - 所有数据库字段使用下划线命名
   - Sequelize模型使用field映射

2. **迁移脚本**:
   ```sql
   ALTER TABLE flocks CHANGE userId user_id INT NOT NULL;
   ALTER TABLE flocks CHANGE totalCount total_count INT NOT NULL;
   -- ... 更多字段
   ```

### 方案3: 健康记录模型修复

1. **确认字段类型**:
   - 检查health_records表的实际结构
   - 确保gooseId字段类型匹配

2. **补充缺失字段**:
   - 模型中添加数据库存在但模型缺失的字段

## 📋 执行优先级

1. **高优先级**: 统一库存模型（影响数据一致性）
2. **中优先级**: 健康记录字段匹配（影响功能完整性）
3. **低优先级**: 命名规范化（影响代码规范性）

## ⚠️ 风险提示

- 数据迁移前必须备份数据库
- 需要测试所有相关API接口
- 前端可能需要同步更新
- 建议在开发环境先验证

---

_生成时间: ${new Date().toISOString()}_
_建议立即处理高优先级问题以确保数据一致性_
