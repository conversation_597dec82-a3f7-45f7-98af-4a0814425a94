<!-- pages/order-detail/order-detail.wxml - 重新设计的订单详情页面 -->
<view class="order-detail-page">
  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载订单详情中...</text>
  </view>

  <!-- 订单详情内容 -->
  <scroll-view class="detail-content" scroll-y="true" wx:else>
    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-icon status-{{orderInfo.status}}">
        <text class="icon-text">{{orderInfo.status === 'pending' ? '⏳' : orderInfo.status === 'paid' ? '📦' : orderInfo.status === 'shipped' ? '🚚' : orderInfo.status === 'completed' ? '✅' : '❌'}}</text>
      </view>
      <view class="status-info">
        <text class="status-title">{{orderInfo.statusText}}</text>
        <text class="status-desc">{{orderInfo.status === 'pending' ? '请尽快完成支付' : orderInfo.status === 'paid' ? '商家正在准备发货' : orderInfo.status === 'shipped' ? '商品正在运输途中' : orderInfo.status === 'completed' ? '订单已完成，感谢您的购买' : '订单已取消'}}</text>
      </view>
    </view>

    <!-- 物流信息 -->
    <view class="logistics-section" wx:if="{{orderInfo.status === 'shipped' || orderInfo.status === 'completed'}}">
      <view class="section-header">
        <text class="section-icon">🚚</text>
        <text class="section-title">物流信息</text>
      </view>
      <view class="logistics-info" bindtap="onTrackOrder">
        <view class="logistics-company">
          <text class="company-name">{{orderInfo.logistics.company || '顺丰速运'}}</text>
          <text class="tracking-number">{{orderInfo.logistics.trackingNumber || 'SF1234567890'}}</text>
        </view>
        <view class="logistics-status">
          <text class="status-text">{{orderInfo.logistics.status || '运输中'}}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-section">
      <view class="section-header">
        <text class="section-icon">📍</text>
        <text class="section-title">收货地址</text>
      </view>
      <view class="address-info">
        <view class="address-contact">
          <text class="contact-name">{{orderInfo.address.name}}</text>
          <text class="contact-phone">{{orderInfo.address.phone}}</text>
        </view>
        <text class="address-detail">{{orderInfo.address.detail}}</text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="goods-section">
      <view class="section-header">
        <text class="section-icon">🏪</text>
        <text class="section-title">智慧养鹅官方旗舰店</text>
      </view>
      
      <view class="goods-list">
        <view class="goods-item" wx:for="{{orderInfo.goods}}" wx:key="id">
          <image class="goods-image" src="{{item.image}}" mode="aspectFill" lazy-load="true"></image>
          <view class="goods-info">
            <text class="goods-name">{{item.name}}</text>
            <view class="goods-specs" wx:if="{{item.specs}}">
              <text class="specs-text">已选：{{item.specs}}</text>
            </view>
            <view class="goods-tags" wx:if="{{item.tags && item.tags.length > 0}}">
              <text class="goods-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
            </view>
            <view class="goods-price-qty">
              <text class="goods-price">¥{{item.price}}</text>
              <text class="goods-qty">×{{item.quantity}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-section">
      <view class="section-header">
        <text class="section-icon">📋</text>
        <text class="section-title">订单信息</text>
      </view>
      
      <view class="order-details">
        <view class="detail-item">
          <text class="detail-label">订单编号</text>
          <view class="detail-value">
            <text class="order-number">{{orderInfo.orderNumber}}</text>
            <text class="copy-btn" bindtap="onCopyOrderNumber">复制</text>
          </view>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">下单时间</text>
          <text class="detail-value">{{orderInfo.createTime}}</text>
        </view>
        
        <view class="detail-item" wx:if="{{orderInfo.payTime}}">
          <text class="detail-label">付款时间</text>
          <text class="detail-value">{{orderInfo.payTime}}</text>
        </view>
        
        <view class="detail-item" wx:if="{{orderInfo.shipTime}}">
          <text class="detail-label">发货时间</text>
          <text class="detail-value">{{orderInfo.shipTime}}</text>
        </view>
        
        <view class="detail-item" wx:if="{{orderInfo.receiveTime}}">
          <text class="detail-label">收货时间</text>
          <text class="detail-value">{{orderInfo.receiveTime}}</text>
        </view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="price-section">
      <view class="section-header">
        <text class="section-icon">💰</text>
        <text class="section-title">费用明细</text>
      </view>
      
      <view class="price-details">
        <view class="price-item">
          <text class="price-label">商品总价</text>
          <text class="price-value">¥{{orderInfo.totalPrice}}</text>
        </view>
        
        <view class="price-item">
          <text class="price-label">运费</text>
          <text class="price-value">¥{{orderInfo.shippingFee || '0.00'}}</text>
        </view>
        
        <view class="price-item" wx:if="{{orderInfo.discountAmount > 0}}">
          <text class="price-label">优惠金额</text>
          <text class="price-value discount">-¥{{orderInfo.discountAmount}}</text>
        </view>
        
        <view class="price-item total">
          <text class="price-label">实付金额</text>
          <text class="price-value">¥{{orderInfo.actualAmount || orderInfo.totalPrice}}</text>
        </view>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="bottom-placeholder"></view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{!loading}}">
    <view class="contact-service" bindtap="onContactService">
      <text class="contact-icon">💬</text>
      <text class="contact-text">客服</text>
    </view>
    
    <view class="action-buttons">
      <!-- 待付款状态 -->
      <block wx:if="{{orderInfo.status === 'pending'}}">
        <button class="action-btn secondary" bindtap="onCancelOrder">取消订单</button>
        <button class="action-btn primary" bindtap="onPayOrder">立即付款</button>
      </block>
      
      <!-- 待发货状态 -->
      <block wx:elif="{{orderInfo.status === 'paid'}}">
        <button class="action-btn secondary" bindtap="onContactService">联系商家</button>
      </block>
      
      <!-- 待收货状态 -->
      <block wx:elif="{{orderInfo.status === 'shipped'}}">
        <button class="action-btn secondary" bindtap="onTrackOrder">查看物流</button>
        <button class="action-btn primary" bindtap="onConfirmOrder">确认收货</button>
      </block>
      
      <!-- 已完成状态 -->
      <block wx:elif="{{orderInfo.status === 'completed'}}">
        <button class="action-btn secondary" bindtap="onReviewOrder">评价订单</button>
        <button class="action-btn primary" bindtap="onRebuyOrder">再次购买</button>
      </block>
      
      <!-- 已取消状态 -->
      <block wx:elif="{{orderInfo.status === 'cancelled'}}">
        <button class="action-btn secondary" bindtap="onDeleteOrder">删除订单</button>
        <button class="action-btn primary" bindtap="onRebuyOrder">再次购买</button>
      </block>
    </view>
  </view>
</view>

<!-- 发票选择模态框 -->
<view class="invoice-modal" wx:if="{{showInvoiceModal}}">
  <view class="modal-mask" bindtap="onCloseInvoiceModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">选择发票类型</text>
      <text class="modal-close" bindtap="onCloseInvoiceModal">×</text>
    </view>
    <view class="modal-body">
      <view class="invoice-option" bindtap="onInvoiceTypeSelect" data-type="none">
        <text class="option-text">不开发票</text>
        <text class="option-check" wx:if="{{invoiceData.type === 'none'}}">✓</text>
      </view>
      <view class="invoice-option" bindtap="onInvoiceTypeSelect" data-type="personal">
        <text class="option-text">个人发票</text>
        <text class="option-check" wx:if="{{invoiceData.type === 'personal'}}">✓</text>
      </view>
      <view class="invoice-option" bindtap="onInvoiceTypeSelect" data-type="company">
        <text class="option-text">企业发票</text>
        <text class="option-check" wx:if="{{invoiceData.type === 'company'}}">✓</text>
      </view>
    </view>
  </view>
</view>