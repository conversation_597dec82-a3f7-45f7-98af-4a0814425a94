<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告管理 - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f5f7fb; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .announcement-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #fd7e14;
        }
        .announcement-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .announcement-item.important {
            border-left: 4px solid #dc3545;
        }
        .announcement-item.normal {
            border-left: 4px solid #0dcaf0;
        }
        .announcement-item.draft {
            border-left: 4px solid #6c757d;
        }
    </style>
</head>
<body>
    <%- include('partials/navbar') %>
    <div class="container-fluid">
        <div class="row">
            <%- include('partials/sidebar') %>
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0" id="page-title">公告管理</h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-3">在线</span>
                            <i class="bi bi-bell fs-5 me-3"></i>
                            <i class="bi bi-gear fs-5"></i>
                        </div>
                    </div>
                </nav>
                <div class="container-fluid mt-4">
                    <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><i class="bi bi-megaphone me-2"></i>公告管理</h1>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAnnouncementModal">
                                <i class="bi bi-plus"></i> 发布公告
                            </button>
                        </div>
                    </div>
                    
                    <!-- 公告统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="announcement-card">
                                <h6 class="text-muted">总公告数</h6>
                                <h3 class="text-info">28</h3>
                                <small class="text-muted">条公告</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="announcement-card" style="border-left-color: #28a745;">
                                <h6 class="text-muted">已发布</h6>
                                <h3 class="text-success">22</h3>
                                <small class="text-muted">条公告</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="announcement-card" style="border-left-color: #6c757d;">
                                <h6 class="text-muted">草稿</h6>
                                <h3 class="text-secondary">6</h3>
                                <small class="text-muted">条草稿</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="announcement-card" style="border-left-color: #dc3545;">
                                <h6 class="text-muted">重要公告</h6>
                                <h3 class="text-danger">4</h3>
                                <small class="text-muted">条重要</small>
                            </div>
                        </div>
                    </div>

                    <!-- 快速筛选 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" placeholder="搜索公告标题或内容...">
                                        </div>
                                        <div class="col-md-2">
                                            <select class="form-select">
                                                <option>全部状态</option>
                                                <option>已发布</option>
                                                <option>草稿</option>
                                                <option>已下线</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <select class="form-select">
                                                <option>全部类型</option>
                                                <option>系统通知</option>
                                                <option>活动公告</option>
                                                <option>重要通告</option>
                                                <option>维护通知</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <select class="form-select">
                                                <option>全部租户</option>
                                                <option>绿野生态农场</option>
                                                <option>明珠养殖基地</option>
                                                <option>天源农牧</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="btn-group">
                                                <button class="btn btn-outline-primary">搜索</button>
                                                <button class="btn btn-outline-secondary">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 公告列表 -->
                    <div class="row">
                        <!-- 重要公告 -->
                        <div class="col-md-12">
                            <div class="announcement-item important">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge bg-danger me-2">重要</span>
                                            <span class="badge bg-primary me-2">系统通知</span>
                                            <h5 class="mb-0">系统维护通知</h5>
                                        </div>
                                        <p class="text-muted mb-2">
                                            系统将于2024年1月20日凌晨2:00-4:00进行例行维护升级，期间可能影响部分功能使用。维护期间，数据同步、报表生成等功能可能暂时不可用...
                                        </p>
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="bi bi-person me-1"></i>
                                            <span class="me-3">系统管理员</span>
                                            <i class="bi bi-calendar me-1"></i>
                                            <span class="me-3">2024-01-15 14:30</span>
                                            <i class="bi bi-eye me-1"></i>
                                            <span class="me-3">156 次查看</span>
                                            <i class="bi bi-geo-alt me-1"></i>
                                            <span>全部租户</span>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> 预览
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-pause"></i> 下线
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                                <i class="bi bi-share"></i> 推送
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 普通公告 -->
                        <div class="col-md-12">
                            <div class="announcement-item normal">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge bg-info me-2">通知</span>
                                            <span class="badge bg-success me-2">活动公告</span>
                                            <h5 class="mb-0">春季养殖技术培训班开始报名</h5>
                                        </div>
                                        <p class="text-muted mb-2">
                                            为提升养殖户的专业技术水平，平台将组织春季养殖技术培训班。本次培训将邀请行业专家进行授课，涵盖疾病防控、营养管理、生产效率提升等内容...
                                        </p>
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="bi bi-person me-1"></i>
                                            <span class="me-3">运营部</span>
                                            <i class="bi bi-calendar me-1"></i>
                                            <span class="me-3">2024-01-14 10:20</span>
                                            <i class="bi bi-eye me-1"></i>
                                            <span class="me-3">89 次查看</span>
                                            <i class="bi bi-geo-alt me-1"></i>
                                            <span>指定租户</span>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> 预览
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning">
                                                <i class="bi bi-clock"></i> 定时
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                                <i class="bi bi-share"></i> 推送
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 草稿公告 -->
                        <div class="col-md-12">
                            <div class="announcement-item draft">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge bg-secondary me-2">草稿</span>
                                            <span class="badge bg-warning me-2">维护通知</span>
                                            <h5 class="mb-0">平台功能优化升级预告</h5>
                                        </div>
                                        <p class="text-muted mb-2">
                                            为了提供更好的用户体验，平台将进行功能优化升级。本次升级将新增AI诊断功能、优化数据分析模块、增强系统安全性...
                                        </p>
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="bi bi-person me-1"></i>
                                            <span class="me-3">产品部</span>
                                            <i class="bi bi-calendar me-1"></i>
                                            <span class="me-3">2024-01-13 16:45</span>
                                            <i class="bi bi-eye me-1"></i>
                                            <span class="me-3">- 次查看</span>
                                            <i class="bi bi-geo-alt me-1"></i>
                                            <span>待定</span>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> 预览
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                                <i class="bi bi-check"></i> 发布
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <span class="page-link">上一页</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <!-- 发布公告模态框 -->
    <div class="modal fade" id="addAnnouncementModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">发布公告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">公告标题</label>
                                    <input type="text" class="form-control" placeholder="请输入公告标题">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">公告类型</label>
                                    <select class="form-select">
                                        <option>系统通知</option>
                                        <option>活动公告</option>
                                        <option>重要通告</option>
                                        <option>维护通知</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">优先级</label>
                                    <select class="form-select">
                                        <option>普通</option>
                                        <option>重要</option>
                                        <option>紧急</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">发布时间</label>
                                    <input type="datetime-local" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">有效期至</label>
                                    <input type="date" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">发布范围</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="scope" id="scope1" checked>
                                        <label class="form-check-label" for="scope1">
                                            全部租户
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="scope" id="scope2">
                                        <label class="form-check-label" for="scope2">
                                            指定租户
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select" multiple>
                                        <option>绿野生态农场</option>
                                        <option>明珠养殖基地</option>
                                        <option>天源农牧有限公司</option>
                                        <option>金羽养殖场</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">公告内容</label>
                            <textarea class="form-control" rows="8" placeholder="请输入公告内容..."></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">附件上传</label>
                                    <input type="file" class="form-control" multiple>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">公告选项</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="option1">
                                        <label class="form-check-label" for="option1">
                                            置顶显示
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="option2">
                                        <label class="form-check-label" for="option2">
                                            微信推送
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="option3">
                                        <label class="form-check-label" for="option3">
                                            邮件通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-outline-secondary">保存草稿</button>
                    <button type="button" class="btn btn-warning">定时发布</button>
                    <button type="button" class="btn btn-success">立即发布</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>