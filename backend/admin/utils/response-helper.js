/**
 * 响应助手工具类
 * 统一处理页面渲染和API响应
 */

/**
 * 渲染页面的统一方法
 * @param {Object} res Express响应对象
 * @param {string} template 模板路径
 * @param {Object} options 渲染选项
 * @param {string} options.title 页面标题
 * @param {Object} options.data 页面数据
 * @param {Object} options.user 当前用户信息
 * @param {string} options.currentPage 当前页面标识
 */
function renderPage(res, template, options = {}) {
  const defaultOptions = {
    title: "智慧养鹅管理系统",
    data: {},
    user: null,
    currentPage: "",
    ...options,
  };

  res.render(template, defaultOptions);
}

/**
 * 渲染错误页面
 * @param {Object} res Express响应对象
 * @param {Error|Object} error 错误对象
 * @param {number} statusCode HTTP状态码
 * @param {string} customMessage 自定义错误消息
 */
function renderError(res, error, statusCode = 500, customMessage = null) {
  const errorData = {
    title: "错误 - 智慧养鹅管理系统",
    error: {
      status: statusCode,
      message: customMessage || error.message || "服务器内部错误",
      stack: process.env.NODE_ENV === "development" ? error.stack : "",
    },
  };

  res.status(statusCode).render("error", errorData);
}

/**
 * 返回成功的API响应
 * @param {Object} res Express响应对象
 * @param {*} data 响应数据
 * @param {string} message 成功消息
 * @param {Object} meta 元数据（如分页信息）
 */
function successResponse(res, data = null, message = "操作成功", meta = {}) {
  const response = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString(),
    ...meta,
  };

  res.json(response);
}

/**
 * 返回错误的API响应
 * @param {Object} res Express响应对象
 * @param {string} message 错误消息
 * @param {number} statusCode HTTP状态码
 * @param {*} data 错误相关数据
 * @param {string} errorCode 错误代码
 */
function errorResponse(
  res,
  message = "操作失败",
  statusCode = 400,
  data = null,
  errorCode = null,
) {
  const response = {
    success: false,
    message,
    data,
    errorCode,
    timestamp: new Date().toISOString(),
  };

  res.status(statusCode).json(response);
}

/**
 * 处理异步路由的包装器
 * 自动捕获异步错误并统一处理
 * @param {Function} fn 异步路由处理函数
 * @returns {Function} 包装后的路由处理函数
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 通用的分页处理
 * @param {Object} query 查询参数
 * @returns {Object} 分页配置
 */
function getPaginationConfig(query) {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 10));
  const offset = (page - 1) * limit;

  return {
    page,
    limit,
    offset,
    getMetadata: (total) => ({
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    }),
  };
}

/**
 * 参数验证助手
 * @param {Object} data 要验证的数据
 * @param {Object} rules 验证规则
 * @returns {Object} 验证结果
 */
function validateParams(data, rules) {
  const errors = {};

  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field];

    // 必填验证
    if (
      rule.required &&
      (value === undefined || value === null || value === "")
    ) {
      errors[field] = `${rule.label || field}是必填项`;
      continue;
    }

    // 如果字段为空且不是必填，跳过其他验证
    if (
      !rule.required &&
      (value === undefined || value === null || value === "")
    ) {
      continue;
    }

    // 类型验证
    if (rule.type) {
      switch (rule.type) {
        case "email":
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            errors[field] = `${rule.label || field}格式不正确`;
          }
          break;
        case "number":
          if (isNaN(value)) {
            errors[field] = `${rule.label || field}必须是数字`;
          }
          break;
        case "string":
          if (typeof value !== "string") {
            errors[field] = `${rule.label || field}必须是字符串`;
          }
          break;
      }
    }

    // 长度验证
    if (rule.minLength && value.length < rule.minLength) {
      errors[field] = `${rule.label || field}长度不能少于${rule.minLength}位`;
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      errors[field] = `${rule.label || field}长度不能超过${rule.maxLength}位`;
    }

    // 自定义验证函数
    if (rule.validator && typeof rule.validator === "function") {
      const customError = rule.validator(value, data);
      if (customError) {
        errors[field] = customError;
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

/**
 * 日志记录助手
 * @param {string} level 日志级别
 * @param {string} message 日志消息
 * @param {Object} metadata 元数据
 */
function log(level, message, metadata = {}) {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    level: level.toUpperCase(),
    message,
    ...metadata,
  };

  try { const { Logger } = require('../middleware/errorHandler'); Logger.info(JSON.stringify(logData)); } catch(_) {}

}

module.exports = {
  renderPage,
  renderError,
  successResponse,
  errorResponse,
  asyncHandler,
  getPaginationConfig,
  validateParams,
  log,
};
