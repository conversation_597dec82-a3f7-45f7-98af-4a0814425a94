/* pages/shop/reviews.wxss */

.reviews-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 总体评价 */
.rating-overview {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.rating-score-large {
  font-size: 88rpx;
  font-weight: bold;
  color: #ff4757;
  line-height: 1;
}

.rating-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.rating-stars-large {
  display: flex;
  gap: 5rpx;
}

.rating-stars-large .star {
  font-size: 36rpx;
  color: #ddd;
}

.rating-stars-large .star.filled {
  color: #ffd700;
}

.rating-count {
  font-size: 26rpx;
  color: #7f8c8d;
}

/* 评分分布 */
.rating-distribution {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.distribution-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.distribution-item:last-child {
  margin-bottom: 0;
}

.star-label {
  font-size: 26rpx;
  color: #7f8c8d;
  width: 60rpx;
}

.distribution-bar {
  flex: 1;
  height: 16rpx;
  background-color: #ecf0f1;
  border-radius: 8rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffd700 0%, #ff6b35 100%);
  transition: width 0.8s ease;
}

.star-count {
  font-size: 24rpx;
  color: #95a5a6;
  width: 60rpx;
  text-align: right;
}

/* 筛选区域 */
.filter-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.filter-tabs {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 30rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  color: #7f8c8d;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #ff4757;
  color: white;
  transform: scale(1.05);
}

/* 评价列表 */
.reviews-list {
  padding: 0 20rpx;
}

.review-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.reviewer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.reviewer-info {
  flex: 1;
}

.reviewer-name {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.review-date {
  font-size: 24rpx;
  color: #95a5a6;
}

.review-rating {
  display: flex;
  gap: 2rpx;
}

.review-rating .star {
  font-size: 28rpx;
  color: #ddd;
}

.review-rating .star.filled {
  color: #ffd700;
}

.review-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #34495e;
  margin-bottom: 20rpx;
}

.review-images {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.review-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
}

.review-sku {
  margin-bottom: 20rpx;
}

.sku-info {
  font-size: 24rpx;
  color: #7f8c8d;
  background-color: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.review-actions {
  display: flex;
  justify-content: flex-end;
}

.helpful-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.helpful-action:active {
  background-color: #ecf0f1;
  transform: scale(0.95);
}

.helpful-icon {
  font-size: 24rpx;
}

.helpful-text {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.load-more:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.load-more-text {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #bdc3c7;
}

/* 空状态 */
.empty-reviews {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #bdc3c7;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 480rpx) {
  .rating-overview {
    padding: 30rpx 20rpx;
    gap: 20rpx;
  }

  .rating-score-large {
    font-size: 72rpx;
  }

  .rating-distribution {
    padding: 25rpx 20rpx;
  }

  .filter-tabs {
    padding: 15rpx 20rpx;
    gap: 20rpx;
  }

  .reviews-list {
    padding: 0 15rpx;
  }

  .review-item {
    padding: 25rpx 20rpx;
  }
}