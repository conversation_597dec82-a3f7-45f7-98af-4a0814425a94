/**
 * 租户鹅群控制器 - 多租户鹅群管理
 * Tenant Flock Controller - Multi-tenant flock management
 */

/**
 * 获取鹅群列表
 */
exports.getFlocks = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, breed, search } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = "WHERE 1=1";
    const replacements = [];

    if (status) {
      whereClause += " AND status = ?";
      replacements.push(status);
    }

    if (breed) {
      whereClause += " AND breed = ?";
      replacements.push(breed);
    }

    if (search) {
      whereClause += " AND (name LIKE ? OR batchNumber LIKE ?)";
      replacements.push(`%${search}%`, `%${search}%`);
    }

    // 获取总数
    const [countResult] = await req.tenantDb.query(
      `SELECT COUNT(*) as total FROM flocks ${whereClause}`,
      { replacements },
    );
    const total = countResult[0].total;

    // 获取分页数据
    const [flocks] = await req.tenantDb.query(
      `SELECT f.*, u.nickname as ownerName 
       FROM flocks f 
       LEFT JOIN users u ON f.userId = u.id 
       ${whereClause} 
       ORDER BY f.createdAt DESC 
       LIMIT ? OFFSET ?`,
      {
        replacements: [...replacements, parseInt(limit), parseInt(offset)],
      },
    );

    res.json({
      success: true,
      data: {
        flocks,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取鹅群列表失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取鹅群列表失败",
    });
  }
};

/**
 * 获取鹅群详情
 */
exports.getFlockById = async (req, res) => {
  try {
    const { id } = req.params;

    const [flocks] = await req.tenantDb.query(
      `SELECT f.*, u.nickname as ownerName, u.phone as ownerPhone
       FROM flocks f 
       LEFT JOIN users u ON f.userId = u.id 
       WHERE f.id = ?`,
      { replacements: [id] },
    );

    if (flocks.length === 0) {
      return res.status(404).json({
        success: false,
        message: "鹅群不存在",
      });
    }

    const flock = flocks[0];

    // 获取相关的健康记录
    const [healthRecords] = await req.tenantDb.query(
      "SELECT * FROM health_records WHERE flockId = ? ORDER BY checkDate DESC LIMIT 5",
      { replacements: [id] },
    );

    // 获取相关的生产记录
    const [productionRecords] = await req.tenantDb.query(
      "SELECT * FROM production_records WHERE flockId = ? ORDER BY recordDate DESC LIMIT 5",
      { replacements: [id] },
    );

    res.json({
      success: true,
      data: {
        flock,
        healthRecords,
        productionRecords,
      },
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取鹅群详情失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取鹅群详情失败",
    });
  }
};

/**
 * 创建鹅群
 */
exports.createFlock = async (req, res) => {
  try {
    const {
      name,
      batchNumber,
      breed,
      totalCount,
      maleCount,
      femaleCount,
      ageGroup,
      establishedDate,
      location,
      description,
    } = req.body;

    // 验证必填字段
    if (!name || !batchNumber || !breed || !totalCount || !establishedDate) {
      return res.status(400).json({
        success: false,
        message: "缺少必填字段",
      });
    }

    // 检查租户的鹅群数量限制
    const [countResult] = await req.tenantDb.query(
      'SELECT COUNT(*) as total FROM flocks WHERE status = "active"',
    );

    const currentCount = countResult[0].total;
    const maxFlocks = req.tenant.maxFlocks || 5;

    if (currentCount >= maxFlocks) {
      return res.status(403).json({
        success: false,
        message: `当前订阅计划最多支持${maxFlocks}个鹅群，请升级订阅计划`,
        code: "FLOCK_LIMIT_EXCEEDED",
      });
    }

    // 检查批次号是否重复
    const [existingFlock] = await req.tenantDb.query(
      "SELECT id FROM flocks WHERE batchNumber = ?",
      { replacements: [batchNumber] },
    );

    if (existingFlock.length > 0) {
      return res.status(400).json({
        success: false,
        message: "批次号已存在",
      });
    }

    // 创建鹅群
    const [result] = await req.tenantDb.query(
      `INSERT INTO flocks (
        userId, name, batchNumber, breed, totalCount, currentCount, 
        maleCount, femaleCount, ageGroup, establishedDate, location, 
        description, status, createdAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())`,
      {
        replacements: [
          req.userId,
          name,
          batchNumber,
          breed,
          totalCount,
          totalCount, // currentCount初始等于totalCount
          maleCount || 0,
          femaleCount || 0,
          ageGroup || "young",
          establishedDate,
          location || "",
          description || "",
        ],
      },
    );

    res.status(201).json({
      success: true,
      message: "鹅群创建成功",
      data: {
        id: result.insertId,
        name,
        batchNumber,
      },
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("创建鹅群失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "创建鹅群失败",
    });
  }
};

/**
 * 更新鹅群信息
 */
exports.updateFlock = async (req, res) => {
  try {
    const { id } = req.params;
    const updateFields = req.body;

    // 检查鹅群是否存在
    const [existingFlock] = await req.tenantDb.query(
      "SELECT * FROM flocks WHERE id = ?",
      { replacements: [id] },
    );

    if (existingFlock.length === 0) {
      return res.status(404).json({
        success: false,
        message: "鹅群不存在",
      });
    }

    // 如果更新批次号，检查是否重复
    if (updateFields.batchNumber) {
      const [duplicateFlock] = await req.tenantDb.query(
        "SELECT id FROM flocks WHERE batchNumber = ? AND id != ?",
        { replacements: [updateFields.batchNumber, id] },
      );

      if (duplicateFlock.length > 0) {
        return res.status(400).json({
          success: false,
          message: "批次号已存在",
        });
      }
    }

    // 构建更新语句
    const allowedFields = [
      "name",
      "batchNumber",
      "breed",
      "totalCount",
      "currentCount",
      "maleCount",
      "femaleCount",
      "ageGroup",
      "location",
      "description",
      "healthStatus",
      "avgDailyEggProduction",
      "status",
    ];

    const updatePairs = [];
    const values = [];

    for (const [key, value] of Object.entries(updateFields)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updatePairs.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (updatePairs.length === 0) {
      return res.status(400).json({
        success: false,
        message: "没有有效的更新字段",
      });
    }

    updatePairs.push("updatedAt = NOW()");
    values.push(id);

    await req.tenantDb.query(
      `UPDATE flocks SET ${updatePairs.join(", ")} WHERE id = ?`,
      { replacements: values },
    );

    res.json({
      success: true,
      message: "鹅群信息更新成功",
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新鹅群信息失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "更新鹅群信息失败",
    });
  }
};

/**
 * 删除鹅群
 */
exports.deleteFlock = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查鹅群是否存在
    const [existingFlock] = await req.tenantDb.query(
      "SELECT * FROM flocks WHERE id = ?",
      { replacements: [id] },
    );

    if (existingFlock.length === 0) {
      return res.status(404).json({
        success: false,
        message: "鹅群不存在",
      });
    }

    // 软删除（更新状态为deleted）
    await req.tenantDb.query(
      'UPDATE flocks SET status = "deleted", updatedAt = NOW() WHERE id = ?',
      { replacements: [id] },
    );

    res.json({
      success: true,
      message: "鹅群删除成功",
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("删除鹅群失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "删除鹅群失败",
    });
  }
};

/**
 * 获取鹅群统计信息
 */
exports.getFlockStats = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取鹅群基本信息
    const [flocks] = await req.tenantDb.query(
      "SELECT * FROM flocks WHERE id = ?",
      { replacements: [id] },
    );

    if (flocks.length === 0) {
      return res.status(404).json({
        success: false,
        message: "鹅群不存在",
      });
    }

    const flock = flocks[0];

    // 获取健康统计
    const [healthStats] = await req.tenantDb.query(
      `SELECT 
        COUNT(*) as totalChecks,
        SUM(CASE WHEN result = 'healthy' THEN 1 ELSE 0 END) as healthyCount,
        SUM(CASE WHEN result = 'sick' THEN 1 ELSE 0 END) as sickCount
       FROM health_records 
       WHERE flockId = ? AND checkDate >= DATE_SUB(NOW(), INTERVAL 30 DAY)`,
      { replacements: [id] },
    );

    // 获取生产统计（最近30天）
    const [productionStats] = await req.tenantDb.query(
      `SELECT 
        COUNT(*) as totalRecords,
        SUM(eggCount) as totalEggs,
        AVG(eggCount) as avgDailyEggs,
        MAX(eggCount) as maxDailyEggs
       FROM production_records 
       WHERE flockId = ? AND recordDate >= DATE_SUB(NOW(), INTERVAL 30 DAY)`,
      { replacements: [id] },
    );

    // 获取生产趋势（最近7天）
    const [productionTrend] = await req.tenantDb.query(
      `SELECT 
        DATE(recordDate) as date,
        SUM(eggCount) as eggCount
       FROM production_records 
       WHERE flockId = ? AND recordDate >= DATE_SUB(NOW(), INTERVAL 7 DAY)
       GROUP BY DATE(recordDate)
       ORDER BY date`,
      { replacements: [id] },
    );

    res.json({
      success: true,
      data: {
        flock: {
          id: flock.id,
          name: flock.name,
          batchNumber: flock.batchNumber,
          breed: flock.breed,
          totalCount: flock.totalCount,
          currentCount: flock.currentCount,
          healthStatus: flock.healthStatus,
          avgDailyEggProduction: flock.avgDailyEggProduction,
        },
        healthStats: healthStats[0] || {
          totalChecks: 0,
          healthyCount: 0,
          sickCount: 0,
        },
        productionStats: productionStats[0] || {
          totalRecords: 0,
          totalEggs: 0,
          avgDailyEggs: 0,
          maxDailyEggs: 0,
        },
        productionTrend: productionTrend || [],
      },
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取鹅群统计失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取鹅群统计失败",
    });
  }
};
