/**
 * 财务记录控制器
 * 处理财务记录的CRUD操作和业务逻辑
 */

const db = require('../config/database');
const ResponseHelper = require('../utils/responseHelper');

/**
 * 获取财务记录列表
 * GET /api/v1/finance/records
 */
exports.getFinancialRecords = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 20,
      type, // income, expense
      category,
      batchNumber,
      startDate,
      endDate,
      autoGenerated,
      status = 'confirmed'
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = ['userId = ?'];
    let queryParams = [userId];

    // 构建查询条件
    if (type) {
      whereConditions.push('type = ?');
      queryParams.push(type);
    }
    if (category) {
      whereConditions.push('category = ?');
      queryParams.push(category);
    }
    if (batchNumber) {
      whereConditions.push('batch_number = ?');
      queryParams.push(batchNumber);
    }
    if (startDate) {
      whereConditions.push('recordDate >= ?');
      queryParams.push(startDate);
    }
    if (endDate) {
      whereConditions.push('recordDate <= ?');
      queryParams.push(endDate);
    }
    if (autoGenerated !== undefined) {
      whereConditions.push('auto_generated = ?');
      queryParams.push(autoGenerated === 'true');
    }
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询记录
    const [records] = await db.query(`
      SELECT 
        fr.*,
        CASE 
          WHEN fr.related_record_type = 'entry' THEN '入栏记录'
          WHEN fr.related_record_type = 'weight' THEN '称重记录'
          WHEN fr.related_record_type = 'sale' THEN '出栏记录'
          ELSE '手动记录'
        END as related_record_type_name,
        CASE 
          WHEN fr.auto_generated = 1 THEN '自动生成'
          ELSE '手动录入'
        END as generation_type
      FROM financial_records fr
      WHERE ${whereClause}
      ORDER BY recordDate DESC, createdAt DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, parseInt(limit), offset]);

    // 查询总数
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total
      FROM financial_records
      WHERE ${whereClause}
    `, queryParams);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return ResponseHelper.success(res, {
      records,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('获取财务记录失败:', error);
    return ResponseHelper.error(res, '获取财务记录失败', 500, error.message);
  }
};

/**
 * 创建财务记录
 * POST /api/v1/finance/records
 */
exports.createFinancialRecord = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      type, // income, expense
      category,
      amount,
      description,
      recordDate,
      notes,
      batchNumber,
      paymentMethod = 'cash',
      supplier,
      invoiceNumber,
      relatedRecordType,
      relatedRecordId
    } = req.body;

    // 验证必填字段
    if (!type || !category || !amount || !description) {
      return ResponseHelper.error(res, '缺少必填字段', 400);
    }

    if (amount <= 0) {
      return ResponseHelper.error(res, '金额必须大于0', 400);
    }

    // 创建财务记录
    const [result] = await db.query(`
      INSERT INTO financial_records (
        userId, type, category, amount, description, recordDate, notes,
        batch_number, payment_method, supplier, invoice_number,
        related_record_type, related_record_id, auto_generated, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId, type, category, parseFloat(amount), description,
      recordDate || new Date().toISOString().split('T')[0], notes,
      batchNumber, paymentMethod, supplier, invoiceNumber,
      relatedRecordType, relatedRecordId, false, 'confirmed'
    ]);

    // 获取创建的记录
    const [newRecord] = await db.query(`
      SELECT * FROM financial_records WHERE id = ?
    `, [result.insertId]);

    return ResponseHelper.success(res, newRecord[0], '财务记录创建成功', 201);

  } catch (error) {
    console.error('创建财务记录失败:', error);
    return ResponseHelper.error(res, '创建财务记录失败', 500, error.message);
  }
};

/**
 * 自动创建财务记录（由生产记录触发）
 */
exports.createAutoFinancialRecord = async (recordData, transaction = null) => {
  try {
    const {
      userId,
      type, // income, expense
      category,
      amount,
      description,
      recordDate,
      batchNumber,
      supplier,
      relatedRecordType,
      relatedRecordId,
      paymentMethod = 'bank_transfer'
    } = recordData;

    const query = `
      INSERT INTO financial_records (
        userId, type, category, amount, description, recordDate,
        batch_number, supplier, related_record_type, related_record_id,
        auto_generated, payment_method, status, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      userId, type, category, parseFloat(amount), description, recordDate,
      batchNumber, supplier, relatedRecordType, relatedRecordId,
      true, paymentMethod, 'confirmed', '系统自动生成'
    ];

    let result;
    if (transaction) {
      [result] = await transaction.query(query, params);
    } else {
      [result] = await db.query(query, params);
    }

    return {
      success: true,
      recordId: result.insertId
    };

  } catch (error) {
    console.error('自动创建财务记录失败:', error);
    throw error;
  }
};

/**
 * 更新财务记录
 * PUT /api/v1/finance/records/:id
 */
exports.updateFinancialRecord = async (req, res) => {
  try {
    const userId = req.user.id;
    const recordId = req.params.id;
    const {
      type,
      category,
      amount,
      description,
      recordDate,
      notes,
      paymentMethod,
      supplier,
      invoiceNumber,
      status
    } = req.body;

    // 检查记录是否存在且属于当前用户
    const [existingRecord] = await db.query(`
      SELECT * FROM financial_records WHERE id = ? AND userId = ?
    `, [recordId, userId]);

    if (existingRecord.length === 0) {
      return ResponseHelper.error(res, '财务记录不存在', 404);
    }

    // 检查是否为自动生成的记录
    if (existingRecord[0].auto_generated) {
      return ResponseHelper.error(res, '自动生成的记录不能手动修改', 403);
    }

    // 构建更新字段
    const updateFields = [];
    const updateParams = [];

    if (type) {
      updateFields.push('type = ?');
      updateParams.push(type);
    }
    if (category) {
      updateFields.push('category = ?');
      updateParams.push(category);
    }
    if (amount !== undefined) {
      if (amount <= 0) {
        return ResponseHelper.error(res, '金额必须大于0', 400);
      }
      updateFields.push('amount = ?');
      updateParams.push(parseFloat(amount));
    }
    if (description) {
      updateFields.push('description = ?');
      updateParams.push(description);
    }
    if (recordDate) {
      updateFields.push('recordDate = ?');
      updateParams.push(recordDate);
    }
    if (notes !== undefined) {
      updateFields.push('notes = ?');
      updateParams.push(notes);
    }
    if (paymentMethod) {
      updateFields.push('payment_method = ?');
      updateParams.push(paymentMethod);
    }
    if (supplier !== undefined) {
      updateFields.push('supplier = ?');
      updateParams.push(supplier);
    }
    if (invoiceNumber !== undefined) {
      updateFields.push('invoice_number = ?');
      updateParams.push(invoiceNumber);
    }
    if (status) {
      updateFields.push('status = ?');
      updateParams.push(status);
    }

    if (updateFields.length === 0) {
      return ResponseHelper.error(res, '没有需要更新的字段', 400);
    }

    updateFields.push('updatedAt = NOW()');
    updateParams.push(recordId, userId);

    // 执行更新
    await db.query(`
      UPDATE financial_records 
      SET ${updateFields.join(', ')}
      WHERE id = ? AND userId = ?
    `, updateParams);

    // 获取更新后的记录
    const [updatedRecord] = await db.query(`
      SELECT * FROM financial_records WHERE id = ?
    `, [recordId]);

    return ResponseHelper.success(res, updatedRecord[0], '财务记录更新成功');

  } catch (error) {
    console.error('更新财务记录失败:', error);
    return ResponseHelper.error(res, '更新财务记录失败', 500, error.message);
  }
};

/**
 * 删除财务记录
 * DELETE /api/v1/finance/records/:id
 */
exports.deleteFinancialRecord = async (req, res) => {
  try {
    const userId = req.user.id;
    const recordId = req.params.id;

    // 检查记录是否存在且属于当前用户
    const [existingRecord] = await db.query(`
      SELECT * FROM financial_records WHERE id = ? AND userId = ?
    `, [recordId, userId]);

    if (existingRecord.length === 0) {
      return ResponseHelper.error(res, '财务记录不存在', 404);
    }

    // 检查是否为自动生成的记录
    if (existingRecord[0].auto_generated) {
      return ResponseHelper.error(res, '自动生成的记录不能删除', 403);
    }

    // 删除记录
    await db.query(`
      DELETE FROM financial_records WHERE id = ? AND userId = ?
    `, [recordId, userId]);

    return ResponseHelper.success(res, null, '财务记录删除成功');

  } catch (error) {
    console.error('删除财务记录失败:', error);
    return ResponseHelper.error(res, '删除财务记录失败', 500, error.message);
  }
};

/**
 * 获取批次财务统计
 * GET /api/v1/finance/batch-stats/:batchNumber
 */
exports.getBatchFinancialStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const { batchNumber } = req.params;

    // 获取批次财务统计
    const [stats] = await db.query(`
      SELECT
        batch_number,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_cost,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_revenue,
        (SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) -
         SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as net_profit,
        COUNT(*) as record_count,
        MIN(recordDate) as first_record_date,
        MAX(recordDate) as last_record_date
      FROM financial_records
      WHERE batch_number = ? AND userId = ?
      GROUP BY batch_number
    `, [batchNumber, userId]);

    if (stats.length === 0) {
      return ResponseHelper.success(res, {
        batchNumber,
        totalCost: 0,
        totalRevenue: 0,
        netProfit: 0,
        recordCount: 0,
        profitMargin: 0,
        cycleDays: 0
      });
    }

    const stat = stats[0];
    const profitMargin = stat.total_cost > 0 ? (stat.net_profit / stat.total_cost) * 100 : 0;
    const cycleDays = stat.first_record_date && stat.last_record_date ?
      Math.ceil((new Date(stat.last_record_date) - new Date(stat.first_record_date)) / (1000 * 60 * 60 * 24)) : 0;

    // 获取详细记录
    const [records] = await db.query(`
      SELECT
        fr.*,
        CASE
          WHEN fr.related_record_type = 'entry' THEN '入栏记录'
          WHEN fr.related_record_type = 'weight' THEN '称重记录'
          WHEN fr.related_record_type = 'sale' THEN '出栏记录'
          ELSE '手动记录'
        END as related_record_type_name,
        CASE
          WHEN fr.auto_generated = 1 THEN '自动生成'
          ELSE '手动录入'
        END as generation_type
      FROM financial_records fr
      WHERE fr.batch_number = ? AND fr.userId = ?
      ORDER BY fr.recordDate DESC, fr.createdAt DESC
    `, [batchNumber, userId]);

    return ResponseHelper.success(res, {
      batchNumber,
      totalCost: parseFloat(stat.total_cost),
      totalRevenue: parseFloat(stat.total_revenue),
      netProfit: parseFloat(stat.net_profit),
      profitMargin: Math.round(profitMargin * 100) / 100,
      recordCount: stat.record_count,
      firstRecordDate: stat.first_record_date,
      lastRecordDate: stat.last_record_date,
      cycleDays,
      records
    });

  } catch (error) {
    console.error('获取批次财务统计失败:', error);
    return ResponseHelper.error(res, '获取批次财务统计失败', 500, error.message);
  }
};

/**
 * 获取所有批次财务概览
 * GET /api/v1/finance/batch-overview
 */
exports.getBatchFinancialOverview = async (req, res) => {
  try {
    const userId = req.user.id;

    const [batches] = await db.query(`
      SELECT
        fr.batch_number,
        f.name as flock_name,
        f.breed,
        f.totalCount,
        f.currentCount,
        f.status as flock_status,
        SUM(CASE WHEN fr.type = 'expense' THEN fr.amount ELSE 0 END) as total_cost,
        SUM(CASE WHEN fr.type = 'income' THEN fr.amount ELSE 0 END) as total_revenue,
        (SUM(CASE WHEN fr.type = 'income' THEN fr.amount ELSE 0 END) -
         SUM(CASE WHEN fr.type = 'expense' THEN fr.amount ELSE 0 END)) as net_profit,
        COUNT(fr.id) as record_count,
        MIN(fr.recordDate) as first_record_date,
        MAX(fr.recordDate) as last_record_date
      FROM financial_records fr
      LEFT JOIN flocks f ON fr.batch_number = f.batchNumber AND f.userId = fr.userId
      WHERE fr.userId = ? AND fr.batch_number IS NOT NULL
      GROUP BY fr.batch_number, f.name, f.breed, f.totalCount, f.currentCount, f.status
      ORDER BY MAX(fr.recordDate) DESC
    `, [userId]);

    const overview = batches.map(batch => {
      const profitMargin = batch.total_cost > 0 ? (batch.net_profit / batch.total_cost) * 100 : 0;
      const cycleDays = batch.first_record_date && batch.last_record_date ?
        Math.ceil((new Date(batch.last_record_date) - new Date(batch.first_record_date)) / (1000 * 60 * 60 * 24)) : 0;

      return {
        batchNumber: batch.batch_number,
        flockName: batch.flock_name,
        breed: batch.breed,
        totalCount: batch.totalCount,
        currentCount: batch.currentCount,
        flockStatus: batch.flock_status,
        totalCost: parseFloat(batch.total_cost),
        totalRevenue: parseFloat(batch.total_revenue),
        netProfit: parseFloat(batch.net_profit),
        profitMargin: Math.round(profitMargin * 100) / 100,
        recordCount: batch.record_count,
        firstRecordDate: batch.first_record_date,
        lastRecordDate: batch.last_record_date,
        cycleDays
      };
    });

    // 计算总体统计
    const totalStats = overview.reduce((acc, batch) => {
      acc.totalCost += batch.totalCost;
      acc.totalRevenue += batch.totalRevenue;
      acc.totalProfit += batch.netProfit;
      acc.totalRecords += batch.recordCount;
      return acc;
    }, {
      totalCost: 0,
      totalRevenue: 0,
      totalProfit: 0,
      totalRecords: 0
    });

    return ResponseHelper.success(res, {
      batches: overview,
      summary: {
        totalBatches: overview.length,
        totalCost: totalStats.totalCost,
        totalRevenue: totalStats.totalRevenue,
        totalProfit: totalStats.totalProfit,
        overallProfitMargin: totalStats.totalCost > 0 ?
          Math.round((totalStats.totalProfit / totalStats.totalCost) * 10000) / 100 : 0,
        totalRecords: totalStats.totalRecords
      }
    });

  } catch (error) {
    console.error('获取批次财务概览失败:', error);
    return ResponseHelper.error(res, '获取批次财务概览失败', 500, error.message);
  }
};
