// components/form-builder/form-builder.js
const { UI, BUSINESS } = require('../../constants/index.js');

/**
 * 表单构建器组件
 * 支持多种表单控件，自动验证，数据绑定
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 表单配置
    formConfig: {
      type: Object,
      value: {}
    },
    // 表单数据
    formData: {
      type: Object,
      value: {}
    },
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 表单标题
    title: {
      type: String,
      value: '表单'
    },
    // 提交按钮文字
    submitText: {
      type: String,
      value: '提交'
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      value: '取消'
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      value: true
    },
    // 是否正在提交
    submitting: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 表单字段数据
    fields: [],
    // 验证错误
    errors: {},
    // 表单是否有效
    isValid: true,
    // 支持的字段类型
    fieldTypes: {
      'text': 'input',
      'number': 'input',
      'password': 'input',
      'textarea': 'textarea',
      'select': 'picker',
      'date': 'picker',
      'time': 'picker',
      'datetime': 'picker',
      'switch': 'switch',
      'radio': 'radio-group',
      'checkbox': 'checkbox-group',
      'slider': 'slider',
      'rate': 'rate'
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'formConfig': function(newConfig) {
      this.initializeForm(newConfig);
    },
    'formData': function(newData) {
      this.updateFormData(newData);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      try { const logger = require('../../utils/logger.js'); logger.debug && logger.debug('[FormBuilder] 组件初始化'); } catch(_) {}
      if (this.properties.formConfig) {
        this.initializeForm(this.properties.formConfig);
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化表单
     */
    initializeForm(config) {
      if (!config || !config.fields) {
        try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[FormBuilder] 表单配置无效'); } catch(_) {}
        return;
      }

      const fields = config.fields.map(field => ({
        ...field,
        id: field.name || field.id || `field_${Math.random().toString(36).substr(2, 9)}`,
        value: this.getFieldValue(field.name),
        error: '',
        required: field.required || false,
        disabled: field.disabled || false,
        placeholder: field.placeholder || `请输入${field.label}`,
        rules: field.rules || []
      }));

      this.setData({
        fields,
        errors: {}
      });
    },

    /**
     * 更新表单数据
     */
    updateFormData(data) {
      if (!data || !this.data.fields.length) return;

      const updatedFields = this.data.fields.map(field => ({
        ...field,
        value: data[field.name] !== undefined ? data[field.name] : field.value
      }));

      this.setData({
        fields: updatedFields
      });
    },

    /**
     * 获取字段值
     */
    getFieldValue(fieldName) {
      const formData = this.properties.formData || {};
      return formData[fieldName] || '';
    },

    /**
     * 字段值变化处理
     */
    onFieldChange(e) {
      const { field, value } = e.detail;
      const fieldIndex = this.data.fields.findIndex(f => f.id === field.id);
      
      if (fieldIndex === -1) return;

      const updatedFields = [...this.data.fields];
      updatedFields[fieldIndex] = {
        ...updatedFields[fieldIndex],
        value: value,
        error: ''
      };

      this.setData({
        fields: updatedFields
      });

      // 实时验证
      this.validateField(updatedFields[fieldIndex]);

      // 触发数据变化事件
      this.triggerEvent('change', {
        field: field.name,
        value: value,
        formData: this.getFormData()
      });
    },

    /**
     * 字段失焦验证
     */
    onFieldBlur(e) {
      const { field } = e.detail;
      const fieldData = this.data.fields.find(f => f.id === field.id);
      if (fieldData) {
        this.validateField(fieldData);
      }
    },

    /**
     * 验证单个字段
     */
    validateField(field) {
      const errors = { ...this.data.errors };
      const fieldErrors = [];

      // 必填验证
      if (field.required && (!field.value || field.value.toString().trim() === '')) {
        fieldErrors.push(`${field.label}不能为空`);
      }

      // 规则验证
      if (field.value && field.rules && field.rules.length > 0) {
        field.rules.forEach(rule => {
          const error = this.validateRule(field.value, rule, field.label);
          if (error) {
            fieldErrors.push(error);
          }
        });
      }

      if (fieldErrors.length > 0) {
        errors[field.name] = fieldErrors[0]; // 只显示第一个错误
      } else {
        delete errors[field.name];
      }

      this.setData({
        errors,
        isValid: Object.keys(errors).length === 0
      });

      return fieldErrors.length === 0;
    },

    /**
     * 验证规则
     */
    validateRule(value, rule, fieldLabel) {
      switch (rule.type) {
        case 'min':
          if (value.length < rule.value) {
            return rule.message || `${fieldLabel}至少需要${rule.value}个字符`;
          }
          break;
        case 'max':
          if (value.length > rule.value) {
            return rule.message || `${fieldLabel}不能超过${rule.value}个字符`;
          }
          break;
        case 'minLength':
          if (value.toString().length < rule.value) {
            return rule.message || `${fieldLabel}长度不能少于${rule.value}位`;
          }
          break;
        case 'maxLength':
          if (value.toString().length > rule.value) {
            return rule.message || `${fieldLabel}长度不能超过${rule.value}位`;
          }
          break;
        case 'pattern':
          if (!new RegExp(rule.value).test(value)) {
            return rule.message || `${fieldLabel}格式不正确`;
          }
          break;
        case 'email':
          const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailPattern.test(value)) {
            return rule.message || `${fieldLabel}格式不正确`;
          }
          break;
        case 'phone':
          const phonePattern = /^1[3-9]\d{9}$/;
          if (!phonePattern.test(value)) {
            return rule.message || `${fieldLabel}格式不正确`;
          }
          break;
        case 'number':
          if (isNaN(value) || isNaN(parseFloat(value))) {
            return rule.message || `${fieldLabel}必须是数字`;
          }
          break;
        case 'custom':
          if (typeof rule.validator === 'function') {
            const result = rule.validator(value, fieldLabel);
            if (result !== true) {
              return result || rule.message || `${fieldLabel}验证失败`;
            }
          }
          break;
      }
      return null;
    },

    /**
     * 验证整个表单
     */
    validateForm() {
      let isValid = true;
      const errors = {};

      this.data.fields.forEach(field => {
        const valid = this.validateField(field);
        if (!valid) {
          isValid = false;
        }
      });

      return isValid;
    },

    /**
     * 获取表单数据
     */
    getFormData() {
      const formData = {};
      this.data.fields.forEach(field => {
        formData[field.name] = field.value;
      });
      return formData;
    },

    /**
     * 重置表单
     */
    resetForm() {
      const resetFields = this.data.fields.map(field => ({
        ...field,
        value: field.defaultValue || '',
        error: ''
      }));

      this.setData({
        fields: resetFields,
        errors: {},
        isValid: true
      });

      this.triggerEvent('reset');
    },

    /**
     * 设置字段值
     */
    setFieldValue(fieldName, value) {
      const fieldIndex = this.data.fields.findIndex(f => f.name === fieldName);
      if (fieldIndex === -1) return;

      const updatedFields = [...this.data.fields];
      updatedFields[fieldIndex] = {
        ...updatedFields[fieldIndex],
        value: value
      };

      this.setData({
        fields: updatedFields
      });
    },

    /**
     * 设置字段错误
     */
    setFieldError(fieldName, error) {
      const errors = { ...this.data.errors };
      if (error) {
        errors[fieldName] = error;
      } else {
        delete errors[fieldName];
      }

      this.setData({
        errors,
        isValid: Object.keys(errors).length === 0
      });
    },

    /**
     * 提交表单
     */
    onSubmit() {
      if (this.properties.submitting) {
        return;
      }

      const isValid = this.validateForm();
      
      if (isValid) {
        const formData = this.getFormData();
        this.triggerEvent('submit', {
          formData,
          isValid: true
        });
      } else {
        this.triggerEvent('submit', {
          formData: this.getFormData(),
          isValid: false,
          errors: this.data.errors
        });

        wx.showToast({
          title: '请检查表单信息',
          icon: 'error'
        });
      }
    },

    /**
     * 取消表单
     */
    onCancel() {
      this.triggerEvent('cancel');
    },

    /**
     * 关闭表单
     */
    onClose() {
      this.triggerEvent('close');
    },

    /**
     * 处理textarea输入事件
     */
    onTextareaInput(e) {
      const fieldName = e.currentTarget.dataset.name;
      const value = e.detail.value;
      
      const fieldIndex = this.data.fields.findIndex(f => f.name === fieldName);
      if (fieldIndex === -1) return;

      const updatedFields = [...this.data.fields];
      updatedFields[fieldIndex] = {
        ...updatedFields[fieldIndex],
        value: value
      };

      this.setData({
        fields: updatedFields
      });

      // 触发数据变化事件
      this.triggerEvent('change', {
        field: fieldName,
        value: value,
        formData: this.getFormData()
      });
    },

    /**
     * 处理picker选择事件
     */
    onPickerChange(e) {
      const fieldName = e.currentTarget.dataset.name;
      const selectedIndex = e.detail.value;
      
      const fieldIndex = this.data.fields.findIndex(f => f.name === fieldName);
      if (fieldIndex === -1) return;

      const field = this.data.fields[fieldIndex];
      const selectedOption = field.options[selectedIndex];
      const value = selectedOption ? selectedOption.value : '';
      const displayValue = selectedOption ? selectedOption.label : '';

      const updatedFields = [...this.data.fields];
      updatedFields[fieldIndex] = {
        ...updatedFields[fieldIndex],
        value: value,
        displayValue: displayValue,
        selectedIndex: selectedIndex
      };

      this.setData({
        fields: updatedFields
      });

      // 触发数据变化事件
      this.triggerEvent('change', {
        field: fieldName,
        value: value,
        formData: this.getFormData()
      });
    },

    /**
     * 处理日期选择事件
     */
    onDateChange(e) {
      const fieldName = e.currentTarget.dataset.name;
      const value = e.detail.value;
      
      const fieldIndex = this.data.fields.findIndex(f => f.name === fieldName);
      if (fieldIndex === -1) return;

      const updatedFields = [...this.data.fields];
      updatedFields[fieldIndex] = {
        ...updatedFields[fieldIndex],
        value: value
      };

      this.setData({
        fields: updatedFields
      });

      // 触发数据变化事件
      this.triggerEvent('change', {
        field: fieldName,
        value: value,
        formData: this.getFormData()
      });
    },

    /**
     * 处理开关切换事件
     */
    onSwitchChange(e) {
      const fieldName = e.currentTarget.dataset.name;
      const value = e.detail.value;
      
      const fieldIndex = this.data.fields.findIndex(f => f.name === fieldName);
      if (fieldIndex === -1) return;

      const updatedFields = [...this.data.fields];
      updatedFields[fieldIndex] = {
        ...updatedFields[fieldIndex],
        value: value
      };

      this.setData({
        fields: updatedFields
      });

      // 触发数据变化事件
      this.triggerEvent('change', {
        field: fieldName,
        value: value,
        formData: this.getFormData()
      });
    }
  }
});