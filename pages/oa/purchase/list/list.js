// pages/oa/purchase/list/list.js
const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info.js');
const { formatCurrency, formatDate, formatTimeAgo } = require('../../../../utils/format.js');
const request = require('../../../../utils/request.js');
const { API } = require('../../../../constants/index.js');

Page({
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,
    
    // 页面参数
    listType: 'all', // all, my, pending, approved, rejected
    
    // 列表数据
    purchaseList: [],
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      hasMore: true
    },
    
    // 筛选和搜索
    filters: {
      status: 'all',
      category: 'all',
      timeRange: 'all',
      keyword: ''
    },
    
    // 状态配置
    statusOptions: [
      { value: 'all', label: '全部状态', count: 0 },
      { value: 'draft', label: '草稿', count: 0, color: '#999999' },
      { value: 'submitted', label: '待审批', count: 0, color: '#FF9500' },
      { value: 'approved', label: '已批准', count: 0, color: '#00A86B' },
      { value: 'rejected', label: '已拒绝', count: 0, color: '#FF3B30' },
      { value: 'cancelled', label: '已取消', count: 0, color: '#8E8E93' }
    ],
    
    categoryOptions: [
      { value: 'all', label: '全部类别' },
      { value: 'feed', label: '饲料用品' },
      { value: 'equipment', label: '设备器具' },
      { value: 'medicine', label: '药品疫苗' },
      { value: 'facility', label: '基础设施' },
      { value: 'office', label: '办公用品' },
      { value: 'maintenance', label: '维修保养' },
      { value: 'other', label: '其他' }
    ],
    
    timeRangeOptions: [
      { value: 'all', label: '全部时间' },
      { value: 'today', label: '今天' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' }
    ],

    // 选择器索引
    categoryIndex: 0, // 默认为'all'
    timeRangeIndex: 0, // 默认为'all'

    // 显示标签
    selectedCategoryLabel: '全部类别',
    selectedTimeRangeLabel: '全部时间',

    // 页面状态
    loading: false,
    refreshing: false,
    showFilters: false,
    
    // 统计数据
    statistics: {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      totalAmount: 0
    }
  },

  onLoad(options) {
    this.initPage(options);
  },

  onShow() {
    this.loadPurchaseList(true);
  },

  onPullDownRefresh() {
    this.loadPurchaseList(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadPurchaseList(false);
    }
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    // 使用auth-helper进行可靠的登录检查

    if (!isLoggedIn()) {

      wx.showModal({

        title: '需要登录',

        content: '使用此功能需要先登录系统',

        confirmText: '去登录',

        cancelText: '取消',

        success: (res) => {

          if (res.confirm) {

            wx.redirectTo({ url: '/pages/login/login' });

          } else {

            wx.navigateBack();

          }

        }

      });

      return;

    }


    // 获取用户信息并同步数据

    const authUser = getCurrentUser();

    let userInfo = getCurrentUserInfo();


    if (!userInfo && authUser) {

      const app = getApp();

      app.globalData.userInfo = authUser;

      wx.setStorageSync('user_info', authUser);

      userInfo = getCurrentUserInfo();

    }

    

    const permissions = getUserPermissions();


    if (!userInfo) {

      wx.showModal({

        title: '数据错误',

        content: '无法获取用户信息，请重新登录',

        confirmText: '重新登录',

        showCancel: false,

        success: () => {

          wx.redirectTo({ url: '/pages/login/login' });

        }

      });

      return;

    }
    
    // 检查权限
    if (!permissions.canCreateApplication && !permissions.canApprove) {
      wx.showModal({
        title: '没有权限',
        content: '您没有查看采购申请的权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.setData({
      userInfo,
      permissions,
      listType: options.type || 'all'
    });
    
    // 设置页面标题
    const titles = {
      all: '全部采购',
      my: '我的申请',
      pending: '待审批',
      approved: '已批准',
      rejected: '已拒绝'
    };
    
    wx.setNavigationBarTitle({
      title: titles[this.data.listType] || '采购列表'
    });

    // 初始化选择器索引
    this.updateFilterIndexes();
  },

  /**
   * 更新筛选器索引
   */
  updateFilterIndexes() {
    const { filters, categoryOptions, timeRangeOptions } = this.data;

    // 更新类别索引
    const categoryIndex = categoryOptions.findIndex(item => item.value === filters.category);
    const selectedCategory = categoryOptions[categoryIndex >= 0 ? categoryIndex : 0];

    // 更新时间范围索引
    const timeRangeIndex = timeRangeOptions.findIndex(item => item.value === filters.timeRange);
    const selectedTimeRange = timeRangeOptions[timeRangeIndex >= 0 ? timeRangeIndex : 0];

    this.setData({
      categoryIndex: categoryIndex >= 0 ? categoryIndex : 0,
      timeRangeIndex: timeRangeIndex >= 0 ? timeRangeIndex : 0,
      selectedCategoryLabel: selectedCategory.label,
      selectedTimeRangeLabel: selectedTimeRange.label
    });
  },

  /**
   * 加载采购申请列表
   */
  async loadPurchaseList(refresh = false) {
    if (this.data.loading) return;
    
    if (refresh) {
      this.setData({
        refreshing: true,
        'pagination.page': 1,
        'pagination.hasMore': true
      });
    } else {
      this.setData({ loading: true });
    }
    
    try {
      const { pagination, filters, listType } = this.data;
      const params = {
        page: refresh ? 1 : pagination.page,
        limit: pagination.limit,
        type: listType,
        ...filters
      };

      const res = await request.get(API.ENDPOINTS.OA.PURCHASE.REQUESTS, params);
      if (res && res.success !== false) {
        const payload = res.data || res;
        const list = payload.list || [];
        const pg = payload.pagination || { page: params.page, total: list.length, hasMore: list.length === pagination.limit };
        const stats = payload.statistics || this.data.statistics;
        const statusCounts = payload.statusCounts || null;
        const newList = refresh ? list : [...this.data.purchaseList, ...list];

        const updates = {
          purchaseList: newList,
          'pagination.total': pg.total,
          'pagination.page': (pg.page || params.page) + (pg.hasMore ? 1 : 0),
          'pagination.hasMore': !!pg.hasMore,
          statistics: stats
        };
        if (statusCounts) {
          updates.statusOptions = this.data.statusOptions.map(option => ({ ...option, count: statusCounts[option.value] || 0 }));
        }
        this.setData(updates);
      } else {
        throw new Error(res?.message || '加载采购列表失败');
      }
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载采购列表失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 更新状态统计
   */
  updateStatusCounts(statusCounts) {
    if (!statusCounts) return;
    
    const statusOptions = this.data.statusOptions.map(option => ({
      ...option,
      count: statusCounts[option.value] || 0
    }));
    
    this.setData({ statusOptions });
  },

  /**
   * 显示/隐藏筛选器
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      'filters.status': status
    });
    this.loadPurchaseList(true);
  },

  /**
   * 类别筛选
   */
  onCategoryFilter(e) {
    const categoryIndex = e.detail.value;
    const selectedCategory = this.data.categoryOptions[categoryIndex];

    this.setData({
      'filters.category': selectedCategory.value,
      categoryIndex: categoryIndex,
      selectedCategoryLabel: selectedCategory.label
    });
    this.loadPurchaseList(true);
  },

  /**
   * 时间范围筛选
   */
  onTimeRangeFilter(e) {
    const timeRangeIndex = e.detail.value;
    const selectedRange = this.data.timeRangeOptions[timeRangeIndex];

    this.setData({
      'filters.timeRange': selectedRange.value,
      timeRangeIndex: timeRangeIndex,
      selectedTimeRangeLabel: selectedRange.label
    });
    this.loadPurchaseList(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      'filters.keyword': keyword
    });
    
    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadPurchaseList(true);
    }, 500);
  },

  /**
   * 清除筛选
   */
  clearFilters() {
    this.setData({
      filters: {
        status: 'all',
        category: 'all',
        timeRange: 'all',
        keyword: ''
      },
      showFilters: false,
      categoryIndex: 0,
      timeRangeIndex: 0,
      selectedCategoryLabel: '全部类别',
      selectedTimeRangeLabel: '全部时间'
    });
    this.loadPurchaseList(true);
  },

  /**
   * 点击采购申请项
   */
  onPurchaseItemTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/oa/purchase/detail/detail?id=${id}`
    });
  },

  /**
   * 快速操作
   */
  onQuickAction(e) {
    const { action, id } = e.currentTarget.dataset;
    
    switch (action) {
      case 'edit':
        this.editPurchase(id);
        break;
      case 'copy':
        this.copyPurchase(id);
        break;
      case 'cancel':
        this.cancelPurchase(id);
        break;
      case 'approve':
        this.approvePurchase(id);
        break;
      case 'reject':
        this.rejectPurchase(id);
        break;
    }
  },

  /**
   * 编辑采购申请
   */
  editPurchase(id) {
    wx.navigateTo({
      url: `/pages/oa/purchase/apply/apply?id=${id}`
    });
  },

  /**
   * 复制采购申请
   */
  copyPurchase(id) {
    wx.showModal({
      title: '复制申请',
      content: '确定要复制这个采购申请吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: `/pages/oa/purchase/apply/apply?copyFrom=${id}`
          });
        }
      }
    });
  },

  /**
   * 取消采购申请
   */
  async cancelPurchase(id) {
    wx.showModal({
      title: '取消申请',
      content: '确定要取消这个采购申请吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await request.post(API.ENDPOINTS.OA.PURCHASE.CANCEL(id));
            if (response && response.success !== false) {
              wx.showToast({
                title: '取消成功',
                icon: 'success'
              });
              this.loadPurchaseList(true);
            } else {
              throw new Error(response?.message || '取消失败');
            }
          } catch (error) {
            wx.showToast({
              title: error.message || '取消失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 审批采购申请
   */
  approvePurchase(id) {
    wx.navigateTo({
      url: `/pages/oa/approval/process/process?type=purchase&id=${id}&action=approve`
    });
  },

  /**
   * 拒绝采购申请
   */
  rejectPurchase(id) {
    wx.navigateTo({
      url: `/pages/oa/approval/process/process?type=purchase&id=${id}&action=reject`
    });
  },

  /**
   * 新建采购申请
   */
  onCreatePurchase() {
    wx.navigateTo({
      url: '/pages/oa/purchase/apply/apply'
    });
  },

  /**
   * 获取状态样式
   */
  getStatusStyle(status) {
    const statusConfig = this.data.statusOptions.find(option => option.value === status);
    return statusConfig ? statusConfig.color : '#999999';
  },

  // 格式化方法
  formatCurrency,
  formatDate,
  formatTimeAgo
});