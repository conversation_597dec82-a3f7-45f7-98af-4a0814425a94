<!-- components/oa/data-card/data-card.wxml -->

<view 
  class="data-card {{size}} {{clickable ? 'clickable' : ''}} {{bordered ? 'bordered' : ''}} {{shadow ? 'shadow' : ''}}"
  style="background-color: {{backgroundColor}}"
  bindtap="onCardTap"
>
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-spinner"></view>
  </view>

  <!-- 卡片内容 -->
  <view class="card-content" wx:else>
    <!-- 头部区域 -->
    <view class="card-header">
      <!-- 图标 -->
      <view wx:if="{{icon}}" class="card-icon" style="color: {{iconColor}}">
        <text class="icon">{{icon}}</text>
      </view>
      
      <!-- 标题 -->
      <view class="card-title-area">
        <text class="card-title">{{title}}</text>
        <text wx:if="{{subtitle}}" class="card-subtitle">{{subtitle}}</text>
      </view>
    </view>

    <!-- 数值区域 -->
    <view class="card-value-area">
      <view class="main-value">
        <text class="value">{{value}}</text>
        <text wx:if="{{unit}}" class="unit">{{unit}}</text>
      </view>
      
      <!-- 趋势指示器 -->
      <view wx:if="{{trendValue}}" class="trend-indicator">
        <text class="trend-icon" style="color: {{getTrendColor()}}">{{getTrendIcon()}}</text>
        <text class="trend-value" style="color: {{getTrendColor()}}">{{trendValue}}</text>
      </view>
    </view>
  </view>
</view>
