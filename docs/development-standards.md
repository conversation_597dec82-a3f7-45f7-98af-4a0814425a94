# 智慧养鹅SAAS平台开发规范 v2.9.2

## 📋 概述

本文档定义了智慧养鹅SAAS平台的开发标准和最佳实践，确保代码质量、架构一致性和团队协作效率。

## 🔧 核心规则

### 1. Context7最佳实践查询 (必须)

**规则**: 开发任何新功能前，必须先查询Context7中的微信小程序最佳实践

**适用场景**:
- 📱 小程序页面开发
- 🧩 组件设计和实现  
- 🔌 API接口设计
- 🗄️ 数据库建模

**实施方法**:
```javascript
// 示例：开发前查询相关最佳实践
// 1. 调用 mcp_context7_resolve-library-id 查找相关库
// 2. 调用 mcp_context7_get-library-docs 获取具体规范
// 3. 根据规范进行开发实施
```

**必查库列表**:
- `/context7/developers_weixin_qq_com-miniprogram-dev-framework` - 小程序框架规范
- `/context7/developers_weixin_qq_com-miniprogram-dev-api` - API规范
- `/tencent/tdesign-miniprogram` - 组件库规范

### 2. Sequential Thinking思考模式 (必须)

**规则**: 处理复杂问题时必须使用分步思考模式

**适用场景**:
- 🏗️ 架构设计决策
- 🐛 复杂问题分析
- 🔄 系统重构规划
- ⚡ 性能优化方案

**实施要求**:
- 最少3步思考过程
- 记录思考历程
- 验证解决方案
- 文档化决策过程

### 3. 四端一致性保证 (强制)

**规则**: 确保小程序、后端、数据库、管理后台的一致性

**检查要点**:

#### 📱 小程序 ↔️ 🖥️ 后端
- API接口规范一致
- 数据格式统一
- 错误处理一致
- 权限验证同步

#### 🖥️ 后端 ↔️ 🗄️ 数据库  
- 模型定义匹配
- 字段命名规范
- 关系映射正确
- 索引策略一致

#### 🗄️ 数据库 ↔️ 🔧 管理后台
- 数据操作一致
- 权限控制同步
- 业务逻辑匹配
- 审计日志完整

## 🛠️ 技术栈规范

### 微信小程序开发

#### 页面结构规范
```
pages/
├── module-name/
│   ├── page-name/
│   │   ├── page-name.js      # 页面逻辑
│   │   ├── page-name.json    # 页面配置
│   │   ├── page-name.wxml    # 页面结构
│   │   └── page-name.wxss    # 页面样式
│   └── ...
```

#### 组件命名规范
```javascript
// ✅ 正确的组件命名
"usingComponents": {
  "c-button": "tdesign-miniprogram/button/button",
  "c-card": "/components/common/card/card",
  "c-list-item": "/components/list-item/list-item"
}

// ❌ 错误的命名
"usingComponents": {
  "myButton": "/components/button/button",
  "customCard": "/components/card/card"
}
```

#### API调用规范
```javascript
// ✅ 标准API调用
const { health } = require('../../utils/api.js');

health.getRecords({
  page: 1,
  limit: 20
}).then(res => {
  if (res.success) {
    // 处理成功响应
  } else {
    // 处理错误响应
  }
}).catch(err => {
  // 处理网络错误
});
```

### 后端API开发

#### RESTful设计规范
```javascript
// ✅ 标准RESTful接口
GET    /api/v1/flocks           # 获取鹅群列表
POST   /api/v1/flocks           # 创建鹅群
GET    /api/v1/flocks/:id       # 获取鹅群详情
PUT    /api/v1/flocks/:id       # 更新鹅群信息
DELETE /api/v1/flocks/:id       # 删除鹅群

// ❌ 非标准接口
GET /api/v1/getFlocks
POST /api/v1/createFlock
```

#### 响应格式规范
```javascript
// ✅ 成功响应
{
  "success": true,
  "data": {
    // 实际数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:00Z"
}

// ✅ 错误响应
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": ["字段名称不能为空"]
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 数据库设计规范

#### 命名规范
```sql
-- ✅ 正确的命名（snake_case）
CREATE TABLE health_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  flock_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ❌ 错误的命名（camelCase）
CREATE TABLE healthRecords (
  id INT PRIMARY KEY AUTO_INCREMENT,
  userId INT NOT NULL,
  flockId INT NOT NULL
);
```

#### 模型定义规范
```javascript
// ✅ 标准Sequelize模型
const HealthRecord = sequelize.define('HealthRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',  // 映射到数据库字段
    comment: '用户ID'
  }
}, {
  tableName: 'health_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});
```

## 🔍 代码质量标准

### 代码审查清单

#### 小程序页面
- [ ] 页面路由配置正确
- [ ] 组件引用规范
- [ ] API调用错误处理完整
- [ ] 性能优化实施
- [ ] 无障碍访问支持

#### 后端接口
- [ ] RESTful设计规范
- [ ] 参数验证完整
- [ ] 错误处理统一
- [ ] 权限控制严格
- [ ] 日志记录完整

#### 数据库操作
- [ ] 字段命名规范
- [ ] 索引配置合理
- [ ] 关系定义明确
- [ ] 数据验证严格
- [ ] 迁移脚本完整

#### 管理后台
- [ ] 业务逻辑一致
- [ ] 权限验证同步
- [ ] 操作日志记录
- [ ] 界面交互友好
- [ ] 数据展示准确

### 测试要求

#### 单元测试
```javascript
// 示例：API接口测试
describe('Health Records API', () => {
  test('should get health records list', async () => {
    const response = await request(app)
      .get('/api/v1/health/records')
      .set('Authorization', `Bearer ${token}`)
      .expect(200);
    
    expect(response.body.success).toBe(true);
    expect(Array.isArray(response.body.data)).toBe(true);
  });
});
```

#### 集成测试
- API接口集成测试
- 数据库操作测试
- 小程序页面流程测试
- 管理后台功能测试

## 🚀 开发流程

### 功能开发流程

1. **需求分析** (使用Sequential Thinking)
   - 分析业务需求
   - 设计技术方案
   - 评估技术风险

2. **技术调研** (查询Context7)
   - 查询相关最佳实践
   - 研究技术规范
   - 确定实施方案

3. **设计开发**
   - 数据库设计
   - API接口设计
   - 小程序页面设计
   - 管理后台设计

4. **编码实施**
   - 遵循编码规范
   - 实施测试驱动开发
   - 确保一致性

5. **测试验证**
   - 单元测试
   - 集成测试
   - 用户体验测试
   - 性能测试

6. **代码审查**
   - 使用标准检查清单
   - 验证一致性
   - 确保质量

### Git提交规范

```bash
# 提交信息格式
<type>(<scope>): <subject>

# 示例
feat(miniprogram): 添加健康记录页面
fix(backend): 修复用户权限验证问题
docs(api): 更新API文档
refactor(database): 优化库存管理模型
test(health): 添加健康记录单元测试
```

## 📊 一致性检查

### 自动化检查脚本

```javascript
// scripts/consistency-check.js
const consistencyRules = [
  'checkApiEndpoints',      // API端点一致性
  'checkDatabaseModels',    // 数据库模型一致性
  'checkComponentUsage',    // 组件使用一致性
  'checkPermissionSync',    // 权限同步一致性
];

// 运行检查
npm run check:consistency
```

### 人工检查清单

#### 每次开发完成后
- [ ] 小程序页面与API接口匹配
- [ ] 数据库模型与业务逻辑一致
- [ ] 管理后台功能与核心业务对应
- [ ] 权限控制在各端同步

#### 每次发布前
- [ ] 端到端功能测试通过
- [ ] 性能指标达标
- [ ] 安全扫描通过
- [ ] 文档更新完整

## 🔧 工具配置

### VS Code配置
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "wechat-miniprogram.formatOnSave": true,
  "wechat-miniprogram.lintOnSave": true
}
```

### ESLint配置
```json
{
  "extends": [
    "eslint:recommended",
    "@tencent/eslint-config-tdesign"
  ],
  "rules": {
    "no-console": "warn",
    "prefer-const": "error",
    "no-unused-vars": "error"
  }
}
```

## 📚 参考资源

### 官方文档
- [微信小程序开发指南](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [TDesign小程序组件库](https://tdesign.tencent.com/miniprogram/overview)
- [MySQL 8.0参考手册](https://dev.mysql.com/doc/refman/8.0/en/)

### 内部文档
- [API设计规范](./backend/docs/restful-api-standards.md)
- [部署指南](./backend/DEPLOYMENT_GUIDE.md)
- [数据库一致性报告](./backend/DBMODEL_CONSISTENCY_ISSUES.md)

---

**遵循规范，保证质量，确保一致性！** 🚀

*最后更新：2024-01-20*
*维护团队：Smart Goose Development Team*