/**
 * 图片资源常量配置
 * 统一管理所有图片路径，便于维护和更新
 */

// 图片资源基础路径
const BASE_PATHS = {
  ASSETS: '/assets',           // 主要资源目录
  IMAGES: '/images',           // 图片目录
  ICONS: '/images/icons',      // 图标目录
  WEATHER: '/images/weather'   // 天气图标目录
};

// 默认图片/头像
const DEFAULTS = {
  AVATAR: '/images/default_avatar.png',
  LOGO: `${BASE_PATHS.IMAGES}/default-logo.png`,
  PLACEHOLDER: `${BASE_PATHS.IMAGES}/placeholder.png`,
  NO_DATA: `${BASE_PATHS.IMAGES}/no-data.png`,
  ERROR: `${BASE_PATHS.IMAGES}/error.png`,
  LOADING: `${BASE_PATHS.IMAGES}/loading.gif`
};

// 功能图标 - 统一使用SVG格式提高清晰度
const ICONS = {
  // ===== 导航相关 =====
  HOME: `${BASE_PATHS.ICONS}/home.svg`,
  BACK: `${BASE_PATHS.ICONS}/arrow_left.svg`,
  FORWARD: `${BASE_PATHS.ICONS}/arrow_right.svg`,
  CLOSE: `${BASE_PATHS.ICONS}/close.svg`,
  MENU: `${BASE_PATHS.ICONS}/menu.svg`,
  SEARCH: `${BASE_PATHS.ICONS}/search.svg`,
  FILTER: `${BASE_PATHS.ICONS}/filter.svg`,
  
  // ===== 操作相关 =====
  ADD: `${BASE_PATHS.ICONS}/add.svg`,
  EDIT: `${BASE_PATHS.ICONS}/edit.svg`,
  DELETE: `${BASE_PATHS.ICONS}/delete.svg`,
  SAVE: `${BASE_PATHS.ICONS}/save.svg`,
  CANCEL: `${BASE_PATHS.ICONS}/cancel.svg`,
  CONFIRM: `${BASE_PATHS.ICONS}/check.svg`,
  REFRESH: `${BASE_PATHS.ICONS}/refresh.svg`,
  
  // ===== 状态相关 =====
  SUCCESS: `${BASE_PATHS.ICONS}/check-circle.svg`,
  WARNING: `${BASE_PATHS.ICONS}/warning.svg`,
  ERROR: `${BASE_PATHS.ICONS}/error.svg`,
  INFO: `${BASE_PATHS.ICONS}/info.svg`,
  LOADING: `${BASE_PATHS.IMAGES}/icon_refresh.png`,
  
  // ===== 业务功能 =====
  HEALTH: `${BASE_PATHS.ICONS}/health.svg`,
  PRODUCTION: `${BASE_PATHS.ICONS}/production.svg`,
  INVENTORY: `${BASE_PATHS.ICONS}/inventory.svg`,
  FINANCE: `${BASE_PATHS.ICONS}/finance.svg`,
  AI: `${BASE_PATHS.ICONS}/ai.svg`,
  CAMERA: `${BASE_PATHS.ICONS}/camera.svg`,
  
  // ===== 个人中心 =====
  PROFILE: `${BASE_PATHS.ICONS}/profile.svg`,
  SETTINGS: `${BASE_PATHS.ICONS}/settings.svg`,
  HELP: `${BASE_PATHS.ICONS}/help.svg`,
  ABOUT: `${BASE_PATHS.ICONS}/about.svg`,
  FEEDBACK: `${BASE_PATHS.ICONS}/feedback.svg`,
  
  // ===== 通用功能 =====
  LOCATION: `${BASE_PATHS.ICONS}/map-pin.svg`,
  TIME: `${BASE_PATHS.ICONS}/clock.svg`,
  CALENDAR: `${BASE_PATHS.ICONS}/calendar.svg`,
  PHONE: `${BASE_PATHS.ICONS}/phone.svg`,
  EMAIL: `${BASE_PATHS.ICONS}/email.svg`,
  SHARE: `${BASE_PATHS.ICONS}/share.svg`,
  DOWNLOAD: `${BASE_PATHS.ICONS}/download.svg`,
  UPLOAD: `${BASE_PATHS.ICONS}/upload.svg`,
  
  // ===== 眼睛图标 =====
  EYE_OPEN: `${BASE_PATHS.ICONS}/eye_open.svg`,
  EYE_CLOSE: `${BASE_PATHS.ICONS}/eye_close.svg`,
  
  // ===== 购物相关 =====
  CART: `${BASE_PATHS.ICONS}/cart.svg`,
  SHOP: `${BASE_PATHS.ICONS}/shop.svg`,
  ORDER: `${BASE_PATHS.ICONS}/order.svg`,
  PAYMENT: `${BASE_PATHS.ICONS}/payment.svg`
};

// PNG格式图标（特定需求）
const ICONS_PNG = {
  // ===== 相机相关 =====
  CAMERA: `${BASE_PATHS.IMAGES}/icon_camera.png`,
  LIGHT: `${BASE_PATHS.IMAGES}/icon_light.png`,
  
  // ===== 业务模块图标 =====
  HEALTH_RECORD: `${BASE_PATHS.ASSETS}/icons/health_record.png`,
  PRODUCTION_RECORD: `${BASE_PATHS.ASSETS}/icons/production_record.png`,
  MATERIAL: `${BASE_PATHS.ASSETS}/icons/material.png`,
  FINANCE: `${BASE_PATHS.ASSETS}/icons/finance.png`,
  
  // ===== 设置相关 =====
  SETTINGS: `${BASE_PATHS.ASSETS}/icons/settings.png`,
  HELP: `${BASE_PATHS.ASSETS}/icons/help.png`,
  ABOUT: `${BASE_PATHS.ASSETS}/icons/about.png`,
  FEEDBACK: `${BASE_PATHS.ASSETS}/icons/feedback.png`,
  
  // ===== 状态图标 =====
  DEATH: `${BASE_PATHS.ASSETS}/icons/death.png`,
  HEALTH_ICON: `${BASE_PATHS.ASSETS}/icons/health_icon.svg`
};

// 天气图标
const WEATHER = {
  SUNNY: `${BASE_PATHS.WEATHER}/sunny.png`,
  CLOUDY: `${BASE_PATHS.WEATHER}/cloudy.png`,
  OVERCAST: `${BASE_PATHS.WEATHER}/overcast.png`,
  RAINY: `${BASE_PATHS.WEATHER}/rainy.png`,
  SNOWY: `${BASE_PATHS.WEATHER}/snowy.png`,
  THUNDER: `${BASE_PATHS.WEATHER}/thunder.png`,
  FOGGY: `${BASE_PATHS.WEATHER}/foggy.png`,
  WINDY: `${BASE_PATHS.WEATHER}/windy.png`
};

// 背景图片
const BACKGROUNDS = {
  LOGIN: `${BASE_PATHS.IMAGES}/bg_login.jpg`,
  HOME: `${BASE_PATHS.IMAGES}/bg_home.jpg`,
  PROFILE: `${BASE_PATHS.IMAGES}/bg_profile.jpg`
};

// 品牌相关图片
const BRAND = {
  LOGO: `${BASE_PATHS.IMAGES}/logo.png`,
  LOGO_WHITE: `${BASE_PATHS.IMAGES}/logo_white.png`,
  LOGO_SMALL: `${BASE_PATHS.IMAGES}/logo_small.png`,
  SPLASH: `${BASE_PATHS.IMAGES}/splash.png`
};

// 业务相关图片
const BUSINESS = {
  // ===== 鹅群相关 =====
  GOOSE: `${BASE_PATHS.IMAGES}/goose.png`,
  FLOCK: `${BASE_PATHS.IMAGES}/flock.jpg`,
  FARM: `${BASE_PATHS.IMAGES}/farm.jpg`,
  
  // ===== 健康相关 =====
  HEALTHY: `${BASE_PATHS.IMAGES}/healthy.png`,
  SICK: `${BASE_PATHS.IMAGES}/sick.png`,
  MEDICINE: `${BASE_PATHS.IMAGES}/medicine.png`,
  
  // ===== 生产相关 =====
  EGG: `${BASE_PATHS.IMAGES}/egg.png`,
  FEED: `${BASE_PATHS.IMAGES}/feed.png`,
  ENVIRONMENT: `${BASE_PATHS.IMAGES}/environment.jpg`,
  
  // ===== AI相关 =====
  AI_ROBOT: `${BASE_PATHS.IMAGES}/ai_robot.png`,
  AI_DIAGNOSIS: `${BASE_PATHS.IMAGES}/ai_diagnosis.png`,
  AI_ANALYSIS: `${BASE_PATHS.IMAGES}/ai_analysis.png`
};

// 空状态图片
const EMPTY_STATES = {
      NO_DATA: `${BASE_PATHS.IMAGES}/icon_empty.png`,
  NO_NETWORK: `${BASE_PATHS.IMAGES}/empty_network.png`,
  NO_PERMISSION: `${BASE_PATHS.IMAGES}/empty_permission.png`,
  NO_SEARCH_RESULT: `${BASE_PATHS.IMAGES}/empty_search.png`,
  UNDER_CONSTRUCTION: `${BASE_PATHS.IMAGES}/under_construction.png`
};

// 图片工具函数
const IMAGE_UTILS = {
  /**
   * 获取图片完整路径
   * @param {string} path 相对路径
   * @returns {string} 完整路径
   */
  getFullPath: (path) => {
    if (path.startsWith('/')) return path;
    return `${BASE_PATHS.IMAGES}/${path}`;
  },
  
  /**
   * 获取默认头像
   * @param {string} type 头像类型 (user, admin, guest)
   * @returns {string} 头像路径
   */
  getDefaultAvatar: (type = 'user') => {
    const avatars = {
      user: DEFAULTS.AVATAR,
      admin: `${BASE_PATHS.ASSETS}/icons/admin_avatar.png`,
      guest: `${BASE_PATHS.ASSETS}/icons/guest_avatar.png`
    };
    return avatars[type] || DEFAULTS.AVATAR;
  },
  
  /**
   * 获取状态图标
   * @param {string} status 状态值
   * @returns {string} 图标路径
   */
  getStatusIcon: (status) => {
    const statusIcons = {
      success: ICONS.SUCCESS,
      warning: ICONS.WARNING,
      error: ICONS.ERROR,
      info: ICONS.INFO,
      loading: ICONS.LOADING
    };
    return statusIcons[status] || ICONS.INFO;
  },
  
  /**
   * 获取天气图标
   * @param {string} weather 天气状况
   * @returns {string} 天气图标路径
   */
  getWeatherIcon: (weather) => {
    const weatherMap = {
      '晴': WEATHER.SUNNY,
      '多云': WEATHER.CLOUDY,
      '阴': WEATHER.OVERCAST,
      '雨': WEATHER.RAINY,
      '雪': WEATHER.SNOWY,
      '雷': WEATHER.THUNDER,
      '雾': WEATHER.FOGGY,
      '风': WEATHER.WINDY
    };
    return weatherMap[weather] || WEATHER.CLOUDY;
  }
};

// 导出所有图片常量
module.exports = {
  BASE_PATHS,
  DEFAULTS,
  ICONS,
  ICONS_PNG,
  WEATHER,
  BACKGROUNDS,
  BRAND,
  BUSINESS,
  EMPTY_STATES,
  IMAGE_UTILS
};