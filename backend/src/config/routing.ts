import { Routing } from "express-zod-api";
import { healthEndpoint } from "../endpoints/health.endpoint";
import {
  createFlockEndpoint,
  getFlockListEndpoint,
  getFlockByIdEndpoint,
  updateFlockEndpoint,
  deleteFlockEndpoint,
} from "../endpoints/flock.endpoints";
import {
  getHealthRecordsEndpoint,
  createHealthRecordEndpoint,
  getHealthRecordByIdEndpoint,
  updateHealthRecordEndpoint,
  deleteHealthRecordEndpoint,
  getHealthStatsEndpoint,
} from "../endpoints/health.endpoints";
import {
  getProductionRecordsEndpoint,
  createProductionRecordEndpoint,
  getProductionRecordByIdEndpoint,
  updateProductionRecordEndpoint,
  deleteProductionRecordEndpoint,
  getProductionStatsEndpoint,
  getProductionTrendsEndpoint,
} from "../endpoints/production.endpoints";

// 定义API路由结构
export const routing: Routing = {
  // 健康检查端点（无需认证）
  health: healthEndpoint,

  // V2版本API（新的Zod端点）
  v2: {
    // 认证相关
    auth: {
      // login: loginEndpoint,
      // refresh: refreshTokenEndpoint,
    },

    // 鹅群管理
    flocks: {
      "": getFlockListEndpoint, // GET /v2/flocks
      create: createFlockEndpoint, // POST /v2/flocks/create
      ":id": {
        "": getFlockByIdEndpoint, // GET /v2/flocks/:id
        update: updateFlockEndpoint, // PATCH /v2/flocks/:id/update
        delete: deleteFlockEndpoint, // DELETE /v2/flocks/:id/delete
      },
    },

    // 健康记录
    health: {
      records: {
        "": getHealthRecordsEndpoint, // GET /v2/health/records
        create: createHealthRecordEndpoint, // POST /v2/health/records/create
        ":id": {
          "": getHealthRecordByIdEndpoint, // GET /v2/health/records/:id
          update: updateHealthRecordEndpoint, // PATCH /v2/health/records/:id/update
          delete: deleteHealthRecordEndpoint, // DELETE /v2/health/records/:id/delete
        },
      },
      stats: getHealthStatsEndpoint, // GET /v2/health/stats
    },

    // 生产记录
    production: {
      records: {
        "": getProductionRecordsEndpoint, // GET /v2/production/records
        create: createProductionRecordEndpoint, // POST /v2/production/records/create
        ":id": {
          "": getProductionRecordByIdEndpoint, // GET /v2/production/records/:id
          update: updateProductionRecordEndpoint, // PATCH /v2/production/records/:id/update
          delete: deleteProductionRecordEndpoint, // DELETE /v2/production/records/:id/delete
        },
      },
      stats: getProductionStatsEndpoint, // GET /v2/production/stats
      trends: getProductionTrendsEndpoint, // GET /v2/production/trends
    },

    // AI服务
    ai: {
      // diagnosis: aiDiagnosisEndpoint,      // POST /v2/ai/diagnosis
      // chat: aiChatEndpoint,                // POST /v2/ai/chat
    },

    // 用户管理
    users: {
      // profile: getUserProfileEndpoint,     // GET /v2/users/profile
      // ":id": getUserByIdEndpoint,          // GET /v2/users/:id
    },

    // 租户管理（管理员功能）
    tenants: {
      // "": {
      //   "get": getTenantsEndpoint,         // GET /v2/tenants
      //   "post": createTenantEndpoint,      // POST /v2/tenants
      // },
      // ":id": getTenantByIdEndpoint,        // GET /v2/tenants/:id
    },
  },

  // 兼容性路由（保留原有的JavaScript API）
  v1: {
    // 这里可以代理到原有的Express路由
    // 或者逐步迁移到新的Zod端点
  },

  // 管理面板API
  admin: {
    // dashboard: adminDashboardEndpoint,
    // stats: adminStatsEndpoint,
  },

  // 文档和开发工具
  docs: {
    // swagger: swaggerUIEndpoint,
    // postman: postmanCollectionEndpoint,
  },
};

// 导出路由类型（用于客户端代码生成）
export type ApiRouting = typeof routing;
