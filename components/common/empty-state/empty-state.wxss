/* 通用空状态组件样式 */

:host {
  --c-empty-title-color: #333333;
  --c-empty-description-color: #666666;
  --c-empty-bg-color: transparent;
  
  --c-empty-icon-size-small: 120rpx;
  --c-empty-icon-size-medium: 160rpx;
  --c-empty-icon-size-large: 200rpx;
  --c-empty-icon-size-xlarge: 240rpx;
  
  --c-empty-spacing-small: 20rpx;
  --c-empty-spacing-medium: 30rpx;
  --c-empty-spacing-large: 40rpx;
}

/* 基础空状态样式 */
.c-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--c-empty-spacing-large);
  background-color: var(--c-empty-bg-color);
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

/* ===== 垂直对齐方式 ===== */
.c-empty-state--top {
  justify-content: flex-start;
  padding-top: 80rpx;
}

.c-empty-state--center {
  justify-content: center;
  min-height: 400rpx;
}

.c-empty-state--bottom {
  justify-content: flex-end;
  padding-bottom: 80rpx;
}

/* ===== 空状态图标 ===== */
.c-empty-state__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--c-empty-spacing-medium);
}

.c-empty-state__icon-img {
  opacity: 0.6;
}

/* 图标尺寸 */
.c-empty-state--icon-small .c-empty-state__icon-img {
  width: var(--c-empty-icon-size-small);
  height: var(--c-empty-icon-size-small);
}

.c-empty-state--icon-medium .c-empty-state__icon-img {
  width: var(--c-empty-icon-size-medium);
  height: var(--c-empty-icon-size-medium);
}

.c-empty-state--icon-large .c-empty-state__icon-img {
  width: var(--c-empty-icon-size-large);
  height: var(--c-empty-icon-size-large);
}

.c-empty-state--icon-xlarge .c-empty-state__icon-img {
  width: var(--c-empty-icon-size-xlarge);
  height: var(--c-empty-icon-size-xlarge);
}

/* ===== 空状态标题 ===== */
.c-empty-state__title {
  margin-bottom: var(--c-empty-spacing-small);
  font-size: 32rpx;
  font-weight: 600;
  color: var(--c-empty-title-color);
  line-height: 1.5;
}

/* ===== 空状态描述 ===== */
.c-empty-state__description {
  margin-bottom: var(--c-empty-spacing-large);
  font-size: 26rpx;
  color: var(--c-empty-description-color);
  line-height: 1.6;
  max-width: 400rpx;
}

/* ===== 操作按钮 ===== */
.c-empty-state__button {
  margin-bottom: var(--c-empty-spacing-medium);
}

.c-empty-button {
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
  outline: none;
}

/* 按钮类型样式 */
.c-empty-button.type-primary {
  background-color: #2E8B57;
  color: #FFFFFF;
}

.c-empty-button.type-primary:active {
  background-color: #246A42;
  transform: scale(0.95);
}

.c-empty-button.type-default {
  background-color: #FFFFFF;
  color: #333333;
  border: 2rpx solid #E5E7EB;
}

.c-empty-button.type-default:active {
  background-color: #F9FAFB;
  border-color: #D1D5DB;
  transform: scale(0.95);
}

.c-empty-button.type-text {
  background-color: transparent;
  color: #2E8B57;
}

.c-empty-button.type-text:active {
  background-color: rgba(46, 139, 87, 0.1);
  transform: scale(0.95);
}

/* 重置小程序button默认样式 */
.c-empty-button::after {
  border: none;
}

/* ===== 自定义内容插槽 ===== */
.c-empty-state__slot {
  width: 100%;
}

.c-empty-state__slot:empty {
  display: none;
}

/* ===== 动画效果 ===== */

/* 加载旋转动画 */
@keyframes c-empty-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 淡入动画 */
.c-empty-state {
  animation: c-empty-fade-in 0.3s ease-out;
}

@keyframes c-empty-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== 特殊状态样式 ===== */

/* 错误状态 */
.c-empty-state.data-type-error .c-empty-state__title {
  color: #DC3545;
}

/* 网络错误状态 */
.c-empty-state.data-type-network .c-empty-state__title {
  color: #FFC107;
}

/* 维护状态 */
.c-empty-state.data-type-maintenance .c-empty-state__title {
  color: #17A2B8;
}

/* ===== 响应式设计 ===== */

/* 小屏幕适配 */
@media (max-width: 750rpx) {
  .c-empty-state {
    padding: var(--c-empty-spacing-medium);
  }
  
  .c-empty-state--icon-xlarge .c-empty-state__icon-img {
    width: var(--c-empty-icon-size-large);
    height: var(--c-empty-icon-size-large);
  }
  
  .c-empty-state--icon-large .c-empty-state__icon-img {
    width: var(--c-empty-icon-size-medium);
    height: var(--c-empty-icon-size-medium);
  }
  
  .c-empty-state__title {
    font-size: 28rpx;
  }
  
  .c-empty-state__description {
    font-size: 24rpx;
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  :host {
    --c-empty-title-color: #F9FAFB;
    --c-empty-description-color: #D1D5DB;
  }
  
  .c-empty-button.type-default {
    background-color: #374151;
    color: #F9FAFB;
    border-color: #4B5563;
  }
  
  .c-empty-button.type-default:active {
    background-color: #4B5563;
    border-color: #6B7280;
  }
}