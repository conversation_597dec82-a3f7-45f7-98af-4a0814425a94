/**
 * 财务记录路由
 * 处理财务相关的API路由
 */

const express = require('express');
const router = express.Router();
const financeController = require('../controllers/finance.controller');
const { verifyToken } = require('../middleware/auth.middleware');
const { validatePermission } = require('../middleware/permission.middleware');

// 应用认证中间件
router.use(verifyToken);

// ================================
// 财务记录基础CRUD操作
// ================================

/**
 * 获取财务记录列表
 * GET /api/v1/finance/records
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - limit: 每页数量 (默认: 20)
 * - type: 记录类型 (income/expense)
 * - category: 分类
 * - batchNumber: 批次号
 * - startDate: 开始日期
 * - endDate: 结束日期
 * - autoGenerated: 是否自动生成 (true/false)
 * - status: 状态 (pending/confirmed/cancelled)
 */
router.get('/records', 
  validatePermission('finance_view'), 
  financeController.getFinancialRecords
);

/**
 * 创建财务记录
 * POST /api/v1/finance/records
 * 请求体:
 * {
 *   "type": "expense|income",
 *   "category": "分类名称",
 *   "amount": 金额,
 *   "description": "描述",
 *   "recordDate": "记录日期",
 *   "notes": "备注",
 *   "batchNumber": "批次号",
 *   "paymentMethod": "支付方式",
 *   "supplier": "供应商/买家",
 *   "invoiceNumber": "发票号码"
 * }
 */
router.post('/records', 
  validatePermission('finance_create'), 
  financeController.createFinancialRecord
);

/**
 * 更新财务记录
 * PUT /api/v1/finance/records/:id
 * 注意: 自动生成的记录不能修改
 */
router.put('/records/:id', 
  validatePermission('finance_edit'), 
  financeController.updateFinancialRecord
);

/**
 * 删除财务记录
 * DELETE /api/v1/finance/records/:id
 * 注意: 自动生成的记录不能删除
 */
router.delete('/records/:id', 
  validatePermission('finance_delete'), 
  financeController.deleteFinancialRecord
);

// ================================
// 批次财务分析
// ================================

/**
 * 获取批次财务统计
 * GET /api/v1/finance/batch-stats/:batchNumber
 * 返回指定批次的详细财务统计信息
 */
router.get('/batch-stats/:batchNumber', 
  validatePermission('finance_view'), 
  financeController.getBatchFinancialStats
);

/**
 * 获取所有批次财务概览
 * GET /api/v1/finance/batch-overview
 * 返回所有批次的财务概览信息
 */
router.get('/batch-overview', 
  validatePermission('finance_view'), 
  financeController.getBatchFinancialOverview
);

// ================================
// 财务报表和分析
// ================================

/**
 * 获取财务趋势数据
 * GET /api/v1/finance/trends
 * 查询参数:
 * - period: 时间周期 (daily/weekly/monthly/yearly)
 * - startDate: 开始日期
 * - endDate: 结束日期
 * - type: 数据类型 (income/expense/profit)
 */
router.get('/trends', 
  validatePermission('finance_view'), 
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { period = 'monthly', startDate, endDate, type = 'all' } = req.query;
      
      // 构建日期格式
      let dateFormat;
      switch (period) {
        case 'daily':
          dateFormat = '%Y-%m-%d';
          break;
        case 'weekly':
          dateFormat = '%Y-%u';
          break;
        case 'monthly':
          dateFormat = '%Y-%m';
          break;
        case 'yearly':
          dateFormat = '%Y';
          break;
        default:
          dateFormat = '%Y-%m';
      }

      let whereClause = 'WHERE userId = ?';
      let queryParams = [userId];

      if (startDate) {
        whereClause += ' AND recordDate >= ?';
        queryParams.push(startDate);
      }
      if (endDate) {
        whereClause += ' AND recordDate <= ?';
        queryParams.push(endDate);
      }

      const db = require('../config/database');
      const [trends] = await db.query(`
        SELECT 
          DATE_FORMAT(recordDate, '${dateFormat}') as period,
          SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as income,
          SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expense,
          (SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) - 
           SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as profit,
          COUNT(*) as record_count
        FROM financial_records 
        ${whereClause}
        GROUP BY DATE_FORMAT(recordDate, '${dateFormat}')
        ORDER BY period ASC
      `, queryParams);

      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.success(res, {
        period,
        trends: trends.map(trend => ({
          period: trend.period,
          income: parseFloat(trend.income),
          expense: parseFloat(trend.expense),
          profit: parseFloat(trend.profit),
          recordCount: trend.record_count
        }))
      });

    } catch (error) {
      console.error('获取财务趋势失败:', error);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.error(res, '获取财务趋势失败', 500, error.message);
    }
  }
);

/**
 * 获取分类统计
 * GET /api/v1/finance/category-stats
 * 查询参数:
 * - type: 记录类型 (income/expense)
 * - startDate: 开始日期
 * - endDate: 结束日期
 */
router.get('/category-stats', 
  validatePermission('finance_view'), 
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { type, startDate, endDate } = req.query;

      let whereClause = 'WHERE userId = ?';
      let queryParams = [userId];

      if (type) {
        whereClause += ' AND type = ?';
        queryParams.push(type);
      }
      if (startDate) {
        whereClause += ' AND recordDate >= ?';
        queryParams.push(startDate);
      }
      if (endDate) {
        whereClause += ' AND recordDate <= ?';
        queryParams.push(endDate);
      }

      const db = require('../config/database');
      const [stats] = await db.query(`
        SELECT 
          category,
          type,
          SUM(amount) as total_amount,
          COUNT(*) as record_count,
          AVG(amount) as avg_amount,
          MIN(amount) as min_amount,
          MAX(amount) as max_amount
        FROM financial_records 
        ${whereClause}
        GROUP BY category, type
        ORDER BY total_amount DESC
      `, queryParams);

      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.success(res, {
        categories: stats.map(stat => ({
          category: stat.category,
          type: stat.type,
          totalAmount: parseFloat(stat.total_amount),
          recordCount: stat.record_count,
          avgAmount: parseFloat(stat.avg_amount),
          minAmount: parseFloat(stat.min_amount),
          maxAmount: parseFloat(stat.max_amount)
        }))
      });

    } catch (error) {
      console.error('获取分类统计失败:', error);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.error(res, '获取分类统计失败', 500, error.message);
    }
  }
);

/**
 * 导出财务数据
 * GET /api/v1/finance/export
 * 查询参数:
 * - format: 导出格式 (csv/excel)
 * - type: 记录类型 (income/expense/all)
 * - startDate: 开始日期
 * - endDate: 结束日期
 * - batchNumber: 批次号
 */
router.get('/export', 
  validatePermission('finance_export'), 
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { format = 'csv', type, startDate, endDate, batchNumber } = req.query;

      let whereClause = 'WHERE userId = ?';
      let queryParams = [userId];

      if (type && type !== 'all') {
        whereClause += ' AND type = ?';
        queryParams.push(type);
      }
      if (startDate) {
        whereClause += ' AND recordDate >= ?';
        queryParams.push(startDate);
      }
      if (endDate) {
        whereClause += ' AND recordDate <= ?';
        queryParams.push(endDate);
      }
      if (batchNumber) {
        whereClause += ' AND batch_number = ?';
        queryParams.push(batchNumber);
      }

      const db = require('../config/database');
      const [records] = await db.query(`
        SELECT 
          id,
          type,
          category,
          amount,
          description,
          recordDate,
          batch_number,
          supplier,
          payment_method,
          invoice_number,
          CASE WHEN auto_generated = 1 THEN '自动生成' ELSE '手动录入' END as generation_type,
          status,
          notes,
          createdAt
        FROM financial_records 
        ${whereClause}
        ORDER BY recordDate DESC, createdAt DESC
      `, queryParams);

      // 这里可以根据format参数生成不同格式的文件
      // 目前返回JSON格式，实际应用中可以生成CSV或Excel文件

      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.success(res, {
        format,
        exportTime: new Date().toISOString(),
        recordCount: records.length,
        records
      }, '数据导出成功');

    } catch (error) {
      console.error('导出财务数据失败:', error);
      const ResponseHelper = require('../utils/responseHelper');
      return ResponseHelper.error(res, '导出财务数据失败', 500, error.message);
    }
  }
);

module.exports = router;
