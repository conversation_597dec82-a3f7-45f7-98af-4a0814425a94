<!-- pages/production/record-add/record-add.wxml -->
<view class="container">
  <!-- 标签页导航 -->
  <view class="tab-nav">
    <block wx:for="{{tabs}}" wx:key="id">
      <view class="tab-item {{activeTab === item.id ? 'active' : ''}}" 
            data-tab="{{item.id}}" 
            bindtap="onTabChange">
        <text class="tab-text">{{item.name}}</text>
      </view>
    </block>
  </view>

  <!-- 表单区域 -->
  <scroll-view class="form-scroll" scroll-y enhanced>
    <view class="form-wrapper">
      
      <!-- 基本信息卡片 -->
      <view class="card">
        <view class="card-header">
          <text class="card-title">基本信息</text>
        </view>
        
        <view class="card-content">
          <!-- 批次号 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">批次号</text>
              <text class="required">*</text>
            </view>
            <view wx:if="{{recordType === 'entry'}}" class="input-wrapper">
              <input class="form-input" placeholder="自动生成" value="{{batch}}" disabled />
            </view>
            <picker wx:else class="input-wrapper" range="{{batchOptions}}" range-key="label" bindchange="onBatchChange">
              <view class="form-input picker">
                <text class="{{batch ? 'value' : 'placeholder'}}">{{batchLabel || '选择批次'}}</text>
                <image class="picker-arrow" src="/images/icons/arrow_down.png" mode="aspectFit"></image>
              </view>
            </picker>
          </view>

          <!-- 日期 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">记录日期</text>
              <text class="required">*</text>
            </view>
            <picker class="input-wrapper" mode="date" value="{{date}}" bindchange="onDateChange">
              <view class="form-input picker">
                <text class="{{date ? 'value' : 'placeholder'}}">{{date || '选择日期'}}</text>
                <image class="picker-arrow" src="/images/icons/calendar.png" mode="aspectFit"></image>
              </view>
            </picker>
          </view>

          <!-- 数量 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">数量</text>
              <text class="required">*</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="{{recordType === 'entry' ? '输入数量' : maxCount > 0 ? '最多' + maxCount + '只' : '请先选择批次'}}" value="{{count}}" type="number" bindinput="onCountInput" />
              <view class="input-unit">只</view>
            </view>
          </view>
          
          <!-- 批次信息显示（称重和出栏记录） -->
          <view wx:if="{{recordType !== 'entry' && selectedBatchInfo}}" class="input-group">
            <view class="input-label">
              <text class="label-text">批次信息</text>
            </view>
            <view class="batch-info">
              <view class="info-item">
                <text class="info-label">品种：</text>
                <text class="info-value">{{selectedBatchInfo.breed}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">入栏日期：</text>
                <text class="info-value">{{selectedBatchInfo.entryDate}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">当前存栏：</text>
                <text class="info-value">{{selectedBatchInfo.currentCount}}只</text>
              </view>
              <view wx:if="{{selectedBatchInfo.avgWeight}}" class="info-item">
                <text class="info-label">平均重量：</text>
                <text class="info-value">{{selectedBatchInfo.avgWeight}}</text>
              </view>
            </view>
          </view>

          <!-- 重量（称重和出栏需要） -->
          <view wx:if="{{recordType === 'weight' || recordType === 'sale'}}" class="input-group">
            <view class="input-label">
              <text class="label-text">{{recordType === 'weight' ? '平均重量' : '单只重量'}}</text>
              <text wx:if="{{recordType === 'sale'}}" class="required">*</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="输入重量" value="{{weight}}" type="digit" bindinput="onWeightInput" />
              <view class="input-unit">kg</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 入栏信息卡片 -->
      <view wx:if="{{recordType === 'entry'}}" class="card">
        <view class="card-header">
          <text class="card-title">入栏信息</text>
        </view>
        
        <view class="card-content">
          <!-- 来源 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">鹅苗来源</text>
              <text class="required">*</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="输入来源地址" value="{{source}}" bindinput="onSourceInput" />
            </view>
          </view>

          <!-- 品种 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">鹅的品种</text>
            </view>
            <picker class="input-wrapper" range="{{breedOptions}}" range-key="label" bindchange="onBreedChange">
              <view class="form-input picker">
                <text class="{{breed ? 'value' : 'placeholder'}}">{{breedLabel || '选择品种'}}</text>
                <image class="picker-arrow" src="/images/icons/arrow_down.png" mode="aspectFit"></image>
              </view>
            </picker>
          </view>

          <!-- 日龄 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">鹅苗日龄</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="输入日龄" value="{{age}}" type="number" bindinput="onAgeInput" />
              <view class="input-unit">天</view>
            </view>
          </view>

          <!-- 成本 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">采购成本</text>
              <text class="required">*</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="输入总成本" value="{{cost}}" type="digit" bindinput="onCostInput" />
              <view class="input-unit">元</view>
            </view>
          </view>

          <!-- 供应商 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">供应商</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="输入供应商名称" value="{{supplier}}" bindinput="onSupplierInput" />
            </view>
          </view>
        </view>
      </view>

      <!-- 称重信息卡片 -->
      <view wx:elif="{{recordType === 'weight'}}" class="card">
        <view class="card-header">
          <text class="card-title">称重信息</text>
        </view>
        
        <view class="card-content">
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">称重说明</text>
            </view>
            <view class="info-tip">
              <view class="tip-icon">💡</view>
              <text class="tip-text">定期称重有助于监控鹅群健康成长状况</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 出栏信息卡片 -->
      <view wx:elif="{{recordType === 'sale'}}" class="card">
        <view class="card-header">
          <text class="card-title">出栏信息</text>
        </view>
        
        <view class="card-content">
          <!-- 单价 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">销售单价</text>
              <text class="required">*</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="输入单价" value="{{price}}" type="digit" bindinput="onPriceInput" />
              <view class="input-unit">元/kg</view>
            </view>
          </view>

          <!-- 买方 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">买方信息</text>
              <text class="required">*</text>
            </view>
            <view class="input-wrapper">
              <input class="form-input" placeholder="输入买方名称" value="{{buyer}}" bindinput="onBuyerInput" />
            </view>
          </view>

          <!-- 总收入 -->
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">预计收入</text>
            </view>
            <view class="income-display">
              <text class="currency">¥</text>
              <text class="amount">{{totalIncome || '0.00'}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息卡片 -->
      <view class="card">
        <view class="card-header">
          <text class="card-title">备注信息</text>
        </view>
        
        <view class="card-content">
          <view class="input-group">
            <view class="input-label">
              <text class="label-text">备注内容</text>
            </view>
            <view class="textarea-wrapper">
              <textarea class="form-textarea" placeholder="请输入备注信息（可选）" value="{{notes}}" bindinput="onNotesInput" maxlength="200"></textarea>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部间距 -->
      <view class="bottom-space"></view>
    </view>
  </scroll-view>

  <!-- 底部操作按钮 -->
  <view class="action-bar">
    <button class="action-btn cancel" bindtap="onCancel">
      <image class="btn-icon" src="/images/icons/trash.png" mode="aspectFit"></image>
      <text>取消</text>
    </button>
    <button class="action-btn primary" loading="{{loading}}" bindtap="onSave">
      <image wx:if="{{!loading}}" class="btn-icon" src="/images/icons/check-circle.png" mode="aspectFit"></image>
      <text>{{loading ? '保存中...' : '保存记录'}}</text>
    </button>
  </view>
</view>