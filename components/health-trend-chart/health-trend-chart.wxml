<!-- components/health-trend-chart/health-trend-chart.wxml -->
<view class="chart-container">
  <view class="chart-header">
    <text class="chart-title">健康趋势</text>
    <view class="chart-legend">
      <view class="legend-item">
        <view class="legend-color healthy"></view>
        <text class="legend-text">健康</text>
      </view>
      <view class="legend-item">
        <view class="legend-color sick"></view>
        <text class="legend-text">生病</text>
      </view>
      <view class="legend-item">
        <view class="legend-color death"></view>
        <text class="legend-text">死亡</text>
      </view>
    </view>
  </view>
  
  <view class="chart-content">
    <canvas 
      type="2d"
      class="chart-canvas" 
      id="healthTrendChart"
      bindtouchstart="onTouchStart"
      bindtouchmove="onTouchMove"
      bindtouchend="onTouchEnd">
    </canvas>
    
    <!-- 数据点提示 -->
    <view class="tooltip" wx:if="{{showTooltip}}" style="left: {{tooltipX}}px; top: {{tooltipY}}px;">
      <view class="tooltip-content">
        <text class="tooltip-date">{{tooltipData.date}}</text>
        <view class="tooltip-item">
          <text class="tooltip-label healthy">健康: {{tooltipData.healthy}}</text>
        </view>
        <view class="tooltip-item">
          <text class="tooltip-label sick">生病: {{tooltipData.sick}}</text>
        </view>
        <view class="tooltip-item">
          <text class="tooltip-label death">死亡: {{tooltipData.death}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view wx:if="{{!chartData || chartData.length === 0}}" class="empty-chart">
    <image class="empty-icon" src="/images/icon_chart_empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无数据</text>
  </view>
</view>
