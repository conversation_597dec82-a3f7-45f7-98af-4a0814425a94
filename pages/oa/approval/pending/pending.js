// pages/oa/approval/pending/pending.js

const { PERMISSIONS, PermissionMixin } = require('../../../../utils/oa-permissions');
const request = require('../../../../utils/request.js');
const { API } = require('../../../../constants/index.js');

Page({
  // 混入权限检查功能
  ...PermissionMixin,

  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    submitting: false,
    showFilters: false,
    showActionModal: false,

    // 筛选条件
    filters: {
      businessType: 'all',
      timeRange: 'all',
      keyword: ''
    },

    // 筛选选项
    businessTypeOptions: [
      { value: 'all', label: '全部类型' },
      { value: 'reimbursement', label: '报销申请' },
      { value: 'leave', label: '请假申请' },
      { value: 'purchase', label: '采购申请' },
      { value: 'other', label: '其他申请' }
    ],

    timeRangeOptions: [
      { value: 'all', label: '全部时间' },
      { value: 'today', label: '今天' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' }
    ],

    // 审批列表数据
    approvals: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      hasMore: true
    },

    // 统计数据
    statistics: {
      total: 0,
      urgent: 0,
      today: 0
    },

    // 审批操作相关
    currentApproval: null,
    modalAction: '',
    actionRemarks: ''
  },

  onLoad(options) {
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('待审批页面加载', options); } catch(_) {}
    this.initPage();
  },

  onShow() {
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadMoreApprovals();
    }
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查审批权限
    const hasApprovalPermission = this.hasAnyPermission([
      PERMISSIONS.APPROVE_PURCHASE,
      PERMISSIONS.APPROVE_REIMBURSEMENT,
      PERMISSIONS.APPROVE_LEAVE
    ]);
    
    if (!hasApprovalPermission) {
      wx.showModal({
        title: '权限不足',
        content: '您没有审批权限，请联系管理员',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限验证通过，加载数据
    this.loadInitialData();
  },

  /**
   * 初始化页面
   */
  initPage() {
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('初始化待审批页面'); } catch(_) {}
    // 权限检查会自动触发 onPermissionReady
  },

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    await this.loadStatistics();
    await this.loadApprovals(true);
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      await Promise.all([
        this.loadStatistics(),
        this.loadApprovals(true)
      ]);
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('刷新数据失败', error); } catch(_) {}
    } finally {
      this.setData({ refreshing: false });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const res = await request.get(API.ENDPOINTS.OA.APPROVALS.PENDING);
      if (res && res.success !== false) {
        const payload = res.data || res;
        if (Array.isArray(payload)) {
          const total = payload.length;
          const urgent = payload.filter(i => i.isUrgent).length;
          const today = payload.filter(i => (i.created_at || '').startsWith(new Date().toISOString().split('T')[0])).length;
          this.setData({ statistics: { total, urgent, today } });
        } else if (payload && (payload.total !== undefined)) {
          this.setData({ statistics: payload });
        }
      }
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载统计数据失败', error); } catch(_) {}
    }
  },

  /**
   * 加载审批列表
   */
  async loadApprovals(refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      if (refresh) {
        this.setData({
          'pagination.page': 1,
          'pagination.hasMore': true
        });
      }

      const { pagination, filters } = this.data;
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        businessType: filters.businessType,
        timeRange: filters.timeRange,
        keyword: filters.keyword
      };

      const res = await request.get(API.ENDPOINTS.OA.APPROVALS.PENDING, params);
      if (res && res.success !== false) {
        const payload = res.data || res;
        const list = Array.isArray(payload.list) ? payload.list : (Array.isArray(payload) ? payload : []);
        const newPagination = payload.pagination || { page: pagination.page, pages: list.length < pagination.limit ? pagination.page : pagination.page + 1 };
        const filteredApprovals = this.filterApprovals(list);

        this.setData({
          approvals: refresh ? filteredApprovals : [...this.data.approvals, ...filteredApprovals],
          'pagination.page': newPagination.page,
          'pagination.total': newPagination.total || filteredApprovals.length,
          'pagination.hasMore': newPagination.page < (newPagination.pages || newPagination.page)
        });
      }

    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载审批列表失败', error); } catch(_) {}
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载更多审批
   */
  loadMoreApprovals() {
    const currentPage = this.data.pagination.page;
    this.setData({
      'pagination.page': currentPage + 1
    });
    this.loadApprovals(false);
  },

  /**
   * 过滤审批数据
   */
  filterApprovals(approvals) {
    const { filters } = this.data;
    
    return approvals.filter(approval => {
      // 业务类型筛选
      if (filters.businessType !== 'all' && approval.business_type !== filters.businessType) {
        return false;
      }

      // 时间范围筛选
      if (filters.timeRange !== 'all') {
        const createdDate = new Date(approval.created_at);
        const now = new Date();
        
        switch (filters.timeRange) {
          case 'today':
            if (createdDate.toDateString() !== now.toDateString()) {
              return false;
            }
            break;
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            if (createdDate < weekAgo) {
              return false;
            }
            break;
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            if (createdDate < monthAgo) {
              return false;
            }
            break;
        }
      }

      // 关键词搜索
      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase();
        if (!approval.title.toLowerCase().includes(keyword) &&
            !approval.applicant_name.toLowerCase().includes(keyword)) {
          return false;
        }
      }

      return true;
    });
  },

  /**
   * 快速筛选
   */
  onQuickFilter(e) {
    const { type } = e.currentTarget.dataset;
    
    let filters = { ...this.data.filters };
    
    switch (type) {
      case 'all':
        filters = {
          businessType: 'all',
          timeRange: 'all',
          keyword: ''
        };
        break;
      case 'urgent':
        // 这里可以添加紧急筛选逻辑
        break;
      case 'today':
        filters.timeRange = 'today';
        break;
    }
    
    this.setData({ filters });
    this.loadApprovals(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      'filters.keyword': keyword
    });
    
    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadApprovals(true);
    }, 500);
  },

  /**
   * 搜索确认
   */
  onSearchConfirm() {
    this.loadApprovals(true);
  },

  /**
   * 清除搜索
   */
  clearSearch() {
    this.setData({
      'filters.keyword': ''
    });
    this.loadApprovals(true);
  },

  /**
   * 切换筛选面板
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 业务类型变更
   */
  onBusinessTypeChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.businessType': value
    });
  },

  /**
   * 时间范围变更
   */
  onTimeRangeChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.timeRange': value
    });
  },

  /**
   * 重置筛选
   */
  resetFilters() {
    this.setData({
      filters: {
        businessType: 'all',
        timeRange: 'all',
        keyword: ''
      }
    });
  },

  /**
   * 应用筛选
   */
  applyFilters() {
    this.setData({
      showFilters: false
    });
    this.loadApprovals(true);
  },

  /**
   * 审批项点击
   */
  onApprovalTap(e) {
    const { approval } = e.currentTarget.dataset;
    
    // 跳转到详情页面
    let url = '';
    switch (approval.business_type) {
      case 'reimbursement':
        url = `/pages/oa/reimbursement/detail/detail?id=${approval.id}`;
        break;
      case 'leave':
        url = `/pages/oa/leave/detail/detail?id=${approval.id}`;
        break;
      case 'purchase':
        url = `/pages/oa/purchase/detail/detail?id=${approval.id}`;
        break;
      default:
        wx.showToast({
          title: '暂不支持查看此类型详情',
          icon: 'none'
        });
        return;
    }
    
    wx.navigateTo({
      url,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 审批操作点击
   */
  onActionTap(e) {
    const { action, approval } = e.currentTarget.dataset;
    
    // 添加触摸反馈
    wx.vibrateShort();
    
    this.setData({
      currentApproval: approval,
      modalAction: action,
      showActionModal: true,
      actionRemarks: ''
    });
  },

  /**
   * 隐藏操作弹窗
   */
  hideActionModal() {
    this.setData({
      showActionModal: false,
      currentApproval: null,
      modalAction: '',
      actionRemarks: ''
    });
  },

  /**
   * 审批意见输入
   */
  onRemarksInput(e) {
    this.setData({
      actionRemarks: e.detail.value
    });
  },

  /**
   * 确认审批操作
   */
  async confirmAction() {
    const { currentApproval, modalAction, actionRemarks } = this.data;
    
    if (!actionRemarks.trim()) {
      wx.showToast({
        title: '请输入审批意见',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ submitting: true });
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      wx.showToast({
        title: modalAction === 'approve' ? '审批通过' : '审批拒绝',
        icon: 'success'
      });
      
      this.hideActionModal();
      
      // 重新加载数据
      setTimeout(() => {
        this.refreshData();
      }, 1000);
      
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('审批操作失败', error); } catch(_) {}
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  }
});