/**
 * 优化后的生产记录项组件
 * 提供美观的记录展示和交互功能
 */

Component({
  properties: {
    // 记录数据
    record: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          this.processRecordData(newVal);
        }
      }
    },
    
    // 显示模式
    displayMode: {
      type: String,
      value: 'default' // default, compact, detailed
    },
    
    // 是否显示财务关联状态
    showFinanceLink: {
      type: Boolean,
      value: true
    },
    
    // 是否显示状态指示器
    showStatus: {
      type: Boolean,
      value: true
    }
  },

  data: {
    processedRecord: {},
    formattedDate: '',
    weekday: ''
  },

  methods: {
    /**
     * 处理记录数据
     */
    processRecordData(record) {
      const processedRecord = {
        ...record,
        details: this.extractRecordDetails(record),
        financeLinked: this.checkFinanceLink(record)
      };

      this.setData({
        processedRecord,
        formattedDate: this.formatDate(record.date),
        weekday: this.getWeekday(record.date)
      });
    },

    /**
     * 提取记录详情
     */
    extractRecordDetails(record) {
      switch (record.type) {
        case 'entry':
          return {
            count: record.count || record.totalCount || 0,
            source: record.source || record.supplier || '未知来源',
            cost: this.formatCurrency(record.totalCost || record.cost || 0)
          };
        
        case 'sale':
          return {
            count: record.saleCount || record.count || 0,
            weight: this.formatWeight(record.totalWeight || record.weight || 0),
            price: this.formatCurrency(record.unitPrice || record.price || 0),
            totalIncome: this.formatCurrency(record.totalIncome || record.income || 0)
          };
        
        case 'weight':
          return {
            count: record.weightCount || record.count || 0,
            weight: this.formatWeight(record.averageWeight || record.weight || 0)
          };
        
        default:
          return {};
      }
    },

    /**
     * 检查财务关联状态
     */
    checkFinanceLink(record) {
      return !!(record.financeRecordId || record.finance_record_id || record.financeLinked);
    },

    /**
     * 格式化日期
     */
    formatDate(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const recordDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      
      const diffTime = recordDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        return '今天';
      } else if (diffDays === -1) {
        return '昨天';
      } else if (diffDays === 1) {
        return '明天';
      } else if (diffDays > -7 && diffDays < 0) {
        return `${Math.abs(diffDays)}天前`;
      } else if (diffDays < 7 && diffDays > 0) {
        return `${diffDays}天后`;
      } else {
        return `${date.getMonth() + 1}/${date.getDate()}`;
      }
    },

    /**
     * 获取星期几
     */
    getWeekday(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      return weekdays[date.getDay()];
    },

    /**
     * 格式化货币
     */
    formatCurrency(amount) {
      if (typeof amount !== 'number') {
        amount = parseFloat(amount) || 0;
      }
      return amount.toFixed(2);
    },

    /**
     * 格式化重量
     */
    formatWeight(weight) {
      if (typeof weight !== 'number') {
        weight = parseFloat(weight) || 0;
      }
      return weight.toFixed(1);
    },

    /**
     * 点击事件处理
     */
    onItemTap() {
      this.triggerEvent('itemTap', {
        record: this.data.processedRecord,
        originalRecord: this.properties.record
      });
    },

    /**
     * 获取记录类型显示名称
     */
    getRecordTypeName(type) {
      const typeMap = {
        'entry': '入栏记录',
        'weight': '称重记录',
        'sale': '出栏记录'
      };
      return typeMap[type] || '未知记录';
    },

    /**
     * 获取记录类型图标
     */
    getRecordTypeIcon(type) {
      const iconMap = {
        'entry': '📥',
        'weight': '⚖️',
        'sale': '📤'
      };
      return iconMap[type] || '📝';
    },

    /**
     * 获取状态显示文本
     */
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'approved': '已审核',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知状态';
    },

    /**
     * 检查是否为今天的记录
     */
    isToday(dateStr) {
      if (!dateStr) return false;
      
      const date = new Date(dateStr);
      const today = new Date();
      
      return date.getFullYear() === today.getFullYear() &&
             date.getMonth() === today.getMonth() &&
             date.getDate() === today.getDate();
    },

    /**
     * 获取记录摘要信息
     */
    getRecordSummary() {
      const { record } = this.properties;
      
      switch (record.type) {
        case 'entry':
          return `入栏 ${record.count || 0} 只，成本 ¥${this.formatCurrency(record.totalCost || 0)}`;
        
        case 'sale':
          return `出栏 ${record.saleCount || record.count || 0} 只，收入 ¥${this.formatCurrency(record.totalIncome || 0)}`;
        
        case 'weight':
          return `称重 ${record.weightCount || record.count || 0} 只，平均 ${this.formatWeight(record.averageWeight || 0)}kg`;
        
        default:
          return '记录详情';
      }
    }
  },

  // 组件生命周期
  lifetimes: {
    attached() {
      if (this.properties.record) {
        this.processRecordData(this.properties.record);
      }
    }
  },

  // 页面生命周期
  pageLifetimes: {
    show() {
      // 页面显示时可以刷新数据
      if (this.properties.record) {
        this.processRecordData(this.properties.record);
      }
    }
  }
});
