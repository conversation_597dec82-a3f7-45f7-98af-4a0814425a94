/**
 * 表单验证工具
 * 符合微信小程序最佳实践的表单验证解决方案
 */

/**
 * 验证表单数据
 * @param {Array} fields 表单字段配置
 * @param {Object} formData 表单数据
 * @returns {Object} 验证结果 { isValid: boolean, errors: Array, firstError: string }
 */
function validateForm(fields, formData) {
  const errors = [];
  
  if (!fields || !Array.isArray(fields)) {
    return { isValid: false, errors: ['表单配置错误'], firstError: '表单配置错误' };
  }
  
  if (!formData || typeof formData !== 'object') {
    return { isValid: false, errors: ['表单数据错误'], firstError: '表单数据错误' };
  }
  
  for (let field of fields) {
    const fieldKey = field.key || field.name;
    const fieldValue = formData[fieldKey];
    const fieldLabel = field.label || fieldKey;
    
    // 必填字段验证
    if (field.required) {
      if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
        errors.push(`请填写${fieldLabel}`);
        continue;
      }
    }
    
    // 字段类型特定验证
    if (fieldValue && fieldValue !== '') {
      switch (field.type) {
        case 'number':
          if (isNaN(fieldValue) || parseFloat(fieldValue) < 0) {
            errors.push(`${fieldLabel}必须是有效的正数`);
          }
          break;
          
        case 'date':
          if (!isValidDate(fieldValue)) {
            errors.push(`${fieldLabel}格式不正确`);
          }
          break;
          
        case 'time':
          if (!isValidTime(fieldValue)) {
            errors.push(`${fieldLabel}格式不正确`);
          }
          break;
          
        case 'input':
        case 'text':
          if (field.minLength && fieldValue.length < field.minLength) {
            errors.push(`${fieldLabel}至少需要${field.minLength}个字符`);
          }
          if (field.maxLength && fieldValue.length > field.maxLength) {
            errors.push(`${fieldLabel}不能超过${field.maxLength}个字符`);
          }
          break;
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors,
    firstError: errors.length > 0 ? errors[0] : null
  };
}

/**
 * 验证日期格式 (YYYY-MM-DD)
 */
function isValidDate(dateString) {
  if (!dateString) return false;
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date) && dateString === formatDate(date);
}

/**
 * 验证时间格式 (HH:MM)
 */
function isValidTime(timeString) {
  if (!timeString) return false;
  const regex = /^([01]\d|2[0-3]):([0-5]\d)$/;
  return regex.test(timeString);
}

/**
 * 格式化日期为 YYYY-MM-DD
 */
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 格式化时间为 HH:MM
 */
function formatTime(date) {
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
}

/**
 * 获取当前日期时间的默认值
 */
function getDefaultDateTime() {
  const now = new Date();
  return {
    date: formatDate(now),
    time: formatTime(now)
  };
}

/**
 * 显示验证错误提示
 * @param {string} message 错误信息
 * @param {number} duration 显示时长，默认2000ms
 */
function showValidationError(message, duration = 2000) {
  if (!message) return;
  
  wx.showToast({
    title: message,
    icon: 'none',
    duration: duration,
    mask: false
  });
}

/**
 * 统一的表单验证并显示错误
 * @param {Array} fields 表单字段配置
 * @param {Object} formData 表单数据
 * @returns {boolean} 验证是否通过
 */
function validateAndShowError(fields, formData) {
  const result = validateForm(fields, formData);
  
  if (!result.isValid) {
    showValidationError(result.firstError);
    return false;
  }
  
  return true;
}

/**
 * 表单字段标准化
 * 确保字段配置符合最佳实践
 */
function normalizeFormFields(fields) {
  if (!Array.isArray(fields)) return [];
  
  return fields.map(field => {
    // 确保每个字段都有key或name
    if (!field.key && !field.name) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('表单字段缺少key或name', field); } catch(_) {}
    }
    
    // 标准化字段配置
    return {
      key: field.key || field.name,
      name: field.name || field.key,
      label: field.label || field.key || field.name,
      type: field.type || 'input',
      required: Boolean(field.required),
      placeholder: field.placeholder || `请输入${field.label || field.key || field.name}`,
      ...field
    };
  });
}

module.exports = {
  validateForm,
  validateAndShowError,
  showValidationError,
  normalizeFormFields,
  getDefaultDateTime,
  formatDate,
  formatTime,
  isValidDate,
  isValidTime
};