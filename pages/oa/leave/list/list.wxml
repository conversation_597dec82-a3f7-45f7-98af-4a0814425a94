<!-- pages/oa/leave/list/list.wxml -->
<view class="list-container">
  
  <!-- 搜索和筛选区域 -->
  <view class="search-filter-section">
    <!-- 搜索框 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <image class="search-icon" src="/images/icons/search.png" mode="aspectFit"></image>
        <input 
          class="search-input"
          placeholder="搜索请假原因..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearchSubmit"
          confirm-type="search" />
        <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">
          <text>×</text>
        </view>
      </view>
      <button class="search-btn" bindtap="onSearchSubmit">搜索</button>
    </view>
    
    <!-- 筛选器 -->
    <view class="filter-row">
      <picker 
        range="{{statusOptions}}" 
        range-key="label" 
        bindchange="onFilterChange"
        data-type="status"
        class="filter-picker">
        <view class="filter-item">
          <text class="filter-label">{{selectedStatusLabel}}</text>
          <image class="filter-arrow" src="/images/icons/arrow_down.png" mode="aspectFit"></image>
        </view>
      </picker>
      
      <picker 
        range="{{typeOptions}}" 
        range-key="label" 
        bindchange="onFilterChange"
        data-type="type"
        class="filter-picker">
        <view class="filter-item">
          <text class="filter-label">{{selectedTypeLabel}}</text>
          <image class="filter-arrow" src="/images/icons/arrow_down.png" mode="aspectFit"></image>
        </view>
      </picker>
      
      <picker 
        range="{{timeRangeOptions}}" 
        range-key="label" 
        bindchange="onFilterChange"
        data-type="timeRange"
        class="filter-picker">
        <view class="filter-item">
          <text class="filter-label">{{selectedTimeRangeLabel}}</text>
          <image class="filter-arrow" src="/images/icons/arrow_down.png" mode="aspectFit"></image>
        </view>
      </picker>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section" wx:if="{{total > 0}}">
    <text class="stats-text">共找到 {{total}} 条记录</text>
    <button class="apply-btn" bindtap="onApplyLeave">
      <image class="apply-icon" src="/images/icons/plus.png" mode="aspectFit"></image>
      <text>申请请假</text>
    </button>
  </view>

  <!-- 请假记录列表 -->
  <view class="leave-list-section">
    <block wx:for="{{leaveList}}" wx:key="id">
      <view class="leave-item" bindtap="onLeaveItemTap" data-id="{{item.id}}">
        <!-- 头部信息 -->
        <view class="leave-header">
          <view class="leave-type-badge" style="background-color: {{item.typeColor}};">
            {{item.typeLabel}}
          </view>
          <view class="leave-status" style="color: {{item.statusColor}};">
            {{item.statusText}}
          </view>
        </view>
        
        <!-- 请假内容 -->
        <view class="leave-content">
          <text class="leave-reason">{{item.reason}}</text>
          <view class="leave-time-info">
            <view class="time-row">
              <text class="time-label">请假时间：</text>
              <text class="time-value">{{item.startDate}} 至 {{item.endDate}}</text>
            </view>
            <view class="days-info">
              <text class="days-text">共 {{item.days}} 天</text>
            </view>
          </view>
        </view>
        
        <!-- 底部信息 -->
        <view class="leave-footer">
          <view class="applicant-info">
            <text class="applicant-name">{{item.applicant}}</text>
            <text class="applicant-dept">{{item.department}}</text>
          </view>
          <view class="time-info">
            <text class="submit-time">{{item.submitTime}}</text>
          </view>
          <view class="arrow-icon">></view>
        </view>
        
        <!-- 审批信息 -->
        <view wx:if="{{item.status !== 'pending'}}" class="approve-info">
          <text class="approve-text">
            {{item.status === 'approved' ? '批准' : '拒绝'}}人：{{item.approver}} | {{item.approveTime}}
          </text>
          <text wx:if="{{item.rejectReason}}" class="reject-reason">拒绝原因：{{item.rejectReason}}</text>
        </view>
      </view>
    </block>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{leaveList.length === 0 && !loading}}" class="empty-state">
    <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无请假记录</text>
    <button class="empty-apply-btn" bindtap="onApplyLeave">立即申请</button>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{loadingMore}}" class="loading-more">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view wx:if="{{!hasMore && leaveList.length > 0}}" class="no-more">
    <text>没有更多数据了</text>
  </view>

  <!-- 全屏加载 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 浮动申请按钮 -->
  <view class="floating-btn" bindtap="onApplyLeave">
    <image class="floating-icon" src="/images/icons/add_white.png" mode="aspectFit"></image>
  </view>

</view>