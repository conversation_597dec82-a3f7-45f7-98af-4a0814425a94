#!/usr/bin/env node

/**
 * 创建权限系统基础数据
 * 插入角色、权限和用户角色关联数据
 */

const mysql = require("mysql2/promise");

const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "zhihuiyange",
  password: process.env.DB_PASSWORD || "zhihuiyange123",
  database: process.env.DB_NAME || "zhihuiyange_local",
  multipleStatements: false,
};

async function executeSQL(connection, sql) {
  try {
    console.log(`⚡ 执行: ${sql.slice(0, 80)}...`);
    const [result] = await connection.execute(sql);
    console.log(`✅ 成功`);
    return result;
  } catch (error) {
    console.log(`⚠️  失败: ${error.message.slice(0, 100)}`);
    return null;
  }
}

async function createPermissionsData() {
  let connection;

  try {
    console.log("🔐 创建权限系统基础数据");
    console.log("==================================================");
    
    connection = await mysql.createConnection(dbConfig);
    console.log("✅ 数据库连接成功");

    // 1. 创建基础权限数据
    console.log("📝 插入基础权限数据...");
    
    const permissions = [
      ['VIEW_FINANCE', 'finance', 'view', '查看财务数据', 'finance'],
      ['MANAGE_FINANCE', 'finance', 'manage', '管理财务数据', 'finance'],
      ['VIEW_PRODUCTION', 'production', 'view', '查看生产数据', 'production'],
      ['MANAGE_PRODUCTION', 'production', 'manage', '管理生产数据', 'production'],
      ['VIEW_HEALTH', 'health', 'view', '查看健康数据', 'health'],
      ['MANAGE_HEALTH', 'health', 'manage', '管理健康数据', 'health'],
      ['VIEW_INVENTORY', 'inventory', 'view', '查看库存数据', 'inventory'],
      ['MANAGE_INVENTORY', 'inventory', 'manage', '管理库存数据', 'inventory'],
      ['VIEW_OA', 'oa', 'view', '查看办公自动化', 'oa'],
      ['MANAGE_OA', 'oa', 'manage', '管理办公自动化', 'oa'],
      ['ADMIN_ACCESS', 'system', 'admin', '系统管理权限', 'system']
    ];

    for (const [code, resource, action, description, category] of permissions) {
      await executeSQL(connection, `
        INSERT IGNORE INTO permissions (name, code, resource, action, description, category)
        VALUES ('${description}', '${code}', '${resource}', '${action}', '${description}', '${category}')
      `);
    }

    // 2. 创建基础角色数据
    console.log("👥 插入基础角色数据...");
    
    const roles = [
      ['admin', '管理员', '系统管理员，拥有所有权限', '["*"]'],
      ['manager', '经理', '管理经理，拥有管理权限', '["VIEW_*", "MANAGE_FINANCE", "MANAGE_PRODUCTION", "MANAGE_HEALTH", "MANAGE_INVENTORY"]'],
      ['employee', '员工', '普通员工，拥有基础权限', '["VIEW_PRODUCTION", "VIEW_HEALTH", "VIEW_INVENTORY"]'],
      ['viewer', '观察者', '只读用户，仅查看权限', '["VIEW_*"]']
    ];

    for (const [name, displayName, description, permissions] of roles) {
      await executeSQL(connection, `
        INSERT IGNORE INTO roles (name, display_name, description, permissions)
        VALUES ('${name}', '${displayName}', '${description}', '${permissions}')
      `);
    }

    // 3. 设置用户默认角色（为现有用户分配角色）
    console.log("🔗 为现有用户分配默认角色...");
    
    // 检查是否有用户
    const [userCount] = await connection.execute(`SELECT COUNT(*) as count FROM users`);
    
    if (userCount[0].count > 0) {
      // 为第一个用户分配管理员角色
      await executeSQL(connection, `
        INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by)
        SELECT u.id, r.id, 1
        FROM users u, roles r 
        WHERE u.id = 1 AND r.name = 'admin'
        LIMIT 1
      `);
      
      // 为其他用户分配员工角色
      await executeSQL(connection, `
        INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by)
        SELECT u.id, r.id, 1
        FROM users u, roles r 
        WHERE u.id > 1 AND r.name = 'employee'
      `);
    }

    console.log("🎯 权限系统数据创建完成！");
    console.log("==================================================");
    console.log("📝 创建摘要:");
    console.log("   ✅ 基础权限数据已插入");
    console.log("   ✅ 基础角色数据已插入");
    console.log("   ✅ 用户角色关联已创建");
    console.log("   ✅ 权限系统可正常使用");

    return true;

  } catch (error) {
    console.error("❌ 创建过程中出现错误:", error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔒 数据库连接已关闭");
    }
  }
}

async function main() {
  const success = await createPermissionsData();
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main();
}

module.exports = { createPermissionsData };