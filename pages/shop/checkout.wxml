<!-- pages/shop/checkout.wxml -->
<view class="checkout-container">
  <!-- 收货地址 -->
  <view class="address-section">
    <view class="section-header">
      <text class="section-title">收货地址</text>
    </view>
    <view class="address-card" bindtap="onSelectAddress">
      <view class="address-info">
        <view class="address-row">
          <text class="address-name">{{address.name}}</text>
          <text class="address-phone">{{address.phone}}</text>
        </view>
        <text class="address-detail">{{address.detail}}</text>
      </view>
      <view class="arrow-right">
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="goods-section">
    <view class="section-header">
      <text class="section-title">商品清单</text>
    </view>
    <view class="goods-list">
      <block wx:for="{{cartItems}}" wx:key="id">
        <view class="goods-item">
          <image class="goods-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="goods-name">{{item.name}}</text>
            <view class="goods-price-qty">
              <text class="goods-price">¥{{item.price}}</text>
              <text class="goods-qty">x{{item.quantity}}</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 订单备注 -->
  <view class="note-section">
    <view class="note-header">
      <text class="note-title">订单备注</text>
    </view>
    <view class="note-input-wrapper">
      <textarea 
        class="note-input" 
        placeholder="选填，请输入备注信息" 
        value="{{note}}"
        bindinput="onNoteInput"
        maxlength="200">
      </textarea>
    </view>
  </view>

  <!-- 优惠券 -->
  <view class="coupon-section" wx:if="{{false}}">
    <view class="coupon-item">
      <text class="coupon-label">优惠券</text>
      <view class="coupon-select">
        <text class="coupon-text">请选择优惠券</text>
        <view class="arrow-right">
          <text class="arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 价格明细 -->
  <view class="price-section">
    <view class="price-details">
      <view class="price-item">
        <text class="price-label">商品金额</text>
        <text class="price-value">¥{{totalPrice}}</text>
      </view>
      <view class="price-item">
        <text class="price-label">运费</text>
        <text class="price-value">¥0.00</text>
      </view>
      <view class="price-item coupon" wx:if="{{discountAmount > 0}}">
        <text class="price-label">优惠券</text>
        <view class="coupon-info">
          <text class="coupon-selected">已选1张</text>
          <text class="coupon-amount">-¥{{discountAmount}}</text>
          <view class="arrow-right">
            <text class="arrow">></text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 发票选项 -->
  <view class="invoice-section">
    <view class="invoice-item" bindtap="onInvoiceSelect">
      <text class="invoice-label">发票</text>
      <text class="invoice-option">{{invoiceData.type === 'none' ? '不开发票' : (invoiceData.type === 'personal' ? '个人发票' : '企业发票')}}</text>
      <view class="arrow-right">
        <text class="arrow">></text>
      </view>
    </view>
  </view>
</view>

<!-- 底部价格和提交 -->
<view class="bottom-section">
  <view class="total-price">
    <text class="total-label">合计：</text>
    <text class="total-amount">¥{{actualAmount || totalPrice}}</text>
    <text class="savings" wx:if="{{discountAmount > 0}}">共省¥{{discountAmount}}</text>
  </view>
  <button class="submit-btn" bindtap="onSubmitOrder">提交订单</button>
</view>

<!-- 发票选择模态框 -->
<view class="modal-overlay" wx:if="{{showInvoiceModal}}" bindtap="onCloseInvoiceModal">
  <view class="invoice-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">选择发票类型</text>
      <text class="modal-close" bindtap="onCloseInvoiceModal">×</text>
    </view>
    <view class="invoice-types">
      <view class="invoice-type-item" bindtap="onInvoiceTypeSelect" data-type="none">
        <text class="type-title">不开发票</text>
        <text class="type-desc">订单完成后无发票</text>
        <view class="type-check" wx:if="{{invoiceData.type === 'none'}}">✓</view>
      </view>
      <view class="invoice-type-item" bindtap="onInvoiceTypeSelect" data-type="personal">
        <text class="type-title">个人发票</text>
        <text class="type-desc">开具个人电子发票</text>
        <view class="type-check" wx:if="{{invoiceData.type === 'personal'}}">✓</view>
      </view>
      <view class="invoice-type-item" bindtap="onInvoiceTypeSelect" data-type="company">
        <text class="type-title">企业发票</text>
        <text class="type-desc">开具企业增值税发票</text>
        <view class="type-check" wx:if="{{invoiceData.type === 'company'}}">✓</view>
      </view>
    </view>
  </view>
</view>