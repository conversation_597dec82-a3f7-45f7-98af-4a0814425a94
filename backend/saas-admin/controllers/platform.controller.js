const { Op } = require("sequelize");

// 使用真实的数据库连接和模型
const TenantDatabaseManager = require("../../models/tenant-database.model");
let saasDb;

// 响应工具类
class ResponseHelper {
  static success(res, data = null, message = "操作成功", code = 200) {
    return res.status(code).json({
      success: true,
      code,
      message,
      data,
      timestamp: new Date().toISOString(),
    });
  }

  static error(res, message = "操作失败", code = 500, error = null) {
    return res.status(code).json({
      success: false,
      code,
      message,
      error: process.env.NODE_ENV === "development" ? error : null,
      timestamp: new Date().toISOString(),
    });
  }

  static paginate(data, page, limit, total) {
    return {
      items: data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(total),
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }
}

/**
 * 平台仪表板
 */
exports.getDashboard = async (req, res) => {
  try {
    // 初始化SAAS数据库连接
    if (!saasDb) {
      saasDb = await TenantDatabaseManager.initSaasConnection();
    }

    // 获取平台核心指标
    const [tenantStats] = await saasDb.query(
      'SELECT COUNT(*) as totalTenants, COUNT(CASE WHEN status = "active" THEN 1 END) as activeTenants, COUNT(CASE WHEN subscriptionPlan = "trial" THEN 1 END) as trialTenants FROM tenants',
    );

    const { totalTenants, activeTenants, trialTenants } = tenantStats[0];

    // 模拟其他指标
    const monthlyRevenue = 45678;
    const activeUsers = 287;

    // 获取最新租户
    const [recentTenants] = await saasDb.query(
      "SELECT * FROM tenants ORDER BY createdAt DESC LIMIT 5",
    );

    // 生成模拟收入数据的辅助方法
    function generateMockRevenueData(days) {
      const data = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        data.push({
          date: date.toISOString().split("T")[0],
          revenue: Math.floor(Math.random() * 10000) + 5000,
        });
      }
      return data;
    }

    // 生成模拟租户分布数据的辅助方法
    function generateMockTenantDistribution() {
      return [
        { plan: "trial", count: 15, percentage: 30 },
        { plan: "basic", count: 20, percentage: 40 },
        { plan: "standard", count: 10, percentage: 20 },
        { plan: "premium", count: 4, percentage: 8 },
        { plan: "enterprise", count: 1, percentage: 2 },
      ];
    }

    // 模拟收入趋势数据
    const revenueData = generateMockRevenueData(30);

    // 模拟租户分布数据
    const tenantDistribution = generateMockTenantDistribution();

    const dashboardData = {
      metrics: {
        totalTenants,
        activeTenants,
        trialTenants,
        monthlyRevenue,
        activeUsers,
        systemHealth: 99.9,
      },
      recentTenants: recentTenants.map((tenant) => ({
        id: tenant.id,
        companyName: tenant.companyName,
        tenantCode: tenant.tenantCode,
        status: tenant.status,
        subscriptionPlan: tenant.subscriptionPlan,
        userCount: 0, // 简化
        lastActiveAt: tenant.lastActiveAt,
        createdAt: tenant.createdAt,
      })),
      charts: {
        revenueData,
        tenantDistribution,
      },
    };

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("dashboard/index", {
        title: "平台仪表板",
        ...dashboardData,
      });
    } else {
      // 如果是API请求，返回JSON
      return ResponseHelper.success(res, dashboardData, "获取仪表板数据成功");
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取仪表板数据失败:", error); } catch(_) {}

    return ResponseHelper.error(res, "获取仪表板数据失败", 500, error.message);
  }
};

/**
 * 获取租户列表
 */
exports.getTenants = async (req, res) => {
  try {
    // 初始化SAAS数据库连接
    if (!saasDb) {
      saasDb = await TenantDatabaseManager.initSaasConnection();
    }

    const {
      page = 1,
      limit = 10,
      search = "",
      status = "",
      subscriptionPlan = "",
      scale = "",
      sortBy = "createdAt",
      sortOrder = "DESC",
    } = req.query;

    // 验证分页参数
    if (isNaN(page) || isNaN(limit) || page < 1 || limit < 1 || limit > 100) {
      return ResponseHelper.error(res, "分页参数无效", 400);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    let whereClause = "1=1";
    const params = [];

    // 搜索条件
    if (search) {
      whereClause +=
        " AND (companyName LIKE ? OR contactName LIKE ? OR contactEmail LIKE ? OR tenantCode LIKE ?)";
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // 状态筛选
    if (status) {
      whereClause += " AND status = ?";
      params.push(status);
    }

    // 订阅计划筛选
    if (subscriptionPlan) {
      whereClause += " AND subscriptionPlan = ?";
      params.push(subscriptionPlan);
    }

    // 排序字段验证
    const allowedSortFields = [
      "createdAt",
      "companyName",
      "status",
      "subscriptionPlan",
      "lastActiveAt",
    ];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : "createdAt";
    const order = sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC";

    // 查询租户总数
    const [countResult] = await saasDb.query(
      `
      SELECT COUNT(*) as total FROM tenants WHERE ${whereClause}
    `,
      { replacements: params },
    );
    const count = countResult[0].total;

    // 查询租户数据
    const [tenants] = await saasDb.query(
      `
      SELECT * FROM tenants 
      WHERE ${whereClause}
      ORDER BY ${sortField} ${order}
      LIMIT ? OFFSET ?
    `,
      { replacements: [...params, parseInt(limit), offset] },
    );

    const paginatedData = ResponseHelper.paginate(tenants, page, limit, count);

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("tenants/index", {
        title: "租户管理",
        tenants: paginatedData.items,
        pagination: paginatedData, // 传递完整的分页数据对象
        searchParams: {
          search,
          status,
          subscriptionPlan,
          scale,
          sortBy,
          sortOrder,
        },
      });
    } else {
      // 如果是API请求，返回JSON
      return ResponseHelper.success(res, paginatedData, "获取租户列表成功");
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取租户列表失败:", error); } catch(_) {}

    return ResponseHelper.error(res, "获取租户列表失败", 500, error.message);
  }
};

/**
 * 获取租户详情
 */
exports.getTenantDetail = async (req, res) => {
  try {
    // 初始化SAAS数据库连接
    if (!saasDb) {
      saasDb = await TenantDatabaseManager.initSaasConnection();
    }

    const { id } = req.params;

    // 查询租户基本信息
    const [tenantResult] = await saasDb.query(
      "SELECT * FROM tenants WHERE id = ?",
      { replacements: [id] },
    );

    if (tenantResult.length === 0) {
      return ResponseHelper.error(res, "租户不存在", 404);
    }

    const tenant = tenantResult[0];

    // 模拟统计数据
    const tenantDetail = {
      tenant: tenant,
      tenantUsers: [], // 简化
      tenantFlocks: [], // 简化
      tenantStats: {
        totalUsers: 10,
        activeUsers: 8,
        totalFlocks: 5,
        totalGeese: 500,
        monthlyRevenue: 2999,
        storageUsed: "2.3GB",
      },
      monthlyStats: {
        newUsers: 3,
        activeFlocks: 4,
        eggProduction: 1200,
        healthAlerts: 2,
      },
      productionStats: {
        dailyEggs: 45,
        weeklyEggs: 310,
        monthlyEggs: 1350,
      },
      healthAlerts: [
        { type: "warning", message: "鹅群A体温异常", date: new Date() },
        { type: "info", message: "疫苗接种提醒", date: new Date() },
      ],
      usageStats: {
        apiCalls: 850,
        storageUsage: 65,
        bandwidth: "120MB",
      },
    };

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("tenants/detail", {
        title: `租户详情 - ${tenant.companyName}`,
        ...tenantDetail,
      });
    } else {
      // 如果是API请求，返回JSON
      return ResponseHelper.success(res, tenantDetail, "获取租户详情成功");
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取租户详情失败:", error); } catch(_) {}

    return ResponseHelper.error(res, "获取租户详情失败", 500, error.message);
  }
};

/**
 * 创建租户
 */
exports.createTenant = async (req, res) => {
  try {
    const {
      companyName,
      contactName,
      contactPhone,
      contactEmail,
      address,
      businessLicense,
      industry,
      scale,
      subscriptionPlan,
    } = req.body;

    // 验证必填字段
    if (!companyName || !contactName || !contactPhone || !contactEmail) {
      return ResponseHelper.error(res, "缺少必填字段", 400);
    }

    // 生成租户代码
    const tenantCode = await this.generateTenantCode();

    // 获取订阅计划配置
    const planConfig = this.getPlanConfig(subscriptionPlan || "trial");

    const tenant = await Tenant.create({
      tenantCode,
      companyName,
      contactName,
      contactPhone,
      contactEmail,
      address: address || "",
      businessLicense: businessLicense || "",
      industry: industry || "",
      scale: scale || "small",
      status: "pending",
      subscriptionPlan: subscriptionPlan || "trial",
      subscriptionStartDate: new Date(),
      subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
      maxUsers: planConfig.maxUsers,
      maxFlocks: planConfig.maxFlocks,
      storageLimit: planConfig.storageLimit,
      apiCallsLimit: planConfig.apiCallsLimit,
      monthlyFee: planConfig.monthlyPrice,
    });

    return ResponseHelper.success(res, tenant, "租户创建成功", 201);
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("创建租户失败:", error); } catch(_) {}

    return ResponseHelper.error(res, "创建租户失败", 500, error.message);
  }
};

/**
 * 更新租户
 */
exports.updateTenant = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const tenant = await Tenant.findByPk(id);
    if (!tenant) {
      return ResponseHelper.error(res, "租户不存在", 404);
    }

    await tenant.update(updateData);

    return ResponseHelper.success(res, tenant, "租户更新成功");
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("更新租户失败:", error); } catch(_) {}

    return ResponseHelper.error(res, "更新租户失败", 500, error.message);
  }
};

/**
 * 删除租户
 */
exports.deleteTenant = async (req, res) => {
  try {
    const { id } = req.params;

    const tenant = await Tenant.findByPk(id);
    if (!tenant) {
      return ResponseHelper.error(res, "租户不存在", 404);
    }

    await tenant.destroy();

    return ResponseHelper.success(res, null, "租户删除成功");
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("删除租户失败:", error); } catch(_) {}

    return ResponseHelper.error(res, "删除租户失败", 500, error.message);
  }
};

// 辅助方法
exports.getRevenueData = async (days) => {
  // 模拟收入数据
  const data = [];
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    data.push({
      date: date.toISOString().split("T")[0],
      revenue: Math.floor(Math.random() * 10000) + 5000,
    });
  }
  return data;
};

exports.getTenantDistribution = async () => {
  // 实际应从数据库统计不同订阅计划的租户数量
  const plans = ["trial", "basic", "standard", "premium", "enterprise"];
  const totalTenants = await Tenant.count();

  // 模拟数据 - 实际应从数据库查询
  const counts = {
    trial: Math.floor(totalTenants * 0.3),
    basic: Math.floor(totalTenants * 0.4),
    standard: Math.floor(totalTenants * 0.2),
    premium: Math.floor(totalTenants * 0.08),
    enterprise: Math.floor(totalTenants * 0.02),
  };

  return plans.map((plan) => ({
    plan,
    count: counts[plan],
    percentage: Math.round((counts[plan] / totalTenants) * 100),
  }));
};

exports.getTenantStats = async (tenantId) => {
  // 获取租户用户数量
  const totalUsers = await TenantUser.count({
    where: {
      tenantId,
      status: "active",
    },
  });

  // 获取鹅群数量和总鹅数
  const flocks = await TenantFlock.findAll({
    where: {
      tenantId,
      status: "active",
    },
  });

  const totalFlocks = flocks.length;
  const totalGeese = flocks.reduce((sum, flock) => sum + flock.currentCount, 0);

  // 计算月度增长（示例：随机数）
  const monthlyGrowth = parseFloat((Math.random() * 10 + 5).toFixed(2)); // 5-15% 之间

  // 计算收入增长（示例：随机数）
  const revenueGrowth = parseFloat((Math.random() * 10 + 3).toFixed(2)); // 3-13% 之间

  return {
    totalUsers,
    totalFlocks,
    totalGeese,
    monthlyGrowth,
    revenueGrowth,
  };
};

exports.getMonthlyStats = async (tenantId) => {
  // 模拟月度统计数据
  return {
    newFlocks: 2,
    totalEggs: 1250,
    healthChecks: 15,
    apiCalls: 3200,
    storageUsed: **********,
  };
};

exports.getProductionStats = async (tenantId) => {
  // 获取鹅群数据
  const flocks = await TenantFlock.findAll({
    where: {
      tenantId,
      status: "active",
    },
  });

  // 计算产蛋数据（示例数据）
  const monthlyEggs = flocks.reduce(
    (sum, flock) => sum + Math.floor(Math.random() * 1000) + 500,
    0,
  );

  const avgDailyEggs = Math.floor(monthlyEggs / 30);

  // 计算饲料转化率（示例数据）
  const feedConversionRatio = parseFloat((Math.random() * 1.5 + 2).toFixed(2)); // 2-3.5 之间

  // 计算生产效率（示例数据）
  const efficiency = parseFloat((Math.random() * 10 + 80).toFixed(2)); // 80-90% 之间

  // 计算产蛋增长（示例数据）
  const eggGrowth = parseFloat((Math.random() * 10 + 5).toFixed(2)); // 5-15% 之间

  return {
    monthlyEggs,
    avgDailyEggs,
    feedConversionRatio,
    efficiency,
    eggGrowth,
  };
};

exports.getHealthAlerts = async (tenantId) => {
  // 模拟健康预警数据
  return [
    {
      level: "warning",
      icon: "exclamation-triangle-fill",
      title: "鹅群健康预警",
      message: "白鹅群A有2只鹅出现轻微症状，建议关注",
      createdAt: new Date(),
    },
    {
      level: "info",
      icon: "info-circle-fill",
      title: "疫苗接种提醒",
      message: "灰鹅群B需要在3天内进行疫苗接种",
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    },
  ];
};

exports.getUsageStats = async (tenantId) => {
  // 模拟使用统计数据
  return {
    storageUsed: **********,
    monthlyApiCalls: 3200,
  };
};

exports.generateTenantCode = async () => {
  // 生成唯一租户代码
  const prefix = "T";
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `${prefix}${timestamp}${random}`;
};

exports.getPlanConfig = (planCode) => {
  const plans = {
    trial: {
      maxUsers: 3,
      maxFlocks: 5,
      storageLimit: 1073741824,
      apiCallsLimit: 1000,
      monthlyPrice: 0,
    },
    basic: {
      maxUsers: 10,
      maxFlocks: 20,
      storageLimit: 5368709120,
      apiCallsLimit: 10000,
      monthlyPrice: 99,
    },
    standard: {
      maxUsers: 50,
      maxFlocks: 100,
      storageLimit: **********0,
      apiCallsLimit: 50000,
      monthlyPrice: 299,
    },
    premium: {
      maxUsers: 200,
      maxFlocks: 500,
      storageLimit: 107374182400,
      apiCallsLimit: 200000,
      monthlyPrice: 599,
    },
    enterprise: {
      maxUsers: 1000,
      maxFlocks: 2000,
      storageLimit: 1099511627776,
      apiCallsLimit: 1000000,
      monthlyPrice: 1999,
    },
  };
  return plans[planCode] || plans.trial;
};
