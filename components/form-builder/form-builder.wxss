/* components/form-builder/form-builder.wxss */

.form-builder {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.form-builder.hidden {
  opacity: 0;
  pointer-events: none;
}

.form-builder.visible {
  opacity: 1;
  pointer-events: auto;
}

.form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.form-container {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

/* 表单头部 */
.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  transition: all 0.2s ease;
}

.close-btn:active {
  background-color: #e0e0e0;
  transform: scale(0.9);
}

.close-icon {
  font-size: 32rpx;
  color: #666;
  line-height: 1;
}

/* 表单内容 */
.form-content {
  flex: 1;
  padding: 32rpx;
  max-height: 60vh;
}

.form-field {
  margin-bottom: 40rpx;
}

.form-field:last-child {
  margin-bottom: 0;
}

/* 字段标签 */
.field-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.required-mark {
  color: #ff4d4f;
  font-size: 28rpx;
  margin-left: 4rpx;
}

/* 字段容器 */
.field-input,
.field-textarea,
.field-picker,
.field-switch,
.field-radio,
.field-checkbox,
.field-slider,
.field-rate {
  position: relative;
}

/* 错误提示 */
.field-error {
  margin-top: 8rpx;
  padding: 8rpx 16rpx;
  background-color: #fff2f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4d4f;
}

.error-text {
  font-size: 24rpx;
  color: #ff4d4f;
  line-height: 1.4;
}

/* 帮助文本 */
.field-help {
  margin-top: 8rpx;
}

.help-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 表单底部 */
.form-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 0 0 16rpx 16rpx;
}

.cancel-btn,
.submit-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background-color: #e0e0e0;
}

.submit-btn {
  background-color: #0066cc;
  color: #fff;
}

.submit-btn:active {
  background-color: #0052a3;
}

.submit-btn:disabled,
.submit-btn.submitting {
  background-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

/* 多行文本框样式 */
.field-textarea {
  margin-top: 16rpx;
}

.textarea-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #E8E8E8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.textarea-input:focus {
  border-color: #0066CC;
}

/* 选择器样式 */
.field-picker {
  margin-top: 16rpx;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border: 2rpx solid #E8E8E8;
  border-radius: 8rpx;
  background-color: #fff;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.picker-text:empty::before {
  content: attr(placeholder);
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  transform: rotate(90deg);
}

/* 开关样式 */
.field-switch {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

/* 显示字段样式 */
.field-display {
  padding: 24rpx 0;
}

.display-content {
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 24rpx 32rpx;
  min-height: 80rpx;
  display: flex;
  align-items: center;
}

.display-text {
  color: #495057;
  font-size: 28rpx;
  line-height: 1.4;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .form-container {
    width: 95%;
    max-height: 85vh;
  }

  .form-content {
    padding: 24rpx;
  }

  .form-footer {
    padding: 24rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .form-container {
    background-color: #1f1f1f;
  }

  .form-title {
    color: #fff;
  }

  .form-header {
    border-bottom-color: #333;
  }

  .close-btn {
    background-color: #333;
  }

  .close-icon {
    color: #ccc;
  }

  .label-text {
    color: #fff;
  }

  .form-footer {
    background-color: #2a2a2a;
    border-top-color: #333;
  }
}