<!-- 通用空状态组件 -->
<view 
  class="c-empty-state c-empty-state--{{verticalAlign}} c-empty-state--icon-{{iconSize}} data-type-{{type}} {{customClass}}"
  style="{{customStyle}}"
  bindtap="onEmptyTap"
>
  <!-- 空状态图标 -->
  <view class="c-empty-state__icon">
    <image 
      class="c-empty-state__icon-img" 
      src="{{icon || presets[type].icon}}"
      mode="aspectFit"
      style="{{type === 'loading' ? 'animation: c-empty-spin 1s linear infinite' : ''}}"
    ></image>
  </view>
  
  <!-- 空状态标题 -->
  <view wx:if="{{title || presets[type].title}}" class="c-empty-state__title">
    <text>{{title || presets[type].title}}</text>
  </view>
  
  <!-- 空状态描述 -->
  <view wx:if="{{description || presets[type].description}}" class="c-empty-state__description">
    <text>{{description || presets[type].description}}</text>
  </view>
  
  <!-- 操作按钮 -->
  <view 
    wx:if="{{showButton && (buttonText || presets[type].buttonText)}}" 
    class="c-empty-state__button"
  >
    <button 
      class="c-empty-button type-{{buttonType}}"
      size="medium"
      bindtap="onButtonTap"
    >
      {{buttonText || presets[type].buttonText}}
    </button>
  </view>
  
  <!-- 自定义内容插槽 -->
  <view class="c-empty-state__slot">
    <slot></slot>
  </view>
</view>