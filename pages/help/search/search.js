// pages/help/search/search.js
Page({
  data: {
    searchKeyword: '',
    isSearching: false,
    showSuggestions: false,
    showHistory: true,
    
    // 搜索建议
    suggestions: [],
    
    // 搜索历史
    searchHistory: [],
    
    // 热门搜索
    hotSearches: [
      'AI诊断', '健康记录', '数据同步', '环境监控',
      '登录问题', '密码重置', '数据导出', '设备连接'
    ],
    
    // 筛选类型
    activeFilter: 'all',
    filterTypes: [
      { key: 'all', name: '全部', count: 0 },
      { key: 'articles', name: '文章', count: 0 },
      { key: 'questions', name: '问题', count: 0 },
      { key: 'categories', name: '分类', count: 0 }
    ],
    
    // 搜索结果
    searchResults: {
      articles: [],
      questions: [],
      categories: []
    },
    
    filteredResults: {
      articles: [],
      questions: [],
      categories: []
    },
    
    totalResults: 0,
    
    // 快速入口
    quickAccess: [
      { id: 1, title: '常见问题', icon: '❓', action: 'faq' },
      { id: 2, title: '新手指南', icon: '🚀', action: 'newbie' },
      { id: 3, title: '视频教程', icon: '🎥', action: 'video' },
      { id: 4, title: '联系客服', icon: '🎧', action: 'contact' }
    ],
    
    // 模拟数据
    allData: {
      articles: [
        {
          id: 201,
          title: '如何添加健康记录',
          summary: '详细介绍如何在系统中添加和管理鹅群的健康记录',
          categoryName: '健康管理',
          views: 580,
          tags: ['健康记录', '数据录入']
        },
        {
          id: 202,
          title: 'AI诊断使用说明',
          summary: '学习如何使用AI智能诊断功能，快速识别鹅群健康问题',
          categoryName: '健康管理',
          views: 425,
          tags: ['AI诊断', '智能识别']
        },
        {
          id: 301,
          title: '环境监控数据说明',
          summary: '了解各项环境参数的含义和标准值范围',
          categoryName: '生产管理',
          views: 356,
          tags: ['环境监控', '数据分析']
        }
      ],
      questions: [
        {
          id: 1,
          question: '如何重置登录密码？',
          answer: '您可以在登录页面点击"忘记密码"，通过手机验证码重置密码。',
          category: 'account',
          expanded: false
        },
        {
          id: 2,
          question: 'AI诊断的准确率如何？',
          answer: '我们的AI诊断系统准确率达到95%以上，建议结合专业兽医意见。',
          category: 'health',
          expanded: false
        },
        {
          id: 3,
          question: '数据同步失败怎么办？',
          answer: '请检查网络连接，重启应用或联系技术支持。',
          category: 'technical',
          expanded: false
        }
      ],
      categories: [
        {
          id: 1,
          title: '账户与登录',
          icon: '👤',
          description: '账户注册、登录问题、个人信息管理'
        },
        {
          id: 2,
          title: '健康管理',
          icon: '🩺',
          description: '鹅群健康监控、AI诊断、健康记录管理'
        },
        {
          id: 3,
          title: '生产管理',
          icon: '🏭',
          description: '环境监控、库存管理、生产数据统计'
        }
      ]
    }
  },

  onLoad: function (options) {
    const { keyword } = options;
    
    if (keyword) {
      this.setData({
        searchKeyword: decodeURIComponent(keyword)
      });
      this.performSearch();
    }
    
    this.loadSearchHistory();
  },

  onShow: function () {
    // 页面显示时的逻辑
  },

  // 加载搜索历史
  loadSearchHistory: function() {
    const history = wx.getStorageSync('helpSearchHistory') || [];
    this.setData({
      searchHistory: history.slice(0, 10) // 只显示最近10条
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    if (keyword.trim()) {
      this.getSuggestions(keyword);
      this.setData({
        showSuggestions: true,
        showHistory: false
      });
    } else {
      this.setData({
        showSuggestions: false,
        showHistory: true,
        isSearching: false
      });
    }
  },

  // 获取搜索建议
  getSuggestions: function(keyword) {
    const allKeywords = [
      ...this.data.hotSearches,
      'AI诊断准确率', '健康记录添加', '环境数据异常',
      '登录密码重置', '数据同步问题', '设备连接失败',
      '生产数据导出', '系统设置', '账户管理'
    ];
    
    const suggestions = allKeywords
      .filter(item => item.includes(keyword))
      .slice(0, 5);
    
    this.setData({ suggestions });
  },

  // 清除搜索
  onClearSearch: function() {
    this.setData({
      searchKeyword: '',
      showSuggestions: false,
      showHistory: true,
      isSearching: false
    });
  },

  // 选择搜索建议
  onSelectSuggestion: function(e) {
    const { keyword } = e.currentTarget.dataset;
    this.setData({
      searchKeyword: keyword,
      showSuggestions: false
    });
    this.performSearch();
  },

  // 选择搜索历史
  onSelectHistory: function(e) {
    const { keyword } = e.currentTarget.dataset;
    this.setData({
      searchKeyword: keyword,
      showHistory: false
    });
    this.performSearch();
  },

  // 删除搜索历史
  onDeleteHistory: function(e) {
    e.stopPropagation();
    const { keyword, index } = e.currentTarget.dataset;
    
    const history = this.data.searchHistory;
    history.splice(index, 1);
    
    this.setData({ searchHistory: history });
    
    // 更新本地存储
    wx.setStorageSync('helpSearchHistory', history);
  },

  // 清空搜索历史
  onClearHistory: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ searchHistory: [] });
          wx.removeStorageSync('helpSearchHistory');
          wx.showToast({
            title: '已清空历史',
            icon: 'success'
          });
        }
      }
    });
  },

  // 选择热门搜索
  onSelectHotSearch: function(e) {
    const { keyword } = e.currentTarget.dataset;
    this.setData({
      searchKeyword: keyword,
      showHistory: false
    });
    this.performSearch();
  },

  // 执行搜索
  onSearch: function() {
    if (!this.data.searchKeyword.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }
    
    this.performSearch();
  },

  // 执行搜索逻辑
  performSearch: function() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) return;
    
    this.setData({
      isSearching: true,
      showSuggestions: false,
      showHistory: false
    });
    
    // 保存搜索历史
    this.saveSearchHistory(keyword);
    
    // 执行搜索
    this.searchContent(keyword);
  },

  // 保存搜索历史
  saveSearchHistory: function(keyword) {
    let history = wx.getStorageSync('helpSearchHistory') || [];
    
    // 移除重复项
    history = history.filter(item => item !== keyword);
    
    // 添加到开头
    history.unshift(keyword);
    
    // 只保留最近20条
    history = history.slice(0, 20);
    
    // 更新数据和存储
    this.setData({
      searchHistory: history.slice(0, 10)
    });
    wx.setStorageSync('helpSearchHistory', history);
  },

  // 搜索内容
  searchContent: function(keyword) {
    const { allData } = this.data;
    const lowerKeyword = keyword.toLowerCase();
    
    // 搜索文章
    const articles = allData.articles.filter(item =>
      item.title.toLowerCase().includes(lowerKeyword) ||
      item.summary.toLowerCase().includes(lowerKeyword) ||
      item.tags.some(tag => tag.toLowerCase().includes(lowerKeyword))
    );
    
    // 搜索问题
    const questions = allData.questions.filter(item =>
      item.question.toLowerCase().includes(lowerKeyword) ||
      item.answer.toLowerCase().includes(lowerKeyword)
    ).map(item => ({ ...item, expanded: false }));
    
    // 搜索分类
    const categories = allData.categories.filter(item =>
      item.title.toLowerCase().includes(lowerKeyword) ||
      item.description.toLowerCase().includes(lowerKeyword)
    );
    
    const searchResults = { articles, questions, categories };
    const totalResults = articles.length + questions.length + categories.length;
    
    // 更新筛选类型计数
    const filterTypes = this.data.filterTypes.map(filter => ({
      ...filter,
      count: filter.key === 'all' ? totalResults : 
             filter.key === 'articles' ? articles.length :
             filter.key === 'questions' ? questions.length :
             categories.length
    }));
    
    this.setData({
      searchResults,
      totalResults,
      filterTypes
    });
    
    // 应用当前筛选
    this.applyFilter();
  },

  // 选择筛选类型
  onSelectFilter: function(e) {
    const { filter } = e.currentTarget.dataset;
    this.setData({ activeFilter: filter });
    this.applyFilter();
  },

  // 应用筛选
  applyFilter: function() {
    const { activeFilter, searchResults } = this.data;
    
    let filteredResults = {
      articles: [],
      questions: [],
      categories: []
    };
    
    if (activeFilter === 'all') {
      filteredResults = searchResults;
    } else if (activeFilter === 'articles') {
      filteredResults.articles = searchResults.articles;
    } else if (activeFilter === 'questions') {
      filteredResults.questions = searchResults.questions;
    } else if (activeFilter === 'categories') {
      filteredResults.categories = searchResults.categories;
    }
    
    this.setData({ filteredResults });
  },

  // 展开/折叠问题
  onToggleQuestion: function(e) {
    const { index } = e.currentTarget.dataset;
    const questions = this.data.filteredResults.questions;
    
    questions[index].expanded = !questions[index].expanded;
    
    this.setData({
      'filteredResults.questions': questions
    });
  },

  // 查看文章
  onViewArticle: function(e) {
    const { article } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/help/article/article?id=${article.id}&title=${encodeURIComponent(article.title)}`
    });
  },

  // 查看分类
  onViewCategory: function(e) {
    const { category } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/help/category/category?id=${category.id}&title=${encodeURIComponent(category.title)}`
    });
  },

  // 快速入口
  onQuickAccess: function(e) {
    const { action } = e.currentTarget.dataset;
    
    switch (action) {
      case 'faq':
        wx.navigateTo({
          url: '/pages/help/faq/faq'
        });
        break;
      case 'newbie':
        wx.showToast({
          title: '新手指南开发中',
          icon: 'none'
        });
        break;
      case 'video':
        wx.showToast({
          title: '视频教程开发中',
          icon: 'none'
        });
        break;
      case 'contact':
        this.onContact();
        break;
    }
  },

  // 反馈
  onFeedback: function() {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback'
    });
  },

  // 联系客服
  onContact: function() {
    wx.showModal({
      title: '联系客服',
      content: '客服热线：************\n工作时间：周一至周五 9:00-18:00',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    const { searchKeyword } = this.data;
    return {
      title: `智慧养鹅 - 搜索${searchKeyword ? `"${searchKeyword}"` : '帮助'}`,
      desc: '快速找到您需要的帮助内容',
      path: `/pages/help/search/search${searchKeyword ? `?keyword=${encodeURIComponent(searchKeyword)}` : ''}`
    };
  }
});