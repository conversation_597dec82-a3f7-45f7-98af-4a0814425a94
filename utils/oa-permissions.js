/**
 * OA权限系统兼容性封装
 * 兼容旧版API调用，重新导出统一权限系统
 */

// 前端权限常量定义（从后端同步）
const PERMISSIONS = {
  // 平台级权限
  PLATFORM: {
    SUPER_ADMIN: 'platform:super_admin',
    TENANT_MANAGE: 'platform:tenant:manage',
    SYSTEM_CONFIG: 'platform:system:config',
    CROSS_TENANT_VIEW: 'platform:cross_tenant:view',
    ANALYTICS: 'platform:analytics:view',
    MONITORING: 'platform:monitoring:view'
  },
  
  // OA办公权限
  OA: {
    ACCESS: 'oa:access',
    FINANCE_VIEW: 'oa:finance:view',
    FINANCE_MANAGE: 'oa:finance:manage',
    PURCHASE_VIEW: 'oa:purchase:view',
    PURCHASE_CREATE: 'oa:purchase:create',
    PURCHASE_APPROVE: 'oa:purchase:approve',
    REIMBURSEMENT_VIEW: 'oa:reimbursement:view',
    REIMBURSEMENT_CREATE: 'oa:reimbursement:create',
    REIMBURSEMENT_APPROVE: 'oa:reimbursement:approve',
    APPROVAL_PROCESS: 'oa:approval:process',
    STAFF_MANAGE: 'oa:staff:manage'
  },
  
  // 生产管理权限
  PRODUCTION: {
    VIEW: 'production:view',
    MANAGE: 'production:manage',
    RECORD_CREATE: 'production:record:create',
    RECORD_UPDATE: 'production:record:update',
    INVENTORY_VIEW: 'production:inventory:view',
    INVENTORY_MANAGE: 'production:inventory:manage'
  },
  
  // 健康管理权限
  HEALTH: {
    VIEW: 'health:view',
    MANAGE: 'health:manage',
    DIAGNOSIS: 'health:diagnosis',
    AI_DIAGNOSIS: 'health:ai_diagnosis'
  },
  
  // 商城权限
  SHOP: {
    VIEW: 'shop:view',
    MANAGE: 'shop:manage',
    ORDER_VIEW: 'shop:order:view',
    ORDER_PROCESS: 'shop:order:process'
  },
  
  // 用户管理权限
  USER: {
    VIEW: 'user:view',
    MANAGE: 'user:manage'
  }
};

// 角色定义
const ROLES = {
  PLATFORM_SUPER_ADMIN: 'platform_super_admin',
  TENANT_ADMIN: 'tenant_admin',
  DEPARTMENT_MANAGER: 'department_manager',
  EMPLOYEE: 'employee',
  VETERINARIAN: 'veterinarian',
  PRODUCTION_MANAGER: 'production_manager',
  FINANCIAL_STAFF: 'financial_staff',
  SALES_STAFF: 'sales_staff'
};

// 简化的权限映射（前端版本）
const ROLE_PERMISSIONS = {
  [ROLES.PLATFORM_SUPER_ADMIN]: Object.values(PERMISSIONS).flatMap(p => Object.values(p)),
  [ROLES.TENANT_ADMIN]: [
    ...Object.values(PERMISSIONS.OA),
    ...Object.values(PERMISSIONS.PRODUCTION),
    ...Object.values(PERMISSIONS.HEALTH),
    ...Object.values(PERMISSIONS.SHOP),
    ...Object.values(PERMISSIONS.USER)
  ],
  [ROLES.DEPARTMENT_MANAGER]: [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.PURCHASE_VIEW,
    PERMISSIONS.OA.PURCHASE_CREATE,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.PRODUCTION.MANAGE,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.HEALTH.MANAGE,
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.USER.VIEW
  ],
  [ROLES.EMPLOYEE]: [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.SHOP.VIEW
  ],
  // 简化角色名兼容映射
  'admin': [
    ...Object.values(PERMISSIONS.OA),
    ...Object.values(PERMISSIONS.PRODUCTION),
    ...Object.values(PERMISSIONS.HEALTH),
    ...Object.values(PERMISSIONS.SHOP),
    ...Object.values(PERMISSIONS.USER)
  ],
  'manager': [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.PURCHASE_VIEW,
    PERMISSIONS.OA.PURCHASE_CREATE,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.PRODUCTION.MANAGE,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.HEALTH.MANAGE,
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.USER.VIEW
  ],
  'user': [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.SHOP.VIEW
  ]
};

/**
 * 前端权限检查器
 */
class FrontendPermissionChecker {
  static getRolePermissions(role) {
    return ROLE_PERMISSIONS[role] || [];
  }
  
  static hasPermission(role, permission) {
    const rolePermissions = this.getRolePermissions(role);
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[FrontendPermissionChecker] 角色/权限列表', role, rolePermissions); logger.debug && logger.debug('[FrontendPermissionChecker] 检查权限', permission); } catch(_) {}
    
    // 检查具体权限
    const hasExactPermission = rolePermissions.includes(permission);
    if (hasExactPermission) {
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[FrontendPermissionChecker] 找到精确权限匹配'); } catch(_) {}
      return true;
    }
    
    // 检查通配符权限
    const hasWildcardPermission = rolePermissions.some(perm => {
      if (perm.endsWith(':*')) {
        const prefix = perm.slice(0, -2);
        return permission.startsWith(prefix + ':');
      }
      return false;
    });
    
    if (hasWildcardPermission) {
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[FrontendPermissionChecker] 找到通配符权限匹配'); } catch(_) {}
      return true;
    }
    
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[FrontendPermissionChecker] 权限检查失败'); } catch(_) {}
    return false;
  }
}

/**
 * OA权限管理器（兼容性封装）
 */
class OAPermissionManager {
  constructor() {
    this.initialized = true;
  }
  
  async init() {
    return Promise.resolve();
  }
  
  hasPermission(permission) {
    try {
      const userInfo = wx.getStorageSync('user_info');
      if (!userInfo) {
        try { const logger = require('./logger.js'); logger.warn && logger.warn('[OA Permission] 未找到用户信息'); } catch(_) {}
        return false;
      }
      
      const userRole = userInfo.roleCode || userInfo.role;
      if (!userRole) {
        try { const logger = require('./logger.js'); logger.warn && logger.warn('[OA Permission] 未找到用户角色'); } catch(_) {}
        return false;
      }
      
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[OA Permission] 检查权限', { permission, userRole }); } catch(_) {}
      
      // 管理员拥有所有权限
      if (userRole === 'admin' || userRole === '管理员') {
        try { const logger = require('./logger.js'); logger.debug && logger.debug('[OA Permission] 管理员权限通过'); } catch(_) {}
        return true;
      }
      
      const hasPermission = FrontendPermissionChecker.hasPermission(userRole, permission);
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[OA Permission] 权限检查结果', hasPermission); } catch(_) {}
      
      return hasPermission;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[OA Permission] 权限检查失败', error); } catch(_) {}
      return false;
    }
  }
  
  hasRole(role) {
    try {
      const userInfo = wx.getStorageSync('user_info');
      return userInfo && userInfo.role === role;
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[OA Permission] 角色检查失败', error); } catch(_) {}
      return false;
    }
  }
  
  requirePermission(permission, errorMessage) {
    if (!this.hasPermission(permission)) {
      const message = errorMessage || `需要权限: ${permission}`;
      wx.showModal({
        title: '权限不足',
        content: message,
        showCancel: false
      });
      return false;
    }
    return true;
  }
  
  requireRole(role, errorMessage) {
    if (!this.hasRole(role)) {
      const message = errorMessage || `需要角色: ${role}`;
      wx.showModal({
        title: '权限不足',
        content: message,
        showCancel: false
      });
      return false;
    }
    return true;
  }
  
  checkPermissions(config) {
    if (!config) return true;
    
    if (config.permissions && Array.isArray(config.permissions)) {
      return config.permissions.every(permission => this.hasPermission(permission));
    }
    
    if (config.roles && Array.isArray(config.roles)) {
      return config.roles.some(role => this.hasRole(role));
    }
    
    return true;
  }
}

// 创建全局实例
const oaPermissionManager = new OAPermissionManager();

// 页面权限检查混入（兼容性封装）
const PermissionMixin = {
  onLoad() {
    // 权限系统初始化
    if (!oaPermissionManager.initialized) {
      oaPermissionManager.init().then(() => {
        this.onPermissionReady && this.onPermissionReady();
      });
    } else {
      this.onPermissionReady && this.onPermissionReady();
    }
  },

  // 权限检查方法
  hasPermission(permission) {
    return oaPermissionManager.hasPermission(permission);
  },

  hasRole(role) {
    return oaPermissionManager.hasRole(role);
  },

  requirePermission(permission, errorMessage) {
    return oaPermissionManager.requirePermission(permission, errorMessage);
  },

  requireRole(role, errorMessage) {
    return oaPermissionManager.requireRole(role, errorMessage);
  },

  checkPermissions(config) {
    return oaPermissionManager.checkPermissions(config);
  }
};

// 导出兼容性API
module.exports = {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
  OAPermissionManager,
  oaPermissionManager,
  PermissionMixin
};