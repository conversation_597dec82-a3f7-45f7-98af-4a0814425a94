#!/bin/bash

# 智慧养鹅本地开发环境一键启动脚本
echo "========================================="
echo "  智慧养鹅本地开发环境一键启动脚本"
echo "========================================="

# 检查是否在正确的目录
if [ ! -f "app.js" ] || [ ! -d "backend" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null
then
    echo "错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 安装小程序依赖（如果有）
echo "检查并安装小程序依赖..."
# 小程序一般不需要npm依赖，但保留此步骤以备将来使用

# 进入后端目录并安装依赖
echo "检查并安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
else
    echo "后端依赖已安装"
fi

# 返回项目根目录
cd ..

# 提示用户启动MySQL并检查数据库连接
echo ""
echo "请确保MySQL服务已启动并且数据库连接正常"
echo "如果尚未初始化数据库，请运行以下命令:"
echo "./backend/scripts/init-db-local.sh"

# 检查数据库连接（示例代码，根据实际需求调整）
mysql -u root -e "USE zhihuiyange_local;" &> /dev/null
if [ $? -ne 0 ]; then
  echo "数据库连接失败，请检查数据库配置"
  exit 1
fi

echo "数据库连接正常"

# 启动后端服务
echo "启动后端服务..."
cd backend
echo "后端API服务将运行在 http://localhost:3001"
echo "管理后台将运行在 http://localhost:3001/admin"
echo ""
echo "按 Ctrl+C 可以停止服务"
echo ""

# 使用nodemon启动开发服务器（如果安装了nodemon）
if command -v nodemon &> /dev/null
then
    export NODE_ENV=local
    nodemon app.js
else
    export NODE_ENV=local
    node app.js
fi