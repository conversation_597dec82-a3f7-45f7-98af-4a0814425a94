{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/models/*": ["src/models/*"], "@/middleware/*": ["src/middleware/*"], "@/endpoints/*": ["src/endpoints/*"], "@/utils/*": ["src/utils/*"], "@/config/*": ["src/config/*"]}}, "include": ["src/**/*", "types/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}