// 智慧养鹅小程序 - 高级交互管理器 V1.0
// 提供手势识别、拖拽、滑动等高级交互功能

/**
 * 手势识别器
 */
class GestureRecognizer {
  constructor(element, options = {}) {
    this.element = element;
    this.options = {
      threshold: 10,           // 最小移动距离
      longPressTime: 500,      // 长按时间阈值
      swipeThreshold: 50,      // 滑动距离阈值
      pinchThreshold: 10,      // 缩放阈值
      ...options
    };
    
    this.state = {
      isTracking: false,
      startPoint: null,
      currentPoint: null,
      startTime: 0,
      touches: []
    };
    
    this.callbacks = {};
    this.longPressTimer = null;
    
    this.init();
  }

  init() {
    this.bindEvents();
  }

  bindEvents() {
    // 绑定触摸事件
    this.element.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.element.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.element.addEventListener('touchend', this.onTouchEnd.bind(this));
    this.element.addEventListener('touchcancel', this.onTouchCancel.bind(this));
  }

  onTouchStart(e) {
    const touch = e.touches[0];
    this.state.isTracking = true;
    this.state.startPoint = { x: touch.clientX, y: touch.clientY };
    this.state.currentPoint = { ...this.state.startPoint };
    this.state.startTime = Date.now();
    this.state.touches = Array.from(e.touches);

    // 触发开始事件
    this.trigger('start', {
      point: this.state.startPoint,
      touches: this.state.touches
    });

    // 设置长按定时器
    this.longPressTimer = setTimeout(() => {
      if (this.state.isTracking) {
        this.trigger('longpress', {
          point: this.state.currentPoint,
          duration: Date.now() - this.state.startTime
        });
      }
    }, this.options.longPressTime);
  }

  onTouchMove(e) {
    if (!this.state.isTracking) return;

    const touch = e.touches[0];
    const newPoint = { x: touch.clientX, y: touch.clientY };
    const deltaX = newPoint.x - this.state.startPoint.x;
    const deltaY = newPoint.y - this.state.startPoint.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // 更新当前点
    this.state.currentPoint = newPoint;
    this.state.touches = Array.from(e.touches);

    // 如果移动距离超过阈值，取消长按
    if (distance > this.options.threshold && this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 触发移动事件
    this.trigger('move', {
      point: newPoint,
      delta: { x: deltaX, y: deltaY },
      distance,
      touches: this.state.touches
    });

    // 检测多点触控
    if (e.touches.length === 2) {
      this.handlePinch(e.touches);
    }
  }

  onTouchEnd(e) {
    if (!this.state.isTracking) return;

    const duration = Date.now() - this.state.startTime;
    const deltaX = this.state.currentPoint.x - this.state.startPoint.x;
    const deltaY = this.state.currentPoint.y - this.state.startPoint.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // 清除长按定时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 判断手势类型
    if (distance < this.options.threshold && duration < 300) {
      // 点击
      this.trigger('tap', {
        point: this.state.currentPoint,
        duration
      });
    } else if (distance > this.options.swipeThreshold) {
      // 滑动
      const direction = this.getSwipeDirection(deltaX, deltaY);
      this.trigger('swipe', {
        direction,
        distance,
        delta: { x: deltaX, y: deltaY },
        duration
      });
    }

    // 触发结束事件
    this.trigger('end', {
      point: this.state.currentPoint,
      delta: { x: deltaX, y: deltaY },
      distance,
      duration
    });

    this.reset();
  }

  onTouchCancel(e) {
    this.trigger('cancel', {});
    this.reset();
  }

  handlePinch(touches) {
    if (touches.length !== 2) return;

    const touch1 = touches[0];
    const touch2 = touches[1];
    const distance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    );

    if (!this.state.initialPinchDistance) {
      this.state.initialPinchDistance = distance;
      return;
    }

    const scale = distance / this.state.initialPinchDistance;
    const center = {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2
    };

    this.trigger('pinch', {
      scale,
      center,
      distance
    });
  }

  getSwipeDirection(deltaX, deltaY) {
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);

    if (absX > absY) {
      return deltaX > 0 ? 'right' : 'left';
    } else {
      return deltaY > 0 ? 'down' : 'up';
    }
  }

  reset() {
    this.state.isTracking = false;
    this.state.startPoint = null;
    this.state.currentPoint = null;
    this.state.startTime = 0;
    this.state.touches = [];
    this.state.initialPinchDistance = null;
  }

  on(event, callback) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
  }

  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  trigger(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => {
        callback(data);
      });
    }
  }

  destroy() {
    this.element.removeEventListener('touchstart', this.onTouchStart);
    this.element.removeEventListener('touchmove', this.onTouchMove);
    this.element.removeEventListener('touchend', this.onTouchEnd);
    this.element.removeEventListener('touchcancel', this.onTouchCancel);
    
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
    }
    
    this.callbacks = {};
  }
}

/**
 * 拖拽管理器
 */
class DragManager {
  constructor(options = {}) {
    this.options = {
      threshold: 10,
      enableSorting: true,
      enableDropZones: true,
      animationDuration: 300,
      ...options
    };
    
    this.state = {
      dragging: null,
      dragOffset: { x: 0, y: 0 },
      placeholder: null,
      activeDropZone: null
    };
    
    this.callbacks = {};
    this.init();
  }

  init() {
    this.bindEvents();
  }

  bindEvents() {
    // 全局事件监听
    document.addEventListener('touchmove', this.onGlobalTouchMove.bind(this));
    document.addEventListener('touchend', this.onGlobalTouchEnd.bind(this));
  }

  makeDraggable(element, data = {}) {
    const gestureRecognizer = new GestureRecognizer(element);
    
    gestureRecognizer.on('start', (e) => {
      this.startDrag(element, e.point, data);
    });
    
    gestureRecognizer.on('move', (e) => {
      if (this.state.dragging === element) {
        this.updateDrag(e.point);
      }
    });
    
    gestureRecognizer.on('end', () => {
      if (this.state.dragging === element) {
        this.endDrag();
      }
    });

    // 存储手势识别器引用以便后续清理
    element._dragGestureRecognizer = gestureRecognizer;
  }

  startDrag(element, point, data) {
    this.state.dragging = element;
    this.state.dragData = data;

    // 计算拖拽偏移
    const rect = element.getBoundingClientRect();
    this.state.dragOffset = {
      x: point.x - rect.left,
      y: point.y - rect.top
    };

    // 添加拖拽样式
    element.classList.add('dragging');

    // 创建占位符
    if (this.options.enableSorting) {
      this.createPlaceholder(element);
    }

    // 触发开始事件
    this.trigger('dragstart', {
      element,
      data,
      point
    });
  }

  updateDrag(point) {
    if (!this.state.dragging) return;

    const element = this.state.dragging;
    const x = point.x - this.state.dragOffset.x;
    const y = point.y - this.state.dragOffset.y;

    // 更新元素位置
    element.style.transform = `translate(${x}px, ${y}px)`;

    // 检测拖放区域
    if (this.options.enableDropZones) {
      this.checkDropZones(point);
    }

    // 检测排序
    if (this.options.enableSorting && this.state.placeholder) {
      this.checkSorting(point);
    }

    // 触发移动事件
    this.trigger('dragmove', {
      element,
      point,
      position: { x, y }
    });
  }

  endDrag() {
    if (!this.state.dragging) return;

    const element = this.state.dragging;
    const dropZone = this.state.activeDropZone;
    const placeholder = this.state.placeholder;

    // 执行放置逻辑
    if (dropZone) {
      this.performDrop(element, dropZone);
    } else if (placeholder) {
      this.performSort(element, placeholder);
    } else {
      this.returnToOriginal(element);
    }

    // 清理状态
    this.cleanup();
  }

  createPlaceholder(element) {
    const placeholder = element.cloneNode(false);
    placeholder.classList.add('drag-placeholder');
    placeholder.style.height = element.offsetHeight + 'px';
    element.parentNode.insertBefore(placeholder, element);
    this.state.placeholder = placeholder;
  }

  checkDropZones(point) {
    const dropZones = document.querySelectorAll('.drop-zone');
    let activeZone = null;

    dropZones.forEach(zone => {
      const rect = zone.getBoundingClientRect();
      if (point.x >= rect.left && point.x <= rect.right &&
          point.y >= rect.top && point.y <= rect.bottom) {
        activeZone = zone;
      }
    });

    // 更新活动拖放区域
    if (this.state.activeDropZone !== activeZone) {
      if (this.state.activeDropZone) {
        this.state.activeDropZone.classList.remove('drag-over');
      }
      
      if (activeZone) {
        activeZone.classList.add('drag-over');
        // 验证是否可以放置
        const canDrop = this.validateDrop(this.state.dragging, activeZone);
        activeZone.classList.toggle('drag-valid', canDrop);
        activeZone.classList.toggle('drag-invalid', !canDrop);
      }
      
      this.state.activeDropZone = activeZone;
    }
  }

  checkSorting(point) {
    if (!this.state.placeholder) return;

    const container = this.state.placeholder.parentNode;
    const siblings = Array.from(container.children).filter(
      child => child !== this.state.placeholder && child !== this.state.dragging
    );

    let insertBefore = null;
    
    for (const sibling of siblings) {
      const rect = sibling.getBoundingClientRect();
      const centerY = rect.top + rect.height / 2;
      
      if (point.y < centerY) {
        insertBefore = sibling;
        break;
      }
    }

    // 移动占位符
    if (insertBefore) {
      container.insertBefore(this.state.placeholder, insertBefore);
    } else {
      container.appendChild(this.state.placeholder);
    }
  }

  validateDrop(element, dropZone) {
    // 可以通过数据属性或回调函数来验证
    const acceptTypes = dropZone.dataset.acceptTypes;
    const elementType = element.dataset.dragType;
    
    if (acceptTypes && elementType) {
      return acceptTypes.split(',').includes(elementType);
    }
    
    // 触发验证事件
    let canDrop = true;
    this.trigger('validate', {
      element,
      dropZone,
      callback: (result) => { canDrop = result; }
    });
    
    return canDrop;
  }

  performDrop(element, dropZone) {
    if (this.validateDrop(element, dropZone)) {
      // 动画到拖放区域
      this.animateToTarget(element, dropZone, () => {
        this.trigger('drop', {
          element,
          dropZone,
          data: this.state.dragData
        });
      });
    } else {
      this.returnToOriginal(element);
    }
  }

  performSort(element, placeholder) {
    // 动画到占位符位置
    const placeholderRect = placeholder.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();
    
    const deltaX = placeholderRect.left - elementRect.left;
    const deltaY = placeholderRect.top - elementRect.top;
    
    element.style.transition = `transform ${this.options.animationDuration}ms ease`;
    element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    
    setTimeout(() => {
      // 插入到正确位置
      placeholder.parentNode.insertBefore(element, placeholder);
      element.style.transition = '';
      element.style.transform = '';
      
      this.trigger('sort', {
        element,
        data: this.state.dragData
      });
    }, this.options.animationDuration);
  }

  returnToOriginal(element) {
    element.style.transition = `transform ${this.options.animationDuration}ms ease`;
    element.style.transform = '';
    
    setTimeout(() => {
      element.style.transition = '';
    }, this.options.animationDuration);
  }

  animateToTarget(element, target, callback) {
    const targetRect = target.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();
    
    const deltaX = targetRect.left - elementRect.left;
    const deltaY = targetRect.top - elementRect.top;
    
    element.style.transition = `transform ${this.options.animationDuration}ms ease`;
    element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    
    setTimeout(() => {
      callback && callback();
    }, this.options.animationDuration);
  }

  cleanup() {
    const element = this.state.dragging;
    
    if (element) {
      element.classList.remove('dragging');
    }
    
    if (this.state.placeholder) {
      this.state.placeholder.remove();
    }
    
    if (this.state.activeDropZone) {
      this.state.activeDropZone.classList.remove('drag-over', 'drag-valid', 'drag-invalid');
    }
    
    this.state = {
      dragging: null,
      dragOffset: { x: 0, y: 0 },
      placeholder: null,
      activeDropZone: null
    };
  }

  on(event, callback) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
  }

  trigger(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => {
        callback(data);
      });
    }
  }

  destroy() {
    document.removeEventListener('touchmove', this.onGlobalTouchMove);
    document.removeEventListener('touchend', this.onGlobalTouchEnd);
    this.callbacks = {};
  }
}

/**
 * 滑动交互管理器
 */
class SwipeManager {
  constructor(options = {}) {
    this.options = {
      threshold: 50,
      velocity: 0.3,
      snapBack: true,
      ...options
    };
    
    this.swipeItems = new Set();
  }

  makeSwipeable(element, actions = []) {
    const gestureRecognizer = new GestureRecognizer(element, {
      swipeThreshold: this.options.threshold
    });
    
    let startTranslate = 0;
    let currentTranslate = 0;
    let isAnimating = false;
    
    // 创建动作按钮
    this.createSwipeActions(element, actions);
    
    gestureRecognizer.on('start', () => {
      if (isAnimating) return;
      startTranslate = this.getCurrentTranslate(element);
      element.classList.add('swiping');
    });
    
    gestureRecognizer.on('move', (e) => {
      if (isAnimating) return;
      
      currentTranslate = startTranslate + e.delta.x;
      
      // 限制滑动范围
      const maxLeft = 0;
      const maxRight = -this.getActionsWidth(element);
      currentTranslate = Math.max(maxRight, Math.min(maxLeft, currentTranslate));
      
      element.style.transform = `translateX(${currentTranslate}px)`;
    });
    
    gestureRecognizer.on('end', (e) => {
      if (isAnimating) return;
      
      element.classList.remove('swiping');
      
      const velocity = Math.abs(e.delta.x) / e.duration;
      const actionsWidth = this.getActionsWidth(element);
      
      let targetTranslate = startTranslate;
      
      // 根据滑动距离和速度决定最终位置
      if (e.delta.x < -this.options.threshold || velocity > this.options.velocity) {
        targetTranslate = -actionsWidth;
      } else if (e.delta.x > this.options.threshold || velocity > this.options.velocity) {
        targetTranslate = 0;
      }
      
      this.animateToPosition(element, targetTranslate);
    });
    
    // 存储引用
    element._swipeGestureRecognizer = gestureRecognizer;
    this.swipeItems.add(element);
  }

  createSwipeActions(element, actions) {
    // 检查是否已经存在动作容器
    let actionsContainer = element.querySelector('.swipe-actions');
    if (!actionsContainer) {
      actionsContainer = document.createElement('div');
      actionsContainer.className = 'swipe-actions';
      element.appendChild(actionsContainer);
    }
    
    // 清空现有动作
    actionsContainer.innerHTML = '';
    
    // 创建动作按钮
    actions.forEach(action => {
      const button = document.createElement('button');
      button.className = `swipe-action ${action.type || ''}`;
      button.innerHTML = action.icon ? `<i class="${action.icon}"></i>` : action.text;
      button.style.backgroundColor = action.color || '';
      
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        action.handler && action.handler(element);
        this.closeSwipe(element);
      });
      
      actionsContainer.appendChild(button);
    });
  }

  getCurrentTranslate(element) {
    const transform = element.style.transform;
    const match = transform.match(/translateX\((-?\d+(?:\.\d+)?)px\)/);
    return match ? parseFloat(match[1]) : 0;
  }

  getActionsWidth(element) {
    const actionsContainer = element.querySelector('.swipe-actions');
    return actionsContainer ? actionsContainer.offsetWidth : 0;
  }

  animateToPosition(element, targetTranslate) {
    element.style.transition = 'transform 0.3s ease';
    element.style.transform = `translateX(${targetTranslate}px)`;
    
    setTimeout(() => {
      element.style.transition = '';
    }, 300);
  }

  closeSwipe(element) {
    this.animateToPosition(element, 0);
  }

  closeAllSwipes() {
    this.swipeItems.forEach(element => {
      this.closeSwipe(element);
    });
  }

  destroy() {
    this.swipeItems.forEach(element => {
      if (element._swipeGestureRecognizer) {
        element._swipeGestureRecognizer.destroy();
      }
    });
    this.swipeItems.clear();
  }
}

/**
 * 高级交互管理器主类
 */
class AdvancedInteractionManager {
  constructor() {
    this.gestureRecognizer = null;
    this.dragManager = new DragManager();
    this.swipeManager = new SwipeManager();
    this.rippleEffects = new Set();
    
    this.init();
  }

  init() {
    this.setupRippleEffects();
    this.setupGlobalGestures();
  }

  // 创建手势识别器
  createGestureRecognizer(element, options) {
    return new GestureRecognizer(element, options);
  }

  // 使元素可拖拽
  makeDraggable(element, data) {
    this.dragManager.makeDraggable(element, data);
  }

  // 使元素可滑动
  makeSwipeable(element, actions) {
    this.swipeManager.makeSwipeable(element, actions);
  }

  // 设置波纹效果
  setupRippleEffects() {
    document.addEventListener('touchstart', (e) => {
      const element = e.target.closest('.ripple-container');
      if (element) {
        this.createRipple(element, e.touches[0]);
      }
    });
  }

  createRipple(container, touch) {
    const rect = container.getBoundingClientRect();
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;
    
    const ripple = document.createElement('div');
    ripple.className = 'ripple-effect';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    
    container.appendChild(ripple);
    this.rippleEffects.add(ripple);
    
    // 动画结束后移除
    setTimeout(() => {
      ripple.remove();
      this.rippleEffects.delete(ripple);
    }, 600);
  }

  // 设置全局手势
  setupGlobalGestures() {
    // 全局双击检测
    let lastTap = 0;
    document.addEventListener('touchend', (e) => {
      const currentTime = Date.now();
      const tapLength = currentTime - lastTap;
      
      if (tapLength < 300 && tapLength > 0) {
        // 双击事件
        const event = new CustomEvent('doubletap', {
          detail: {
            target: e.target,
            clientX: e.changedTouches[0].clientX,
            clientY: e.changedTouches[0].clientY
          }
        });
        e.target.dispatchEvent(event);
      }
      
      lastTap = currentTime;
    });
  }

  // 创建磁性吸附效果
  createMagneticEffect(element, targets) {
    const gestureRecognizer = this.createGestureRecognizer(element);
    
    gestureRecognizer.on('move', (e) => {
      const closestTarget = this.findClosestMagneticTarget(e.point, targets);
      
      if (closestTarget) {
        element.classList.add('attracted');
        closestTarget.classList.add('active');
        
        // 清除其他目标的活动状态
        targets.forEach(target => {
          if (target !== closestTarget) {
            target.classList.remove('active');
          }
        });
      } else {
        element.classList.remove('attracted');
        targets.forEach(target => target.classList.remove('active'));
      }
    });
  }

  findClosestMagneticTarget(point, targets) {
    let closestTarget = null;
    let minDistance = Infinity;
    const magneticThreshold = 50;
    
    targets.forEach(target => {
      const rect = target.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const distance = Math.sqrt(
        Math.pow(point.x - centerX, 2) + Math.pow(point.y - centerY, 2)
      );
      
      if (distance < magneticThreshold && distance < minDistance) {
        minDistance = distance;
        closestTarget = target;
      }
    });
    
    return closestTarget;
  }

  // 创建长按选择功能
  createLongPressSelection(elements, options = {}) {
    const selectedItems = new Set();
    let selectionMode = false;
    
    elements.forEach(element => {
      const gestureRecognizer = this.createGestureRecognizer(element);
      
      gestureRecognizer.on('longpress', () => {
        if (!selectionMode) {
          selectionMode = true;
          this.enterSelectionMode(elements);
        }
        
        this.toggleSelection(element, selectedItems);
      });
      
      gestureRecognizer.on('tap', () => {
        if (selectionMode) {
          this.toggleSelection(element, selectedItems);
        }
      });
    });
    
    return {
      getSelectedItems: () => Array.from(selectedItems),
      clearSelection: () => {
        selectedItems.clear();
        this.exitSelectionMode(elements);
        selectionMode = false;
      },
      isSelectionMode: () => selectionMode
    };
  }

  enterSelectionMode(elements) {
    elements.forEach(element => {
      element.classList.add('selecting');
    });
  }

  exitSelectionMode(elements) {
    elements.forEach(element => {
      element.classList.remove('selecting', 'selected');
    });
  }

  toggleSelection(element, selectedItems) {
    if (selectedItems.has(element)) {
      selectedItems.delete(element);
      element.classList.remove('selected');
    } else {
      selectedItems.add(element);
      element.classList.add('selected');
    }
  }

  // 销毁管理器
  destroy() {
    this.dragManager.destroy();
    this.swipeManager.destroy();
    
    // 清理波纹效果
    this.rippleEffects.forEach(ripple => {
      ripple.remove();
    });
    this.rippleEffects.clear();
  }
}

// 创建全局实例
const advancedInteractionManager = new AdvancedInteractionManager();

// 页面混入对象
const AdvancedInteractionMixin = {
  data: {
    interactionManager: null
  },

  onLoad() {
    this.data.interactionManager = advancedInteractionManager;
  },

  // 便捷方法
  createGestureRecognizer(selector, options) {
    const element = document.querySelector(selector);
    if (element) {
      return advancedInteractionManager.createGestureRecognizer(element, options);
    }
    return null;
  },

  makeDraggable(selector, data) {
    const element = document.querySelector(selector);
    if (element) {
      advancedInteractionManager.makeDraggable(element, data);
    }
  },

  makeSwipeable(selector, actions) {
    const element = document.querySelector(selector);
    if (element) {
      advancedInteractionManager.makeSwipeable(element, actions);
    }
  }
};

module.exports = {
  GestureRecognizer,
  DragManager,
  SwipeManager,
  AdvancedInteractionManager,
  advancedInteractionManager,
  AdvancedInteractionMixin
};