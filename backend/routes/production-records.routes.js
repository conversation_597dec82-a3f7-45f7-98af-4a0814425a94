const express = require("express");
const router = express.Router();
const { verifyToken } = require("../middleware/auth.middleware");
const productionController = require("../controllers/production.controller");

/**
 * 生产记录管理路由
 * 遵循RESTful API设计规范
 * 路径: /api/v1/production-records
 */

// 所有路由都需要验证令牌
router.use(verifyToken);

// ================================
// 基础CRUD操作
// ================================

// 获取生产记录列表 (V1版本 - 兼容旧版本)
// GET /api/v1/production-records?page=1&limit=20&sort=-createdAt&type=feeding&status=active
router.get("/", productionController.getRecords);

// 创建生产记录 (V1版本 - 兼容旧版本)
// POST /api/v1/production-records
router.post("/", productionController.createRecord);

// ================================
// V2版本API - 支持生长记录、称重记录、出栏记录
// ================================

// 获取V2生产记录列表
// GET /api/v1/production-records/v2?page=1&limit=20&type=growth
router.get("/v2", productionController.getRecordsV2);

// 创建V2生产记录
// POST /api/v1/production-records/v2
router.post("/v2", productionController.createRecordV2);

// 获取生产记录详情
// GET /api/v1/production-records/:id
router.get("/:id", productionController.getRecordById);

// 更新生产记录（完整更新）
// PUT /api/v1/production-records/:id
router.put("/:id", productionController.updateRecord);

// 部分更新生产记录
// PATCH /api/v1/production-records/:id
router.patch("/:id", productionController.patchRecord);

// 删除生产记录
// DELETE /api/v1/production-records/:id
router.delete("/:id", productionController.deleteRecord);

// ================================
// 批量操作 (暂时禁用 - 待实现)
// ================================

// 批量创建生产记录
// POST /api/v1/production-records/batch/create
router.post('/batch/create', require('../controllers/production-stats.controller').batchCreateRecords);

// 批量更新生产记录
// POST /api/v1/production-records/batch/update
// router.post('/batch/update', productionController.batchUpdateRecords);

// 批量删除生产记录
// POST /api/v1/production-records/batch/delete
// router.post('/batch/delete', productionController.batchDeleteRecords);

// ================================
// 统计和分析
// ================================

// 获取生产记录统计概览
// GET /api/v1/production-records/stats/overview?startDate=2024-01-01&endDate=2024-01-31
router.get("/stats/overview", productionController.getRecordStats);

// 获取生产趋势数据
// GET /api/v1/production-records/stats/trends?period=month&type=feeding
router.get('/stats/trends', require('../controllers/production-stats.controller').getProductionTrends);

// 获取生产效率分析
// GET /api/v1/production-records/stats/efficiency
router.get('/stats/efficiency', require('../controllers/production-stats.controller').getProductionEfficiency);

// 获取成本分析
// GET /api/v1/production-records/stats/costs
router.get('/stats/costs', require('../controllers/production-stats.controller').getCostAnalysis);

// ================================
// 数据导出
// ================================

// 导出生产记录为CSV
// GET /api/v1/production-records/export/csv?startDate=2024-01-01&endDate=2024-01-31
router.get("/export/csv", productionController.exportRecordsCSV);

// 导出生产记录为Excel
// GET /api/v1/production-records/export/excel
router.get("/export/excel", productionController.exportRecordsExcel);

// 导出生产报告PDF
// GET /api/v1/production-records/export/pdf
router.get("/export/pdf", productionController.exportProductionReportPDF);

// ================================
// 搜索和过滤
// ================================

// 搜索生产记录
// GET /api/v1/production-records/search?q=keyword&type=feeding&dateRange=week
router.get("/search", productionController.searchRecords);

// 高级过滤
// POST /api/v1/production-records/filter
router.post("/filter", productionController.filterRecords);

// ================================
// 记录类型管理
// ================================

// 获取生产记录类型列表
// GET /api/v1/production-records/types
router.get("/types", productionController.getRecordTypes);

// 获取记录模板
// GET /api/v1/production-records/templates?type=feeding
router.get("/templates", productionController.getRecordTemplates);

// ================================
// 审批流程
// ================================

// 提交记录审批
// POST /api/v1/production-records/:id/submit
router.post("/:id/submit", productionController.submitRecordForApproval);

// 审批记录
// POST /api/v1/production-records/:id/approve
router.post("/:id/approve", productionController.approveRecord);

// 拒绝记录
// POST /api/v1/production-records/:id/reject
router.post("/:id/reject", productionController.rejectRecord);

// 获取待审批记录列表
// GET /api/v1/production-records/pending-approval
router.get("/pending-approval", productionController.getPendingApprovalRecords);

// ================================
// 关联数据
// ================================

// 获取记录关联的物料使用情况
// GET /api/v1/production-records/:id/materials
router.get("/:id/materials", productionController.getRecordMaterials);

// 获取记录关联的成本明细
// GET /api/v1/production-records/:id/costs
router.get("/:id/costs", productionController.getRecordCosts);

// 获取记录关联的环境数据
// GET /api/v1/production-records/:id/environment
router.get("/:id/environment", productionController.getRecordEnvironment);

// ================================
// 历史和版本管理
// ================================

// 获取记录修改历史
// GET /api/v1/production-records/:id/history
router.get("/:id/history", productionController.getRecordHistory);

// 恢复记录到指定版本
// POST /api/v1/production-records/:id/restore/:version
router.post("/:id/restore/:version", productionController.restoreRecordVersion);

// ================================
// 数据验证和质量检查
// ================================

// 验证记录数据
// POST /api/v1/production-records/validate
router.post("/validate", productionController.validateRecordData);

// 数据质量检查
// GET /api/v1/production-records/quality-check
router.get("/quality-check", productionController.performQualityCheck);

// ================================
// 通知和提醒
// ================================

// 获取记录相关通知
// GET /api/v1/production-records/:id/notifications
router.get("/:id/notifications", productionController.getRecordNotifications);

// 设置记录提醒
// POST /api/v1/production-records/:id/reminders
router.post("/:id/reminders", productionController.setRecordReminders);

module.exports = router;
