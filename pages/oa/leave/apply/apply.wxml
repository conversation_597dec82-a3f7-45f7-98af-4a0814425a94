<!-- pages/oa/leave/apply/apply.wxml -->
<view class="apply-container">
  


  <!-- 请假信息 -->
  <view class="form-section">
    <view class="section-title">
      <text>请假信息</text>
    </view>
    
    <!-- 请假类型 -->
    <view class="form-item">
      <text class="form-label">请假类型 <text class="required">*</text></text>
      <picker 
        range="{{leaveTypes}}" 
        range-key="label" 
        bindchange="onLeaveTypeChange"
        class="form-picker">
        <view class="picker-content">
          <text class="picker-text" wx:if="{{formData.type}}">
            {{selectedTypeLabel}}
          </text>
          <text class="picker-placeholder" wx:else>请选择请假类型</text>
          <image class="picker-arrow" src="/images/icons/arrow_down.png" mode="aspectFit"></image>
        </view>
      </picker>
    </view>

    <!-- 请假原因 -->
    <view class="form-item">
      <text class="form-label">请假原因 <text class="required">*</text></text>
      <textarea 
        class="form-textarea"
        placeholder="请详细描述请假原因..."
        value="{{formData.reason}}"
        data-field="reason"
        bindinput="onInputChange"
        maxlength="200"
        show-confirm-bar="{{false}}"
        auto-height></textarea>
      <view class="char-count">{{formData.reason.length}}/200</view>
    </view>

    <!-- 请假时间 -->
    <view class="form-item">
      <text class="form-label">开始时间 <text class="required">*</text></text>
      <view class="datetime-row">
        <picker 
          mode="date" 
          value="{{formData.startDate}}" 
          data-field="startDate"
          bindchange="onDateChange"
          class="date-picker">
          <view class="picker-content">
            <text class="picker-text" wx:if="{{formData.startDate}}">{{formData.startDate}}</text>
            <text class="picker-placeholder" wx:else>选择日期</text>
            <image class="picker-icon" src="/images/icons/calendar.png" mode="aspectFit"></image>
          </view>
        </picker>
        <picker 
          mode="time" 
          value="{{formData.startTime}}" 
          data-field="startTime"
          bindchange="onTimeChange"
          class="time-picker">
          <view class="picker-content">
            <text class="picker-text">{{formData.startTime}}</text>
            <image class="picker-icon" src="/images/icons/time.png" mode="aspectFit"></image>
          </view>
        </picker>
      </view>
    </view>

    <view class="form-item">
      <text class="form-label">结束时间 <text class="required">*</text></text>
      <view class="datetime-row">
        <picker 
          mode="date" 
          value="{{formData.endDate}}" 
          data-field="endDate"
          bindchange="onDateChange"
          class="date-picker">
          <view class="picker-content">
            <text class="picker-text" wx:if="{{formData.endDate}}">{{formData.endDate}}</text>
            <text class="picker-placeholder" wx:else>选择日期</text>
            <image class="picker-icon" src="/images/icons/calendar.png" mode="aspectFit"></image>
          </view>
        </picker>
        <picker 
          mode="time" 
          value="{{formData.endTime}}" 
          data-field="endTime"
          bindchange="onTimeChange"
          class="time-picker">
          <view class="picker-content">
            <text class="picker-text">{{formData.endTime}}</text>
            <image class="picker-icon" src="/images/icons/time.png" mode="aspectFit"></image>
          </view>
        </picker>
      </view>
    </view>

    <!-- 请假天数 -->
    <view class="form-item" wx:if="{{formData.days > 0}}">
      <text class="form-label">请假天数</text>
      <view class="days-display">
        <text class="days-number">{{formData.days}}</text>
        <text class="days-unit">天</text>
      </view>
    </view>



    <!-- 附件上传 -->
    <view class="form-item">
      <text class="form-label">相关证明</text>
      <view class="attachment-section">
        <view class="attachment-list" wx:if="{{formData.attachment.length > 0}}">
          <block wx:for="{{formData.attachment}}" wx:key="index">
            <view class="attachment-item">
              <image 
                class="attachment-image" 
                src="{{item}}" 
                mode="aspectFill"
                bindtap="onPreviewAttachment"
                data-url="{{item}}"></image>
              <view class="delete-btn" 
                    bindtap="onDeleteAttachment" 
                    data-index="{{index}}">
                <text>×</text>
              </view>
            </view>
          </block>
        </view>
        <view class="upload-btn" bindtap="onChooseAttachment" wx:if="{{formData.attachment.length < 3}}">
          <image class="upload-icon" src="/images/icons/camera.png" mode="aspectFit"></image>
          <text class="upload-text">添加证明</text>
        </view>
        <view class="upload-tips">
          <text>可上传病假条、证明文件等，最多3张</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="reset-btn" bindtap="onReset" disabled="{{submitting}}">
      重置
    </button>
    <button class="submit-btn" bindtap="onSubmit" disabled="{{submitting}}" loading="{{submitting}}">
      {{submitting ? '提交中...' : '提交申请'}}
    </button>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>

</view>