/* 智慧养鹅小程序 - 统一图标系统 V1.0 */
/* 提供完整的图标解决方案：SVG图标 + 图标字体 + 图标组件 */

/* ==================== 图标基础系统 ==================== */

/* SVG图标基础类 */
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
  fill: currentColor; /* 继承文字颜色 */
  overflow: hidden;
  flex-shrink: 0; /* 防止在flex布局中被压缩 */
}

/* 图标尺寸系统 */
.icon-xs {
  width: 24rpx;
  height: 24rpx;
  font-size: 24rpx;
}

.icon-sm {
  width: 32rpx;
  height: 32rpx;
  font-size: 32rpx;
}

.icon-md {
  width: 40rpx;
  height: 40rpx;
  font-size: 40rpx;
}

.icon-lg {
  width: 48rpx;
  height: 48rpx;
  font-size: 48rpx;
}

.icon-xl {
  width: 56rpx;
  height: 56rpx;
  font-size: 56rpx;
}

.icon-2xl {
  width: 64rpx;
  height: 64rpx;
  font-size: 64rpx;
}

.icon-3xl {
  width: 80rpx;
  height: 80rpx;
  font-size: 80rpx;
}

/* ==================== 图标颜色系统 ==================== */

/* 主色系图标 */
.icon-primary {
  color: var(--primary);
}

.icon-secondary {
  color: var(--secondary);
}

.icon-accent {
  color: var(--accent);
}

/* 语义色系图标 */
.icon-success {
  color: var(--success);
}

.icon-warning {
  color: var(--warning);
}

.icon-error {
  color: var(--error);
}

.icon-info {
  color: var(--info);
}

/* 中性色系图标 */
.icon-text-primary {
  color: var(--text-primary);
}

.icon-text-secondary {
  color: var(--text-secondary);
}

.icon-text-tertiary {
  color: var(--text-tertiary);
}

.icon-white {
  color: #ffffff;
}

.icon-black {
  color: #000000;
}

/* ==================== 图标状态系统 ==================== */

/* 交互状态 */
.icon-interactive {
  transition: var(--transition-all);
  cursor: pointer;
}

.icon-interactive:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.icon-interactive:active {
  opacity: 0.6;
  transform: scale(0.95);
}

/* 禁用状态 */
.icon-disabled {
  opacity: 0.4;
  pointer-events: none;
}

/* 加载状态 */
.icon-loading {
  animation: icon-spin 1s linear infinite;
}

@keyframes icon-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 图标组合组件 ==================== */

/* 带标签的图标 */
.icon-with-label {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.icon-with-label.vertical {
  flex-direction: column;
  text-align: center;
}

.icon-with-label .icon-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  white-space: nowrap;
}

/* 图标按钮 */
.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 88rpx;
  min-height: 88rpx;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  background-color: transparent;
  border: none;
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.icon-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: var(--radius-full);
  background-color: currentColor;
  opacity: 0.1;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.icon-button:active::before {
  width: 100%;
  height: 100%;
}

.icon-button:active {
  transform: scale(0.95);
}

/* 图标按钮变体 */
.icon-button-filled {
  background-color: var(--primary);
  color: var(--text-inverse);
}

.icon-button-outlined {
  border: 2rpx solid var(--border-medium);
  color: var(--text-primary);
}

.icon-button-ghost {
  background-color: transparent;
  color: var(--text-secondary);
}

/* ==================== 导航图标系统 ==================== */

/* TabBar图标 */
.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: var(--space-xs);
  transition: all 0.2s ease;
}

.tabbar-icon.active {
  color: var(--primary);
  transform: scale(1.1);
}

.tabbar-icon.inactive {
  color: var(--text-tertiary);
}

/* 页面导航图标 */
.nav-icon {
  width: 40rpx;
  height: 40rpx;
  color: var(--text-secondary);
}

.nav-back-icon {
  width: 44rpx;
  height: 44rpx;
  color: var(--text-primary);
}

/* ==================== 业务功能图标 ==================== */

/* 状态指示图标 */
.status-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--space-xs);
}

.status-icon.healthy {
  color: var(--success);
}

.status-icon.warning {
  color: var(--warning);
}

.status-icon.error {
  color: var(--error);
}

.status-icon.processing {
  color: var(--info);
  animation: icon-pulse 2s infinite;
}

@keyframes icon-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 操作图标 */
.action-icon {
  width: 36rpx;
  height: 36rpx;
  color: var(--text-secondary);
  transition: var(--transition-all);
}

.action-icon:active {
  color: var(--primary);
  transform: scale(0.9);
}

/* 功能入口图标 */
.feature-icon {
  width: 64rpx;
  height: 64rpx;
  padding: var(--space-md);
  background: linear-gradient(135deg, var(--primary-subtle) 0%, var(--secondary-subtle) 100%);
  border-radius: var(--radius-xl);
  color: var(--primary);
}

/* ==================== 图标装饰效果 ==================== */

/* 图标背景装饰 */
.icon-bg-circle {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-full);
  padding: var(--space-sm);
}

.icon-bg-square {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
}

.icon-bg-primary {
  background-color: var(--primary);
  color: var(--text-inverse);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
}

/* 图标边框 */
.icon-bordered {
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
}

/* 图标阴影 */
.icon-shadow {
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
}

/* ==================== 图标动画效果 ==================== */

/* 呼吸效果 */
.icon-breathe {
  animation: icon-breathe 2s ease-in-out infinite;
}

@keyframes icon-breathe {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
}

/* 摇摆效果 */
.icon-wobble {
  animation: icon-wobble 1s ease-in-out;
}

@keyframes icon-wobble {
  0% { transform: rotate(0deg); }
  15% { transform: rotate(-5deg); }
  30% { transform: rotate(5deg); }
  45% { transform: rotate(-3deg); }
  60% { transform: rotate(3deg); }
  75% { transform: rotate(-1deg); }
  100% { transform: rotate(0deg); }
}

/* 跳动效果 */
.icon-bounce {
  animation: icon-bounce 0.6s ease;
}

@keyframes icon-bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-12rpx); }
  60% { transform: translateY(-6rpx); }
}

/* 旋转效果 */
.icon-rotate {
  animation: icon-rotate 0.5s ease;
}

@keyframes icon-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 响应式图标 ==================== */

/* 小屏设备 */
@media screen and (max-width: 750rpx) {
  .icon-responsive {
    width: 36rpx;
    height: 36rpx;
  }
  
  .feature-icon-responsive {
    width: 56rpx;
    height: 56rpx;
  }
}

/* 大屏设备 */
@media screen and (min-width: 1024rpx) {
  .icon-responsive {
    width: 48rpx;
    height: 48rpx;
  }
  
  .feature-icon-responsive {
    width: 72rpx;
    height: 72rpx;
  }
}

/* ==================== 深色模式适配 ==================== */

.dark-theme .icon-auto {
  filter: brightness(1.2);
}

.dark-theme .icon-invert {
  filter: invert(1);
}

/* ==================== 无障碍支持 ==================== */

/* 屏幕阅读器 */
.icon[aria-hidden="true"] {
  pointer-events: none;
}

.icon[role="img"] {
  display: inline-block;
}

/* 高对比度模式 - 小程序通过类名控制 */
/* @media (prefers-contrast: high) { */ /* 小程序不支持 */
.high-contrast .icon {
  filter: contrast(1.5);
}
/* } */

/* ==================== 图标网格系统 ==================== */

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100rpx, 1fr));
  gap: var(--space-lg);
  padding: var(--space-lg);
}

.icon-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md);
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
}

.icon-grid-item:active {
  background-color: var(--bg-secondary);
  transform: scale(0.95);
}

.icon-grid-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  text-align: center;
}

/* ==================== 特殊用途图标 ==================== */

/* 空状态图标 */
.empty-icon {
  width: 120rpx;
  height: 120rpx;
  color: var(--text-tertiary);
  margin-bottom: var(--space-lg);
}

/* 加载图标 */
.loading-icon {
  width: 40rpx;
  height: 40rpx;
  color: var(--primary);
  animation: icon-spin 1s linear infinite;
}

/* 成功图标 */
.success-icon {
  width: 80rpx;
  height: 80rpx;
  color: var(--success);
  animation: icon-bounce 0.6s ease;
}

/* 错误图标 */
.error-icon {
  width: 80rpx;
  height: 80rpx;
  color: var(--error);
  animation: icon-wobble 1s ease;
}

/* ==================== 工具类 ==================== */

/* 图标对齐 */
.icon-align-top {
  vertical-align: top;
}

.icon-align-middle {
  vertical-align: middle;
}

.icon-align-bottom {
  vertical-align: bottom;
}

/* 图标间距 */
.icon-mr-xs {
  margin-right: var(--space-xs);
}

.icon-mr-sm {
  margin-right: var(--space-sm);
}

.icon-ml-xs {
  margin-left: var(--space-xs);
}

.icon-ml-sm {
  margin-left: var(--space-sm);
}

/* 图标显示/隐藏 */
.icon-show {
  display: inline-block;
}

.icon-hide {
  display: none;
}