// backend/controllers/oa.controller.js

/**
 * OA系统控制器
 * 处理OA模块的通用接口
 */

const db = require("../config/database");
const {
  generateResponse,
  generateErrorResponse,
} = require("../utils/response-helper");

/**
 * 获取OA系统统计数据
 */
exports.getOAStats = async (req, res) => {
  try {
    const userId = req.user.id;

    // 并行查询各种统计数据
    const [pendingApprovals, todayTasks, thisMonthExpense, unreadNotices] =
      await Promise.all([
        // 待审批数量
        db.query(
          `
        SELECT COUNT(*) as count 
        FROM oa_approvals 
        WHERE status = 'pending' 
        AND (approver_id = ? OR creator_id = ?)
      `,
          [userId, userId],
        ),

        // 今日任务数
        db.query(
          `
        SELECT COUNT(*) as count 
        FROM oa_tasks 
        WHERE DATE(created_at) = CURDATE() 
        AND (assignee_id = ? OR creator_id = ?)
      `,
          [userId, userId],
        ),

        // 本月支出总额
        db.query(`
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM oa_expenses 
        WHERE YEAR(expense_date) = YEAR(NOW()) 
        AND MONTH(expense_date) = MONTH(NOW())
        AND status = 'approved'
      `),

        // 未读通知数
        db.query(
          `
        SELECT COUNT(*) as count 
        FROM oa_notifications 
        WHERE recipient_id = ? 
        AND is_read = false
      `,
          [userId],
        ),
      ]);

    const stats = {
      pendingApprovals: pendingApprovals[0][0].count,
      todayTasks: todayTasks[0][0].count,
      thisMonthExpense: Math.round(thisMonthExpense[0][0].total),
      unreadNotices: unreadNotices[0][0].count,
    };

    res.json(generateResponse(stats));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取OA统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取统计数据失败"));
  }
};

/**
 * 获取最近活动记录
 */
exports.getRecentActivities = async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 10;

    // 查询最近的活动记录
    const [activities] = await db.query(
      `
      SELECT 
        a.id,
        a.type,
        a.title,
        a.description,
        a.status,
        a.created_at,
        CASE 
          WHEN a.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) 
          THEN CONCAT(TIMESTAMPDIFF(MINUTE, a.created_at, NOW()), '分钟前')
          WHEN a.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) 
          THEN CONCAT(TIMESTAMPDIFF(HOUR, a.created_at, NOW()), '小时前')
          ELSE DATE_FORMAT(a.created_at, '%m-%d %H:%i')
        END as time
      FROM oa_activities a
      WHERE a.user_id = ?
      ORDER BY a.created_at DESC
      LIMIT ?
    `,
      [userId, limit],
    );

    res.json(generateResponse(activities));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取最近活动失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取活动记录失败"));
  }
};

/**
 * 获取财务概览数据
 */
exports.getFinanceOverview = async (req, res) => {
  try {
    const { timeRange = "month" } = req.query;
    let dateCondition = "";

    // 根据时间范围构建查询条件
    switch (timeRange) {
      case "week":
        dateCondition = "AND YEARWEEK(expense_date) = YEARWEEK(NOW())";
        break;
      case "month":
        dateCondition =
          "AND YEAR(expense_date) = YEAR(NOW()) AND MONTH(expense_date) = MONTH(NOW())";
        break;
      case "quarter":
        dateCondition =
          "AND YEAR(expense_date) = YEAR(NOW()) AND QUARTER(expense_date) = QUARTER(NOW())";
        break;
      case "year":
        dateCondition = "AND YEAR(expense_date) = YEAR(NOW())";
        break;
    }

    // 并行查询财务数据
    const [
      revenueData,
      expenseData,
      pendingPayments,
      lastPeriodRevenue,
      lastPeriodExpense,
    ] = await Promise.all([
      // 总收入
      db.query(`
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM oa_revenues 
        WHERE status = 'confirmed' ${dateCondition}
      `),

      // 总支出
      db.query(`
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM oa_expenses 
        WHERE status = 'approved' ${dateCondition}
      `),

      // 待付款
      db.query(`
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM oa_expenses 
        WHERE status = 'approved' AND payment_status = 'pending'
      `),

      // 上期收入（用于计算增长率）
      db.query(`
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM oa_revenues 
        WHERE status = 'confirmed' 
        ${timeRange === "month" ? "AND YEAR(expense_date) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND MONTH(expense_date) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))" : ""}
      `),

      // 上期支出
      db.query(`
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM oa_expenses 
        WHERE status = 'approved' 
        ${timeRange === "month" ? "AND YEAR(expense_date) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND MONTH(expense_date) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))" : ""}
      `),
    ]);

    const totalRevenue = parseFloat(revenueData[0][0].total);
    const totalExpense = parseFloat(expenseData[0][0].total);
    const netProfit = totalRevenue - totalExpense;
    const pendingAmount = parseFloat(pendingPayments[0][0].total);

    const lastRevenue = parseFloat(lastPeriodRevenue[0][0].total);
    const lastExpense = parseFloat(lastPeriodExpense[0][0].total);

    // 计算增长率
    const revenueGrowth =
      lastRevenue > 0
        ? (((totalRevenue - lastRevenue) / lastRevenue) * 100).toFixed(1)
        : 0;
    const expenseGrowth =
      lastExpense > 0
        ? (((totalExpense - lastExpense) / lastExpense) * 100).toFixed(1)
        : 0;
    const profitGrowth =
      lastRevenue > 0
        ? (
            ((netProfit - (lastRevenue - lastExpense)) /
              (lastRevenue - lastExpense)) *
            100
          ).toFixed(1)
        : 0;

    const overview = {
      totalRevenue,
      totalExpense,
      netProfit,
      pendingPayments: pendingAmount,
      revenueGrowth: parseFloat(revenueGrowth),
      expenseGrowth: parseFloat(expenseGrowth),
      profitGrowth: parseFloat(profitGrowth),
      cashFlow: totalRevenue - totalExpense + pendingAmount,
    };

    res.json(generateResponse(overview));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取财务概览失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取财务概览失败"));
  }
};

/**
 * 获取收支趋势数据
 */
exports.getFinanceTrend = async (req, res) => {
  try {
    const { timeRange = "month" } = req.query;

    // 获取最近12个月的收支数据
    const [trendData] = await db.query(`
      SELECT 
        DATE_FORMAT(expense_date, '%Y-%m') as month,
        MONTH(expense_date) as month_num,
        SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as revenue,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expense
      FROM (
        SELECT expense_date, 'revenue' as type, amount FROM oa_revenues WHERE status = 'confirmed'
        UNION ALL
        SELECT expense_date, 'expense' as type, amount FROM oa_expenses WHERE status = 'approved'
      ) combined
      WHERE expense_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(expense_date, '%Y-%m'), MONTH(expense_date)
      ORDER BY month
    `);

    // 获取支出分类数据
    const [categoryData] = await db.query(`
      SELECT 
        category,
        SUM(amount) as amount,
        COUNT(*) as count
      FROM oa_expenses 
      WHERE status = 'approved' 
      AND YEAR(expense_date) = YEAR(NOW()) 
      AND MONTH(expense_date) = MONTH(NOW())
      GROUP BY category
      ORDER BY amount DESC
    `);

    // 计算总支出用于百分比计算
    const totalExpense = categoryData.reduce(
      (sum, item) => sum + parseFloat(item.amount),
      0,
    );

    // 处理分类数据
    const categories = categoryData.map((item, index) => ({
      name: item.category || "其他",
      amount: parseFloat(item.amount),
      percentage:
        totalExpense > 0
          ? Math.round((parseFloat(item.amount) / totalExpense) * 100)
          : 0,
      color: ["#FF6B35", "#0066CC", "#00A86B", "#9B59B6", "#FFA500", "#E74C3C"][
        index % 6
      ],
    }));

    // 构建月份标签和数据数组
    const months = [];
    const revenue = [];
    const expense = [];

    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey =
        date.getFullYear() + "-" + String(date.getMonth() + 1).padStart(2, "0");
      months.push(date.getMonth() + 1 + "月");

      const monthData = trendData.find((item) => item.month === monthKey);
      revenue.push(monthData ? parseFloat(monthData.revenue) : 0);
      expense.push(monthData ? parseFloat(monthData.expense) : 0);
    }

    const result = {
      trendData: {
        months,
        revenue,
        expense,
      },
      categories,
    };

    res.json(generateResponse(result));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取财务趋势失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取财务趋势失败"));
  }
};

/**
 * 获取最近交易记录
 */
exports.getRecentTransactions = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    const [transactions] = await db.query(
      `
      SELECT 
        id,
        type,
        description,
        amount,
        category,
        expense_date as date,
        status,
        created_at
      FROM (
        SELECT 
          CONCAT('revenue_', id) as id,
          'income' as type,
          description,
          amount,
          category,
          expense_date,
          status,
          created_at
        FROM oa_revenues
        WHERE status IN ('confirmed', 'pending')
        
        UNION ALL
        
        SELECT 
          CONCAT('expense_', id) as id,
          'expense' as type,
          description,
          amount,
          category,
          expense_date,
          status,
          created_at
        FROM oa_expenses
        WHERE status IN ('approved', 'pending')
      ) combined
      ORDER BY created_at DESC
      LIMIT ?
    `,
      [limit],
    );

    // 格式化数据
    const formattedTransactions = transactions.map((transaction) => ({
      id: transaction.id,
      type: transaction.type,
      description: transaction.description,
      amount: parseFloat(transaction.amount),
      category: transaction.category,
      date: transaction.date.toISOString().split("T")[0],
      status:
        transaction.status === "confirmed" || transaction.status === "approved"
          ? "completed"
          : "pending",
    }));

    res.json(generateResponse(formattedTransactions));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取交易记录失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取交易记录失败"));
  }
};

/**
 * 获取财务报表数据
 */
exports.getFinanceReports = async (req, res) => {
  try {
    const { type = "income_expense", startDate, endDate } = req.query;
    const userId = req.user.id;

    let reportData = [];
    let summary = {
      totalIncome: 0,
      totalExpense: 0,
      netProfit: 0,
      recordCount: 0,
    };

    // 根据报表类型生成不同数据
    switch (type) {
      case "income_expense":
        reportData = await getIncomeExpenseReport(startDate, endDate, userId);
        break;
      case "monthly_summary":
        reportData = await getMonthlySummaryReport(startDate, endDate, userId);
        break;
      case "quarterly_report":
        reportData = await getQuarterlyReport(startDate, endDate, userId);
        break;
      case "annual_report":
        reportData = await getAnnualReport(startDate, endDate, userId);
        break;
      case "category_analysis":
        reportData = await getCategoryAnalysisReport(
          startDate,
          endDate,
          userId,
        );
        break;
      case "trend_analysis":
        reportData = await getTrendAnalysisReport(startDate, endDate, userId);
        break;
      default:
        reportData = await getIncomeExpenseReport(startDate, endDate, userId);
    }

    // 计算总计数据
    if (type === "income_expense") {
      const totals = await calculateFinanceTotals(startDate, endDate, userId);
      summary = totals;
    }

    res.json(
      generateResponse({
        reportData,
        summary,
        type,
        dateRange: { startDate, endDate },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取财务报表数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取财务报表数据失败"));
  }
};

/**
 * 导出财务报表
 */
exports.exportFinanceReport = async (req, res) => {
  try {
    const { type, format, startDate, endDate, applicantInfo } = req.body;
    const userId = req.user.id;

    // 记录导出操作到活动日志
    await db.query(
      "INSERT INTO oa_activities (user_id, type, title, description, created_at) VALUES (?, ?, ?, ?, ?)",
      [
        userId,
        "export",
        "导出财务报表",
        `导出${type}报表，格式：${format}，时间范围：${startDate} 至 ${endDate}`,
        new Date(),
      ],
    );

    res.json(
      generateResponse({
        message: "导出成功",
        exportTime: new Date().toISOString(),
        applicant: applicantInfo.name,
        department: applicantInfo.department,
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("导出财务报表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("导出财务报表失败"));
  }
};

// 辅助函数

/**
 * 获取收支明细报表
 */
async function getIncomeExpenseReport(startDate, endDate, userId) {
  const revenueQuery = `
    SELECT 
      id,
      amount,
      category,
      description,
      revenue_date as date,
      'income' as type
    FROM oa_revenues 
    WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
    ORDER BY revenue_date DESC
  `;

  const expenseQuery = `
    SELECT 
      id,
      amount,
      category,
      description,
      expense_date as date,
      'expense' as type
    FROM oa_expenses 
    WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    ORDER BY expense_date DESC
  `;

  const [revenues] = await db.query(revenueQuery, [userId, startDate, endDate]);
  const [expenses] = await db.query(expenseQuery, [userId, startDate, endDate]);

  // 合并并按日期排序
  const combined = [...revenues, ...expenses].sort(
    (a, b) => new Date(b.date) - new Date(a.date),
  );

  return combined;
}

/**
 * 获取月度汇总报表
 */
async function getMonthlySummaryReport(startDate, endDate, userId) {
  const query = `
    SELECT 
      DATE_FORMAT(date, '%Y-%m') as month,
      SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as totalIncome,
      SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as totalExpense,
      (SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) - 
       SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as netProfit
    FROM (
      SELECT amount, revenue_date as date, 'revenue' as type FROM oa_revenues WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
      UNION ALL
      SELECT amount, expense_date as date, 'expense' as type FROM oa_expenses WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    ) combined
    GROUP BY DATE_FORMAT(date, '%Y-%m')
    ORDER BY month DESC
  `;

  const [results] = await db.query(query, [
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
  ]);
  return results;
}

/**
 * 获取分类分析报表
 */
async function getCategoryAnalysisReport(startDate, endDate, userId) {
  const query = `
    SELECT 
      category,
      SUM(amount) as amount,
      COUNT(*) as count,
      ROUND((SUM(amount) / (
        SELECT SUM(amount) FROM (
          SELECT amount FROM oa_revenues WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
          UNION ALL
          SELECT amount FROM oa_expenses WHERE user_id = ? AND expense_date BETWEEN ? AND ?
        ) total_transactions
      )) * 100, 2) as percentage
    FROM (
      SELECT category, amount FROM oa_revenues WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
      UNION ALL
      SELECT category, amount FROM oa_expenses WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    ) transactions
    GROUP BY category
    ORDER BY amount DESC
  `;

  const [results] = await db.query(query, [
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
  ]);

  return results;
}

/**
 * 获取季度报表
 */
async function getQuarterlyReport(startDate, endDate, userId) {
  const query = `
    SELECT 
      CONCAT(YEAR(date), '-Q', QUARTER(date)) as quarter,
      SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as totalIncome,
      SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as totalExpense,
      (SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) - 
       SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as netProfit
    FROM (
      SELECT amount, revenue_date as date, 'revenue' as type FROM oa_revenues WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
      UNION ALL
      SELECT amount, expense_date as date, 'expense' as type FROM oa_expenses WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    ) combined
    GROUP BY YEAR(date), QUARTER(date)
    ORDER BY quarter DESC
  `;

  const [results] = await db.query(query, [
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
  ]);
  return results;
}

/**
 * 获取年度报表
 */
async function getAnnualReport(startDate, endDate, userId) {
  const query = `
    SELECT 
      YEAR(date) as year,
      SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as totalIncome,
      SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as totalExpense,
      (SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) - 
       SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as netProfit
    FROM (
      SELECT amount, revenue_date as date, 'revenue' as type FROM oa_revenues WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
      UNION ALL
      SELECT amount, expense_date as date, 'expense' as type FROM oa_expenses WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    ) combined
    GROUP BY YEAR(date)
    ORDER BY year DESC
  `;

  const [results] = await db.query(query, [
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
  ]);
  return results;
}

/**
 * 获取趋势分析报表
 */
async function getTrendAnalysisReport(startDate, endDate, userId) {
  const query = `
    SELECT 
      DATE_FORMAT(date, '%Y-%m') as month,
      SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as income,
      SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expense,
      (SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) - 
       SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as profit
    FROM (
      SELECT amount, revenue_date as date, 'revenue' as type FROM oa_revenues WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
      UNION ALL
      SELECT amount, expense_date as date, 'expense' as type FROM oa_expenses WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    ) combined
    GROUP BY DATE_FORMAT(date, '%Y-%m')
    ORDER BY month
  `;

  const [results] = await db.query(query, [
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
  ]);
  return results;
}

/**
 * 计算财务总计数据
 */
async function calculateFinanceTotals(startDate, endDate, userId) {
  const query = `
    SELECT 
      SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as totalIncome,
      SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as totalExpense,
      (SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) - 
       SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as netProfit,
      COUNT(*) as recordCount
    FROM (
      SELECT amount, 'revenue' as type FROM oa_revenues WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
      UNION ALL
      SELECT amount, 'expense' as type FROM oa_expenses WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    ) combined
  `;

  const [results] = await db.query(query, [
    userId,
    startDate,
    endDate,
    userId,
    startDate,
    endDate,
  ]);
  return (
    results[0] || {
      totalIncome: 0,
      totalExpense: 0,
      netProfit: 0,
      recordCount: 0,
    }
  );
}

// ==============================
// 采购管理相关API
// ==============================

/**
 * 创建采购申请
 */
exports.createPurchaseRequest = async (req, res) => {
  const db = require("../config/database");

  try {
    const {
      title,
      description,
      category,
      urgencyLevel,
      expectedDate,
      businessPurpose,
      items,
      applicantInfo,
      totalAmount,
      status = "draft",
    } = req.body;

    const userId = req.user.id;

    // 生成申请单号
    const requestNumber = await generatePurchaseRequestNumber();

    // 开始事务
    await db.beginTransaction();

    try {
      // 插入采购申请主记录
      const [purchaseResult] = await db.query(
        `INSERT INTO oa_purchase_requests (
          request_number, title, description, category, urgency_level, 
          expected_date, business_purpose, total_amount, status,
          applicant_id, applicant_name, applicant_employee_id, 
          department, position, phone, manager_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          requestNumber,
          title,
          description,
          category,
          urgencyLevel,
          expectedDate,
          businessPurpose,
          totalAmount,
          status,
          userId,
          applicantInfo.name,
          applicantInfo.employeeId,
          applicantInfo.department,
          applicantInfo.position,
          applicantInfo.phone,
          applicantInfo.managerId,
        ],
      );

      const requestId = purchaseResult.insertId;

      // 插入采购物品清单
      for (const item of items) {
        if (item.itemName && item.quantity && item.unitPrice) {
          await db.query(
            `INSERT INTO oa_purchase_items (
              request_id, item_name, specification, quantity, unit, 
              unit_price, total_price, category, brand, 
              supplier_suggestion, remarks
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              requestId,
              item.itemName,
              item.specification,
              item.quantity,
              item.unit,
              item.unitPrice,
              item.totalPrice,
              item.category,
              item.brand,
              item.supplierSuggestion,
              item.remarks,
            ],
          );
        }
      }

      // 如果是提交状态，创建审批流程记录
      if (status === "submitted") {
        await db.query(
          `INSERT INTO oa_purchase_approvals (
            request_id, approver_id, approver_name, step, action, action_text, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            requestId,
            userId,
            applicantInfo.name,
            1,
            "submit",
            "提交申请",
            "pending",
          ],
        );

        // 记录活动日志
        await db.query(
          "INSERT INTO oa_activities (user_id, type, title, description, created_at) VALUES (?, ?, ?, ?, ?)",
          [
            userId,
            "purchase",
            "提交采购申请",
            `提交采购申请：${title}`,
            new Date(),
          ],
        );
      }

      await db.commit();

      res.json(
        generateResponse({
          id: requestId,
          requestNumber,
          message:
            status === "submitted" ? "采购申请提交成功" : "采购申请保存成功",
        }),
      );
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("创建采购申请失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("创建采购申请失败"));
  }
};

/**
 * 获取采购申请列表
 */
exports.getPurchaseRequestList = async (req, res) => {
  const db = require("../config/database");

  try {
    const {
      page = 1,
      limit = 20,
      type = "all",
      status = "all",
      category = "all",
      timeRange = "all",
      keyword = "",
    } = req.query;

    const userId = req.user.id;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];

    // 根据类型筛选
    if (type === "my") {
      whereConditions.push("r.applicant_id = ?");
      queryParams.push(userId);
    } else if (type === "pending") {
      whereConditions.push("r.status = ?");
      queryParams.push("submitted");
    } else if (type === "approved") {
      whereConditions.push("r.status = ?");
      queryParams.push("approved");
    } else if (type === "rejected") {
      whereConditions.push("r.status = ?");
      queryParams.push("rejected");
    }

    // 状态筛选
    if (status !== "all") {
      whereConditions.push("r.status = ?");
      queryParams.push(status);
    }

    // 类别筛选
    if (category !== "all") {
      whereConditions.push("r.category = ?");
      queryParams.push(category);
    }

    // 时间范围筛选
    if (timeRange !== "all") {
      const { startDate, endDate } = getDateRangeByType(timeRange);
      whereConditions.push("r.created_at BETWEEN ? AND ?");
      queryParams.push(startDate, endDate);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push(
        "(r.title LIKE ? OR r.description LIKE ? OR r.request_number LIKE ?)",
      );
      queryParams.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    const whereClause =
      whereConditions.length > 0
        ? "WHERE " + whereConditions.join(" AND ")
        : "";

    // 查询主列表
    const listQuery = `
      SELECT 
        r.*,
        GROUP_CONCAT(DISTINCT i.item_name ORDER BY i.id SEPARATOR ', ') as itemNames,
        COUNT(DISTINCT i.id) as itemCount
      FROM oa_purchase_requests r
      LEFT JOIN oa_purchase_items i ON r.id = i.request_id
      ${whereClause}
      GROUP BY r.id
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(parseInt(limit), parseInt(offset));

    const [purchaseList] = await db.query(listQuery, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(DISTINCT r.id) as total
      FROM oa_purchase_requests r
      LEFT JOIN oa_purchase_items i ON r.id = i.request_id
      ${whereClause}
    `;

    const [countResult] = await db.query(countQuery, queryParams.slice(0, -2));
    const total = countResult[0].total;

    // 查询状态统计
    const statusQuery = `
      SELECT 
        status,
        COUNT(*) as count,
        SUM(total_amount) as amount
      FROM oa_purchase_requests r
      ${whereClause.replace("r.status = ?", "1=1")}
      GROUP BY status
    `;

    const statusParams = queryParams.slice(0, -2).filter((_, index) => {
      return whereConditions[index] !== "r.status = ?";
    });

    const [statusCounts] = await db.query(statusQuery, statusParams);

    // 格式化返回数据
    const formattedList = purchaseList.map((item) => ({
      ...item,
      statusText: getStatusText(item.status),
      statusColor: getStatusColor(item.status),
      urgencyLabel: getUrgencyLabel(item.urgency_level),
      urgencyColor: getUrgencyColor(item.urgency_level),
      categoryLabel: getCategoryLabel(item.category),
      canEdit:
        item.applicant_id === userId &&
        (item.status === "draft" || item.status === "rejected"),
      canCancel:
        item.applicant_id === userId &&
        (item.status === "submitted" || item.status === "draft"),
      canApprove:
        req.user.permissions?.includes("purchase_approve") &&
        item.status === "submitted",
      items: item.itemNames
        ? item.itemNames.split(", ").map((name) => ({ itemName: name }))
        : [],
    }));

    res.json(
      generateResponse({
        list: formattedList,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          hasMore: offset + formattedList.length < total,
        },
        statusCounts: statusCounts.reduce((acc, item) => {
          acc[item.status] = item.count;
          return acc;
        }, {}),
        statistics: {
          total,
          pending:
            statusCounts.find((s) => s.status === "submitted")?.count || 0,
          approved:
            statusCounts.find((s) => s.status === "approved")?.count || 0,
          rejected:
            statusCounts.find((s) => s.status === "rejected")?.count || 0,
          totalAmount: statusCounts.reduce(
            (sum, s) => sum + (s.amount || 0),
            0,
          ),
        },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取采购申请列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取采购申请列表失败"));
  }
};

/**
 * 获取采购申请详情
 */
exports.getPurchaseRequestDetail = async (req, res) => {
  const db = require("../config/database");

  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 查询采购申请详情
    const [purchaseResults] = await db.query(
      "SELECT * FROM oa_purchase_requests WHERE id = ?",
      [id],
    );

    if (purchaseResults.length === 0) {
      return res.status(404).json(generateErrorResponse("采购申请不存在"));
    }

    const purchaseDetail = purchaseResults[0];

    // 查询物品清单
    const [items] = await db.query(
      "SELECT * FROM oa_purchase_items WHERE request_id = ? ORDER BY id",
      [id],
    );

    // 查询审批流程
    const [approvalFlow] = await db.query(
      "SELECT * FROM oa_purchase_approvals WHERE request_id = ? ORDER BY step, created_at",
      [id],
    );

    // 查询附件
    const [attachments] = await db.query(
      "SELECT * FROM oa_purchase_attachments WHERE request_id = ? ORDER BY upload_time DESC",
      [id],
    );

    // 查询评论
    const [comments] = await db.query(
      "SELECT * FROM oa_purchase_comments WHERE request_id = ? ORDER BY created_at DESC",
      [id],
    );

    // 格式化返回数据
    const formattedDetail = {
      ...purchaseDetail,
      statusText: getStatusText(purchaseDetail.status),
      statusColor: getStatusColor(purchaseDetail.status),
      urgencyLabel: getUrgencyLabel(purchaseDetail.urgency_level),
      urgencyColor: getUrgencyColor(purchaseDetail.urgency_level),
      categoryLabel: getCategoryLabel(purchaseDetail.category),
      items,
      approvalFlow,
      attachments,
      comments,
    };

    res.json(generateResponse(formattedDetail));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取采购申请详情失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取采购申请详情失败"));
  }
};

/**
 * 更新采购申请
 */
exports.updatePurchaseRequest = async (req, res) => {
  const db = require("../config/database");

  try {
    const { id } = req.params;
    const userId = req.user.id;
    const {
      title,
      description,
      category,
      urgencyLevel,
      expectedDate,
      businessPurpose,
      items,
      totalAmount,
    } = req.body;

    // 检查权限
    const [existingResults] = await db.query(
      "SELECT * FROM oa_purchase_requests WHERE id = ? AND applicant_id = ?",
      [id, userId],
    );

    if (existingResults.length === 0) {
      return res
        .status(404)
        .json(generateErrorResponse("采购申请不存在或无权限编辑"));
    }

    const existingRequest = existingResults[0];

    // 只有草稿和被拒绝的申请可以编辑
    if (!["draft", "rejected"].includes(existingRequest.status)) {
      return res.status(400).json(generateErrorResponse("当前状态不允许编辑"));
    }

    // 开始事务
    await db.beginTransaction();

    try {
      // 更新主记录
      await db.query(
        `UPDATE oa_purchase_requests SET 
          title = ?, description = ?, category = ?, urgency_level = ?,
          expected_date = ?, business_purpose = ?, total_amount = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?`,
        [
          title,
          description,
          category,
          urgencyLevel,
          expectedDate,
          businessPurpose,
          totalAmount,
          id,
        ],
      );

      // 删除原有物品清单
      await db.query("DELETE FROM oa_purchase_items WHERE request_id = ?", [
        id,
      ]);

      // 插入新的物品清单
      for (const item of items) {
        if (item.itemName && item.quantity && item.unitPrice) {
          await db.query(
            `INSERT INTO oa_purchase_items (
              request_id, item_name, specification, quantity, unit, 
              unit_price, total_price, category, brand, 
              supplier_suggestion, remarks
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              id,
              item.itemName,
              item.specification,
              item.quantity,
              item.unit,
              item.unitPrice,
              item.totalPrice,
              item.category,
              item.brand,
              item.supplierSuggestion,
              item.remarks,
            ],
          );
        }
      }

      await db.commit();

      res.json(generateResponse({ message: "采购申请更新成功" }));
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新采购申请失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("更新采购申请失败"));
  }
};

/**
 * 取消采购申请
 */
exports.cancelPurchaseRequest = async (req, res) => {
  const db = require("../config/database");

  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 检查权限
    const [existingResults] = await db.query(
      "SELECT * FROM oa_purchase_requests WHERE id = ? AND applicant_id = ?",
      [id, userId],
    );

    if (existingResults.length === 0) {
      return res
        .status(404)
        .json(generateErrorResponse("采购申请不存在或无权限操作"));
    }

    const existingRequest = existingResults[0];

    // 只有草稿和已提交的申请可以取消
    if (!["draft", "submitted"].includes(existingRequest.status)) {
      return res.status(400).json(generateErrorResponse("当前状态不允许取消"));
    }

    // 更新状态为已取消
    await db.query(
      "UPDATE oa_purchase_requests SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
      ["cancelled", id],
    );

    // 记录审批流程
    await db.query(
      `INSERT INTO oa_purchase_approvals (
        request_id, approver_id, approver_name, step, action, action_text, 
        comment, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        userId,
        req.user.name,
        0,
        "cancel",
        "取消申请",
        "申请人取消了采购申请",
        "cancelled",
        new Date(),
      ],
    );

    // 记录活动日志
    await db.query(
      "INSERT INTO oa_activities (user_id, type, title, description, created_at) VALUES (?, ?, ?, ?, ?)",
      [
        userId,
        "purchase",
        "取消采购申请",
        `取消采购申请：${existingRequest.title}`,
        new Date(),
      ],
    );

    res.json(generateResponse({ message: "采购申请取消成功" }));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("取消采购申请失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("取消采购申请失败"));
  }
};

// ==============================
// 辅助函数
// ==============================

/**
 * 生成采购申请单号
 */
async function generatePurchaseRequestNumber() {
  const db = require("../config/database");
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");

  const prefix = `PR${year}${month}${day}`;

  // 查询今天已有的最大序号
  const [results] = await db.query(
    "SELECT request_number FROM oa_purchase_requests WHERE request_number LIKE ? ORDER BY request_number DESC LIMIT 1",
    [`${prefix}%`],
  );

  let sequence = 1;
  if (results.length > 0) {
    const lastNumber = results[0].request_number;
    const lastSequence = parseInt(lastNumber.slice(-4));
    sequence = lastSequence + 1;
  }

  return `${prefix}${String(sequence).padStart(4, "0")}`;
}

/**
 * 根据时间范围类型获取日期范围
 */
function getDateRangeByType(timeRange) {
  const now = new Date();
  let startDate, endDate;

  switch (timeRange) {
    case "today":
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    case "week":
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startDate = new Date(
        startOfWeek.getFullYear(),
        startOfWeek.getMonth(),
        startOfWeek.getDate(),
      );
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 7);
      break;
    case "month":
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      break;
    case "quarter":
      const quarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), quarter * 3, 1);
      endDate = new Date(now.getFullYear(), quarter * 3 + 3, 1);
      break;
    default:
      startDate = new Date(2020, 0, 1);
      endDate = new Date();
  }

  return {
    startDate: startDate.toISOString().split("T")[0],
    endDate: endDate.toISOString().split("T")[0],
  };
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
  const statusMap = {
    draft: "草稿",
    submitted: "待审批",
    approved: "已批准",
    rejected: "已拒绝",
    cancelled: "已取消",
  };
  return statusMap[status] || status;
}

/**
 * 获取状态颜色
 */
function getStatusColor(status) {
  const colorMap = {
    draft: "#999999",
    submitted: "#FF9500",
    approved: "#00A86B",
    rejected: "#FF3B30",
    cancelled: "#8E8E93",
  };
  return colorMap[status] || "#999999";
}

/**
 * 获取紧急程度标签
 */
function getUrgencyLabel(urgency) {
  const labelMap = {
    low: "一般",
    normal: "正常",
    high: "紧急",
    urgent: "特急",
  };
  return labelMap[urgency] || urgency;
}

/**
 * 获取紧急程度颜色
 */
function getUrgencyColor(urgency) {
  const colorMap = {
    low: "#00A86B",
    normal: "#0066CC",
    high: "#FF9500",
    urgent: "#FF3B30",
  };
  return colorMap[urgency] || "#0066CC";
}

/**
 * 获取类别标签
 */
function getCategoryLabel(category) {
  const labelMap = {
    feed: "饲料用品",
    equipment: "设备器具",
    medicine: "药品疫苗",
    facility: "基础设施",
    office: "办公用品",
    maintenance: "维修保养",
    other: "其他",
  };
  return labelMap[category] || category;
}

// ==============================
// 报销管理相关接口
// ==============================

/**
 * 创建报销申请
 */
exports.createReimbursement = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      title,
      description,
      category,
      businessPurpose,
      expensePeriodStart,
      expensePeriodEnd,
      paymentMethod,
      bankAccount,
      items,
    } = req.body;

    // 验证必填字段
    if (
      !title ||
      !category ||
      !businessPurpose ||
      !items ||
      items.length === 0
    ) {
      return res
        .status(400)
        .json(generateErrorResponse("请填写完整的报销信息"));
    }

    // 计算总金额
    const totalAmount = items.reduce(
      (sum, item) => sum + parseFloat(item.amount || 0),
      0,
    );

    // 生成报销单号
    const reimbursementNumber = await generateReimbursementNumber();

    // 获取用户信息
    const [userResult] = await db.query("SELECT * FROM users WHERE id = ?", [
      userId,
    ]);
    if (userResult.length === 0) {
      return res.status(404).json(generateErrorResponse("用户不存在"));
    }
    const user = userResult[0];

    // 开始事务
    await db.beginTransaction();

    try {
      // 插入报销申请
      const [reimbursementResult] = await db.query(
        `
        INSERT INTO oa_reimbursements (
          reimbursement_number, user_id, title, description, category,
          business_purpose, expense_period_start, expense_period_end,
          total_amount, payment_method, bank_account, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft')
      `,
        [
          reimbursementNumber,
          userId,
          title,
          description,
          category,
          businessPurpose,
          expensePeriodStart,
          expensePeriodEnd,
          totalAmount,
          paymentMethod,
          bankAccount,
        ],
      );

      const reimbursementId = reimbursementResult.insertId;

      // 插入报销明细
      for (const item of items) {
        await db.query(
          `
          INSERT INTO oa_reimbursement_items (
            reimbursement_id, expense_date, item_description, amount,
            category, location, invoice_number, has_invoice, remarks
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
          [
            reimbursementId,
            item.expenseDate,
            item.description,
            item.amount,
            item.category,
            item.location,
            item.invoiceNumber,
            item.hasInvoice || false,
            item.remarks,
          ],
        );
      }

      await db.commit();

      res.json(
        generateResponse({
          id: reimbursementId,
          reimbursementNumber,
          message: "报销申请创建成功",
        }),
      );
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("创建报销申请失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("创建报销申请失败"));
  }
};

/**
 * 获取报销申请列表
 */
exports.getReimbursements = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      status = "all",
      category = "all",
      timeRange = "all",
      keyword = "",
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let queryParams = [];

    // 根据用户权限决定查询范围
    const userPermissions = req.user.permissions || {};
    if (!userPermissions.canApprove) {
      // 普通用户只能查看自己的报销申请
      whereConditions.push("r.user_id = ?");
      queryParams.push(userId);
    }

    // 状态筛选
    if (status !== "all") {
      whereConditions.push("r.status = ?");
      queryParams.push(status);
    }

    // 类别筛选
    if (category !== "all") {
      whereConditions.push("r.category = ?");
      queryParams.push(category);
    }

    // 时间范围筛选
    if (timeRange !== "all") {
      const timeCondition = getTimeRangeCondition(timeRange);
      if (timeCondition) {
        whereConditions.push(timeCondition);
      }
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push(
        "(r.title LIKE ? OR r.description LIKE ? OR u.username LIKE ?)",
      );
      const keywordPattern = `%${keyword}%`;
      queryParams.push(keywordPattern, keywordPattern, keywordPattern);
    }

    const whereClause =
      whereConditions.length > 0
        ? "WHERE " + whereConditions.join(" AND ")
        : "";

    // 查询报销申请列表
    const [reimbursements] = await db.query(
      `
      SELECT
        r.*,
        u.username as applicant_name,
        u.employee_id as applicant_employee_id,
        u.department,
        u.position,
        u.phone,
        approver.username as approver_name
      FROM oa_reimbursements r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN users approver ON r.approved_by = approver.id
      ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `,
      [...queryParams, parseInt(limit), offset],
    );

    // 查询总数
    const [countResult] = await db.query(
      `
      SELECT COUNT(*) as total
      FROM oa_reimbursements r
      LEFT JOIN users u ON r.user_id = u.id
      ${whereClause}
    `,
      queryParams,
    );

    const total = countResult[0].total;

    // 格式化数据
    const formattedReimbursements = reimbursements.map((reimbursement) => ({
      ...reimbursement,
      statusLabel: getReimbursementStatusLabel(reimbursement.status),
      statusColor: getReimbursementStatusColor(reimbursement.status),
      categoryLabel: getReimbursementCategoryLabel(reimbursement.category),
      totalAmountFormatted: formatCurrency(reimbursement.total_amount),
    }));

    res.json(
      generateResponse({
        list: formattedReimbursements,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取报销申请列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取报销申请列表失败"));
  }
};

/**
 * 获取报销申请详情
 */
exports.getReimbursementDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 查询报销申请基本信息
    const [reimbursements] = await db.query(
      `
      SELECT
        r.*,
        u.username as applicant_name,
        u.employee_id as applicant_employee_id,
        u.department,
        u.position,
        u.phone,
        u.email,
        approver.username as approver_name,
        payer.username as payer_name
      FROM oa_reimbursements r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN users approver ON r.approved_by = approver.id
      LEFT JOIN users payer ON r.paid_by = payer.id
      WHERE r.id = ?
    `,
      [id],
    );

    if (reimbursements.length === 0) {
      return res.status(404).json(generateErrorResponse("报销申请不存在"));
    }

    const reimbursement = reimbursements[0];

    // 权限检查
    const userPermissions = req.user.permissions || {};
    if (!userPermissions.canApprove && reimbursement.user_id !== userId) {
      return res
        .status(403)
        .json(generateErrorResponse("没有权限查看此报销申请"));
    }

    // 查询报销明细
    const [items] = await db.query(
      `
      SELECT * FROM oa_reimbursement_items
      WHERE reimbursement_id = ?
      ORDER BY expense_date DESC
    `,
      [id],
    );

    // 查询审批流程（如果有）
    const [approvals] = await db.query(
      `
      SELECT
        a.*,
        u.username as approver_name
      FROM oa_approval_steps a
      LEFT JOIN users u ON a.approver_id = u.id
      WHERE a.approval_id IN (
        SELECT id FROM oa_approvals
        WHERE business_id = ? AND business_type = 'reimbursement'
      )
      ORDER BY a.step_number ASC
    `,
      [id],
    );

    // 格式化数据
    const formattedReimbursement = {
      ...reimbursement,
      statusLabel: getReimbursementStatusLabel(reimbursement.status),
      statusColor: getReimbursementStatusColor(reimbursement.status),
      categoryLabel: getReimbursementCategoryLabel(reimbursement.category),
      totalAmountFormatted: formatCurrency(reimbursement.total_amount),
      items: items.map((item) => ({
        ...item,
        amountFormatted: formatCurrency(item.amount),
        categoryLabel: getReimbursementCategoryLabel(item.category),
      })),
      approvals: approvals.map((approval) => ({
        ...approval,
        statusLabel: getApprovalStatusLabel(approval.status),
        statusColor: getApprovalStatusColor(approval.status),
      })),
    };

    res.json(generateResponse(formattedReimbursement));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取报销申请详情失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取报销申请详情失败"));
  }
};

/**
 * 提交报销申请
 */
exports.submitReimbursement = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 检查报销申请是否存在且属于当前用户
    const [reimbursements] = await db.query(
      `
      SELECT * FROM oa_reimbursements
      WHERE id = ? AND user_id = ? AND status = 'draft'
    `,
      [id, userId],
    );

    if (reimbursements.length === 0) {
      return res
        .status(404)
        .json(generateErrorResponse("报销申请不存在或无法提交"));
    }

    const reimbursement = reimbursements[0];

    // 检查是否有报销明细
    const [items] = await db.query(
      `
      SELECT COUNT(*) as count FROM oa_reimbursement_items
      WHERE reimbursement_id = ?
    `,
      [id],
    );

    if (items[0].count === 0) {
      return res
        .status(400)
        .json(generateErrorResponse("请添加报销明细后再提交"));
    }

    // 开始事务
    await db.beginTransaction();

    try {
      // 更新报销申请状态
      await db.query(
        `
        UPDATE oa_reimbursements
        SET status = 'submitted', submitted_at = NOW()
        WHERE id = ?
      `,
        [id],
      );

      // 创建审批流程
      await createApprovalWorkflow(
        id,
        "reimbursement",
        reimbursement.title,
        userId,
        reimbursement.total_amount,
      );

      await db.commit();

      res.json(generateResponse({ message: "报销申请提交成功" }));
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("提交报销申请失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("提交报销申请失败"));
  }
};

/**
 * 审批报销申请
 */
exports.approveReimbursement = async (req, res) => {
  try {
    const { id } = req.params;
    const { action, remarks } = req.body;
    const userId = req.user.id;

    // 验证操作类型
    if (!["approve", "reject"].includes(action)) {
      return res.status(400).json(generateErrorResponse("无效的审批操作"));
    }

    // 检查用户是否有审批权限
    const userPermissions = req.user.permissions || {};
    if (!userPermissions.canApprove) {
      return res.status(403).json(generateErrorResponse("没有审批权限"));
    }

    // 查询报销申请
    const [reimbursements] = await db.query(
      `
      SELECT * FROM oa_reimbursements
      WHERE id = ? AND status = 'submitted'
    `,
      [id],
    );

    if (reimbursements.length === 0) {
      return res
        .status(404)
        .json(generateErrorResponse("报销申请不存在或状态不正确"));
    }

    const reimbursement = reimbursements[0];

    // 开始事务
    await db.beginTransaction();

    try {
      const newStatus = action === "approve" ? "approved" : "rejected";

      // 更新报销申请状态
      await db.query(
        `
        UPDATE oa_reimbursements
        SET status = ?, approved_by = ?, approved_at = NOW(), approval_remarks = ?
        WHERE id = ?
      `,
        [newStatus, userId, remarks, id],
      );

      // 更新审批流程
      await updateApprovalWorkflow(
        id,
        "reimbursement",
        action,
        userId,
        remarks,
      );

      // 如果审批通过，创建支出记录
      if (action === "approve") {
        await db.query(
          `
          INSERT INTO oa_expenses (
            user_id, category, description, amount, expense_date,
            status, reimbursement_id
          ) VALUES (?, ?, ?, ?, ?, 'approved', ?)
        `,
          [
            reimbursement.user_id,
            reimbursement.category,
            reimbursement.title,
            reimbursement.total_amount,
            new Date().toISOString().split("T")[0],
            id,
          ],
        );
      }

      await db.commit();

      res.json(
        generateResponse({
          message: action === "approve" ? "审批通过" : "审批拒绝",
        }),
      );
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("审批报销申请失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("审批报销申请失败"));
  }
};

/**
 * 获取报销统计数据
 */
exports.getReimbursementStatistics = async (req, res) => {
  try {
    const userId = req.user.id;
    const userPermissions = req.user.permissions || {};

    let whereCondition = "";
    let queryParams = [];

    // 根据用户权限决定查询范围
    if (!userPermissions.canApprove) {
      whereCondition = "WHERE user_id = ?";
      queryParams.push(userId);
    }

    // 查询统计数据
    const [stats] = await db.query(
      `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'submitted' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
        COALESCE(SUM(CASE WHEN status = 'approved' THEN total_amount ELSE 0 END), 0) as totalAmount
      FROM oa_reimbursements
      ${whereCondition}
    `,
      queryParams,
    );

    const statistics = stats[0];

    res.json(
      generateResponse({
        total: statistics.total,
        pending: statistics.pending,
        approved: statistics.approved,
        rejected: statistics.rejected,
        totalAmount: parseFloat(statistics.totalAmount || 0),
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取报销统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取报销统计数据失败"));
  }
};

/**
 * 获取待审批列表
 */
exports.getPendingApprovals = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, businessType = "all" } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = [
      "a.status = ?",
      "step.approver_id = ?",
      "step.status = ?",
    ];
    let queryParams = ["pending", userId, "pending"];

    // 业务类型筛选
    if (businessType !== "all") {
      whereConditions.push("a.business_type = ?");
      queryParams.push(businessType);
    }

    const whereClause = "WHERE " + whereConditions.join(" AND ");

    // 查询待审批列表
    const [approvals] = await db.query(
      `
      SELECT
        a.*,
        step.step_number,
        step.step_name,
        step.deadline,
        u.username as applicant_name,
        u.department as applicant_department
      FROM oa_approvals a
      LEFT JOIN oa_approval_steps step ON a.id = step.approval_id AND step.step_number = a.current_step
      LEFT JOIN users u ON a.applicant_id = u.id
      ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `,
      [...queryParams, parseInt(limit), offset],
    );

    // 查询总数
    const [countResult] = await db.query(
      `
      SELECT COUNT(*) as total
      FROM oa_approvals a
      LEFT JOIN oa_approval_steps step ON a.id = step.approval_id AND step.step_number = a.current_step
      ${whereClause}
    `,
      queryParams,
    );

    const total = countResult[0].total;

    res.json(
      generateResponse({
        list: approvals,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取待审批列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取待审批列表失败"));
  }
};

/**
 * 获取审批历史
 */
exports.getApprovalHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      businessType = "all",
      status = "all",
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = ["step.approver_id = ?", "step.status != ?"];
    let queryParams = [userId, "pending"];

    // 业务类型筛选
    if (businessType !== "all") {
      whereConditions.push("a.business_type = ?");
      queryParams.push(businessType);
    }

    // 状态筛选
    if (status !== "all") {
      whereConditions.push("step.status = ?");
      queryParams.push(status);
    }

    const whereClause = "WHERE " + whereConditions.join(" AND ");

    // 查询审批历史
    const [approvals] = await db.query(
      `
      SELECT
        a.*,
        step.step_number,
        step.step_name,
        step.status as step_status,
        step.action,
        step.remarks,
        step.processed_at,
        u.username as applicant_name,
        u.department as applicant_department
      FROM oa_approvals a
      LEFT JOIN oa_approval_steps step ON a.id = step.approval_id
      LEFT JOIN users u ON a.applicant_id = u.id
      ${whereClause}
      ORDER BY step.processed_at DESC
      LIMIT ? OFFSET ?
    `,
      [...queryParams, parseInt(limit), offset],
    );

    // 查询总数
    const [countResult] = await db.query(
      `
      SELECT COUNT(*) as total
      FROM oa_approvals a
      LEFT JOIN oa_approval_steps step ON a.id = step.approval_id
      ${whereClause}
    `,
      queryParams,
    );

    const total = countResult[0].total;

    res.json(
      generateResponse({
        list: approvals,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取审批历史失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取审批历史失败"));
  }
};

/**
 * 获取审批历史统计数据
 */
exports.getApprovalHistoryStatistics = async (req, res) => {
  try {
    const userId = req.user.id;

    // 查询统计数据
    const [stats] = await db.query(
      `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN step.status = 'approved' THEN 1 END) as approved,
        COUNT(CASE WHEN step.status = 'rejected' THEN 1 END) as rejected,
        COUNT(CASE WHEN DATE(step.processed_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as thisMonth
      FROM oa_approval_steps step
      WHERE step.approver_id = ? AND step.status != 'pending'
    `,
      [userId],
    );

    const statistics = stats[0];

    res.json(
      generateResponse({
        total: statistics.total,
        approved: statistics.approved,
        rejected: statistics.rejected,
        thisMonth: statistics.thisMonth,
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取审批历史统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取审批历史统计数据失败"));
  }
};

/**
 * 获取审批统计数据（待审批页面用）
 */
exports.getApprovalStatistics = async (req, res) => {
  try {
    const userId = req.user.id;

    // 查询统计数据
    const [stats] = await db.query(
      `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN DATEDIFF(NOW(), a.created_at) > 1 THEN 1 END) as urgent,
        COUNT(CASE WHEN step.deadline < NOW() THEN 1 END) as overdue,
        COUNT(CASE WHEN DATE(a.created_at) = CURDATE() THEN 1 END) as today
      FROM oa_approvals a
      LEFT JOIN oa_approval_steps step ON a.id = step.approval_id AND step.step_number = a.current_step
      WHERE a.status = 'pending' AND step.approver_id = ? AND step.status = 'pending'
    `,
      [userId],
    );

    const statistics = stats[0];

    res.json(
      generateResponse({
        total: statistics.total,
        urgent: statistics.urgent,
        overdue: statistics.overdue,
        today: statistics.today,
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取审批统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取审批统计数据失败"));
  }
};

/**
 * 处理审批
 */
exports.processApproval = async (req, res) => {
  try {
    const { id } = req.params;
    const { action, remarks } = req.body;
    const userId = req.user.id;

    // 验证操作类型
    if (!["approve", "reject"].includes(action)) {
      return res.status(400).json(generateErrorResponse("无效的审批操作"));
    }

    // 检查用户是否有审批权限
    const userPermissions = req.user.permissions || {};
    if (!userPermissions.canApprove) {
      return res.status(403).json(generateErrorResponse("没有审批权限"));
    }

    // 查询审批实例和当前步骤
    const [approvals] = await db.query(
      `
      SELECT
        a.*,
        step.id as step_id,
        step.step_number,
        step.approver_id
      FROM oa_approvals a
      LEFT JOIN oa_approval_steps step ON a.id = step.approval_id AND step.step_number = a.current_step
      WHERE a.id = ? AND a.status = 'pending'
    `,
      [id],
    );

    if (approvals.length === 0) {
      return res
        .status(404)
        .json(generateErrorResponse("审批实例不存在或状态不正确"));
    }

    const approval = approvals[0];

    // 检查是否为当前审批人
    if (approval.approver_id !== userId) {
      return res
        .status(403)
        .json(generateErrorResponse("您不是当前步骤的审批人"));
    }

    // 开始事务
    await db.beginTransaction();

    try {
      const newStepStatus = action === "approve" ? "approved" : "rejected";

      // 更新审批步骤
      await db.query(
        `
        UPDATE oa_approval_steps
        SET status = ?, action = ?, remarks = ?, processed_at = NOW()
        WHERE id = ?
      `,
        [newStepStatus, action, remarks, approval.step_id],
      );

      let newApprovalStatus = "pending";

      if (action === "reject") {
        // 拒绝则整个审批流程结束
        newApprovalStatus = "rejected";
      } else if (approval.current_step >= approval.total_steps) {
        // 最后一步通过则整个审批流程通过
        newApprovalStatus = "approved";
      } else {
        // 进入下一步
        await db.query(
          `
          UPDATE oa_approvals
          SET current_step = current_step + 1
          WHERE id = ?
        `,
          [id],
        );
      }

      // 如果审批流程结束，更新状态
      if (newApprovalStatus !== "pending") {
        await db.query(
          `
          UPDATE oa_approvals
          SET status = ?, completed_at = NOW()
          WHERE id = ?
        `,
          [newApprovalStatus, id],
        );
      }

      // 更新业务表状态
      await this.updateBusinessStatus(
        approval.business_type,
        approval.business_id,
        newApprovalStatus,
      );

      await db.commit();

      res.json(
        generateResponse({
          message: action === "approve" ? "审批通过" : "审批拒绝",
        }),
      );
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("处理审批失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("处理审批失败"));
  }
};

/**
 * 更新业务表状态
 */
async function updateBusinessStatus(businessType, businessId, approvalStatus) {
  let tableName = "";
  let statusValue = "";

  switch (businessType) {
    case "reimbursement":
      tableName = "oa_reimbursements";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    case "leave":
      tableName = "oa_leaves";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    case "purchase":
      tableName = "oa_purchase_requests";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    default:
      return; // 不支持的业务类型
  }

  await db.query(
    `
    UPDATE ${tableName}
    SET status = ?, updated_at = NOW()
    WHERE id = ?
  `,
    [statusValue, businessId],
  );
}

// ==============================
// 流程模板管理相关接口
// ==============================

/**
 * 获取流程模板列表
 */
exports.getWorkflowTemplates = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category = "all",
      status = "all",
      keyword = "",
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let queryParams = [];

    // 类别筛选
    if (category !== "all") {
      whereConditions.push("category = ?");
      queryParams.push(category);
    }

    // 状态筛选
    if (status !== "all") {
      const isActive = status === "active" ? 1 : 0;
      whereConditions.push("is_active = ?");
      queryParams.push(isActive);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push("(name LIKE ? OR description LIKE ?)");
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    const whereClause =
      whereConditions.length > 0
        ? "WHERE " + whereConditions.join(" AND ")
        : "";

    // 查询模板列表
    const [templates] = await db.query(
      `
      SELECT
        id,
        name,
        category,
        description,
        version,
        is_active,
        creator_id,
        creator_name,
        usage_count,
        last_used_at,
        created_at,
        updated_at
      FROM oa_workflow_templates
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `,
      [...queryParams, parseInt(limit), offset],
    );

    // 查询总数
    const [countResult] = await db.query(
      `
      SELECT COUNT(*) as total
      FROM oa_workflow_templates
      ${whereClause}
    `,
      queryParams,
    );

    const total = countResult[0].total;

    res.json(
      generateResponse({
        list: templates,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取流程模板列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取流程模板列表失败"));
  }
};

/**
 * 获取流程模板统计数据
 */
exports.getWorkflowTemplateStatistics = async (req, res) => {
  try {
    // 查询统计数据
    const [stats] = await db.query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive,
        COUNT(CASE WHEN last_used_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recentUsed
      FROM oa_workflow_templates
    `);

    const statistics = stats[0];

    res.json(
      generateResponse({
        total: statistics.total,
        active: statistics.active,
        inactive: statistics.inactive,
        recentUsed: statistics.recentUsed,
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取流程模板统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取流程模板统计数据失败"));
  }
};

/**
 * 创建流程模板
 */
exports.createWorkflowTemplate = async (req, res) => {
  try {
    const { name, category, description, isActive } = req.body;
    const userId = req.user.id;
    const userName = req.user.username;

    // 验证必填字段
    if (!name || !category) {
      return res
        .status(400)
        .json(generateErrorResponse("模板名称和业务类别不能为空"));
    }

    // 检查模板名称是否重复
    const [existing] = await db.query(
      `
      SELECT id FROM oa_workflow_templates
      WHERE name = ? AND category = ?
    `,
      [name, category],
    );

    if (existing.length > 0) {
      return res
        .status(400)
        .json(generateErrorResponse("该类别下已存在同名模板"));
    }

    // 创建模板
    const [result] = await db.query(
      `
      INSERT INTO oa_workflow_templates
      (name, category, description, is_active, creator_id, creator_name)
      VALUES (?, ?, ?, ?, ?, ?)
    `,
      [name, category, description || "", isActive ? 1 : 0, userId, userName],
    );

    res.json(
      generateResponse({
        id: result.insertId,
        message: "模板创建成功",
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("创建流程模板失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("创建流程模板失败"));
  }
};

/**
 * 更新流程模板
 */
exports.updateWorkflowTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, category, description, isActive } = req.body;

    // 验证必填字段
    if (!name || !category) {
      return res
        .status(400)
        .json(generateErrorResponse("模板名称和业务类别不能为空"));
    }

    // 检查模板是否存在
    const [existing] = await db.query(
      `
      SELECT id FROM oa_workflow_templates WHERE id = ?
    `,
      [id],
    );

    if (existing.length === 0) {
      return res.status(404).json(generateErrorResponse("模板不存在"));
    }

    // 检查名称是否重复（排除当前模板）
    const [duplicate] = await db.query(
      `
      SELECT id FROM oa_workflow_templates
      WHERE name = ? AND category = ? AND id != ?
    `,
      [name, category, id],
    );

    if (duplicate.length > 0) {
      return res
        .status(400)
        .json(generateErrorResponse("该类别下已存在同名模板"));
    }

    // 更新模板
    await db.query(
      `
      UPDATE oa_workflow_templates
      SET name = ?, category = ?, description = ?, is_active = ?, updated_at = NOW()
      WHERE id = ?
    `,
      [name, category, description || "", isActive ? 1 : 0, id],
    );

    res.json(generateResponse({ message: "模板更新成功" }));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新流程模板失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("更新流程模板失败"));
  }
};

/**
 * 删除流程模板
 */
exports.deleteWorkflowTemplate = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查模板是否存在
    const [existing] = await db.query(
      `
      SELECT id, name FROM oa_workflow_templates WHERE id = ?
    `,
      [id],
    );

    if (existing.length === 0) {
      return res.status(404).json(generateErrorResponse("模板不存在"));
    }

    // 检查是否有正在使用的审批流程
    const [inUse] = await db.query(
      `
      SELECT COUNT(*) as count FROM oa_approvals
      WHERE template_id = ? AND status = 'pending'
    `,
      [id],
    );

    if (inUse[0].count > 0) {
      return res
        .status(400)
        .json(generateErrorResponse("该模板正在被使用，无法删除"));
    }

    // 删除模板（级联删除步骤）
    await db.query(`DELETE FROM oa_workflow_templates WHERE id = ?`, [id]);

    res.json(generateResponse({ message: "模板删除成功" }));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("删除流程模板失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("删除流程模板失败"));
  }
};

/**
 * 复制流程模板
 */
exports.copyWorkflowTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userName = req.user.username;

    // 查询原模板
    const [templates] = await db.query(
      `
      SELECT * FROM oa_workflow_templates WHERE id = ?
    `,
      [id],
    );

    if (templates.length === 0) {
      return res.status(404).json(generateErrorResponse("模板不存在"));
    }

    const template = templates[0];
    const newName = `${template.name}_副本`;

    // 检查新名称是否重复
    let copyIndex = 1;
    let finalName = newName;

    while (true) {
      const [duplicate] = await db.query(
        `
        SELECT id FROM oa_workflow_templates
        WHERE name = ? AND category = ?
      `,
        [finalName, template.category],
      );

      if (duplicate.length === 0) break;

      copyIndex++;
      finalName = `${newName}${copyIndex}`;
    }

    // 创建新模板
    const [result] = await db.query(
      `
      INSERT INTO oa_workflow_templates
      (name, category, description, version, is_active, steps_config, conditions_config, creator_id, creator_name)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `,
      [
        finalName,
        template.category,
        template.description,
        1, // 新版本从1开始
        0, // 默认停用
        template.steps_config,
        template.conditions_config,
        userId,
        userName,
      ],
    );

    // 复制模板步骤
    const [steps] = await db.query(
      `
      SELECT * FROM oa_workflow_template_steps WHERE template_id = ?
    `,
      [id],
    );

    if (steps.length > 0) {
      const stepValues = steps.map((step) => [
        result.insertId,
        step.step_number,
        step.step_name,
        step.step_type,
        step.approver_type,
        step.approver_config,
        step.condition_config,
        step.is_required,
        step.timeout_hours,
      ]);

      await db.query(
        `
        INSERT INTO oa_workflow_template_steps
        (template_id, step_number, step_name, step_type, approver_type, approver_config, condition_config, is_required, timeout_hours)
        VALUES ?
      `,
        [stepValues],
      );
    }

    res.json(
      generateResponse({
        id: result.insertId,
        message: "模板复制成功",
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("复制流程模板失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("复制流程模板失败"));
  }
};

/**
 * 切换模板状态
 */
exports.toggleWorkflowTemplateStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    // 检查模板是否存在
    const [existing] = await db.query(
      `
      SELECT id FROM oa_workflow_templates WHERE id = ?
    `,
      [id],
    );

    if (existing.length === 0) {
      return res.status(404).json(generateErrorResponse("模板不存在"));
    }

    // 更新状态
    await db.query(
      `
      UPDATE oa_workflow_templates
      SET is_active = ?, updated_at = NOW()
      WHERE id = ?
    `,
      [isActive ? 1 : 0, id],
    );

    res.json(
      generateResponse({
        message: isActive ? "模板已启用" : "模板已停用",
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("切换模板状态失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("切换模板状态失败"));
  }
};

// ==============================
// 权限管理相关接口
// ==============================

/**
 * 获取权限列表
 */
exports.getPermissions = async (req, res) => {
  try {
    const { module = "all" } = req.query;

    let whereConditions = ["is_active = 1"];
    let queryParams = [];

    // 模块筛选
    if (module !== "all") {
      whereConditions.push("module = ?");
      queryParams.push(module);
    }

    const whereClause = "WHERE " + whereConditions.join(" AND ");

    // 查询权限列表
    const [permissions] = await db.query(
      `
      SELECT
        id,
        name,
        code,
        description,
        module,
        resource,
        action,
        sort_order
      FROM oa_permissions
      ${whereClause}
      ORDER BY module, sort_order
    `,
      queryParams,
    );

    res.json(generateResponse(permissions));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取权限列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取权限列表失败"));
  }
};

/**
 * 获取角色列表
 */
exports.getRoles = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      level = "all",
      status = "all",
      keyword = "",
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let queryParams = [];

    // 级别筛选
    if (level !== "all") {
      whereConditions.push("level = ?");
      queryParams.push(parseInt(level));
    }

    // 状态筛选
    if (status !== "all") {
      const isActive = status === "active" ? 1 : 0;
      whereConditions.push("is_active = ?");
      queryParams.push(isActive);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push(
        "(name LIKE ? OR code LIKE ? OR description LIKE ?)",
      );
      queryParams.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    const whereClause =
      whereConditions.length > 0
        ? "WHERE " + whereConditions.join(" AND ")
        : "";

    // 查询角色列表
    const [roles] = await db.query(
      `
      SELECT
        id,
        name,
        code,
        description,
        level,
        is_system,
        is_active,
        data_scope,
        creator_id,
        creator_name,
        created_at,
        updated_at
      FROM oa_roles
      ${whereClause}
      ORDER BY level, created_at DESC
      LIMIT ? OFFSET ?
    `,
      [...queryParams, parseInt(limit), offset],
    );

    // 查询总数
    const [countResult] = await db.query(
      `
      SELECT COUNT(*) as total
      FROM oa_roles
      ${whereClause}
    `,
      queryParams,
    );

    const total = countResult[0].total;

    res.json(
      generateResponse({
        list: roles,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取角色列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取角色列表失败"));
  }
};

/**
 * 获取角色统计数据
 */
exports.getRoleStatistics = async (req, res) => {
  try {
    // 查询统计数据
    const [stats] = await db.query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive,
        COUNT(CASE WHEN is_system = 1 THEN 1 END) as systemRoles
      FROM oa_roles
    `);

    const statistics = stats[0];

    res.json(
      generateResponse({
        total: statistics.total,
        active: statistics.active,
        inactive: statistics.inactive,
        systemRoles: statistics.systemRoles,
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取角色统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取角色统计数据失败"));
  }
};

/**
 * 创建角色
 */
exports.createRole = async (req, res) => {
  try {
    const { name, code, description, level, dataScope, isActive } = req.body;
    const userId = req.user.id;
    const userName = req.user.username;

    // 验证必填字段
    if (!name || !code) {
      return res
        .status(400)
        .json(generateErrorResponse("角色名称和代码不能为空"));
    }

    // 验证角色代码格式
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(code)) {
      return res
        .status(400)
        .json(
          generateErrorResponse(
            "角色代码只能包含字母、数字和下划线，且以字母或下划线开头",
          ),
        );
    }

    // 检查角色代码是否重复
    const [existing] = await db.query(
      `
      SELECT id FROM oa_roles WHERE code = ?
    `,
      [code],
    );

    if (existing.length > 0) {
      return res.status(400).json(generateErrorResponse("角色代码已存在"));
    }

    // 创建角色
    const [result] = await db.query(
      `
      INSERT INTO oa_roles
      (name, code, description, level, data_scope, is_active, creator_id, creator_name)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `,
      [
        name,
        code,
        description || "",
        level || 3,
        dataScope || "self",
        isActive ? 1 : 0,
        userId,
        userName,
      ],
    );

    res.json(
      generateResponse({
        id: result.insertId,
        message: "角色创建成功",
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("创建角色失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("创建角色失败"));
  }
};

/**
 * 更新角色
 */
exports.updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, level, dataScope, isActive } = req.body;

    // 验证必填字段
    if (!name || !code) {
      return res
        .status(400)
        .json(generateErrorResponse("角色名称和代码不能为空"));
    }

    // 验证角色代码格式
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(code)) {
      return res
        .status(400)
        .json(
          generateErrorResponse(
            "角色代码只能包含字母、数字和下划线，且以字母或下划线开头",
          ),
        );
    }

    // 检查角色是否存在
    const [existing] = await db.query(
      `
      SELECT id, is_system FROM oa_roles WHERE id = ?
    `,
      [id],
    );

    if (existing.length === 0) {
      return res.status(404).json(generateErrorResponse("角色不存在"));
    }

    // 系统角色不允许修改代码
    if (existing[0].is_system && existing[0].code !== code) {
      return res
        .status(400)
        .json(generateErrorResponse("系统角色不允许修改代码"));
    }

    // 检查代码是否重复（排除当前角色）
    const [duplicate] = await db.query(
      `
      SELECT id FROM oa_roles
      WHERE code = ? AND id != ?
    `,
      [code, id],
    );

    if (duplicate.length > 0) {
      return res.status(400).json(generateErrorResponse("角色代码已存在"));
    }

    // 更新角色
    await db.query(
      `
      UPDATE oa_roles
      SET name = ?, code = ?, description = ?, level = ?, data_scope = ?, is_active = ?, updated_at = NOW()
      WHERE id = ?
    `,
      [
        name,
        code,
        description || "",
        level || 3,
        dataScope || "self",
        isActive ? 1 : 0,
        id,
      ],
    );

    res.json(generateResponse({ message: "角色更新成功" }));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新角色失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("更新角色失败"));
  }
};

/**
 * 删除角色
 */
exports.deleteRole = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查角色是否存在
    const [existing] = await db.query(
      `
      SELECT id, name, is_system FROM oa_roles WHERE id = ?
    `,
      [id],
    );

    if (existing.length === 0) {
      return res.status(404).json(generateErrorResponse("角色不存在"));
    }

    const role = existing[0];

    // 系统角色不能删除
    if (role.is_system) {
      return res.status(400).json(generateErrorResponse("系统角色不能删除"));
    }

    // 检查是否有用户使用该角色
    const [users] = await db.query(
      `
      SELECT COUNT(*) as count FROM oa_user_roles
      WHERE role_id = ? AND is_active = 1
    `,
      [id],
    );

    if (users[0].count > 0) {
      return res
        .status(400)
        .json(generateErrorResponse("该角色正在被使用，无法删除"));
    }

    // 删除角色（级联删除权限关联）
    await db.query(`DELETE FROM oa_roles WHERE id = ?`, [id]);

    res.json(generateResponse({ message: "角色删除成功" }));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("删除角色失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("删除角色失败"));
  }
};

/**
 * 切换角色状态
 */
exports.toggleRoleStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    // 检查角色是否存在
    const [existing] = await db.query(
      `
      SELECT id FROM oa_roles WHERE id = ?
    `,
      [id],
    );

    if (existing.length === 0) {
      return res.status(404).json(generateErrorResponse("角色不存在"));
    }

    // 更新状态
    await db.query(
      `
      UPDATE oa_roles
      SET is_active = ?, updated_at = NOW()
      WHERE id = ?
    `,
      [isActive ? 1 : 0, id],
    );

    res.json(
      generateResponse({
        message: isActive ? "角色已启用" : "角色已停用",
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("切换角色状态失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("切换角色状态失败"));
  }
};

/**
 * 获取角色权限
 */
exports.getRolePermissions = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查角色是否存在
    const [roles] = await db.query(
      `
      SELECT id FROM oa_roles WHERE id = ?
    `,
      [id],
    );

    if (roles.length === 0) {
      return res.status(404).json(generateErrorResponse("角色不存在"));
    }

    // 查询角色权限
    const [permissions] = await db.query(
      `
      SELECT
        p.id,
        p.name,
        p.code,
        p.description,
        p.module,
        p.resource,
        p.action
      FROM oa_permissions p
      INNER JOIN oa_role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ? AND p.is_active = 1
      ORDER BY p.module, p.sort_order
    `,
      [id],
    );

    res.json(generateResponse(permissions));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取角色权限失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取角色权限失败"));
  }
};

/**
 * 更新角色权限
 */
exports.updateRolePermissions = async (req, res) => {
  try {
    const { id } = req.params;
    const { permissionIds } = req.body;

    // 检查角色是否存在
    const [roles] = await db.query(
      `
      SELECT id, is_system FROM oa_roles WHERE id = ?
    `,
      [id],
    );

    if (roles.length === 0) {
      return res.status(404).json(generateErrorResponse("角色不存在"));
    }

    // 验证权限ID
    if (!Array.isArray(permissionIds)) {
      return res.status(400).json(generateErrorResponse("权限ID必须是数组"));
    }

    // 开始事务
    await db.beginTransaction();

    try {
      // 删除原有权限关联
      await db.query(
        `
        DELETE FROM oa_role_permissions WHERE role_id = ?
      `,
        [id],
      );

      // 添加新的权限关联
      if (permissionIds.length > 0) {
        const values = permissionIds.map((permissionId) => [id, permissionId]);
        await db.query(
          `
          INSERT INTO oa_role_permissions (role_id, permission_id) VALUES ?
        `,
          [values],
        );
      }

      await db.commit();

      res.json(generateResponse({ message: "权限配置成功" }));
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新角色权限失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("更新角色权限失败"));
  }
};

/**
 * 获取用户权限
 */
exports.getUserPermissions = async (req, res) => {
  try {
    const { userId } = req.params;

    // 查询用户角色和权限
    const [permissions] = await db.query(
      `
      SELECT DISTINCT
        p.id,
        p.name,
        p.code,
        p.description,
        p.module,
        p.resource,
        p.action,
        r.data_scope
      FROM oa_permissions p
      INNER JOIN oa_role_permissions rp ON p.id = rp.permission_id
      INNER JOIN oa_roles r ON rp.role_id = r.id
      INNER JOIN oa_user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
        AND ur.is_active = 1
        AND r.is_active = 1
        AND p.is_active = 1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      ORDER BY p.module, p.sort_order
    `,
      [userId],
    );

    // 查询用户角色
    const [roles] = await db.query(
      `
      SELECT
        r.id,
        r.name,
        r.code,
        r.level,
        r.data_scope
      FROM oa_roles r
      INNER JOIN oa_user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
        AND ur.is_active = 1
        AND r.is_active = 1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      ORDER BY r.level
    `,
      [userId],
    );

    res.json(
      generateResponse({
        permissions,
        roles,
        dataScope: roles.length > 0 ? roles[0].data_scope : "self",
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取用户权限失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取用户权限失败"));
  }
};

/**
 * 获取用户列表（权限管理用）
 */
exports.getUsersForPermission = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      department = "all",
      role = "all",
      status = "all",
      keyword = "",
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let queryParams = [];

    // 部门筛选
    if (department !== "all") {
      whereConditions.push("u.department_id = ?");
      queryParams.push(parseInt(department));
    }

    // 状态筛选
    if (status !== "all") {
      const isActive = status === "active" ? 1 : 0;
      whereConditions.push("u.is_active = ?");
      queryParams.push(isActive);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push(
        "(u.username LIKE ? OR u.email LIKE ? OR u.real_name LIKE ?)",
      );
      queryParams.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    const whereClause =
      whereConditions.length > 0
        ? "WHERE " + whereConditions.join(" AND ")
        : "";

    // 查询用户列表
    const [users] = await db.query(
      `
      SELECT
        u.id,
        u.username,
        u.email,
        u.real_name,
        u.avatar,
        u.is_active,
        u.department_id,
        d.name as department_name,
        u.created_at,
        u.updated_at
      FROM users u
      LEFT JOIN oa_departments d ON u.department_id = d.id
      ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `,
      [...queryParams, parseInt(limit), offset],
    );

    // 查询用户角色
    for (let user of users) {
      const [roles] = await db.query(
        `
        SELECT
          r.id,
          r.name,
          r.code,
          r.level,
          r.data_scope
        FROM oa_roles r
        INNER JOIN oa_user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
          AND ur.is_active = 1
          AND r.is_active = 1
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
        ORDER BY r.level
      `,
        [user.id],
      );

      user.roles = roles;
    }

    // 角色筛选（在查询后进行）
    let filteredUsers = users;
    if (role !== "all") {
      filteredUsers = users.filter((user) =>
        user.roles.some((r) => r.id === parseInt(role)),
      );
    }

    // 查询总数
    const [countResult] = await db.query(
      `
      SELECT COUNT(*) as total
      FROM users u
      LEFT JOIN oa_departments d ON u.department_id = d.id
      ${whereClause}
    `,
      queryParams,
    );

    const total = countResult[0].total;

    res.json(
      generateResponse({
        list: filteredUsers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      }),
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取用户列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取用户列表失败"));
  }
};

/**
 * 获取用户权限统计数据
 */
exports.getUserPermissionStatistics = async (req, res) => {
  try {
    // 查询统计数据
    const [stats] = await db.query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive
      FROM users
    `);

    // 查询有角色的用户数
    const [roleStats] = await db.query(`
      SELECT COUNT(DISTINCT ur.user_id) as hasRoles
      FROM oa_user_roles ur
      INNER JOIN oa_roles r ON ur.role_id = r.id
      WHERE ur.is_active = 1
        AND r.is_active = 1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `);

    const statistics = {
      ...stats[0],
      hasRoles: roleStats[0].hasRoles,
    };

    res.json(generateResponse(statistics));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取用户权限统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取用户权限统计数据失败"));
  }
};

/**
 * 获取用户角色
 */
exports.getUserRoles = async (req, res) => {
  try {
    const { userId } = req.params;

    // 查询用户角色
    const [roles] = await db.query(
      `
      SELECT
        r.id,
        r.name,
        r.code,
        r.description,
        r.level,
        r.data_scope,
        ur.assigned_at,
        ur.expires_at
      FROM oa_roles r
      INNER JOIN oa_user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
        AND ur.is_active = 1
        AND r.is_active = 1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      ORDER BY r.level
    `,
      [userId],
    );

    res.json(generateResponse(roles));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取用户角色失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取用户角色失败"));
  }
};

/**
 * 更新用户角色
 */
exports.updateUserRoles = async (req, res) => {
  try {
    const { userId } = req.params;
    const { roleIds } = req.body;
    const assignerId = req.user.id;
    const assignerName = req.user.username;

    // 验证角色ID
    if (!Array.isArray(roleIds)) {
      return res.status(400).json(generateErrorResponse("角色ID必须是数组"));
    }

    // 检查用户是否存在
    const [users] = await db.query(
      `
      SELECT id, username FROM users WHERE id = ?
    `,
      [userId],
    );

    if (users.length === 0) {
      return res.status(404).json(generateErrorResponse("用户不存在"));
    }

    // 开始事务
    await db.beginTransaction();

    try {
      // 删除原有角色关联
      await db.query(
        `
        UPDATE oa_user_roles
        SET is_active = 0
        WHERE user_id = ?
      `,
        [userId],
      );

      // 添加新的角色关联
      if (roleIds.length > 0) {
        const values = roleIds.map((roleId) => [
          userId,
          roleId,
          assignerId,
          assignerName,
        ]);

        await db.query(
          `
          INSERT INTO oa_user_roles (user_id, role_id, assigned_by, assigned_by_name)
          VALUES ?
        `,
          [values],
        );
      }

      await db.commit();

      res.json(generateResponse({ message: "角色分配成功" }));
    } catch (error) {
      await db.rollback();
      throw error;
    }
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("更新用户角色失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("更新用户角色失败"));
  }
};

/**
 * 获取部门列表
 */
exports.getDepartments = async (req, res) => {
  try {
    const { active = "all" } = req.query;

    let whereConditions = [];
    let queryParams = [];

    // 状态筛选
    if (active !== "all") {
      const isActive = active === "true" ? 1 : 0;
      whereConditions.push("is_active = ?");
      queryParams.push(isActive);
    }

    const whereClause =
      whereConditions.length > 0
        ? "WHERE " + whereConditions.join(" AND ")
        : "";

    // 查询部门列表
    const [departments] = await db.query(
      `
      SELECT
        id,
        name,
        code,
        parent_id,
        level,
        path,
        manager_id,
        manager_name,
        description,
        sort_order,
        is_active,
        created_at
      FROM oa_departments
      ${whereClause}
      ORDER BY sort_order, created_at
    `,
      queryParams,
    );

    res.json(generateResponse(departments));
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("获取部门列表失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取部门列表失败"));
  }
};

// ==============================
// 辅助函数
// ==============================

/**
 * 生成报销单号
 */
async function generateReimbursementNumber() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");

  const prefix = `RB${year}${month}${day}`;

  // 查询当天最大序号
  const [result] = await db.query(
    `
    SELECT MAX(CAST(SUBSTRING(reimbursement_number, -4) AS UNSIGNED)) as max_seq
    FROM oa_reimbursements
    WHERE reimbursement_number LIKE ?
  `,
    [`${prefix}%`],
  );

  const nextSeq = (result[0].max_seq || 0) + 1;
  return `${prefix}${String(nextSeq).padStart(4, "0")}`;
}

/**
 * 获取报销状态标签
 */
function getReimbursementStatusLabel(status) {
  const labelMap = {
    draft: "草稿",
    submitted: "待审批",
    approved: "已批准",
    rejected: "已拒绝",
    paid: "已付款",
    cancelled: "已取消",
  };
  return labelMap[status] || status;
}

/**
 * 获取报销状态颜色
 */
function getReimbursementStatusColor(status) {
  const colorMap = {
    draft: "#999999",
    submitted: "#FF9500",
    approved: "#00A86B",
    rejected: "#FF3B30",
    paid: "#007AFF",
    cancelled: "#8E8E93",
  };
  return colorMap[status] || "#999999";
}

/**
 * 获取报销类别标签
 */
function getReimbursementCategoryLabel(category) {
  const labelMap = {
    travel: "差旅费",
    office: "办公用品",
    meal: "餐费",
    transport: "交通费",
    communication: "通讯费",
    training: "培训费",
    entertainment: "招待费",
    other: "其他",
  };
  return labelMap[category] || category;
}

/**
 * 获取审批状态标签
 */
function getApprovalStatusLabel(status) {
  const labelMap = {
    pending: "待处理",
    approved: "已通过",
    rejected: "已拒绝",
    skipped: "已跳过",
  };
  return labelMap[status] || status;
}

/**
 * 获取审批状态颜色
 */
function getApprovalStatusColor(status) {
  const colorMap = {
    pending: "#FF9500",
    approved: "#00A86B",
    rejected: "#FF3B30",
    skipped: "#8E8E93",
  };
  return colorMap[status] || "#999999";
}

/**
 * 格式化货币
 */
function formatCurrency(amount) {
  return `¥${parseFloat(amount || 0).toFixed(2)}`;
}

/**
 * 获取时间范围条件
 */
function getTimeRangeCondition(timeRange) {
  const now = new Date();
  let startDate, endDate;

  switch (timeRange) {
    case "today":
      startDate = endDate = now.toISOString().split("T")[0];
      return `DATE(r.created_at) = '${startDate}'`;

    case "week":
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
      const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6));
      return `DATE(r.created_at) BETWEEN '${weekStart.toISOString().split("T")[0]}' AND '${weekEnd.toISOString().split("T")[0]}'`;

    case "month":
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      return `DATE(r.created_at) BETWEEN '${monthStart.toISOString().split("T")[0]}' AND '${monthEnd.toISOString().split("T")[0]}'`;

    case "quarter":
      const quarter = Math.floor(now.getMonth() / 3);
      const quarterStart = new Date(now.getFullYear(), quarter * 3, 1);
      const quarterEnd = new Date(now.getFullYear(), quarter * 3 + 3, 0);
      return `DATE(r.created_at) BETWEEN '${quarterStart.toISOString().split("T")[0]}' AND '${quarterEnd.toISOString().split("T")[0]}'`;

    default:
      return null;
  }
}

/**
 * 创建审批流程
 */
async function createApprovalWorkflow(
  businessId,
  businessType,
  title,
  applicantId,
  amount,
) {
  // 根据业务类型和金额确定审批流程
  const [workflows] = await db.query(
    `
    SELECT * FROM oa_approval_workflows
    WHERE category = ? AND is_active = true
    ORDER BY version DESC LIMIT 1
  `,
    [businessType],
  );

  if (workflows.length === 0) {
    throw new Error("未找到对应的审批流程");
  }

  const workflow = workflows[0];
  const stepsConfig = JSON.parse(workflow.steps_config);

  // 创建审批实例
  const [approvalResult] = await db.query(
    `
    INSERT INTO oa_approvals (
      workflow_id, business_id, business_type, title, applicant_id, total_steps
    ) VALUES (?, ?, ?, ?, ?, ?)
  `,
    [
      workflow.id,
      businessId,
      businessType,
      title,
      applicantId,
      stepsConfig.length,
    ],
  );

  const approvalId = approvalResult.insertId;

  // 创建审批步骤
  for (let i = 0; i < stepsConfig.length; i++) {
    const step = stepsConfig[i];

    // 根据条件判断是否需要此步骤
    if (step.condition && !evaluateCondition(step.condition, { amount })) {
      continue;
    }

    // 查找审批人（简化处理，实际应该根据角色和部门查找）
    const [approvers] = await db.query(`
      SELECT id FROM users
      WHERE JSON_CONTAINS(permissions, '{"canApprove": true}')
      LIMIT 1
    `);

    if (approvers.length > 0) {
      await db.query(
        `
        INSERT INTO oa_approval_steps (
          approval_id, step_number, step_name, approver_id, deadline
        ) VALUES (?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 3 DAY))
      `,
        [approvalId, step.step, step.name, approvers[0].id],
      );
    }
  }

  return approvalId;
}

/**
 * 更新审批流程
 */
async function updateApprovalWorkflow(
  businessId,
  businessType,
  action,
  approverId,
  remarks,
) {
  // 查找审批实例
  const [approvals] = await db.query(
    `
    SELECT * FROM oa_approvals
    WHERE business_id = ? AND business_type = ? AND status = 'pending'
  `,
    [businessId, businessType],
  );

  if (approvals.length === 0) {
    return;
  }

  const approval = approvals[0];

  // 更新当前步骤
  await db.query(
    `
    UPDATE oa_approval_steps
    SET status = ?, action = ?, remarks = ?, processed_at = NOW()
    WHERE approval_id = ? AND step_number = ? AND approver_id = ?
  `,
    [
      action === "approve" ? "approved" : "rejected",
      action,
      remarks,
      approval.id,
      approval.current_step,
      approverId,
    ],
  );

  // 更新审批实例状态
  if (action === "reject") {
    await db.query(
      `
      UPDATE oa_approvals
      SET status = 'rejected', completed_at = NOW()
      WHERE id = ?
    `,
      [approval.id],
    );
  } else if (approval.current_step >= approval.total_steps) {
    await db.query(
      `
      UPDATE oa_approvals
      SET status = 'approved', completed_at = NOW()
      WHERE id = ?
    `,
      [approval.id],
    );
  } else {
    await db.query(
      `
      UPDATE oa_approvals
      SET current_step = current_step + 1
      WHERE id = ?
    `,
      [approval.id],
    );
  }
}

/**
 * 评估条件表达式
 */
function evaluateCondition(condition, context) {
  try {
    // 简单的条件评估，实际应该使用更安全的表达式解析器
    const expression = condition.replace(/amount/g, context.amount);
    return eval(expression);
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error("条件评估失败:", error); } catch(_) {}

    return true; // 默认通过
  }
}
