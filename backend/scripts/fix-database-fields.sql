-- 修复数据库字段不匹配问题
-- 针对13个字段不匹配的模型进行修复

USE zhihuiyange_local;

-- 1. 修复ai_configs表，添加缺失字段
ALTER TABLE ai_configs 
ADD COLUMN IF NOT EXISTS provider VARCHAR(50) COMMENT 'AI服务提供商',
ADD COLUMN IF NOT EXISTS priority INT DEFAULT 0 COMMENT '优先级';

-- 2. 修复users表，添加缺失字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS tenant_role VARCHAR(50) COMMENT '租户角色',
ADD COLUMN IF NOT EXISTS last_login_at DATETIME COMMENT '最后登录时间';

-- 3. 确保flocks表字段完整
ALTER TABLE flocks 
ADD COLUMN IF NOT EXISTS flock_id VARCHAR(50) COMMENT '鹅群标识符';

-- 4. 修复production_records表
ALTER TABLE production_records 
ADD COLUMN IF NOT EXISTS production_date DATE COMMENT '生产日期';

-- 5. 修复health_records表
ALTER TABLE health_records 
ADD COLUMN IF NOT EXISTS record_status ENUM('active', 'archived') DEFAULT 'active' COMMENT '记录状态';

-- 6. 修复unified_inventory表，添加缺失字段
ALTER TABLE unified_inventory 
ADD COLUMN IF NOT EXISTS item_name VARCHAR(100) COMMENT '物品名称',
ADD COLUMN IF NOT EXISTS quantity INT DEFAULT 0 COMMENT '数量';

-- 显示完成信息
SELECT 'Database fields fix completed!' as status;