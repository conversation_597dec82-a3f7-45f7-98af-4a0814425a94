/* pages/profile/profile.wxss - 个人中心页面样式 V2.0 */
@import '/styles/design-system.wxss';

.profile-container {
  min-height: 100vh;
  background: #f2f2f7;
  padding: 0;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

/* 全局卡片样式 - 确保所有组件宽度一致并添加边框 */
.profile-container c-card {
  margin: 16rpx !important;
  border: 1rpx solid #e0e0e0 !important;
  border-radius: 24rpx !important;
  background: #ffffff !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05) !important;
}

/* 用户信息横幅 */
.user-info-banner {
  background: linear-gradient(135deg, #0066CC 0%, #00CC99 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  color: #ffffff;
  margin: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.user-info-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.user-info-content:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.user-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name-line {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.2;
}

.user-role {
  font-size: 20rpx;
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 600;
  backdrop-filter: blur(5rpx);
}

.farm-name-line {
  display: flex;
  align-items: center;
}

.farm-name {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
}

.edit-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-left: 24rpx;
}

.user-info-content:active .edit-icon {
  opacity: 1;
  transform: scale(1.1);
}

/* 区块 */
.section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(232, 235, 255, 0.2) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
}

.section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
}

.stats-section::before {
  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

.menu-section::before {
  background: linear-gradient(90deg, var(--accent) 0%, var(--accent-light) 100%);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xl);
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.section-title::before {
  content: '📊';
  font-size: var(--text-lg);
  opacity: 0.6;
}

.menu-section .section-title::before {
  content: '⚙️';
}

.section-more {
  font-size: var(--text-sm);
  color: var(--primary);
  font-weight: var(--font-semibold);
  padding: var(--space-xs) var(--space-sm);
  border: 2rpx solid var(--primary-light);
  border-radius: var(--radius-lg);
  background-color: var(--primary-subtle);
  transition: var(--transition-all);
}

.section-more:active {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  color: var(--text-inverse);
  transform: scale(0.95);
}

/* 订单快捷入口 - 风格统一版 */
.order-status-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
  position: relative;
  z-index: 1;
  padding: 0 12rpx;
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 12rpx;
  background: #ffffff;
  border-radius: var(--radius-xl);
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  min-height: 100rpx;
}

.order-status-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
  border-color: #d0d0d0;
}

.status-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
  opacity: 0.8;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.order-status-item:active .status-icon {
  opacity: 1;
  transform: scale(1.05);
}

.status-text {
  font-size: 22rpx;
  color: #333333;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  margin-top: auto;
}

.status-count {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  background: #ff4757;
  color: #ffffff;
  font-size: 18rpx;
  font-weight: bold;
  padding: 3rpx 6rpx;
  border-radius: 16rpx;
  min-width: 28rpx;
  text-align: center;
  line-height: 1.2;
}

@keyframes statusBadgePulse {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

/* OA办公模块 - 统一设计风格 */
.oa-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 12rpx;
  position: relative;
  z-index: 1;
}

.oa-item {
  padding: 24rpx;
  background: #ffffff;
  border-radius: var(--radius-xl);
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  min-height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.oa-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
  border-color: #d0d0d0;
}

.oa-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
  gap: 12rpx;
}

.oa-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.3;
  flex: 1;
}

.oa-badge {
  background: #ff4757;
  color: #ffffff;
  font-size: 18rpx;
  font-weight: bold;
  padding: 3rpx 6rpx;
  border-radius: 16rpx;
  min-width: 28rpx;
  text-align: center;
  line-height: 1.2;
  flex-shrink: 0;
}

.oa-description {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.3;
  flex: 1;
}

/* 删除原有的数据统计样式 */
.stat-title {
  font-size: 24rpx;
  color: #666666;
}

/* 功能服务 - 与办公管理模块完全一致的样式 */
.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 12rpx;
  position: relative;
  z-index: 1;
}

.service-item {
  padding: 24rpx;
  background: #ffffff;
  border-radius: var(--radius-xl);
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
  border-color: #d0d0d0;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
  gap: 12rpx;
}

.service-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.3;
  flex: 1;
}

.service-badge {
  background: #ff4757;
  color: #ffffff;
  font-size: 18rpx;
  font-weight: bold;
  padding: 3rpx 6rpx;
  border-radius: 16rpx;
  min-width: 28rpx;
  text-align: center;
  line-height: 1.2;
  flex-shrink: 0;
}

.service-description {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.3;
  flex: 1;
}

.arrow-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.5;
  transition: var(--transition-all);
}

.menu-item:active .arrow-icon {
  opacity: 1;
  transform: translateX(4rpx);
}

/* 退出登录 */
.logout-section {
  padding: 16rpx;
}

.logout-btn {
  width: 100%;
  height: 120rpx;
  background: #ff4757;
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:active {
  background: #ff3742;
  transform: scale(0.98);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 可访问性支持 */
@media (prefers-contrast: high) {

  .user-info-section,
  .section,
  .order-status-item,
  .oa-item,
  .menu-item {
    border-width: 2rpx;
    border-color: var(--text-primary);
  }

  .user-name,
  .section-title,
  .status-text,
  .menu-title {
    font-weight: var(--font-bold);
  }
}

@media (prefers-reduced-motion: reduce) {

  .profile-container,
  .user-info-section,
  .section,
  .order-status-item,
  .oa-item,
  .menu-item,
  .logout-section {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .status-count {
    animation: none !important;
  }
}