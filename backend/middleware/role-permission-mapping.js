/**
 * 智慧养鹅SAAS平台 - 角色权限映射系统
 * 基于微信小程序开发规范和SAAS多租户架构设计
 * 
 * 四级权限体系：
 * 1. 平台超级管理员
 * 2. 租户管理员
 * 3. 部门经理
 * 4. 普通员工
 */

// 权限定义 - 基于auth-unified.js的PERMISSIONS常量
const PERMISSIONS = {
  // 平台级权限
  PLATFORM: {
    SUPER_ADMIN: 'platform:super_admin',
    TENANT_MANAGE: 'platform:tenant:manage',
    SYSTEM_CONFIG: 'platform:system:config',
    CROSS_TENANT_VIEW: 'platform:cross_tenant:view',
    ANALYTICS: 'platform:analytics:view',
    MONITORING: 'platform:monitoring:view'
  },
  
  // 租户级权限
  TENANT: {
    ADMIN: 'tenant:admin',
    USER_MANAGE: 'tenant:user:manage',
    DEPT_MANAGE: 'tenant:dept:manage',
    CONFIG: 'tenant:config',
    AUDIT: 'tenant:audit'
  },
  
  // 生产管理权限
  PRODUCTION: {
    VIEW: 'production:view',
    MANAGE: 'production:manage',
    RECORD_CREATE: 'production:record:create',
    RECORD_UPDATE: 'production:record:update',
    RECORD_DELETE: 'production:record:delete',
    INVENTORY_VIEW: 'production:inventory:view',
    INVENTORY_MANAGE: 'production:inventory:manage',
    ENVIRONMENT_VIEW: 'production:environment:view',
    ENVIRONMENT_MANAGE: 'production:environment:manage'
  },
  
  // 健康管理权限
  HEALTH: {
    VIEW: 'health:view',
    MANAGE: 'health:manage',
    DIAGNOSIS: 'health:diagnosis',
    AI_DIAGNOSIS: 'health:ai_diagnosis',
    TREATMENT: 'health:treatment',
    REPORT_VIEW: 'health:report:view',
    REPORT_EXPORT: 'health:report:export'
  },
  
  // OA办公权限
  OA: {
    ACCESS: 'oa:access',
    FINANCE_VIEW: 'oa:finance:view',
    FINANCE_MANAGE: 'oa:finance:manage',
    PURCHASE_VIEW: 'oa:purchase:view',
    PURCHASE_CREATE: 'oa:purchase:create',
    PURCHASE_APPROVE: 'oa:purchase:approve',
    REIMBURSEMENT_VIEW: 'oa:reimbursement:view',
    REIMBURSEMENT_CREATE: 'oa:reimbursement:create',
    REIMBURSEMENT_APPROVE: 'oa:reimbursement:approve',
    APPROVAL_PROCESS: 'oa:approval:process',
    STAFF_MANAGE: 'oa:staff:manage'
  },
  
  // 商城权限
  SHOP: {
    VIEW: 'shop:view',
    MANAGE: 'shop:manage',
    PRODUCT_CREATE: 'shop:product:create',
    PRODUCT_UPDATE: 'shop:product:update',
    PRODUCT_DELETE: 'shop:product:delete',
    ORDER_VIEW: 'shop:order:view',
    ORDER_PROCESS: 'shop:order:process',
    ORDER_REFUND: 'shop:order:refund'
  },
  
  // 用户管理权限
  USER: {
    VIEW: 'user:view',
    MANAGE: 'user:manage',
    CREATE: 'user:create',
    UPDATE: 'user:update',
    DELETE: 'user:delete',
    ROLE_ASSIGN: 'user:role:assign'
  }
};

// 角色定义
const ROLES = {
  // 平台级角色
  PLATFORM_SUPER_ADMIN: 'platform_super_admin',
  
  // 租户级角色
  TENANT_ADMIN: 'tenant_admin',
  DEPARTMENT_MANAGER: 'department_manager',
  EMPLOYEE: 'employee',
  
  // 专业角色
  VETERINARIAN: 'veterinarian',        // 兽医
  PRODUCTION_MANAGER: 'production_manager', // 生产管理员
  FINANCIAL_STAFF: 'financial_staff',  // 财务人员
  SALES_STAFF: 'sales_staff'          // 销售人员
};

// 角色权限映射
const ROLE_PERMISSIONS = {
  // 平台超级管理员 - 拥有所有权限
  [ROLES.PLATFORM_SUPER_ADMIN]: [
    // 平台权限
    ...Object.values(PERMISSIONS.PLATFORM),
    // 租户权限
    ...Object.values(PERMISSIONS.TENANT),
    // 业务权限
    ...Object.values(PERMISSIONS.PRODUCTION),
    ...Object.values(PERMISSIONS.HEALTH),
    ...Object.values(PERMISSIONS.OA),
    ...Object.values(PERMISSIONS.SHOP),
    ...Object.values(PERMISSIONS.USER)
  ],
  
  // 租户管理员 - 租户内所有权限
  [ROLES.TENANT_ADMIN]: [
    // 租户管理权限
    PERMISSIONS.TENANT.ADMIN,
    PERMISSIONS.TENANT.USER_MANAGE,
    PERMISSIONS.TENANT.DEPT_MANAGE,
    PERMISSIONS.TENANT.CONFIG,
    PERMISSIONS.TENANT.AUDIT,
    
    // 生产管理权限
    ...Object.values(PERMISSIONS.PRODUCTION),
    
    // 健康管理权限
    ...Object.values(PERMISSIONS.HEALTH),
    
    // OA办公权限
    ...Object.values(PERMISSIONS.OA),
    
    // 商城管理权限
    ...Object.values(PERMISSIONS.SHOP),
    
    // 用户管理权限
    ...Object.values(PERMISSIONS.USER)
  ],
  
  // 部门经理 - 部门级管理权限
  [ROLES.DEPARTMENT_MANAGER]: [
    // 基础查看权限
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.USER.VIEW,
    
    // 生产管理权限
    PERMISSIONS.PRODUCTION.MANAGE,
    PERMISSIONS.PRODUCTION.RECORD_CREATE,
    PERMISSIONS.PRODUCTION.RECORD_UPDATE,
    PERMISSIONS.PRODUCTION.INVENTORY_VIEW,
    PERMISSIONS.PRODUCTION.ENVIRONMENT_VIEW,
    
    // 健康管理权限
    PERMISSIONS.HEALTH.MANAGE,
    PERMISSIONS.HEALTH.DIAGNOSIS,
    PERMISSIONS.HEALTH.TREATMENT,
    PERMISSIONS.HEALTH.REPORT_VIEW,
    
    // OA办公权限
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.PURCHASE_VIEW,
    PERMISSIONS.OA.PURCHASE_CREATE,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_CREATE,
    PERMISSIONS.OA.APPROVAL_PROCESS,
    
    // 商城权限
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.SHOP.ORDER_VIEW
  ],
  
  // 普通员工 - 基础权限
  [ROLES.EMPLOYEE]: [
    // 基础查看权限
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.SHOP.VIEW,
    
    // 基础操作权限
    PERMISSIONS.PRODUCTION.RECORD_CREATE,
    PERMISSIONS.HEALTH.DIAGNOSIS,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_CREATE,
    PERMISSIONS.SHOP.ORDER_VIEW
  ],
  
  // 兽医 - 健康专业权限
  [ROLES.VETERINARIAN]: [
    // 基础权限
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.OA.ACCESS,
    
    // 健康管理专业权限
    ...Object.values(PERMISSIONS.HEALTH),
    
    // 相关生产权限
    PERMISSIONS.PRODUCTION.RECORD_CREATE,
    PERMISSIONS.PRODUCTION.RECORD_UPDATE,
    PERMISSIONS.PRODUCTION.ENVIRONMENT_VIEW
  ],
  
  // 生产管理员 - 生产专业权限
  [ROLES.PRODUCTION_MANAGER]: [
    // 基础权限
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.SHOP.VIEW,  
    PERMISSIONS.OA.ACCESS,
    
    // 生产管理专业权限
    ...Object.values(PERMISSIONS.PRODUCTION),
    
    // 相关健康权限
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.HEALTH.DIAGNOSIS,
    PERMISSIONS.HEALTH.REPORT_VIEW
  ],
  
  // 财务人员 - 财务专业权限
  [ROLES.FINANCIAL_STAFF]: [
    // 基础权限
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.SHOP.VIEW,
    
    // OA财务专业权限
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.FINANCE_MANAGE,
    PERMISSIONS.OA.PURCHASE_VIEW,
    PERMISSIONS.OA.PURCHASE_APPROVE,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_APPROVE,
    PERMISSIONS.OA.APPROVAL_PROCESS,
    
    // 商城订单权限
    PERMISSIONS.SHOP.ORDER_VIEW,
    PERMISSIONS.SHOP.ORDER_PROCESS
  ],
  
  // 销售人员 - 销售专业权限
  [ROLES.SALES_STAFF]: [
    // 基础权限
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.OA.ACCESS,
    
    // 商城专业权限
    ...Object.values(PERMISSIONS.SHOP),
    
    // 相关权限
    PERMISSIONS.USER.VIEW
  ]
};

// 权限继承关系
const ROLE_HIERARCHY = {
  [ROLES.PLATFORM_SUPER_ADMIN]: [],
  [ROLES.TENANT_ADMIN]: [ROLES.PLATFORM_SUPER_ADMIN],
  [ROLES.DEPARTMENT_MANAGER]: [ROLES.TENANT_ADMIN],
  [ROLES.EMPLOYEE]: [ROLES.DEPARTMENT_MANAGER],
  
  // 专业角色继承基础员工权限
  [ROLES.VETERINARIAN]: [ROLES.EMPLOYEE],
  [ROLES.PRODUCTION_MANAGER]: [ROLES.EMPLOYEE],
  [ROLES.FINANCIAL_STAFF]: [ROLES.EMPLOYEE],
  [ROLES.SALES_STAFF]: [ROLES.EMPLOYEE]
};

/**
 * 角色权限映射器
 */
class RolePermissionMapper {
  
  /**
   * 获取角色的所有权限（包括继承的权限）
   * @param {string} role 角色名称
   * @returns {string[]} 权限列表
   */
  static getRolePermissions(role) {
    if (!ROLE_PERMISSIONS[role]) {
      return [];
    }
    
    const permissions = new Set(ROLE_PERMISSIONS[role]);
    
    // 添加继承的权限
    const parentRoles = this.getParentRoles(role);
    parentRoles.forEach(parentRole => {
      if (ROLE_PERMISSIONS[parentRole]) {
        ROLE_PERMISSIONS[parentRole].forEach(permission => {
          permissions.add(permission);
        });
      }
    });
    
    return Array.from(permissions);
  }
  
  /**
   * 获取角色的父级角色
   * @param {string} role 角色名称
   * @returns {string[]} 父级角色列表
   */
  static getParentRoles(role) {
    const parents = [];
    const visited = new Set();
    
    const findParents = (currentRole) => {
      if (visited.has(currentRole)) return;
      visited.add(currentRole);
      
      const directParents = ROLE_HIERARCHY[currentRole] || [];
      directParents.forEach(parent => {
        parents.push(parent);
        findParents(parent);
      });
    };
    
    findParents(role);
    return parents;
  }
  
  /**
   * 检查角色是否有指定权限
   * @param {string} role 角色名称
   * @param {string} permission 权限名称
   * @returns {boolean} 是否有权限
   */
  static hasPermission(role, permission) {
    const rolePermissions = this.getRolePermissions(role);
    return rolePermissions.includes(permission);
  }
  
  /**
   * 检查角色是否有任一权限
   * @param {string} role 角色名称
   * @param {string[]} permissions 权限列表
   * @returns {boolean} 是否有任一权限
   */
  static hasAnyPermission(role, permissions) {
    const rolePermissions = this.getRolePermissions(role);
    return permissions.some(permission => rolePermissions.includes(permission));
  }
  
  /**
   * 检查角色是否有所有权限
   * @param {string} role 角色名称
   * @param {string[]} permissions 权限列表
   * @returns {boolean} 是否有所有权限
   */
  static hasAllPermissions(role, permissions) {
    const rolePermissions = this.getRolePermissions(role);
    return permissions.every(permission => rolePermissions.includes(permission));
  }
  
  /**
   * 获取所有角色
   * @returns {string[]} 角色列表
   */
  static getAllRoles() {
    return Object.values(ROLES);
  }
  
  /**
   * 获取所有权限
   * @returns {string[]} 权限列表
   */
  static getAllPermissions() {
    const permissions = new Set();
    Object.values(PERMISSIONS).forEach(category => {
      Object.values(category).forEach(permission => {
        permissions.add(permission);
      });
    });
    return Array.from(permissions);
  }
  
  /**
   * 验证权限配置
   * @returns {Object} 验证结果
   */
  static validateConfiguration() {
    const errors = [];
    const warnings = [];
    
    // 检查角色权限引用的权限是否存在
    const allPermissions = this.getAllPermissions();
    Object.entries(ROLE_PERMISSIONS).forEach(([role, permissions]) => {
      permissions.forEach(permission => {
        if (!allPermissions.includes(permission)) {
          errors.push(`角色 ${role} 引用了不存在的权限: ${permission}`);
        }
      });
    });
    
    // 检查权限继承是否存在循环引用
    Object.keys(ROLE_HIERARCHY).forEach(role => {
      const parents = this.getParentRoles(role);
      if (parents.includes(role)) {
        errors.push(`角色 ${role} 存在循环继承`);
      }
    });
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}

module.exports = {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
  ROLE_HIERARCHY,
  RolePermissionMapper
};