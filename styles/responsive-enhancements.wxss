/* 智慧养鹅小程序 - 响应式设计增强 V1.0 */
/* 支持更多设备类型和屏幕尺寸 */

/* ==================== 设备类型检测 ==================== */

/* 小屏幕手机 (< 375px) */
@media screen and (max-width: 750rpx) {
  .container {
    padding: var(--space-md);
  }
  
  .section {
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
  }
  
  .text-responsive {
    font-size: var(--text-sm);
  }
  
  .button-responsive {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-sm);
  }
  
  /* 网格自适应 */
  .grid-responsive {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }
}

/* 标准手机 (375px - 414px) */
@media screen and (min-width: 750rpx) and (max-width: 828rpx) {
  .container {
    padding: var(--space-lg);
  }
  
  .section {
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
  }
  
  .text-responsive {
    font-size: var(--text-base);
  }
  
  .button-responsive {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--text-base);
  }
  
  /* 网格自适应 */
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }
}

/* 大屏手机 (> 414px) */
@media screen and (min-width: 828rpx) {
  .container {
    padding: var(--space-xl);
    max-width: 1200rpx;
    margin: 0 auto;
  }
  
  .section {
    padding: var(--space-2xl);
    margin-bottom: var(--space-2xl);
  }
  
  .text-responsive {
    font-size: var(--text-lg);
  }
  
  .button-responsive {
    padding: var(--space-lg) var(--space-xl);
    font-size: var(--text-lg);
  }
  
  /* 网格自适应 */
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-lg);
  }
  
  /* 平板布局优化 */
  .tablet-layout {
    display: flex;
    gap: var(--space-2xl);
  }
  
  .tablet-sidebar {
    width: 300rpx;
    flex-shrink: 0;
  }
  
  .tablet-main {
    flex: 1;
  }
}

/* ==================== 横竖屏适配 ==================== */

/* 横屏模式 */
@media screen and (orientation: landscape) {
  .landscape-hide {
    display: none;
  }
  
  .landscape-show {
    display: block;
  }
  
  /* 横屏时的容器调整 */
  .landscape-container {
    display: flex;
    height: 100vh;
  }
  
  .landscape-sidebar {
    width: 400rpx;
    background: var(--bg-primary);
    border-right: 1rpx solid var(--border-light);
  }
  
  .landscape-content {
    flex: 1;
    overflow-y: auto;
  }
  
  /* 横屏时的网格调整 */
  .landscape-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-md);
  }
}

/* 竖屏模式 */
@media screen and (orientation: portrait) {
  .portrait-hide {
    display: none;
  }
  
  .portrait-show {
    display: block;
  }
  
  /* 竖屏时的布局调整 */
  .portrait-stack {
    flex-direction: column;
  }
}

/* ==================== 高像素密度屏幕适配 ==================== */

/* Retina屏幕优化 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .retina-border {
    border-width: 0.5rpx;
  }
  
  .retina-shadow {
    box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  }
}

/* 超高清屏幕优化 */
@media screen and (-webkit-min-device-pixel-ratio: 3) {
  .ultra-hd-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
}

/* ==================== 安全区域适配 ==================== */

/* iPhone X 系列安全区域 */
.safe-area-container {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

/* 底部安全区域 */
.safe-bottom {
  padding-bottom: calc(var(--space-lg) + constant(safe-area-inset-bottom));
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
}

/* 顶部安全区域 */
.safe-top {
  padding-top: calc(var(--space-lg) + constant(safe-area-inset-top));
  padding-top: calc(var(--space-lg) + env(safe-area-inset-top));
}

/* ==================== 可访问性增强 ==================== */

/* 动效减少偏好 - 小程序通过类名控制 */
/* @media (prefers-reduced-motion: reduce) { */ /* 小程序不支持 */
.reduced-motion {
  animation: none !important;
  transition: none !important;
}

.reduced-motion view,
.reduced-motion text,
.reduced-motion button,
.reduced-motion input,
.reduced-motion image,
.reduced-motion scroll-view {
  animation: none !important;
  transition: none !important;
}
/* } */

/* 高对比度模式 - 小程序通过类名控制 */
/* @media (prefers-contrast: high) { */ /* 小程序不支持 */
.high-contrast {
  --border-light: #000000;
  --border-medium: #000000;
  --text-secondary: #000000;
  --bg-secondary: #ffffff;
}
/* } */

/* 深色模式偏好 - 小程序通过类名控制 */
/* @media (prefers-color-scheme: dark) { */ /* 小程序不支持 */
.dark-mode-auto {
  --bg-primary: #1a1a1a;
  --bg-secondary: #0f0f0f;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #999999;
  --border-light: #333333;
  --border-medium: #555555;
}
/* } */

/* ==================== 设备特定优化 ==================== */

/* iPad Pro 适配 */
@media screen and (min-width: 1024px) {
  .ipad-pro-layout {
    display: grid;
    grid-template-columns: 320px 1fr 320px;
    gap: var(--space-2xl);
    max-width: 1400rpx;
    margin: 0 auto;
  }
  
  .ipad-pro-sidebar {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    height: fit-content;
    position: sticky;
    top: var(--space-2xl);
  }
  
  .ipad-pro-main {
    min-height: 100vh;
  }
}

/* 折叠屏设备适配 */
@media screen and (min-width: 1200px) {
  .foldable-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--space-3xl);
  }
  
  .foldable-primary {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-lg);
  }
  
  .foldable-secondary {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: var(--space-xl);
  }
}

/* ==================== 字体缩放适配 ==================== */

/* 大字体偏好 */
@media screen and (min-resolution: 2dppx) {
  .large-text {
    font-size: calc(var(--text-base) * 1.2);
    line-height: calc(var(--leading-normal) * 1.1);
  }
}

/* 小字体偏好 */
@media screen and (max-resolution: 1dppx) {
  .small-text {
    font-size: calc(var(--text-base) * 0.9);
    line-height: calc(var(--leading-normal) * 0.95);
  }
}

/* ==================== 动态视口单位 ==================== */

/* 动态视口高度 - 小程序只支持标准vh */
.dvh-full {
  height: 100vh;
  /* height: 100dvh; */ /* 小程序不支持 */
}

.dvh-screen {
  min-height: 100vh;
  /* min-height: 100dvh; */ /* 小程序不支持 */
}

/* 小视口高度 - 小程序只支持标准vh */
.svh-full {
  height: 100vh;
  /* height: 100svh; */ /* 小程序不支持 */
}

/* 大视口高度 - 小程序只支持标准vh */
.lvh-full {
  height: 100vh;
  /* height: 100lvh; */ /* 小程序不支持 */
}

/* ==================== 容器查询（改为媒体查询） ==================== */

/* 基于容器大小的样式 - 小程序不支持@container，改为媒体查询 */
/* @container (min-width: 600rpx) { */ /* 小程序不支持容器查询 */
@media screen and (min-width: 600rpx) {
  .container-responsive {
    padding: var(--space-2xl);
  }
  
  .container-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
/* } */

/* @container (min-width: 900rpx) { */ /* 小程序不支持容器查询 */
@media screen and (min-width: 900rpx) {
  .container-responsive {
    padding: var(--space-3xl);
  }
  
  .container-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
/* } */

/* ==================== 响应式工具类 ==================== */

/* 显示/隐藏工具类 */
.hide-mobile {
  display: none;
}

@media screen and (min-width: 750rpx) {
  .hide-mobile {
    display: block;
  }
  
  .show-mobile {
    display: none;
  }
}

.hide-tablet {
  display: block;
}

@media screen and (min-width: 1024rpx) {
  .hide-tablet {
    display: none;
  }
  
  .show-tablet {
    display: block;
  }
}

/* 间距响应式类 */
.p-responsive {
  padding: var(--space-sm);
}

@media screen and (min-width: 750rpx) {
  .p-responsive {
    padding: var(--space-lg);
  }
}

@media screen and (min-width: 1024rpx) {
  .p-responsive {
    padding: var(--space-2xl);
  }
}

/* 字体响应式类 */
.text-responsive-sm {
  font-size: var(--text-xs);
}

@media screen and (min-width: 750rpx) {
  .text-responsive-sm {
    font-size: var(--text-sm);
  }
}

@media screen and (min-width: 1024rpx) {
  .text-responsive-sm {
    font-size: var(--text-base);
  }
}

/* ==================== 性能优化 ==================== */

/* 预加载关键资源 */
.preload-critical {
  /* 小程序中使用display:none来控制可见性 */
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* 延迟加载非关键内容 */
.lazy-load {
  /* 小程序中使用opacity来控制延迟显示 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-load.loaded {
  opacity: 1;
}

/* GPU加速 */
.gpu-layer {
  transform: translateZ(0);
  will-change: transform;
}

/* 避免重排重绘 */
.no-reflow {
  /* 小程序中使用transform来优化渲染性能 */
  transform: translateZ(0);
  backface-visibility: hidden;
}