// components/lazy-list/lazy-list.js
const { throttle } = require('../../utils/performance.js');

/**
 * 懒加载列表组件
 * 优化大数据列表的渲染性能
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 列表数据
    items: {
      type: Array,
      value: []
    },
    // 每次渲染的数量
    pageSize: {
      type: Number,
      value: 20
    },
    // 预加载阈值
    threshold: {
      type: Number,
      value: 3
    },
    // 是否启用虚拟滚动
    virtual: {
      type: Boolean,
      value: false
    },
    // 每项高度（虚拟滚动时需要）
    itemHeight: {
      type: Number,
      value: 100
    },
    // 容器高度（虚拟滚动时需要）
    containerHeight: {
      type: Number,
      value: 600
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    displayItems: [],
    currentPage: 1,
    loading: false,
    hasMore: true,
    // 虚拟滚动相关
    startIndex: 0,
    endIndex: 0,
    visibleItems: [],
    paddingTop: 0,
    paddingBottom: 0
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initList();
      this.throttledLoadMore = throttle(this.loadMore.bind(this), 300);
    },

    detached() {
      // 清理资源
      this.throttledLoadMore = null;
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'items': function(newItems) {
      this.initList();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化列表
     */
    initList() {
      const { items, pageSize, virtual, containerHeight, itemHeight } = this.properties;
      
      if (virtual) {
        this.initVirtualList();
      } else {
        const displayItems = items.slice(0, pageSize);
        this.setData({
          displayItems,
          currentPage: 1,
          hasMore: items.length > pageSize
        });
      }
    },

    /**
     * 初始化虚拟列表
     */
    initVirtualList() {
      const { containerHeight, itemHeight } = this.properties;
      const visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // 多渲染2个作为缓冲
      
      this.setData({
        endIndex: Math.min(visibleCount, this.properties.items.length)
      });
      
      this.updateVisibleItems();
    },

    /**
     * 更新可见项
     */
    updateVisibleItems() {
      const { items, itemHeight } = this.properties;
      const { startIndex, endIndex } = this.data;
      
      const visibleItems = items.slice(startIndex, endIndex);
      const paddingTop = startIndex * itemHeight;
      const paddingBottom = (items.length - endIndex) * itemHeight;
      
      this.setData({
        visibleItems,
        paddingTop,
        paddingBottom
      });
    },

    /**
     * 加载更多数据
     */
    loadMore() {
      if (this.data.loading || !this.data.hasMore) {
        return;
      }

      const { items, pageSize } = this.properties;
      const { currentPage, displayItems } = this.data;
      
      this.setData({ loading: true });
      
      // 模拟异步加载
      setTimeout(() => {
        const nextPage = currentPage + 1;
        const startIndex = (nextPage - 1) * pageSize;
        const endIndex = nextPage * pageSize;
        const newItems = items.slice(startIndex, endIndex);
        
        if (newItems.length > 0) {
          this.setData({
            displayItems: [...displayItems, ...newItems],
            currentPage: nextPage,
            loading: false,
            hasMore: endIndex < items.length
          });
        } else {
          this.setData({
            loading: false,
            hasMore: false
          });
        }
      }, 100);
    },

    /**
     * 滚动事件处理
     */
    onScroll(e) {
      const { virtual, containerHeight, itemHeight } = this.properties;
      const { scrollTop } = e.detail;
      
      if (virtual) {
        this.handleVirtualScroll(scrollTop);
      } else {
        this.handleNormalScroll(e);
      }
    },

    /**
     * 虚拟滚动处理
     */
    handleVirtualScroll(scrollTop) {
      const { containerHeight, itemHeight, items } = this.properties;
      const visibleCount = Math.ceil(containerHeight / itemHeight) + 2;
      
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(startIndex + visibleCount, items.length);
      
      if (startIndex !== this.data.startIndex || endIndex !== this.data.endIndex) {
        this.setData({
          startIndex,
          endIndex
        });
        this.updateVisibleItems();
      }
    },

    /**
     * 普通滚动处理
     */
    handleNormalScroll(e) {
      const { scrollTop, scrollHeight, height } = e.detail;
      const { threshold } = this.properties;
      
      // 接近底部时触发加载更多
      if (scrollHeight - scrollTop - height < threshold * 50) {
        this.throttledLoadMore();
      }
    },

    /**
     * 刷新列表
     */
    refresh() {
      this.setData({
        currentPage: 1,
        hasMore: true,
        loading: false
      });
      this.initList();
    },

    /**
     * 滚动到顶部
     */
    scrollToTop() {
      this.createSelectorQuery()
        .select('.lazy-list')
        .node()
        .exec((res) => {
          if (res[0] && res[0].node) {
            res[0].node.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
          }
        });
    },

    /**
     * 项目点击事件
     */
    onItemTap(e) {
      const { index } = e.currentTarget.dataset;
      const { virtual } = this.properties;
      
      let item;
      if (virtual) {
        item = this.data.visibleItems[index];
      } else {
        item = this.data.displayItems[index];
      }
      
      this.triggerEvent('itemtap', { item, index });
    }
  }
});