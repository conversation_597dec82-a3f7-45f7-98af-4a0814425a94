// pages/announcement/announcement-list/announcement-list.js
Page({
  data: {
    activeFilter: 'all',
    searchKeyword: '',
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    announcements: [],
    filteredAnnouncements: [],
    announcementCounts: {
      all: 0,
      important: 0,
      notice: 0,
      policy: 0,
      activity: 0
    },
    unreadCount: 0
  },

  onLoad: function (options) {
    this.loadAnnouncements();
  },

  onPullDownRefresh: function () {
    this.setData({
      page: 1,
      announcements: []
    });
    this.loadAnnouncements(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.onLoadMore();
    }
  },

  // 加载公告列表
  loadAnnouncements(callback) {
    this.setData({ loading: true });

    // 模拟API调用
    setTimeout(() => {
      const mockAnnouncements = this.getMockAnnouncements();
      const allAnnouncements = this.data.page === 1 ? 
        mockAnnouncements : 
        [...this.data.announcements, ...mockAnnouncements];
      
      this.setData({
        announcements: allAnnouncements,
        loading: false,
        hasMore: mockAnnouncements.length === this.data.pageSize
      });
      
      this.filterAnnouncements();
      this.calculateCounts();
      callback && callback();
    }, 1000);
  },

  // 获取模拟公告数据
  getMockAnnouncements() {
    const baseAnnouncements = [
      {
        id: 1,
        title: '关于加强冬季养殖管理的重要通知',
        summary: '随着气温下降，各养殖户需要特别注意保温措施，确保鹅群健康过冬...',
        type: 'important',
        typeText: '重要',
        publisher: '养殖管理部',
        publishTime: '2小时前',
        viewCount: 156,
        attachmentCount: 2,
        isRead: false,
        isTop: true,
        isImportant: true
      },
      {
        id: 2,
        title: '新版养殖管理系统上线公告',
        summary: '为了提升用户体验，我们对养殖管理系统进行了全面升级...',
        type: 'notice',
        typeText: '通知',
        publisher: '技术部',
        publishTime: '5小时前',
        viewCount: 89,
        attachmentCount: 0,
        isRead: true,
        isTop: false,
        isImportant: false
      },
      {
        id: 3,
        title: '2024年养殖补贴政策解读',
        summary: '根据最新政策文件，2024年度养殖补贴标准有所调整...',
        type: 'policy',
        typeText: '政策',
        publisher: '政策解读组',
        publishTime: '1天前',
        viewCount: 234,
        attachmentCount: 3,
        isRead: false,
        isTop: false,
        isImportant: true
      },
      {
        id: 4,
        title: '春季养殖技术培训班报名通知',
        summary: '为提高养殖户的技术水平，特举办春季养殖技术培训班...',
        type: 'activity',
        typeText: '活动',
        publisher: '培训中心',
        publishTime: '2天前',
        viewCount: 67,
        attachmentCount: 1,
        isRead: true,
        isTop: false,
        isImportant: false
      },
      {
        id: 5,
        title: '疫病防控工作指导意见',
        summary: '针对近期疫病防控形势，现发布最新的防控工作指导意见...',
        type: 'important',
        typeText: '重要',
        publisher: '防疫部门',
        publishTime: '3天前',
        viewCount: 178,
        attachmentCount: 0,
        isRead: false,
        isTop: false,
        isImportant: true
      }
    ];

    // 根据页码返回对应数据
    const startIndex = (this.data.page - 1) * this.data.pageSize;
    return baseAnnouncements.slice(startIndex, startIndex + this.data.pageSize);
  },

  // 筛选公告
  filterAnnouncements() {
    let filtered = [...this.data.announcements];
    
    // 按类型筛选
    if (this.data.activeFilter !== 'all') {
      filtered = filtered.filter(announcement => {
        if (this.data.activeFilter === 'important') {
          return announcement.isImportant;
        }
        return announcement.type === this.data.activeFilter;
      });
    }
    
    // 按关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(announcement => 
        announcement.title.toLowerCase().includes(keyword) ||
        announcement.summary.toLowerCase().includes(keyword) ||
        announcement.publisher.toLowerCase().includes(keyword)
      );
    }
    
    // 排序：置顶在前，然后按时间倒序
    filtered.sort((a, b) => {
      if (a.isTop && !b.isTop) return -1;
      if (!a.isTop && b.isTop) return 1;
      return new Date(b.publishTime) - new Date(a.publishTime);
    });
    
    this.setData({
      filteredAnnouncements: filtered
    });
  },

  // 计算各类型数量
  calculateCounts() {
    const { announcements } = this.data;
    const counts = {
      all: announcements.length,
      important: announcements.filter(a => a.isImportant).length,
      notice: announcements.filter(a => a.type === 'notice').length,
      policy: announcements.filter(a => a.type === 'policy').length,
      activity: announcements.filter(a => a.type === 'activity').length
    };
    
    const unreadCount = announcements.filter(a => !a.isRead).length;
    
    this.setData({ 
      announcementCounts: counts,
      unreadCount: unreadCount
    });
  },

  // 切换筛选条件
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter
    });
    this.filterAnnouncements();
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.filterAnnouncements();
  },

  // 执行搜索
  onSearch() {
    this.filterAnnouncements();
  },

  // 清除搜索
  onClearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.filterAnnouncements();
  },

  // 点击公告项
  onAnnouncementTap(e) {
    const announcementId = e.currentTarget.dataset.id;
    
    // 标记为已读
    this.markAsRead(announcementId);
    
    // 跳转到详情页
    wx.navigateTo({
      url: `/pages/announcement/announcement-detail/announcement-detail?id=${announcementId}`
    });
  },

  // 标记为已读
  markAsRead(announcementId) {
    const announcements = this.data.announcements.map(announcement => {
      if (announcement.id === announcementId && !announcement.isRead) {
        return { ...announcement, isRead: true };
      }
      return announcement;
    });

    this.setData({ announcements });
    this.filterAnnouncements();
    this.calculateCounts();
  },

  // 全部标记为已读
  onMarkAllRead() {
    wx.showModal({
      title: '确认操作',
      content: '确定要将所有公告标记为已读吗？',
      success: (res) => {
        if (res.confirm) {
          const announcements = this.data.announcements.map(announcement => ({
            ...announcement,
            isRead: true
          }));

          this.setData({ announcements });
          this.filterAnnouncements();
          this.calculateCounts();

          wx.showToast({
            title: '已全部标记为已读',
            icon: 'success'
          });
        }
      }
    });
  },

  // 加载更多
  onLoadMore() {
    if (this.data.loading || !this.data.hasMore) return;
    
    this.setData({
      page: this.data.page + 1
    });
    this.loadAnnouncements();
  }
});
