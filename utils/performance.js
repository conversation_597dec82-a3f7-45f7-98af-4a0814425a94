/**
 * 性能优化工具集
 * 提供组件加载、渲染性能优化的通用工具函数
 */

/**
 * 防抖函数 - 限制函数执行频率
 * @param {Function} func 要执行的函数
 * @param {number} wait 等待时间(ms)
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait, immediate = false) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(this, args);
  };
}

/**
 * 节流函数 - 控制函数执行频率
 * @param {Function} func 要执行的函数
 * @param {number} limit 时间间隔(ms)
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 懒加载管理器
 * 管理组件和数据的懒加载
 */
class LazyLoadManager {
  constructor() {
    this.loadedComponents = new Set();
    this.loadingComponents = new Map();
    this.cache = new Map();
  }

  /**
   * 懒加载组件
   * @param {string} componentName 组件名称
   * @param {Function} loader 加载函数
   * @returns {Promise} 加载Promise
   */
  async loadComponent(componentName, loader) {
    // 如果已加载，直接返回
    if (this.loadedComponents.has(componentName)) {
      return this.cache.get(componentName);
    }

    // 如果正在加载，返回现有Promise
    if (this.loadingComponents.has(componentName)) {
      return this.loadingComponents.get(componentName);
    }

    // 开始加载
    const loadPromise = loader()
      .then(result => {
        this.loadedComponents.add(componentName);
        this.cache.set(componentName, result);
        this.loadingComponents.delete(componentName);
        return result;
      })
      .catch(error => {
        this.loadingComponents.delete(componentName);
        try { const logger = require('./logger.js'); logger.error && logger.error(`[LazyLoad] 组件加载失败: ${componentName}`, error); } catch(_) {}
        throw error;
      });

    this.loadingComponents.set(componentName, loadPromise);
    return loadPromise;
  }

  /**
   * 预加载组件
   * @param {Array} componentNames 组件名称列表
   * @param {Object} loaders 加载器映射
   */
  async preloadComponents(componentNames, loaders) {
    const promises = componentNames.map(name => {
      const loader = loaders[name];
      if (loader && !this.loadedComponents.has(name)) {
        return this.loadComponent(name, loader);
      }
      return Promise.resolve();
    });

    try {
      await Promise.all(promises);
      try { const logger = require('./logger.js'); logger.debug && logger.debug('[LazyLoad] 预加载完成', componentNames); } catch(_) {}
    } catch (error) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[LazyLoad] 预加载部分失败', error); } catch(_) {}
    }
  }

  /**
   * 清理缓存
   * @param {string} componentName 组件名称，不传则清理所有
   */
  clearCache(componentName) {
    if (componentName) {
      this.cache.delete(componentName);
      this.loadedComponents.delete(componentName);
    } else {
      this.cache.clear();
      this.loadedComponents.clear();
    }
  }
}

/**
 * 数据缓存管理器
 * 管理页面数据的缓存和更新策略
 */
class DataCacheManager {
  constructor() {
    this.cache = new Map();
    this.timestamps = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5分钟默认过期时间
  }

  /**
   * 设置缓存数据
   * @param {string} key 缓存键
   * @param {*} data 缓存数据
   * @param {number} ttl 过期时间(ms)
   */
  set(key, data, ttl = this.defaultTTL) {
    this.cache.set(key, data);
    this.timestamps.set(key, Date.now() + ttl);
  }

  /**
   * 获取缓存数据
   * @param {string} key 缓存键
   * @returns {*} 缓存数据或null
   */
  get(key) {
    if (!this.cache.has(key)) {
      return null;
    }

    const expireTime = this.timestamps.get(key);
    if (Date.now() > expireTime) {
      this.delete(key);
      return null;
    }

    return this.cache.get(key);
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    this.cache.delete(key);
    this.timestamps.delete(key);
  }

  /**
   * 清理过期缓存
   */
  cleanExpired() {
    const now = Date.now();
    for (const [key, expireTime] of this.timestamps.entries()) {
      if (now > expireTime) {
        this.delete(key);
      }
    }
  }

  /**
   * 清理所有缓存
   */
  clear() {
    this.cache.clear();
    this.timestamps.clear();
  }
}

/**
 * 渲染性能优化器
 * 提供渲染性能优化相关工具
 */
class RenderOptimizer {
  constructor() {
    this.updateQueue = [];
    this.isUpdating = false;
    this.frameId = null;
  }

  /**
   * 批量setData更新
   * @param {Object} page 页面实例
   * @param {Object} data 要更新的数据
   * @param {Function} callback 回调函数
   */
  batchUpdate(page, data, callback) {
    this.updateQueue.push({ page, data, callback });
    
    if (!this.isUpdating) {
      this.scheduleUpdate();
    }
  }

  /**
   * 调度更新
   */
  scheduleUpdate() {
    this.isUpdating = true;
    this.frameId = wx.nextTick(() => {
      this.flushUpdates();
    });
  }

  /**
   * 执行批量更新
   */
  flushUpdates() {
    const updates = this.updateQueue.splice(0);
    const pageUpdates = new Map();

    // 合并同一页面的更新
    updates.forEach(({ page, data, callback }) => {
      if (!pageUpdates.has(page)) {
        pageUpdates.set(page, { data: {}, callbacks: [] });
      }
      
      const pageUpdate = pageUpdates.get(page);
      Object.assign(pageUpdate.data, data);
      if (callback) {
        pageUpdate.callbacks.push(callback);
      }
    });

    // 执行合并后的更新
    pageUpdates.forEach(({ data, callbacks }, page) => {
      page.setData(data, () => {
        callbacks.forEach(callback => callback());
      });
    });

    this.isUpdating = false;
  }

  /**
   * 取消调度的更新
   */
  cancelUpdate() {
    if (this.frameId) {
      wx.nextTick.cancel(this.frameId);
      this.frameId = null;
      this.isUpdating = false;
    }
  }
}

/**
 * 内存管理器
 * 管理页面内存使用和清理
 */
class MemoryManager {
  constructor() {
    this.timers = new Set();
    this.listeners = new Map();
    this.observers = new Set();
  }

  /**
   * 注册定时器
   * @param {number} timerId 定时器ID
   */
  addTimer(timerId) {
    this.timers.add(timerId);
  }

  /**
   * 注册事件监听器
   * @param {string} event 事件名
   * @param {Function} handler 处理函数
   */
  addListener(event, handler) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(handler);
  }

  /**
   * 注册观察者
   * @param {Object} observer 观察者对象
   */
  addObserver(observer) {
    this.observers.add(observer);
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    // 清理定时器
    this.timers.forEach(timerId => {
      clearTimeout(timerId);
      clearInterval(timerId);
    });
    this.timers.clear();

    // 清理事件监听器
    this.listeners.forEach((handlers, event) => {
      handlers.forEach(handler => {
        // 根据实际情况清理事件监听器
        if (typeof wx.offListener === 'function') {
          wx.offListener(event, handler);
        }
      });
    });
    this.listeners.clear();

    // 清理观察者
    this.observers.forEach(observer => {
      if (observer && typeof observer.disconnect === 'function') {
        observer.disconnect();
      }
    });
    this.observers.clear();
  }
}

// 创建全局实例
const lazyLoadManager = new LazyLoadManager();
const dataCacheManager = new DataCacheManager();
const renderOptimizer = new RenderOptimizer();
const memoryManager = new MemoryManager();

// 页面混入对象
const performanceMixin = {
  data: {
    _isPageDestroyed: false
  },

  onLoad() {
    this._memoryManager = new MemoryManager();
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[Performance] 页面性能监控启动'); } catch(_) {}
  },

  onUnload() {
    this.data._isPageDestroyed = true;
    if (this._memoryManager) {
      this._memoryManager.cleanup();
    }
    try { const logger = require('./logger.js'); logger.debug && logger.debug('[Performance] 页面资源清理完成'); } catch(_) {}
  },

  /**
   * 安全的setData，避免页面销毁后更新
   */
  safeSetData(data, callback) {
    if (this.data._isPageDestroyed) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('[Performance] 页面已销毁，跳过setData'); } catch(_) {}
      return;
    }
    this.setData(data, callback);
  },

  /**
   * 批量更新数据
   */
  batchSetData(data, callback) {
    renderOptimizer.batchUpdate(this, data, callback);
  }
};

module.exports = {
  debounce,
  throttle,
  LazyLoadManager,
  DataCacheManager,
  RenderOptimizer,
  MemoryManager,
  lazyLoadManager,
  dataCacheManager,
  renderOptimizer,
  memoryManager,
  performanceMixin
};