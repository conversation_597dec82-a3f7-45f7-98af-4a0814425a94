<!-- 侧边栏 -->
<div class="col-md-2 sidebar p-0">
    <div class="p-4">
        <h4 class="text-white">
            <i class="bi bi-egg-fill me-2"></i>智慧养鹅
        </h4>
        <p class="text-muted small">SaaS管理平台</p>
    </div>
    
    <ul class="nav flex-column px-2">
        <!-- 核心管理 -->
        <li class="nav-item mb-2">
            <div class="nav-header text-muted small mb-2 px-3">核心管理</div>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/dashboard">
                <i class="bi bi-speedometer2 me-2"></i>仪表板
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/tenants">
                <i class="bi bi-building me-2"></i>租户管理
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/cross-tenant-data">
                <i class="bi bi-diagram-3 me-2"></i>数据监控
                <span class="badge bg-primary ms-2">NEW</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/users">
                <i class="bi bi-people me-2"></i>平台用户
            </a>
        </li>
        
        <!-- 订阅管理 -->
        <li class="nav-item mb-2 mt-4">
            <div class="nav-header text-muted small mb-2 px-3">订阅管理</div>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/plans">
                <i class="bi bi-card-checklist me-2"></i>订阅计划
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/pricing">
                <i class="bi bi-currency-dollar me-2"></i>价格管理
            </a>
        </li>
        
        <!-- 系统配置 -->
        <li class="nav-item mb-2 mt-4">
            <div class="nav-header text-muted small mb-2 px-3">系统配置</div>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/ai-config">
                <i class="bi bi-robot me-2"></i>AI配置
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/knowledge">
                <i class="bi bi-book me-2"></i>知识库
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/announcements">
                <i class="bi bi-megaphone me-2"></i>公告管理
            </a>
        </li>
        
        <!-- 系统监控 -->
        <li class="nav-item mb-2 mt-4">
            <div class="nav-header text-muted small mb-2 px-3">系统监控</div>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/monitoring">
                <i class="bi bi-activity me-2"></i>性能监控
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="/saas-admin/logs">
                <i class="bi bi-file-text me-2"></i>系统日志
            </a>
        </li>
    </ul>
    
    <div class="px-3 py-4 mt-auto">
        <div class="d-flex align-items-center">
            <div class="avatar me-3">A</div>
            <div>
                <div class="text-white"><%= typeof adminName !== 'undefined' ? adminName : '管理员' %></div>
                <div class="text-muted small">超级管理员</div>
            </div>
        </div>
        <div class="mt-3">
            <a href="/saas-admin/logout" class="btn btn-outline-light w-100">
                <i class="bi bi-box-arrow-right me-1"></i>退出登录
            </a>
        </div>
    </div>
</div>

<style>
    .sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
    }
    
    .nav-link {
        color: rgba(255,255,255,0.7);
        transition: all 0.3s;
        border-radius: 8px;
        margin: 2px 0;
    }
    
    .nav-link:hover, .nav-link.active {
        color: white;
        background-color: rgba(255,255,255,0.1);
    }
    
    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #28a745;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }
    
    .nav-header {
        font-weight: 600;
        color: rgba(255,255,255,0.8) !important;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        padding-bottom: 5px;
    }
</style>