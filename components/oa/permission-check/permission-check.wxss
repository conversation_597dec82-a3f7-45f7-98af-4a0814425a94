/* components/oa/permission-check/permission-check.wxss */

.permission-granted {
  width: 100%;
}

.permission-denied {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.permission-denied .denied-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.permission-denied .denied-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}