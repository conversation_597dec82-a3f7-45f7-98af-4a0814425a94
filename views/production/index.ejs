<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总记录数</h6>
                        <h3 class="mb-0"><%= total %></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard-data fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">今日产蛋</h6>
                        <h3 class="mb-0">
                            <%= records.filter(r => moment(r.createdAt).format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')).reduce((sum, r) => sum + (r.产蛋数量 || 0), 0) %>
                        </h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-egg fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">平均温度</h6>
                        <h3 class="mb-0">
                            <%= records.length > 0 ? (records.reduce((sum, r) => sum + (r.温度 || 0), 0) / records.length).toFixed(1) : 0 %>°C
                        </h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-thermometer-half fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">平均湿度</h6>
                        <h3 class="mb-0">
                            <%= records.length > 0 ? (records.reduce((sum, r) => sum + (r.湿度 || 0), 0) / records.length).toFixed(1) : 0 %>%
                        </h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-droplet fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        <a href="/production/create" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>添加生产记录
        </a>
        <button type="button" class="btn btn-success" onclick="exportData()">
            <i class="bi bi-download me-2"></i>导出数据
        </button>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="搜索记录..." id="searchInput">
            <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- 错误提示 -->
<% if (error) { %>
<div class="alert alert-warning" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    <%= error %>
</div>
<% } %>

<!-- 生产记录列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-clipboard-data me-2"></i>生产记录列表
        </h5>
    </div>
    <div class="card-body">
        <% if (records.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>记录日期</th>
                        <th>产蛋数量</th>
                        <th>饲料消耗(kg)</th>
                        <th>温度(°C)</th>
                        <th>湿度(%)</th>
                        <th>备注</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <% records.forEach(function(record) { %>
                    <tr>
                        <td>
                            <strong><%= moment(record.记录日期 || record.createdAt).format('YYYY-MM-DD') %></strong>
                        </td>
                        <td>
                            <span class="badge bg-success fs-6">
                                <i class="bi bi-egg me-1"></i><%= record.产蛋数量 || 0 %>
                            </span>
                        </td>
                        <td><%= record.饲料消耗 || 0 %></td>
                        <td>
                            <% const temp = record.温度 || 0; %>
                            <span class="badge <%= temp > 30 ? 'bg-danger' : temp > 25 ? 'bg-warning' : 'bg-info' %>">
                                <%= temp %>°C
                            </span>
                        </td>
                        <td>
                            <% const humidity = record.湿度 || 0; %>
                            <span class="badge <%= humidity > 70 ? 'bg-danger' : humidity > 60 ? 'bg-warning' : 'bg-info' %>">
                                <%= humidity %>%
                            </span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <%= record.备注 ? (record.备注.length > 20 ? record.备注.substring(0, 20) + '...' : record.备注) : '-' %>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <%= moment(record.createdAt).format('MM-DD HH:mm') %>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="/production/edit/<%= record.id %>" class="btn btn-outline-primary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" title="删除" 
                                        onclick="deleteRecord(<%= record.id %>)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
        <% } else { %>
        <div class="text-center py-5">
            <i class="bi bi-clipboard-data" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">暂无生产记录</h5>
            <p class="text-muted">点击上方按钮添加第一条生产记录</p>
        </div>
        <% } %>
    </div>
</div>

<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// 删除记录
function deleteRecord(recordId) {
    if (confirm('确定要删除这条生产记录吗？此操作不可恢复。')) {
        fetch(`/api/production/${recordId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage('生产记录删除成功', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showMessage(result.message || '删除失败', 'danger');
            }
        })
        .catch(error => {
            console.error('删除记录错误:', error);
            showMessage('删除记录失败', 'danger');
        });
    }
}

// 导出数据
function exportData() {
    showMessage('导出功能开发中...', 'info');
}
</script>
