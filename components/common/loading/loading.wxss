/* 通用加载组件样式 */

:host {
  --c-loading-primary-color: #2E8B57;
  --c-loading-text-color: #666666;
  --c-loading-skeleton-bg: #F3F4F6;
  --c-loading-skeleton-shine: #E5E7EB;
  
  --c-loading-size-small: 32rpx;
  --c-loading-size-medium: 40rpx;
  --c-loading-size-large: 48rpx;
  
  --c-loading-text-size-small: 24rpx;
  --c-loading-text-size-medium: 26rpx;
  --c-loading-text-size-large: 28rpx;
}

/* 基础加载样式 */
.c-loading {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 居中样式 */
.c-loading--center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 遮罩样式 */
.c-loading--overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.8);
}

/* ===== 加载内容容器 ===== */
.c-loading__content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

/* 文本位置布局 */
.c-loading--text-right .c-loading__content {
  flex-direction: row;
}

.c-loading--text-left .c-loading__content {
  flex-direction: row-reverse;
}

.c-loading--text-top .c-loading__content {
  flex-direction: column-reverse;
}

.c-loading--text-bottom .c-loading__content {
  flex-direction: column;
}

/* ===== 旋转加载器 ===== */
.c-loading__spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.c-loading__spinner-icon {
  width: var(--c-loading-size-medium);
  height: var(--c-loading-size-medium);
  animation: c-loading-spin 1s linear infinite;
}

.c-loading--small .c-loading__spinner-icon {
  width: var(--c-loading-size-small);
  height: var(--c-loading-size-small);
}

.c-loading--large .c-loading__spinner-icon {
  width: var(--c-loading-size-large);
  height: var(--c-loading-size-large);
}

/* ===== 点状加载器 ===== */
.c-loading__dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.c-loading__dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: currentColor;
  animation: c-loading-dots 1.4s ease-in-out infinite both;
}

.c-loading__dot:nth-child(1) {
  animation-delay: -0.32s;
}

.c-loading__dot:nth-child(2) {
  animation-delay: -0.16s;
}

.c-loading--small .c-loading__dot {
  width: 8rpx;
  height: 8rpx;
}

.c-loading--large .c-loading__dot {
  width: 16rpx;
  height: 16rpx;
}

/* ===== 条状加载器 ===== */
.c-loading__bars {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.c-loading__bar {
  width: 6rpx;
  height: 32rpx;
  background-color: currentColor;
  border-radius: 3rpx;
  animation: c-loading-bars 1.2s ease-in-out infinite;
}

.c-loading__bar:nth-child(1) {
  animation-delay: -1.1s;
}

.c-loading__bar:nth-child(2) {
  animation-delay: -1.0s;
}

.c-loading__bar:nth-child(3) {
  animation-delay: -0.9s;
}

.c-loading__bar:nth-child(4) {
  animation-delay: -0.8s;
}

.c-loading--small .c-loading__bar {
  width: 4rpx;
  height: 24rpx;
}

.c-loading--large .c-loading__bar {
  width: 8rpx;
  height: 40rpx;
}

/* ===== 骨架屏 ===== */
.c-loading__skeleton {
  display: flex;
  align-items: flex-start;
  width: 100%;
  padding: 20rpx;
}

.c-loading__skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, var(--c-loading-skeleton-bg) 25%, var(--c-loading-skeleton-shine) 50%, var(--c-loading-skeleton-bg) 75%);
  background-size: 400% 100%;
  animation: c-loading-skeleton 1.5s ease-in-out infinite;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.c-loading__skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.c-loading__skeleton-line {
  height: 24rpx;
  border-radius: 12rpx;
  background: linear-gradient(90deg, var(--c-loading-skeleton-bg) 25%, var(--c-loading-skeleton-shine) 50%, var(--c-loading-skeleton-bg) 75%);
  background-size: 400% 100%;
  animation: c-loading-skeleton 1.5s ease-in-out infinite;
}

.c-loading--small .c-loading__skeleton-avatar {
  width: 60rpx;
  height: 60rpx;
}

.c-loading--small .c-loading__skeleton-line {
  height: 20rpx;
}

.c-loading--large .c-loading__skeleton-avatar {
  width: 100rpx;
  height: 100rpx;
}

.c-loading--large .c-loading__skeleton-line {
  height: 28rpx;
}

/* ===== 加载文本 ===== */
.c-loading__text {
  color: var(--c-loading-text-color);
  font-size: var(--c-loading-text-size-medium);
  line-height: 1.5;
}

.c-loading--small .c-loading__text {
  font-size: var(--c-loading-text-size-small);
}

.c-loading--large .c-loading__text {
  font-size: var(--c-loading-text-size-large);
}

/* 文本间距 */
.c-loading--text-top .c-loading__text,
.c-loading--text-bottom .c-loading__text {
  margin: 16rpx 0;
}

.c-loading--text-left .c-loading__text,
.c-loading--text-right .c-loading__text {
  margin: 0 16rpx;
}

/* ===== 自定义加载器 ===== */
.c-loading__custom {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== 动画定义 ===== */

/* 旋转动画 */
@keyframes c-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 点状动画 */
@keyframes c-loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 条状动画 */
@keyframes c-loading-bars {
  0%, 40%, 100% {
    transform: scaleY(0.4);
    opacity: 0.5;
  }
  20% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* 骨架屏动画 */
@keyframes c-loading-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== 响应式设计 ===== */

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  :host {
    --c-loading-text-color: #D1D5DB;
    --c-loading-skeleton-bg: #374151;
    --c-loading-skeleton-shine: #4B5563;
  }
  
  .c-loading--overlay {
    background-color: rgba(0, 0, 0, 0.8);
  }
}