// utils/validator.js
// 前端输入验证工具

/**
 * 验证规则配置
 */
const VALIDATION_RULES = {
  // 用户相关验证规则
  user: {
    username: {
      required: true,
      minLength: 3,
      maxLength: 30,
      pattern: /^[a-zA-Z0-9]+$/,
      message: '用户名只能包含字母和数字，长度3-30个字符'
    },
    password: {
      required: true,
      minLength: 6,
      maxLength: 128,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      message: '密码至少6个字符，必须包含大小写字母和数字'
    },
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入有效的邮箱地址'
    },
    phone: {
      required: false,
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号码'
    },
    name: {
      required: false,
      minLength: 2,
      maxLength: 50,
      message: '姓名长度2-50个字符'
    },
    farmName: {
      required: false,
      minLength: 2,
      maxLength: 100,
      message: '农场名称长度2-100个字符'
    }
  },

  // 健康记录相关验证规则
  healthRecord: {
    gooseId: {
      required: true,
      minLength: 1,
      maxLength: 50,
      message: '鹅群编号不能为空，最多50个字符'
    },
    healthStatus: {
      required: true,
      enum: ['healthy', 'sick', 'recovering', 'dead'],
      message: '请选择有效的健康状态'
    },
    symptoms: {
      required: false,
      maxLength: 1000,
      message: '症状描述最多1000个字符'
    },
    diagnosis: {
      required: false,
      maxLength: 1000,
      message: '诊断结果最多1000个字符'
    },
    treatment: {
      required: false,
      maxLength: 1000,
      message: '治疗方案最多1000个字符'
    }
  },

  // 生产记录相关验证规则
  productionRecord: {
    eggCount: {
      required: false,
      type: 'number',
      min: 0,
      max: 10000,
      message: '产蛋数量必须是0-10000之间的整数'
    },
    feedConsumption: {
      required: false,
      type: 'number',
      min: 0,
      max: 999999.99,
      precision: 2,
      message: '饲料消耗必须是0-999999.99之间的数值'
    },
    temperature: {
      required: false,
      type: 'number',
      min: -50,
      max: 100,
      precision: 2,
      message: '温度必须是-50°C到100°C之间的数值'
    },
    humidity: {
      required: false,
      type: 'number',
      min: 0,
      max: 100,
      precision: 2,
      message: '湿度必须是0%-100%之间的数值'
    },
    recordedDate: {
      required: true,
      type: 'date',
      maxDate: new Date(),
      message: '记录日期不能是未来时间'
    },
    batchNumber: {
      required: false,
      minLength: 1,
      maxLength: 50,
      message: '批次号最多50个字符'
    }
  }
};

/**
 * 验证器类
 */
class Validator {
  /**
   * 验证单个字段
   * @param {any} value - 要验证的值
   * @param {Object} rule - 验证规则
   * @param {string} fieldName - 字段名称
   * @returns {Object} 验证结果 {valid: boolean, message: string}
   */
  static validateField(value, rule, fieldName) {
    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      return {
        valid: false,
        message: `${fieldName}为必填项`
      };
    }

    // 如果不是必填且值为空，则跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return { valid: true };
    }

    // 类型验证
    if (rule.type) {
      if (rule.type === 'number') {
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return {
            valid: false,
            message: `${fieldName}必须是数字`
          };
        }
        value = numValue;
      } else if (rule.type === 'date') {
        const dateValue = new Date(value);
        if (isNaN(dateValue.getTime())) {
          return {
            valid: false,
            message: `${fieldName}日期格式不正确`
          };
        }
        value = dateValue;
      }
    }

    // 长度验证
    if (rule.minLength && value.toString().length < rule.minLength) {
      return {
        valid: false,
        message: rule.message || `${fieldName}长度不能少于${rule.minLength}个字符`
      };
    }

    if (rule.maxLength && value.toString().length > rule.maxLength) {
      return {
        valid: false,
        message: rule.message || `${fieldName}长度不能超过${rule.maxLength}个字符`
      };
    }

    // 数值范围验证
    if (rule.type === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        return {
          valid: false,
          message: rule.message || `${fieldName}不能小于${rule.min}`
        };
      }

      if (rule.max !== undefined && value > rule.max) {
        return {
          valid: false,
          message: rule.message || `${fieldName}不能大于${rule.max}`
        };
      }

      // 精度验证
      if (rule.precision !== undefined) {
        const decimalPlaces = (value.toString().split('.')[1] || '').length;
        if (decimalPlaces > rule.precision) {
          return {
            valid: false,
            message: rule.message || `${fieldName}最多保留${rule.precision}位小数`
          };
        }
      }
    }

    // 日期范围验证
    if (rule.type === 'date') {
      if (rule.maxDate && value > rule.maxDate) {
        return {
          valid: false,
          message: rule.message || `${fieldName}不能是未来时间`
        };
      }

      if (rule.minDate && value < rule.minDate) {
        return {
          valid: false,
          message: rule.message || `${fieldName}不能早于${rule.minDate.toLocaleDateString()}`
        };
      }
    }

    // 正则表达式验证
    if (rule.pattern && !rule.pattern.test(value.toString())) {
      return {
        valid: false,
        message: rule.message || `${fieldName}格式不正确`
      };
    }

    // 枚举值验证
    if (rule.enum && !rule.enum.includes(value)) {
      return {
        valid: false,
        message: rule.message || `${fieldName}必须是${rule.enum.join('、')}之一`
      };
    }

    return { valid: true };
  }

  /**
   * 验证对象
   * @param {Object} data - 要验证的数据对象
   * @param {string} type - 验证类型 (user, healthRecord, productionRecord)
   * @returns {Object} 验证结果 {valid: boolean, errors: Array}
   */
  static validateObject(data, type) {
    const rules = VALIDATION_RULES[type];
    if (!rules) {
      try { const logger = require('./logger.js'); logger.warn && logger.warn('未找到验证规则', type); } catch(_) {}
      return { valid: true, errors: [] };
    }

    const errors = [];

    for (const [fieldName, rule] of Object.entries(rules)) {
      const value = data[fieldName];
      const result = this.validateField(value, rule, fieldName);
      
      if (!result.valid) {
        errors.push({
          field: fieldName,
          message: result.message,
          value: value
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 验证表单数据
   * @param {Object} formData - 表单数据
   * @param {string} type - 验证类型
   * @returns {Promise} 验证结果
   */
  static async validateForm(formData, type) {
    return new Promise((resolve) => {
      const result = this.validateObject(formData, type);
      resolve(result);
    });
  }

  /**
   * 显示验证错误
   * @param {Array} errors - 错误数组
   * @param {Function} showToast - 显示提示的函数
   */
  static showValidationErrors(errors, showToast) {
    if (errors && errors.length > 0) {
      const message = errors.map(error => error.message).join('\n');
      if (showToast) {
        showToast(message);
      } else {
        try { const logger = require('./logger.js'); logger.error && logger.error('验证错误', message); } catch(_) {}
      }
    }
  }
}

/**
 * 便捷验证函数
 */
const validateUser = (data) => Validator.validateObject(data, 'user');
const validateHealthRecord = (data) => Validator.validateObject(data, 'healthRecord');
const validateProductionRecord = (data) => Validator.validateObject(data, 'productionRecord');

module.exports = {
  Validator,
  validateUser,
  validateHealthRecord,
  validateProductionRecord,
  VALIDATION_RULES
};
