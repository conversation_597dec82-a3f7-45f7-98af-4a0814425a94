/* components/weather/weather.wxss */
.weather-container {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  color: white;
  margin-bottom: 20rpx;
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.location {
  display: flex;
  align-items: center;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
}

.location-text {
  font-size: 26rpx;
  opacity: 0.9;
}

.update-time {
  font-size: 22rpx;
  opacity: 0.8;
}

.current-weather {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.weather-main {
  flex: 1;
}

.temperature {
  font-size: 80rpx;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 10rpx;
}

.weather-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

.weather-icon {
  width: 120rpx;
  height: 120rpx;
}

.weather-image {
  width: 100%;
  height: 100%;
}

.weather-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.detail-item {
  text-align: center;
  flex: 1;
}

.detail-label {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.detail-value {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
}

.detail-value.good {
  color: #00b894;
}

.detail-value.moderate {
  color: #fdcb6e;
}

.detail-value.poor {
  color: #e84393;
}

.forecast {
  margin-top: 20rpx;
}

.forecast-title {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 20rpx;
}

.forecast-list {
  display: flex;
  justify-content: space-between;
}

.forecast-item {
  text-align: center;
  flex: 1;
}

.forecast-date {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.forecast-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

.forecast-temp {
  display: block;
  font-size: 22rpx;
  opacity: 0.9;
}
