/**
 * AI服务配置管理
 * 支持硅基流动和智谱AI两个服务商
 */

const { getBackendApiKey } = require('./backend-ai-config.js');

// AI服务商配置
const AI_PROVIDERS = {
  // 硅基流动配置 - 使用免费模型
  SILICONFLOW: {
    name: 'SiliconFlow',
    baseUrl: 'https://api.siliconflow.cn/v1',
    apiKey: 'sk-wtaexxdipxltezdcbdemlovvjutwjdtmauymcreplyctkdkj',
    models: {
      chat: 'Qwen/Qwen2.5-7B-Instruct',           // 免费模型
      vision: 'Qwen/Qwen2.5-VL-7B-Instruct',     // 免费视觉模型
      reasoning: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', // 免费推理模型
      embedding: 'BAAI/bge-large-zh-v1.5'        // 免费嵌入模型
    },
    maxTokens: 4096,
    temperature: 0.7,
    supportedFeatures: ['chat', 'vision', 'reasoning', 'embedding']
  },

  // 智谱AI配置 - 使用免费模型
  ZHIPU: {
    name: 'ZhipuAI',
    baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
    apiKey: 'e4be2d0eb8f94154859c17a9143dd065.sgaX2314HGYgXRYV',
    models: {
      chat: 'glm-4-flash',                        // 免费模型
      vision: 'glm-4v-flash',                     // 免费视觉模型
      embedding: 'embedding-2'                    // 嵌入模型
    },
    maxTokens: 4096,
    temperature: 0.7,
    supportedFeatures: ['chat', 'vision', 'embedding']
  }
};

// 默认使用的AI服务商
const DEFAULT_PROVIDER = 'SILICONFLOW';

// 应用场景配置
const AI_SCENARIOS = {
  // 健康管理 - AI疾病诊断
  HEALTH_DIAGNOSIS: {
    provider: 'SILICONFLOW',
    model: 'chat',
    systemPrompt: `你是一位专业的鹅类养殖兽医专家，具有丰富的禽类疾病诊断经验。
请根据用户提供的症状描述，进行初步的疾病诊断分析。

诊断要求：
1. 仔细分析症状，考虑常见的鹅类疾病
2. 提供可能的疾病名称和概率
3. 给出详细的症状分析
4. 提供治疗建议和预防措施
5. 强调及时就医的重要性

回答格式：
- 可能疾病：[疾病名称] (概率：XX%)
- 症状分析：[详细分析]
- 治疗建议：[具体建议]
- 预防措施：[预防方法]
- 注意事项：[重要提醒]

请用专业但易懂的语言回答，确保养殖户能够理解。`,
    maxTokens: 2048,
    temperature: 0.3
  },

  // 知识库 - 智能问答
  KNOWLEDGE_QA: {
    provider: 'ZHIPU',
    model: 'chat',
    systemPrompt: `你是智慧养鹅系统的知识库助手，专门回答鹅类养殖相关问题。

知识领域包括：
1. 鹅类品种特性和选择
2. 饲养管理技术
3. 疾病防治知识
4. 营养配方和饲料
5. 繁殖育种技术
6. 环境控制要求
7. 市场行情分析

回答要求：
- 准确专业，基于科学依据
- 语言通俗易懂，适合养殖户理解
- 提供具体可操作的建议
- 必要时提供相关数据和标准
- 强调安全和规范操作

如果问题超出鹅类养殖范围，请礼貌地引导用户提问相关问题。`,
    maxTokens: 1024,
    temperature: 0.5
  },

  // 财务管理 - 数据分析
  FINANCE_ANALYSIS: {
    provider: 'SILICONFLOW',
    model: 'chat',
    systemPrompt: `你是一位专业的养殖业财务分析师，擅长分析养鹅场的经营数据。

分析能力：
1. 成本结构分析
2. 收益趋势预测
3. 投资回报评估
4. 风险识别预警
5. 经营建议制定

分析要求：
- 基于提供的财务数据进行客观分析
- 识别关键财务指标和趋势
- 提供具体的改进建议
- 预测未来发展趋势
- 给出风险提示和应对策略

回答格式：
- 数据概况：[总体情况]
- 关键指标：[重要指标分析]
- 趋势分析：[发展趋势]
- 改进建议：[具体建议]
- 风险提示：[潜在风险]`,
    maxTokens: 2048,
    temperature: 0.4
  },

  // 图像识别 - 疾病识别
  IMAGE_RECOGNITION: {
    provider: 'ZHIPU',
    model: 'vision',
    systemPrompt: `你是专业的鹅类疾病图像识别专家，能够通过图片识别鹅的健康状况。

识别能力：
1. 外观异常检测
2. 行为状态分析
3. 环境条件评估
4. 疾病症状识别

分析要求：
- 仔细观察图片中的细节
- 识别异常症状和体征
- 评估健康状况
- 提供初步诊断建议
- 给出处理建议

回答格式：
- 观察结果：[图片内容描述]
- 健康评估：[健康状况判断]
- 异常发现：[发现的问题]
- 初步诊断：[可能的疾病]
- 处理建议：[应对措施]`,
    maxTokens: 1024,
    temperature: 0.2
  },

  // 内容推荐
  CONTENT_RECOMMENDATION: {
    provider: 'ZHIPU',
    model: 'chat',
    systemPrompt: `你是智慧养鹅系统的内容推荐专家，根据用户的兴趣和需求推荐相关内容。

推荐类型：
1. 养殖技术文章
2. 疾病防治指南
3. 市场行情分析
4. 成功案例分享
5. 政策法规更新

推荐原则：
- 基于用户历史行为和偏好
- 考虑当前季节和市场情况
- 优先推荐实用性强的内容
- 保持内容的时效性和准确性

回答格式：
- 推荐理由：[为什么推荐]
- 内容概要：[主要内容]
- 适用场景：[适合什么情况]
- 预期收益：[学习后的好处]`,
    maxTokens: 1024,
    temperature: 0.6
  },

  // AI智能盘点 - 鹅群数量识别
  AI_INVENTORY_COUNTING: {
    provider: 'ZHIPU',
    model: 'vision',
    systemPrompt: `你是专业的鹅群数量识别专家，能够通过图像精确统计鹅的数量。

识别要求：
1. 仔细观察图片中的每一只鹅，包括部分遮挡的鹅
2. 区分鹅和其他动物（如鸭子、鸡等）
3. 只统计活体鹅，不包括雕像、玩具等
4. 如果图片模糊或光线不足，请在置信度中体现
5. 对于重叠或遮挡的鹅，根据可见部分进行合理推断

识别步骤：
1. 整体扫描图片，识别所有可能的鹅
2. 逐个确认每只鹅的特征（头部、身体、脚等）
3. 排除非鹅类动物和非活体对象
4. 统计最终数量并评估置信度

请严格按照以下JSON格式返回结果：
{
  "count": 数量（整数），
  "confidence": 置信度（0-100的整数），
  "details": "详细描述识别过程，包括如何区分每只鹅",
  "warnings": "如果有识别困难或不确定的地方，请详细说明",
  "analysis": {
    "total_animals": "图片中动物总数",
    "confirmed_geese": "确认的鹅数量",
    "uncertain_objects": "不确定的对象数量",
    "image_quality": "图片质量评估（清晰/模糊/光线不足等）"
  }
}

注意事项：
- 如果无法清楚识别，置信度应该较低（<60%）
- 如果图片中没有鹅，count应为0
- 请确保返回的是有效的JSON格式
- 置信度计算要考虑图片质量、遮挡程度、识别难度等因素`,
    maxTokens: 1024,
    temperature: 0.1
  }
};

// 获取AI服务商配置
function getProviderConfig(providerName = DEFAULT_PROVIDER) {
  return AI_PROVIDERS[providerName] || AI_PROVIDERS[DEFAULT_PROVIDER];
}

// 获取场景配置
function getScenarioConfig(scenarioName) {
  return AI_SCENARIOS[scenarioName];
}

// 获取完整的AI配置
async function getAIConfig(scenarioName) {
  const scenario = getScenarioConfig(scenarioName);
  if (!scenario) {
    throw new Error(`未找到场景配置: ${scenarioName}`);
  }

  const provider = getProviderConfig(scenario.provider);

  // 获取后台配置的API密钥，如果没有则使用默认密钥
  let apiKey = provider.apiKey;
  try {
    const backendApiKey = await getBackendApiKey(scenario.provider);
    if (backendApiKey) {
      apiKey = backendApiKey;
    }
  } catch (error) {
    try { const logger = require('./logger.js'); logger.warn && logger.warn('获取后台API密钥失败，使用默认配置', error); } catch(_) {}
  }

  return {
    provider: provider,
    scenario: scenario,
    model: provider.models[scenario.model],
    apiKey: apiKey,
    baseUrl: provider.baseUrl,
    systemPrompt: scenario.systemPrompt,
    maxTokens: scenario.maxTokens || provider.maxTokens,
    temperature: scenario.temperature || provider.temperature
  };
}



// 验证API密钥格式
function validateApiKey(provider, apiKey) {
  if (!apiKey) return false;
  
  switch (provider) {
    case 'SILICONFLOW':
      return apiKey.startsWith('sk-');
    case 'ZHIPU':
      return apiKey.includes('.');
    default:
      return false;
  }
}

// 获取所有可用的场景
function getAvailableScenarios() {
  return Object.keys(AI_SCENARIOS);
}

// 获取所有可用的服务商
function getAvailableProviders() {
  return Object.keys(AI_PROVIDERS);
}

module.exports = {
  AI_PROVIDERS,
  AI_SCENARIOS,
  DEFAULT_PROVIDER,
  getProviderConfig,
  getScenarioConfig,
  getAIConfig,
  validateApiKey,
  getAvailableScenarios,
  getAvailableProviders
};
