@import '/styles/oa-common.wxss';
/* pages/oa/oa.wxss */

.oa-container {
  padding: var(--space-lg);
  background-color: var(--bg-secondary);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl) var(--space-xl);
  margin-bottom: var(--space-xl);
  color: var(--text-inverse);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-primary);
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-full);
}

.page-title {
  display: block;
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-tight);
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  line-height: var(--leading-normal);
}

/* 统计概览 */
.stats-section {
  margin-bottom: var(--space-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl) var(--space-lg);
  display: flex;
  align-items: center;
  position: relative;
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: var(--space-lg);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-card.approvals .stat-icon {
  background: var(--permission-admin-bg);
  color: var(--permission-admin);
}

.stat-card.tasks .stat-icon {
  background: var(--permission-user-bg);
  color: var(--permission-user);
}

.stat-card.expense .stat-icon {
  background: var(--accent-subtle);
  color: var(--accent);
}

.stat-card.notices .stat-icon {
  background: var(--success-bg);
  color: var(--success);
}

.stat-icon image {
  width: 32rpx;
  height: 32rpx;
}

.stat-content {
  flex: 1;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  line-height: var(--leading-none);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.stat-badge {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: var(--error);
  color: var(--text-inverse);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  min-width: 32rpx;
  text-align: center;
  box-shadow: var(--shadow-sm);
}

/* 功能模块 */
.modules-section, .quick-actions-section, .activities-section {
  margin-bottom: var(--space-xl);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.section-title::before {
  content: '';
  width: 4rpx;
  height: 24rpx;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-full);
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.module-item {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.module-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, var(--primary) 0%, var(--secondary) 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.module-item:active {
  transform: translateX(8rpx) scale(0.98);
  box-shadow: var(--shadow-lg);
}

.module-item:active::before {
  transform: scaleY(1);
}

.module-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  position: relative;
}

.module-icon image {
  width: 100%;
  height: 100%;
}

.module-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #FF4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

.module-content {
  flex: 1;
}

.module-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.module-description {
  font-size: 24rpx;
  color: #666;
}

.module-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.module-arrow image {
  width: 100%;
  height: 100%;
}

/* 快捷操作 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.quick-action-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.quick-action-item:active {
  transform: scale(0.98);
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto 16rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon image {
  width: 32rpx;
  height: 32rpx;
}

.action-title {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 最近动态 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.view-all-btn {
  font-size: 26rpx;
  color: #0066CC;
}

.activities-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.activity-item {
  padding: 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:active {
  background-color: #f8f9fa;
}

.activity-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon.approval {
  background: rgba(155, 89, 182, 0.1);
}

.activity-icon.reimbursement {
  background: rgba(255, 107, 53, 0.1);
}

.activity-icon.purchase {
  background: rgba(46, 204, 113, 0.1);
}

.activity-icon image {
  width: 28rpx;
  height: 28rpx;
}

.activity-content {
  flex: 1;
}

.activity-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.activity-description {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

.activity-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.activity-status.approved {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.activity-status.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #f39c12;
}

.activity-status.submitted {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  min-width: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}