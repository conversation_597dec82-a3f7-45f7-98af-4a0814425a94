/**
 * 通用空状态组件
 * 用于显示无数据、网络错误、搜索无结果等状态
 */
const { UI, IMAGES } = require('../../../constants/index.js');

Component({
  options: {
    virtualHost: true
  },

  properties: {
    // 空状态类型
    type: {
      type: String,
      value: 'empty' // empty, error, network, search, loading, maintenance
    },
    
    // 自定义图标
    icon: {
      type: String,
      value: ''
    },
    
    // 主标题
    title: {
      type: String,
      value: ''
    },
    
    // 描述文本
    description: {
      type: String,
      value: ''
    },
    
    // 按钮文本
    buttonText: {
      type: String,
      value: ''
    },
    
    // 按钮类型
    buttonType: {
      type: String,
      value: 'primary' // 参考button组件的type
    },
    
    // 是否显示按钮
    showButton: {
      type: Boolean,
      value: true
    },
    
    // 图标尺寸
    iconSize: {
      type: String,
      value: 'large' // small, medium, large, xlarge
    },
    
    // 垂直对齐方式
    verticalAlign: {
      type: String,
      value: 'center' // top, center, bottom
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    // UI常量
    colors: UI.COLORS,
    spacing: UI.SPACING,
    
    // 预设图标
    icons: IMAGES.EMPTY_STATES,
    
    // 预设配置
    presets: {
      empty: {
        icon: IMAGES.EMPTY_STATES.NO_DATA,
        title: '暂无数据',
        description: '当前没有任何内容',
        buttonText: '重新加载'
      },
      error: {
        icon: IMAGES.EMPTY_STATES.NO_NETWORK,
        title: '出错了',
        description: '页面出现了一些问题，请稍后重试',
        buttonText: '重新加载'
      },
      network: {
        icon: IMAGES.EMPTY_STATES.NO_NETWORK,
        title: '网络连接失败',
        description: '请检查网络设置后重试',
        buttonText: '重新连接'
      },
      search: {
        icon: IMAGES.EMPTY_STATES.NO_SEARCH_RESULT,
        title: '搜索无结果',
        description: '换个关键词试试看',
        buttonText: '重新搜索'
      },
      loading: {
        icon: IMAGES.ICONS.LOADING,
        title: '加载中...',
        description: '请稍候',
        buttonText: ''
      },
      maintenance: {
        icon: IMAGES.EMPTY_STATES.UNDER_CONSTRUCTION,
        title: '功能维护中',
        description: '该功能正在维护，请稍后访问',
        buttonText: '返回首页'
      }
    }
  },

  computed: {
    // 当前配置
    currentConfig() {
      const preset = this.data.presets[this.data.type] || this.data.presets.empty;
      
      return {
        icon: this.data.icon || preset.icon,
        title: this.data.title || preset.title,
        description: this.data.description || preset.description,
        buttonText: this.data.buttonText || preset.buttonText
      };
    },
    
    // 空状态样式类
    emptyClass() {
      const classes = ['c-empty-state'];
      
      // 垂直对齐
      classes.push(`c-empty-state--${this.data.verticalAlign}`);
      
      // 图标尺寸
      classes.push(`c-empty-state--icon-${this.data.iconSize}`);
      
      // 自定义类
      if (this.data.customClass) {
        classes.push(this.data.customClass);
      }
      
      return classes.join(' ');
    },
    
    // 图标样式
    iconStyle() {
      const config = this.currentConfig;
      const styles = [];
      
      // 加载状态的旋转动画
      if (this.data.type === 'loading') {
        styles.push('animation: c-empty-spin 1s linear infinite');
      }
      
      return styles.join('; ');
    }
  },

  methods: {
    /**
     * 按钮点击事件
     */
    onButtonTap() {
      this.triggerEvent('buttonclick', {
        type: this.data.type
      });
      
      // 触发对应类型的事件
      switch (this.data.type) {
        case 'empty':
          this.triggerEvent('reload');
          break;
        case 'error':
          this.triggerEvent('retry');
          break;
        case 'network':
          this.triggerEvent('reconnect');
          break;
        case 'search':
          this.triggerEvent('research');
          break;
        case 'maintenance':
          this.triggerEvent('gohome');
          break;
        default:
          this.triggerEvent('action');
          break;
      }
    },
    
    /**
     * 整个空状态区域点击事件
     */
    onEmptyTap() {
      this.triggerEvent('tap', {
        type: this.data.type
      });
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化
    }
  }
});