@import '/styles/oa-common.wxss';
/* pages/oa/finance/reports/reports.wxss */
.container {
  padding: 0;
  min-height: 100vh;
  background: #f5f5f5;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 报表类型选择 */
.report-types-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.report-types-scroll {
  width: 100%;
}

.report-types-list {
  display: flex;
  gap: 20rpx;
  padding: 10rpx 0;
}

.report-type-item {
  min-width: 200rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  background: #f9fafb;
  text-align: center;
  transition: all 0.3s ease;
}

.report-type-item.active {
  border-color: #667eea;
  background: #f0f4ff;
}

.type-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.type-name {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.type-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* 时间范围选择 */
.time-range-section {
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.time-range-picker {
  position: relative;
}

.selected-range {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: #f9fafb;
  font-size: 28rpx;
}

.arrow {
  transition: transform 0.3s ease;
  color: #666;
}

.arrow.up {
  transform: rotate(180deg);
}

.range-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
}

.range-options.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.range-option {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333;
}

.range-option:last-child {
  border-bottom: none;
}

.range-option.active {
  color: #667eea;
  background: #f0f4ff;
}

/* 自定义日期选择弹窗 */
.custom-date-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.custom-date-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.date-inputs {
  padding: 30rpx;
}

.date-input-item {
  margin-bottom: 30rpx;
}

.date-input-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.picker-input {
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: #f9fafb;
  font-size: 28rpx;
  color: #333;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  background: white;
  font-size: 28rpx;
  border-radius: 0;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #667eea;
  font-weight: bold;
}

/* 报表摘要 */
.summary-section {
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.summary-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.summary-card {
  padding: 24rpx;
  border-radius: 12rpx;
  text-align: center;
}

.summary-card.income {
  background: linear-gradient(135deg, #4ade80 0%, #16a34a 100%);
  color: white;
}

.summary-card.expense {
  background: linear-gradient(135deg, #f87171 0%, #dc2626 100%);
  color: white;
}

.summary-card.profit {
  background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%);
  color: white;
}

.summary-card.count {
  background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
  color: white;
}

.card-value {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.card-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 操作按钮区域 */
.actions-section {
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
  width: 100%;
  padding: 28rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.loading {
  opacity: 0.7;
}

.export-actions {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
}

.export-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.export-buttons {
  display: flex;
  gap: 16rpx;
}

.export-btn {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  background: white;
  font-size: 26rpx;
  color: #333;
}

.export-btn:active {
  background: #f0f4ff;
  border-color: #667eea;
  color: #667eea;
}

/* 报表数据展示 */
.report-data-section {
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 数据表格 */
.data-table,
.summary-table,
.category-table {
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e5e7eb;
}

.table-header {
  display: flex;
  background: #f9fafb;
  padding: 20rpx 0;
  font-weight: bold;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1rpx solid #e5e7eb;
}

.table-row {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 26rpx;
  align-items: center;
  transition: background 0.2s ease;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:active {
  background: #f9fafb;
}

/* 收支明细表列宽 */
.data-table .col-date {
  flex: 0 0 120rpx;
  text-align: center;
}

.data-table .col-type {
  flex: 0 0 100rpx;
  text-align: center;
}

.data-table .col-amount {
  flex: 0 0 140rpx;
  text-align: right;
  font-weight: bold;
}

.data-table .col-desc {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 20rpx;
}

.col-amount.income {
  color: #16a34a;
}

.col-amount.expense {
  color: #dc2626;
}

/* 月度汇总表列宽 */
.summary-table .col-month {
  flex: 0 0 120rpx;
  text-align: center;
}

.summary-table .col-income,
.summary-table .col-expense,
.summary-table .col-profit {
  flex: 1;
  text-align: right;
  font-weight: bold;
}

.col-profit.positive {
  color: #16a34a;
}

.col-profit.negative {
  color: #dc2626;
}

/* 分类分析表列宽 */
.category-table .col-category {
  flex: 1;
  text-align: left;
}

.category-table .col-amount {
  flex: 0 0 140rpx;
  text-align: right;
  font-weight: bold;
}

.category-table .col-percent {
  flex: 0 0 100rpx;
  text-align: right;
}

.category-table .col-count {
  flex: 0 0 80rpx;
  text-align: right;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  margin-bottom: 12rpx;
  color: #666;
}

.empty-desc {
  font-size: 26rpx;
  line-height: 1.4;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}