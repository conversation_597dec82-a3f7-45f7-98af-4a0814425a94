/**
 * 统一API接口规范定义
 * Unified API Endpoints Definition
 * 
 * 统一管理所有API接口地址和调用方法
 * 支持SAAS多租户、权限控制、版本管理
 */

const { PERMISSIONS } = require('./unified-permission.js');

/**
 * API版本定义
 */
const API_VERSIONS = {
  V1: '/api/v1',
  V2: '/api/v2',
  TENANT: '/api/v1/tenant',
  PLATFORM: '/api/v1/platform'
};

/**
 * API端点分类定义
 */
const API_ENDPOINTS = {
  // ================================
  // 认证相关接口
  // ================================
  AUTH: {
    base: '/auth',
    endpoints: {
      LOGIN: {
        path: '/login',
        method: 'POST',
        description: '用户登录',
        permissions: null, // 无需权限
        cache: false
      },
      REGISTER: {
        path: '/register', 
        method: 'POST',
        description: '用户注册',
        permissions: null,
        cache: false
      },
      LOGOUT: {
        path: '/logout',
        method: 'POST',
        description: '用户登出',
        permissions: null,
        cache: false
      },
      REFRESH_TOKEN: {
        path: '/refresh',
        method: 'POST',
        description: '刷新令牌',
        permissions: null,
        cache: false
      },
      USER_INFO: {
        path: '/userinfo',
        method: 'GET',
        description: '获取当前用户信息',
        permissions: null,
        cache: true,
        cacheTTL: 300000 // 5分钟
      },
      CHANGE_PASSWORD: {
        path: '/password',
        method: 'PUT',
        description: '修改密码',
        permissions: null,
        cache: false
      },
      RESET_PASSWORD: {
        path: '/reset-password',
        method: 'POST',
        description: '重置密码',
        permissions: null,
        cache: false
      }
    }
  },

  // ================================
  // 鹅群管理接口
  // ================================
  FLOCKS: {
    base: '/flocks',
    endpoints: {
      LIST: {
        path: '',
        method: 'GET',
        description: '获取鹅群列表',
        permissions: [PERMISSIONS.FLOCK_VIEW],
        cache: true,
        cacheTTL: 60000 // 1分钟
      },
      CREATE: {
        path: '',
        method: 'POST',
        description: '创建鹅群',
        permissions: [PERMISSIONS.FLOCK_CREATE],
        cache: false
      },
      DETAIL: {
        path: '/:id',
        method: 'GET',
        description: '获取鹅群详情',
        permissions: [PERMISSIONS.FLOCK_VIEW],
        cache: true,
        cacheTTL: 300000 // 5分钟
      },
      UPDATE: {
        path: '/:id',
        method: 'PUT',
        description: '更新鹅群信息',
        permissions: [PERMISSIONS.FLOCK_UPDATE],
        cache: false
      },
      DELETE: {
        path: '/:id',
        method: 'DELETE',
        description: '删除鹅群',
        permissions: [PERMISSIONS.FLOCK_DELETE],
        cache: false
      },
      STATISTICS: {
        path: '/:id/statistics',
        method: 'GET',
        description: '获取鹅群统计信息',
        permissions: [PERMISSIONS.FLOCK_VIEW],
        cache: true,
        cacheTTL: 300000
      }
    }
  },

  // ================================
  // 健康管理接口
  // ================================
  HEALTH: {
    base: '/health',
    endpoints: {
      RECORDS: {
        path: '/records',
        method: 'GET',
        description: '获取健康记录列表',
        permissions: [PERMISSIONS.HEALTH_VIEW],
        cache: true,
        cacheTTL: 60000
      },
      CREATE_RECORD: {
        path: '/records',
        method: 'POST',
        description: '创建健康记录',
        permissions: [PERMISSIONS.HEALTH_CREATE],
        cache: false
      },
      RECORD_DETAIL: {
        path: '/records/:id',
        method: 'GET',
        description: '获取健康记录详情',
        permissions: [PERMISSIONS.HEALTH_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      UPDATE_RECORD: {
        path: '/records/:id',
        method: 'PUT',
        description: '更新健康记录',
        permissions: [PERMISSIONS.HEALTH_UPDATE],
        cache: false
      },
      DELETE_RECORD: {
        path: '/records/:id',
        method: 'DELETE',
        description: '删除健康记录',
        permissions: [PERMISSIONS.HEALTH_DELETE],
        cache: false
      },
      KNOWLEDGE_BASE: {
        path: '/knowledge',
        method: 'GET',
        description: '获取健康知识库',
        permissions: [PERMISSIONS.HEALTH_VIEW],
        cache: true,
        cacheTTL: 3600000 // 1小时
      },
      AI_DIAGNOSIS: {
        path: '/ai-diagnosis',
        method: 'POST',
        description: 'AI健康诊断',
        permissions: [PERMISSIONS.AI_DIAGNOSIS],
        cache: false
      },
      HEALTH_REPORT: {
        path: '/report',
        method: 'GET',
        description: '生成健康报告',
        permissions: [PERMISSIONS.HEALTH_VIEW, PERMISSIONS.REPORTS_VIEW],
        cache: true,
        cacheTTL: 600000 // 10分钟
      }
    }
  },

  // ================================
  // 生产管理接口
  // ================================
  PRODUCTION: {
    base: '/production',
    endpoints: {
      RECORDS: {
        path: '/records',
        method: 'GET',
        description: '获取生产记录列表',
        permissions: [PERMISSIONS.PRODUCTION_VIEW],
        cache: true,
        cacheTTL: 60000
      },
      CREATE_RECORD: {
        path: '/records',
        method: 'POST',
        description: '创建生产记录',
        permissions: [PERMISSIONS.PRODUCTION_CREATE],
        cache: false
      },
      RECORD_DETAIL: {
        path: '/records/:id',
        method: 'GET',
        description: '获取生产记录详情',
        permissions: [PERMISSIONS.PRODUCTION_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      UPDATE_RECORD: {
        path: '/records/:id',
        method: 'PUT',
        description: '更新生产记录',
        permissions: [PERMISSIONS.PRODUCTION_UPDATE],
        cache: false
      },
      DELETE_RECORD: {
        path: '/records/:id',
        method: 'DELETE',
        description: '删除生产记录',
        permissions: [PERMISSIONS.PRODUCTION_DELETE],
        cache: false
      },
      STATISTICS: {
        path: '/statistics',
        method: 'GET',
        description: '获取生产统计数据',
        permissions: [PERMISSIONS.PRODUCTION_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      TRENDS: {
        path: '/trends',
        method: 'GET',
        description: '获取生产趋势分析',
        permissions: [PERMISSIONS.PRODUCTION_VIEW, PERMISSIONS.REPORTS_VIEW],
        cache: true,
        cacheTTL: 600000
      }
    }
  },

  // ================================
  // 统一库存管理接口
  // ================================
  INVENTORY: {
    base: '/inventory',
    endpoints: {
      LIST: {
        path: '',
        method: 'GET',
        description: '获取库存列表',
        permissions: [PERMISSIONS.INVENTORY_VIEW],
        cache: true,
        cacheTTL: 60000
      },
      CREATE: {
        path: '',
        method: 'POST',
        description: '添加库存物料',
        permissions: [PERMISSIONS.INVENTORY_CREATE],
        cache: false
      },
      DETAIL: {
        path: '/:id',
        method: 'GET',
        description: '获取库存详情',
        permissions: [PERMISSIONS.INVENTORY_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      UPDATE: {
        path: '/:id',
        method: 'PUT',
        description: '更新库存信息',
        permissions: [PERMISSIONS.INVENTORY_UPDATE],
        cache: false
      },
      DELETE: {
        path: '/:id',
        method: 'DELETE',
        description: '删除库存记录',
        permissions: [PERMISSIONS.INVENTORY_DELETE],
        cache: false
      },
      CATEGORIES: {
        path: '/categories',
        method: 'GET',
        description: '获取库存分类',
        permissions: [PERMISSIONS.INVENTORY_VIEW],
        cache: true,
        cacheTTL: 3600000 // 1小时
      },
      LOW_STOCK: {
        path: '/low-stock',
        method: 'GET',
        description: '获取低库存警告',
        permissions: [PERMISSIONS.INVENTORY_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      STATISTICS: {
        path: '/statistics',
        method: 'GET',
        description: '获取库存统计',
        permissions: [PERMISSIONS.INVENTORY_VIEW],
        cache: true,
        cacheTTL: 600000
      }
    }
  },

  // ================================
  // 用户管理接口
  // ================================
  USERS: {
    base: '/users',
    endpoints: {
      LIST: {
        path: '',
        method: 'GET',
        description: '获取用户列表',
        permissions: [PERMISSIONS.USER_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      CREATE: {
        path: '',
        method: 'POST',
        description: '创建用户',
        permissions: [PERMISSIONS.USER_CREATE],
        cache: false
      },
      DETAIL: {
        path: '/:id',
        method: 'GET',
        description: '获取用户详情',
        permissions: [PERMISSIONS.USER_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      UPDATE: {
        path: '/:id',
        method: 'PUT',
        description: '更新用户信息',
        permissions: [PERMISSIONS.USER_UPDATE],
        cache: false
      },
      DELETE: {
        path: '/:id',
        method: 'DELETE',
        description: '删除用户',
        permissions: [PERMISSIONS.USER_DELETE],
        cache: false
      },
      PROFILE: {
        path: '/:id/profile',
        method: 'GET',
        description: '获取用户档案',
        permissions: [PERMISSIONS.USER_VIEW],
        cache: true,
        cacheTTL: 600000
      },
      PERMISSIONS: {
        path: '/:id/permissions',
        method: 'GET',
        description: '获取用户权限',
        permissions: [PERMISSIONS.USER_VIEW, PERMISSIONS.ROLE_MANAGE],
        cache: true,
        cacheTTL: 300000
      },
      ASSIGN_ROLE: {
        path: '/:id/role',
        method: 'PUT',
        description: '分配用户角色',
        permissions: [PERMISSIONS.STAFF_MANAGE, PERMISSIONS.ROLE_MANAGE],
        cache: false
      }
    }
  },

  // ================================
  // 商城管理接口
  // ================================
  SHOP: {
    base: '/shop',
    endpoints: {
      PRODUCTS: {
        path: '/products',
        method: 'GET',
        description: '获取商品列表',
        permissions: [PERMISSIONS.SHOP_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      PRODUCT_DETAIL: {
        path: '/products/:id',
        method: 'GET',
        description: '获取商品详情',
        permissions: [PERMISSIONS.SHOP_VIEW],
        cache: true,
        cacheTTL: 600000
      },
      CREATE_PRODUCT: {
        path: '/products',
        method: 'POST',
        description: '创建商品',
        permissions: [PERMISSIONS.SHOP_CREATE],
        cache: false
      },
      UPDATE_PRODUCT: {
        path: '/products/:id',
        method: 'PUT',
        description: '更新商品',
        permissions: [PERMISSIONS.SHOP_UPDATE],
        cache: false
      },
      DELETE_PRODUCT: {
        path: '/products/:id',
        method: 'DELETE',
        description: '删除商品',
        permissions: [PERMISSIONS.SHOP_DELETE],
        cache: false
      },
      ORDERS: {
        path: '/orders',
        method: 'GET',
        description: '获取订单列表',
        permissions: [PERMISSIONS.SHOP_VIEW],
        cache: true,
        cacheTTL: 60000
      },
      CREATE_ORDER: {
        path: '/orders',
        method: 'POST',
        description: '创建订单',
        permissions: [PERMISSIONS.SHOP_VIEW],
        cache: false
      },
      ORDER_DETAIL: {
        path: '/orders/:id',
        method: 'GET',
        description: '获取订单详情',
        permissions: [PERMISSIONS.SHOP_VIEW],
        cache: true,
        cacheTTL: 300000
      }
    }
  },

  // ================================
  // 财务管理接口
  // ================================
  FINANCE: {
    base: '/finance',
    endpoints: {
      RECORDS: {
        path: '/records',
        method: 'GET',
        description: '获取财务记录',
        permissions: [PERMISSIONS.FINANCE_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      CREATE_RECORD: {
        path: '/records',
        method: 'POST',
        description: '创建财务记录',
        permissions: [PERMISSIONS.FINANCE_CREATE],
        cache: false
      },
      REPORTS: {
        path: '/reports',
        method: 'GET',
        description: '获取财务报表',
        permissions: [PERMISSIONS.FINANCE_VIEW, PERMISSIONS.REPORTS_VIEW],
        cache: true,
        cacheTTL: 600000
      },
      EXPORT: {
        path: '/export',
        method: 'GET',
        description: '导出财务数据',
        permissions: [PERMISSIONS.FINANCE_EXPORT],
        cache: false
      },
      REIMBURSEMENTS: {
        path: '/reimbursements',
        method: 'GET',
        description: '获取报销记录',
        permissions: [PERMISSIONS.FINANCE_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      CREATE_REIMBURSEMENT: {
        path: '/reimbursements',
        method: 'POST',
        description: '创建报销申请',
        permissions: [PERMISSIONS.FINANCE_CREATE],
        cache: false
      },
      APPROVE_REIMBURSEMENT: {
        path: '/reimbursements/:id/approve',
        method: 'POST',
        description: '审批报销申请',
        permissions: [PERMISSIONS.FINANCE_APPROVE],
        cache: false
      }
    }
  },

  // ================================
  // OA办公接口
  // ================================
  OA: {
    base: '/oa',
    endpoints: {
      TASKS: {
        path: '/tasks',
        method: 'GET',
        description: '获取任务列表',
        permissions: [PERMISSIONS.TASK_VIEW],
        cache: true,
        cacheTTL: 60000
      },
      CREATE_TASK: {
        path: '/tasks',
        method: 'POST',
        description: '创建任务',
        permissions: [PERMISSIONS.TASK_CREATE],
        cache: false
      },
      TASK_DETAIL: {
        path: '/tasks/:id',
        method: 'GET',
        description: '获取任务详情',
        permissions: [PERMISSIONS.TASK_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      UPDATE_TASK: {
        path: '/tasks/:id',
        method: 'PUT',
        description: '更新任务',
        permissions: [PERMISSIONS.TASK_UPDATE],
        cache: false
      },
      ASSIGN_TASK: {
        path: '/tasks/:id/assign',
        method: 'POST',
        description: '分配任务',
        permissions: [PERMISSIONS.TASK_ASSIGN],
        cache: false
      },
      APPROVALS: {
        path: '/approvals',
        method: 'GET',
        description: '获取审批列表',
        permissions: [PERMISSIONS.APPROVAL_VIEW],
        cache: true,
        cacheTTL: 60000
      },
      PROCESS_APPROVAL: {
        path: '/approvals/:id/process',
        method: 'POST',
        description: '处理审批',
        permissions: [PERMISSIONS.APPROVAL_PROCESS],
        cache: false
      }
    }
  },

  // ================================
  // AI服务接口
  // ================================
  AI: {
    base: '/ai',
    endpoints: {
      CHAT: {
        path: '/chat',
        method: 'POST',
        description: 'AI对话服务',
        permissions: [PERMISSIONS.AI_CHAT],
        cache: false
      },
      DIAGNOSIS: {
        path: '/diagnosis',
        method: 'POST',
        description: 'AI健康诊断',
        permissions: [PERMISSIONS.AI_DIAGNOSIS],
        cache: false
      },
      IMAGE_RECOGNITION: {
        path: '/image-recognition',
        method: 'POST',
        description: 'AI图像识别',
        permissions: [PERMISSIONS.AI_DIAGNOSIS],
        cache: false
      },
      CONFIG: {
        path: '/config',
        method: 'GET',
        description: '获取AI配置',
        permissions: [PERMISSIONS.AI_CONFIG],
        cache: true,
        cacheTTL: 600000
      },
      UPDATE_CONFIG: {
        path: '/config',
        method: 'PUT',
        description: '更新AI配置',
        permissions: [PERMISSIONS.AI_CONFIG],
        cache: false
      },
      STATISTICS: {
        path: '/statistics',
        method: 'GET',
        description: '获取AI使用统计',
        permissions: [PERMISSIONS.AI_STATS],
        cache: true,
        cacheTTL: 300000
      }
    }
  },

  // ================================
  // 系统管理接口
  // ================================
  SYSTEM: {
    base: '/system',
    endpoints: {
      SETTINGS: {
        path: '/settings',
        method: 'GET',
        description: '获取系统设置',
        permissions: [PERMISSIONS.SETTINGS_MANAGE],
        cache: true,
        cacheTTL: 600000
      },
      UPDATE_SETTINGS: {
        path: '/settings',
        method: 'PUT',
        description: '更新系统设置',
        permissions: [PERMISSIONS.SETTINGS_MANAGE],
        cache: false
      },
      ANNOUNCEMENTS: {
        path: '/announcements',
        method: 'GET',
        description: '获取系统公告',
        permissions: null,
        cache: true,
        cacheTTL: 600000
      },
      DATA_EXPORT: {
        path: '/export',
        method: 'POST',
        description: '数据导出',
        permissions: [PERMISSIONS.DATA_EXPORT],
        cache: false
      },
      DATA_IMPORT: {
        path: '/import',
        method: 'POST',
        description: '数据导入',
        permissions: [PERMISSIONS.DATA_IMPORT],
        cache: false
      },
      BACKUP: {
        path: '/backup',
        method: 'POST',
        description: '数据备份',
        permissions: [PERMISSIONS.DATA_BACKUP],
        cache: false
      }
    }
  },

  // ================================
  // 平台管理接口（SAAS管理员专用）
  // ================================
  PLATFORM: {
    base: '/platform',
    version: API_VERSIONS.PLATFORM,
    endpoints: {
      TENANTS: {
        path: '/tenants',
        method: 'GET',
        description: '获取租户列表',
        permissions: [PERMISSIONS.TENANT_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      CREATE_TENANT: {
        path: '/tenants',
        method: 'POST',
        description: '创建租户',
        permissions: [PERMISSIONS.TENANT_CREATE],
        cache: false
      },
      TENANT_DETAIL: {
        path: '/tenants/:id',
        method: 'GET',
        description: '获取租户详情',
        permissions: [PERMISSIONS.TENANT_VIEW],
        cache: true,
        cacheTTL: 300000
      },
      UPDATE_TENANT: {
        path: '/tenants/:id',
        method: 'PUT',
        description: '更新租户信息',
        permissions: [PERMISSIONS.TENANT_UPDATE],
        cache: false
      },
      SUSPEND_TENANT: {
        path: '/tenants/:id/suspend',
        method: 'POST',
        description: '暂停租户',
        permissions: [PERMISSIONS.TENANT_SUSPEND],
        cache: false
      },
      ACTIVATE_TENANT: {
        path: '/tenants/:id/activate',
        method: 'POST',
        description: '激活租户',
        permissions: [PERMISSIONS.TENANT_ACTIVATE],
        cache: false
      },
      PLATFORM_ANALYTICS: {
        path: '/analytics',
        method: 'GET',
        description: '平台分析数据',
        permissions: [PERMISSIONS.PLATFORM_ANALYTICS],
        cache: true,
        cacheTTL: 300000
      },
      SUBSCRIPTION_PLANS: {
        path: '/subscription-plans',
        method: 'GET',
        description: '获取订阅计划',
        permissions: [PERMISSIONS.SUBSCRIPTION_VIEW],
        cache: true,
        cacheTTL: 3600000
      },
      SYSTEM_MONITOR: {
        path: '/monitor',
        method: 'GET',
        description: '系统监控数据',
        permissions: [PERMISSIONS.SYSTEM_MONITOR],
        cache: true,
        cacheTTL: 60000
      }
    }
  }
};

/**
 * API端点构建器
 */
class ApiEndpointBuilder {
  constructor(baseUrl = '', version = API_VERSIONS.V1) {
    this.baseUrl = baseUrl;
    this.version = version;
  }

  /**
   * 构建完整的API端点路径
   */
  buildEndpoint(category, endpoint, params = {}) {
    const categoryConfig = API_ENDPOINTS[category];
    if (!categoryConfig) {
      throw new Error(`API分类 ${category} 不存在`);
    }

    const endpointConfig = categoryConfig.endpoints[endpoint];
    if (!endpointConfig) {
      throw new Error(`API端点 ${category}.${endpoint} 不存在`);
    }

    // 使用特定版本或默认版本
    const version = categoryConfig.version || this.version;
    let path = `${version}${categoryConfig.base}${endpointConfig.path}`;

    // 替换路径参数
    Object.keys(params).forEach(key => {
      path = path.replace(`:${key}`, params[key]);
    });

    return {
      ...endpointConfig,
      fullPath: path,
      category: category,
      name: endpoint
    };
  }

  /**
   * 获取端点配置
   */
  getEndpointConfig(category, endpoint) {
    const categoryConfig = API_ENDPOINTS[category];
    if (!categoryConfig) {
      return null;
    }

    const endpointConfig = categoryConfig.endpoints[endpoint];
    if (!endpointConfig) {
      return null;
    }

    return {
      ...endpointConfig,
      category: category,
      name: endpoint,
      basePath: categoryConfig.base
    };
  }

  /**
   * 获取所有端点
   */
  getAllEndpoints() {
    const allEndpoints = {};
    
    Object.keys(API_ENDPOINTS).forEach(category => {
      allEndpoints[category] = {};
      Object.keys(API_ENDPOINTS[category].endpoints).forEach(endpoint => {
        allEndpoints[category][endpoint] = this.getEndpointConfig(category, endpoint);
      });
    });

    return allEndpoints;
  }

  /**
   * 根据权限筛选端点
   */
  getEndpointsByPermissions(userPermissions) {
    const filteredEndpoints = {};
    
    Object.keys(API_ENDPOINTS).forEach(category => {
      filteredEndpoints[category] = {};
      Object.keys(API_ENDPOINTS[category].endpoints).forEach(endpoint => {
        const config = this.getEndpointConfig(category, endpoint);
        
        // 如果端点不需要权限或用户拥有所需权限
        if (!config.permissions || 
            config.permissions.some(perm => userPermissions.includes(perm))) {
          filteredEndpoints[category][endpoint] = config;
        }
      });
    });

    return filteredEndpoints;
  }
}

// 创建默认构建器实例
const defaultBuilder = new ApiEndpointBuilder();

// 导出便捷方法
const endpoints = {
  // 构建方法
  build: (category, endpoint, params) => defaultBuilder.buildEndpoint(category, endpoint, params),
  get: (category, endpoint) => defaultBuilder.getEndpointConfig(category, endpoint),
  getAll: () => defaultBuilder.getAllEndpoints(),
  getByPermissions: (permissions) => defaultBuilder.getEndpointsByPermissions(permissions),
  
  // 创建构建器
  createBuilder: (baseUrl, version) => new ApiEndpointBuilder(baseUrl, version),
  
  // 常量
  VERSIONS: API_VERSIONS,
  ENDPOINTS: API_ENDPOINTS
};

module.exports = {
  API_VERSIONS,
  API_ENDPOINTS,
  ApiEndpointBuilder,
  endpoints,
  default: endpoints
};