<!--pages/oa/purchase/apply/apply.wxml-->
<view class="container">
  <!-- 采购申请表单 -->
  <view class="form-section">
    <!-- 基本信息 -->
    <view class="form-item">
      <text class="form-label">采购标题 <text class="required">*</text></text>
      <input 
        class="form-input"
        placeholder="请输入采购标题"
        value="{{formData.title}}"
        bindinput="onInputChange"
        data-field="title"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">采购说明</text>
      <textarea 
        class="form-textarea"
        placeholder="请输入采购说明（可选）"
        value="{{formData.description}}"
        bindinput="onInputChange"
        data-field="description"
        maxlength="200"
      ></textarea>
    </view>
    
    <view class="form-row">
      <view class="form-item half">
        <text class="form-label">采购类别 <text class="required">*</text></text>
        <picker
          mode="selector"
          range="{{categories}}"
          range-key="label"
          value="{{categoryIndex}}"
          bindchange="onInputChange"
          data-field="category"
        >
          <view class="form-picker">
            <text class="picker-text">{{selectedCategoryLabel}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="form-item half">
        <text class="form-label">紧急程度</text>
        <picker
          mode="selector"
          range="{{urgencyLevels}}"
          range-key="label"
          value="{{urgencyIndex}}"
          bindchange="onInputChange"
          data-field="urgencyLevel"
        >
          <view class="form-picker">
            <text class="picker-text" style="color: {{selectedUrgencyColor}}">{{selectedUrgencyLabel}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="form-item">
      <text class="form-label">预期到货日期 <text class="required">*</text></text>
      <picker 
        mode="date" 
        value="{{formData.expectedDate}}"
        bindchange="onDateChange"
      >
        <view class="form-picker">
          <text class="picker-text">{{formData.expectedDate || '2025-08-12'}}</text>
          <text class="picker-icon">📅</text>
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <text class="form-label">业务目的 <text class="required">*</text></text>
      <textarea 
        class="form-textarea"
        placeholder="请详细说明此次采购的业务目的和必要性"
        value="{{formData.businessPurpose}}"
        bindinput="onInputChange"
        data-field="businessPurpose"
        maxlength="300"
      ></textarea>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn" 
      bindtap="onSubmit" 
      disabled="{{submitting}}"
      loading="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交申请'}}
    </button>
  </view>
</view>