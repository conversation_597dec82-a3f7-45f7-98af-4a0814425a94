<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-trial { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <%- include('partials/navbar') %>

    <div class="container-fluid">
        <div class="row">
            <%- include('partials/sidebar') %>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 顶部导航 -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0" id="page-title"><%= title %></h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-3">在线</span>
                            <i class="bi bi-bell fs-5 me-3"></i>
                            <i class="bi bi-gear fs-5"></i>
                        </div>
                    </div>
                </nav>

                <div class="container-fluid mt-4">
                    <!-- 面包屑导航 -->
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/saas-admin/dashboard">仪表盘</a></li>
                            <li class="breadcrumb-item"><a href="/saas-admin/tenants">租户管理</a></li>
                            <li class="breadcrumb-item active"><%= tenant.companyName %></li>
                        </ol>
                    </nav>

                    <!-- 租户基本信息 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">租户信息</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>公司名称：</strong><%= tenant.companyName %></p>
                                            <p><strong>租户代码：</strong><%= tenant.tenantCode %></p>
                                            <p><strong>联系人：</strong><%= tenant.contactName %></p>
                                            <p><strong>联系电话：</strong><%= tenant.contactPhone %></p>
                                            <p><strong>联系邮箱：</strong><%= tenant.contactEmail %></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>状态：</strong>
                                                <span class="status-badge status-<%= tenant.status %>">
                                                    <%= tenant.status === 'active' ? '活跃' : '非活跃' %>
                                                </span>
                                            </p>
                                            <p><strong>订阅计划：</strong><%= tenant.subscriptionPlan %></p>
                                            <p><strong>创建时间：</strong><%= new Date(tenant.createdAt).toLocaleDateString('zh-CN') %></p>
                                            <p><strong>最后活跃：</strong><%= tenant.lastActiveAt ? new Date(tenant.lastActiveAt).toLocaleDateString('zh-CN') : '从未' %></p>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-primary me-2">
                                            <i class="bi bi-pencil"></i> 编辑信息
                                        </button>
                                        <button class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-eye"></i> 查看详情
                                        </button>
                                        <button class="btn btn-outline-danger">
                                            <i class="bi bi-pause"></i> 暂停服务
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card">
                                <h6>使用统计</h6>
                                <p class="mb-1">用户数量: <strong><%= tenantStats.totalUsers %></strong></p>
                                <p class="mb-1">活跃用户: <strong><%= tenantStats.activeUsers %></strong></p>
                                <p class="mb-1">鹅群数量: <strong><%= tenantStats.totalFlocks %></strong></p>
                                <p class="mb-1">总鹅数: <strong><%= tenantStats.totalGeese %></strong></p>
                                <p class="mb-0">存储使用: <strong><%= tenantStats.storageUsed %></strong></p>
                            </div>
                        </div>
                    </div>

                    <!-- 功能标签页 -->
                    <ul class="nav nav-tabs" id="tenantTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                                用户管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="flocks-tab" data-bs-toggle="tab" data-bs-target="#flocks" type="button" role="tab">
                                鹅群管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="health-tab" data-bs-toggle="tab" data-bs-target="#health" type="button" role="tab">
                                健康记录
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="production-tab" data-bs-toggle="tab" data-bs-target="#production" type="button" role="tab">
                                生产记录
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab">
                                库存管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ai-diagnosis-tab" data-bs-toggle="tab" data-bs-target="#ai-diagnosis" type="button" role="tab">
                                AI诊断
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                                设置
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="tenantTabsContent">
                        <!-- 用户管理标签页 -->
                        <div class="tab-pane fade show active" id="users" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6>租户用户列表</h6>
                                        <button class="btn btn-sm btn-success">
                                            <i class="bi bi-person-plus"></i> 新增用户
                                        </button>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>用户名</th>
                                                    <th>邮箱</th>
                                                    <th>角色</th>
                                                    <th>状态</th>
                                                    <th>最后登录</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <% if (tenantUsers && tenantUsers.length > 0) { %>
                                                    <% tenantUsers.forEach(user => { %>
                                                    <tr>
                                                        <td><%= user.username %></td>
                                                        <td><%= user.email %></td>
                                                        <td><%= user.role %></td>
                                                        <td>
                                                            <span class="badge bg-<%= user.status === 'active' ? 'success' : 'secondary' %>">
                                                                <%= user.status === 'active' ? '活跃' : '非活跃' %>
                                                            </span>
                                                        </td>
                                                        <td><%= user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('zh-CN') : '从未' %></td>
                                                        <td>
                                                            <button class="btn btn-sm btn-outline-primary me-1">
                                                                <i class="bi bi-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-secondary me-1">
                                                                <i class="bi bi-pencil"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    <% }) %>
                                                <% } else { %>
                                                    <tr>
                                                        <td colspan="6" class="text-center text-muted">暂无用户数据</td>
                                                    </tr>
                                                <% } %>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 鹅群管理标签页 -->
                        <div class="tab-pane fade" id="flocks" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6>鹅群列表</h6>
                                        <button class="btn btn-sm btn-success">
                                            <i class="bi bi-plus"></i> 新增鹅群
                                        </button>
                                    </div>
                                    <div class="row">
                                        <% if (tenantFlocks && tenantFlocks.length > 0) { %>
                                            <% tenantFlocks.forEach(flock => { %>
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="card">
                                                    <div class="card-body">
                                                        <h6 class="card-title"><%= flock.name %></h6>
                                                        <p class="card-text mb-1">品种: <%= flock.breed %></p>
                                                        <p class="card-text mb-1">当前数量: <%= flock.currentCount %></p>
                                                        <p class="card-text mb-1">健康状态: 
                                                            <span class="badge bg-<%= flock.healthStatus === 'good' ? 'success' : 'warning' %>">
                                                                <%= flock.healthStatus === 'good' ? '健康' : '需关注' %>
                                                            </span>
                                                        </p>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary">查看</button>
                                                            <button class="btn btn-outline-secondary">编辑</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <% }) %>
                                        <% } else { %>
                                            <div class="col-12">
                                                <div class="text-center text-muted py-4">
                                                    <i class="bi bi-collection fs-1"></i>
                                                    <p class="mt-2">暂无鹅群数据</p>
                                                </div>
                                            </div>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 健康记录标签页 -->
                        <div class="tab-pane fade" id="health" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>健康记录</h6>
                                        <div>
                                            <button class="btn btn-sm btn-success me-2">
                                                <i class="bi bi-plus"></i> 新增记录
                                            </button>
                                            <a href="/saas-admin/tenants/<%= tenant.id %>/health" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> 查看全部
                                            </a>
                                        </div>
                                    </div>
                                    
                                    <!-- 健康统计 -->
                                    <div class="row mb-4">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-success mb-1">85%</h5>
                                                <small class="text-muted">健康率</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-warning mb-1">12</h5>
                                                <small class="text-muted">病鹅数量</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-info mb-1">3</h5>
                                                <small class="text-muted">本周检查</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-danger mb-1">2</h5>
                                                <small class="text-muted">待处理警报</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 最近健康记录 -->
                                    <h6 class="mb-3">最近健康记录</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>时间</th>
                                                    <th>鹅群</th>
                                                    <th>检查类型</th>
                                                    <th>健康状态</th>
                                                    <th>异常数量</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <% if (healthAlerts && healthAlerts.length > 0) { %>
                                                    <% healthAlerts.forEach(alert => { %>
                                                    <tr>
                                                        <td><%= new Date(alert.date).toLocaleDateString('zh-CN') %></td>
                                                        <td>鹅群A</td>
                                                        <td>例行检查</td>
                                                        <td>
                                                            <span class="badge bg-<%= alert.type === 'warning' ? 'warning' : 'info' %>">
                                                                <%= alert.type === 'warning' ? '需关注' : '正常' %>
                                                            </span>
                                                        </td>
                                                        <td><%= alert.type === 'warning' ? '3' : '0' %></td>
                                                        <td>
                                                            <button class="btn btn-sm btn-outline-primary">查看</button>
                                                        </td>
                                                    </tr>
                                                    <% }) %>
                                                <% } else { %>
                                                    <tr>
                                                        <td colspan="6" class="text-center text-muted">暂无健康记录</td>
                                                    </tr>
                                                <% } %>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 生产记录标签页 -->
                        <div class="tab-pane fade" id="production" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>生产记录</h6>
                                        <div>
                                            <button class="btn btn-sm btn-success me-2">
                                                <i class="bi bi-plus"></i> 新增记录
                                            </button>
                                            <a href="/saas-admin/tenants/<%= tenant.id %>/production" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> 查看全部
                                            </a>
                                        </div>
                                    </div>
                                    
                                    <!-- 生产统计 -->
                                    <div class="row mb-4">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-primary mb-1"><%= productionStats.dailyEggs %></h5>
                                                <small class="text-muted">今日产蛋</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-success mb-1"><%= productionStats.weeklyEggs %></h5>
                                                <small class="text-muted">本周产蛋</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-info mb-1"><%= productionStats.monthlyEggs %></h5>
                                                <small class="text-muted">本月产蛋</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-warning mb-1">89%</h5>
                                                <small class="text-muted">产蛋率</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 最近生产记录 -->
                                    <h6 class="mb-3">最近生产记录</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>日期</th>
                                                    <th>鹅群</th>
                                                    <th>产蛋数量</th>
                                                    <th>产蛋率</th>
                                                    <th>鹅蛋重量(kg)</th>
                                                    <th>品质等级</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><%= new Date().toLocaleDateString('zh-CN') %></td>
                                                    <td>鹅群A</td>
                                                    <td>45</td>
                                                    <td>
                                                        <span class="badge bg-success">89%</span>
                                                    </td>
                                                    <td>2.8</td>
                                                    <td>
                                                        <span class="badge bg-primary">优质</span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><%= new Date(Date.now() - 86400000).toLocaleDateString('zh-CN') %></td>
                                                    <td>鹅群B</td>
                                                    <td>38</td>
                                                    <td>
                                                        <span class="badge bg-warning">76%</span>
                                                    </td>
                                                    <td>2.3</td>
                                                    <td>
                                                        <span class="badge bg-success">合格</span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><%= new Date(Date.now() - 172800000).toLocaleDateString('zh-CN') %></td>
                                                    <td>鹅群A</td>
                                                    <td>42</td>
                                                    <td>
                                                        <span class="badge bg-success">84%</span>
                                                    </td>
                                                    <td>2.6</td>
                                                    <td>
                                                        <span class="badge bg-primary">优质</span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 库存管理标签页 -->
                        <div class="tab-pane fade" id="inventory" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6>库存管理</h6>
                                        <div>
                                            <button class="btn btn-sm btn-success me-2">
                                                <i class="bi bi-plus"></i> 新增记录
                                            </button>
                                            <a href="/saas-admin/tenants/<%= tenant.id %>/inventory" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> 查看全部
                                            </a>
                                        </div>
                                    </div>
                                    
                                    <!-- 库存统计 -->
                                    <div class="row mb-4">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-primary mb-1">1,240</h5>
                                                <small class="text-muted">总库存(枚)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-success mb-1">980</h5>
                                                <small class="text-muted">可销售</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-warning mb-1">180</h5>
                                                <small class="text-muted">预留库存</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-danger mb-1">80</h5>
                                                <small class="text-muted">已损耗</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 库存明细 -->
                                    <h6 class="mb-3">库存明细</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>商品类型</th>
                                                    <th>等级</th>
                                                    <th>当前库存</th>
                                                    <th>最低库存</th>
                                                    <th>状态</th>
                                                    <th>最后更新</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>鹅蛋</td>
                                                    <td>
                                                        <span class="badge bg-primary">优质</span>
                                                    </td>
                                                    <td>580</td>
                                                    <td>100</td>
                                                    <td>
                                                        <span class="badge bg-success">充足</span>
                                                    </td>
                                                    <td><%= new Date().toLocaleDateString('zh-CN') %></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>鹅蛋</td>
                                                    <td>
                                                        <span class="badge bg-success">合格</span>
                                                    </td>
                                                    <td>400</td>
                                                    <td>200</td>
                                                    <td>
                                                        <span class="badge bg-success">充足</span>
                                                    </td>
                                                    <td><%= new Date().toLocaleDateString('zh-CN') %></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>饲料</td>
                                                    <td>
                                                        <span class="badge bg-info">标准</span>
                                                    </td>
                                                    <td>85</td>
                                                    <td>100</td>
                                                    <td>
                                                        <span class="badge bg-warning">偏低</span>
                                                    </td>
                                                    <td><%= new Date().toLocaleDateString('zh-CN') %></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI诊断标签页 -->
                        <div class="tab-pane fade" id="ai-diagnosis" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6>AI诊断记录</h6>
                                        <div>
                                            <button class="btn btn-sm btn-success me-2">
                                                <i class="bi bi-plus"></i> 新增诊断
                                            </button>
                                            <a href="/saas-admin/tenants/<%= tenant.id %>/ai-diagnosis" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> 查看全部
                                            </a>
                                        </div>
                                    </div>
                                    
                                    <!-- AI诊断统计 -->
                                    <div class="row mb-4">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-primary mb-1">24</h5>
                                                <small class="text-muted">总诊断次数</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-success mb-1">18</h5>
                                                <small class="text-muted">健康诊断</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-warning mb-1">5</h5>
                                                <small class="text-muted">需要关注</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="text-danger mb-1">1</h5>
                                                <small class="text-muted">紧急处理</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 最近AI诊断 -->
                                    <h6 class="mb-3">最近AI诊断</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>时间</th>
                                                    <th>鹅群</th>
                                                    <th>症状</th>
                                                    <th>诊断结果</th>
                                                    <th>置信度</th>
                                                    <th>状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><%= new Date().toLocaleDateString('zh-CN') %></td>
                                                    <td>鹅群A</td>
                                                    <td>食欲不振</td>
                                                    <td>消化不良</td>
                                                    <td>
                                                        <span class="badge bg-success">92%</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-warning">处理中</span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><%= new Date(Date.now() - 86400000).toLocaleDateString('zh-CN') %></td>
                                                    <td>鹅群B</td>
                                                    <td>羽毛异常</td>
                                                    <td>营养缺乏</td>
                                                    <td>
                                                        <span class="badge bg-success">85%</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success">已完成</span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">查看</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 设置标签页 -->
                        <div class="tab-pane fade" id="settings" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <h6>租户设置</h6>
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">小程序配置</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enableMiniprogram">
                                                <label class="form-check-label" for="enableMiniprogram">
                                                    启用小程序功能
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API限制</label>
                                            <input type="number" class="form-control" value="10000" placeholder="每月API调用限制">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">存储限制 (GB)</label>
                                            <input type="number" class="form-control" value="5" placeholder="存储空间限制">
                                        </div>
                                        <button type="submit" class="btn btn-primary">保存设置</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>