@import '/styles/oa-common.wxss';
/* pages/oa/leave/list/list.wxss */

.list-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 120rpx;
}

/* ==================== 搜索和筛选区域 ==================== */
.search-filter-section {
  background: white;
  padding: 30rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  height: 72rpx;
  background: #f8f9fa;
  border-radius: 36rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #0066CC;
  background: #f8fbff;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin: 0 20rpx 0 24rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
  border-radius: 50%;
  background: #ccc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.search-btn {
  height: 72rpx;
  padding: 0 32rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border: none;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 筛选器 */
.filter-row {
  display: flex;
  gap: 16rpx;
}

.filter-picker {
  flex: 1;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.filter-item:active {
  border-color: #0066CC;
  background: #f8fbff;
}

.filter-label {
  font-size: 26rpx;
  color: #333;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* ==================== 统计信息 ==================== */
.stats-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 20rpx;
  background: white;
  border-bottom: 2rpx solid #f0f0f0;
}

.stats-text {
  font-size: 26rpx;
  color: #666;
}

.apply-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  height: 60rpx;
  padding: 0 20rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.apply-icon {
  width: 24rpx;
  height: 24rpx;
}

/* ==================== 请假记录列表 ==================== */
.leave-list-section {
  padding: 0 20rpx;
}

.leave-item {
  background: white;
  border-radius: 16rpx;
  margin: 20rpx 0;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.leave-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 头部信息 */
.leave-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.leave-type-badge {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.leave-status {
  font-size: 28rpx;
  font-weight: 600;
}

/* 请假内容 */
.leave-content {
  margin-bottom: 24rpx;
}

.leave-reason {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.leave-time-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-row {
  display: flex;
  align-items: center;
}

.time-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.time-value {
  font-size: 26rpx;
  color: #333;
}

.days-info {
  background: #f0f8ff;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #0066CC;
}

.days-text {
  font-size: 24rpx;
  color: #0066CC;
  font-weight: 500;
}

/* 底部信息 */
.leave-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.applicant-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.applicant-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.applicant-dept {
  font-size: 24rpx;
  color: #999;
}

.time-info {
  flex: 1;
  text-align: center;
}

.submit-time {
  font-size: 24rpx;
  color: #999;
}

.arrow-icon {
  color: #ccc;
  font-size: 28rpx;
  margin-left: 20rpx;
}

/* 审批信息 */
.approve-info {
  margin-top: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 3rpx solid #00A86B;
}

.approve-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.reject-reason {
  font-size: 24rpx;
  color: #FF3B30;
  display: block;
}

/* ==================== 空状态 ==================== */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: white;
  margin: 40rpx 20rpx;
  border-radius: 16rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

.empty-apply-btn {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* ==================== 加载状态 ==================== */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

/* ==================== 浮动按钮 ==================== */
.floating-btn {
  position: fixed;
  right: 40rpx;
  bottom: 160rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  box-shadow: 0 6rpx 20rpx rgba(0, 102, 204, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: all 0.3s ease;
}

.floating-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.3);
}

.floating-icon {
  width: 48rpx;
  height: 48rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}