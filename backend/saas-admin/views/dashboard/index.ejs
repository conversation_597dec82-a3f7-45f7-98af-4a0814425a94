<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧养鹅SaaS平台 - 仪表板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #212529);
            color: white;
            height: 100vh;
            position: fixed;
            overflow-y: auto;
        }
        
        .main-content {
            margin-left: 250px;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: none;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card {
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card.warning {
            border-left-color: #ffc107;
        }
        
        .stat-card.danger {
            border-left-color: #dc3545;
        }
        
        .stat-card.info {
            border-left-color: #17a2b8;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.7);
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .metric-title {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .trend-up {
            color: #28a745;
        }
        
        .trend-down {
            color: #dc3545;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <%- include('partials/sidebar') %>
            
            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 顶部导航 -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0">仪表板</h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-3">在线</span>
                            <i class="bi bi-bell fs-5 me-3"></i>
                            <i class="bi bi-gear fs-5"></i>
                        </div>
                    </div>
                </nav>
                
                <!-- 统计卡片 -->
                <div class="container-fluid mt-4">
                    <div class="row g-4 mb-4">
                        <div class="col-md-3">
                            <div class="card stat-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="metric-title">总租户数</div>
                                            <div class="metric-value">125</div>
                                            <div class="small text-muted">
                                                <span class="trend-up"><i class="bi bi-arrow-up"></i> 12%</span> 比上周
                                            </div>
                                        </div>
                                        <div class="fs-1 text-success">
                                            <i class="bi bi-building"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card stat-card h-100 warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="metric-title">活跃租户</div>
                                            <div class="metric-value">98</div>
                                            <div class="small text-muted">
                                                <span class="trend-up"><i class="bi bi-arrow-up"></i> 5%</span> 比上周
                                            </div>
                                        </div>
                                        <div class="fs-1 text-warning">
                                            <i class="bi bi-activity"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card stat-card h-100 info">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="metric-title">月收入</div>
                                            <div class="metric-value">¥25,600</div>
                                            <div class="small text-muted">
                                                <span class="trend-up"><i class="bi bi-arrow-up"></i> 8%</span> 比上月
                                            </div>
                                        </div>
                                        <div class="fs-1 text-info">
                                            <i class="bi bi-currency-dollar"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card stat-card h-100 danger">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="metric-title">活跃用户</div>
                                            <div class="metric-value">1,250</div>
                                            <div class="small text-muted">
                                                <span class="trend-down"><i class="bi bi-arrow-down"></i> 2%</span> 比上周
                                            </div>
                                        </div>
                                        <div class="fs-1 text-danger">
                                            <i class="bi bi-people"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图表区域 -->
                    <div class="row g-4 mb-4">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <h6>API调用趋势</h6>
                                <div style="position: relative; height: 300px;">
                                    <canvas id="apiChart"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="chart-container">
                                <h6>租户分布</h6>
                                <div style="position: relative; height: 300px;">
                                    <canvas id="tenantChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 最新租户 -->
                    <div class="row g-4">
                        <div class="col-md-12">
                            <div class="chart-container">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>最新租户</h6>
                                    <a href="/saas-admin/tenants" class="btn btn-sm btn-outline-primary">查看全部</a>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>租户名称</th>
                                                <th>租户代码</th>
                                                <th>状态</th>
                                                <th>订阅计划</th>
                                                <th>用户数</th>
                                                <th>最后活跃</th>
                                                <th>创建时间</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>北京现代农业科技有限公司</td>
                                                <td>TENANT001</td>
                                                <td><span class="badge bg-success">活跃</span></td>
                                                <td>高级版</td>
                                                <td>15</td>
                                                <td>2024-01-15</td>
                                                <td>2023-12-01</td>
                                            </tr>
                                            <tr>
                                                <td>上海绿源生态农场</td>
                                                <td>TENANT002</td>
                                                <td><span class="badge bg-success">活跃</span></td>
                                                <td>标准版</td>
                                                <td>8</td>
                                                <td>2024-01-14</td>
                                                <td>2023-12-05</td>
                                            </tr>
                                            <tr>
                                                <td>广州白云山禽类养殖场</td>
                                                <td>TENANT003</td>
                                                <td><span class="badge bg-warning">试用</span></td>
                                                <td>试用版</td>
                                                <td>3</td>
                                                <td>2024-01-13</td>
                                                <td>2024-01-10</td>
                                            </tr>
                                            <tr>
                                                <td>深圳生态农业发展有限公司</td>
                                                <td>TENANT004</td>
                                                <td><span class="badge bg-success">活跃</span></td>
                                                <td>企业版</td>
                                                <td>42</td>
                                                <td>2024-01-12</td>
                                                <td>2023-11-20</td>
                                            </tr>
                                            <tr>
                                                <td>杭州西湖农业科技园</td>
                                                <td>TENANT005</td>
                                                <td><span class="badge bg-danger">暂停</span></td>
                                                <td>基础版</td>
                                                <td>5</td>
                                                <td>2024-01-01</td>
                                                <td>2023-10-15</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // API调用趋势图
        const apiCtx = document.getElementById('apiChart').getContext('2d');
        const apiChart = new Chart(apiCtx, {
            type: 'line',
            data: {
                labels: ['1月1日', '1月2日', '1月3日', '1月4日', '1月5日', '1月6日', '1月7日'],
                datasets: [{
                    label: 'API调用次数',
                    data: [12500, 13200, 11800, 14100, 15600, 14800, 16200],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // 租户分布图
        const tenantCtx = document.getElementById('tenantChart').getContext('2d');
        const tenantChart = new Chart(tenantCtx, {
            type: 'doughnut',
            data: {
                labels: ['活跃', '试用', '暂停'],
                datasets: [{
                    data: [98, 15, 12],
                    backgroundColor: [
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
