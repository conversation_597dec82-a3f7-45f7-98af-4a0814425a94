// components/section-header/section-header.js
const { IMAGES } = require('../../constants/index.js');

Component({
  properties: {
    // 标题文本
    title: {
      type: String,
      value: ''
    },
    // 标题图标
    icon: {
      type: String,
      value: ''
    },
    // 更多按钮文本
    moreText: {
      type: String,
      value: '更多'
    },
    // 是否显示更多按钮
    showMore: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  methods: {
    // 点击更多按钮
    onMoreTap() {
      this.triggerEvent('moretap');
    }
  }
});