// pages/management/staff/staff.js
const { IMAGES, UI, BUSINESS, API } = require('../../../constants/index.js');
const request = require('../../../utils/request.js');
const app = getApp();

/**
 * 员工管理页面 - 租户内部员工管理
 * 功能：员工列表、添加员工、编辑员工、权限管理、考勤统计
 */

Page({
  data: {
    // 用户权限
    userRole: 'manager',
    canManageStaff: false,
    
    // 员工列表
    staffList: [],
    filteredStaffList: [],
    
    // 搜索和筛选
    searchKeyword: '',
    filterRole: 'all',
    filterStatus: 'all',
    
    // 角色选项
    roleOptions: [
      { value: 'all', label: '全部角色' },
      { value: 'admin', label: '管理员' },
      { value: 'manager', label: '经理' },
      { value: 'user', label: '普通员工' }
    ],
    
    // 状态选项
    statusOptions: [
      { value: 'all', label: '全部状态' },
      { value: 'active', label: '在职' },
      { value: 'inactive', label: '离职' },
      { value: 'suspended', label: '停职' }
    ],
    
    // 统计信息
    stats: {
      total: 0,
      active: 0,
      inactive: 0,
      todayAttendance: 0
    },
    
    // 页面状态
    loading: true,
    refreshing: false,
    showAddModal: false,
    showFilterModal: false,
    
    // 新增员工表单
    newStaff: {
      name: '',
      phone: '',
      email: '',
      role: 'user',
      department: '',
      position: ''
    },
    
    // 表单验证
    formErrors: {}
  },

  onLoad: function(options) {
    try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('员工管理页面加载'); } catch(_) {}
    this.checkUserPermission();
    this.loadStaffData();
  },

  onShow: function() {
    // 页面显示时刷新数据
    if (!this.data.loading) {
      this.refreshData();
    }
  },

  // 检查用户权限
  checkUserPermission: function() {
    const userInfo = app.globalData.userInfo || {};
    const userRole = userInfo.role || 'user';
    
    // 检查是否有员工管理权限
    const canManageStaff = ['admin', 'manager'].includes(userRole);
    
    if (!canManageStaff) {
      wx.showModal({
        title: '权限不足',
        content: '您没有员工管理权限',
        showCancel: false,
        complete: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.setData({
      userRole,
      canManageStaff
    });
  },

  // 加载员工数据
  loadStaffData: function() {
    this.setData({ loading: true });
    
    Promise.all([
      this.loadStaffList(),
      this.loadStaffStats()
    ]).then(() => {
      this.setData({ loading: false });
      this.filterStaffList();
    }).catch(error => {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('加载员工数据失败', error); } catch(_) {}
      this.setData({ loading: false });
      this.loadMockData(); // 加载模拟数据
    });
  },

  // 加载员工列表
  loadStaffList: function() {
    return request.get(API.ENDPOINTS.STAFF.LIST)
      .then(response => {
        if (response.success) {
          this.setData({
            staffList: response.data
          });
        }
      });
  },

  // 加载员工统计
  loadStaffStats: function() {
    return request.get(API.ENDPOINTS.STAFF.STATS)
      .then(response => {
        if (response.success) {
          this.setData({
            stats: response.data
          });
        }
      });
  },

  // 加载模拟数据
  loadMockData: function() {
    const mockStaffList = [
      {
        id: 1,
        name: '张三',
        phone: '138****1001',
        email: '<EMAIL>',
        role: 'manager',
        roleLabel: '经理',
        department: '生产部',
        position: '生产经理',
        status: 'active',
        statusLabel: '在职',
        joinDate: '2023-01-15',
        lastActiveTime: '2024-01-15 09:30',
        avatar: '/assets/icons/user.png'
      },
      {
        id: 2,
        name: '李四',
        phone: '138****1002',
        email: '<EMAIL>',
        role: 'user',
        roleLabel: '普通员工',
        department: '饲养部',
        position: '饲养员',
        status: 'active',
        statusLabel: '在职',
        joinDate: '2023-03-20',
        lastActiveTime: '2024-01-15 08:45',
        avatar: '/assets/icons/user.png'
      },
      {
        id: 3,
        name: '王五',
        phone: '138****1003',
        email: '<EMAIL>',
        role: 'user',
        roleLabel: '普通员工',
        department: '健康部',
        position: '兽医',
        status: 'active',
        statusLabel: '在职',
        joinDate: '2023-06-10',
        lastActiveTime: '2024-01-14 17:20',
        avatar: '/assets/icons/user.png'
      }
    ];
    
    const mockStats = {
      total: 12,
      active: 10,
      inactive: 2,
      todayAttendance: 9
    };
    
    this.setData({
      staffList: mockStaffList,
      stats: mockStats
    });
    
    this.filterStaffList();
  },

  // 筛选员工列表
  filterStaffList: function() {
    let filtered = [...this.data.staffList];
    
    // 按关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(staff => 
        staff.name.toLowerCase().includes(keyword) ||
        staff.phone.includes(keyword) ||
        staff.department.toLowerCase().includes(keyword) ||
        staff.position.toLowerCase().includes(keyword)
      );
    }
    
    // 按角色筛选
    if (this.data.filterRole !== 'all') {
      filtered = filtered.filter(staff => staff.role === this.data.filterRole);
    }
    
    // 按状态筛选
    if (this.data.filterStatus !== 'all') {
      filtered = filtered.filter(staff => staff.status === this.data.filterStatus);
    }
    
    this.setData({
      filteredStaffList: filtered
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.filterStaffList();
  },

  // 显示筛选弹窗
  showFilterModal: function() {
    this.setData({
      showFilterModal: true
    });
  },

  // 隐藏筛选弹窗
  hideFilterModal: function() {
    this.setData({
      showFilterModal: false
    });
  },

  // 角色筛选改变
  onRoleFilterChange: function(e) {
    this.setData({
      filterRole: e.detail.value
    });
    this.filterStaffList();
  },

  // 状态筛选改变
  onStatusFilterChange: function(e) {
    this.setData({
      filterStatus: e.detail.value
    });
    this.filterStaffList();
  },

  // 重置筛选
  resetFilter: function() {
    this.setData({
      searchKeyword: '',
      filterRole: 'all',
      filterStatus: 'all',
      showFilterModal: false
    });
    this.filterStaffList();
  },

  // 显示添加员工弹窗
  showAddStaffModal: function() {
    this.setData({
      showAddModal: true,
      newStaff: {
        name: '',
        phone: '',
        email: '',
        role: 'user',
        department: '',
        position: ''
      },
      formErrors: {}
    });
  },

  // 隐藏添加员工弹窗
  hideAddStaffModal: function() {
    this.setData({
      showAddModal: false
    });
  },

  // 表单输入
  onFormInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`newStaff.${field}`]: value,
      [`formErrors.${field}`]: '' // 清除错误
    });
  },

  // 表单验证
  validateForm: function() {
    const { newStaff } = this.data;
    const errors = {};
    
    if (!newStaff.name.trim()) {
      errors.name = '请输入员工姓名';
    }
    
    if (!newStaff.phone.trim()) {
      errors.phone = '请输入手机号码';
    } else if (!/^1[3-9]\d{9}$/.test(newStaff.phone)) {
      errors.phone = '手机号码格式不正确';
    }
    
    if (newStaff.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newStaff.email)) {
      errors.email = '邮箱格式不正确';
    }
    
    if (!newStaff.department.trim()) {
      errors.department = '请输入部门';
    }
    
    if (!newStaff.position.trim()) {
      errors.position = '请输入职位';
    }
    
    this.setData({
      formErrors: errors
    });
    
    return Object.keys(errors).length === 0;
  },

  // 提交添加员工
  submitAddStaff: function() {
    if (!this.validateForm()) {
      return;
    }
    
    wx.showLoading({
      title: '添加中...'
    });
    
    request.post(API.ENDPOINTS.STAFF.CREATE, this.data.newStaff)
      .then(response => {
        wx.hideLoading();
        if (response.success) {
          wx.showToast({
            title: '添加成功',
            icon: 'success'
          });
          this.hideAddStaffModal();
          this.loadStaffData();
        } else {
          wx.showToast({
            title: response.message || '添加失败',
            icon: 'none'
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('添加员工失败', error); } catch(_) {}
        wx.showToast({
          title: '添加失败',
          icon: 'none'
        });
      });
  },

  // 查看员工详情
  viewStaffDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/management/staff/detail/detail?id=${id}`
    });
  },

  // 编辑员工
  editStaff: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/management/staff/edit/edit?id=${id}`
    });
  },

  // 删除员工
  deleteStaff: function(e) {
    const { id, name } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除员工"${name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteStaff(id);
        }
      }
    });
  },

  // 执行删除员工
  performDeleteStaff: function(id) {
    wx.showLoading({
      title: '删除中...'
    });
    
    request.delete(`${API.ENDPOINTS.STAFF.DELETE}/${id}`)
      .then(response => {
        wx.hideLoading();
        if (response.success) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          this.loadStaffData();
        } else {
          wx.showToast({
            title: response.message || '删除失败',
            icon: 'none'
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('删除员工失败', error); } catch(_) {}
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        });
      });
  },

  // 刷新数据
  refreshData: function() {
    this.setData({ refreshing: true });
    this.loadStaffData().finally(() => {
      this.setData({ refreshing: false });
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  }
});