/* 智慧养鹅小程序 - 无障碍体验增强 V1.0 */
/* 符合WCAG 2.1 AA级标准的无障碍设计 */

/* ==================== 色彩对比度优化 ==================== */

/* 高对比度文字颜色 */
.text-high-contrast {
  color: #000000;
  background-color: #ffffff;
}

.text-high-contrast-inverse {
  color: #ffffff;
  background-color: #000000;
}

/* 状态色彩的高对比度版本 */
.status-success-a11y {
  background-color: #0A5D0A;
  color: #ffffff;
  border: 2rpx solid #0A5D0A;
}

.status-warning-a11y {
  background-color: #B8860B;
  color: #ffffff;
  border: 2rpx solid #B8860B;
}

.status-error-a11y {
  background-color: #B91C1C;
  color: #ffffff;
  border: 2rpx solid #B91C1C;
}

.status-info-a11y {
  background-color: #1E40AF;
  color: #ffffff;
  border: 2rpx solid #1E40AF;
}

/* ==================== 焦点状态优化 ==================== */

/* 统一的焦点样式 */
.focus-visible {
  outline: 3rpx solid var(--primary);
  outline-offset: 2rpx;
  box-shadow: 0 0 0 6rpx rgba(0, 102, 204, 0.2);
}

/* 按钮焦点状态 */
button:focus,
.btn:focus,
.btn-primary:focus,
.btn-secondary:focus {
  outline: 3rpx solid var(--primary);
  outline-offset: 2rpx;
  box-shadow: 0 0 0 6rpx rgba(0, 102, 204, 0.2);
}

/* 输入框焦点状态 */
input:focus,
.input:focus,
.search-input:focus,
.symptom-input:focus {
  outline: 3rpx solid var(--primary);
  outline-offset: 2rpx;
  border-color: var(--primary);
  box-shadow: 0 0 0 6rpx rgba(0, 102, 204, 0.2);
}

/* 链接焦点状态 */
.link:focus,
.nav-item:focus {
  outline: 3rpx solid var(--primary);
  outline-offset: 2rpx;
  text-decoration: underline;
  text-decoration-thickness: 2rpx;
}

/* ==================== 跳过链接 ==================== */

/* 跳转到主要内容的链接 */
.skip-link {
  position: absolute;
  top: -80rpx;
  left: var(--space-lg);
  background-color: var(--primary);
  color: var(--text-inverse);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-semibold);
  z-index: 1000;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: var(--space-lg);
  outline: 3rpx solid var(--text-inverse);
  outline-offset: 2rpx;
}

/* ==================== 屏幕阅读器支持 ==================== */

/* 屏幕阅读器专用文本 */
.sr-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 可见的屏幕阅读器文本 */
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: var(--space-sm);
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
  background-color: var(--bg-primary);
  border: 2rpx solid var(--primary);
  border-radius: var(--radius-md);
}

/* ==================== 语义化增强 ==================== */

/* 主要内容区域 */
.main-content {
  min-height: 400rpx;
}

/* 导航区域 */
.navigation[role="navigation"] {
  border-bottom: 2rpx solid var(--border-medium);
  padding-bottom: var(--space-md);
}

/* 页面标题 */
.page-title[role="heading"] {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  outline: none;
}

/* 区域标题 */
.section-title[role="heading"] {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  outline: none;
}

/* ==================== 表单无障碍 ==================== */

/* 表单组 */
.form-group {
  margin-bottom: var(--space-xl);
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.form-label.required::after {
  content: ' *';
  color: var(--error);
  font-weight: var(--font-bold);
}

/* 表单描述 */
.form-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-relaxed);
}

/* 表单错误信息 */
.form-error {
  font-size: var(--text-sm);
  color: var(--error);
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.form-error::before {
  content: '⚠';
  font-size: var(--text-base);
  color: var(--error);
}

/* 表单成功信息 */
.form-success {
  font-size: var(--text-sm);
  color: var(--success);
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.form-success::before {
  content: '✓';
  font-size: var(--text-base);
  color: var(--success);
}

/* ==================== 状态指示器 ==================== */

/* 加载状态 */
.loading-indicator[aria-live="polite"] {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.loading-indicator::before {
  content: '';
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid var(--border-light);
  border-top: 3rpx solid var(--primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

/* 进度指示器 */
.progress-indicator[role="progressbar"] {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  height: 16rpx;
  position: relative;
}

.progress-indicator::after {
  content: attr(aria-valuenow) '%';
  position: absolute;
  top: 20rpx;
  left: 0;
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

/* ==================== 按钮无障碍增强 ==================== */

/* 按钮状态 */
button[disabled],
.btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

button[aria-expanded="true"]::after,
.btn[aria-expanded="true"]::after {
  content: ' (已展开)';
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

button[aria-expanded="false"]::after,
.btn[aria-expanded="false"]::after {
  content: ' (已折叠)';
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

/* ==================== 图标无障碍 ==================== */

/* 装饰性图标 */
.icon[aria-hidden="true"] {
  pointer-events: none;
}

/* 信息性图标 */
.icon[role="img"] {
  display: inline-block;
  vertical-align: middle;
}

/* 图标按钮 */
.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 88rpx;
  min-height: 88rpx;
  border-radius: var(--radius-md);
  background-color: transparent;
  border: 2rpx solid var(--border-medium);
  transition: var(--transition-all);
}

.icon-button:focus {
  outline: 3rpx solid var(--primary);
  outline-offset: 2rpx;
  border-color: var(--primary);
}

.icon-button:active {
  background-color: var(--bg-secondary);
  transform: scale(0.95);
}

/* ==================== 模态框无障碍 ==================== */

/* 模态框遮罩 */
.modal-overlay[role="dialog"] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 模态框内容 */
.modal-content[role="document"] {
  background-color: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin: var(--space-lg);
  max-width: 600rpx;
  width: 100%;
  box-shadow: var(--shadow-xl);
  position: relative;
}

.modal-content:focus {
  outline: 3rpx solid var(--primary);
  outline-offset: -3rpx;
}

/* 模态框标题 */
.modal-title[role="heading"] {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  outline: none;
}

/* ==================== 表格无障碍 ==================== */

/* 表格容器 */
.table-container[role="table"] {
  overflow-x: auto;
  border: 2rpx solid var(--border-medium);
  border-radius: var(--radius-lg);
}

/* 表格标题 */
.table-caption {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: left;
  margin-bottom: var(--space-md);
  padding: var(--space-md) var(--space-lg);
  background-color: var(--bg-secondary);
}

/* 表格头部 */
.table-header[role="rowgroup"] {
  background-color: var(--bg-tertiary);
  border-bottom: 2rpx solid var(--border-medium);
}

.table-header-cell[role="columnheader"] {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  padding: var(--space-md) var(--space-lg);
  text-align: left;
  border-right: 1rpx solid var(--border-light);
}

/* 表格行 */
.table-row[role="row"] {
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: var(--bg-secondary);
}

.table-row:focus-within {
  background-color: var(--primary-subtle);
  outline: 2rpx solid var(--primary);
  outline-offset: -2rpx;
}

/* 表格单元格 */
.table-cell[role="cell"] {
  padding: var(--space-md) var(--space-lg);
  color: var(--text-primary);
  border-right: 1rpx solid var(--border-light);
  vertical-align: top;
}

/* ==================== 通知和提示 ==================== */

/* 实时通知区域 */
.live-region[aria-live="assertive"] {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  background-color: var(--bg-primary);
  border: 2rpx solid var(--primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-lg);
  z-index: 1001;
  max-width: 400rpx;
}

.live-region[aria-live="polite"] {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  background-color: var(--bg-secondary);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  max-width: 400rpx;
}

/* 提示信息 */
.tooltip[role="tooltip"] {
  background-color: var(--text-primary);
  color: var(--text-inverse);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  position: absolute;
  z-index: 1002;
  white-space: nowrap;
  box-shadow: var(--shadow-lg);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -10rpx;
  border-width: 10rpx;
  border-style: solid;
  border-color: var(--text-primary) transparent transparent transparent;
}

/* ==================== 媒体查询 - 用户偏好 ==================== */

/* 减少动画偏好 - 小程序通过类名控制 */
/* @media (prefers-reduced-motion: reduce) { */ /* 小程序不支持 */
.respect-motion-preference {
  animation: none !important;
  transition: none !important;
}

.respect-motion-preference view,
.respect-motion-preference text,
.respect-motion-preference button,
.respect-motion-preference input,
.respect-motion-preference image,
.respect-motion-preference scroll-view {
  animation: none !important;
  transition: none !important;
}
/* } */

/* 高对比度偏好 - 小程序通过类名控制 */
/* @media (prefers-contrast: high) { */ /* 小程序不支持 */
.respect-contrast-preference {
  --text-primary: #000000;
  --text-secondary: #000000;
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --border-light: #000000;
  --border-medium: #000000;
}

.respect-contrast-preference button,
.respect-contrast-preference .btn {
  border: 2rpx solid #000000;
}
/* } */

/* 大字体偏好 - 小程序通过类名控制 */
/* @media (prefers-font-size: large) { */ /* 小程序不支持 */
.respect-font-preference {
  font-size: 36rpx; /* var(--text-base) * 1.2 的计算结果 */
  line-height: 1.65; /* var(--leading-normal) * 1.1 的计算结果 */
}
/* } */

/* ==================== 键盘导航 ==================== */

/* 键盘导航焦点指示器 */
.keyboard-navigation-active view:focus,
.keyboard-navigation-active text:focus,
.keyboard-navigation-active button:focus,
.keyboard-navigation-active input:focus,
.keyboard-navigation-active textarea:focus,
.keyboard-navigation-active picker:focus,
.keyboard-navigation-active slider:focus,
.keyboard-navigation-active switch:focus,
.keyboard-navigation-active checkbox:focus,
.keyboard-navigation-active radio:focus {
  outline: 3rpx solid var(--primary) !important;
  outline-offset: 2rpx !important;
  box-shadow: 0 0 0 6rpx rgba(0, 102, 204, 0.2) !important;
}

/* 隐藏鼠标焦点样式 */
.mouse-navigation-active view:focus,
.mouse-navigation-active text:focus,
.mouse-navigation-active button:focus,
.mouse-navigation-active input:focus,
.mouse-navigation-active textarea:focus,
.mouse-navigation-active picker:focus,
.mouse-navigation-active slider:focus,
.mouse-navigation-active switch:focus,
.mouse-navigation-active checkbox:focus,
.mouse-navigation-active radio:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* ==================== 错误和成功状态 ==================== */

/* 错误状态增强 */
.error-state[aria-invalid="true"] {
  border-color: var(--error) !important;
  background-color: rgba(244, 67, 54, 0.05);
}

.error-state[aria-invalid="true"]:focus {
  outline: 3rpx solid var(--error);
  outline-offset: 2rpx;
  box-shadow: 0 0 0 6rpx rgba(244, 67, 54, 0.2);
}

/* 成功状态增强 */
.success-state[aria-valid="true"] {
  border-color: var(--success) !important;
  background-color: rgba(76, 175, 80, 0.05);
}

.success-state[aria-valid="true"]:focus {
  outline: 3rpx solid var(--success);
  outline-offset: 2rpx;
  box-shadow: 0 0 0 6rpx rgba(76, 175, 80, 0.2);
}

/* ==================== 工具类 ==================== */

/* 无障碍隐藏 */
.a11y-hidden {
  position: absolute !important;
  width: 1rpx !important;
  height: 1rpx !important;
  padding: 0 !important;
  margin: -1rpx !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 高对比度文本 */
.high-contrast-text {
  color: #000000 !important;
  background-color: #ffffff !important;
  border: 2rpx solid #000000 !important;
  padding: var(--space-xs) var(--space-sm) !important;
}

/* 大目标区域 */
.large-target {
  min-width: 88rpx !important;
  min-height: 88rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 焦点陷阱 */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  opacity: 0;
  pointer-events: none;
}