/**
 * 智慧养鹅小程序 - 统一API客户端
 * 基于微信小程序开发规范和SAAS架构设计
 * 
 * 功能特性：
 * 1. 统一请求格式和响应处理
 * 2. 自动Token管理和刷新
 * 3. 租户隔离支持
 * 4. 错误重试机制
 * 5. 请求缓存优化
 * 6. 网络状态感知
 */

const { FirstScreenOptimizer, APIOptimizer } = require('./performance-optimizer.js');

/**
 * 统一API客户端类
 */
class UnifiedAPIClient {
  constructor(options = {}) {
    this.baseURL = options.baseURL || getApp().globalData.baseUrl;
    this.timeout = options.timeout || 10000;
    this.retryTimes = options.retryTimes || 3;
    this.retryDelay = options.retryDelay || 1000;
    
    // 请求拦截器队列
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    
    // 请求去重器
    this.requestDeduplicator = APIOptimizer.createRequestDeduplicator();
    
    // 并发请求控制器
    this.batchRequester = APIOptimizer.createBatchRequester(5);
    
    // 初始化默认拦截器
    this.initDefaultInterceptors();
  }
  
  /**
   * 初始化默认拦截器
   */
  initDefaultInterceptors() {
    // 请求拦截器 - 添加认证头和租户信息
    this.addRequestInterceptor((config) => {
      const token = wx.getStorageSync('access_token');
      if (token) {
        config.header = config.header || {};
        config.header['Authorization'] = `Bearer ${token}`;
      }
      
      // 添加租户ID
      const tenantId = this.getTenantId();
      if (tenantId) {
        config.header = config.header || {};
        config.header['X-Tenant-Id'] = tenantId;
      }
      
      // 添加请求ID用于追踪
      config.header = config.header || {};
      config.header['X-Request-Id'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      return config;
    });
    
    // 响应拦截器 - 统一错误处理
    this.addResponseInterceptor(
      (response) => {
        // 成功响应处理
        if (response.data && response.data.success) {
          return response.data;
        }
        
        // 处理非标准格式响应
        return {
          success: true,
          data: response.data,
          timestamp: new Date().toISOString()
        };
      },
      (error) => {
        // 错误响应处理
        try { const logger = require('./logger.js'); logger.error && logger.error('[API Error]', error); } catch(_) {}
        
        // Token过期处理
        if (error.statusCode === 401) {
          this.handleTokenExpired();
        }
        
        // 租户权限错误
        if (error.statusCode === 403) {
          this.handlePermissionDenied(error);
        }
        
        return Promise.reject(this.formatError(error));
      }
    );
  }
  
  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(fulfilled, rejected) {
    this.requestInterceptors.push({ fulfilled, rejected });
  }
  
  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(fulfilled, rejected) {
    this.responseInterceptors.push({ fulfilled, rejected });
  }
  
  /**
   * 获取租户ID
   */
  getTenantId() {
    const userInfo = wx.getStorageSync('user_info');
    return userInfo?.tenantId || 'default';
  }
  
  /**
   * 处理Token过期
   */
  async handleTokenExpired() {
    try {
      const refreshToken = wx.getStorageSync('refresh_token');
      if (!refreshToken) {
        this.redirectToLogin();
        return;
      }
      
      // 刷新Token
      const response = await this.request({
        url: '/api/v2/auth/refresh',
        method: 'POST',
        data: { refresh_token: refreshToken }
      });
      
      if (response.success) {
        wx.setStorageSync('access_token', response.data.access_token);
        wx.setStorageSync('refresh_token', response.data.refresh_token);
      } else {
        this.redirectToLogin();
      }
    } catch (error) {
      try { const logger = require('./logger.js'); logger.error && logger.error('[Token Refresh Failed]', error); } catch(_) {}
      this.redirectToLogin();
    }
  }
  
  /**
   * 处理权限拒绝
   */
  handlePermissionDenied(error) {
    wx.showModal({
      title: '权限不足',
      content: error.data?.message || '您没有权限执行此操作',
      showCancel: false,
      confirmText: '我知道了'
    });
  }
  
  /**
   * 跳转到登录页
   */
  redirectToLogin() {
    // 清除本地存储
    wx.removeStorageSync('access_token');
    wx.removeStorageSync('refresh_token');
    wx.removeStorageSync('user_info');
    
    // 跳转登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  }
  
  /**
   * 格式化错误信息
   */
  formatError(error) {
    const formattedError = {
      success: false,
      code: error.data?.code || 'UNKNOWN_ERROR',
      message: error.data?.message || '网络请求失败',
      statusCode: error.statusCode,
      timestamp: new Date().toISOString()
    };
    
    // 根据错误类型提供用户友好的提示
    if (error.statusCode === 0 || !error.statusCode) {
      formattedError.message = '网络连接失败，请检查网络设置';
    } else if (error.statusCode === 500) {
      formattedError.message = '服务器异常，请稍后重试';
    } else if (error.statusCode === 404) {
      formattedError.message = '请求的资源不存在';
    }
    
    return formattedError;
  }
  
  /**
   * 核心请求方法
   */
  async request(config) {
    // 应用请求拦截器
    for (const interceptor of this.requestInterceptors) {
      if (interceptor.fulfilled) {
        try {
          config = await interceptor.fulfilled(config);
        } catch (error) {
          if (interceptor.rejected) {
            return interceptor.rejected(error);
          }
          throw error;
        }
      }
    }
    
    // 构建完整URL
    const fullUrl = config.url.startsWith('http') 
      ? config.url 
      : `${this.baseURL}${config.url}`;
    
    // 请求配置
    const requestConfig = {
      url: fullUrl,
      method: config.method || 'GET',
      data: config.data,
      header: {
        'Content-Type': 'application/json',
        ...config.header
      },
      timeout: config.timeout || this.timeout,
      ...config
    };
    
    // 网络状态感知请求
    return APIOptimizer.networkAwareRequest(
      () => this.executeRequest(requestConfig),
      {
        fallbackCache: config.cacheKey,
        retryTimes: config.retryTimes || this.retryTimes
      }
    ).then(response => {
      // 应用响应拦截器
      return this.applyResponseInterceptors(response);
    }).catch(error => {
      // 应用错误拦截器
      return this.applyErrorInterceptors(error);
    });
  }
  
  /**
   * 执行网络请求
   */
  executeRequest(config) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...config,
        success: (res) => {
          resolve(res);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }
  
  /**
   * 应用响应拦截器
   */
  async applyResponseInterceptors(response) {
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.fulfilled) {
        try {
          response = await interceptor.fulfilled(response);
        } catch (error) {
          if (interceptor.rejected) {
            return interceptor.rejected(error);
          }
          throw error;
        }
      }
    }
    return response;
  }
  
  /**
   * 应用错误拦截器
   */
  async applyErrorInterceptors(error) {
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.rejected) {
        try {
          return await interceptor.rejected(error);
        } catch (e) {
          error = e;
        }
      }
    }
    throw error;
  }
  
  /**
   * GET请求
   */
  get(url, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'GET'
    });
  }
  
  /**
   * POST请求
   */
  post(url, data, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'POST',
      data
    });
  }
  
  /**
   * PUT请求
   */
  put(url, data, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'PUT',
      data
    });
  }
  
  /**
   * DELETE请求
   */
  delete(url, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'DELETE'
    });
  }
  
  /**
   * 批量请求
   */
  async batch(requests) {
    const results = await Promise.allSettled(
      requests.map(request => 
        this.batchRequester(() => this.request(request))
      )
    );
    
    return results.map((result, index) => ({
      index,
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null
    }));
  }
  
  /**
   * 缓存请求
   */
  cachedRequest(key, request, maxAge = 5 * 60 * 1000) {
    return FirstScreenOptimizer.smartDataCache(
      key,
      () => this.request(request),
      maxAge
    );
  }
}

// 创建默认实例
const apiClient = new UnifiedAPIClient();

// 业务API模块
const api = {
  // 认证相关
  auth: {
    login: (data) => apiClient.post('/api/v2/auth/login', data),
    register: (data) => apiClient.post('/api/v2/auth/register', data),
    wechatLogin: (data) => apiClient.post('/api/v2/auth/wechat', data),
    refreshToken: (data) => apiClient.post('/api/v2/auth/refresh', data),
    getUserInfo: () => apiClient.get('/api/v2/users/profile')
  },
  
  // 健康管理
  health: {
    getRecords: (params) => apiClient.get('/api/v2/health/records', { data: params }),
    createRecord: (data) => apiClient.post('/api/v2/health/records', data),
    getStats: () => apiClient.cachedRequest('health_stats', { url: '/api/v2/health/stats' }),
    aiDiagnosis: (data) => apiClient.post('/api/v2/health/ai-diagnosis', data)
  },
  
  // 生产管理
  production: {
    getRecords: (params) => apiClient.get('/api/v2/production/records', { data: params }),
    createRecord: (data) => apiClient.post('/api/v2/production/records', data),
    getInventory: () => apiClient.get('/api/v2/production/inventory'),
    updateInventory: (id, data) => apiClient.put(`/api/v2/production/inventory/${id}`, data),
    getEnvironment: () => apiClient.cachedRequest('environment_data', 
      { url: '/api/v2/production/environment' }, 2 * 60 * 1000)
  },
  
  // OA办公
  oa: {
    getStats: () => apiClient.get('/api/v2/oa/stats'),
    getFinanceOverview: () => apiClient.get('/api/v2/oa/finance/overview'),
    createPurchaseApplication: (data) => apiClient.post('/api/v2/oa/purchase/applications', data),
    createReimbursementApplication: (data) => apiClient.post('/api/v2/oa/reimbursement/applications', data),
    getPendingApprovals: () => apiClient.get('/api/v2/oa/approvals/pending')
  },
  
  // 商城
  shop: {
    getProducts: (params) => apiClient.get('/api/v2/shop/products', { data: params }),
    createOrder: (data) => apiClient.post('/api/v2/shop/orders', data),
    getUserOrders: (params) => apiClient.get('/api/v2/shop/orders', { data: params })
  }
};

module.exports = {
  UnifiedAPIClient,
  apiClient,
  api
};