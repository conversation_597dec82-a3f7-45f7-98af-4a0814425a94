<!-- components/form-builder/form-builder.wxml -->
<view class="form-builder {{visible ? 'visible' : 'hidden'}}">
  <view class="form-overlay" bindtap="onClose"></view>
  
  <view class="form-container">
    <!-- 表单头部 -->
    <view class="form-header">
      <text class="form-title">{{title}}</text>
      <view class="close-btn" bindtap="onClose">
        <text class="close-icon">×</text>
      </view>
    </view>

    <!-- 表单内容 -->
    <scroll-view class="form-content" scroll-y="{{true}}">
      <block wx:for="{{fields}}" wx:key="id">
        <view class="form-field">
          <!-- 字段标签 -->
          <view class="field-label">
            <text class="label-text">{{item.label}}</text>
            <text wx:if="{{item.required}}" class="required-mark">*</text>
          </view>

          <!-- 文本输入框 -->
          <view wx:if="{{item.type === 'text' || item.type === 'number' || item.type === 'password'}}" class="field-input">
            <form-field-input
              field="{{item}}"
              bind:change="onFieldChange"
              bind:blur="onFieldBlur">
            </form-field-input>
          </view>

          <!-- 多行文本框 (使用原生textarea) -->
          <view wx:elif="{{item.type === 'textarea'}}" class="field-textarea">
            <textarea
              class="textarea-input"
              placeholder="{{item.placeholder}}"
              value="{{item.value}}"
              maxlength="{{item.maxlength || 500}}"
              show-confirm-bar="{{false}}"
              bindinput="onTextareaInput"
              bindblur="onFieldBlur"
              data-name="{{item.name}}">
            </textarea>
          </view>

          <!-- 选择器 (使用原生picker) -->
          <view wx:elif="{{item.type === 'select'}}" class="field-picker">
            <picker
              mode="selector"
              range="{{item.options}}"
              range-key="{{item.optionKey || 'label'}}"
              value="{{item.selectedIndex || 0}}"
              bindchange="onPickerChange"
              data-name="{{item.name}}">
              <view class="picker-display">
                <text class="picker-text">{{item.displayValue || item.placeholder || '请选择'}}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <!-- 日期选择器 -->
          <view wx:elif="{{item.type === 'date'}}" class="field-picker">
            <picker
              mode="date"
              value="{{item.value}}"
              start="{{item.start}}"
              end="{{item.end}}"
              bindchange="onDateChange"
              data-name="{{item.name}}">
              <view class="picker-display">
                <text class="picker-text">{{item.value || item.placeholder || '请选择日期'}}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <!-- 开关 (使用原生switch) -->
          <view wx:elif="{{item.type === 'switch'}}" class="field-switch">
            <switch
              checked="{{item.value}}"
              bindchange="onSwitchChange"
              data-name="{{item.name}}">
            </switch>
          </view>

          <!-- 显示字段 (只读显示) -->
          <view wx:elif="{{item.type === 'display'}}" class="field-display">
            <view class="display-content">
              <text class="display-text">{{item.value || item.placeholder}}</text>
            </view>
          </view>

          <!-- 字段错误提示 -->
          <view wx:if="{{errors[item.name]}}" class="field-error">
            <text class="error-text">{{errors[item.name]}}</text>
          </view>

          <!-- 字段帮助文本 -->
          <view wx:if="{{item.help}}" class="field-help">
            <text class="help-text">{{item.help}}</text>
          </view>
        </view>
      </block>
    </scroll-view>

    <!-- 表单底部按钮 -->
    <view class="form-footer">
      <button 
        wx:if="{{showCancel}}"
        class="cancel-btn" 
        bindtap="onCancel">
        {{cancelText}}
      </button>
      
      <button 
        class="submit-btn {{submitting ? 'submitting' : ''}}" 
        disabled="{{submitting || !isValid}}"
        bindtap="onSubmit">
        <text wx:if="{{submitting}}">提交中...</text>
        <text wx:else>{{submitText}}</text>
      </button>
    </view>
  </view>
</view>