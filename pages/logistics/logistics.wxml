<!-- pages/logistics/logistics.wxml -->
<view class="logistics-container">
  <!-- 物流信息头部 -->
  <view class="logistics-header">
    <view class="logistics-status">
      <image class="status-icon" src="/images/icons/truck.svg" mode="aspectFit"></image>
      <view class="status-info">
        <text class="status-text">{{logisticsInfo.status}}</text>
        <text class="status-desc">{{logisticsInfo.statusDesc}}</text>
      </view>
    </view>
    <view class="logistics-company">
      <text class="company-name">{{logisticsInfo.company}}</text>
      <text class="tracking-number">运单号：{{logisticsInfo.trackingNumber}}</text>
    </view>
  </view>

  <!-- 物流进度 -->
  <view class="logistics-progress">
    <view class="progress-title">物流进度</view>
    <view class="progress-list">
      <block wx:for="{{logisticsInfo.progress}}" wx:key="id">
        <view class="progress-item {{index === 0 ? 'current' : ''}}">
          <view class="progress-dot"></view>
          <view class="progress-line" wx:if="{{index < logisticsInfo.progress.length - 1}}"></view>
          <view class="progress-content">
            <view class="progress-time">{{item.time}}</view>
            <view class="progress-desc">{{item.description}}</view>
            <view class="progress-location" wx:if="{{item.location}}">{{item.location}}</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-info-section">
    <view class="section-title">订单信息</view>
    <view class="order-details">
      <view class="detail-row">
        <text class="detail-label">订单号：</text>
        <text class="detail-value">{{orderInfo.orderNumber}}</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">下单时间：</text>
        <text class="detail-value">{{orderInfo.createTime}}</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">收货地址：</text>
        <text class="detail-value">{{orderInfo.address}}</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">收货人：</text>
        <text class="detail-value">{{orderInfo.receiver}} {{orderInfo.phone}}</text>
      </view>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="goods-info-section">
    <view class="section-title">商品信息</view>
    <view class="goods-list">
      <block wx:for="{{orderInfo.goods}}" wx:key="id">
        <view class="goods-item">
          <image class="goods-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="goods-details">
            <text class="goods-name">{{item.name}}</text>
            <view class="goods-price-qty">
              <text class="goods-price">¥{{item.price}}</text>
              <text class="goods-qty">x{{item.quantity}}</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="action-btn copy-btn" bindtap="onCopyTrackingNumber">复制运单号</button>
    <button class="action-btn contact-btn" bindtap="onContactService">联系客服</button>
  </view>
</view>
