/**
 * 企业级生产管理系统 JavaScript
 * 功能：生产记录管理、数据分析、图表展示
 */

class ProductionManagement {
  constructor() {
    this.currentPage = 1;
    this.pageSize = 10;
    this.sortBy = "recordedDate";
    this.sortOrder = "DESC";
    this.filters = {
      search: "",
      flockId: "",
      dateRange: "",
      healthStatus: "",
    };
    this.selectedRecords = new Set();
    this.records = [];
    this.totalCount = 0;
    this.charts = {};

    this.init();
  }

  init() {
    this.bindEvents();
    this.loadProductionRecords();
    this.loadStats();
    this.initCharts();
    this.loadFlockOptions();
  }

  bindEvents() {
    // 搜索
    const searchInput = document.getElementById("searchInput");
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener("input", (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.filters.search = e.target.value;
          this.currentPage = 1;
          this.loadProductionRecords();
        }, 300);
      });
    }

    // 筛选器
    document.getElementById("flockFilter")?.addEventListener("change", (e) => {
      this.filters.flockId = e.target.value;
      this.currentPage = 1;
      this.loadProductionRecords();
    });

    document
      .getElementById("dateRangeFilter")
      ?.addEventListener("change", (e) => {
        this.filters.dateRange = e.target.value;
        this.currentPage = 1;
        this.loadProductionRecords();
      });

    document.getElementById("healthFilter")?.addEventListener("change", (e) => {
      this.filters.healthStatus = e.target.value;
      this.currentPage = 1;
      this.loadProductionRecords();
    });

    // 分页大小
    document
      .getElementById("pageSizeSelect")
      ?.addEventListener("change", (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadProductionRecords();
      });

    // 全选
    document.getElementById("selectAll")?.addEventListener("change", (e) => {
      this.toggleSelectAll(e.target.checked);
    });

    // 排序
    document.querySelectorAll(".sortable").forEach((th) => {
      th.addEventListener("click", () => {
        const sortField = th.dataset.sort;
        if (this.sortBy === sortField) {
          this.sortOrder = this.sortOrder === "ASC" ? "DESC" : "ASC";
        } else {
          this.sortBy = sortField;
          this.sortOrder = "DESC";
        }
        this.updateSortIcons();
        this.loadProductionRecords();
      });
    });

    // 图表时间范围
    document.getElementById("trendPeriod")?.addEventListener("change", (e) => {
      this.updateTrendChart(e.target.value);
    });
  }

  async loadProductionRecords() {
    try {
      this.showLoading();

      const params = new URLSearchParams({
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder,
        ...this.filters,
      });

      const response = await fetch(`/api/production/records?${params}`);
      const result = await response.json();

      if (result.success) {
        this.records = result.data.items;
        this.totalCount = result.data.pagination.total;
        this.renderRecords();
        this.renderPagination(result.data.pagination);
        this.updatePaginationInfo(result.data.pagination);
      } else {
        this.showError(result.message || "加载生产记录失败");
      }
    } catch (error) {
      console.error("加载生产记录失败:", error);
      this.showError("网络错误，请稍后重试");
    } finally {
      this.hideLoading();
    }
  }

  async loadStats() {
    try {
      const response = await fetch("/api/production/stats");
      const result = await response.json();

      if (result.success) {
        this.updateStats(result.data);
      }
    } catch (error) {
      console.error("加载统计数据失败:", error);
    }
  }

  async loadFlockOptions() {
    try {
      const response = await fetch("/api/flocks");
      const result = await response.json();

      if (result.success) {
        const flockFilter = document.getElementById("flockFilter");
        if (flockFilter) {
          const options = result.data.items
            .map(
              (flock) =>
                `<option value="${flock.id}">${flock.name} (${flock.batchNumber})</option>`,
            )
            .join("");
          flockFilter.innerHTML =
            '<option value="">全部鹅群</option>' + options;
        }
      }
    } catch (error) {
      console.error("加载鹅群选项失败:", error);
    }
  }

  renderRecords() {
    const tbody = document.getElementById("productionTableBody");
    if (!tbody) return;

    if (this.records.length === 0) {
      this.showEmpty();
      return;
    }

    tbody.innerHTML = this.records
      .map(
        (record) => `
            <tr data-record-id="${record.id}">
                <td>
                    <div class="form-check">
                        <input class="form-check-input record-checkbox" type="checkbox" 
                               value="${record.id}" onchange="productionManager.toggleRecordSelection(${record.id}, this.checked)">
                    </div>
                </td>
                <td>
                    <div class="record-date">
                        <strong>${this.formatDate(record.recordedDate)}</strong>
                        <small class="text-muted d-block">${this.formatTime(record.recordedDate)}</small>
                    </div>
                </td>
                <td>
                    <div class="flock-info">
                        <span class="flock-name">${record.flockName || "未知鹅群"}</span>
                        <small class="text-muted d-block">${record.batchNumber || ""}</small>
                    </div>
                </td>
                <td>
                    <div class="egg-count">
                        <span class="count-value">${record.eggCount}</span>
                        <small class="text-success">枚</small>
                    </div>
                </td>
                <td>
                    <div class="egg-weight">
                        ${record.eggWeight ? `${record.eggWeight}g` : "-"}
                    </div>
                </td>
                <td>
                    <div class="feed-consumption">
                        <span class="consumption-value">${record.feedConsumption || 0}</span>
                        <small class="text-warning">kg</small>
                    </div>
                </td>
                <td>
                    <div class="environment-info">
                        <small class="d-block">
                            <i class="bi bi-thermometer-half text-danger"></i> 
                            ${record.temperature || "-"}°C
                        </small>
                        <small class="d-block">
                            <i class="bi bi-droplet text-info"></i> 
                            ${record.humidity || "-"}%
                        </small>
                    </div>
                </td>
                <td>
                    <div class="mortality-count">
                        ${
                          record.mortalityCount
                            ? `<span class="text-danger">${record.mortalityCount}</span>`
                            : '<span class="text-success">0</span>'
                        }
                    </div>
                </td>
                <td>
                    <span class="health-status ${record.healthStatus}">${this.getHealthStatusText(record.healthStatus)}</span>
                </td>
                <td>
                    <div class="production-rate">
                        <span class="rate-value">${this.calculateProductionRate(record)}%</span>
                        <div class="rate-bar">
                            <div class="rate-fill" style="width: ${this.calculateProductionRate(record)}%"></div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="conversion-ratio">
                        ${this.calculateConversionRatio(record)}
                    </div>
                </td>
                <td>
                    <div class="record-notes">
                        ${
                          record.notes
                            ? record.notes.length > 20
                              ? record.notes.substring(0, 20) + "..."
                              : record.notes
                            : "-"
                        }
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm btn-outline-primary btn-action" 
                                onclick="productionManager.viewRecord(${record.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success btn-action" 
                                onclick="productionManager.editRecord(${record.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                onclick="productionManager.deleteRecord(${record.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `,
      )
      .join("");

    this.hideEmpty();
    this.hideError();
  }

  initCharts() {
    this.initTrendChart();
    this.initDistributionChart();
  }

  initTrendChart() {
    const ctx = document.getElementById("productionTrendChart");
    if (!ctx) return;

    this.charts.trend = new Chart(ctx, {
      type: "line",
      data: {
        labels: [],
        datasets: [
          {
            label: "产蛋数量",
            data: [],
            borderColor: "#3b82f6",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            tension: 0.4,
          },
          {
            label: "饲料消耗",
            data: [],
            borderColor: "#f59e0b",
            backgroundColor: "rgba(245, 158, 11, 0.1)",
            tension: 0.4,
            yAxisID: "y1",
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            type: "linear",
            display: true,
            position: "left",
            title: {
              display: true,
              text: "产蛋数量",
            },
          },
          y1: {
            type: "linear",
            display: true,
            position: "right",
            title: {
              display: true,
              text: "饲料消耗(kg)",
            },
            grid: {
              drawOnChartArea: false,
            },
          },
        },
      },
    });

    this.updateTrendChart(30);
  }

  initDistributionChart() {
    const ctx = document.getElementById("flockDistributionChart");
    if (!ctx) return;

    this.charts.distribution = new Chart(ctx, {
      type: "doughnut",
      data: {
        labels: [],
        datasets: [
          {
            data: [],
            backgroundColor: [
              "#3b82f6",
              "#10b981",
              "#f59e0b",
              "#ef4444",
              "#8b5cf6",
              "#06b6d4",
            ],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });

    this.updateDistributionChart();
  }

  async updateTrendChart(days = 30) {
    try {
      const response = await fetch(`/api/production/trends?days=${days}`);
      const result = await response.json();

      if (result.success && this.charts.trend) {
        const data = result.data;
        this.charts.trend.data.labels = data.labels;
        this.charts.trend.data.datasets[0].data = data.eggCounts;
        this.charts.trend.data.datasets[1].data = data.feedConsumption;
        this.charts.trend.update();
      }
    } catch (error) {
      console.error("更新趋势图表失败:", error);
    }
  }

  async updateDistributionChart() {
    try {
      const response = await fetch("/api/production/distribution");
      const result = await response.json();

      if (result.success && this.charts.distribution) {
        const data = result.data;
        this.charts.distribution.data.labels = data.labels;
        this.charts.distribution.data.datasets[0].data = data.values;
        this.charts.distribution.update();
      }
    } catch (error) {
      console.error("更新分布图表失败:", error);
    }
  }

  // 工具方法
  getHealthStatusText(status) {
    const statusMap = {
      normal: "正常",
      warning: "警告",
      critical: "严重",
    };
    return statusMap[status] || status;
  }

  calculateProductionRate(record) {
    // 简化的产蛋率计算，实际应该基于鹅群数量
    return Math.min(100, Math.round((record.eggCount / 100) * 100));
  }

  calculateConversionRatio(record) {
    if (!record.feedConsumption || !record.eggCount) return "-";
    return (record.feedConsumption / record.eggCount).toFixed(2);
  }

  formatDate(dateString) {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN");
  }

  formatTime(dateString) {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  updateStats(stats) {
    document.getElementById("todayEggs").textContent = stats.todayEggs || 0;
    document.getElementById("productionRate").textContent =
      (stats.productionRate || 0) + "%";
    document.getElementById("feedConsumption").textContent =
      stats.feedConsumption || 0;
    document.getElementById("conversionRatio").textContent =
      stats.conversionRatio || 0;
  }

  // 状态管理方法
  showLoading() {
    document
      .getElementById("loadingState")
      ?.style.setProperty("display", "flex");
    document.getElementById("emptyState")?.style.setProperty("display", "none");
    document.getElementById("errorState")?.style.setProperty("display", "none");
  }

  hideLoading() {
    document
      .getElementById("loadingState")
      ?.style.setProperty("display", "none");
  }

  showEmpty() {
    document.getElementById("emptyState")?.style.setProperty("display", "flex");
    document
      .getElementById("loadingState")
      ?.style.setProperty("display", "none");
    document.getElementById("errorState")?.style.setProperty("display", "none");
    document.getElementById("productionTableBody").innerHTML = "";
  }

  hideEmpty() {
    document.getElementById("emptyState")?.style.setProperty("display", "none");
  }

  showError(message) {
    const errorState = document.getElementById("errorState");
    const errorMessage = document.getElementById("errorMessage");
    if (errorState && errorMessage) {
      errorMessage.textContent = message;
      errorState.style.setProperty("display", "flex");
      document
        .getElementById("loadingState")
        ?.style.setProperty("display", "none");
      document
        .getElementById("emptyState")
        ?.style.setProperty("display", "none");
    }
  }

  hideError() {
    document.getElementById("errorState")?.style.setProperty("display", "none");
  }
}

// 全局实例
let productionManager;

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", function () {
  productionManager = new ProductionManagement();
});

// 全局函数
function showCreateRecordModal() {
  console.log("显示创建生产记录模态框");
}

function resetFilters() {
  document.getElementById("searchInput").value = "";
  document.getElementById("flockFilter").value = "";
  document.getElementById("dateRangeFilter").value = "";
  document.getElementById("healthFilter").value = "";

  productionManager.filters = {
    search: "",
    flockId: "",
    dateRange: "",
    healthStatus: "",
  };
  productionManager.currentPage = 1;
  productionManager.loadProductionRecords();
}

function exportProduction() {
  console.log("导出生产数据");
}
