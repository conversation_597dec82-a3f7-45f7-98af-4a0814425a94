// pages/more/more.js
const logger = require('../../utils/logger.js');

Page({
  data: {
    userInfo: {
      name: '张养殖户',
      role: '养殖场主',
      farmName: '绿野生态养殖场',
      avatar: '/images/default_avatar.png',
      totalDays: 365,
      totalRecords: 1250
    }
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  onShow: function () {
    // 页面显示时刷新用户信息
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: {
          ...this.data.userInfo,
          ...userInfo
        }
      });
    }

    // 计算使用天数
    const firstUseDate = wx.getStorageSync('firstUseDate');
    if (firstUseDate) {
      const now = new Date();
      const firstDate = new Date(firstUseDate);
      const diffTime = Math.abs(now - firstDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      this.setData({
        'userInfo.totalDays': diffDays
      });
    } else {
      // 首次使用，记录当前时间
      wx.setStorageSync('firstUseDate', new Date().toISOString());
      this.setData({
        'userInfo.totalDays': 1
      });
    }

    // 统计记录数量
    this.calculateTotalRecords();
  },

  // 计算总记录数
  calculateTotalRecords() {
    let totalRecords = 0;
    
    // 统计健康记录
    const healthRecords = wx.getStorageSync('healthRecords') || [];
    totalRecords += healthRecords.length;
    
    // 统计生产记录
    const productionRecords = wx.getStorageSync('productionRecords') || [];
    totalRecords += productionRecords.length;
    
    // 统计财务记录
    const financeRecords = wx.getStorageSync('financeRecords') || [];
    totalRecords += financeRecords.length;
    
    this.setData({
      'userInfo.totalRecords': totalRecords
    });
  },

  // 点击功能项
  onFunctionTap(e) {
    const url = e.currentTarget.dataset.url;
    
    if (!url) {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      });
      return;
    }

    // 检查页面是否存在
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    try {
      wx.navigateTo({
        url: url,
        fail: (err) => {
          logger.error('页面跳转失败:', err);
          // 实现快捷功能
          this.handleQuickFunction(url);
        }
      });
    } catch (error) {
      logger.error('跳转异常:', error);
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '智慧养鹅管理系统',
      desc: '专业的养鹅管理解决方案',
      path: '/pages/home/<USER>'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '智慧养鹅管理系统',
      query: '',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // ==================== 新增功能实现 ====================

  // 处理工具功能
  handleToolFunction: function(tool) {
    if (!tool) return;
    
    const toolActions = {
      calculator: () => this.showCalculator(),
      calendar: () => this.showCalendar(),
      notepad: () => this.showNotepad(),
      weather: () => this.showWeather(),
      converter: () => this.showConverter(),
      backup: () => this.showDataBackup()
    };

    const action = toolActions[tool.id] || toolActions[tool];
    if (action) {
      action();
    } else {
      wx.showModal({
        title: '工具功能',
        content: `${tool.name || tool}工具功能正在为您准备中，即将为您提供便捷的辅助功能。`,
        showCancel: false
      });
    }
  },

  // 处理快捷功能
  handleQuickFunction: function(item) {
    if (!item) return;
    
    // 基于URL判断功能类型
    if (typeof item === 'string') {
      if (item.includes('scan')) {
        this.showScanQR();
      } else if (item.includes('voice')) {
        this.showVoiceInput();
      } else if (item.includes('location')) {
        this.showLocationService();
      } else if (item.includes('camera')) {
        this.showCameraFunction();
      } else {
        wx.showModal({
          title: '功能说明',
          content: '该功能页面正在开发中，敬请期待更好的使用体验。',
          showCancel: false
        });
      }
      return;
    }

    const quickActions = {
      scan: () => this.showScanQR(),
      voice: () => this.showVoiceInput(),
      location: () => this.showLocationService(),
      camera: () => this.showCameraFunction(),
      file: () => this.showFileManager()
    };

    const action = quickActions[item.id] || quickActions[item];
    if (action) {
      action();
    } else {
      wx.showModal({
        title: '快捷功能',
        content: `${item.name || item}功能正在完善中，将为您提供更便捷的操作体验。`,
        showCancel: false
      });
    }
  },

  // 显示计算器
  showCalculator: function() {
    wx.showModal({
      title: '计算器',
      content: '智能计算器功能：\n• 基础数学运算\n• 科学计算功能\n• 单位换算工具\n• 历史记录查看',
      showCancel: true,
      confirmText: '打开计算器'
    });
  },

  // 显示日历
  showCalendar: function() {
    wx.showModal({
      title: '日历工具',
      content: '智能日历功能：\n• 日期查询工具\n• 节假日提醒\n• 重要事件标记\n• 生产计划安排',
      showCancel: true,
      confirmText: '查看日历'
    });
  },

  // 显示记事本
  showNotepad: function() {
    wx.showModal({
      title: '记事本',
      content: '便签记事功能：\n• 快速记录想法\n• 待办事项管理\n• 重要信息备忘\n• 同步云端存储',
      showCancel: true,
      confirmText: '打开记事本'
    });
  },

  // 显示天气
  showWeather: function() {
    wx.showModal({
      title: '天气预报',
      content: '智能天气服务：\n• 实时天气信息\n• 7天天气预报\n• 农业气象提醒\n• 养殖环境建议',
      showCancel: true,
      confirmText: '查看天气'
    });
  },

  // 显示单位转换器
  showConverter: function() {
    wx.showModal({
      title: '单位转换器',
      content: '万能转换工具：\n• 重量单位转换\n• 长度单位转换\n• 容量单位转换\n• 温度单位转换',
      showCancel: true,
      confirmText: '使用转换器'
    });
  },

  // 显示数据备份
  showDataBackup: function() {
    wx.showModal({
      title: '数据备份',
      content: '数据安全保障：\n• 自动数据备份\n• 云端数据同步\n• 数据恢复功能\n• 备份进度查看',
      showCancel: true,
      confirmText: '管理备份'
    });
  },

  // 显示扫码功能
  showScanQR: function() {
    wx.scanCode({
      success: (res) => {
        wx.showModal({
          title: '扫码结果',
          content: `扫描结果：${res.result}`,
          showCancel: false
        });
      },
      fail: () => {
        wx.showToast({
          title: '扫码已取消',
          icon: 'none'
        });
      }
    });
  },

  // 显示语音输入
  showVoiceInput: function() {
    wx.showModal({
      title: '语音输入',
      content: '语音识别功能：\n• 语音转文字\n• 智能语音命令\n• 多语言识别\n• 离线语音支持',
      showCancel: true,
      confirmText: '开始录音'
    });
  },

  // 显示位置服务
  showLocationService: function() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        wx.showModal({
          title: '位置信息',
          content: `当前位置：\n纬度：${res.latitude.toFixed(6)}\n经度：${res.longitude.toFixed(6)}`,
          showCancel: false
        });
      },
      fail: () => {
        wx.showToast({
          title: '位置获取失败',
          icon: 'none'
        });
      }
    });
  },

  // 显示相机功能
  showCameraFunction: function() {
    wx.chooseImage({
      count: 1,
      sourceType: ['camera'],
      success: (res) => {
        wx.showModal({
          title: '拍照完成',
          content: '照片已保存，您可以在相册中查看或进行进一步处理。',
          showCancel: false
        });
      }
    });
  },

  // 显示文件管理器
  showFileManager: function() {
    wx.showModal({
      title: '文件管理器',
      content: '文件管理功能：\n• 本地文件浏览\n• 文件分类管理\n• 云端文件同步\n• 文件分享功能',
      showCancel: true,
      confirmText: '打开文件管理器'
    });
  }
});
