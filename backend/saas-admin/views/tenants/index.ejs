<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧养鹅SaaS平台 - 租户管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #212529);
            color: white;
            height: 100vh;
            position: fixed;
            overflow-y: auto;
        }
        
        .main-content {
            margin-left: 250px;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: none;
            transition: transform 0.3s ease;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.7);
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        
        .pagination .page-link {
            color: var(--primary-color);
        }
        
        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <%- include('partials/sidebar') %>
            
            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 顶部导航 -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0">租户管理</h5>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#createTenantModal">
                                <i class="bi bi-plus-circle me-1"></i>新增租户
                            </button>
                            <span class="badge bg-success me-3">在线</span>
                            <i class="bi bi-bell fs-5 me-3"></i>
                            <i class="bi bi-gear fs-5"></i>
                        </div>
                    </div>
                </nav>
                
                <!-- 筛选和搜索区域 -->
                <div class="container-fluid mt-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <form id="filterForm">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">搜索</label>
                                        <input type="text" class="form-control" name="search" placeholder="公司名称/租户代码/联系人" 
                                            value="<%= typeof search !== 'undefined' ? search : '' %>">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" name="status">
                                            <option value="">全部</option>
                                            <option value="active" <%= typeof status !== 'undefined' && status === 'active' ? 'selected' : '' %>>活跃</option>
                                            <option value="pending" <%= typeof status !== 'undefined' && status === 'pending' ? 'selected' : '' %>>待审核</option>
                                            <option value="suspended" <%= typeof status !== 'undefined' && status === 'suspended' ? 'selected' : '' %>>已暂停</option>
                                            <option value="trial" <%= typeof status !== 'undefined' && status === 'trial' ? 'selected' : '' %>>试用</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">订阅计划</label>
                                        <select class="form-select" name="subscriptionPlan">
                                            <option value="">全部</option>
                                            <option value="trial" <%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan === 'trial' ? 'selected' : '' %>>试用版</option>
                                            <option value="basic" <%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan === 'basic' ? 'selected' : '' %>>基础版</option>
                                            <option value="standard" <%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan === 'standard' ? 'selected' : '' %>>标准版</option>
                                            <option value="premium" <%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan === 'premium' ? 'selected' : '' %>>高级版</option>
                                            <option value="enterprise" <%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan === 'enterprise' ? 'selected' : '' %>>企业版</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">规模</label>
                                        <select class="form-select" name="scale">
                                            <option value="">全部</option>
                                            <option value="small" <%= typeof scale !== 'undefined' && scale === 'small' ? 'selected' : '' %>>小型</option>
                                            <option value="medium" <%= typeof scale !== 'undefined' && scale === 'medium' ? 'selected' : '' %>>中型</option>
                                            <option value="large" <%= typeof scale !== 'undefined' && scale === 'large' ? 'selected' : '' %>>大型</option>
                                            <option value="enterprise" <%= typeof scale !== 'undefined' && scale === 'enterprise' ? 'selected' : '' %>>企业级</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="bi bi-search me-1"></i>搜索
                                        </button>
                                        <a href="/saas-admin/tenants" class="btn btn-outline-secondary">
                                            <i class="bi bi-x-circle me-1"></i>重置
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- 租户列表 -->
                    <div class="card">
                        <div class="card-header bg-white">
                            <h6 class="mb-0">租户列表</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>公司名称</th>
                                            <th>租户代码</th>
                                            <th>状态</th>
                                            <th>订阅计划</th>
                                            <th>规模</th>
                                            <th>联系人</th>
                                            <th>用户数</th>
                                            <th>最后活跃</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% if (typeof tenants !== 'undefined' && tenants.length > 0) { %>
                                            <% tenants.forEach(function(tenant) { %>
                                                <tr>
                                                    <td><%= tenant.companyName %></td>
                                                    <td><%= tenant.tenantCode %></td>
                                                    <td>
                                                        <% if (tenant.status === 'active') { %>
                                                            <span class="status-badge bg-success">活跃</span>
                                                        <% } else if (tenant.status === 'pending') { %>
                                                            <span class="status-badge bg-warning">待审核</span>
                                                        <% } else if (tenant.status === 'suspended') { %>
                                                            <span class="status-badge bg-danger">已暂停</span>
                                                        <% } else if (tenant.status === 'trial') { %>
                                                            <span class="status-badge bg-info">试用</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if (tenant.subscriptionPlan === 'trial') { %>
                                                            试用版
                                                        <% } else if (tenant.subscriptionPlan === 'basic') { %>
                                                            基础版
                                                        <% } else if (tenant.subscriptionPlan === 'standard') { %>
                                                            标准版
                                                        <% } else if (tenant.subscriptionPlan === 'premium') { %>
                                                            高级版
                                                        <% } else if (tenant.subscriptionPlan === 'enterprise') { %>
                                                            企业版
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if (tenant.scale === 'small') { %>
                                                            小型
                                                        <% } else if (tenant.scale === 'medium') { %>
                                                            中型
                                                        <% } else if (tenant.scale === 'large') { %>
                                                            大型
                                                        <% } else if (tenant.scale === 'enterprise') { %>
                                                            企业级
                                                        <% } %>
                                                    </td>
                                                    <td><%= tenant.contactPerson %></td>
                                                    <td><%= tenant.userCount %></td>
                                                    <td><%= tenant.lastActiveAt ? new Date(tenant.lastActiveAt).toLocaleDateString('zh-CN') : '-' %></td>
                                                    <td>
                                                        <a href="/saas-admin/tenants/<%= tenant.id %>" class="btn btn-sm btn-outline-primary">
                                                            <i class="bi bi-eye"></i> 查看
                                                        </a>
                                                    </td>
                                                </tr>
                                            <% }); %>
                                        <% } else { %>
                                            <tr>
                                                <td colspan="9" class="text-center">暂无数据</td>
                                            </tr>
                                        <% } %>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <% if (typeof pagination !== 'undefined' && pagination.pagination.pages > 1) { %>
                                <nav>
                                    <ul class="pagination justify-content-center">
                                        <% if (pagination.pagination.hasPrev) { %>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<%= pagination.pagination.page - 1 %>&limit=<%= pagination.pagination.limit %><%= typeof search !== 'undefined' && search ? '&search=' + search : '' %><%= typeof status !== 'undefined' && status ? '&status=' + status : '' %><%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan ? '&subscriptionPlan=' + subscriptionPlan : '' %><%= typeof scale !== 'undefined' && scale ? '&scale=' + scale : '' %>">上一页</a>
                                            </li>
                                        <% } %>
                                        
                                        <% for (let i = 1; i <= pagination.pagination.pages; i++) { %>
                                            <li class="page-item <%= i === pagination.pagination.page ? 'active' : '' %>">
                                                <a class="page-link" href="?page=<%= i %>&limit=<%= pagination.pagination.limit %><%= typeof search !== 'undefined' && search ? '&search=' + search : '' %><%= typeof status !== 'undefined' && status ? '&status=' + status : '' %><%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan ? '&subscriptionPlan=' + subscriptionPlan : '' %><%= typeof scale !== 'undefined' && scale ? '&scale=' + scale : '' %>">
                                                    <%= i %>
                                                </a>
                                            </li>
                                        <% } %>
                                        
                                        <% if (pagination.pagination.hasNext) { %>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<%= pagination.pagination.page + 1 %>&limit=<%= pagination.pagination.limit %><%= typeof search !== 'undefined' && search ? '&search=' + search : '' %><%= typeof status !== 'undefined' && status ? '&status=' + status : '' %><%= typeof subscriptionPlan !== 'undefined' && subscriptionPlan ? '&subscriptionPlan=' + subscriptionPlan : '' %><%= typeof scale !== 'undefined' && scale ? '&scale=' + scale : '' %>">下一页</a>
                                            </li>
                                        <% } %>
                                    </ul>
                                </nav>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增租户模态框 -->
    <div class="modal fade" id="createTenantModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新增租户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createTenantForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">公司名称 *</label>
                                <input type="text" class="form-control" name="companyName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">租户代码 *</label>
                                <input type="text" class="form-control" name="tenantCode" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">订阅计划 *</label>
                                <select class="form-select" name="subscriptionPlan" required>
                                    <option value="">请选择</option>
                                    <option value="trial">试用版</option>
                                    <option value="basic">基础版</option>
                                    <option value="standard">标准版</option>
                                    <option value="premium">高级版</option>
                                    <option value="enterprise">企业版</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">规模 *</label>
                                <select class="form-select" name="scale" required>
                                    <option value="">请选择</option>
                                    <option value="small">小型</option>
                                    <option value="medium">中型</option>
                                    <option value="large">大型</option>
                                    <option value="enterprise">企业级</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">联系人 *</label>
                                <input type="text" class="form-control" name="contactPerson" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">联系电话 *</label>
                                <input type="text" class="form-control" name="contactPhone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">联系邮箱</label>
                                <input type="email" class="form-control" name="contactEmail">
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">地址</label>
                                <textarea class="form-control" name="address" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="saveTenantBtn">
                        <i class="bi bi-save me-1"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单提交处理
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const params = new URLSearchParams();
            
            for (const [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }
            
            window.location.search = params.toString();
        });
        
        // 保存租户
        document.getElementById('saveTenantBtn').addEventListener('click', function() {
            const form = document.getElementById('createTenantForm');
            const formData = new FormData(form);
            
            // 验证必填字段
            let isValid = true;
            form.querySelectorAll('[required]').forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 收集表单数据
            const data = {};
            for (const [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // 发送请求
            fetch('/saas-admin/tenants', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('租户创建成功');
                    window.location.reload();
                } else {
                    alert('创建失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('创建失败，请稍后重试');
            });
        });
    </script>
</body>
</html>