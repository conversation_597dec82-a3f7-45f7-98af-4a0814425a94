/**
 * AI服务请求封装
 * 支持多个AI服务商的统一调用接口
 */

const { getAIConfig } = require('./ai-config.js');
let logger;
try { logger = require('./logger.js'); } catch (_) { logger = console; }

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1秒
  backoffMultiplier: 2
};

// 错误类型
const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  RATE_LIMIT: 'RATE_LIMIT',
  INVALID_REQUEST: 'INVALID_REQUEST',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR'
};

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 错误分类
 */
function classifyError(error, statusCode) {
  if (!statusCode) return ERROR_TYPES.NETWORK_ERROR;
  
  switch (statusCode) {
    case 401:
    case 403:
      return ERROR_TYPES.AUTHENTICATION_ERROR;
    case 429:
      return ERROR_TYPES.RATE_LIMIT;
    case 400:
      return ERROR_TYPES.INVALID_REQUEST;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_TYPES.API_ERROR;
    default:
      return ERROR_TYPES.API_ERROR;
  }
}

/**
 * 硅基流动API调用 - 兼容OpenAI格式
 */
async function callSiliconFlowAPI(config, messages, options = {}) {
  const requestData = {
    model: config.model,
    messages: messages,
    max_tokens: options.maxTokens || config.maxTokens,
    temperature: options.temperature || config.temperature,
    stream: false
  };

  const requestOptions = {
    url: `${config.baseUrl}/chat/completions`,
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`
    },
    data: requestData,
    timeout: 30000
  };

  return new Promise((resolve, reject) => {
    wx.request({
      ...requestOptions,
      success: (res) => {
        try { logger.debug && logger.debug('硅基流动API响应', { statusCode: res.statusCode }); } catch(_) {}
        if (res.statusCode === 200 && res.data.choices && res.data.choices.length > 0) {
          resolve({
            content: res.data.choices[0].message.content,
            usage: res.data.usage,
            model: res.data.model,
            provider: 'SiliconFlow'
          });
        } else {
          reject({
            type: classifyError(res.data, res.statusCode),
            message: res.data.error?.message || '硅基流动API请求失败',
            statusCode: res.statusCode,
            data: res.data
          });
        }
      },
      fail: (err) => {
        try { logger.error && logger.error('硅基流动API网络错误', err); } catch(_) {}
        reject({
          type: ERROR_TYPES.NETWORK_ERROR,
          message: err.errMsg || '硅基流动网络请求失败',
          error: err
        });
      }
    });
  });
}

/**
 * 智谱AI API调用 - 使用智谱AI专用格式
 */
async function callZhipuAPI(config, messages, options = {}) {
  const requestData = {
    model: config.model,
    messages: messages,
    max_tokens: options.maxTokens || config.maxTokens,
    temperature: options.temperature || config.temperature,
    stream: false
  };

  const requestOptions = {
    url: `${config.baseUrl}/chat/completions`,
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`
    },
    data: requestData,
    timeout: 30000
  };

  return new Promise((resolve, reject) => {
    wx.request({
      ...requestOptions,
      success: (res) => {
        try { logger.debug && logger.debug('智谱AI API响应', { statusCode: res.statusCode }); } catch(_) {}
        if (res.statusCode === 200 && res.data.choices && res.data.choices.length > 0) {
          resolve({
            content: res.data.choices[0].message.content,
            usage: res.data.usage,
            model: res.data.model,
            provider: 'ZhipuAI'
          });
        } else {
          reject({
            type: classifyError(res.data, res.statusCode),
            message: res.data.error?.message || '智谱AI API请求失败',
            statusCode: res.statusCode,
            data: res.data
          });
        }
      },
      fail: (err) => {
        try { logger.error && logger.error('智谱AI API网络错误', err); } catch(_) {}
        reject({
          type: ERROR_TYPES.NETWORK_ERROR,
          message: err.errMsg || '智谱AI网络请求失败',
          error: err
        });
      }
    });
  });
}

/**
 * 带重试的API调用
 */
async function callWithRetry(apiFunction, config, messages, options = {}) {
  let lastError;
  
  for (let attempt = 0; attempt <= RETRY_CONFIG.maxRetries; attempt++) {
    try {
      const result = await apiFunction(config, messages, options);
      return result;
    } catch (error) {
      lastError = error;
      
      // 如果是认证错误或无效请求，不重试
      if (error.type === ERROR_TYPES.AUTHENTICATION_ERROR || 
          error.type === ERROR_TYPES.INVALID_REQUEST) {
        throw error;
      }
      
      // 如果不是最后一次尝试，等待后重试
      if (attempt < RETRY_CONFIG.maxRetries) {
        const delayTime = RETRY_CONFIG.retryDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt);
        try { logger.warn && logger.warn(`AI请求失败，${delayTime}ms后重试 (${attempt + 1}/${RETRY_CONFIG.maxRetries})`); } catch(_) {}
        await delay(delayTime);
      }
    }
  }
  
  throw lastError;
}

/**
 * 统一的AI服务调用接口
 */
async function callAIService(scenarioName, userMessage, options = {}) {
  try {
    // 获取配置
    const config = await getAIConfig(scenarioName);
    
    // 构建消息
    const messages = [
      {
        role: 'system',
        content: config.systemPrompt
      },
      {
        role: 'user',
        content: userMessage
      }
    ];
    
    // 根据服务商选择API调用函数
    let apiFunction;
    switch (config.provider.name) {
      case 'SiliconFlow':
        apiFunction = callSiliconFlowAPI;
        break;
      case 'ZhipuAI':
        apiFunction = callZhipuAPI;
        break;
      default:
        throw new Error(`不支持的AI服务商: ${config.provider.name}`);
    }
    
    // 调用API
    const result = await callWithRetry(apiFunction, config, messages, options);
    
    return {
      success: true,
      data: {
        content: result.content,
        usage: result.usage,
        model: result.model,
        provider: config.provider.name,
        scenario: scenarioName
      }
    };
    
  } catch (error) {
    try { logger.error && logger.error('AI服务调用失败', error); } catch(_) {}
    
    return {
      success: false,
      error: {
        type: error.type || ERROR_TYPES.API_ERROR,
        message: error.message || 'AI服务调用失败',
        details: error
      }
    };
  }
}

/**
 * 图像识别专用接口
 */
async function callImageRecognition(imageBase64, description = '') {
  try {
    const config = await getAIConfig('IMAGE_RECOGNITION');
    
    const messages = [
      {
        role: 'system',
        content: config.systemPrompt
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: description || '请分析这张图片中鹅的健康状况'
          },
          {
            type: 'image_url',
            image_url: {
              url: `data:image/jpeg;base64,${imageBase64}`
            }
          }
        ]
      }
    ];
    
    let apiFunction;
    switch (config.provider.name) {
      case 'SiliconFlow':
        apiFunction = callSiliconFlowAPI;
        break;
      case 'ZhipuAI':
        apiFunction = callZhipuAPI;
        break;
      default:
        throw new Error(`不支持的AI服务商: ${config.provider.name}`);
    }
    
    const result = await callWithRetry(apiFunction, config, messages);
    
    return {
      success: true,
      data: {
        content: result.content,
        usage: result.usage,
        model: result.model,
        provider: config.provider.name
      }
    };
    
  } catch (error) {
    try { logger.error && logger.error('图像识别失败', error); } catch(_) {}
    
    return {
      success: false,
      error: {
        type: error.type || ERROR_TYPES.API_ERROR,
        message: error.message || '图像识别失败',
        details: error
      }
    };
  }
}

/**
 * 批量处理多个AI请求
 */
async function batchAIRequests(requests) {
  const results = [];
  
  for (const request of requests) {
    try {
      const result = await callAIService(request.scenario, request.message, request.options);
      results.push({
        id: request.id,
        ...result
      });
    } catch (error) {
      results.push({
        id: request.id,
        success: false,
        error: error
      });
    }
  }
  
  return results;
}

module.exports = {
  callAIService,
  callImageRecognition,
  batchAIRequests,
  ERROR_TYPES
};
