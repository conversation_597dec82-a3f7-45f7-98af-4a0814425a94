<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SAAS平台监控面板 - 智慧养鹅</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

  <style>
    :root {
      --primary-color: #0d6efd;
      --success-color: #198754;
      --warning-color: #ffc107;
      --danger-color: #dc3545;
      --info-color: #0dcaf0;
      --dark-color: #212529;
    }

    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .dashboard-header {
      background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .metric-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      border-left: 4px solid var(--primary-color);
      transition: transform 0.2s, box-shadow 0.2s;
    }

    .metric-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .metric-card.success {
      border-left-color: var(--success-color);
    }

    .metric-card.warning {
      border-left-color: var(--warning-color);
    }

    .metric-card.danger {
      border-left-color: var(--danger-color);
    }

    .metric-card.info {
      border-left-color: var(--info-color);
    }

    .metric-value {
      font-size: 2.5rem;
      font-weight: bold;
      color: var(--dark-color);
    }

    .metric-label {
      color: #6c757d;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .metric-change {
      font-size: 0.8rem;
      padding: 0.2rem 0.5rem;
      border-radius: 20px;
      margin-top: 0.5rem;
    }

    .metric-change.positive {
      background-color: #d1e7dd;
      color: #0f5132;
    }

    .metric-change.negative {
      background-color: #f8d7da;
      color: #842029;
    }

    .chart-container {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .chart-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--dark-color);
      margin-bottom: 1rem;
    }

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 8px;
    }

    .status-online {
      background-color: var(--success-color);
      animation: pulse 2s infinite;
    }

    .status-warning {
      background-color: var(--warning-color);
    }

    .status-offline {
      background-color: var(--danger-color);
    }

    @keyframes pulse {
      0% {
        opacity: 1;
      }

      50% {
        opacity: 0.5;
      }

      100% {
        opacity: 1;
      }
    }

    .tenant-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .tenant-item {
      padding: 1rem;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .tenant-item:last-child {
      border-bottom: none;
    }

    .tenant-info h6 {
      margin: 0;
      color: var(--dark-color);
    }

    .tenant-info small {
      color: #6c757d;
    }

    .badge-subscription {
      font-size: 0.7rem;
      padding: 0.3rem 0.6rem;
    }

    .subscription-trial {
      background-color: var(--info-color);
      color: white;
    }

    .subscription-basic {
      background-color: var(--success-color);
      color: white;
    }

    .subscription-premium {
      background-color: var(--warning-color);
      color: var(--dark-color);
    }

    .subscription-enterprise {
      background-color: var(--dark-color);
      color: white;
    }

    .refresh-btn {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      border-radius: 8px;
      padding: 0.5rem 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .refresh-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .alert-panel {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .alert-item {
      padding: 0.75rem;
      margin-bottom: 0.5rem;
      border-radius: 8px;
      border-left: 4px solid;
    }

    .alert-critical {
      border-left-color: var(--danger-color);
      background-color: #f8d7da;
    }

    .alert-warning {
      border-left-color: var(--warning-color);
      background-color: #fff3cd;
    }

    .alert-info {
      border-left-color: var(--info-color);
      background-color: #d1ecf1;
    }
  </style>
</head>

<body>
  <!-- 页面头部 -->
  <div class="dashboard-header position-relative">
    <div class="container">
      <h1 class="mb-0">
        <i class="fas fa-tachometer-alt me-3"></i>
        SAAS平台监控面板
      </h1>
      <p class="mb-0 mt-2 opacity-75">智慧养鹅平台运营监控与管理</p>

      <button class="refresh-btn" onclick="refreshDashboard()">
        <i class="fas fa-sync-alt"></i> 刷新数据
      </button>
    </div>
  </div>

  <div class="container-fluid">
    <!-- 核心指标 -->
    <div class="row mb-4">
      <div class="col-md-3 mb-3">
        <div class="metric-card success">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <div class="metric-label">活跃租户</div>
              <div class="metric-value" id="activeTenants">0</div>
              <div class="metric-change positive" id="tenantsChange">
                <i class="fas fa-arrow-up"></i> +12.5%
              </div>
            </div>
            <div class="text-success fs-1">
              <i class="fas fa-users"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-3 mb-3">
        <div class="metric-card info">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <div class="metric-label">月度收入</div>
              <div class="metric-value" id="monthlyRevenue">¥0</div>
              <div class="metric-change positive" id="revenueChange">
                <i class="fas fa-arrow-up"></i> +8.3%
              </div>
            </div>
            <div class="text-info fs-1">
              <i class="fas fa-dollar-sign"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-3 mb-3">
        <div class="metric-card warning">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <div class="metric-label">API调用/小时</div>
              <div class="metric-value" id="apiCalls">0</div>
              <div class="metric-change positive" id="apiChange">
                <i class="fas fa-arrow-up"></i> +15.7%
              </div>
            </div>
            <div class="text-warning fs-1">
              <i class="fas fa-exchange-alt"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-3 mb-3">
        <div class="metric-card danger">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <div class="metric-label">系统负载</div>
              <div class="metric-value" id="systemLoad">0%</div>
              <div class="metric-change negative" id="loadChange">
                <i class="fas fa-arrow-down"></i> -5.2%
              </div>
            </div>
            <div class="text-danger fs-1">
              <i class="fas fa-server"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mb-4">
      <!-- 租户增长趋势 -->
      <div class="col-lg-8 mb-4">
        <div class="chart-container">
          <div class="chart-title">
            <i class="fas fa-chart-line me-2"></i>
            租户增长趋势
          </div>
          <canvas id="tenantGrowthChart" height="100"></canvas>
        </div>
      </div>

      <!-- 收入分析 -->
      <div class="col-lg-4 mb-4">
        <div class="chart-container">
          <div class="chart-title">
            <i class="fas fa-chart-pie me-2"></i>
            收入来源分析
          </div>
          <canvas id="revenueChart" height="200"></canvas>
        </div>
      </div>
    </div>

    <!-- 详细信息区域 -->
    <div class="row">
      <!-- 租户列表 -->
      <div class="col-lg-6 mb-4">
        <div class="chart-container">
          <div class="chart-title">
            <i class="fas fa-building me-2"></i>
            最新租户
            <span class="badge bg-primary ms-2" id="tenantCount">0</span>
          </div>
          <div class="tenant-list" id="tenantList">
            <!-- 租户数据将通过JavaScript加载 -->
          </div>
        </div>
      </div>

      <!-- 系统告警 -->
      <div class="col-lg-6 mb-4">
        <div class="alert-panel">
          <div class="chart-title">
            <i class="fas fa-exclamation-triangle me-2"></i>
            系统告警
            <span class="badge bg-danger ms-2" id="alertCount">0</span>
          </div>
          <div id="alertList">
            <!-- 告警数据将通过JavaScript加载 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 系统状态 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="chart-container">
          <div class="chart-title">
            <i class="fas fa-heartbeat me-2"></i>
            系统服务状态
          </div>
          <div class="row" id="serviceStatus">
            <!-- 服务状态将通过JavaScript加载 -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // 全局变量
    let tenantGrowthChart, revenueChart;
    let dashboardData = {};

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function () {
      initializeDashboard();
      loadDashboardData();

      // 设置自动刷新
      setInterval(loadDashboardData, 30000); // 30秒刷新一次
    });

    /**
     * 初始化仪表板
     */
    function initializeDashboard() {
      // 初始化图表
      initializeTenantGrowthChart();
      initializeRevenueChart();
    }

    /**
     * 加载仪表板数据
     */
    async function loadDashboardData() {
      try {
        console.log('正在加载仪表板数据...');

        // 模拟API调用 - 实际应用中替换为真实的API端点
        const response = await fetch('/api/v1/platform/dashboard', {
          headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('access_token')
          }
        });

        if (response.ok) {
          dashboardData = await response.json();
          updateDashboard(dashboardData);
        } else {
          // 使用模拟数据
          dashboardData = generateMockData();
          updateDashboard(dashboardData);
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        // 使用模拟数据
        dashboardData = generateMockData();
        updateDashboard(dashboardData);
      }
    }

    /**
     * 更新仪表板显示
     */
    function updateDashboard(data) {
      // 更新核心指标
      updateMetrics(data.basicStats);

      // 更新图表
      updateTenantGrowthChart(data.growthTrends);
      updateRevenueChart(data.revenueStats);

      // 更新租户列表
      updateTenantList(data.recentTenants);

      // 更新告警信息
      updateAlerts(data.alerts);

      // 更新服务状态
      updateServiceStatus(data.systemHealth);
    }

    /**
     * 更新核心指标
     */
    function updateMetrics(stats) {
      document.getElementById('activeTenants').textContent = stats.activeTenants || 0;
      document.getElementById('monthlyRevenue').textContent = formatCurrency(stats.monthlyRevenue || 0);
      document.getElementById('apiCalls').textContent = formatNumber(stats.apiCallsPerHour || 0);
      document.getElementById('systemLoad').textContent = (stats.systemLoad || 0) + '%';
    }

    /**
     * 初始化租户增长图表
     */
    function initializeTenantGrowthChart() {
      const ctx = document.getElementById('tenantGrowthChart').getContext('2d');
      tenantGrowthChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: [],
          datasets: [{
            label: '新增租户',
            data: [],
            borderColor: '#0d6efd',
            backgroundColor: 'rgba(13, 110, 253, 0.1)',
            tension: 0.4,
            fill: true
          }, {
            label: '活跃租户',
            data: [],
            borderColor: '#198754',
            backgroundColor: 'rgba(25, 135, 84, 0.1)',
            tension: 0.4,
            fill: false
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    /**
     * 初始化收入图表
     */
    function initializeRevenueChart() {
      const ctx = document.getElementById('revenueChart').getContext('2d');
      revenueChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['基础版', '标准版', '高级版', '企业版'],
          datasets: [{
            data: [],
            backgroundColor: [
              '#198754',
              '#0d6efd',
              '#ffc107',
              '#6c757d'
            ],
            borderWidth: 2,
            borderColor: '#fff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
            }
          }
        }
      });
    }

    /**
     * 更新租户增长图表
     */
    function updateTenantGrowthChart(trends) {
      if (trends && trends.daily) {
        tenantGrowthChart.data.labels = trends.daily.labels;
        tenantGrowthChart.data.datasets[0].data = trends.daily.newTenants;
        tenantGrowthChart.data.datasets[1].data = trends.daily.activeTenants;
        tenantGrowthChart.update();
      }
    }

    /**
     * 更新收入图表
     */
    function updateRevenueChart(revenueStats) {
      if (revenueStats && revenueStats.byPlan) {
        revenueChart.data.datasets[0].data = [
          revenueStats.byPlan.basic || 0,
          revenueStats.byPlan.standard || 0,
          revenueStats.byPlan.premium || 0,
          revenueStats.byPlan.enterprise || 0
        ];
        revenueChart.update();
      }
    }

    /**
     * 更新租户列表
     */
    function updateTenantList(tenants) {
      const tenantList = document.getElementById('tenantList');
      const tenantCount = document.getElementById('tenantCount');

      tenantCount.textContent = tenants ? tenants.length : 0;

      if (tenants && tenants.length > 0) {
        tenantList.innerHTML = tenants.map(tenant => `
                    <div class="tenant-item">
                        <div class="tenant-info">
                            <h6>${tenant.companyName}</h6>
                            <small>${tenant.contactEmail}</small>
                        </div>
                        <div>
                            <span class="badge badge-subscription subscription-${tenant.subscriptionPlan}">
                                ${getSubscriptionPlanName(tenant.subscriptionPlan)}
                            </span>
                            <div class="mt-1">
                                <span class="status-indicator ${getStatusClass(tenant.status)}"></span>
                                <small>${getStatusName(tenant.status)}</small>
                            </div>
                        </div>
                    </div>
                `).join('');
      } else {
        tenantList.innerHTML = '<div class="text-center text-muted py-4">暂无租户数据</div>';
      }
    }

    /**
     * 更新告警信息
     */
    function updateAlerts(alerts) {
      const alertList = document.getElementById('alertList');
      const alertCount = document.getElementById('alertCount');

      alertCount.textContent = alerts ? alerts.length : 0;

      if (alerts && alerts.length > 0) {
        alertList.innerHTML = alerts.map(alert => `
                    <div class="alert-item alert-${alert.severity}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>${alert.title}</strong>
                                <div class="small">${alert.description}</div>
                            </div>
                            <small class="text-muted">${formatTime(alert.timestamp)}</small>
                        </div>
                    </div>
                `).join('');
      } else {
        alertList.innerHTML = '<div class="text-center text-muted py-4">系统运行正常，无告警信息</div>';
      }
    }

    /**
     * 更新服务状态
     */
    function updateServiceStatus(systemHealth) {
      const serviceStatus = document.getElementById('serviceStatus');

      if (systemHealth && systemHealth.services) {
        serviceStatus.innerHTML = systemHealth.services.map(service => `
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <span class="status-indicator ${getServiceStatusClass(service.status)}"></span>
                            <div>
                                <div class="fw-bold">${service.name}</div>
                                <small class="text-muted">${service.description}</small>
                            </div>
                        </div>
                    </div>
                `).join('');
      }
    }

    /**
     * 刷新仪表板
     */
    function refreshDashboard() {
      const refreshBtn = document.querySelector('.refresh-btn');
      const icon = refreshBtn.querySelector('i');

      icon.classList.add('fa-spin');
      loadDashboardData().finally(() => {
        icon.classList.remove('fa-spin');
      });
    }

    /**
     * 生成模拟数据
     */
    function generateMockData() {
      return {
        basicStats: {
          activeTenants: 156,
          monthlyRevenue: 89600,
          apiCallsPerHour: 12450,
          systemLoad: 72
        },
        growthTrends: {
          daily: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            newTenants: [12, 19, 8, 15, 24, 18],
            activeTenants: [120, 135, 140, 148, 165, 156]
          }
        },
        revenueStats: {
          byPlan: {
            basic: 25600,
            standard: 34200,
            premium: 19800,
            enterprise: 10000
          }
        },
        recentTenants: [
          {
            companyName: '绿野农场',
            contactEmail: '<EMAIL>',
            subscriptionPlan: 'premium',
            status: 'active'
          },
          {
            companyName: '阳光养殖',
            contactEmail: '<EMAIL>',
            subscriptionPlan: 'standard',
            status: 'active'
          },
          {
            companyName: '新希望农业',
            contactEmail: '<EMAIL>',
            subscriptionPlan: 'enterprise',
            status: 'trial'
          }
        ],
        alerts: [
          {
            title: '高API调用量',
            description: '租户"绿野农场"API调用量超过限制',
            severity: 'warning',
            timestamp: new Date(Date.now() - 1000 * 60 * 15)
          },
          {
            title: '系统负载过高',
            description: '服务器CPU使用率达到85%',
            severity: 'critical',
            timestamp: new Date(Date.now() - 1000 * 60 * 5)
          }
        ],
        systemHealth: {
          services: [
            { name: 'API网关', description: '请求路由服务', status: 'online' },
            { name: '数据库', description: 'MySQL主从集群', status: 'online' },
            { name: '缓存服务', description: 'Redis集群', status: 'warning' },
            { name: '文件存储', description: '对象存储服务', status: 'online' }
          ]
        }
      };
    }

    // 辅助函数
    function formatCurrency(amount) {
      return '¥' + amount.toLocaleString();
    }

    function formatNumber(num) {
      return num.toLocaleString();
    }

    function formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    }

    function getSubscriptionPlanName(plan) {
      const names = {
        trial: '试用版',
        basic: '基础版',
        standard: '标准版',
        premium: '高级版',
        enterprise: '企业版'
      };
      return names[plan] || plan;
    }

    function getStatusClass(status) {
      const classes = {
        active: 'status-online',
        trial: 'status-warning',
        suspended: 'status-offline',
        expired: 'status-offline'
      };
      return classes[status] || 'status-offline';
    }

    function getStatusName(status) {
      const names = {
        active: '活跃',
        trial: '试用',
        suspended: '暂停',
        expired: '过期'
      };
      return names[status] || status;
    }

    function getServiceStatusClass(status) {
      const classes = {
        online: 'status-online',
        warning: 'status-warning',
        offline: 'status-offline'
      };
      return classes[status] || 'status-offline';
    }
  </script>
</body>

</html>