<!-- 帮助文章详情页 -->
<view class="article-container">
  <!-- 文章头部 -->
  <view class="article-header">
    <view class="header-meta">
      <text class="category-badge">{{articleInfo.categoryName}}</text>
      <text class="difficulty-badge difficulty-{{articleInfo.difficulty}}">{{articleInfo.difficultyText}}</text>
    </view>
    <text class="article-title">{{articleInfo.title}}</text>
    <view class="article-info">
      <view class="info-item">
        <text class="info-icon">👁️</text>
        <text class="info-text">{{articleInfo.views}}次浏览</text>
      </view>
      <view class="info-item">
        <text class="info-icon">👍</text>
        <text class="info-text">{{articleInfo.likes}}人点赞</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🕒</text>
        <text class="info-text">{{articleInfo.updateTime}}</text>
      </view>
    </view>
  </view>

  <!-- 文章目录 -->
  <view class="toc-section" wx:if="{{articleInfo.toc.length > 0}}">
    <view class="toc-header" bindtap="onToggleTOC">
      <text class="toc-title">📋 文章目录</text>
      <text class="toc-toggle">{{showTOC ? '收起' : '展开'}}</text>
    </view>
    <view class="toc-content" wx:if="{{showTOC}}">
      <view class="toc-item" 
            wx:for="{{articleInfo.toc}}" 
            wx:key="id"
            bindtap="onScrollToSection" 
            data-section="{{item.id}}">
        <text class="toc-number">{{index + 1}}</text>
        <text class="toc-text">{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 文章内容 -->
  <view class="article-content">
    <view class="content-section" 
          wx:for="{{articleInfo.content}}" 
          wx:key="id"
          id="section-{{item.id}}">
      
      <!-- 章节标题 -->
      <view class="section-title" wx:if="{{item.type === 'title'}}">
        <text class="title-text">{{item.text}}</text>
      </view>
      
      <!-- 段落内容 -->
      <view class="section-paragraph" wx:if="{{item.type === 'paragraph'}}">
        <text class="paragraph-text">{{item.text}}</text>
      </view>
      
      <!-- 图片内容 -->
      <view class="section-image" wx:if="{{item.type === 'image'}}">
        <image class="content-image" 
               src="{{item.src}}" 
               mode="aspectFit"
               bindtap="onPreviewImage" 
               data-src="{{item.src}}" />
        <text class="image-caption" wx:if="{{item.caption}}">{{item.caption}}</text>
      </view>
      
      <!-- 步骤列表 -->
      <view class="section-steps" wx:if="{{item.type === 'steps'}}">
        <view class="step-item" wx:for="{{item.steps}}" wx:key="*this" wx:for-item="step">
          <view class="step-number">{{index + 1}}</view>
          <text class="step-text">{{step}}</text>
        </view>
      </view>
      
      <!-- 提示框 -->
      <view class="section-tip" wx:if="{{item.type === 'tip'}}">
        <text class="tip-icon">💡</text>
        <text class="tip-text">{{item.text}}</text>
      </view>
      
      <!-- 警告框 -->
      <view class="section-warning" wx:if="{{item.type === 'warning'}}">
        <text class="warning-icon">⚠️</text>
        <text class="warning-text">{{item.text}}</text>
      </view>
    </view>
  </view>

  <!-- 文章标签 -->
  <view class="tags-section" wx:if="{{articleInfo.tags.length > 0}}">
    <text class="tags-title">相关标签：</text>
    <view class="tags-list">
      <text class="tag-item" 
            wx:for="{{articleInfo.tags}}" 
            wx:key="*this"
            bindtap="onTagSearch" 
            data-tag="{{item}}">#{{item}}</text>
    </view>
  </view>

  <!-- 操作区域 -->
  <view class="actions-section">
    <view class="action-buttons">
      <button class="action-btn like-btn {{isLiked ? 'liked' : ''}}" 
              bindtap="onToggleLike">
        <text class="btn-icon">{{isLiked ? '❤️' : '🤍'}}</text>
        <text class="btn-text">{{isLiked ? '已点赞' : '点赞'}}</text>
      </button>
      <button class="action-btn share-btn" bindtap="onShare">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享</text>
      </button>
      <button class="action-btn feedback-btn" bindtap="onFeedback">
        <text class="btn-icon">💬</text>
        <text class="btn-text">反馈</text>
      </button>
    </view>
  </view>

  <!-- 相关文章 */
  <view class="related-articles" wx:if="{{relatedArticles.length > 0}}">
    <view class="section-header">
      <text class="section-title">相关文章</text>
    </view>
    <view class="related-list">
      <view class="related-item" 
            wx:for="{{relatedArticles}}" 
            wx:key="id"
            bindtap="onViewRelated" 
            data-article="{{item}}">
        <view class="related-content">
          <text class="related-title">{{item.title}}</text>
          <text class="related-summary">{{item.summary}}</text>
          <view class="related-meta">
            <text class="meta-text">{{item.views}}次浏览</text>
            <text class="meta-text">{{item.updateTime}}</text>
          </view>
        </view>
        <text class="related-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 评价区域 -->
  <view class="rating-section">
    <view class="rating-header">
      <text class="rating-title">这篇文章对您有帮助吗？</text>
    </view>
    <view class="rating-buttons">
      <button class="rating-btn helpful-btn {{isHelpful === true ? 'active' : ''}}" 
              bindtap="onRateHelpful" data-helpful="true">
        <text class="rating-icon">👍</text>
        <text class="rating-text">有帮助</text>
        <text class="rating-count">({{articleInfo.helpfulCount}})</text>
      </button>
      <button class="rating-btn unhelpful-btn {{isHelpful === false ? 'active' : ''}}" 
              bindtap="onRateHelpful" data-helpful="false">
        <text class="rating-icon">👎</text>
        <text class="rating-text">没帮助</text>
        <text class="rating-count">({{articleInfo.unhelpfulCount}})</text>
      </button>
    </view>
    <view class="rating-tips" wx:if="{{isHelpful === false}}">
      <text class="tips-text">告诉我们哪里可以改进，或者联系客服获得更多帮助</text>
    </view>
  </view>

  <!-- 底部导航 -->
  <view class="bottom-nav">
    <button class="nav-btn prev-btn" bindtap="onPrevArticle" disabled="{{!hasPrev}}">
      <text class="nav-icon">⬅️</text>
      <text class="nav-text">上一篇</text>
    </button>
    <button class="nav-btn back-btn" bindtap="onBackToCategory">
      <text class="nav-icon">📂</text>
      <text class="nav-text">返回分类</text>
    </button>
    <button class="nav-btn next-btn" bindtap="onNextArticle" disabled="{{!hasNext}}">
      <text class="nav-text">下一篇</text>
      <text class="nav-icon">➡️</text>
    </button>
  </view>
</view>

<!-- 浮动目录按钮 -->
<view class="floating-toc" wx:if="{{articleInfo.toc.length > 0}}" bindtap="onToggleFloatingTOC">
  <text class="toc-icon">📋</text>
</view>

<!-- 浮动目录弹窗 -->
<view class="toc-modal" wx:if="{{showFloatingTOC}}">
  <view class="modal-overlay" bindtap="onCloseFloatingTOC"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">文章目录</text>
      <text class="modal-close" bindtap="onCloseFloatingTOC">×</text>
    </view>
    <view class="modal-toc">
      <view class="modal-toc-item" 
            wx:for="{{articleInfo.toc}}" 
            wx:key="id"
            bindtap="onScrollToSectionAndClose" 
            data-section="{{item.id}}">
        <text class="modal-toc-number">{{index + 1}}</text>
        <text class="modal-toc-text">{{item.title}}</text>
      </view>
    </view>
  </view>
</view>