// pages/health/knowledge/detail/detail.js
const { getAllArticles } = require('../../../../utils/knowledge-data.js');

Page({
  data: {
    articleId: null,
    articleData: {},
    relatedArticles: [],
    loading: true
  },

  onLoad: function (options) {
    const articleId = options.id;
    if (articleId) {
      this.setData({ articleId });
      this.loadArticleDetail(articleId);
      this.loadRelatedArticles(articleId);
    }
  },

  // 加载文章详情
  loadArticleDetail: function(articleId) {
    try {
      const allArticles = getAllArticles();
      const articleData = allArticles.find(article => article.id == articleId) || allArticles[0];
      
      this.setData({
        articleData: articleData,
        loading: false
      });

      // 增加浏览量
      this.increaseViewCount(articleId);
    } catch (error) {
      console.error('加载文章详情失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载相关文章
  loadRelatedArticles: function(articleId) {
    try {
      const allArticles = getAllArticles();
      const currentArticle = allArticles.find(article => article.id == articleId);
      
      // 获取同类别的其他文章作为相关文章
      let relatedArticles = [];
      if (currentArticle) {
        relatedArticles = allArticles
          .filter(article => article.category === currentArticle.category && article.id != articleId)
          .slice(0, 2); // 最多显示2篇相关文章
      }
      
      // 如果同类别文章不足，用其他文章补充
      if (relatedArticles.length < 2) {
        const otherArticles = allArticles
          .filter(article => article.id != articleId && !relatedArticles.find(r => r.id === article.id))
          .slice(0, 2 - relatedArticles.length);
        relatedArticles = relatedArticles.concat(otherArticles);
      }

      this.setData({ relatedArticles });
    } catch (error) {
      console.error('加载相关文章失败:', error);
    }
  },

  // 增加浏览量
  increaseViewCount: function(articleId) {
    // 这里可以调用API增加浏览量
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('增加文章浏览量', articleId); } catch(_) {}
  },

  // 点击相关文章
  onRelatedItemTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: `/pages/health/knowledge/detail/detail?id=${id}`
    });
  },

  // 分享配置
  onShareAppMessage: function () {
    return {
      title: this.data.articleData.title,
      path: `/pages/health/knowledge/detail/detail?id=${this.data.articleId}`,
      imageUrl: '/images/share_default.png'
    };
  }
});
