/* 智慧养鹅小程序 - 高级交互模式 V1.0 */
/* 提供手势支持、拖拽交互、复杂动画等高级交互功能 */

/* ==================== 手势交互基础 ==================== */

/* 触摸区域基础样式 */
.touch-area {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* 可拖拽元素 */
.draggable {
  cursor: grab;
  cursor: -webkit-grab;
  transition: transform 0.2s ease;
  will-change: transform;
  position: relative;
  z-index: 1;
}

.draggable:active {
  cursor: grabbing;
  cursor: -webkit-grabbing;
  z-index: 1000;
}

.draggable.dragging {
  transition: none;
  box-shadow: var(--shadow-lg);
  transform: scale(1.05);
  z-index: 1000;
}

/* 拖拽占位符 */
.drag-placeholder {
  background-color: var(--primary-subtle);
  border: 2rpx dashed var(--primary);
  border-radius: var(--radius-md);
  opacity: 0.6;
  transition: all 0.2s ease;
}

/* 拖拽目标区域 */
.drop-zone {
  position: relative;
  border: 2rpx solid transparent;
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  min-height: 100rpx;
}

.drop-zone.drag-over {
  border-color: var(--primary);
  background-color: var(--primary-subtle);
  box-shadow: inset 0 0 20rpx rgba(0, 102, 204, 0.1);
}

.drop-zone.drag-valid {
  border-color: var(--success);
  background-color: var(--success-bg);
}

.drop-zone.drag-invalid {
  border-color: var(--error);
  background-color: var(--error-bg);
}

/* ==================== 滑动手势样式 ==================== */

/* 可滑动卡片 */
.swipeable-card {
  position: relative;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transform: translateX(0);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.swipeable-card.swiping {
  transition: none;
}

/* 滑动动作按钮 */
.swipe-actions {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: stretch;
  z-index: -1;
}

.swipe-action {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--space-lg);
  color: var(--text-inverse);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.swipe-action.delete {
  background-color: var(--error);
}

.swipe-action.edit {
  background-color: var(--warning);
}

.swipe-action.archive {
  background-color: var(--info);
}

.swipe-action:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* ==================== 长按交互 ==================== */

/* 长按选择模式 */
.long-press-item {
  position: relative;
  transition: all 0.3s ease;
  border-radius: var(--radius-md);
}

.long-press-item.selecting {
  transform: scale(0.98);
  box-shadow: 0 0 0 4rpx var(--primary-subtle);
}

.long-press-item.selected {
  background-color: var(--primary-subtle);
  transform: scale(0.95);
}

/* 选择指示器 */
.selection-indicator {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid var(--border-medium);
  border-radius: var(--radius-full);
  background-color: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0.8);
}

.long-press-item.selecting .selection-indicator,
.long-press-item.selected .selection-indicator {
  opacity: 1;
  transform: scale(1);
}

.long-press-item.selected .selection-indicator {
  border-color: var(--primary);
  background-color: var(--primary);
  color: var(--text-inverse);
}

.selection-indicator::after {
  content: '✓';
  font-size: 24rpx;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.long-press-item.selected .selection-indicator::after {
  opacity: 1;
}

/* ==================== 缩放和旋转手势 ==================== */

/* 可缩放元素 */
.scalable {
  transform-origin: center center;
  transition: transform 0.2s ease;
  will-change: transform;
}

.scalable.scaling {
  transition: none;
}

/* 缩放限制指示 */
.scale-limit-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1000;
}

.scale-limit-indicator.show {
  opacity: 1;
}

/* 可旋转元素 */
.rotatable {
  transform-origin: center center;
  transition: transform 0.2s ease;
  will-change: transform;
}

.rotatable.rotating {
  transition: none;
}

/* ==================== 复杂动画序列 ==================== */

/* 动画序列容器 */
.animation-sequence {
  position: relative;
}

/* 连锁动画 */
.chain-animation {
  opacity: 0;
  transform: translateY(50rpx) scale(0.9);
}

.chain-animation.animate {
  animation: chain-enter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.chain-animation:nth-child(1) { animation-delay: 0.1s; }
.chain-animation:nth-child(2) { animation-delay: 0.2s; }
.chain-animation:nth-child(3) { animation-delay: 0.3s; }
.chain-animation:nth-child(4) { animation-delay: 0.4s; }
.chain-animation:nth-child(5) { animation-delay: 0.5s; }

@keyframes chain-enter {
  0% {
    opacity: 0;
    transform: translateY(50rpx) scale(0.9);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-10rpx) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 波纹扩散动画 */
.ripple-container {
  position: relative;
  overflow: hidden;
}

.ripple-effect {
  position: absolute;
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-expand 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-expand {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* ==================== 磁性吸附效果 ==================== */

/* 磁性容器 */
.magnetic-container {
  position: relative;
}

/* 磁性元素 */
.magnetic-element {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.magnetic-element.attracted {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

/* 磁性目标 */
.magnetic-target {
  position: relative;
  transition: all 0.3s ease;
}

.magnetic-target::before {
  content: '';
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  border: 2rpx dashed var(--primary);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.magnetic-target.active::before {
  opacity: 0.6;
}

/* ==================== 惯性滚动和回弹 ==================== */

/* 惯性滚动容器 */
.inertial-scroll {
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 回弹效果 */
.bounce-scroll {
  position: relative;
  overflow: hidden;
}

.bounce-content {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.bounce-content.bouncing {
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ==================== 多点触控支持 ==================== */

/* 多点触控区域 */
.multi-touch-area {
  touch-action: none;
  position: relative;
  overflow: hidden;
}

/* 触控点指示器 */
.touch-point {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid var(--primary);
  border-radius: var(--radius-full);
  background-color: rgba(0, 102, 204, 0.2);
  transform: translate(-50%, -50%) scale(0);
  animation: touch-point-appear 0.2s ease forwards;
  pointer-events: none;
  z-index: 1000;
}

@keyframes touch-point-appear {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.touch-point.removing {
  animation: touch-point-disappear 0.2s ease forwards;
}

@keyframes touch-point-disappear {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
}

/* ==================== 自定义手势反馈 ==================== */

/* 手势识别反馈 */
.gesture-feedback {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1001;
  white-space: nowrap;
}

.gesture-feedback.show {
  opacity: 1;
}

.gesture-feedback.success {
  background-color: var(--success);
}

.gesture-feedback.error {
  background-color: var(--error);
}

/* 手势路径指示 */
.gesture-path {
  position: absolute;
  pointer-events: none;
  z-index: 999;
  stroke: var(--primary);
  stroke-width: 6rpx;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
  opacity: 0.8;
}

/* ==================== 高级动画序列 ==================== */

/* 弹性动画 */
.elastic-animation {
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 物理动画 */
.physics-animation {
  animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 流体动画 */
.fluid-animation {
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 有机动画 */
.organic-animation {
  animation-timing-function: cubic-bezier(0.23, 1, 0.320, 1);
}

/* ==================== 性能优化的交互 ==================== */

/* 硬件加速的交互元素 */
.hw-accelerated-interaction {
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, opacity;
}

/* 批处理动画 */
.batch-interaction {
  animation-fill-mode: both;
  animation-play-state: paused;
}

.batch-interaction.play {
  animation-play-state: running;
}

/* ==================== 响应式交互 ==================== */

/* 移动端优化的交互 */
@media screen and (max-width: 750rpx) {
  .touch-area {
    min-height: 88rpx; /* 最小触控区域 */
  }
  
  .draggable {
    /* 移动端减少动画复杂度 */
    transition-duration: 0.1s;
  }
  
  .swipe-action {
    min-width: 100rpx; /* 移动端减小按钮宽度 */
  }
}

/* 大屏设备增强交互 */
@media screen and (min-width: 1024rpx) {
  .hover-enhanced:hover {
    transform: translateY(-4rpx);
    box-shadow: var(--shadow-lg);
  }
  
  .magnetic-element {
    /* 大屏设备增强磁性效果 */
    transition-duration: 0.4s;
  }
}

/* ==================== 无障碍交互支持 ==================== */

/* 键盘导航增强 */
.keyboard-interactive:focus {
  outline: 3rpx solid var(--primary);
  outline-offset: 2rpx;
}

/* 屏幕阅读器支持 */
.sr-interaction-hint {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 减少动效模式 - 小程序通过类名控制 */
/* @media (prefers-reduced-motion: reduce) { */ /* 小程序不支持 */
.respect-motion-preference {
  animation: none !important;
  transition-duration: 0.01s !important;
}

.respect-motion-preference .chain-animation {
  opacity: 1;
  transform: none;
  animation: none;
}
/* } */

/* ==================== 调试和开发工具 ==================== */

/* 触控调试 */
.touch-debug .touch-area {
  border: 2rpx solid red;
  background-color: rgba(255, 0, 0, 0.1);
}

.touch-debug .draggable {
  border: 2rpx solid blue;
  background-color: rgba(0, 0, 255, 0.1);
}

.touch-debug .drop-zone {
  border: 2rpx solid green;
  background-color: rgba(0, 255, 0, 0.1);
}

/* 性能监控 */
.interaction-perf-monitor {
  position: fixed;
  top: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-sm);
  font-size: var(--text-xs);
  z-index: 10000;
  font-family: monospace;
}