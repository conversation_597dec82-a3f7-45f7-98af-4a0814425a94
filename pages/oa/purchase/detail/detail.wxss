@import '/styles/oa-common.wxss';

/* pages/oa/purchase/detail/detail.wxss */
.container {
  padding: 0;
  min-height: 100vh;
  background: #f5f5f5;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
}

.header-content {
  display: flex;
  flex-direction: column;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.request-number {
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 500;
}

.request-title {
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1.3;
}

.status-info {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.status-badge {
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.urgency-badge {
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
}

/* 通用区块样式 */
.section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

/* 申请人信息 */
.applicant-section {
  background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
  border: 2rpx solid #007aff;
}

.auto-fill-tag {
  background: #007aff;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.applicant-card {
  display: flex;
  gap: 24rpx;
  align-items: flex-start;
}

.applicant-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #007aff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}

.applicant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0, 122, 255, 0.1);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 基本信息 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.value.amount {
  color: #007aff;
  font-size: 32rpx;
}

.description-section {
  margin-top: 24rpx;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 物品清单 */
.items-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.item-card {
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 24rpx;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.item-card:active {
  transform: scale(0.98);
  border-color: #007aff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #e5e7eb;
}

.item-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.item-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #007aff;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  min-width: 120rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.total-summary {
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
  border-radius: 12rpx;
  border: 2rpx solid #007aff;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.summary-row.total {
  border-top: 1rpx solid #e0e7ff;
  margin-top: 12rpx;
  padding-top: 20rpx;
}

.summary-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.summary-row.total .summary-label {
  font-weight: bold;
  font-size: 30rpx;
}

.summary-value {
  font-size: 28rpx;
  color: #007aff;
  font-weight: bold;
}

.summary-row.total .summary-value {
  font-size: 36rpx;
}

/* 审批流程 */
.view-flow-btn {
  background: transparent;
  color: #007aff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.arrow {
  font-size: 20rpx;
}

.approval-timeline {
  position: relative;
  padding-left: 40rpx;
}

.timeline-item {
  position: relative;
  padding-bottom: 40rpx;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -28rpx;
  top: 24rpx;
  bottom: -16rpx;
  width: 2rpx;
  background: #e5e7eb;
}

.timeline-item:last-child::before {
  display: none;
}

.timeline-dot {
  position: absolute;
  left: -36rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #007aff;
  border: 4rpx solid white;
  box-shadow: 0 0 0 2rpx #007aff;
}

.timeline-item.approved .timeline-dot {
  background: #00a86b;
  box-shadow: 0 0 0 2rpx #00a86b;
}

.timeline-item.rejected .timeline-dot {
  background: #ff3b30;
  box-shadow: 0 0 0 2rpx #ff3b30;
}

.timeline-content {
  padding: 16rpx 20rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.timeline-item.approved .timeline-content {
  border-color: #00a86b;
}

.timeline-item.rejected .timeline-content {
  border-color: #ff3b30;
}

.approval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.approver-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.approval-time {
  font-size: 24rpx;
  color: #999;
}

.approval-action {
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 26rpx;
  color: #007aff;
  font-weight: 500;
}

.timeline-item.approved .action-text {
  color: #00a86b;
}

.timeline-item.rejected .action-text {
  color: #ff3b30;
}

.approval-comment {
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
}

.comment-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 附件 */
.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  transition: all 0.3s ease;
}

.attachment-item:active {
  border-color: #007aff;
  background: #f0f8ff;
}

.attachment-icon {
  font-size: 32rpx;
}

.attachment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.attachment-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.attachment-size {
  font-size: 24rpx;
  color: #999;
}

.attachment-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 评论记录 */
.add-comment-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.add-icon {
  font-size: 28rpx;
  font-weight: bold;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.comment-item {
  display: flex;
  gap: 16rpx;
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.comment-item:active {
  background: #f0f8ff;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #007aff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.comment-avatar .avatar-text {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-author {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.comment-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮 */
.actions-section {
  background: white;
  margin: 0 20rpx 40rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn {
  padding: 28rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: white;
}

.action-btn.success {
  background: linear-gradient(135deg, #00a86b 0%, #16a34a 100%);
  color: white;
}

.action-btn.danger {
  background: linear-gradient(135deg, #ff3b30 0%, #dc2626 100%);
  color: white;
}

.action-btn.warning {
  background: linear-gradient(135deg, #ff9500 0%, #f59e0b 100%);
  color: white;
}

.action-btn.secondary {
  background: #f9fafb;
  color: #333;
  border: 2rpx solid #e5e7eb;
}

.action-btn:disabled {
  opacity: 0.6;
  transform: none;
}

/* 加载状态 */
.loading-overlay,
.refresh-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-content,
.refresh-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.1);
  border: 2rpx solid rgba(0, 122, 255, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  display: block;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 60rpx;
  max-width: 400rpx;
  margin-left: auto;
  margin-right: auto;
}

.retry-btn {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.retry-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .applicant-card {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .applicant-info {
    width: 100%;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4rpx;
  }

  .detail-value {
    text-align: left;
  }
}