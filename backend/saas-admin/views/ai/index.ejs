<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-content {
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .ai-record-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
        }

        .confidence-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .confidence-high { background-color: #d4edda; color: #155724; }
        .confidence-medium { background-color: #fff3cd; color: #856404; }
        .confidence-low { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <%- include('../partials/navbar') %>

    <div class="container-fluid">
        <div class="row">
            <%- include('../partials/sidebar') %>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="main-content">
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h2">
                                <i class="bi bi-robot me-2"></i>AI诊断记录
                            </h1>
                            <button type="button" class="btn btn-primary">
                                <i class="bi bi-plus"></i> 新增诊断
                            </button>
                        </div>
                    </div>
                    
                    <% if (typeof aiRecords !== 'undefined' && aiRecords.length > 0) { %>
                        <% aiRecords.forEach(function(record) { %>
                            <div class="ai-record-card">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h5 class="mb-2">
                                            鹅群编号: <%= record.flockCode %>
                                            <span class="confidence-badge <%= record.confidence >= 0.8 ? 'confidence-high' : record.confidence >= 0.6 ? 'confidence-medium' : 'confidence-low' %>">
                                                置信度: <%= Math.round(record.confidence * 100) %>%
                                            </span>
                                        </h5>
                                        <p class="mb-2"><strong>症状:</strong> <%= record.symptoms %></p>
                                        <p class="mb-2"><strong>诊断结果:</strong> <%= record.diagnosis %></p>
                                        <p class="mb-3"><strong>建议:</strong> <%= record.recommendations %></p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <span class="badge <%= record.status === 'completed' ? 'bg-success' : record.status === 'pending' ? 'bg-warning' : 'bg-secondary' %> mb-2">
                                            <%= record.status === 'completed' ? '已完成' : record.status === 'pending' ? '待处理' : '进行中' %>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            <%= new Date(record.createdAt).toLocaleString('zh-CN') %>
                                        </small>
                                        <div class="mt-2">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-primary">查看</button>
                                                <button type="button" class="btn btn-outline-secondary">编辑</button>
                                                <button type="button" class="btn btn-outline-danger">删除</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <% }) %>
                    <% } else { %>
                        <div class="ai-record-card text-center py-5">
                            <i class="bi bi-robot display-1 text-muted"></i>
                            <h4 class="mt-3 text-muted">暂无AI诊断记录</h4>
                            <p class="text-muted">点击上方"新增诊断"按钮来创建AI诊断记录</p>
                        </div>
                    <% } %>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>