/**
 * 精简版常量入口文件
 * 为主包优化，只导入核心必需的常量
 */

// 导入核心常量（保留在主包）
const CORE = require('./core.constants.js');

// 按需导入其他常量的工厂函数
const loadConstants = {
  // 加载完整API常量
  loadFullAPI: () => require('./api.constants.js'),
  
  // 加载业务常量
  loadBusiness: () => require('./business.constants.js'),
  
  // 加载UI常量
  loadUI: () => require('./ui.constants.js'),
  
  // 加载图片常量
  loadImages: () => require('./images.constants.js'),
  
  // 加载配置常量
  loadConfig: () => require('./config.constants.js'),
  
  // 加载扩展API（分包）
  loadExtendedAPI: () => require('../constants-data/api-extended.js')
};

// 统一导出
module.exports = {
  // 核心常量直接可用
  ...CORE,
  
  // 按需加载函数
  loadConstants,
  
  // 兼容性导出（保持向后兼容）
  API: CORE,
  USER: CORE.USER,
  UI: CORE.UI,
  IMAGES: CORE.IMAGES
};
