<!-- 帮助中心页面 -->
<view class="help-container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-container">
      <text class="search-icon">🔍</text>
      <input class="search-input" 
             placeholder="搜索帮助内容、常见问题..." 
             value="{{searchKeyword}}" 
             bindinput="onSearchInput" 
             bindconfirm="onSearch" 
             bindfocus="onSearchFocus" />
      <text class="clear-btn" wx:if="{{searchKeyword}}" bindtap="onClearSearch">×</text>
    </view>
    <button class="search-btn" bindtap="onSearch">搜索</button>
  </view>

  <!-- 搜索建议 -->
  <view class="search-suggestions" wx:if="{{showSuggestions && suggestions.length > 0}}">
    <view class="suggestion-item" 
          wx:for="{{suggestions}}" 
          wx:key="*this"
          bindtap="onSelectSuggestion" 
          data-keyword="{{item}}">
      <text class="suggestion-text">{{item}}</text>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{isSearching}}">
    <view class="results-header">
      <text class="results-title">搜索结果</text>
      <text class="results-count">共{{searchResults.total}}条结果</text>
    </view>
    
    <!-- 分类结果 -->
    <view class="result-section" wx:if="{{searchResults.categories > 0}}">
      <text class="result-section-title">相关分类</text>
      <view class="category-results">
        <view class="category-result-item" 
              wx:for="{{categories}}" 
              wx:key="id"
              bindtap="onViewCategory" 
              data-category="{{item}}">
          <text class="category-result-icon">{{item.icon}}</text>
          <text class="category-result-title">{{item.title}}</text>
        </view>
      </view>
    </view>

    <!-- 问题结果 -->
    <view class="result-section" wx:if="{{searchResults.questions > 0}}">
      <text class="result-section-title">相关问题</text>
      <view class="question-results">
        <view class="question-result-item" 
              wx:for="{{commonQuestions}}" 
              wx:key="id"
              bindtap="onToggleFAQ" 
              data-index="{{index}}">
          <text class="question-result-text">{{item.question}}</text>
          <text class="question-result-arrow">></text>
        </view>
      </view>
    </view>

    <view class="no-results" wx:if="{{searchResults.total === 0}}">
      <text class="no-results-icon">😕</text>
      <text class="no-results-text">未找到相关内容</text>
      <text class="no-results-tips">试试其他关键词或浏览下方分类</text>
    </view>
  </view>

  <!-- 正常内容 -->
  <view class="normal-content" wx:if="{{!isSearching}}">


    <!-- 帮助分类 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">帮助分类</text>
      </view>
      <view class="category-grid">
        <view class="category-item" 
              wx:for="{{helpCategories}}" 
              wx:key="id"
              bindtap="onViewCategory" 
              data-category="{{item}}">
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-title">{{item.title}}</text>
          <text class="category-count">{{item.articles.length}}篇文章</text>
        </view>
      </view>
    </view>
  
    <!-- 常见问题 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">常见问题</text>
        <text class="view-more" bindtap="onViewMoreFAQ">查看更多</text>
      </view>
      <view class="faq-list">
        <view class="faq-item" wx:for="{{faqList.slice(0, 5)}}" wx:key="id">
          <view class="faq-question" bindtap="onToggleFAQ" data-index="{{index}}">
            <text class="question-text">{{item.question}}</text>
            <text class="arrow-icon {{item.expanded ? 'expanded' : ''}}">{{item.expanded ? '▲' : '▼'}}</text>
          </view>
          <view wx:if="{{item.expanded}}" class="faq-answer">
            <text class="answer-text">{{item.answer}}</text>
            <view class="answer-actions">
              <text class="helpful-btn" bindtap="onMarkHelpful" data-id="{{item.id}}">👍 有帮助</text>
              <text class="unhelpful-btn" bindtap="onMarkUnhelpful" data-id="{{item.id}}">👎 没帮助</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门教程 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">热门教程</text>
      </view>
      <view class="tutorial-list">
        <view class="tutorial-item" 
              wx:for="{{hotTutorials}}" 
              wx:key="id"
              bindtap="onViewTutorial" 
              data-tutorial="{{item}}">
          <text class="tutorial-icon">{{item.icon}}</text>
          <view class="tutorial-content">
            <text class="tutorial-title">{{item.title}}</text>
            <text class="tutorial-desc">{{item.description}}</text>
            <view class="tutorial-meta">
              <text class="tutorial-views">{{item.views}}次浏览</text>
              <text class="tutorial-time">{{item.duration}}</text>
            </view>
          </view>
          <text class="tutorial-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">联系我们</text>
      </view>
      <view class="contact-info">
        <view class="contact-item" bindtap="onCall">
          <text class="contact-icon">📞</text>
          <view class="contact-details">
            <text class="contact-title">客服热线</text>
            <text class="contact-value">{{contactInfo.phone}}</text>
          </view>
          <text class="contact-action">拨打</text>
        </view>
        <view class="contact-item" bindtap="onCopyEmail">
          <text class="contact-icon">📧</text>
          <view class="contact-details">
            <text class="contact-title">邮箱地址</text>
            <text class="contact-value">{{contactInfo.email}}</text>
          </view>
          <text class="contact-action">复制</text>
        </view>
        <view class="contact-item">
          <text class="contact-icon">🕒</text>
          <view class="contact-details">
            <text class="contact-title">工作时间</text>
            <text class="contact-value">{{contactInfo.workTime}}</text>
          </view>
        </view>
        <view class="contact-item" bindtap="onOpenChat">
          <text class="contact-icon">💬</text>
          <view class="contact-details">
            <text class="contact-title">在线客服</text>
            <text class="contact-value">智能客服24小时在线</text>
          </view>
          <text class="contact-action">咨询</text>
        </view>
      </view>
    </view>

  </view>


</view>