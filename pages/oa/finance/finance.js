// pages/oa/finance/finance.js

/**
 * 财务管理模块主页面 - OA标准版本
 * 功能：财务数据概览、快捷操作、活动记录
 * 符合OA系统统一标准和API规范
 */

const { PERMISSIONS, PermissionMixin } = require('../../../utils/oa-permissions');
const { formatCurrency, formatDate } = require('../../../utils/format');
const request = require('../../../utils/request');
const { API } = require('../../../constants/index.js');
const { createLoading, wrapWithLoading } = require('../../../utils/loading-final-solution');
let logger; try { logger = require('../../../utils/logger.js'); } catch(_) { logger = console; }

Page({
  // 混入权限检查功能
  ...PermissionMixin,
  
  data: {
    loading: false,
    permissionReady: false,
    
    // 时间范围选项
    timeRanges: [
      { label: '本月', value: 'month' },
      { label: '本季度', value: 'quarter' },
      { label: '本年', value: 'year' },
      { label: '自定义', value: 'custom' }
    ],
    selectedTimeRange: '本月',
    
    // 模态框相关
    showStatModal: false,
    currentStatType: '',
    statModalData: null,
    
    // 财务统计数据
    financialStats: {
      totalRevenue: 125000.00,     // 总收入
      totalExpense: 89000.00,      // 总支出
      netProfit: 36000.00,         // 净利润
      accountBalance: 158000.00,   // 账户余额
      revenueGrowth: 12.5,         // 收入增长率
      expenseGrowth: 8.2,          // 支出增长率
      profitGrowth: 15.8,          // 利润增长率
    },
    
    // 最近活动
    recentActivities: [
      {
        id: 'activity_001',
        type: 'income',
        icon: '💰',
        title: '商品销售收入',
        description: '在线订单收款',
        amount: '+￥5,800',
        time: '2小时前'
      },
      {
        id: 'activity_002',
        type: 'expense',
        icon: '💸',
        title: '采购费用支出',
        description: '饲料采购付款',
        amount: '-￥2,300',
        time: '5小时前'
      },
      {
        id: 'activity_003',
        type: 'transfer',
        icon: '🔄',
        title: '内部资金调拨',
        description: '运营资金转账',
        amount: '￥10,000',
        time: '1天前'
      },
      {
        id: 'activity_004',
        type: 'income',
        icon: '💰',
        title: '投资收益',
        description: '理财产品收益',
        amount: '+￥1,200',
        time: '2天前'
      }
    ],
    
    // 财务提醒
    alerts: [
      {
        id: 'alert_001',
        level: 'warning',
        icon: '⚠️',
        message: '本月支出已超预算15%，请注意控制',
        time: '今天',
        action: 'budget'
      },
      {
        id: 'alert_002',
        level: 'error',
        icon: 'ℹ️',
        message: '季度财务报表需要在月底前提交',
        time: '昨天',
        action: 'report'
      }
    ]
  },

  /**
   * 页面加载
   */
  onLoad: function(options) {
    try { logger.debug && logger.debug('财务管理页面加载参数', options); } catch(_) {}
    // 先启用调试模式
    wx.setStorageSync('debugMode', 'true');
    // 执行权限检查
    this.checkPagePermissions();
  },

  /**
   * 页面显示
   */
  onShow: function() {
    // 页面显示时刷新数据，但要避免重复加载
    if (!this.data.loading && this.data.permissionReady) {
      this.refreshData();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady: function() {
    try { logger.debug && logger.debug('财务管理权限检查完成'); } catch(_) {}
    this.setData({ permissionReady: true });
    this.loadFinanceData();
  },

  // ==================== 数据加载 ====================

  /**
   * 加载财务数据
   */
  async loadFinanceData() {
    // 防止重复加载
    if (this.data.loading) {
      try { logger.debug && logger.debug('数据正在加载中，跳过重复请求'); } catch(_) {}
      return;
    }
    
    this.setData({ loading: true });
    
    try {
      await wrapWithLoading(async () => {
        await Promise.all([
          this.loadFinancialStats(),
          this.loadRecentActivities(),
          this.loadAlerts()
        ]);
      }, {
        title: '加载财务数据...',
        mask: true
      });
      
      this.setData({ loading: false });
    } catch (error) {
      try { logger.error && logger.error('加载财务数据失败', error); } catch(_) {}
      this.setData({ loading: false });
      this.showToast('数据加载失败', 'none');
    }
  },

  /**
   * 加载财务统计数据
   */
  loadFinancialStats: function() {
    return new Promise((resolve) => {
      // 模拟API调用
      request.get(API.ENDPOINTS.OA.FINANCE.STATS, {
        timeRange: this.data.selectedTimeRange
      }).then((result) => {
        if (result.success) {
          this.setData({
            financialStats: result.data
          });
        }
        resolve();
      }).catch(() => {
        // 使用模拟数据
        resolve();
      });
    });
  },

  /**
   * 加载最近活动数据
   */
  loadRecentActivities: function() {
    return new Promise((resolve) => {
      request.get(API.ENDPOINTS.OA.FINANCE.ACTIVITIES, {
        limit: 10
      }).then((result) => {
        if (result.success) {
          this.setData({
            recentActivities: result.data
          });
        }
        resolve();
      }).catch(() => {
        // 使用模拟数据
        resolve();
      });
    });
  },

  /**
   * 加载提醒数据
   */
  loadAlerts: function() {
    return new Promise((resolve) => {
      request.get(API.ENDPOINTS.OA.FINANCE.ALERTS).then((result) => {
        if (result.success) {
          this.setData({
            alerts: result.data
          });
        }
        resolve();
      }).catch(() => {
        // 使用模拟数据
        resolve();
      });
    });
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    return this.loadFinanceData();
  },

  // ==================== 事件处理 ====================

  /**
   * 时间范围选择
   */
  onTimeRangeChange: function(e) {
    const index = parseInt(e.detail.value);
    const selectedRange = this.data.timeRanges[index];
    
    try { logger.debug && logger.debug('切换时间范围', selectedRange); } catch(_) {}
    
    this.setData({
      selectedTimeRange: selectedRange.label
    });
    
    // 重新加载统计数据
    this.loadFinancialStats();
  },



  /**
   * 统计数据点击 - 显示详细数据模态框
   */
  onStatTap: function(e) {
    const { type } = e.currentTarget.dataset;
    try { logger.debug && logger.debug('点击统计项', type); } catch(_) {}
    
    // 获取对应类型的详细数据
    const detailData = this.getStatDetailData(type);
    
    // 显示数据详情模态框
    this.setData({
      showStatModal: true,
      currentStatType: type,
      statModalData: detailData
    });
  },

  /**
   * 获取统计项详细数据
   */
  getStatDetailData: function(type) {
    const stats = this.data.financialStats;
    
    switch(type) {
      case 'revenue':
        return {
          title: '收入详情',
          value: stats.totalRevenue,
          trend: `+${stats.revenueGrowth}%`,
          items: [
            { label: '商品销售', amount: '¥85,000', percent: '68%' },
            { label: '服务收入', amount: '¥25,000', percent: '20%' },
            { label: '其他收入', amount: '¥15,000', percent: '12%' }
          ]
        };
      case 'expense':
        return {
          title: '支出详情', 
          value: stats.totalExpense,
          trend: `-${stats.expenseGrowth}%`,
          items: [
            { label: '饲料采购', amount: '¥35,000', percent: '39%' },
            { label: '人工成本', amount: '¥28,000', percent: '31%' },
            { label: '设备维护', amount: '¥15,000', percent: '17%' },
            { label: '其他支出', amount: '¥11,000', percent: '13%' }
          ]
        };
      case 'profit':
        return {
          title: '利润详情',
          value: stats.netProfit,
          trend: `+${stats.profitGrowth}%`,
          items: [
            { label: '毛利润', amount: '¥48,000', percent: '133%' },
            { label: '运营费用', amount: '¥-12,000', percent: '-33%' },
            { label: '净利润', amount: '¥36,000', percent: '100%' }
          ]
        };
      case 'balance':
        return {
          title: '账户详情',
          value: stats.accountBalance,
          trend: '稳定',
          items: [
            { label: '现金账户', amount: '¥120,000', percent: '76%' },
            { label: '银行存款', amount: '¥30,000', percent: '19%' },
            { label: '其他资产', amount: '¥8,000', percent: '5%' }
          ]
        };
      default:
        return null;
    }
  },

  /**
   * 关闭统计详情模态框
   */
  closeStatModal: function() {
    this.setData({
      showStatModal: false,
      currentStatType: '',
      statModalData: null
    });
  },

  /**
   * 查看全部活动
   */
  onViewAllActivities: function() {
    try { logger.debug && logger.debug('查看全部活动'); } catch(_) {}
    
    wx.navigateTo({
      url: '/pages/oa/finance/overview/overview?tab=activities',
      fail: (err) => {
        try { logger.error && logger.error('跳转活动列表失败', err); } catch(_) {}
        this.showToast('页面跳转失败', 'none');
      }
    });
  },

  /**
   * 活动项点击
   */
  onActivityTap: function(e) {
    const { id } = e.currentTarget.dataset;
    const activity = this.data.recentActivities.find(item => item.id === id);
    
    try { logger.debug && logger.debug('点击活动', activity); } catch(_) {}
    
    if (activity) {
      wx.navigateTo({
        url: `/pages/oa/finance/detail/detail?id=${id}`,
        fail: () => {
          // 如果详情页面不存在，显示模态框
          this.showActivityDetail(activity);
        }
      });
    }
  },

  /**
   * 提醒处理
   */
  onAlertAction: function(e) {
    const { id } = e.currentTarget.dataset;
    const alert = this.data.alerts.find(item => item.id === id);
    
    try { logger.debug && logger.debug('处理提醒', alert); } catch(_) {}
    
    if (!alert) return;
    
    // 根据提醒类型执行相应操作
    switch(alert.action) {
      case 'budget':
        wx.navigateTo({
          url: '/pages/oa/finance/budget/budget'
        });
        break;
      case 'report':
        wx.navigateTo({
          url: '/pages/oa/finance/reports/reports'
        });
        break;
      default:
        const loading = createLoading({
          title: '处理提醒中...',
          mask: true
        });
        
        loading.show();
        
        // 模拟处理时间
        setTimeout(() => {
          loading.hide();
          this.showToast('处理完成', 'success');
        }, 1000);
        break;
    }
    
    // 标记提醒为已处理
    this.markAlertAsHandled(id);
  },

  // ==================== 辅助方法 ====================

  /**
   * 检查页面权限
   */
  checkPagePermissions: function() {
    // 先检查登录状态
    const token = wx.getStorageSync('access_token');
    const userInfo = wx.getStorageSync('user_info');
    
    try { logger.debug && logger.debug('[权限检查] Token', !!token); logger.debug && logger.debug('[权限检查] UserInfo', userInfo); } catch(_) {}
    
    if (!token || !userInfo) {
      wx.showModal({
        title: '未登录',
        content: '请先登录账号',
        showCancel: false,
        confirmText: '去登录',
        success: () => {
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      });
      return;
    }
    
    // 检查财务权限 - 使用正确的权限名称
    const hasPermission = this.hasPermission(PERMISSIONS.OA.FINANCE_VIEW);
    
    try { logger.debug && logger.debug('[权限检查] 财务权限', hasPermission); logger.debug && logger.debug('[权限检查] 用户角色', userInfo.roleCode || userInfo.role); } catch(_) {}
    
    if (!hasPermission) {
      // 检查是否是管理员角色
      const isAdmin = userInfo.roleCode === 'admin' || userInfo.role === '管理员' || userInfo.role === 'admin';
      
      if (isAdmin) {
        try { logger.debug && logger.debug('[权限检查] 管理员绕过权限检查'); } catch(_) {}
        this.onPermissionReady();
        return;
      }
      
      wx.showModal({
        title: '权限不足',
        content: '您没有访问财务管理的权限，请联系管理员',
        showCancel: false,
        confirmText: '返回',
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限检查通过，触发权限就绪回调
    this.onPermissionReady();
  },

  /**
   * 显示活动详情
   */
  showActivityDetail: function(activity) {
    wx.showModal({
      title: activity.title,
      content: `${activity.description}\n金额: ${activity.amount}\n时间: ${activity.time}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 标记提醒为已处理
   */
  markAlertAsHandled: function(alertId) {
    // 从API标记为已处理
    request.post(API.ENDPOINTS.OA.FINANCE.ALERTS_HANDLE, {
      alertId: alertId
    }).then(() => {
      // 从本地列表移除
      const alerts = this.data.alerts.filter(item => item.id !== alertId);
      this.setData({ alerts });
      this.showToast('提醒已处理', 'success');
    }).catch(() => {
      // 如果API失败，也从本地移除
      const alerts = this.data.alerts.filter(item => item.id !== alertId);
      this.setData({ alerts });
      this.showToast('提醒已处理', 'success');
    });
  },

  /**
   * 显示提示信息
   */
  showToast: function(title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000
    });
  }

});