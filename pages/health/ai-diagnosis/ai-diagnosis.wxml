<!-- pages/health/ai-diagnosis/ai-diagnosis.wxml -->
<view class="diagnosis-container">
  <view class="card">
    <view class="card-title">症状描述</view>
    <textarea class="symptom-input" placeholder="请详细描述鹅群的症状表现，如精神状态、食欲、粪便情况等..." bindinput="onSymptomInput" value="{{symptoms}}"></textarea>
  </view>
  
  <view class="card">
    <view class="card-title">图片上传</view>
    <view class="image-upload">
      <block wx:for="{{uploadedImages}}" wx:key="index">
        <view class="uploaded-image">
          <image src="{{item.url}}" mode="aspectFill" bindtap="onDeleteImage" data-index="{{index}}"></image>
          <view class="delete-btn" bindtap="onDeleteImage" data-index="{{index}}">✕</view>
        </view>
      </block>
      
      <view wx:if="{{uploadedImages.length < 3}}" class="upload-btn" bindtap="onChooseImage">
        <image src="/images/icon_camera.png" mode="aspectFit"></image>
        <text>上传图片</text>
      </view>
    </view>
    <view class="upload-tip">最多可上传3张图片，支持拍照或从相册选择</view>
  </view>
  
  <button class="btn btn-primary btn-block" bindtap="onStartDiagnosis" loading="{{isDiagnosing}}">
    {{isDiagnosing ? '诊断中...' : '开始AI诊断'}}
  </button>
  
  <!-- 诊断结果 -->
  <view wx:if="{{diagnosisResult}}" class="result-section">
    <view class="card">
      <view class="result-header">
        <text class="result-title">诊断结果</text>
        <view class="confidence">置信度: {{diagnosisResult.confidence}}</view>
      </view>
      
      <view class="disease-info">
        <text class="disease-name">{{diagnosisResult.disease}}</text>
        <text class="disease-desc">{{diagnosisResult.description}}</text>
      </view>
      
      <view class="suggestions">
        <view class="section-title">处理建议</view>
        <block wx:for="{{diagnosisResult.suggestions}}" wx:key="index">
          <view class="suggestion-item">{{index + 1}}. {{item}}</view>
        </block>
      </view>
      
      <view class="medications">
        <view class="section-title">推荐用药</view>
        <block wx:for="{{diagnosisResult.medications}}" wx:key="name">
          <view class="medication-item">
            <text class="medication-name">{{item.name}}</text>
            <text class="medication-dosage">{{item.dosage}}</text>
          </view>
        </block>
      </view>
    </view>
    
    <view class="result-actions">
      <button class="btn btn-default" bindtap="onClearResult">重新诊断</button>
      <button class="btn btn-primary" bindtap="onSaveResult">保存结果</button>
    </view>
  </view>
</view>