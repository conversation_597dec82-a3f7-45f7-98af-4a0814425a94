/**
 * 财务集成功能测试
 * 测试生产记录与财务记录的自动关联功能
 */

const request = require('supertest');
const app = require('../server'); // 假设服务器文件
const db = require('../config/database');

describe('财务集成功能测试', () => {
  let authToken;
  let testUserId = 1;
  let testBatchNumber;

  beforeAll(async () => {
    // 设置测试环境
    // 这里应该包含登录获取token的逻辑
    authToken = 'test-token';
    testBatchNumber = `TEST-${Date.now()}`;
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
  });

  describe('入栏记录与支出记录关联', () => {
    test('创建入栏记录应该自动生成支出记录', async () => {
      const entryData = {
        batchNumber: testBatchNumber,
        breed: 'taihu',
        count: 100,
        weight: 85,
        source: '测试供应商',
        supplier: '测试供应商',
        unitCost: 28.5,
        totalCost: 2850,
        recordDate: '2025-08-15',
        notes: '测试入栏记录',
        shedNumber: 'A1',
        dayAge: 1,
        healthStatus: 'healthy'
      };

      const response = await request(app)
        .post('/api/v1/production/entry-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(entryData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.entryRecord).toBeDefined();
      expect(response.body.data.financeRecordId).toBeDefined();

      // 验证财务记录是否正确创建
      const financeRecordId = response.body.data.financeRecordId;
      const [financeRecord] = await db.query(
        'SELECT * FROM financial_records WHERE id = ?',
        [financeRecordId]
      );

      expect(financeRecord.length).toBe(1);
      expect(financeRecord[0].type).toBe('expense');
      expect(financeRecord[0].category).toBe('鹅苗采购');
      expect(parseFloat(financeRecord[0].amount)).toBe(2850);
      expect(financeRecord[0].batch_number).toBe(testBatchNumber);
      expect(financeRecord[0].auto_generated).toBe(1);
      expect(financeRecord[0].related_record_type).toBe('entry');
    });

    test('入栏记录创建失败时不应该生成财务记录', async () => {
      const invalidEntryData = {
        // 缺少必填字段
        batchNumber: testBatchNumber,
        breed: 'taihu'
        // 缺少count和totalCost
      };

      const response = await request(app)
        .post('/api/v1/production/entry-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidEntryData)
        .expect(400);

      expect(response.body.success).toBe(false);

      // 验证没有创建财务记录
      const [financeRecords] = await db.query(
        'SELECT * FROM financial_records WHERE batch_number = ? AND related_record_type = "entry"',
        [testBatchNumber]
      );

      // 应该只有之前测试创建的一条记录
      expect(financeRecords.length).toBe(1);
    });
  });

  describe('出栏记录与收入记录关联', () => {
    test('创建出栏记录应该自动生成收入记录', async () => {
      const saleData = {
        batchNumber: testBatchNumber,
        count: 20,
        weight: 64,
        unitPrice: 35,
        totalIncome: 700,
        buyer: '测试买家',
        recordDate: '2025-08-15',
        notes: '测试出栏记录',
        averageWeight: 3.2
      };

      const response = await request(app)
        .post('/api/v1/production/sale-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(saleData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.saleRecord).toBeDefined();
      expect(response.body.data.financeRecordId).toBeDefined();

      // 验证财务记录是否正确创建
      const financeRecordId = response.body.data.financeRecordId;
      const [financeRecord] = await db.query(
        'SELECT * FROM financial_records WHERE id = ?',
        [financeRecordId]
      );

      expect(financeRecord.length).toBe(1);
      expect(financeRecord[0].type).toBe('income');
      expect(financeRecord[0].category).toBe('鹅只销售');
      expect(parseFloat(financeRecord[0].amount)).toBe(700);
      expect(financeRecord[0].batch_number).toBe(testBatchNumber);
      expect(financeRecord[0].auto_generated).toBe(1);
      expect(financeRecord[0].related_record_type).toBe('sale');
    });

    test('出栏数量超过存栏数时应该失败', async () => {
      const invalidSaleData = {
        batchNumber: testBatchNumber,
        count: 200, // 超过存栏数
        weight: 640,
        unitPrice: 35,
        totalIncome: 7000,
        buyer: '测试买家',
        recordDate: '2025-08-15'
      };

      const response = await request(app)
        .post('/api/v1/production/sale-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidSaleData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('出栏数量不能超过当前存栏数');
    });
  });

  describe('称重记录创建', () => {
    test('创建称重记录不应该生成财务记录', async () => {
      const weightData = {
        batchNumber: testBatchNumber,
        count: 50,
        averageWeight: 2.8,
        totalWeight: 140,
        recordDate: '2025-08-15',
        notes: '测试称重记录',
        feedRatio: '2.5:1'
      };

      const response = await request(app)
        .post('/api/v1/production/weight-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(weightData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();

      // 验证没有创建财务记录
      const [financeRecords] = await db.query(
        'SELECT * FROM financial_records WHERE batch_number = ? AND related_record_type = "weight"',
        [testBatchNumber]
      );

      expect(financeRecords.length).toBe(0);
    });
  });

  describe('批次财务统计', () => {
    test('获取批次财务统计应该返回正确的数据', async () => {
      const response = await request(app)
        .get(`/api/v1/finance/batch-stats/${testBatchNumber}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      
      const stats = response.body.data;
      expect(stats.batchNumber).toBe(testBatchNumber);
      expect(stats.totalCost).toBe(2850); // 入栏成本
      expect(stats.totalRevenue).toBe(700); // 出栏收入
      expect(stats.netProfit).toBe(-2150); // 净利润（负数，因为还没完全出栏）
      expect(stats.recordCount).toBe(2); // 一条支出，一条收入
      expect(stats.records).toHaveLength(2);
    });

    test('获取不存在批次的统计应该返回空数据', async () => {
      const response = await request(app)
        .get('/api/v1/finance/batch-stats/NON-EXISTENT-BATCH')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      
      const stats = response.body.data;
      expect(stats.totalCost).toBe(0);
      expect(stats.totalRevenue).toBe(0);
      expect(stats.netProfit).toBe(0);
      expect(stats.recordCount).toBe(0);
    });
  });

  describe('批次财务概览', () => {
    test('获取批次财务概览应该包含所有批次', async () => {
      const response = await request(app)
        .get('/api/v1/finance/batch-overview')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      
      const overview = response.body.data;
      expect(overview.batches).toBeDefined();
      expect(overview.summary).toBeDefined();
      
      // 查找测试批次
      const testBatch = overview.batches.find(b => b.batchNumber === testBatchNumber);
      expect(testBatch).toBeDefined();
      expect(testBatch.totalCost).toBe(2850);
      expect(testBatch.totalRevenue).toBe(700);
      expect(testBatch.netProfit).toBe(-2150);
    });
  });

  describe('财务记录管理', () => {
    test('不应该允许修改自动生成的财务记录', async () => {
      // 先获取自动生成的财务记录ID
      const [records] = await db.query(
        'SELECT id FROM financial_records WHERE batch_number = ? AND auto_generated = 1 LIMIT 1',
        [testBatchNumber]
      );

      expect(records.length).toBeGreaterThan(0);
      const recordId = records[0].id;

      const response = await request(app)
        .put(`/api/v1/finance/records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 3000,
          description: '尝试修改自动生成的记录'
        })
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('自动生成的记录不能手动修改');
    });

    test('不应该允许删除自动生成的财务记录', async () => {
      // 先获取自动生成的财务记录ID
      const [records] = await db.query(
        'SELECT id FROM financial_records WHERE batch_number = ? AND auto_generated = 1 LIMIT 1',
        [testBatchNumber]
      );

      expect(records.length).toBeGreaterThan(0);
      const recordId = records[0].id;

      const response = await request(app)
        .delete(`/api/v1/finance/records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('自动生成的记录不能删除');
    });

    test('应该允许创建手动财务记录', async () => {
      const manualRecord = {
        type: 'expense',
        category: '饲料费用',
        amount: 500,
        description: '手动添加的饲料费用',
        recordDate: '2025-08-15',
        batchNumber: testBatchNumber,
        supplier: '饲料供应商',
        notes: '手动创建的测试记录'
      };

      const response = await request(app)
        .post('/api/v1/finance/records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(manualRecord)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.auto_generated).toBe(0);
      expect(response.body.data.type).toBe('expense');
      expect(parseFloat(response.body.data.amount)).toBe(500);
    });
  });

  // 清理测试数据的辅助函数
  async function cleanupTestData() {
    try {
      // 删除测试的财务记录
      await db.query(
        'DELETE FROM financial_records WHERE batch_number = ?',
        [testBatchNumber]
      );

      // 删除测试的生产记录
      await db.query(
        'DELETE FROM production_records_v2 WHERE batch = ?',
        [testBatchNumber]
      );

      // 删除测试的鹅群记录
      await db.query(
        'DELETE FROM flocks WHERE batchNumber = ?',
        [testBatchNumber]
      );

      console.log('测试数据清理完成');
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }
});
