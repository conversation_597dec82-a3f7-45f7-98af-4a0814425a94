// backend/middleware/security.js
// 安全增强中间件

const rateLimit = require("express-rate-limit");
const { createError } = require("../utils/error-handler");
const { logOperation } = require("./audit-logger");

/**
 * 批量操作安全验证中间件
 * @param {Object} options - 配置选项
 * @returns {Function} Express中间件函数
 */
function batchOperationSecurity(options = {}) {
  const {
    maxItems = 100,
    requireConfirmation = true,
    allowedRoles = ["admin", "manager"],
    sensitiveOperations = ["DELETE", "UPDATE"],
  } = options;

  return async (req, res, next) => {
    try {
      // 检查是否为批量操作
      const isBatchOperation = req.body.ids && Array.isArray(req.body.ids);
      const isSensitiveOperation = sensitiveOperations.includes(req.method);

      if (!isBatchOperation || !isSensitiveOperation) {
        return next();
      }

      // 验证用户权限
      if (!req.user) {
        throw createError.tokenInvalid("用户未认证");
      }

      if (!allowedRoles.includes(req.user.role)) {
        // 记录安全事件
        await logOperation(req, {
          action: "SECURITY_VIOLATION",
          resource: "BATCH_OPERATION",
          level: "WARN",
          success: false,
          description: `用户 ${req.user.username} 尝试执行未授权的批量操作`,
          errorMessage: "权限不足",
        });

        throw createError.insufficientPermissions("权限不足，无法执行批量操作");
      }

      // 验证批量操作数量限制
      if (req.body.ids.length > maxItems) {
        throw createError.validationFailed(`批量操作数量不能超过${maxItems}个`);
      }

      // 验证确认参数
      if (requireConfirmation && !req.body.confirmed) {
        return res.status(400).json({
          success: false,
          message: "批量操作需要确认",
          requireConfirmation: true,
          data: {
            operation: req.method,
            itemCount: req.body.ids.length,
            confirmationRequired: true,
          },
        });
      }

      // 记录批量操作
      await logOperation(req, {
        action: "BATCH_OPERATION_AUTHORIZED",
        resource: "BATCH_OPERATION",
        level: "INFO",
        success: true,
        description: `用户 ${req.user.username} 执行批量${req.method}操作，影响${req.body.ids.length}个项目`,
      });

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * 敏感操作二次验证中间件
 * @param {Object} options - 配置选项
 * @returns {Function} Express中间件函数
 */
function sensitiveOperationVerification(options = {}) {
  const {
    requirePassword = true,
    requireTwoFactor = false,
    sensitiveActions = [
      "DELETE",
      "BATCH_DELETE",
      "ROLE_CHANGE",
      "PERMISSION_CHANGE",
    ],
  } = options;

  return async (req, res, next) => {
    try {
      const action = req.body.action || req.method;

      // 检查是否为敏感操作
      if (!sensitiveActions.includes(action)) {
        return next();
      }

      // 验证用户认证
      if (!req.user) {
        throw createError.tokenInvalid("用户未认证");
      }

      // 验证密码确认
      if (requirePassword && !req.body.password) {
        return res.status(400).json({
          success: false,
          message: "敏感操作需要密码确认",
          requirePasswordConfirmation: true,
        });
      }

      // 验证双因子认证
      if (requireTwoFactor && !req.body.twoFactorCode) {
        return res.status(400).json({
          success: false,
          message: "敏感操作需要双因子认证",
          requireTwoFactor: true,
        });
      }

      // 记录敏感操作
      await logOperation(req, {
        action: "SENSITIVE_OPERATION",
        resource: "SECURITY",
        level: "WARN",
        success: true,
        description: `用户 ${req.user.username} 执行敏感操作: ${action}`,
      });

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * IP白名单验证中间件
 * @param {Array} allowedIPs - 允许的IP地址列表
 * @returns {Function} Express中间件函数
 */
function ipWhitelist(allowedIPs = []) {
  return async (req, res, next) => {
    try {
      if (allowedIPs.length === 0) {
        return next();
      }

      const clientIP = req.ip || req.connection.remoteAddress;

      if (!allowedIPs.includes(clientIP)) {
        // 记录安全事件
        await logOperation(req, {
          action: "IP_BLOCKED",
          resource: "SECURITY",
          level: "WARN",
          success: false,
          description: `IP地址 ${clientIP} 不在白名单中`,
          errorMessage: "IP地址被拒绝",
        });

        throw createError.insufficientPermissions("IP地址不被允许");
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * 请求频率限制
 */
const createRateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15分钟
    max = 100, // 最大请求数
    message = "请求过于频繁，请稍后再试",
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
  } = options;

  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message,
      retryAfter: Math.ceil(windowMs / 1000),
    },
    skipSuccessfulRequests,
    skipFailedRequests,
    handler: async (req, res) => {
      // 记录频率限制事件
      await logOperation(req, {
        action: "RATE_LIMIT_EXCEEDED",
        resource: "SECURITY",
        level: "WARN",
        success: false,
        description: `IP ${req.ip} 超出请求频率限制`,
      });

      res.status(429).json({
        success: false,
        message,
        retryAfter: Math.ceil(windowMs / 1000),
      });
    },
  });
};

/**
 * 管理员操作验证中间件
 */
function adminOperationSecurity() {
  return async (req, res, next) => {
    try {
      // 验证管理员权限
      if (!req.user || req.user.role !== "admin") {
        // 记录未授权访问
        await logOperation(req, {
          action: "UNAUTHORIZED_ADMIN_ACCESS",
          resource: "ADMIN",
          level: "ERROR",
          success: false,
          description: `用户 ${req.user?.username || "anonymous"} 尝试访问管理员功能`,
        });

        throw createError.insufficientPermissions("需要管理员权限");
      }

      // 记录管理员操作
      await logOperation(req, {
        action: "ADMIN_OPERATION",
        resource: "ADMIN",
        level: "INFO",
        success: true,
        description: `管理员 ${req.user.username} 执行操作: ${req.method} ${req.originalUrl}`,
      });

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * SQL注入防护中间件
 */
function sqlInjectionProtection() {
  const suspiciousPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /('|(\\')|(;)|(\\;)|(\|)|(\*)|(%)|(<)|(>)|(\{)|(\})|(\[)|(\]))/,
    /((\%3C)|<)((\%2F)|\/)*[a-z0-9\%]+((\%3E)|>)/i,
    /((\%3C)|<)((\%69)|i|(\%49))((\%6D)|m|(\%4D))((\%67)|g|(\%47))[^\n]+((\%3E)|>)/i,
  ];

  return async (req, res, next) => {
    try {
      const checkData = (data, path = "") => {
        if (typeof data === "string") {
          for (const pattern of suspiciousPatterns) {
            if (pattern.test(data)) {
              throw new Error(
                `Suspicious pattern detected in ${path}: ${data.substring(0, 100)}`,
              );
            }
          }
        } else if (typeof data === "object" && data !== null) {
          for (const [key, value] of Object.entries(data)) {
            checkData(value, path ? `${path}.${key}` : key);
          }
        }
      };

      // 检查查询参数
      checkData(req.query, "query");

      // 检查请求体
      if (req.body) {
        checkData(req.body, "body");
      }

      next();
    } catch (error) {
      // 记录安全事件
      await logOperation(req, {
        action: "SQL_INJECTION_ATTEMPT",
        resource: "SECURITY",
        level: "CRITICAL",
        success: false,
        description: `检测到SQL注入尝试: ${error.message}`,
        errorMessage: error.message,
      });

      throw createError.validationFailed("请求包含不安全的内容");
    }
  };
}

/**
 * XSS防护中间件
 */
function xssProtection() {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
  ];

  return async (req, res, next) => {
    try {
      const sanitizeData = (data, path = "") => {
        if (typeof data === "string") {
          for (const pattern of xssPatterns) {
            if (pattern.test(data)) {
              throw new Error(
                `XSS pattern detected in ${path}: ${data.substring(0, 100)}`,
              );
            }
          }
          return data;
        } else if (typeof data === "object" && data !== null) {
          const sanitized = {};
          for (const [key, value] of Object.entries(data)) {
            sanitized[key] = sanitizeData(value, path ? `${path}.${key}` : key);
          }
          return sanitized;
        }
        return data;
      };

      // 清理查询参数
      req.query = sanitizeData(req.query, "query");

      // 清理请求体
      if (req.body) {
        req.body = sanitizeData(req.body, "body");
      }

      next();
    } catch (error) {
      // 记录安全事件
      await logOperation(req, {
        action: "XSS_ATTEMPT",
        resource: "SECURITY",
        level: "CRITICAL",
        success: false,
        description: `检测到XSS攻击尝试: ${error.message}`,
        errorMessage: error.message,
      });

      throw createError.validationFailed("请求包含不安全的脚本内容");
    }
  };
}

/**
 * 精细化权限控制中间件
 * @param {string|Array} requiredPermissions - 需要的权限
 * @param {Object} options - 配置选项
 * @returns {Function} Express中间件函数
 */
function requirePermissions(requiredPermissions, options = {}) {
  const {
    requireAll = true, // 是否需要所有权限
    checkResourceOwnership = false, // 是否检查资源所有权
    resourceIdParam = "id", // 资源ID参数名
  } = options;

  return async (req, res, next) => {
    try {
      // 验证用户认证
      if (!req.user) {
        throw createError.tokenInvalid("用户未认证");
      }

      // 获取用户权限（这里需要实现获取用户权限的逻辑）
      const userPermissions = await getUserPermissions(req.user.id);

      // 转换为数组
      const permissions = Array.isArray(requiredPermissions)
        ? requiredPermissions
        : [requiredPermissions];

      // 检查权限
      let hasPermission = false;

      if (requireAll) {
        // 需要所有权限
        hasPermission = permissions.every((perm) =>
          userPermissions.includes(perm),
        );
      } else {
        // 需要任一权限
        hasPermission = permissions.some((perm) =>
          userPermissions.includes(perm),
        );
      }

      if (!hasPermission) {
        // 记录权限拒绝事件
        await logOperation(req, {
          action: "PERMISSION_DENIED",
          resource: "SECURITY",
          level: "WARN",
          success: false,
          description: `用户 ${req.user.username} 缺少权限: ${permissions.join(", ")}`,
        });

        throw createError.insufficientPermissions("权限不足");
      }

      // 检查资源所有权
      if (checkResourceOwnership && req.params[resourceIdParam]) {
        const hasOwnership = await checkResourceOwnership(
          req.user.id,
          req.params[resourceIdParam],
          req.route.path,
        );

        if (!hasOwnership) {
          throw createError.insufficientPermissions("无权访问此资源");
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * 获取用户权限列表
 * @param {number} userId - 用户ID
 * @returns {Array} 权限列表
 */
async function getUserPermissions(userId) {
  // 这里应该从数据库查询用户的所有权限
  // 包括直接分配的权限和通过角色获得的权限

  // 简化实现，实际应该查询数据库
  const mockPermissions = [
    "health:read",
    "health:create",
    "health:update",
    "production:read",
    "production:create",
  ];

  return mockPermissions;
}

/**
 * 检查资源所有权
 * @param {number} userId - 用户ID
 * @param {string} resourceId - 资源ID
 * @param {string} resourceType - 资源类型
 * @returns {boolean} 是否拥有资源
 */
async function checkResourceOwnership(userId, resourceId, resourceType) {
  // 这里应该根据资源类型查询数据库
  // 检查用户是否拥有该资源

  // 简化实现
  return true;
}

module.exports = {
  batchOperationSecurity,
  sensitiveOperationVerification,
  ipWhitelist,
  createRateLimit,
  adminOperationSecurity,
  sqlInjectionProtection,
  xssProtection,
  requirePermissions,
  getUserPermissions,
  checkResourceOwnership,
};
