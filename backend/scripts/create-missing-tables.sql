-- 智慧养鹅系统 - 创建缺失的核心数据表
-- 执行前确保已连接到 zhihuiyange_local 数据库

USE zhihuiyange_local;

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 1. 任务管理表
-- ================================
DROP TABLE IF EXISTS tasks;
CREATE TABLE tasks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  title VARCHAR(200) NOT NULL COMMENT '任务标题',
  description TEXT COMMENT '任务描述',
  status ENUM('pending', 'processing', 'completed', 'overdue') DEFAULT 'pending' COMMENT '任务状态',
  priority ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT '优先级',
  category VARCHAR(50) COMMENT '任务分类',
  assignee VARCHAR(100) COMMENT '负责人',
  deadline DATETIME COMMENT '截止时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_status (user_id, status),
  INDEX idx_deadline (deadline),
  INDEX idx_priority (priority),
  INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务管理表';

-- ================================
-- 2. 公告管理表
-- ================================
DROP TABLE IF EXISTS announcements;
CREATE TABLE announcements (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(200) NOT NULL COMMENT '公告标题',
  content TEXT NOT NULL COMMENT '公告内容',
  summary VARCHAR(500) COMMENT '公告摘要',
  type ENUM('important', 'notice', 'policy', 'activity') NOT NULL COMMENT '公告类型',
  publisher VARCHAR(100) NOT NULL COMMENT '发布者',
  is_top BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
  is_important BOOLEAN DEFAULT FALSE COMMENT '是否重要',
  view_count INT DEFAULT 0 COMMENT '浏览次数',
  attachment_count INT DEFAULT 0 COMMENT '附件数量',
  publish_time DATETIME NOT NULL COMMENT '发布时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_type_publish (type, publish_time),
  INDEX idx_top_important (is_top, is_important),
  INDEX idx_publish_time (publish_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告管理表';

-- ================================
-- 3. 公告已读记录表
-- ================================
DROP TABLE IF EXISTS announcement_reads;
CREATE TABLE announcement_reads (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  announcement_id INT NOT NULL COMMENT '公告ID',
  read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (announcement_id) REFERENCES announcements(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_announcement (user_id, announcement_id),
  INDEX idx_user_id (user_id),
  INDEX idx_announcement_id (announcement_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告已读记录表';

-- ================================
-- 4. 价格记录表
-- ================================
DROP TABLE IF EXISTS price_records;
CREATE TABLE price_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  breed_name VARCHAR(100) NOT NULL COMMENT '品种名称',
  breed_type ENUM('gosling', 'adult') NOT NULL COMMENT '品种类型：鹅苗/成鹅',
  price DECIMAL(10,2) NOT NULL COMMENT '价格',
  change_rate DECIMAL(5,2) COMMENT '变化率(%)',
  market_location VARCHAR(100) COMMENT '市场地点',
  record_date DATE NOT NULL COMMENT '记录日期',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_breed_date (breed_name, record_date),
  INDEX idx_type_date (breed_type, record_date),
  INDEX idx_record_date (record_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格记录表';

-- ================================
-- 5. 价格订阅表
-- ================================
DROP TABLE IF EXISTS price_subscriptions;
CREATE TABLE price_subscriptions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  breed_name VARCHAR(100) NOT NULL COMMENT '品种名称',
  breed_type ENUM('gosling', 'adult') NOT NULL COMMENT '品种类型',
  threshold_price DECIMAL(10,2) COMMENT '阈值价格',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_active (user_id, is_active),
  INDEX idx_breed_type (breed_name, breed_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格订阅表';

-- ================================
-- 6. 库存记录表
-- ================================
DROP TABLE IF EXISTS inventory_records;
CREATE TABLE inventory_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  breed_name VARCHAR(100) NOT NULL COMMENT '品种名称',
  age_category ENUM('gosling', 'young', 'adult') NOT NULL COMMENT '年龄分类',
  area_location VARCHAR(50) NOT NULL COMMENT '区域位置',
  count INT NOT NULL COMMENT '数量',
  health_status ENUM('healthy', 'sick', 'quarantine') DEFAULT 'healthy' COMMENT '健康状态',
  record_date DATE NOT NULL COMMENT '记录日期',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_date (user_id, record_date),
  INDEX idx_health_status (health_status),
  INDEX idx_area_location (area_location),
  INDEX idx_age_category (age_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存记录表';

-- ================================
-- 7. 库存预警表
-- ================================
DROP TABLE IF EXISTS inventory_alerts;
CREATE TABLE inventory_alerts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  alert_type ENUM('density', 'health', 'vaccination', 'environment') NOT NULL COMMENT '预警类型',
  title VARCHAR(200) NOT NULL COMMENT '预警标题',
  description TEXT COMMENT '预警描述',
  level ENUM('info', 'warning', 'error') DEFAULT 'info' COMMENT '预警级别',
  area_location VARCHAR(50) COMMENT '相关区域',
  is_resolved BOOLEAN DEFAULT FALSE COMMENT '是否已解决',
  resolved_at TIMESTAMP NULL COMMENT '解决时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_resolved (user_id, is_resolved),
  INDEX idx_alert_type (alert_type),
  INDEX idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存预警表';

-- ================================
-- 8. 物料管理表（扩展现有功能）
-- ================================
DROP TABLE IF EXISTS materials;
CREATE TABLE materials (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  name VARCHAR(100) NOT NULL COMMENT '物料名称',
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  category ENUM('feed', 'medicine', 'equipment', 'other') NOT NULL COMMENT '物料分类',
  specification VARCHAR(100) COMMENT '规格',
  unit VARCHAR(20) NOT NULL COMMENT '单位',
  supplier VARCHAR(100) COMMENT '供应商',
  shelf_life VARCHAR(50) COMMENT '保质期',
  current_stock DECIMAL(10,2) DEFAULT 0 COMMENT '当前库存',
  safety_stock DECIMAL(10,2) DEFAULT 0 COMMENT '安全库存',
  max_stock DECIMAL(10,2) DEFAULT 0 COMMENT '最大库存',
  status ENUM('normal', 'warning', 'danger') DEFAULT 'normal' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_category (user_id, category),
  INDEX idx_code (code),
  INDEX idx_status (status),
  INDEX idx_supplier (supplier)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料管理表';

-- ================================
-- 9. 物料库存记录表
-- ================================
DROP TABLE IF EXISTS material_stock_records;
CREATE TABLE material_stock_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  material_id INT NOT NULL COMMENT '物料ID',
  type ENUM('in', 'out') NOT NULL COMMENT '出入库类型',
  amount DECIMAL(10,2) NOT NULL COMMENT '数量',
  unit_price DECIMAL(10,2) COMMENT '单价',
  total_price DECIMAL(10,2) COMMENT '总价',
  description TEXT COMMENT '描述',
  operator_id INT NOT NULL COMMENT '操作员ID',
  batch_number VARCHAR(50) COMMENT '批次号',
  expiry_date DATE COMMENT '过期日期',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
  FOREIGN KEY (operator_id) REFERENCES users(id),
  INDEX idx_material_type (material_id, type),
  INDEX idx_created_at (created_at),
  INDEX idx_batch_number (batch_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料库存记录表';

-- ================================
-- 10. 物料预警设置表
-- ================================
DROP TABLE IF EXISTS material_alerts;
CREATE TABLE material_alerts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  material_id INT NOT NULL COMMENT '物料ID',
  alert_type ENUM('lowStock', 'expiry', 'purchase') NOT NULL COMMENT '预警类型',
  is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  threshold_value DECIMAL(10,2) COMMENT '阈值',
  threshold_days INT COMMENT '天数阈值（用于过期预警）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
  INDEX idx_material_type (material_id, alert_type),
  INDEX idx_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料预警设置表';

-- ================================
-- 插入测试数据
-- ================================

-- 插入任务测试数据
INSERT INTO tasks (user_id, title, description, status, priority, category, assignee, deadline) VALUES
(1, '鹅舍清洁消毒', 'A区鹅舍需要进行全面清洁和消毒工作', 'pending', 'high', '日常维护', '张三', '2024-01-20 18:00:00'),
(1, '疫苗接种', 'B区雏鹅第二次疫苗接种', 'processing', 'high', '健康管理', '李四', '2024-01-21 10:00:00'),
(1, '饲料采购', '采购下周所需的优质鹅饲料', 'completed', 'medium', '物料管理', '王五', '2024-01-19 16:00:00'),
(1, '环境监测', '检查各区域温湿度和空气质量', 'overdue', 'medium', '环境监控', '赵六', '2024-01-18 12:00:00'),
(1, '设备维护', '检查和维护自动喂食设备', 'pending', 'low', '设备维护', '孙七', '2024-01-22 14:00:00');

-- 插入公告测试数据
INSERT INTO announcements (title, content, summary, type, publisher, is_top, is_important, view_count, attachment_count, publish_time) VALUES
('关于加强冬季养殖管理的重要通知', '随着气温下降，各养殖户需要特别注意保温措施...', '随着气温下降，各养殖户需要特别注意保温措施，确保鹅群健康过冬...', 'important', '养殖管理部', TRUE, TRUE, 156, 2, '2024-01-20 10:00:00'),
('新版养殖管理系统上线公告', '为了提升用户体验，我们对养殖管理系统进行了全面升级...', '为了提升用户体验，我们对养殖管理系统进行了全面升级...', 'notice', '技术部', FALSE, FALSE, 89, 0, '2024-01-20 15:00:00'),
('2024年养殖补贴政策解读', '根据最新政策文件，2024年度养殖补贴标准有所调整...', '根据最新政策文件，2024年度养殖补贴标准有所调整...', 'policy', '政策解读组', FALSE, TRUE, 234, 3, '2024-01-19 09:00:00'),
('春季养殖技术培训班报名通知', '为提高养殖户的技术水平，特举办春季养殖技术培训班...', '为提高养殖户的技术水平，特举办春季养殖技术培训班...', 'activity', '培训中心', FALSE, FALSE, 67, 1, '2024-01-18 14:00:00'),
('疫病防控工作指导意见', '针对近期疫病防控形势，现发布最新的防控工作指导意见...', '针对近期疫病防控形势，现发布最新的防控工作指导意见...', 'important', '防疫部门', FALSE, TRUE, 178, 0, '2024-01-17 11:00:00');

-- 插入价格记录测试数据
INSERT INTO price_records (breed_name, breed_type, price, change_rate, market_location, record_date) VALUES
('白鹅苗', 'gosling', 12.50, 2.1, '山东济南', '2024-01-20'),
('灰鹅苗', 'gosling', 11.80, -1.2, '山东济南', '2024-01-20'),
('狮头鹅苗', 'gosling', 15.00, 3.1, '广东汕头', '2024-01-20'),
('白鹅', 'adult', 18.20, 1.5, '山东济南', '2024-01-20'),
('灰鹅', 'adult', 17.20, -2.1, '山东济南', '2024-01-20'),
('狮头鹅', 'adult', 22.00, 4.2, '广东汕头', '2024-01-20');

-- 插入库存记录测试数据
INSERT INTO inventory_records (user_id, breed_name, age_category, area_location, count, health_status, record_date) VALUES
(1, '白鹅', 'gosling', 'A区', 320, 'healthy', '2024-01-20'),
(1, '白鹅', 'young', 'B区', 450, 'healthy', '2024-01-20'),
(1, '白鹅', 'adult', 'C区', 480, 'healthy', '2024-01-20'),
(1, '灰鹅', 'gosling', 'A区', 180, 'healthy', '2024-01-20'),
(1, '灰鹅', 'young', 'B区', 200, 'sick', '2024-01-20'),
(1, '狮头鹅', 'adult', 'D区', 150, 'quarantine', '2024-01-20');

-- 插入物料测试数据
INSERT INTO materials (user_id, name, code, category, specification, unit, supplier, shelf_life, current_stock, safety_stock, max_stock, status) VALUES
(1, '优质鹅饲料', 'MT001', 'feed', '25kg/袋', '袋', '绿野饲料有限公司', '6个月', 156, 50, 300, 'normal'),
(1, '鹅用疫苗', 'MT002', 'medicine', '100ml/瓶', '瓶', '动物疫苗公司', '12个月', 25, 10, 50, 'warning'),
(1, '自动喂食器', 'MT003', 'equipment', '不锈钢材质', '台', '养殖设备厂', '5年', 8, 2, 15, 'normal');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
