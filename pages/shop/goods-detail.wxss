/* pages/shop/goods-detail.wxss */

/* 容器 */
.detail-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 图片画廊 */
.image-gallery {
  position: relative;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.goods-swiper {
  width: 100%;
  height: 700rpx;
}

.goods-image {
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
}

.image-indicator {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  backdrop-filter: blur(10rpx);
}

/* 商品信息区域 */
.product-info {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

/* 价格区域 */
.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.current-price {
  font-size: 56rpx;
  font-weight: bold;
  color: #ff4757;
  margin-right: 20rpx;
}

.original-price {
  font-size: 32rpx;
  color: #999999;
  text-decoration: line-through;
}

/* 商品标题 */
.product-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 商品描述 */
.product-description {
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #ecf0f1;
  margin: 30rpx 0;
}

/* 商品统计信息 */
.product-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #95a5a6;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #ecf0f1;
  display: flex;
  gap: 20rpx;
  backdrop-filter: blur(20rpx);
  z-index: 1000;
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cart-button {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #2d3436;
  box-shadow: 0 8rpx 24rpx rgba(253, 203, 110, 0.3);
}

.cart-button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(253, 203, 110, 0.4);
}

.buy-button {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(232, 67, 147, 0.3);
}

.buy-button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(232, 67, 147, 0.4);
}

.button-icon {
  font-size: 32rpx;
}

.button-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* SKU选择区域 */
.sku-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.sku-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.sku-group {
  margin-bottom: 30rpx;
}

.sku-label {
  font-size: 28rpx;
  color: #7f8c8d;
  margin-bottom: 15rpx;
}

.sku-values {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.sku-value {
  padding: 12rpx 24rpx;
  border: 2rpx solid #ecf0f1;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #2c3e50;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.sku-value.active {
  border-color: #ff4757;
  background-color: #ff4757;
  color: white;
  transform: scale(1.05);
}

/* 商品详情区域 */
.detail-section,
.params-section,
.reviews-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  padding: 30rpx 30rpx 20rpx;
}

.detail-content {
  padding: 0 30rpx 30rpx;
}

.detail-images {
  margin-bottom: 20rpx;
}

.detail-image {
  width: 100%;
  margin-bottom: 15rpx;
  border-radius: 12rpx;
}

.detail-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #34495e;
}

/* 商品参数 */
.params-list {
  padding: 0 30rpx 30rpx;
}

.param-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.param-item:last-child {
  border-bottom: none;
}

.param-name {
  font-size: 28rpx;
  color: #7f8c8d;
}

.param-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 商品评价 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #ecf0f1;
}

.review-summary {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.rating-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}

.rating-stars {
  display: flex;
  gap: 2rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
}

.star.filled {
  color: #ffd700;
}

.review-count {
  font-size: 24rpx;
  color: #7f8c8d;
}

.reviews-list {
  padding: 0 30rpx;
}

.review-item {
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.reviewer-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.reviewer-info {
  flex: 1;
}

.reviewer-name {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 500;
}

.review-date {
  font-size: 22rpx;
  color: #95a5a6;
  margin-top: 5rpx;
}

.review-rating {
  display: flex;
  gap: 2rpx;
}

.review-content {
  font-size: 26rpx;
  line-height: 1.5;
  color: #34495e;
  margin-bottom: 15rpx;
}

.review-images {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.view-all-reviews {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5rpx;
  padding: 30rpx;
  color: #ff4757;
  font-size: 28rpx;
  font-weight: 500;
}

.arrow {
  font-size: 20rpx;
}

/* 响应式设计 */
@media (max-width: 480rpx) {
  .product-info {
    padding: 30rpx 20rpx;
  }

  .current-price {
    font-size: 48rpx;
  }

  .product-title {
    font-size: 32rpx;
  }

  .bottom-actions {
    padding: 15rpx 20rpx calc(15rpx + env(safe-area-inset-bottom));
  }

  .action-button {
    height: 80rpx;
  }

  .sku-section,
  .detail-section,
  .params-section,
  .reviews-section {
    padding: 20rpx 15rpx;
  }

  .section-title {
    padding: 20rpx 15rpx 15rpx;
  }
}