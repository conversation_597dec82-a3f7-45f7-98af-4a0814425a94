/**
 * 租户API路由 - 面向小程序的多租户API接口
 * Tenant API Routes - Multi-tenant API interfaces for Mini Programs
 */

const express = require("express");
const router = express.Router();
const { Op, sequelize } = require("sequelize");

// 数据模型
const Product = require("../models/product.model");

// 多租户中间件
const {
  identifyTenant,
  requireTenantPermission,
  authenticateTenantUser,
  rateLimitTenant,
  recordTenantUsage,
} = require("../middleware/tenant.middleware");

// RBAC权限控制中间件
const {
  PERMISSIONS,
  requirePermission,
  requireResourceOwner,
  rowLevelSecurity,
  auditLog,
  requireFeature,
} = require("../middleware/rbac.middleware");

// 控制器
const authController = require("../controllers/tenant-auth.controller");
const userController = require("../controllers/tenant-user.controller");
const flockController = require("../controllers/tenant-flock.controller");
const healthController = require("../controllers/tenant-health.controller");
const productionController = require("../controllers/tenant-production.controller");

// 应用中间件到所有租户API路由
router.use(identifyTenant);
router.use(rateLimitTenant);
router.use(recordTenantUsage);

/**
 * 微信小程序登录和认证
 */
// 微信小程序登录
router.post(
  "/auth/wechat-login",
  auditLog("wechat_login"),
  authController.wechatLogin,
);

// 获取用户信息
router.get("/auth/profile", authenticateTenantUser, authController.getProfile);

// 刷新令牌
router.post(
  "/auth/refresh",
  auditLog("refresh_token"),
  authController.refreshToken,
);

/**
 * 用户管理模块 (requires 'basic' plan or higher + USER permissions)
 */
router.use(
  "/users",
  requireTenantPermission(["basic", "standard", "premium", "enterprise"]),
);

// 获取用户列表
router.get(
  "/users",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.USER_READ),
  rowLevelSecurity(),
  userController.getUsers,
);

// 获取用户详情
router.get(
  "/users/:id",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.USER_READ),
  requireResourceOwner("id", "users"),
  userController.getUserById,
);

// 更新用户信息
router.put(
  "/users/:id",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.USER_UPDATE),
  requireResourceOwner("id", "users"),
  auditLog("update_user"),
  userController.updateUser,
);

/**
 * 鹅群管理模块 (all versions + FLOCK permissions)
 */
// 获取鹅群列表
router.get(
  "/flocks",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.FLOCK_READ),
  rowLevelSecurity(),
  flockController.getFlocks,
);

// 获取鹅群详情
router.get(
  "/flocks/:id",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.FLOCK_READ),
  requireResourceOwner("id", "flocks"),
  flockController.getFlockById,
);

// 创建鹅群
router.post(
  "/flocks",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.FLOCK_CREATE),
  auditLog("create_flock"),
  flockController.createFlock,
);

// 更新鹅群信息
router.put(
  "/flocks/:id",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.FLOCK_UPDATE),
  requireResourceOwner("id", "flocks"),
  auditLog("update_flock"),
  flockController.updateFlock,
);

// 删除鹅群
router.delete(
  "/flocks/:id",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.FLOCK_DELETE),
  requireResourceOwner("id", "flocks"),
  auditLog("delete_flock"),
  flockController.deleteFlock,
);

// 鹅群统计信息
router.get(
  "/flocks/:id/stats",
  authenticateTenantUser,
  requirePermission(PERMISSIONS.FLOCK_READ),
  requireResourceOwner("id", "flocks"),
  flockController.getFlockStats,
);

/**
 * 健康管理模块
 * 标准版及以上可用
 */
router.use(
  "/health",
  requireTenantPermission(["standard", "premium", "enterprise"]),
);

// 健康记录列表
router.get(
  "/health/records",
  authenticateTenantUser,
  healthController.getHealthRecords,
);

// 创建健康记录
router.post(
  "/health/records",
  authenticateTenantUser,
  healthController.createHealthRecord,
);

// 健康统计
router.get(
  "/health/stats",
  authenticateTenantUser,
  healthController.getHealthStats,
);

// AI健康诊断 (高级版及以上)
router.post(
  "/health/ai-diagnosis",
  requireTenantPermission(["premium", "enterprise"], ["ai_diagnosis"]),
  authenticateTenantUser,
  healthController.aiDiagnosis,
);

/**
 * 生产管理模块
 * 标准版及以上可用
 */
router.use(
  "/production",
  requireTenantPermission(["standard", "premium", "enterprise"]),
);

// 生产记录列表
router.get(
  "/production/records",
  authenticateTenantUser,
  productionController.getProductionRecords,
);

// 创建生产记录
router.post(
  "/production/records",
  authenticateTenantUser,
  productionController.createProductionRecord,
);

// 生产统计
router.get(
  "/production/stats",
  authenticateTenantUser,
  productionController.getProductionStats,
);

// 生产趋势分析 (高级版及以上)
router.get(
  "/production/trends",
  requireTenantPermission(["premium", "enterprise"], ["advanced_analytics"]),
  authenticateTenantUser,
  productionController.getProductionTrends,
);

/**
 * 商城模块
 * 高级版及以上可用
 */
router.use(
  "/shop",
  requireTenantPermission(["premium", "enterprise"], ["shop_module"]),
);

// 商品列表
router.get("/shop/products", authenticateTenantUser, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category = "",
      search = "",
      sortBy = "created_at",
      sortOrder = "DESC",
      status = "active",
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const whereConditions = { status };

    // 分类筛选
    if (category) {
      whereConditions.category = category;
    }

    // 搜索功能
    if (search) {
      whereConditions[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
        { tags: { [Op.like]: `%${search}%` } },
      ];
    }

    const products = await Product.findAndCountAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: offset,
      order: [[sortBy, sortOrder]],
      attributes: [
        "id",
        "name",
        "description",
        "price",
        "category",
        "image",
        "stock",
        "status",
        "tags",
        "specifications",
        "salesCount",
        "created_at",
      ],
    });

    res.json({
      success: true,
      message: "获取商品列表成功",
      data: {
        products: products.rows,
        pagination: {
          current: parseInt(page),
          total: Math.ceil(products.count / parseInt(limit)),
          pageSize: parseInt(limit),
          totalItems: products.count,
        },
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取商品列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取商品列表失败",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// 商品详情
router.get("/shop/products/:id", authenticateTenantUser, async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findOne({
      where: { id, status: "active" },
      attributes: [
        "id",
        "name",
        "description",
        "price",
        "category",
        "image",
        "stock",
        "status",
        "tags",
        "specifications",
        "salesCount",
        "created_at",
      ],
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "商品不存在或已下架",
      });
    }

    res.json({
      success: true,
      message: "获取商品详情成功",
      data: product,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取商品详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取商品详情失败",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// 商品分类列表
router.get("/shop/categories", authenticateTenantUser, async (req, res) => {
  try {
    const categories = await Product.findAll({
      attributes: [
        "category",
        [sequelize.fn("COUNT", sequelize.col("id")), "count"],
      ],
      where: { status: "active" },
      group: ["category"],
      order: [["category", "ASC"]],
    });

    const categoryData = categories.map((item) => ({
      name: item.category,
      count: parseInt(item.dataValues.count),
      label: getCategoryLabel(item.category),
    }));

    res.json({
      success: true,
      message: "获取分类列表成功",
      data: categoryData,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取分类列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取分类列表失败",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// 添加到购物车
router.post("/shop/cart", authenticateTenantUser, async (req, res) => {
  try {
    const { productId, quantity = 1 } = req.body;
    const userId = req.user.id;

    // 验证商品是否存在且有库存
    const product = await Product.findOne({
      where: { id: productId, status: "active" },
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "商品不存在或已下架",
      });
    }

    if (product.stock < quantity) {
      return res.status(400).json({
        success: false,
        message: "库存不足",
      });
    }

    // 模拟添加到购物车（实际项目中需要购物车数据表）
    const cartItem = {
      id: Date.now(),
      userId: userId,
      productId: productId,
      product: {
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
      },
      quantity: quantity,
      addTime: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "添加到购物车成功",
      data: cartItem,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('添加购物车失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "添加购物车失败",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// 获取购物车
router.get("/shop/cart", authenticateTenantUser, async (req, res) => {
  try {
    // 模拟购物车数据（实际项目中从数据库获取）
    const mockCartItems = [
      {
        id: 1,
        productId: 1,
        product: {
          id: 1,
          name: "优质鹅饲料",
          price: 99.99,
          image: "/images/icons/goods1.png",
        },
        quantity: 2,
        addTime: new Date().toISOString(),
      },
      {
        id: 2,
        productId: 2,
        product: {
          id: 2,
          name: "疫苗套装",
          price: 199.99,
          image: "/images/icons/goods2.png",
        },
        quantity: 1,
        addTime: new Date().toISOString(),
      },
    ];

    const totalAmount = mockCartItems.reduce(
      (sum, item) => sum + item.product.price * item.quantity,
      0,
    );

    res.json({
      success: true,
      message: "获取购物车成功",
      data: {
        items: mockCartItems,
        totalItems: mockCartItems.length,
        totalAmount: totalAmount.toFixed(2),
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取购物车失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取购物车失败",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// 创建订单
router.post("/shop/orders", authenticateTenantUser, async (req, res) => {
  try {
    const { items, deliveryAddress, paymentMethod, remark } = req.body;
    const userId = req.user.id;

    // 验证订单数据
    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: "订单商品不能为空",
      });
    }

    // 计算订单总金额
    let totalAmount = 0;
    for (const item of items) {
      const product = await Product.findByPk(item.productId);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: `商品${item.productId}不存在`,
        });
      }
      totalAmount += product.price * item.quantity;
    }

    // 创建订单（模拟）
    const order = {
      id: Date.now(),
      userId: userId,
      orderNo: `ORD${Date.now()}`,
      items: items,
      totalAmount: totalAmount.toFixed(2),
      deliveryAddress: deliveryAddress,
      paymentMethod: paymentMethod,
      remark: remark,
      status: "pending",
      createTime: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "订单创建成功",
      data: order,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('创建订单失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "创建订单失败",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// 分类标签映射
function getCategoryLabel(category) {
  const categoryMap = {
    feed: "饲料",
    medicine: "药品",
    equipment: "设备",
    supplement: "营养品",
    cleaning: "清洁用品",
    other: "其他",
  };
  return categoryMap[category] || category;
}

/**
 * 知识库模块
 * 所有版本可用，但企业版有更多内容
 */
// 知识库文章列表
router.get("/knowledge/articles", authenticateTenantUser, (req, res) => {
  const isEnterprise = req.tenant.subscriptionPlan === "enterprise";
  res.json({
    success: true,
    data: {
      articles: [],
      hasAdvancedContent: isEnterprise,
    },
  });
});

/**
 * 系统配置和功能权限查询
 */
// 获取租户功能配置
router.get("/config/features", authenticateTenantUser, (req, res) => {
  const tenant = req.tenant;
  const features = tenant.features ? JSON.parse(tenant.features) : [];

  res.json({
    success: true,
    data: {
      tenantCode: tenant.tenantCode,
      subscriptionPlan: tenant.subscriptionPlan,
      features: features,
      limits: {
        maxUsers: tenant.maxUsers,
        maxFlocks: tenant.maxFlocks,
        storageLimit: tenant.storageLimit,
        apiCallsLimit: tenant.apiCallsLimit,
      },
      subscriptionInfo: {
        startDate: tenant.subscriptionStartDate,
        endDate: tenant.subscriptionEndDate,
      },
    },
  });
});

// 获取小程序配置
router.get("/config/miniprogram", authenticateTenantUser, async (req, res) => {
  try {
    const [configResults] = await req.tenantDb.query(
      "SELECT * FROM tenant_miniprogram_configs WHERE tenantId = ?",
      {
        replacements: [req.tenant.id],
        type: req.tenantDb.QueryTypes.SELECT,
      },
    );

    if (configResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: "小程序配置不存在",
      });
    }

    const config = configResults[0];

    res.json({
      success: true,
      data: {
        version: config.version,
        customDomain: config.customDomain,
        themeConfig: config.themeConfig ? JSON.parse(config.themeConfig) : {},
        featuresConfig: config.featuresConfig
          ? JSON.parse(config.featuresConfig)
          : {},
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取小程序配置失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取配置失败",
    });
  }
});

/**
 * 错误处理
 */
router.use((error, req, res, next) => {
  try { const { Logger } = require('../middleware/errorHandler'); Logger.error('租户API错误', { error: error.message, stack: error.stack, path: req.originalUrl, method: req.method }); } catch(_) {}

  if (error.code === "TENANT_CODE_MISSING") {
    return res.status(400).json({
      success: false,
      message: "缺少租户标识，请检查请求头或域名",
      code: error.code,
    });
  }

  if (error.code === "TENANT_NOT_FOUND") {
    return res.status(404).json({
      success: false,
      message: "租户不存在或已停用",
      code: error.code,
    });
  }

  if (error.code === "SUBSCRIPTION_EXPIRED") {
    return res.status(403).json({
      success: false,
      message: "订阅已过期，请联系管理员续费",
      code: error.code,
    });
  }

  res.status(500).json({
    success: false,
    message: "服务器内部错误",
    code: "INTERNAL_SERVER_ERROR",
  });
});

module.exports = router;
