/* 关于我们页面样式 */
.about-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

/* 应用信息区域 */
.app-info-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.app-logo {
  margin-bottom: 24rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
}

.app-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.app-version {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  align-self: center;
}

.app-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-top: 16rpx;
}

/* 产品特色区域 */
.features-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 公司信息区域 */
.company-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.company-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  color: #ffffff;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.company-desc {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 24rpx;
  display: block;
  opacity: 0.9;
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  opacity: 0.8;
  min-width: 160rpx;
}

.detail-value {
  font-size: 26rpx;
  flex: 1;
}

/* 技术团队区域 */
.team-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.team-member {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-bottom: 12rpx;
}

.member-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.member-role {
  font-size: 24rpx;
  color: #666;
}

/* 联系方式区域 */
.contact-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.contact-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.contact-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.contact-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
}

.contact-value {
  font-size: 26rpx;
  color: #666;
}

.contact-action {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

/* 法律信息区域 */
.legal-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.legal-links {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.legal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.legal-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.legal-title {
  font-size: 28rpx;
  color: #1a1a1a;
}

.legal-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 更新日志区域 */
.changelog-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.changelog-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.changelog-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.changelog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.changelog-version {
  font-size: 28rpx;
  font-weight: 600;
  color: #007AFF;
}

.changelog-date {
  font-size: 24rpx;
  color: #666;
}

.changelog-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.changelog-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 反馈按钮区域 */
.feedback-section {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.feedback-btn,
.help-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}

.feedback-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(238, 90, 82, 0.3);
}

.help-btn {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(68, 160, 141, 0.3);
}

.feedback-btn:active,
.help-btn:active {
  transform: scale(0.95);
}

.feedback-text,
.help-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* 版权信息区域 */
.copyright-section {
  text-align: center;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  margin-bottom: 40rpx;
}

.copyright-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  display: block;
}

/* 响应式适配 */
@media (max-width: 480rpx) {

  .features-grid,
  .team-grid {
    grid-template-columns: 1fr;
  }

  .feedback-section {
    flex-direction: column;
  }

  .about-container {
    padding: 16rpx;
  }
}

/* 动画效果 */
.app-info-section,
.features-section,
.company-section,
.team-section,
.contact-section,
.legal-section,
.changelog-section {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}