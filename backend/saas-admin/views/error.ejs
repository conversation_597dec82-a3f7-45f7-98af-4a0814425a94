<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title || '系统错误' %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }

        .error-icon {
            font-size: 4rem;
            color: #ef4444;
            margin-bottom: 24px;
        }

        .error-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .error-message {
            color: #6b7280;
            font-size: 1.1rem;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
        }

        .btn-outline-secondary {
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="bi bi-exclamation-triangle"></i>
        </div>
        <h1 class="error-title"><%= title || '系统错误' %></h1>
        <p class="error-message">
            <%= message || '抱歉，系统遇到了一个错误。请稍后重试或联系管理员。' %>
        </p>
        <div class="error-actions">
            <button type="button" class="btn btn-primary" onclick="history.back()">
                <i class="bi bi-arrow-left"></i>
                返回上页
            </button>
            <a href="/saas-admin/dashboard" class="btn btn-outline-secondary">
                <i class="bi bi-house"></i>
                返回首页
            </a>
        </div>
    </div>
</body>
</html>
