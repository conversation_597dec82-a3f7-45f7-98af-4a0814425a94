// pages/oa/finance/reports/reports.js
const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info.js');
const { isLoggedIn, getCurrentUser } = require('../../../../utils/auth-helper.js');
const { formatCurrency, formatDate, formatPercent } = require('../../../../utils/format.js');
const request = require('../../../../utils/request.js');
const { API } = require('../../../../constants/index.js');
const { PERMISSIONS, PermissionMixin } = require('../../../../utils/oa-permissions');

Page({
  // 混入权限检查功能
  ...PermissionMixin,
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,
    
    // 报表配置
    reportTypes: [
      { id: 'income_expense', name: '收支明细表', icon: 'chart', description: '详细的收入和支出记录' },
      { id: 'monthly_summary', name: '月度汇总表', icon: 'calendar', description: '按月汇总的财务数据' },
      { id: 'quarterly_report', name: '季度报表', icon: 'graph', description: '季度财务分析报表' },
      { id: 'annual_report', name: '年度报表', icon: 'document', description: '年度综合财务报表' },
      { id: 'category_analysis', name: '分类分析表', icon: 'pie-chart', description: '按类别分析收支情况' },
      { id: 'trend_analysis', name: '趋势分析表', icon: 'trending-up', description: '财务趋势变化分析' }
    ],
    
    selectedReportType: 'income_expense',
    
    // 时间范围
    timeRanges: [
      { id: 'this_month', name: '本月' },
      { id: 'last_month', name: '上月' },
      { id: 'this_quarter', name: '本季度' },
      { id: 'last_quarter', name: '上季度' },
      { id: 'this_year', name: '今年' },
      { id: 'last_year', name: '去年' },
      { id: 'custom', name: '自定义' }
    ],
    
    selectedTimeRange: 'this_month',
    selectedTimeRangeName: '本月', // 添加预处理的时间范围名称
    customStartDate: '',
    customEndDate: '',
    
    // 报表数据
    reportData: null,
    summary: {
      totalIncome: 0,
      totalExpense: 0,
      netProfit: 0,
      recordCount: 0
    },
    
    // 状态
    loading: false,
    generating: false,
    showTimeRangePicker: false,
    showCustomDatePicker: false,
    
    // 导出选项
    exportFormats: [
      { id: 'excel', name: 'Excel表格', ext: '.xlsx' },
      { id: 'pdf', name: 'PDF文档', ext: '.pdf' },
      { id: 'csv', name: 'CSV数据', ext: '.csv' }
    ]
  },

  onLoad(options) {
    this.initPage();
    
    // 如果有传入的报表类型，则使用传入的
    if (options.type) {
      this.setData({
        selectedReportType: options.type
      });
    }
  },
  
  onShow() {
    this.loadReportData();
  },
  
  onPullDownRefresh() {
    this.loadReportData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 使用auth-helper进行可靠的登录检查

    if (!isLoggedIn()) {

      wx.showModal({

        title: '需要登录',

        content: '使用此功能需要先登录系统',

        confirmText: '去登录',

        cancelText: '取消',

        success: (res) => {

          if (res.confirm) {

            wx.redirectTo({ url: '/pages/login/login' });

          } else {

            wx.navigateBack();

          }

        }

      });

      return;

    }


    // 获取用户信息并同步数据

    const authUser = getCurrentUser();

    let userInfo = getCurrentUserInfo();


    if (!userInfo && authUser) {

      const app = getApp();

      app.globalData.userInfo = authUser;

      wx.setStorageSync('user_info', authUser);

      userInfo = getCurrentUserInfo();

    }

    

    const permissions = getUserPermissions();


    if (!userInfo) {

      wx.showModal({

        title: '数据错误',

        content: '无法获取用户信息，请重新登录',

        confirmText: '重新登录',

        showCancel: false,

        success: () => {

          wx.redirectTo({ url: '/pages/login/login' });

        }

      });

      return;

    }
    
    this.setData({
      userInfo,
      permissions
    });
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查财务查看权限
    if (!this.hasPermission(PERMISSIONS.OA.FINANCE_VIEW)) {
      wx.showModal({
        title: '没有权限',
        content: '您没有查看财务报表的权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限验证通过，加载数据
    this.loadReportData();
  },

  /**
   * 加载报表数据
   */
  async loadReportData() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const { startDate, endDate } = this.getDateRange();
      const reportType = this.data.selectedReportType;
      const res = await request.get(`${API.ENDPOINTS.OA.FINANCE.REPORTS}`, {
        type: reportType,
        startDate,
        endDate
      });
      const payload = res && res.success !== false ? (res.data || res) : null;
      if (!payload) throw new Error(res?.message || '加载报表数据失败');
      this.setData({
        reportData: payload.reportData || payload.data || payload,
        summary: payload.summary || this.data.summary
      });
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载报表数据失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 获取日期范围
   */
  getDateRange() {
    const now = new Date();
    const timeRange = this.data.selectedTimeRange;
    
    let startDate, endDate;
    
    switch (timeRange) {
      case 'this_month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      case 'last_month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case 'this_quarter':
        const thisQuarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), thisQuarter * 3, 1);
        endDate = new Date(now.getFullYear(), (thisQuarter + 1) * 3, 0);
        break;
      case 'last_quarter':
        const lastQuarter = Math.floor(now.getMonth() / 3) - 1;
        startDate = new Date(now.getFullYear(), lastQuarter * 3, 1);
        endDate = new Date(now.getFullYear(), (lastQuarter + 1) * 3, 0);
        break;
      case 'this_year':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear(), 11, 31);
        break;
      case 'last_year':
        startDate = new Date(now.getFullYear() - 1, 0, 1);
        endDate = new Date(now.getFullYear() - 1, 11, 31);
        break;
      case 'custom':
        startDate = new Date(this.data.customStartDate);
        endDate = new Date(this.data.customEndDate);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    }
    
    return {
      startDate: this.formatDate(startDate, 'YYYY-MM-DD'),
      endDate: this.formatDate(endDate, 'YYYY-MM-DD')
    };
  },

  /**
   * 选择报表类型
   */
  onReportTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedReportType: type
    });
    this.loadReportData();
  },

  /**
   * 选择时间范围
   */
  onTimeRangeChange(e) {
    const range = e.currentTarget.dataset.range;
    const rangeName = this.data.timeRanges.find(r => r.id === range)?.name || '';
    
    this.setData({
      selectedTimeRange: range,
      selectedTimeRangeName: rangeName,
      showTimeRangePicker: false
    });
    
    if (range === 'custom') {
      this.setData({
        showCustomDatePicker: true
      });
    } else {
      this.loadReportData();
    }
  },

  /**
   * 显示/隐藏时间范围选择器
   */
  toggleTimeRangePicker() {
    this.setData({
      showTimeRangePicker: !this.data.showTimeRangePicker
    });
  },

  /**
   * 自定义开始日期选择
   */
  onStartDateChange(e) {
    this.setData({
      customStartDate: e.detail.value
    });
    this.validateCustomDateRange();
  },

  /**
   * 自定义结束日期选择
   */
  onEndDateChange(e) {
    this.setData({
      customEndDate: e.detail.value
    });
    this.validateCustomDateRange();
  },

  /**
   * 验证自定义日期范围
   */
  validateCustomDateRange() {
    const { customStartDate, customEndDate } = this.data;
    
    if (customStartDate && customEndDate) {
      if (new Date(customStartDate) > new Date(customEndDate)) {
        wx.showToast({
          title: '开始日期不能大于结束日期',
          icon: 'none'
        });
        return false;
      }
      this.loadReportData();
      return true;
    }
    
    return false;
  },

  /**
   * 确认自定义日期范围
   */
  confirmCustomDate() {
    if (this.validateCustomDateRange()) {
      this.setData({
        showCustomDatePicker: false
      });
    }
  },

  /**
   * 取消自定义日期范围
   */
  cancelCustomDate() {
    this.setData({
      showCustomDatePicker: false,
      selectedTimeRange: 'this_month'
    });
    this.loadReportData();
  },

  /**
   * 生成报表
   */
  async generateReport() {
    if (this.data.generating) return;
    
    this.setData({ generating: true });
    
    try {
      await this.loadReportData();
      
      wx.showToast({
        title: '报表生成成功',
        icon: 'success'
      });
    } catch (error) {
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    } finally {
      this.setData({ generating: false });
    }
  },

  /**
   * 导出报表
   */
  async exportReport(e) {
    const format = e.currentTarget.dataset.format;
    
    if (!this.data.permissions.canExportData) {
      wx.showToast({
        title: '没有导出权限',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.reportData) {
      wx.showToast({
        title: '请先生成报表',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '正在导出...'
    });
    
    try {
      const { startDate, endDate } = this.getDateRange();
      const res = await request.post(`${API.ENDPOINTS.OA.FINANCE.EXPORT}`, {
        type: this.data.selectedReportType,
        format,
        startDate,
        endDate,
        applicantInfo: {
          name: this.data.userInfo.name,
          department: this.data.userInfo.department,
          employeeId: this.data.userInfo.employeeId,
          exportTime: new Date().toISOString()
        }
      });
      if (res && res.success !== false) {
        wx.showToast({ title: '导出成功', icon: 'success' });
      } else {
        throw new Error(res?.message || '导出失败');
      }
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('导出报表失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '导出失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 点击报表数据行
   */
  onReportItemTap(e) {
    const item = e.currentTarget.dataset.item;
    
    // 显示详细信息
    wx.showModal({
      title: '交易详情',
      content: `类型：${item.type === 'income' ? '收入' : '支出'}\n金额：¥${item.amount}\n日期：${this.formatDate(item.date)}\n描述：${item.description || '无'}`,
      showCancel: false
    });
  },

  // 格式化方法
  formatCurrency(amount) {
    return '¥' + Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  },

  formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) return '-';
    
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day);
  },

  formatPercent(value) {
    return Number(value).toFixed(2) + '%';
  }
});