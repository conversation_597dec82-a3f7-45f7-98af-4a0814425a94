const express = require("express");
const router = express.Router();
const apiService = require("../utils/apiService");

/**
 * 首页路由 - 重定向到仪表板
 */
router.get("/", (req, res) => {
  res.redirect("/dashboard");
});

/**
 * 登录页面
 */
router.get("/login", (req, res) => {
  res.render("auth/login", {
    title: "登录 - 智慧养鹅管理系统",
    layout: false, // 登录页面不使用主布局
  });
});

/**
 * 登录处理
 */
router.post("/login", async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: "用户名和密码不能为空",
      });
    }

    // 简单的本地验证（演示用）
    const validUsers = {
      demo: "demo123",
      admin: "admin123",
      manager: "manager123",
    };

    if (validUsers[username] && validUsers[username] === password) {
      // 创建用户信息
      const user = {
        id: Date.now(),
        username: username,
        email: `${username}@example.com`,
        role:
          username === "admin"
            ? "admin"
            : username === "manager"
              ? "manager"
              : "user",
        status: "active",
        createdAt: new Date().toISOString(),
      };

      // 保存用户信息到session
      req.session.user = user;
      req.session.token = "demo-token-" + Date.now();

      res.json({
        success: true,
        message: "登录成功",
        redirect: "/dashboard",
      });
    } else {
      res.status(401).json({
        success: false,
        message: "用户名或密码错误",
      });
    }
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("Login error:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "登录过程中发生错误，请稍后再试",
    });
  }
});

/**
 * 退出登录
 */
router.get("/logout", (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("Logout error:", err); } catch(_) {}

    }
    res.redirect("/login");
  });
});

/**
 * 个人资料页面
 */
router.get("/profile", (req, res) => {
  if (!req.session.user) {
    return res.redirect("/login");
  }

  res.render("auth/profile", {
    title: "个人资料 - 智慧养鹅管理系统",
    user: req.session.user,
  });
});

module.exports = router;
