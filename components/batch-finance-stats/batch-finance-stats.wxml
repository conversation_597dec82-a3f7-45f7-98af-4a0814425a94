<!-- 批次财务统计组件模板 -->
<view class="batch-finance-stats">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text>加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <text class="error-message">{{error}}</text>
    <view class="retry-btn" bindtap="onRefresh">重试</view>
  </view>

  <!-- 统计数据 -->
  <view wx:else>
    <!-- 卡片样式 -->
    <view wx:if="{{styleType === 'card'}}" class="stats-card">
      <!-- 头部 -->
      <view class="stats-header">
        <view class="batch-title">{{stats.batchNumber}}</view>
        <view class="batch-subtitle">财务统计概览</view>
      </view>

      <!-- 统计网格 -->
      <view class="stats-grid">
        <view class="stats-item">
          <view class="stats-value">¥{{formatAmount(stats.totalCost)}}</view>
          <view class="stats-label">总成本</view>
        </view>
        <view class="stats-item">
          <view class="stats-value">¥{{formatAmount(stats.totalRevenue)}}</view>
          <view class="stats-label">总收入</view>
        </view>
        <view class="stats-item {{getProfitStatusClass(stats.netProfit)}}">
          <view class="stats-value">¥{{formatAmount(stats.netProfit)}}</view>
          <view class="stats-label">净利润</view>
        </view>
        <view class="stats-item profit-margin {{getProfitStatusClass(stats.netProfit)}}">
          <view class="stats-value">{{formatPercent(stats.profitMargin)}}</view>
          <view class="stats-label">利润率</view>
        </view>
      </view>

      <!-- 周期信息 -->
      <view class="cycle-info" wx:if="{{stats.cycleDays > 0}}">
        <text>养殖周期：{{stats.cycleDays}}天</text>
        <text>记录数量：{{stats.recordCount}}条</text>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn" bindtap="onRefresh">刷新</view>
        <view class="action-btn" bindtap="onViewTrends">趋势分析</view>
        <view class="action-btn" bindtap="onExportData">导出数据</view>
      </view>

      <!-- 详细记录 -->
      <view wx:if="{{showDetails && records.length > 0}}" class="records-section">
        <view class="records-header">
          <text class="records-title">财务记录</text>
          <text class="records-count">共{{records.length}}条</text>
        </view>
        <scroll-view class="records-list" scroll-y>
          <view 
            wx:for="{{records}}" 
            wx:key="id" 
            class="record-item {{getRecordTypeClass(item.type)}}"
            data-record="{{item}}"
            bindtap="onViewRecord"
          >
            <view class="record-left">
              <view class="record-description">{{item.description}}</view>
              <view class="record-meta">
                <text>{{formatDate(item.recordDate)}}</text>
                <text wx:if="{{item.supplier}}">{{item.supplier}}</text>
                <text wx:if="{{item.generation_type}}">{{item.generation_type}}</text>
              </view>
            </view>
            <view class="record-right">
              <view class="record-amount">¥{{formatAmount(item.amount)}}</view>
              <view class="record-type">{{getRecordTypeName(item.type)}}</view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 空记录状态 -->
      <view wx:elif="{{showDetails && records.length === 0}}" class="empty-records">
        <view class="empty-icon">📊</view>
        <view class="empty-text">暂无财务记录</view>
      </view>
    </view>

    <!-- 简单样式 -->
    <view wx:elif="{{styleType === 'simple'}}" class="stats-simple">
      <view class="stats-grid">
        <view class="stats-item">
          <view class="stats-value">¥{{formatAmount(stats.totalCost)}}</view>
          <view class="stats-label">总成本</view>
        </view>
        <view class="stats-item">
          <view class="stats-value">¥{{formatAmount(stats.totalRevenue)}}</view>
          <view class="stats-label">总收入</view>
        </view>
        <view class="stats-item {{getProfitStatusClass(stats.netProfit)}}">
          <view class="stats-value">¥{{formatAmount(stats.netProfit)}}</view>
          <view class="stats-label">净利润</view>
        </view>
      </view>
    </view>

    <!-- 紧凑样式 -->
    <view wx:elif="{{styleType === 'compact'}}" class="stats-compact">
      <view class="stats-item">
        <view class="stats-value">¥{{formatAmount(stats.totalCost)}}</view>
        <view class="stats-label">成本</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">¥{{formatAmount(stats.totalRevenue)}}</view>
        <view class="stats-label">收入</view>
      </view>
      <view class="stats-item {{getProfitStatusClass(stats.netProfit)}}">
        <view class="stats-value">¥{{formatAmount(stats.netProfit)}}</view>
        <view class="stats-label">利润</view>
      </view>
      <view class="stats-item {{getProfitStatusClass(stats.netProfit)}}">
        <view class="stats-value">{{formatPercent(stats.profitMargin)}}</view>
        <view class="stats-label">利润率</view>
      </view>
    </view>
  </view>
</view>
