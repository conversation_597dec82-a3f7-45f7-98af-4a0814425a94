-- 创建OA系统相关数据表
-- 智慧养鹅 OA办公自动化系统数据库结构

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==============================
-- OA系统基础表
-- ==============================

-- OA活动记录表
CREATE TABLE IF NOT EXISTS `oa_activities` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '活动类型：approval,purchase,reimbursement,workflow',
  `title` varchar(200) NOT NULL COMMENT '活动标题',
  `description` text COMMENT '活动描述',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '状态：pending,approved,rejected,completed',
  `reference_id` bigint(20) COMMENT '关联业务ID',
  `reference_type` varchar(50) COMMENT '关联业务类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type` (`type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OA活动记录表';

-- OA通知表
CREATE TABLE IF NOT EXISTS `oa_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `recipient_id` bigint(20) NOT NULL COMMENT '接收人ID',
  `sender_id` bigint(20) COMMENT '发送人ID',
  `type` varchar(50) NOT NULL COMMENT '通知类型：approval,task,finance,system',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text COMMENT '通知内容',
  `is_read` boolean NOT NULL DEFAULT false COMMENT '是否已读',
  `reference_id` bigint(20) COMMENT '关联业务ID',
  `reference_type` varchar(50) COMMENT '关联业务类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `read_at` timestamp NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  INDEX `idx_recipient_id` (`recipient_id`),
  INDEX `idx_is_read` (`is_read`),
  INDEX `idx_type` (`type`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OA通知表';

-- ==============================
-- 财务管理相关表
-- ==============================

-- 收入记录表
CREATE TABLE IF NOT EXISTS `oa_revenues` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '创建用户ID',
  `category` varchar(100) NOT NULL COMMENT '收入分类',
  `description` varchar(500) NOT NULL COMMENT '收入描述',
  `amount` decimal(15,2) NOT NULL COMMENT '收入金额',
  `expense_date` date NOT NULL COMMENT '收入日期',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '状态：pending,confirmed,cancelled',
  `payment_method` varchar(100) COMMENT '收款方式',
  `payment_account` varchar(200) COMMENT '收款账户',
  `invoice_number` varchar(100) COMMENT '发票号码',
  `customer_name` varchar(200) COMMENT '客户名称',
  `customer_contact` varchar(200) COMMENT '客户联系方式',
  `attachments` text COMMENT '附件信息（JSON格式）',
  `remarks` text COMMENT '备注',
  `confirmed_by` bigint(20) COMMENT '确认人ID',
  `confirmed_at` timestamp NULL COMMENT '确认时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_category` (`category`),
  INDEX `idx_status` (`status`),
  INDEX `idx_expense_date` (`expense_date`),
  INDEX `idx_amount` (`amount`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收入记录表';

-- 支出记录表
CREATE TABLE IF NOT EXISTS `oa_expenses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '创建用户ID',
  `category` varchar(100) NOT NULL COMMENT '支出分类',
  `description` varchar(500) NOT NULL COMMENT '支出描述',
  `amount` decimal(15,2) NOT NULL COMMENT '支出金额',
  `expense_date` date NOT NULL COMMENT '支出日期',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '状态：pending,approved,rejected,paid',
  `payment_status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '付款状态：pending,paid,overdue',
  `payment_method` varchar(100) COMMENT '付款方式',
  `payment_account` varchar(200) COMMENT '付款账户',
  `vendor_name` varchar(200) COMMENT '供应商名称',
  `vendor_contact` varchar(200) COMMENT '供应商联系方式',
  `invoice_number` varchar(100) COMMENT '发票号码',
  `purchase_order_id` bigint(20) COMMENT '关联采购单ID',
  `reimbursement_id` bigint(20) COMMENT '关联报销单ID',
  `attachments` text COMMENT '附件信息（JSON格式）',
  `remarks` text COMMENT '备注',
  `approved_by` bigint(20) COMMENT '审批人ID',
  `approved_at` timestamp NULL COMMENT '审批时间',
  `paid_by` bigint(20) COMMENT '付款人ID',
  `paid_at` timestamp NULL COMMENT '付款时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_category` (`category`),
  INDEX `idx_status` (`status`),
  INDEX `idx_payment_status` (`payment_status`),
  INDEX `idx_expense_date` (`expense_date`),
  INDEX `idx_amount` (`amount`),
  INDEX `idx_purchase_order_id` (`purchase_order_id`),
  INDEX `idx_reimbursement_id` (`reimbursement_id`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支出记录表';

-- ==============================
-- 采购管理相关表
-- ==============================

-- 采购申请表
CREATE TABLE IF NOT EXISTS `oa_purchase_requests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_number` varchar(100) NOT NULL COMMENT '采购申请编号',
  `user_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `department` varchar(100) COMMENT '申请部门',
  `title` varchar(200) NOT NULL COMMENT '申请标题',
  `description` text COMMENT '申请描述',
  `total_amount` decimal(15,2) NOT NULL COMMENT '申请总金额',
  `expected_date` date COMMENT '期望到货日期',
  `urgency_level` varchar(50) NOT NULL DEFAULT 'normal' COMMENT '紧急程度：low,normal,high,urgent',
  `status` varchar(50) NOT NULL DEFAULT 'draft' COMMENT '状态：draft,submitted,approved,rejected,completed,cancelled',
  `approval_flow_id` bigint(20) COMMENT '审批流程ID',
  `attachments` text COMMENT '附件信息（JSON格式）',
  `remarks` text COMMENT '备注',
  `submitted_at` timestamp NULL COMMENT '提交时间',
  `approved_by` bigint(20) COMMENT '审批人ID',
  `approved_at` timestamp NULL COMMENT '审批时间',
  `approval_remarks` text COMMENT '审批意见',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_number` (`request_number`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_urgency_level` (`urgency_level`),
  INDEX `idx_total_amount` (`total_amount`),
  INDEX `idx_expected_date` (`expected_date`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购申请表';

-- 采购申请明细表
CREATE TABLE IF NOT EXISTS `oa_purchase_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `purchase_request_id` bigint(20) NOT NULL COMMENT '采购申请ID',
  `item_name` varchar(200) NOT NULL COMMENT '物品名称',
  `item_specification` varchar(500) COMMENT '物品规格',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit` varchar(50) NOT NULL COMMENT '单位',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_price` decimal(15,2) NOT NULL COMMENT '总价',
  `category` varchar(100) COMMENT '物品分类',
  `brand` varchar(100) COMMENT '品牌',
  `supplier_suggestion` varchar(200) COMMENT '建议供应商',
  `remarks` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_purchase_request_id` (`purchase_request_id`),
  INDEX `idx_item_name` (`item_name`),
  INDEX `idx_category` (`category`),
  FOREIGN KEY (`purchase_request_id`) REFERENCES `oa_purchase_requests`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购申请明细表';

-- ==============================
-- 报销管理相关表
-- ==============================

-- 报销申请表
CREATE TABLE IF NOT EXISTS `oa_reimbursements` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `reimbursement_number` varchar(100) NOT NULL COMMENT '报销单号',
  `user_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `department` varchar(100) COMMENT '申请部门',
  `title` varchar(200) NOT NULL COMMENT '报销标题',
  `description` text COMMENT '报销说明',
  `total_amount` decimal(15,2) NOT NULL COMMENT '报销总金额',
  `category` varchar(100) NOT NULL COMMENT '报销类别：travel,office,meal,transport,other',
  `business_purpose` text COMMENT '业务目的',
  `expense_period_start` date COMMENT '费用发生开始日期',
  `expense_period_end` date COMMENT '费用发生结束日期',
  `status` varchar(50) NOT NULL DEFAULT 'draft' COMMENT '状态：draft,submitted,approved,rejected,paid,cancelled',
  `approval_flow_id` bigint(20) COMMENT '审批流程ID',
  `payment_method` varchar(100) COMMENT '报销方式',
  `bank_account` varchar(200) COMMENT '银行账户',
  `attachments` text COMMENT '附件信息（JSON格式）',
  `submitted_at` timestamp NULL COMMENT '提交时间',
  `approved_by` bigint(20) COMMENT '审批人ID',
  `approved_at` timestamp NULL COMMENT '审批时间',
  `approval_remarks` text COMMENT '审批意见',
  `paid_by` bigint(20) COMMENT '财务付款人ID',
  `paid_at` timestamp NULL COMMENT '付款时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_reimbursement_number` (`reimbursement_number`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_category` (`category`),
  INDEX `idx_status` (`status`),
  INDEX `idx_total_amount` (`total_amount`),
  INDEX `idx_expense_period` (`expense_period_start`, `expense_period_end`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报销申请表';

-- 报销明细表
CREATE TABLE IF NOT EXISTS `oa_reimbursement_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `reimbursement_id` bigint(20) NOT NULL COMMENT '报销申请ID',
  `expense_date` date NOT NULL COMMENT '费用发生日期',
  `item_description` varchar(500) NOT NULL COMMENT '费用描述',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `category` varchar(100) NOT NULL COMMENT '费用类别',
  `location` varchar(200) COMMENT '费用发生地点',
  `invoice_number` varchar(100) COMMENT '发票号码',
  `has_invoice` boolean NOT NULL DEFAULT false COMMENT '是否有发票',
  `remarks` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_reimbursement_id` (`reimbursement_id`),
  INDEX `idx_expense_date` (`expense_date`),
  INDEX `idx_category` (`category`),
  INDEX `idx_amount` (`amount`),
  FOREIGN KEY (`reimbursement_id`) REFERENCES `oa_reimbursements`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报销明细表';

-- ==============================
-- 审批管理相关表
-- ==============================

-- 审批流程定义表
CREATE TABLE IF NOT EXISTS `oa_approval_workflows` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(200) NOT NULL COMMENT '流程名称',
  `code` varchar(100) NOT NULL COMMENT '流程代码',
  `category` varchar(100) NOT NULL COMMENT '流程类别：purchase,reimbursement,leave,custom',
  `description` text COMMENT '流程描述',
  `trigger_condition` text COMMENT '触发条件（JSON格式）',
  `steps_config` text NOT NULL COMMENT '流程步骤配置（JSON格式）',
  `is_active` boolean NOT NULL DEFAULT true COMMENT '是否启用',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code_version` (`code`, `version`),
  INDEX `idx_category` (`category`),
  INDEX `idx_is_active` (`is_active`),
  INDEX `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批流程定义表';

-- 审批实例表
CREATE TABLE IF NOT EXISTS `oa_approvals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workflow_id` bigint(20) NOT NULL COMMENT '流程定义ID',
  `business_id` bigint(20) NOT NULL COMMENT '业务单据ID',
  `business_type` varchar(100) NOT NULL COMMENT '业务类型：purchase,reimbursement,leave',
  `title` varchar(200) NOT NULL COMMENT '审批标题',
  `applicant_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `current_step` int(11) NOT NULL DEFAULT 1 COMMENT '当前步骤',
  `total_steps` int(11) NOT NULL COMMENT '总步骤数',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '状态：pending,approved,rejected,cancelled',
  `priority` varchar(50) NOT NULL DEFAULT 'normal' COMMENT '优先级：low,normal,high,urgent',
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '启动时间',
  `completed_at` timestamp NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_workflow_id` (`workflow_id`),
  INDEX `idx_business` (`business_id`, `business_type`),
  INDEX `idx_applicant_id` (`applicant_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_priority` (`priority`),
  INDEX `idx_started_at` (`started_at`),
  FOREIGN KEY (`workflow_id`) REFERENCES `oa_approval_workflows`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批实例表';

-- 审批步骤记录表
CREATE TABLE IF NOT EXISTS `oa_approval_steps` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `approval_id` bigint(20) NOT NULL COMMENT '审批实例ID',
  `step_number` int(11) NOT NULL COMMENT '步骤序号',
  `step_name` varchar(200) NOT NULL COMMENT '步骤名称',
  `approver_id` bigint(20) NOT NULL COMMENT '审批人ID',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '状态：pending,approved,rejected,skipped',
  `action` varchar(50) COMMENT '操作：approve,reject,delegate',
  `remarks` text COMMENT '审批意见',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `processed_at` timestamp NULL COMMENT '处理时间',
  `deadline` timestamp NULL COMMENT '处理期限',
  `is_overdue` boolean NOT NULL DEFAULT false COMMENT '是否超期',
  `delegated_to` bigint(20) COMMENT '委托给谁',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_approval_id` (`approval_id`),
  INDEX `idx_approver_id` (`approver_id`),
  INDEX `idx_step_number` (`step_number`),
  INDEX `idx_status` (`status`),
  INDEX `idx_assigned_at` (`assigned_at`),
  INDEX `idx_deadline` (`deadline`),
  FOREIGN KEY (`approval_id`) REFERENCES `oa_approvals`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批步骤记录表';

-- ==============================
-- 任务管理相关表
-- ==============================

-- 任务表
CREATE TABLE IF NOT EXISTS `oa_tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `assignee_id` bigint(20) COMMENT '负责人ID',
  `category` varchar(100) COMMENT '任务类别',
  `priority` varchar(50) NOT NULL DEFAULT 'normal' COMMENT '优先级：low,normal,high,urgent',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '状态：pending,in_progress,completed,cancelled',
  `progress` int(11) NOT NULL DEFAULT 0 COMMENT '进度百分比',
  `estimated_hours` decimal(5,2) COMMENT '预估工时',
  `actual_hours` decimal(5,2) COMMENT '实际工时',
  `start_date` date COMMENT '开始日期',
  `due_date` date COMMENT '截止日期',
  `completed_date` date COMMENT '完成日期',
  `tags` varchar(500) COMMENT '标签（逗号分隔）',
  `attachments` text COMMENT '附件信息（JSON格式）',
  `parent_task_id` bigint(20) COMMENT '父任务ID',
  `business_id` bigint(20) COMMENT '关联业务ID',
  `business_type` varchar(100) COMMENT '关联业务类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_creator_id` (`creator_id`),
  INDEX `idx_assignee_id` (`assignee_id`),
  INDEX `idx_category` (`category`),
  INDEX `idx_priority` (`priority`),
  INDEX `idx_status` (`status`),
  INDEX `idx_due_date` (`due_date`),
  INDEX `idx_parent_task_id` (`parent_task_id`),
  INDEX `idx_business` (`business_id`, `business_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- ==============================
-- 系统配置相关表
-- ==============================

-- OA系统配置表
CREATE TABLE IF NOT EXISTS `oa_system_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(50) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
  `category` varchar(100) NOT NULL COMMENT '配置分类',
  `description` varchar(500) COMMENT '配置描述',
  `is_system` boolean NOT NULL DEFAULT false COMMENT '是否系统配置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  INDEX `idx_category` (`category`),
  INDEX `idx_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OA系统配置表';

-- ==============================
-- 初始化数据
-- ==============================

-- 插入默认审批流程
INSERT INTO `oa_approval_workflows` (`name`, `code`, `category`, `description`, `steps_config`, `created_by`) VALUES
('采购申请审批流程', 'purchase_approval', 'purchase', '适用于所有采购申请的标准审批流程', '[{"step":1,"name":"部门主管审批","roles":["department_manager"],"condition":"amount<10000"},{"step":2,"name":"财务审批","roles":["finance_manager"],"condition":"amount>=10000"},{"step":3,"name":"总经理审批","roles":["general_manager"],"condition":"amount>=50000"}]', 1),
('报销申请审批流程', 'reimbursement_approval', 'reimbursement', '适用于费用报销的标准审批流程', '[{"step":1,"name":"直接主管审批","roles":["direct_manager"]},{"step":2,"name":"财务审批","roles":["finance_manager"],"condition":"amount>=5000"}]', 1),
('请假申请审批流程', 'leave_approval', 'leave', '适用于员工请假申请的审批流程', '[{"step":1,"name":"直接主管审批","roles":["direct_manager"]},{"step":2,"name":"人事审批","roles":["hr_manager"],"condition":"days>=3"}]', 1);

-- 插入系统配置
INSERT INTO `oa_system_configs` (`config_key`, `config_value`, `config_type`, `category`, `description`, `is_system`) VALUES
('purchase_approval_amount_limit', '10000', 'number', 'purchase', '采购审批金额限制', true),
('reimbursement_approval_amount_limit', '5000', 'number', 'reimbursement', '报销审批金额限制', true),
('task_overdue_notification_days', '1', 'number', 'task', '任务超期提醒天数', true),
('approval_deadline_days', '3', 'number', 'approval', '审批默认期限天数', true),
('finance_categories', '["饲料采购","人工成本","设备维护","运输费用","其他费用"]', 'json', 'finance', '财务分类配置', false);

-- ==============================
-- 导出日志表
-- ==============================

-- 导出日志表
CREATE TABLE IF NOT EXISTS `oa_export_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '导出用户ID',
  `report_type` varchar(50) NOT NULL COMMENT '报表类型',
  `export_format` varchar(20) NOT NULL COMMENT '导出格式',
  `start_date` date COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `applicant_name` varchar(100) COMMENT '申请人姓名',
  `applicant_department` varchar(100) COMMENT '申请人部门',
  `applicant_employee_id` varchar(20) COMMENT '申请人工号',
  `export_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导出时间',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '导出状态：pending,completed,failed',
  `file_path` varchar(500) COMMENT '文件路径',
  `file_size` bigint(20) COMMENT '文件大小（字节）',
  `download_count` int NOT NULL DEFAULT 0 COMMENT '下载次数',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_report_type` (`report_type`),
  INDEX `idx_export_time` (`export_time`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导出日志表';

-- ==============================
-- 采购管理相关表
-- ==============================

-- 采购申请表
CREATE TABLE IF NOT EXISTS `oa_purchase_requests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_number` varchar(50) NOT NULL UNIQUE COMMENT '申请单号',
  `title` varchar(200) NOT NULL COMMENT '采购标题',
  `description` text COMMENT '采购说明',
  `category` varchar(50) NOT NULL COMMENT '采购类别：feed,equipment,medicine,facility,office,maintenance,other',
  `urgency_level` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '紧急程度：low,normal,high,urgent',
  `expected_date` date NOT NULL COMMENT '预期到货日期',
  `business_purpose` text NOT NULL COMMENT '业务目的',
  `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `status` varchar(50) NOT NULL DEFAULT 'draft' COMMENT '状态：draft,submitted,approved,rejected,cancelled',
  `applicant_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(100) NOT NULL COMMENT '申请人姓名',
  `applicant_employee_id` varchar(20) COMMENT '申请人工号',
  `department` varchar(100) COMMENT '申请部门',
  `position` varchar(100) COMMENT '申请人职位',
  `phone` varchar(20) COMMENT '联系电话',
  `manager_id` bigint(20) COMMENT '直属上级ID',
  `approver_id` bigint(20) COMMENT '当前审批人ID',
  `approved_at` timestamp NULL COMMENT '审批时间',
  `approval_comment` text COMMENT '审批意见',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_number` (`request_number`),
  INDEX `idx_applicant_id` (`applicant_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_category` (`category`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_expected_date` (`expected_date`),
  INDEX `idx_total_amount` (`total_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购申请表';

-- 采购物品清单表
CREATE TABLE IF NOT EXISTS `oa_purchase_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` bigint(20) NOT NULL COMMENT '采购申请ID',
  `item_name` varchar(200) NOT NULL COMMENT '物品名称',
  `specification` text COMMENT '规格说明',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `unit_price` decimal(15,2) NOT NULL COMMENT '单价',
  `total_price` decimal(15,2) NOT NULL COMMENT '总价',
  `category` varchar(50) COMMENT '物品类别',
  `brand` varchar(100) COMMENT '品牌',
  `supplier_suggestion` varchar(200) COMMENT '建议供应商',
  `remarks` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_request_id` (`request_id`),
  INDEX `idx_item_name` (`item_name`),
  FOREIGN KEY (`request_id`) REFERENCES `oa_purchase_requests`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购物品清单表';

-- 采购审批流程表
CREATE TABLE IF NOT EXISTS `oa_purchase_approvals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` bigint(20) NOT NULL COMMENT '采购申请ID',
  `approver_id` bigint(20) NOT NULL COMMENT '审批人ID',
  `approver_name` varchar(100) NOT NULL COMMENT '审批人姓名',
  `step` int NOT NULL COMMENT '审批步骤',
  `action` varchar(20) NOT NULL COMMENT '审批动作：submit,approve,reject,cancel',
  `action_text` varchar(50) NOT NULL COMMENT '动作描述',
  `comment` text COMMENT '审批意见',
  `status` varchar(20) NOT NULL COMMENT '状态：pending,approved,rejected',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_request_id` (`request_id`),
  INDEX `idx_approver_id` (`approver_id`),
  INDEX `idx_status` (`status`),
  FOREIGN KEY (`request_id`) REFERENCES `oa_purchase_requests`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购审批流程表';

-- 采购附件表
CREATE TABLE IF NOT EXISTS `oa_purchase_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` bigint(20) NOT NULL COMMENT '采购申请ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `uploader_id` bigint(20) NOT NULL COMMENT '上传人ID',
  `uploader_name` varchar(100) NOT NULL COMMENT '上传人姓名',
  `upload_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  PRIMARY KEY (`id`),
  INDEX `idx_request_id` (`request_id`),
  INDEX `idx_uploader_id` (`uploader_id`),
  FOREIGN KEY (`request_id`) REFERENCES `oa_purchase_requests`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购附件表';

-- 采购评论表
CREATE TABLE IF NOT EXISTS `oa_purchase_comments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` bigint(20) NOT NULL COMMENT '采购申请ID',
  `author_id` bigint(20) NOT NULL COMMENT '评论人ID',
  `author_name` varchar(100) NOT NULL COMMENT '评论人姓名',
  `content` text NOT NULL COMMENT '评论内容',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_request_id` (`request_id`),
  INDEX `idx_author_id` (`author_id`),
  INDEX `idx_created_at` (`created_at`),
  FOREIGN KEY (`request_id`) REFERENCES `oa_purchase_requests`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购评论表';

-- 插入示例数据
INSERT IGNORE INTO `oa_purchase_requests` (`id`, `request_number`, `title`, `description`, `category`, `urgency_level`, `expected_date`, `business_purpose`, `total_amount`, `status`, `applicant_id`, `applicant_name`, `applicant_employee_id`, `department`, `position`, `phone`) VALUES
(1, 'PR2024010001', '采购优质鹅饲料', '为春季养殖准备高质量饲料', 'feed', 'normal', '2024-02-15', '确保春季鹅群营养需求，提高生产效率', 5000.00, 'submitted', 1, '张三', 'EMP001', '生产部', '饲养员', '***********'),
(2, 'PR2024010002', '采购自动喂食设备', '提升饲养效率的自动化设备', 'equipment', 'high', '2024-02-20', '实现自动化喂食，减少人工成本', 25000.00, 'pending', 2, '李四', 'EMP002', '设备部', '设备管理员', '***********'),
(3, 'PR2024010003', '采购疫苗用品', '预防疾病的必要疫苗和药品', 'medicine', 'urgent', '2024-02-10', '防疫工作必需品，确保鹅群健康', 3500.00, 'approved', 3, '王五', 'EMP003', '兽医部', '兽医师', '***********');

INSERT IGNORE INTO `oa_purchase_items` (`request_id`, `item_name`, `specification`, `quantity`, `unit`, `unit_price`, `total_price`, `brand`, `supplier_suggestion`, `remarks`) VALUES
(1, '优质鹅饲料', '蛋白质含量18%，颗粒状', 100, '包', 50.00, 5000.00, '康达饲料', '康达饲料有限公司', '需要检查生产日期'),
(2, '自动喂食器', '容量500L，不锈钢材质', 5, '台', 5000.00, 25000.00, '智农设备', '智农机械制造厂', '需要安装调试'),
(3, '禽流感疫苗', '10ml装，冷藏保存', 50, '支', 35.00, 1750.00, '生物制药', '生物制药研究所', '注意冷链运输'),
(3, '消毒剂', '500ml装，广谱杀菌', 35, '瓶', 50.00, 1750.00, '消毒专家', '消毒用品公司', '避免阳光直射');

INSERT IGNORE INTO `oa_purchase_approvals` (`request_id`, `approver_id`, `approver_name`, `step`, `action`, `action_text`, `comment`, `status`) VALUES
(1, 4, '赵六', 1, 'submit', '提交申请', '申请采购春季饲料', 'pending'),
(2, 4, '赵六', 1, 'submit', '提交申请', '申请采购自动化设备', 'pending'),
(3, 4, '赵六', 1, 'submit', '提交申请', '申请采购疫苗用品', 'approved'),
(3, 5, '孙七', 2, 'approve', '审批通过', '同意采购，注意疫苗储存条件', 'approved');

-- ==============================
-- 流程模板管理相关表
-- ==============================

-- 创建流程模板表
CREATE TABLE IF NOT EXISTS `oa_workflow_templates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `category` varchar(50) NOT NULL COMMENT '业务类别：reimbursement,leave,purchase,contract,other',
  `description` text COMMENT '模板描述',
  `version` int(11) DEFAULT 1 COMMENT '版本号',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `steps_config` json COMMENT '步骤配置（JSON格式）',
  `conditions_config` json COMMENT '条件配置（JSON格式）',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) NOT NULL COMMENT '创建人姓名',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `last_used_at` datetime COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_category` (`name`, `category`),
  INDEX `idx_category_active` (`category`, `is_active`),
  INDEX `idx_creator` (`creator_id`),
  INDEX `idx_usage` (`usage_count` DESC),
  FOREIGN KEY (`creator_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程模板表';

-- 创建流程模板步骤表
CREATE TABLE IF NOT EXISTS `oa_workflow_template_steps` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `step_number` int(11) NOT NULL COMMENT '步骤序号',
  `step_name` varchar(100) NOT NULL COMMENT '步骤名称',
  `step_type` enum('approval','condition','notification') DEFAULT 'approval' COMMENT '步骤类型',
  `approver_type` enum('user','role','department') DEFAULT 'user' COMMENT '审批人类型',
  `approver_config` json COMMENT '审批人配置（JSON格式）',
  `condition_config` json COMMENT '条件配置（JSON格式）',
  `is_required` tinyint(1) DEFAULT 1 COMMENT '是否必需',
  `timeout_hours` int(11) DEFAULT 72 COMMENT '超时时间（小时）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_step` (`template_id`, `step_number`),
  INDEX `idx_template_step` (`template_id`, `step_number`),
  FOREIGN KEY (`template_id`) REFERENCES `oa_workflow_templates`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程模板步骤表';

-- 插入默认流程模板数据
INSERT IGNORE INTO `oa_workflow_templates` (`id`, `name`, `category`, `description`, `version`, `is_active`, `steps_config`, `creator_id`, `creator_name`) VALUES
(1, '标准报销审批流程', 'reimbursement', '适用于一般报销申请的标准审批流程', 1, 1, '[{"stepNumber":1,"stepName":"部门主管审批","approverType":"role","approverConfig":{"role":"department_manager"}},{"stepNumber":2,"stepName":"财务审批","approverType":"role","approverConfig":{"role":"finance_manager"}}]', 1, '系统管理员'),
(2, '大额报销审批流程', 'reimbursement', '适用于大额报销申请的多级审批流程', 1, 1, '[{"stepNumber":1,"stepName":"部门主管审批","approverType":"role","approverConfig":{"role":"department_manager"}},{"stepNumber":2,"stepName":"财务经理审批","approverType":"role","approverConfig":{"role":"finance_manager"}},{"stepNumber":3,"stepName":"总经理审批","approverType":"role","approverConfig":{"role":"general_manager"}}]', 1, '系统管理员'),
(3, '请假审批流程', 'leave', '员工请假申请的审批流程', 1, 1, '[{"stepNumber":1,"stepName":"直属主管审批","approverType":"role","approverConfig":{"role":"direct_manager"}},{"stepNumber":2,"stepName":"人事审批","approverType":"role","approverConfig":{"role":"hr_manager"}}]', 1, '系统管理员');

-- ==============================
-- 权限管理系统相关表
-- ==============================

-- 权限表
CREATE TABLE IF NOT EXISTS `oa_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `code` varchar(100) NOT NULL COMMENT '权限代码',
  `description` text COMMENT '权限描述',
  `module` varchar(50) NOT NULL COMMENT '所属模块：system,reimbursement,approval,workflow,report',
  `resource` varchar(100) COMMENT '资源标识',
  `action` varchar(50) COMMENT '操作类型：create,read,update,delete,approve,manage',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`code`),
  INDEX `idx_module` (`module`),
  INDEX `idx_active_sort` (`is_active`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 角色表
CREATE TABLE IF NOT EXISTS `oa_roles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '角色名称',
  `code` varchar(100) NOT NULL COMMENT '角色代码',
  `description` text COMMENT '角色描述',
  `level` int(11) DEFAULT 1 COMMENT '角色级别',
  `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统角色',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `data_scope` enum('all','department','self') DEFAULT 'self' COMMENT '数据权限范围',
  `creator_id` bigint(20) COMMENT '创建人ID',
  `creator_name` varchar(50) COMMENT '创建人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`code`),
  INDEX `idx_active_level` (`is_active`, `level`),
  FOREIGN KEY (`creator_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `oa_role_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  INDEX `idx_role_id` (`role_id`),
  INDEX `idx_permission_id` (`permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `oa_roles`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `oa_permissions`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `oa_user_roles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `assigned_by` bigint(20) COMMENT '分配人ID',
  `assigned_by_name` varchar(50) COMMENT '分配人姓名',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `expires_at` datetime COMMENT '过期时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_role_id` (`role_id`),
  INDEX `idx_active_expires` (`is_active`, `expires_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`role_id`) REFERENCES `oa_roles`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assigned_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 部门表（如果不存在）
CREATE TABLE IF NOT EXISTS `oa_departments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `code` varchar(50) COMMENT '部门代码',
  `parent_id` bigint(20) COMMENT '上级部门ID',
  `level` int(11) DEFAULT 1 COMMENT '部门层级',
  `path` varchar(500) COMMENT '部门路径',
  `manager_id` bigint(20) COMMENT '部门负责人ID',
  `manager_name` varchar(50) COMMENT '部门负责人姓名',
  `description` text COMMENT '部门描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_department_code` (`code`),
  INDEX `idx_parent_id` (`parent_id`),
  INDEX `idx_manager_id` (`manager_id`),
  INDEX `idx_active_sort` (`is_active`, `sort_order`),
  FOREIGN KEY (`parent_id`) REFERENCES `oa_departments`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`manager_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 插入默认权限数据
INSERT IGNORE INTO `oa_permissions` (`id`, `name`, `code`, `description`, `module`, `resource`, `action`, `sort_order`) VALUES
-- 系统管理权限
(1, '用户管理', 'system:user:manage', '管理系统用户', 'system', 'user', 'manage', 1),
(2, '角色管理', 'system:role:manage', '管理系统角色', 'system', 'role', 'manage', 2),
(3, '权限管理', 'system:permission:manage', '管理系统权限', 'system', 'permission', 'manage', 3),
(4, '部门管理', 'system:department:manage', '管理组织部门', 'system', 'department', 'manage', 4),
(5, '系统设置', 'system:setting:manage', '管理系统设置', 'system', 'setting', 'manage', 5),

-- 报销管理权限
(10, '报销申请', 'reimbursement:create', '创建报销申请', 'reimbursement', 'reimbursement', 'create', 10),
(11, '报销查看', 'reimbursement:read', '查看报销信息', 'reimbursement', 'reimbursement', 'read', 11),
(12, '报销编辑', 'reimbursement:update', '编辑报销申请', 'reimbursement', 'reimbursement', 'update', 12),
(13, '报销删除', 'reimbursement:delete', '删除报销申请', 'reimbursement', 'reimbursement', 'delete', 13),
(14, '报销管理', 'reimbursement:manage', '管理所有报销', 'reimbursement', 'reimbursement', 'manage', 14),

-- 审批权限
(20, '审批处理', 'approval:process', '处理审批事项', 'approval', 'approval', 'approve', 20),
(21, '审批查看', 'approval:read', '查看审批信息', 'approval', 'approval', 'read', 21),
(22, '审批管理', 'approval:manage', '管理审批流程', 'approval', 'approval', 'manage', 22),

-- 流程管理权限
(30, '流程设计', 'workflow:design', '设计审批流程', 'workflow', 'workflow', 'create', 30),
(31, '流程管理', 'workflow:manage', '管理流程模板', 'workflow', 'workflow', 'manage', 31),

-- 报表权限
(40, '报表查看', 'report:read', '查看统计报表', 'report', 'report', 'read', 40),
(41, '报表导出', 'report:export', '导出报表数据', 'report', 'report', 'export', 41),
(42, '报表管理', 'report:manage', '管理报表配置', 'report', 'report', 'manage', 42);

-- 插入默认角色数据
INSERT IGNORE INTO `oa_roles` (`id`, `name`, `code`, `description`, `level`, `is_system`, `data_scope`, `creator_id`, `creator_name`) VALUES
(1, '系统管理员', 'admin', '系统最高管理员，拥有所有权限', 1, 1, 'all', 1, '系统'),
(2, '部门经理', 'department_manager', '部门管理人员，负责部门内事务审批', 2, 1, 'department', 1, '系统'),
(3, '财务经理', 'finance_manager', '财务管理人员，负责财务相关审批', 2, 1, 'all', 1, '系统'),
(4, '人事经理', 'hr_manager', '人事管理人员，负责人事相关审批', 2, 1, 'all', 1, '系统'),
(5, '普通员工', 'employee', '普通员工，基础操作权限', 3, 1, 'self', 1, '系统'),
(6, '财务专员', 'finance_specialist', '财务专员，处理财务相关事务', 3, 1, 'department', 1, '系统');

-- 插入默认部门数据
INSERT IGNORE INTO `oa_departments` (`id`, `name`, `code`, `parent_id`, `level`, `path`, `manager_id`, `manager_name`, `sort_order`) VALUES
(1, '总经理办公室', 'CEO_OFFICE', NULL, 1, '/1', 1, '系统管理员', 1),
(2, '人力资源部', 'HR_DEPT', 1, 2, '/1/2', 2, '张三', 2),
(3, '财务部', 'FINANCE_DEPT', 1, 2, '/1/3', 3, '李四', 3),
(4, '技术部', 'TECH_DEPT', 1, 2, '/1/4', 4, '王五', 4),
(5, '市场部', 'MARKET_DEPT', 1, 2, '/1/5', 5, '赵六', 5),
(6, '行政部', 'ADMIN_DEPT', 1, 2, '/1/6', 6, '孙七', 6);

-- 插入角色权限关联数据
INSERT IGNORE INTO `oa_role_permissions` (`role_id`, `permission_id`) VALUES
-- 系统管理员拥有所有权限
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5),
(1, 10), (1, 11), (1, 12), (1, 13), (1, 14),
(1, 20), (1, 21), (1, 22),
(1, 30), (1, 31),
(1, 40), (1, 41), (1, 42),

-- 部门经理权限
(2, 11), (2, 20), (2, 21), (2, 40),

-- 财务经理权限
(3, 11), (3, 14), (3, 20), (3, 21), (3, 40), (3, 41),

-- 人事经理权限
(4, 1), (4, 4), (4, 11), (4, 20), (4, 21), (4, 40),

-- 普通员工权限
(5, 10), (5, 11), (5, 12),

-- 财务专员权限
(6, 11), (6, 20), (6, 21), (6, 40);

SET FOREIGN_KEY_CHECKS = 1;