#!/usr/bin/env node

/**
 * 组件命名规范化脚本
 * 批量更新小程序页面中的组件引用，使用c-前缀标准命名
 * 符合智慧养鹅SAAS平台开发规范
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 需要规范化的页面和组件
const COMPONENT_MAPPING = {
  'form-modal': 'c-form-modal',
  'tab-bar': 'c-tab-bar', 
  'section-header': 'c-section-header',
  'list-item': 'c-list-item',
  'card': 'c-card',
  'empty-state': 'c-empty-state',
  'loading': 'c-loading',
  'environment-data-item': 'c-environment-data-item',
  'trend-chart': 'c-trend-chart',
  'weather': 'c-weather',
  'weather-compact': 'c-weather-compact',
  'goose-price': 'c-goose-price',
  'modal': 'c-modal',
  'icon': 'c-icon',
  'chart': 'c-chart',
  'health-trend-chart': 'c-health-trend-chart',
  'form-builder': 'c-form-builder',
  'lazy-list': 'c-lazy-list'
};

class ComponentNamingNormalizer {
  constructor() {
    this.processedFiles = 0;
    this.updatedFiles = 0;
    this.errors = [];
  }

  /**
   * 规范化JSON配置文件中的组件命名
   */
  normalizeJsonFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const config = JSON.parse(content);
      
      if (!config.usingComponents) {
        return false;
      }

      let hasChanges = false;
      const newComponents = {};
      
      // 处理每个组件引用
      for (const [oldName, componentPath] of Object.entries(config.usingComponents)) {
        if (COMPONENT_MAPPING[oldName]) {
          // 需要更新命名
          newComponents[COMPONENT_MAPPING[oldName]] = componentPath;
          hasChanges = true;
          console.log(`  ${oldName} -> ${COMPONENT_MAPPING[oldName]}`);
        } else if (oldName.startsWith('c-')) {
          // 已经符合规范
          newComponents[oldName] = componentPath;
        } else {
          // 未在映射表中，但不是c-前缀，给出警告
          newComponents[oldName] = componentPath;
          console.warn(`  警告: ${oldName} 不在标准映射表中`);
        }
      }
      
      if (hasChanges) {
        config.usingComponents = newComponents;
        fs.writeFileSync(filePath, JSON.stringify(config, null, 2));
        return true;
      }
      
      return false;
    } catch (error) {
      this.errors.push(`处理JSON文件失败 ${filePath}: ${error.message}`);
      return false;
    }
  }

  /**
   * 规范化WXML文件中的组件标签
   */
  normalizeWxmlFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;
      
      // 替换开始标签和结束标签
      for (const [oldName, newName] of Object.entries(COMPONENT_MAPPING)) {
        const openTagRegex = new RegExp(`<${oldName}(\\s|>)`, 'g');
        const closeTagRegex = new RegExp(`</${oldName}>`, 'g');
        
        if (openTagRegex.test(content) || closeTagRegex.test(content)) {
          content = content.replace(openTagRegex, `<${newName}$1`);
          content = content.replace(closeTagRegex, `</${newName}>`);
          hasChanges = true;
          console.log(`  <${oldName}> -> <${newName}>`);
        }
      }
      
      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        return true;
      }
      
      return false;
    } catch (error) {
      this.errors.push(`处理WXML文件失败 ${filePath}: ${error.message}`);
      return false;
    }
  }

  /**
   * 处理单个页面或组件目录
   */
  processDirectory(dirPath) {
    const jsonFile = path.join(dirPath, path.basename(dirPath) + '.json');
    const wxmlFile = path.join(dirPath, path.basename(dirPath) + '.wxml');
    
    let hasUpdates = false;
    
    console.log(`\n📁 处理目录: ${dirPath}`);
    
    // 处理JSON配置文件
    if (fs.existsSync(jsonFile)) {
      console.log(`  🔧 处理JSON: ${path.basename(jsonFile)}`);
      if (this.normalizeJsonFile(jsonFile)) {
        hasUpdates = true;
      }
    }
    
    // 处理WXML文件
    if (fs.existsSync(wxmlFile)) {
      console.log(`  🔧 处理WXML: ${path.basename(wxmlFile)}`);
      if (this.normalizeWxmlFile(wxmlFile)) {
        hasUpdates = true;
      }
    }
    
    this.processedFiles++;
    if (hasUpdates) {
      this.updatedFiles++;
    }
  }

  /**
   * 批量处理所有页面和组件
   */
  async run() {
    console.log('🚀 开始组件命名规范化...\n');
    
    try {
      // 处理页面目录
      const pagePattern = path.join(process.cwd(), 'pages/**/*.json');
      const pageFiles = glob.sync(pagePattern);
      
      console.log('📱 处理页面文件...');
      for (const jsonFile of pageFiles) {
        const dirPath = path.dirname(jsonFile);
        this.processDirectory(dirPath);
      }
      
      // 处理组件目录
      const componentPattern = path.join(process.cwd(), 'components/**/*.json');
      const componentFiles = glob.sync(componentPattern);
      
      console.log('\n🧩 处理组件文件...');
      for (const jsonFile of componentFiles) {
        const dirPath = path.dirname(jsonFile);
        this.processDirectory(dirPath);
      }
      
      // 输出处理结果
      console.log('\n✅ 组件命名规范化完成!');
      console.log(`📊 处理统计:`);
      console.log(`  - 总处理文件数: ${this.processedFiles}`);
      console.log(`  - 实际更新文件数: ${this.updatedFiles}`);
      
      if (this.errors.length > 0) {
        console.log(`\n❌ 处理错误 (${this.errors.length}个):`);
        this.errors.forEach(error => console.log(`  - ${error}`));
      }
      
      if (this.updatedFiles > 0) {
        console.log('\n🎉 所有组件命名已规范化为c-前缀标准格式!');
        console.log('📋 建议运行测试确保功能正常...');
      } else {
        console.log('\n✨ 所有文件的组件命名已符合规范!');
      }
      
    } catch (error) {
      console.error('❌ 执行过程中发生错误:', error.message);
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const normalizer = new ComponentNamingNormalizer();
  normalizer.run().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = ComponentNamingNormalizer;