// pages/address/edit/edit.js - 地址编辑页面
Page({
  data: {
    isEdit: false,
    addressId: '',
    formData: {
      name: '',
      phone: '',
      region: [],
      detail: '',
      tag: '',
      customTag: '',
      isDefault: false
    },
    isFormValid: false
  },

  onLoad(options) {
    if (options.id) {
      // 编辑模式
      this.setData({
        isEdit: true,
        addressId: options.id
      });
      this.loadAddressData(options.id);
      wx.setNavigationBarTitle({
        title: '编辑地址'
      });
    } else {
      // 新增模式
      wx.setNavigationBarTitle({
        title: '添加地址'
      });
      // 初始化空的region数组
      this.setData({
        'formData.region': []
      });
    }
  },

  // 加载地址数据（编辑模式）
  loadAddressData(addressId) {
    // 这里应该从API或存储中获取地址数据
    // 暂时使用模拟数据
    const mockAddress = {
      name: '张三',
      phone: '13800138000',
      region: ['北京市', '朝阳区', '建国路街道'],
      detail: 'SOHO现代城A座1001室',
      tag: '家',
      customTag: '',
      isDefault: true
    };

    this.setData({
      formData: mockAddress
    });
    this.validateForm();
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    this.validateForm();
  },

  // 省市区选择
  onRegionChange(e) {
  try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('Region selected', e.detail.value); } catch(_) {}
    const region = e.detail.value;
    this.setData({
      'formData.region': region
    });
    this.validateForm();
  },

  // 选择标签
  onSelectTag(e) {
    const { tag } = e.currentTarget.dataset;
    const newTag = this.data.formData.tag === tag ? '' : tag;
    this.setData({
      'formData.tag': newTag,
      'formData.customTag': '' // 清空自定义标签
    });
  },

  // 开关变化
  onSwitchChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 表单验证
  validateForm() {
    const { name, phone, region, detail } = this.data.formData;
    
    const isValid = name && name.trim() !== '' && 
                   phone && phone.trim() !== '' && 
                   this.validatePhone(phone) &&
                   region && Array.isArray(region) && region.length === 3 && 
                   detail && detail.trim() !== '';
    
  try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('Form validation', { name, phone, region, detail, isValid }); } catch(_) {}
    
    this.setData({
      isFormValid: isValid
    });
  },

  // 手机号验证
  validatePhone(phone) {
    const phoneReg = /^1[3-9]\d{9}$/;
    return phoneReg.test(phone);
  },

  // 取消
  onCancel() {
    wx.showModal({
      title: '确认退出',
      content: '退出后当前编辑的内容将不会保存',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 保存
  onSave() {
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请完善地址信息',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证手机号
    if (!this.validatePhone(this.data.formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showLoading({
      title: this.data.isEdit ? '保存中...' : '添加中...'
    });

    // 模拟API请求
    setTimeout(() => {
      wx.hideLoading();
      
      // 处理标签
      let finalTag = this.data.formData.tag;
      if (this.data.formData.customTag.trim() !== '') {
        finalTag = this.data.formData.customTag.trim();
      }

      // 构造保存的数据
      const saveData = {
        ...this.data.formData,
        tag: finalTag,
        province: this.data.formData.region[0],
        city: this.data.formData.region[1],
        district: this.data.formData.region[2]
      };

      // 这里应该调用API保存数据
    try { const logger = require('../../../utils/logger.js'); logger.debug && logger.debug('保存地址数据', saveData); } catch(_) {}

      wx.showToast({
        title: this.data.isEdit ? '保存成功' : '添加成功',
        icon: 'success',
        duration: 1500
      });

      // 震动反馈
      wx.vibrateShort({
        type: 'light'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '地址编辑',
      path: '/pages/address/edit/edit',
      imageUrl: '/images/share/address-edit.png'
    };
  }
});