<!-- pages/production/reimbursement/add/add.wxml -->
<view class="add-reimbursement-container">
  <form bindsubmit="onSubmit">
    <!-- 报销标题 -->
    <view class="form-item">
      <text class="form-label">报销标题 *</text>
      <input class="form-input" name="title" placeholder="请输入报销标题" value="{{formData.title}}" bindinput="onTitleInput" />
    </view>

    <!-- 报销类型 -->
    <view class="form-item">
      <text class="form-label">报销类型 *</text>
      <picker class="form-picker" mode="selector" range="{{categoryOptions}}" value="{{formData.categoryIndex}}" bindchange="onCategoryChange">
        <view class="picker-content">
          <text>{{categoryOptions[formData.categoryIndex]}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 报销金额 -->
    <view class="form-item">
      <text class="form-label">报销金额 *</text>
      <input class="form-input" name="amount" type="digit" placeholder="请输入报销金额" value="{{formData.amount}}" bindinput="onAmountInput" />
    </view>

    <!-- 报销说明 -->
    <view class="form-item">
      <text class="form-label">报销说明</text>
      <textarea class="form-textarea" name="description" placeholder="请输入报销说明" value="{{formData.description}}" bindinput="onDescriptionInput"></textarea>
    </view>

    <!-- 上传凭证 -->
    <view class="form-item">
      <text class="form-label">上传凭证</text>
      <view class="upload-section">
        <block wx:for="{{formData.images}}" wx:key="index">
          <view class="image-item">
            <image src="{{item}}" mode="aspectFill" bindtap="onPreviewImage" data-url="{{item}}"></image>
            <view class="delete-btn" bindtap="onDeleteImage" data-index="{{index}}">×</view>
          </view>
        </block>
        <view wx:if="{{formData.images.length < 3}}" class="upload-btn" bindtap="onChooseImage">
          <text class="upload-icon">+</text>
          <text class="upload-text">上传凭证</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" form-type="submit" disabled="{{!canSubmit}}">提交申请</button>
    </view>
  </form>
</view>
