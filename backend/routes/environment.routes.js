/**
 * 环境监控相关路由
 * Environment Monitoring Routes
 */

const express = require("express");
const router = express.Router();
const { verifyToken } = require("../middleware/auth.middleware");
const {
  identifyTenant,
  authenticateTenantUser,
  recordTenantUsage,
} = require("../middleware/tenant.middleware");

// 应用中间件
router.use(verifyToken);
router.use(identifyTenant);
router.use(authenticateTenantUser);
router.use(recordTenantUsage);

/**
 * @route   GET /production/environment
 * @desc    获取环境监控数据
 * @access  Private
 */
router.get("/", async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const userId = req.user.id;
    const { flockId, date, hours = 24 } = req.query;

    // 构建查询条件
    let whereClause = "WHERE userId = ?";
    const params = [userId];

    if (flockId) {
      whereClause += " AND flockId = ?";
      params.push(flockId);
    }

    if (date) {
      whereClause += " AND DATE(recordedDate) = ?";
      params.push(date);
    } else {
      // 默认获取最近24小时的数据
      whereClause += " AND recordedDate >= DATE_SUB(NOW(), INTERVAL ? HOUR)";
      params.push(parseInt(hours));
    }

    // 获取环境数据
    const [environmentData] = await tenantDb.query(
      `
      SELECT 
        id,
        flockId,
        recordedDate,
        temperature,
        humidity,
        pm25,
        lightHours,
        ventilation,
        notes,
        HOUR(recordedDate) as hour,
        DATE(recordedDate) as date
      FROM production_records 
      ${whereClause}
      AND (temperature IS NOT NULL OR humidity IS NOT NULL)
      ORDER BY recordedDate DESC
    `,
      { replacements: params },
    );

    // 计算当前环境状态
    const latestData = environmentData[0] || {};
    const currentEnvironment = {
      temperature: latestData.temperature || 0,
      humidity: latestData.humidity || 0,
      pm25: latestData.pm25 || 0,
      lightHours: latestData.lightHours || 0,
      updateTime: latestData.recordedDate || new Date(),
      status: calculateEnvironmentStatus(latestData),
    };

    // 计算24小时趋势数据
    const trendData = calculateTrendData(environmentData);

    // 获取环境预警
    const alerts = generateEnvironmentAlerts(currentEnvironment);

    // 获取鹅群环境统计
    const [flockStats] = await tenantDb.query(
      `
      SELECT 
        f.id,
        f.name,
        AVG(pr.temperature) as avgTemp,
        AVG(pr.humidity) as avgHumidity,
        COUNT(pr.id) as recordCount
      FROM flocks f
      LEFT JOIN production_records pr ON f.id = pr.flockId 
        AND pr.recordedDate >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        AND pr.userId = ?
      WHERE f.userId = ? AND f.status = 'active'
      GROUP BY f.id, f.name
    `,
      { replacements: [userId, userId] },
    );

    res.json({
      success: true,
      message: "获取环境监控数据成功",
      data: {
        currentEnvironment,
        trendData,
        alerts,
        flockEnvironment: flockStats || [],
        summary: {
          totalRecords: environmentData.length,
          avgTemperature: calculateAverage(environmentData, "temperature"),
          avgHumidity: calculateAverage(environmentData, "humidity"),
          timeRange: `${hours}小时`,
        },
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取环境监控数据失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取环境监控数据失败",
    });
  }
});

/**
 * @route   POST /production/environment
 * @desc    添加环境监控记录
 * @access  Private
 */
router.post("/", async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const userId = req.user.id;
    const {
      flockId,
      temperature,
      humidity,
      pm25,
      lightHours,
      ventilation,
      notes,
    } = req.body;

    // 验证必要参数
    if (!flockId) {
      return res.status(400).json({
        success: false,
        message: "缺少鹅群ID",
      });
    }

    // 验证鹅群是否属于当前用户
    const [flockCheck] = await tenantDb.query(
      "SELECT id FROM flocks WHERE id = ? AND userId = ?",
      { replacements: [flockId, userId] },
    );

    if (flockCheck.length === 0) {
      return res.status(403).json({
        success: false,
        message: "无权操作该鹅群",
      });
    }

    // 插入环境记录
    const [result] = await tenantDb.query(
      `
      INSERT INTO production_records 
      (userId, flockId, recordedDate, temperature, humidity, pm25, lightHours, ventilation, notes)
      VALUES (?, ?, NOW(), ?, ?, ?, ?, ?, ?)
    `,
      {
        replacements: [
          userId,
          flockId,
          temperature,
          humidity,
          pm25,
          lightHours,
          ventilation,
          notes,
        ],
      },
    );

    res.json({
      success: true,
      message: "环境记录添加成功",
      data: {
        id: result.insertId,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('添加环境记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "添加环境记录失败",
    });
  }
});

/**
 * @route   GET /production/environment/alerts
 * @desc    获取环境预警信息
 * @access  Private
 */
router.get("/alerts", async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const userId = req.user.id;

    // 获取最新环境数据
    const [latestData] = await tenantDb.query(
      `
      SELECT 
        pr.*,
        f.name as flockName
      FROM production_records pr
      JOIN flocks f ON pr.flockId = f.id
      WHERE pr.userId = ? 
        AND (pr.temperature IS NOT NULL OR pr.humidity IS NOT NULL)
        AND pr.recordedDate >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
      ORDER BY pr.recordedDate DESC
    `,
      { replacements: [userId] },
    );

    // 生成预警信息
    const alerts = [];

    latestData.forEach((record) => {
      const warnings = generateEnvironmentAlerts({
        temperature: record.temperature,
        humidity: record.humidity,
        pm25: record.pm25,
      });

      warnings.forEach((warning) => {
        alerts.push({
          ...warning,
          flockId: record.flockId,
          flockName: record.flockName,
          recordedDate: record.recordedDate,
        });
      });
    });

    res.json({
      success: true,
      message: "获取环境预警成功",
      data: {
        alerts,
        totalAlerts: alerts.length,
        summary: {
          critical: alerts.filter((a) => a.level === "critical").length,
          warning: alerts.filter((a) => a.level === "warning").length,
          info: alerts.filter((a) => a.level === "info").length,
        },
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取环境预警失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: "获取环境预警失败",
    });
  }
});

// 辅助函数

/**
 * 计算环境状态
 */
function calculateEnvironmentStatus(data) {
  const temp = data.temperature || 0;
  const humidity = data.humidity || 0;

  // 理想温度范围：18-25°C
  // 理想湿度范围：50-70%

  if (temp >= 18 && temp <= 25 && humidity >= 50 && humidity <= 70) {
    return "优";
  } else if (temp >= 15 && temp <= 30 && humidity >= 40 && humidity <= 80) {
    return "良";
  } else {
    return "一般";
  }
}

/**
 * 计算趋势数据
 */
function calculateTrendData(data) {
  const hourlyData = {};

  data.forEach((record) => {
    const hour = record.hour;
    if (!hourlyData[hour]) {
      hourlyData[hour] = {
        temperature: [],
        humidity: [],
        pm25: [],
      };
    }

    if (record.temperature)
      hourlyData[hour].temperature.push(record.temperature);
    if (record.humidity) hourlyData[hour].humidity.push(record.humidity);
    if (record.pm25) hourlyData[hour].pm25.push(record.pm25);
  });

  const trendData = {
    temperature: [],
    humidity: [],
    pm25: [],
    timeLabels: [],
  };

  for (let i = 23; i >= 0; i--) {
    const hour = (new Date().getHours() - i + 24) % 24;
    const hourData = hourlyData[hour] || {};

    trendData.timeLabels.push(`${hour}:00`);
    trendData.temperature.push(
      hourData.temperature?.length > 0
        ? hourData.temperature.reduce((a, b) => a + b) /
            hourData.temperature.length
        : null,
    );
    trendData.humidity.push(
      hourData.humidity?.length > 0
        ? hourData.humidity.reduce((a, b) => a + b) / hourData.humidity.length
        : null,
    );
    trendData.pm25.push(
      hourData.pm25?.length > 0
        ? hourData.pm25.reduce((a, b) => a + b) / hourData.pm25.length
        : null,
    );
  }

  return trendData;
}

/**
 * 生成环境预警
 */
function generateEnvironmentAlerts(environment) {
  const alerts = [];
  const { temperature, humidity, pm25 } = environment;

  // 温度预警
  if (temperature < 15) {
    alerts.push({
      type: "temperature",
      level: "critical",
      title: "温度过低预警",
      message: `当前温度${temperature}°C，低于适宜范围，请注意保温`,
      value: temperature,
      threshold: 15,
    });
  } else if (temperature > 30) {
    alerts.push({
      type: "temperature",
      level: "critical",
      title: "温度过高预警",
      message: `当前温度${temperature}°C，高于适宜范围，请注意降温`,
      value: temperature,
      threshold: 30,
    });
  } else if (temperature < 18 || temperature > 25) {
    alerts.push({
      type: "temperature",
      level: "warning",
      title: "温度偏离预警",
      message: `当前温度${temperature}°C，偏离最佳范围(18-25°C)`,
      value: temperature,
    });
  }

  // 湿度预警
  if (humidity < 40) {
    alerts.push({
      type: "humidity",
      level: "warning",
      title: "湿度过低",
      message: `当前湿度${humidity}%，建议增加湿度`,
      value: humidity,
    });
  } else if (humidity > 80) {
    alerts.push({
      type: "humidity",
      level: "warning",
      title: "湿度过高",
      message: `当前湿度${humidity}%，建议降低湿度`,
      value: humidity,
    });
  }

  // PM2.5预警
  if (pm25 > 75) {
    alerts.push({
      type: "air_quality",
      level: "critical",
      title: "空气质量差",
      message: `PM2.5浓度${pm25}μg/m³，请加强通风`,
      value: pm25,
    });
  } else if (pm25 > 35) {
    alerts.push({
      type: "air_quality",
      level: "warning",
      title: "空气质量一般",
      message: `PM2.5浓度${pm25}μg/m³，建议注意通风`,
      value: pm25,
    });
  }

  return alerts;
}

/**
 * 计算数组平均值
 */
function calculateAverage(data, field) {
  const values = data
    .filter((item) => item[field] != null)
    .map((item) => item[field]);
  return values.length > 0
    ? Math.round((values.reduce((a, b) => a + b) / values.length) * 10) / 10
    : 0;
}

module.exports = router;
