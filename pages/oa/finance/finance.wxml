<!-- pages/oa/finance/finance.wxml -->
<view class="oa-container">
  
  <!-- 智能提示区块 -->
  <view class="oa-smart-tip">
    <text class="oa-tip-icon">💰</text>
    <view class="oa-tip-content">
      <text class="oa-tip-title">智能财务助手</text>
      <text class="oa-tip-desc">为您提供实时财务数据分析与预警提醒</text>
    </view>
  </view>



  <!-- 财务统计概览 -->
  <view class="oa-section">
    <view class="oa-section-title">财务概览</view>
    <view class="time-range-selector">
      <picker bindchange="onTimeRangeChange" range="{{timeRanges}}" range-key="label">
        <view class="oa-picker">
          <text>{{selectedTimeRange}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>
    <view class="statistics-grid">
      <view class="stat-item" bindtap="onStatTap" data-type="revenue">
        <view class="stat-content">
          <text class="stat-number">￥{{financialStats.totalRevenue}}</text>
          <text class="stat-label">总收入</text>
          <text class="stat-trend positive">+{{financialStats.revenueGrowth}}%</text>
        </view>
      </view>
      
      <view class="stat-item" bindtap="onStatTap" data-type="expense">
        <view class="stat-content">
          <text class="stat-number">￥{{financialStats.totalExpense}}</text>
          <text class="stat-label">总支出</text>
          <text class="stat-trend negative">-{{financialStats.expenseGrowth}}%</text>
        </view>
      </view>
      
      <view class="stat-item" bindtap="onStatTap" data-type="profit">
        <view class="stat-content">
          <text class="stat-number">￥{{financialStats.netProfit}}</text>
          <text class="stat-label">净利润</text>
          <text class="stat-trend positive">+{{financialStats.profitGrowth}}%</text>
        </view>
      </view>
      
      <view class="stat-item" bindtap="onStatTap" data-type="balance">
        <view class="stat-content">
          <text class="stat-number">￥{{financialStats.accountBalance}}</text>
          <text class="stat-label">账户余额</text>
          <text class="stat-trend stable">稳定</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 最近财务活动 -->
  <view class="oa-section">
    <view class="section-header">
      <text class="oa-section-title">最近活动</text>
      <text class="view-all-btn" bindtap="onViewAllActivities">查看全部</text>
    </view>
    
    <view class="oa-list" wx:if="{{recentActivities.length > 0}}">
      <block wx:for="{{recentActivities}}" wx:key="id">
        <view class="oa-list-item" bindtap="onActivityTap" data-id="{{item.id}}">
          <view class="activity-item">
            <view class="activity-icon {{item.type}}">
              <text>{{item.icon}}</text>
            </view>
            <view class="activity-content">
              <text class="activity-title">{{item.title}}</text>
              <text class="activity-desc">{{item.description}}</text>
              <text class="activity-time">{{item.time}}</text>
            </view>
            <view class="activity-amount {{item.type}}">{{item.amount}}</view>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无财务活动</text>
      <button class="oa-action-btn primary" bindtap="onStatTap" data-type="overview">
        查看详情
      </button>
    </view>
  </view>

  <!-- 重要提醒 -->
  <view class="oa-section" wx:if="{{alerts.length > 0}}">
    <view class="oa-section-title">重要提醒</view>
    <view class="alerts-list">
      <block wx:for="{{alerts}}" wx:key="id">
        <view class="oa-permission-notice {{item.level}}">
          <text class="oa-notice-icon">{{item.icon}}</text>
          <view class="alert-content">
            <text class="alert-text">{{item.message}}</text>
            <text class="alert-time">{{item.time}}</text>
          </view>
          <button class="alert-action" bindtap="onAlertAction" data-id="{{item.id}}">
            处理
          </button>
        </view>
      </block>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="oa-loading-overlay" wx:if="{{loading}}">
    <view class="oa-loading-content">
      <view class="oa-loading-spinner"></view>
      <text class="oa-loading-text">加载中...</text>
    </view>
  </view>

  <!-- 统计详情模态框 -->
  <view class="stat-modal-overlay" wx:if="{{showStatModal}}" bindtap="closeStatModal">
    <view class="stat-modal-content" catchtap="">
      <view class="stat-modal-header">
        <text class="stat-modal-title">{{statModalData.title}}</text>
        <view class="stat-modal-close" bindtap="closeStatModal">×</view>
      </view>
      
      <view class="stat-modal-main">
        <view class="stat-modal-total">
          <text class="stat-modal-value">{{statModalData.value}}</text>
          <text class="stat-modal-trend">{{statModalData.trend}}</text>
        </view>
        
        <view class="stat-modal-breakdown">
          <view class="breakdown-title">详细构成</view>
          <view class="breakdown-list">
            <block wx:for="{{statModalData.items}}" wx:key="label">
              <view class="breakdown-item">
                <view class="breakdown-info">
                  <text class="breakdown-label">{{item.label}}</text>
                  <text class="breakdown-percent">{{item.percent}}</text>
                </view>
                <text class="breakdown-amount">{{item.amount}}</text>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
  </view>

</view>