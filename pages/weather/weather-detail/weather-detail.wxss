/* pages/weather/weather-detail/weather-detail.wxss */
.weather-detail-container {
  padding: 20rpx;
  background-color: #F5F6F8;
  min-height: 100vh;
}

.current-weather {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.location {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.location-text {
  font-size: 28rpx;
  opacity: 0.9;
}

.weather-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.temperature {
  font-size: 120rpx;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 10rpx;
}

.weather-desc {
  font-size: 32rpx;
  opacity: 0.9;
}

.weather-icon {
  width: 120rpx;
  height: 120rpx;
}

.weather-icon image {
  width: 100%;
  height: 100%;
}

.weather-details {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.detail-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.forecast {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.forecast-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.forecast-list {
  display: flex;
  flex-direction: column;
}

.forecast-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.forecast-item:last-child {
  border-bottom: none;
}

.forecast-date {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.forecast-icon {
  width: 60rpx;
  height: 60rpx;
  margin: 0 20rpx;
}

.forecast-temp {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  min-width: 120rpx;
}

.update-info {
  text-align: center;
  padding: 20rpx;
}

.update-info text {
  font-size: 24rpx;
  color: #999;
}
