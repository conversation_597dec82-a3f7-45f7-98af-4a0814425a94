// pages/help/article/article.js
Page({
  data: {
    articleInfo: {
      id: 201,
      title: '如何添加健康记录',
      categoryName: '健康管理',
      difficulty: 'easy',
      difficultyText: '简单',
      views: 580,
      likes: 45,
      helpfulCount: 42,
      unhelpfulCount: 3,
      updateTime: '2024-11-28',
      tags: ['健康记录', '数据录入', '基础操作'],
      toc: [
        { id: 'section1', title: '准备工作' },
        { id: 'section2', title: '录入基础信息' },
        { id: 'section3', title: '添加详细数据' },
        { id: 'section4', title: '保存和管理' }
      ],
      content: [
        {
          id: 'section1',
          type: 'title',
          text: '1. 准备工作'
        },
        {
          id: 'intro',
          type: 'paragraph',
          text: '在开始添加健康记录之前，请确保您已经完成了基础的鹅群信息录入。健康记录是养鹅管理的重要组成部分，可以帮助您及时发现和处理健康问题。'
        },
        {
          id: 'requirements',
          type: 'tip',
          text: '建议每天定时记录，最好选择固定的时间点（如早上8点或晚上6点），这样有助于数据的连续性和可比性。'
        },
        {
          id: 'section2',
          type: 'title',
          text: '2. 录入基础信息'
        },
        {
          id: 'basic-steps',
          type: 'steps',
          steps: [
            '打开智慧养鹅App，进入健康管理模块',
            '点击"添加记录"按钮',
            '选择需要记录的鹅只或鹅群',
            '填写基础信息：日期、时间、记录人等'
          ]
        },
        {
          id: 'demo-image',
          type: 'image',
          src: '/images/help/health-record-demo.png',
          caption: '健康记录添加界面示例'
        },
        {
          id: 'section3',
          type: 'title',
          text: '3. 添加详细数据'
        },
        {
          id: 'detailed-info',
          type: 'paragraph',
          text: '详细的健康数据包括体温、体重、精神状态、食欲情况、排泄状况等。每一项数据都很重要，建议如实填写。'
        },
        {
          id: 'data-warning',
          type: 'warning',
          text: '请注意：体温超过42°C或低于40°C时，系统会自动标记为异常，建议立即采取相应措施。'
        },
        {
          id: 'section4',
          type: 'title',
          text: '4. 保存和管理'
        },
        {
          id: 'save-tips',
          type: 'paragraph',
          text: '完成数据录入后，点击"保存"按钮。系统会自动进行数据验证，如有异常会给出提示。保存成功后，您可以在健康记录列表中查看和管理所有记录。'
        }
      ]
    },
    
    // 相关文章
    relatedArticles: [
      {
        id: 202,
        title: 'AI诊断使用说明',
        summary: '学习如何使用AI智能诊断功能，快速识别鹅群健康问题',
        views: 425,
        updateTime: '2024-11-27'
      },
      {
        id: 203,
        title: '健康报告解读',
        summary: '教您如何正确理解和分析系统生成的健康报告',
        views: 356,
        updateTime: '2024-11-26'
      }
    ],
    
    // 状态控制
    showTOC: false,
    showFloatingTOC: false,
    isLiked: false,
    isHelpful: null, // null: 未评价, true: 有帮助, false: 没帮助
    
    // 导航状态
    hasPrev: true,
    hasNext: true
  },

  onLoad: function (options) {
    const { id, title } = options;
    
    if (id) {
      this.loadArticleData(id);
    }
    
    if (title) {
      wx.setNavigationBarTitle({
        title: decodeURIComponent(title)
      });
    }
    
    // 增加浏览量
    this.increaseViews();
    
    // 检查是否已点赞
    this.checkLikeStatus();
    
    // 检查评价状态
    this.checkRatingStatus();
  },

  onShow: function () {
    // 页面显示时的逻辑
  },

  // 加载文章数据
  loadArticleData: function(articleId) {
    // 这里可以从服务器加载具体的文章数据
    // 目前使用模拟数据
    const articleMap = {
      201: {
        title: '如何添加健康记录',
        categoryName: '健康管理'
      },
      202: {
        title: 'AI诊断使用说明',
        categoryName: '健康管理'
      },
      203: {
        title: '健康报告解读',
        categoryName: '健康管理'
      }
    };

    const articleData = articleMap[articleId];
    if (articleData) {
      this.setData({
        'articleInfo.id': articleId,
        'articleInfo.title': articleData.title,
        'articleInfo.categoryName': articleData.categoryName
      });
    }
  },

  // 增加浏览量
  increaseViews: function() {
    const articleInfo = this.data.articleInfo;
    articleInfo.views += 1;
    this.setData({ articleInfo });
    
    // 这里可以调用API更新服务器数据
  },

  // 检查点赞状态
  checkLikeStatus: function() {
    const likedArticles = wx.getStorageSync('likedArticles') || [];
    const isLiked = likedArticles.includes(this.data.articleInfo.id);
    this.setData({ isLiked });
  },

  // 检查评价状态
  checkRatingStatus: function() {
    const ratedArticles = wx.getStorageSync('ratedArticles') || {};
    const rating = ratedArticles[this.data.articleInfo.id];
    if (rating !== undefined) {
      this.setData({ isHelpful: rating });
    }
  },

  // 切换目录显示
  onToggleTOC: function() {
    this.setData({
      showTOC: !this.data.showTOC
    });
  },

  // 滚动到指定章节
  onScrollToSection: function(e) {
    const { section } = e.currentTarget.dataset;
    
    wx.pageScrollTo({
      selector: `#section-${section}`,
      duration: 300
    });
    
    // 收起目录
    this.setData({
      showTOC: false
    });
  },

  // 预览图片
  onPreviewImage: function(e) {
    const { src } = e.currentTarget.dataset;
    
    // 收集所有图片URL
    const imageUrls = this.data.articleInfo.content
      .filter(item => item.type === 'image')
      .map(item => item.src);
    
    wx.previewImage({
      current: src,
      urls: imageUrls
    });
  },

  // 标签搜索
  onTagSearch: function(e) {
    const { tag } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/help/search/search?keyword=${encodeURIComponent(tag)}`,
      fail: () => {
        wx.showToast({
          title: '搜索功能开发中',
          icon: 'none'
        });
      }
    });
  },

  // 切换点赞状态
  onToggleLike: function() {
    const { isLiked, articleInfo } = this.data;
    const newLikedState = !isLiked;
    
    // 更新本地状态
    this.setData({
      isLiked: newLikedState,
      'articleInfo.likes': articleInfo.likes + (newLikedState ? 1 : -1)
    });
    
    // 更新本地存储
    let likedArticles = wx.getStorageSync('likedArticles') || [];
    if (newLikedState) {
      likedArticles.push(articleInfo.id);
    } else {
      likedArticles = likedArticles.filter(id => id !== articleInfo.id);
    }
    wx.setStorageSync('likedArticles', likedArticles);
    
    // 显示反馈
    wx.showToast({
      title: newLikedState ? '已点赞' : '已取消点赞',
      icon: 'success'
    });
  },

  // 分享文章
  onShare: function() {
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  // 反馈
  onFeedback: function() {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback'
    });
  },

  // 查看相关文章
  onViewRelated: function(e) {
    const { article } = e.currentTarget.dataset;
    
    wx.redirectTo({
      url: `/pages/help/article/article?id=${article.id}&title=${encodeURIComponent(article.title)}`
    });
  },

  // 评价文章是否有帮助
  onRateHelpful: function(e) {
    const { helpful } = e.currentTarget.dataset;
    const isHelpful = helpful === 'true';
    
    this.setData({
      isHelpful: isHelpful
    });
    
    // 更新统计数据
    const articleInfo = this.data.articleInfo;
    if (isHelpful) {
      articleInfo.helpfulCount += 1;
    } else {
      articleInfo.unhelpfulCount += 1;
    }
    this.setData({ articleInfo });
    
    // 保存评价状态
    let ratedArticles = wx.getStorageSync('ratedArticles') || {};
    ratedArticles[articleInfo.id] = isHelpful;
    wx.setStorageSync('ratedArticles', ratedArticles);
    
    // 显示反馈
    wx.showToast({
      title: isHelpful ? '感谢您的反馈' : '我们会持续改进',
      icon: 'success'
    });
  },

  // 上一篇文章
  onPrevArticle: function() {
    if (!this.data.hasPrev) return;
    
    // 这里实现跳转到上一篇文章的逻辑
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 下一篇文章
  onNextArticle: function() {
    if (!this.data.hasNext) return;
    
    // 这里实现跳转到下一篇文章的逻辑
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 返回分类
  onBackToCategory: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 显示浮动目录
  onToggleFloatingTOC: function() {
    this.setData({
      showFloatingTOC: !this.data.showFloatingTOC
    });
  },

  // 关闭浮动目录
  onCloseFloatingTOC: function() {
    this.setData({
      showFloatingTOC: false
    });
  },

  // 滚动到章节并关闭浮动目录
  onScrollToSectionAndClose: function(e) {
    const { section } = e.currentTarget.dataset;
    
    wx.pageScrollTo({
      selector: `#section-${section}`,
      duration: 300
    });
    
    this.setData({
      showFloatingTOC: false
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    const { articleInfo } = this.data;
    return {
      title: `智慧养鹅 - ${articleInfo.title}`,
      desc: `${articleInfo.categoryName}帮助文档`,
      path: `/pages/help/article/article?id=${articleInfo.id}&title=${encodeURIComponent(articleInfo.title)}`
    };
  },

  // 页面滚动
  onPageScroll: function(e) {
    // 可以在这里处理页面滚动事件，比如显示/隐藏浮动按钮
  }
});