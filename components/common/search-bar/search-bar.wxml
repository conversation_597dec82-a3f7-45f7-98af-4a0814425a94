<!-- 通用搜索栏组件 -->
<view class="c-search-bar c-search-bar--{{shape}} c-search-bar--{{size}} {{focused ? 'c-search-bar--focused' : ''}} {{disabled ? 'c-search-bar--disabled' : ''}} {{customClass}}" style="{{customStyle}}">
  
  <!-- 搜索输入区域 -->
  <view class="c-search-bar__input-wrapper">
    <!-- 搜索图标 -->
    <view wx:if="{{showSearchIcon}}" class="c-search-bar__search-icon">
      <image class="c-search-bar__icon-img" src="{{searchIcon}}" mode="aspectFit"></image>
    </view>
    
    <!-- 输入框 -->
    <input
      class="c-search-bar__input"
      type="{{inputType}}"
      value="{{inputValue}}"
      placeholder="{{placeholder}}"
      disabled="{{disabled}}"
      readonly="{{readonly}}"
      focus="{{autofocus}}"
      maxlength="{{maxlength}}"
      adjust-position="{{adjustPosition}}"
      cursor-spacing="{{cursorSpacing}}"
      aria-label="{{ariaLabel || placeholder}}"
      bindinput="onInput"
      bindconfirm="onConfirm"
      bindfocus="onFocus"
      bindblur="onBlur"
    />
    
    <!-- 清除按钮 -->
    <view 
      wx:if="{{showClearButton && inputValue && !disabled && !readonly}}" 
      class="c-search-bar__clear-btn"
      bindtap="onClear"
    >
      <image class="c-search-bar__icon-img" src="{{clearIcon}}" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 取消按钮 -->
  <view 
    wx:if="{{showCancelButton}}" 
    class="c-search-bar__cancel-btn {{focused ? 'c-search-bar__cancel-btn--show' : ''}}"
    bindtap="onCancel"
  >
    <text>{{cancelText}}</text>
  </view>
  
  <!-- 搜索历史面板 -->
  <view wx:if="{{showHistoryPanel && historyList.length > 0}}" class="c-search-bar__history-panel">
    <view class="c-search-bar__history-header">
      <text class="c-search-bar__history-title">搜索历史</text>
      <view class="c-search-bar__history-clear" bindtap="onClearHistory">
        <text>清除</text>
      </view>
    </view>
    
    <view class="c-search-bar__history-list">
      <view 
        wx:for="{{historyList}}" 
        wx:key="index"
        class="c-search-bar__history-item"
        data-value="{{item}}"
        data-index="{{index}}"
        bindtap="onHistoryItemTap"
      >
        <view class="c-search-bar__history-text">
          <text>{{item}}</text>
        </view>
        <view class="c-search-bar__history-delete" bindtap="onDeleteHistoryItem" catchtap="stopPropagation">
          <image class="c-search-bar__icon-img" src="{{clearIcon}}" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
</view>