/**
 * 优化后的生产记录页面
 * 提供美观的界面和流畅的交互体验
 */

Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    hasMore: true,
    showAddMenu: false,

    // 筛选状态
    selectedFilter: 'all',
    filterTabs: [
      { value: 'all', label: '全部记录', icon: '📋', count: 0 },
      { value: 'entry', label: '入栏记录', icon: '📥', count: 0 },
      { value: 'weight', label: '称重记录', icon: '⚖️', count: 0 },
      { value: 'sale', label: '出栏记录', icon: '📤', count: 0 }
    ],

    // 数据
    allRecords: [],
    filteredRecords: [],
    groupedRecords: [],

    // 统计数据
    todayRecords: 0,
    totalRecords: 0,
    activeBatches: 0,

    // 分页
    currentPage: 1,
    pageSize: 20
  },

  onLoad(options) {
    this.loadInitialData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  onReachBottom() {
    this.onLoadMore();
  },

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    this.setData({ loading: true });

    try {
      await Promise.all([
        this.loadProductionRecords(),
        this.loadStatistics()
      ]);
    } catch (error) {
      console.error('加载初始数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载生产记录
   */
  async loadProductionRecords(page = 1, append = false) {
    try {
      const app = getApp();
      const response = await wx.request({
        url: `${app.globalData.apiBase}/production/records`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
          'Content-Type': 'application/json'
        },
        data: {
          page,
          limit: this.data.pageSize,
          type: this.data.selectedFilter === 'all' ? undefined : this.data.selectedFilter
        }
      });

      if (response.data.success) {
        const newRecords = response.data.data.records || [];
        const allRecords = append ? [...this.data.allRecords, ...newRecords] : newRecords;
        
        this.setData({
          allRecords,
          hasMore: newRecords.length === this.data.pageSize,
          currentPage: page
        });

        this.processRecords();
        this.updateFilterCounts();
      }
    } catch (error) {
      console.error('加载生产记录失败:', error);
      throw error;
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const app = getApp();
      const [recordsResponse, batchesResponse] = await Promise.all([
        wx.request({
          url: `${app.globalData.apiBase}/production/records`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
            'Content-Type': 'application/json'
          },
          data: {
            startDate: new Date().toISOString().split('T')[0],
            endDate: new Date().toISOString().split('T')[0]
          }
        }),
        wx.request({
          url: `${app.globalData.apiBase}/production/active-batches`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('access_token')}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      const todayRecords = recordsResponse.data.success ? 
        (recordsResponse.data.data.pagination?.total || 0) : 0;
      const activeBatches = batchesResponse.data.success ? 
        (batchesResponse.data.data?.length || 0) : 0;

      this.setData({
        todayRecords,
        totalRecords: this.data.allRecords.length,
        activeBatches
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  /**
   * 处理记录数据
   */
  processRecords() {
    const { allRecords, selectedFilter } = this.data;
    
    // 筛选记录
    let filteredRecords = allRecords;
    if (selectedFilter !== 'all') {
      filteredRecords = allRecords.filter(record => record.type === selectedFilter);
    }

    // 按日期分组
    const groupedRecords = this.groupRecordsByDate(filteredRecords);

    this.setData({
      filteredRecords,
      groupedRecords,
      totalRecords: allRecords.length
    });
  },

  /**
   * 按日期分组记录
   */
  groupRecordsByDate(records) {
    const groups = {};
    
    records.forEach(record => {
      const date = record.date;
      if (!groups[date]) {
        groups[date] = {
          date,
          dateText: this.formatGroupDate(date),
          records: []
        };
      }
      groups[date].records.push(record);
    });

    // 转换为数组并排序
    return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date));
  },

  /**
   * 格式化分组日期
   */
  formatGroupDate(dateStr) {
    const date = new Date(dateStr);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (this.isSameDay(date, today)) {
      return '今天';
    } else if (this.isSameDay(date, yesterday)) {
      return '昨天';
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    }
  },

  /**
   * 判断是否为同一天
   */
  isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  },

  /**
   * 更新筛选标签计数
   */
  updateFilterCounts() {
    const { allRecords } = this.data;
    const counts = {
      all: allRecords.length,
      entry: allRecords.filter(r => r.type === 'entry').length,
      weight: allRecords.filter(r => r.type === 'weight').length,
      sale: allRecords.filter(r => r.type === 'sale').length
    };

    const filterTabs = this.data.filterTabs.map(tab => ({
      ...tab,
      count: counts[tab.value] || 0
    }));

    this.setData({ filterTabs });
  },

  /**
   * 筛选变更
   */
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.value;
    this.setData({ 
      selectedFilter: filter,
      currentPage: 1
    });
    
    this.loadProductionRecords(1, false);
  },

  /**
   * 刷新数据
   */
  async onRefresh() {
    this.setData({ refreshing: true });
    
    try {
      await this.loadInitialData();
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 加载更多
   */
  async onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    const nextPage = this.data.currentPage + 1;
    await this.loadProductionRecords(nextPage, true);
  },

  /**
   * 刷新数据（不显示加载状态）
   */
  async refreshData() {
    try {
      await this.loadProductionRecords(1, false);
      await this.loadStatistics();
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  },

  /**
   * 显示添加菜单
   */
  onShowAddMenu() {
    this.setData({ showAddMenu: true });
  },

  /**
   * 隐藏添加菜单
   */
  onHideAddMenu() {
    this.setData({ showAddMenu: false });
  },

  /**
   * 添加记录
   */
  onAddRecord(e) {
    const type = e.currentTarget.dataset.type;
    this.onHideAddMenu();
    
    // 跳转到对应的添加页面
    const pageMap = {
      entry: '/pages/add-entry-record/add-entry-record',
      weight: '/pages/add-weight-record/add-weight-record',
      sale: '/pages/add-sale-record/add-sale-record'
    };

    const page = pageMap[type];
    if (page) {
      wx.navigateTo({
        url: page,
        success: () => {
          console.log(`跳转到${type}记录添加页面`);
        },
        fail: () => {
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 记录点击事件
   */
  onRecordTap(e) {
    const { record } = e.detail;
    
    // 跳转到记录详情页面
    wx.navigateTo({
      url: `/pages/production-record-detail/production-record-detail?id=${record.id}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '生产记录管理',
      path: '/pages/production-records-optimized/production-records-optimized'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '智慧养鹅生产记录管理'
    };
  }
});
