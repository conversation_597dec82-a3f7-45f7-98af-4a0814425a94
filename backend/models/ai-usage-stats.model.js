// backend/models/ai-usage-stats.model.js
// AI使用统计数据模型

const { DataTypes } = require("sequelize");
const { sequelize } = require("../config/database");

const AIUsageStats = sequelize.define(
  "AIUsageStats",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "user_id",
      comment: "用户ID",
    },
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "flock_id",
      comment: "鹅群ID",
    },
    provider: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: "AI服务提供商",
    },
    model: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "使用的模型",
    },
    scenario: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "使用场景 (AI_INVENTORY_COUNTING, IMAGE_RECOGNITION等)",
    },
    feature: {
      type: DataTypes.STRING(100),
      comment: "功能模块 (ai_inventory, health_diagnosis等)",
    },
    serviceType: {
      type: DataTypes.STRING(50),
      field: "service_type",
      comment: "服务类型",
    },
    usageCount: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      field: "usage_count",
      comment: "使用次数",
    },
    requestTokens: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "request_tokens",
      comment: "请求token数",
    },
    responseTokens: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "response_tokens",
      comment: "响应token数",
    },
    totalTokens: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "total_tokens",
      comment: "总token数",
    },
    cost: {
      type: DataTypes.DECIMAL(10, 6),
      defaultValue: 0,
      comment: "成本（元）",
    },
    responseTime: {
      type: DataTypes.INTEGER,
      field: "response_time",
      comment: "响应时间（毫秒）",
    },
    success: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: "是否成功",
    },
    errorMessage: {
      type: DataTypes.TEXT,
      field: "error_message",
      comment: "错误信息",
    },
    requestData: {
      type: DataTypes.JSON,
      field: "request_data",
      comment: "请求数据（脱敏后）",
    },
    responseData: {
      type: DataTypes.JSON,
      field: "response_data",
      comment: "响应数据（脱敏后）",
    },
    ipAddress: {
      type: DataTypes.STRING(45),
      field: "ip_address",
      comment: "请求IP地址",
    },
    userAgent: {
      type: DataTypes.TEXT,
      field: "user_agent",
      comment: "用户代理",
    },
    sessionId: {
      type: DataTypes.STRING(100),
      field: "session_id",
      comment: "会话ID",
    },
  },
  {
    tableName: "ai_usage_stats",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        fields: ["userId"],
      },
      {
        fields: ["provider"],
      },
      {
        fields: ["scenario"],
      },
      {
        fields: ["success"],
      },
      {
        fields: ["createdAt"],
      },
      {
        fields: ["userId", "createdAt"],
      },
      {
        fields: ["provider", "createdAt"],
      },
    ],
  },
);

module.exports = AIUsageStats;
