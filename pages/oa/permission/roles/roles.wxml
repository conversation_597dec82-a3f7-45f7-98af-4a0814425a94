<!-- pages/oa/permission/roles/roles.wxml -->

<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">角色管理</text>
    <text class="page-subtitle">管理系统角色和权限配置</text>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <oa-data-card
      title="总角色数"
      value="{{statistics.total}}"
      icon="角色"
      iconColor="#007AFF"
      size="small"
    />
    <oa-data-card
      title="启用中"
      value="{{statistics.active}}"
      icon="启用"
      iconColor="#00A86B"
      size="small"
    />
    <oa-data-card
      title="已停用"
      value="{{statistics.inactive}}"
      icon="停用"
      iconColor="#8E8E93"
      size="small"
    />
    <oa-data-card
      title="系统角色"
      value="{{statistics.systemRoles}}"
      icon="系统"
      iconColor="#FF9500"
      size="small"
    />
  </view>

  <!-- 搜索和筛选 -->
  <view class="search-section">
    <view class="search-bar">
      <input 
        class="search-input"
        placeholder="搜索角色..."
        value="{{filters.keyword}}"
        bindinput="onSearchInput"
      />
      <button class="filter-btn" bindtap="toggleFilters">
        筛选 {{showFilters ? '▲' : '▼'}}
      </button>
    </view>
    
    <!-- 筛选面板 -->
    <view wx:if="{{showFilters}}" class="filter-panel">
      <view class="filter-row">
        <text class="filter-label">级别</text>
        <picker 
          mode="selector" 
          range="{{levelOptions}}" 
          range-key="label"
          value="{{levelIndex}}"
          bindchange="onLevelFilter"
        >
          <view class="filter-picker">
            {{selectedLevelLabel}}
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="filter-row">
        <text class="filter-label">状态</text>
        <picker 
          mode="selector" 
          range="{{statusOptions}}" 
          range-key="label"
          value="{{statusIndex}}"
          bindchange="onStatusFilter"
        >
          <view class="filter-picker">
            {{selectedStatusLabel}}
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="filter-actions">
        <button class="clear-btn" bindtap="clearFilters">清除筛选</button>
      </view>
    </view>
  </view>

  <!-- 角色列表 -->
  <view class="list-section">
    <!-- 加载状态 -->
    <view wx:if="{{refreshing}}" class="loading-state">
      <text>加载中...</text>
    </view>

    <!-- 空数据状态 -->
    <view wx:elif="{{roles.length === 0 && !loading}}" class="empty-state">
      <text class="empty-icon">角色管理</text>
      <text class="empty-text">暂无角色</text>
      <button class="create-btn" bindtap="onCreateRole">创建角色</button>
    </view>

    <!-- 角色列表 -->
    <view wx:else class="role-list">
      <view 
        wx:for="{{roles}}" 
        wx:key="id"
        class="role-item"
      >
        <view class="item-header">
          <view class="title-area">
            <text class="item-title">{{item.name}}</text>
            <view class="tags">
              <oa-status-tag
                text="{{item.levelLabel}}"
                type="info"
                size="small"
              />
              <oa-status-tag
                text="{{item.statusLabel}}"
                color="{{item.statusColor}}"
                size="small"
              />
              <oa-status-tag
                wx:if="{{item.is_system}}"
                text="系统角色"
                type="warning"
                size="small"
              />
            </view>
          </view>
          <text class="time-ago">{{item.createdTimeAgo}}</text>
        </view>
        
        <view wx:if="{{item.description}}" class="item-description">
          <text>{{item.description}}</text>
        </view>
        
        <view class="item-info">
          <view class="info-item">
            <text class="info-label">角色代码：</text>
            <text class="info-value">{{item.code}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">数据权限：</text>
            <text class="info-value">{{item.dataScopeLabel}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">创建人：</text>
            <text class="info-value">{{item.creator_name}}</text>
          </view>
        </view>
        
        <view class="item-actions">
          <button 
            class="action-btn config"
            data-role="{{item}}"
            bindtap="onConfigPermissions"
          >
            配置权限
          </button>
          <button 
            class="action-btn edit"
            data-role="{{item}}"
            bindtap="onEditRole"
          >
            编辑
          </button>
          <button 
            class="action-btn toggle"
            data-role="{{item}}"
            bindtap="onToggleStatus"
          >
            {{item.is_active ? '停用' : '启用'}}
          </button>
          <button 
            wx:if="{{!item.is_system}}"
            class="action-btn delete"
            data-role="{{item}}"
            bindtap="onDeleteRole"
          >
            删除
          </button>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view wx:if="{{loading}}" class="loading-more">
        <text>加载更多...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view wx:if="{{!pagination.hasMore && roles.length > 0}}" class="no-more">
        <text>没有更多数据了</text>
      </view>
    </view>
  </view>

  <!-- 浮动创建按钮 -->
  <view class="fab" bindtap="onCreateRole">
    <text class="fab-icon">+</text>
  </view>

  <!-- 角色编辑模态框 -->
  <view wx:if="{{showRoleModal}}" class="modal-overlay" bindtap="hideRoleModal">
    <view class="role-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{currentRole ? '编辑角色' : '创建角色'}}</text>
        <button class="modal-close" bindtap="hideRoleModal">×</button>
      </view>
      
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label">角色名称 *</text>
          <input 
            class="form-input {{errors.name ? 'error' : ''}}"
            placeholder="请输入角色名称"
            value="{{roleForm.name}}"
            data-field="name"
            bindinput="onFormInput"
          />
          <text wx:if="{{errors.name}}" class="error-text">{{errors.name}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">角色代码 *</text>
          <input 
            class="form-input {{errors.code ? 'error' : ''}}"
            placeholder="请输入角色代码"
            value="{{roleForm.code}}"
            data-field="code"
            bindinput="onFormInput"
          />
          <text wx:if="{{errors.code}}" class="error-text">{{errors.code}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">角色级别</text>
          <picker 
            mode="selector" 
            range="{{['一级（管理员）', '二级（经理）', '三级（专员）', '四级（员工）']}}"
            value="{{roleForm.level - 1}}"
            bindchange="onLevelSelect"
          >
            <view class="form-picker">
              {{getLevelLabel(roleForm.level)}}
              <text class="arrow">▼</text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">数据权限</text>
          <picker
            mode="selector"
            range="{{dataScopeOptions}}"
            range-key="label"
            value="{{selectedDataScopeIndex}}"
            bindchange="onDataScopeSelect"
          >
            <view class="form-picker">
              {{getDataScopeLabel(roleForm.dataScope)}}
              <text class="arrow">▼</text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">角色描述</text>
          <textarea 
            class="form-textarea"
            placeholder="请输入角色描述（可选）"
            value="{{roleForm.description}}"
            data-field="description"
            bindinput="onFormInput"
            maxlength="500"
          />
        </view>
        
        <view class="form-item">
          <view class="switch-item">
            <text class="switch-label">启用状态</text>
            <switch 
              checked="{{roleForm.isActive}}"
              bindchange="onStatusSwitch"
              color="#007AFF"
            />
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="hideRoleModal">取消</button>
        <button 
          class="modal-btn confirm"
          loading="{{submitting}}"
          bindtap="saveRole"
        >
          {{currentRole ? '更新' : '创建'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 权限配置模态框 -->
  <view wx:if="{{showPermissionModal}}" class="modal-overlay" bindtap="hidePermissionModal">
    <view class="permission-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">配置权限 - {{currentRole.name}}</text>
        <button class="modal-close" bindtap="hidePermissionModal">×</button>
      </view>

      <view class="modal-content">
        <view
          wx:for="{{permissionTreeWithCounts}}"
          wx:key="module"
          class="permission-module"
        >
          <view class="module-header" data-module="{{item.module}}" bindtap="onModuleToggle">
            <text class="module-name">{{item.name}}</text>
            <text class="module-count">
              {{item.selectedCount}}/{{item.totalCount}}
            </text>
          </view>

          <view class="permission-list">
            <view
              wx:for="{{item.permissions}}"
              wx:key="id"
              wx:for-item="permission"
              class="permission-item"
              data-permission-id="{{permission.id}}"
              bindtap="onPermissionToggle"
            >
              <checkbox checked="{{rolePermissions.includes(permission.id)}}" />
              <view class="permission-info">
                <text class="permission-name">{{permission.name}}</text>
                <text class="permission-desc">{{permission.description}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="hidePermissionModal">取消</button>
        <button
          class="modal-btn confirm"
          loading="{{submitting}}"
          bindtap="savePermissions"
        >
          保存权限
        </button>
      </view>
    </view>
  </view>
</view>
