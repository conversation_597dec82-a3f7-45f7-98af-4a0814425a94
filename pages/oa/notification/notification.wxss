@import '/styles/oa-common.wxss';
/* pages/oa/notification/notification.wxss */

.container {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding-bottom: 40rpx;
}

/* ==================== 页面头部 ==================== */
.page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  padding: 40rpx 30rpx 50rpx 30rpx;
  color: var(--text-inverse);
}

.page-title {
  font-size: 44rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

/* ==================== 统计卡片 ==================== */
.stats-section {
  padding: 30rpx;
}

.stats-grid {
  display: flex;
  gap: 20rpx;
}

.stat-card {
  flex: 1;
  background: var(--bg-primary);
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  text-align: center;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-card.total {
  border-left: 6rpx solid #2196f3;
}

.stat-card.unread {
  border-left: 6rpx solid #ff5722;
}

.stat-card.today {
  border-left: 6rpx solid #4caf50;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.stat-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff5722;
  color: var(--text-inverse);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 20rpx;
  text-align: center;
}

/* ==================== 操作栏 ==================== */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 20rpx;
}

.filter-btn,
.mark-all-btn {
  background: var(--bg-primary);
  border: 2rpx solid #e0e0e0;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #333;
}

.filter-btn:after,
.mark-all-btn:after {
  border: none;
}

.mark-all-btn {
  background: #2196f3;
  color: var(--text-inverse);
  border-color: #2196f3;
}

.mark-all-btn[disabled] {
  background: #e0e0e0;
  color: #999;
  border-color: #e0e0e0;
}

/* ==================== 筛选面板 ==================== */
.filter-panel {
  background: var(--bg-primary);
  margin: 0 30rpx 20rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.filter-row {
  display: flex;
  gap: 20rpx;
}

.filter-item {
  flex: 1;
}

.filter-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}

.picker {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx 16rpx;
  font-size: 26rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow {
  color: #999;
  font-size: 20rpx;
}

/* ==================== 通知列表 ==================== */
.notification-list {
  padding: 0 30rpx;
}

.notification-item {
  background: var(--bg-primary);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  display: flex;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.notification-item.unread {
  border-left: 6rpx solid #ff5722;
}

.notification-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 36rpx;
  flex-shrink: 0;
}

.notification-icon.system {
  background: #e3f2fd;
}

.notification-icon.approval {
  background: #e8f5e8;
}

.notification-icon.task {
  background: #fff3e0;
}

.notification-icon.announcement {
  background: #fce4ec;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.notification-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.type-tag {
  background: #f5f5f5;
  color: #666;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

.type-tag.system {
  background: #e3f2fd;
  color: #1976d2;
}

.type-tag.approval {
  background: #e8f5e8;
  color: #2e7d32;
}

.type-tag.task {
  background: #fff3e0;
  color: #f57c00;
}

.type-tag.announcement {
  background: #fce4ec;
  color: #c2185b;
}

.unread-badge {
  background: #ff5722;
  color: var(--text-inverse);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

.notification-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.notification-footer {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 22rpx;
  color: #999;
}

.sender {
  font-weight: 500;
}

.time {
  flex: 1;
}

.today-badge {
  background: #4caf50;
  color: var(--text-inverse);
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.unread-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff5722;
  border-radius: 50%;
}

/* ==================== 空状态 ==================== */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
}

.empty-icon {
  font-size: 100rpx;
  display: block;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* ==================== 加载状态 ==================== */
.loading-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999;
  background: var(--bg-primary);
  border-radius: 16rpx;
  margin-top: 20rpx;
}