// pages/health/record-list/record-list.js
const api = require('../../../utils/api.js');

Page({
  data: {
    records: [],
    loading: true,
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    statusFilter: 'all' // all, healthy, sick, treated
  },

  onLoad: function (options) {
    // 页面加载时获取健康记录列表
    this.loadRecords();
  },

  onShow: function () {
    // 页面显示
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadRecords(() => {
        wx.stopPullDownRefresh();
      });
    });
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore) {
      this.loadMore();
    }
  },

  // 加载健康记录
  loadRecords: function (callback) {
    const { pageNum, pageSize, statusFilter } = this.data;
    
    // 显示加载状态
    if (pageNum === 1) {
      this.setData({
        loading: true
      });
    }
    
    // 调用API获取健康记录列表
    api.health.getRecords({
      page: pageNum,
      size: pageSize,
      status: statusFilter === 'all' ? '' : statusFilter
    }).then(res => {
      if (res.code === 0) {
        const newRecords = res.data.records || [];
        const hasMore = newRecords.length === pageSize;
        
        this.setData({
          records: pageNum === 1 ? newRecords : this.data.records.concat(newRecords),
          hasMore: hasMore,
          loading: false
        });
      } else {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: res.message || '获取数据失败',
          icon: 'none'
        });
      }
      callback && callback();
    }).catch(err => {
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      callback && callback();
    });
  },

  // 加载更多
  loadMore: function () {
    this.setData({
      pageNum: this.data.pageNum + 1
    }, () => {
      this.loadRecords();
    });
  },

  // 查看记录详情
  onViewDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/health/record-detail/record-detail?id=${id}`
    });
  },

  // 添加新记录
  onAddRecord: function () {
    wx.navigateTo({
      url: '/pages/health/record-detail/record-detail'
    });
  },

  // 筛选状态
  onFilterStatus: function (e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      statusFilter: status,
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadRecords();
    });
  }
});