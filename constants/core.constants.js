/**
 * 核心常量 - 只包含主包必需的常量
 * 减少主包尺寸，其他常量移至分包按需加载
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
};

const CURRENT_ENV = ENV.DEVELOPMENT;

const BASE_URLS = {
  [ENV.DEVELOPMENT]: 'http://localhost:3001',
  [ENV.PRODUCTION]: 'https://api.zhihuiyange.com'
};

// API版本配置
const API_VERSIONS = {
  V1: '/api/v1',
  V2: '/api/v2'
};

// 获取当前环境的基础URL
const BASE_URL = BASE_URLS[CURRENT_ENV];

// 构建完整API路径的辅助函数
const buildApiUrl = (version, path) => `${BASE_URL}${version}${path}`;

// 核心API端点 - 只包含启动和认证必需的
const CORE_ENDPOINTS = {
  AUTH: {
    LOGIN: buildApiUrl(API_VERSIONS.V1, '/auth/login'),
    LOGOUT: buildApiUrl(API_VERSIONS.V1, '/auth/logout'),
    USER_INFO: buildApiUrl(API_VERSIONS.V1, '/auth/userinfo'),
    WECHAT_LOGIN: buildApiUrl(API_VERSIONS.V1, '/auth/wechat-login')
  }
};

// 核心用户角色
const CORE_USER = {
  ROLES: {
    ADMIN: 'admin',
    MANAGER: 'manager',
    USER: 'user'
  },
  DEFAULTS: {
    AVATAR: '/images/default_avatar.png',
    NAME: '用户',
    FARM_NAME: '我的养殖场'
  }
};

// 核心UI常量
const CORE_UI = {
  COLORS: {
    PRIMARY: '#0066CC',
    WHITE: '#FFFFFF',
    BLACK: '#000000',
    SUCCESS: '#52C41A',
    ERROR: '#FF4D4F',
    WARNING: '#FAAD14'
  },
  FONT_SIZES: {
    SMALL: '24rpx',
    NORMAL: '28rpx',
    LARGE: '32rpx'
  }
};

// 核心图片路径
const CORE_IMAGES = {
  DEFAULT_AVATAR: '/images/default_avatar.png',
  DEFAULT_LOGO: '/images/default-logo.png',
  ICONS: {
    ADD: '/images/icons/add.png',
    ARROW_RIGHT: '/images/icons/arrow_right.png'
  }
};

// 导出核心常量
module.exports = {
  ENV,
  CURRENT_ENV,
  BASE_URL,
  API_VERSIONS,
  buildApiUrl,
  ENDPOINTS: CORE_ENDPOINTS,
  USER: CORE_USER,
  UI: CORE_UI,
  IMAGES: CORE_IMAGES
};
