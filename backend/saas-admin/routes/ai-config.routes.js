const express = require("express");
const router = express.Router();

// AI配置管理页面
router.get("/", async (req, res) => {
  try {
    // 模拟AI配置数据
    const aiConfigs = [
      {
        id: 1,
        provider: "OpenAI",
        model: "gpt-4",
        apiKey: "sk-****...****",
        status: "active",
        maxTokens: 4000,
        temperature: 0.7,
        usage: {
          dailyCalls: 1250,
          monthlyCalls: 35600,
          limit: 100000,
        },
        createdAt: "2024-01-15T10:30:00Z",
      },
      {
        id: 2,
        provider: "百度文心",
        model: "ernie-bot-4.0",
        apiKey: "ak-****...****",
        status: "active",
        usage: {
          dailyCalls: 890,
          monthlyCalls: 24500,
          limit: 50000,
        },
        createdAt: "2024-01-10T14:20:00Z",
      },
      {
        id: 3,
        provider: "阿里通义千问",
        model: "qwen-max",
        apiKey: "sk-****...****",
        status: "inactive",
        usage: {
          dailyCalls: 0,
          monthlyCalls: 1200,
          limit: 30000,
        },
        createdAt: "2024-01-08T09:15:00Z",
      },
    ];

    // 全局AI配置统计
    const aiStats = {
      totalProviders: aiConfigs.length,
      activeProviders: aiConfigs.filter((config) => config.status === "active")
        .length,
      totalDailyCalls: aiConfigs.reduce(
        (sum, config) => sum + (config.usage?.dailyCalls || 0),
        0,
      ),
      totalMonthlyCalls: aiConfigs.reduce(
        (sum, config) => sum + (config.usage?.monthlyCalls || 0),
        0,
      ),
      totalLimit: aiConfigs.reduce(
        (sum, config) => sum + (config.usage?.limit || 0),
        0,
      ),
    };

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("ai-config/index", {
        title: "AI配置管理",
        aiConfigs: aiConfigs,
        aiStats: aiStats,
      });
    } else {
      // 如果是API请求，返回JSON
      res.json({
        success: true,
        message: "获取AI配置列表成功",
        data: {
          configs: aiConfigs,
          stats: aiStats,
        },
      });
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取AI配置失败:", error); } catch(_) {}

    if (req.accepts("html")) {
      return res.render("error", {
        title: "错误",
        error: "获取AI配置失败",
        message: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "获取AI配置失败",
        error: error.message,
      });
    }
  }
});

// AI配置详情
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // 模拟获取单个AI配置
    const config = {
      id: parseInt(id),
      provider: "OpenAI",
      model: "gpt-4",
      apiKey:
        "sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
      baseUrl: "https://api.openai.com/v1",
      status: "active",
      maxTokens: 4000,
      temperature: 0.7,
      topP: 1.0,
      frequencyPenalty: 0,
      presencePenalty: 0,
      timeout: 30000,
      retryAttempts: 3,
      usage: {
        dailyCalls: 1250,
        monthlyCalls: 35600,
        limit: 100000,
        costPerCall: 0.03,
      },
      features: {
        textGeneration: true,
        imageAnalysis: true,
        voiceProcessing: false,
        functionCalling: true,
      },
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-20T15:45:00Z",
    };

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("ai-config/detail", {
        title: `AI配置详情 - ${config.provider}`,
        config: config,
      });
    } else {
      // 如果是API请求，返回JSON
      res.json({
        success: true,
        message: "获取AI配置详情成功",
        data: config,
      });
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取AI配置详情失败:", error); } catch(_) {}

    if (req.accepts("html")) {
      return res.render("error", {
        title: "错误",
        error: "获取AI配置详情失败",
        message: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "获取AI配置详情失败",
        error: error.message,
      });
    }
  }
});

// 创建AI配置
router.post("/", async (req, res) => {
  try {
    const { provider, model, apiKey, baseUrl, maxTokens, temperature } =
      req.body;

    // 模拟创建AI配置
    const newConfig = {
      id: Math.floor(Math.random() * 1000) + 4,
      provider,
      model,
      apiKey,
      baseUrl,
      status: "inactive",
      maxTokens: maxTokens || 4000,
      temperature: temperature || 0.7,
      usage: {
        dailyCalls: 0,
        monthlyCalls: 0,
        limit: 10000,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "创建AI配置成功",
      data: newConfig,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "创建AI配置失败",
      error: error.message,
    });
  }
});

// 更新AI配置
router.put("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { provider, model, apiKey, status, maxTokens, temperature } =
      req.body;

    // 模拟更新AI配置
    const updatedConfig = {
      id: parseInt(id),
      provider,
      model,
      apiKey,
      status,
      maxTokens,
      temperature,
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "更新AI配置成功",
      data: updatedConfig,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "更新AI配置失败",
      error: error.message,
    });
  }
});

// 删除AI配置
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;

    res.json({
      success: true,
      message: "删除AI配置成功",
      data: { id: parseInt(id) },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "删除AI配置失败",
      error: error.message,
    });
  }
});

// 测试AI配置
router.post("/:id/test", async (req, res) => {
  try {
    const { id } = req.params;

    // 模拟测试AI配置连接
    const testResult = {
      success: true,
      provider: "OpenAI",
      model: "gpt-4",
      responseTime: 1250,
      status: "connected",
      message: "AI配置测试成功",
    };

    res.json({
      success: true,
      message: "AI配置测试完成",
      data: testResult,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "AI配置测试失败",
      error: error.message,
    });
  }
});

module.exports = router;
