#!/bin/bash

# 🚀 开发环境HTTPS代理服务器启动脚本
# 用于为微信小程序提供HTTPS代理支持

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🚀 启动智慧养鹅开发环境HTTPS代理服务器"
echo ""

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    print_message $RED "❌ 未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查npm包
print_message $YELLOW "📦 检查依赖包..."
if ! npm list express http-proxy-middleware cors &> /dev/null; then
    print_message $YELLOW "📦 安装缺失的依赖包..."
    npm install express http-proxy-middleware cors
fi

# 检查OpenSSL（用于生成SSL证书）
if ! command -v openssl &> /dev/null; then
    print_message $YELLOW "⚠️  未找到OpenSSL，将尝试使用备用方案"
    print_message $YELLOW "   建议安装OpenSSL以获得更好的SSL证书支持"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        print_message $YELLOW "   macOS: brew install openssl"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_message $YELLOW "   Ubuntu/Debian: sudo apt-get install openssl"
        print_message $YELLOW "   CentOS/RHEL: sudo yum install openssl"
    fi
fi

# 创建证书目录
CERT_DIR="../certs"
if [ ! -d "$CERT_DIR" ]; then
    print_message $YELLOW "📁 创建证书目录: $CERT_DIR"
    mkdir -p "$CERT_DIR"
fi

# 设置环境变量
export HTTPS_PORT=${HTTPS_PORT:-8443}
export HTTP_PORT=${HTTP_PORT:-8080}
export TARGET_PORT=${TARGET_PORT:-3000}
export PROXY_DOMAIN=${PROXY_DOMAIN:-dev-api.zhihuiyange.local}

print_message $GREEN "⚙️  配置信息:"
print_message $GREEN "   HTTPS端口: $HTTPS_PORT"
print_message $GREEN "   HTTP端口: $HTTP_PORT"
print_message $GREEN "   目标端口: $TARGET_PORT"
print_message $GREEN "   代理域名: $PROXY_DOMAIN"
echo ""

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
        return 0  # 端口被占用
    else
        return 1  # 端口可用
    fi
}

if check_port $HTTPS_PORT; then
    print_message $RED "❌ 端口 $HTTPS_PORT 已被占用"
    print_message $YELLOW "   请使用其他端口: HTTPS_PORT=8444 ./start-dev-proxy.sh"
    exit 1
fi

if check_port $HTTP_PORT; then
    print_message $RED "❌ 端口 $HTTP_PORT 已被占用"
    print_message $YELLOW "   请使用其他端口: HTTP_PORT=8081 ./start-dev-proxy.sh"
    exit 1
fi

# 检查后端服务器是否运行
print_message $YELLOW "🔍 检查后端服务器 (localhost:$TARGET_PORT)..."
if ! nc -z localhost $TARGET_PORT 2>/dev/null; then
    print_message $YELLOW "⚠️  后端服务器未运行在端口 $TARGET_PORT"
    print_message $YELLOW "   请确保后端服务器已启动"
    print_message $YELLOW "   或使用其他端口: TARGET_PORT=3001 ./start-dev-proxy.sh"
fi

# 添加hosts文件条目（需要管理员权限）
add_hosts_entry() {
    local domain=$1
    local ip="127.0.0.1"
    
    if ! grep -q "$ip $domain" /etc/hosts; then
        print_message $YELLOW "🌐 添加hosts条目: $ip $domain"
        print_message $YELLOW "   这需要管理员权限..."
        
        if [[ "$OSTYPE" == "darwin"* ]] || [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo "$ip $domain" | sudo tee -a /etc/hosts > /dev/null
        else
            print_message $YELLOW "   请手动添加到hosts文件: $ip $domain"
        fi
    fi
}

# 添加域名到hosts文件
add_hosts_entry "$PROXY_DOMAIN"

print_message $GREEN "🚀 启动代理服务器..."
echo ""

# 启动代理服务器
node dev-https-proxy.js

# 清理函数
cleanup() {
    print_message $YELLOW "\n🛑 正在关闭代理服务器..."
    # 杀死所有相关进程
    pkill -f "dev-https-proxy.js" || true
    print_message $GREEN "✅ 代理服务器已关闭"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 等待用户中断
while true; do
    sleep 1
done