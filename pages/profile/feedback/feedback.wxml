<!-- 意见反馈页面 -->
<view class="feedback-container">
  <!-- 反馈类型选择 -->
  <view class="feedback-type-section">
    <view class="section-header">
      <text class="section-title">反馈类型</text>
      <text class="required-mark">*</text>
    </view>
    <view class="type-grid">
      <view class="type-item {{feedbackData.type === item.value ? 'active' : ''}}" 
            wx:for="{{feedbackTypes}}" 
            wx:key="value"
            bindtap="onSelectType" 
            data-type="{{item.value}}">
        <view class="type-icon">{{item.icon}}</view>
        <text class="type-title">{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 问题描述 -->
  <view class="description-section">
    <view class="section-header">
      <text class="section-title">问题描述</text>
      <text class="required-mark">*</text>
    </view>
    <view class="textarea-container">
      <textarea class="feedback-textarea" 
                placeholder="请详细描述您遇到的问题或建议，我们会认真对待每一条反馈..."
                value="{{feedbackData.description}}"
                bindinput="onDescriptionInput"
                maxlength="500"
                auto-height
                show-confirm-bar="{{false}}" />
      <view class="char-count">
        <text class="count-text">{{feedbackData.description.length}}/500</text>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <view class="section-header">
      <text class="section-title">联系方式</text>
      <text class="optional-mark">（选填）</text>
    </view>
    <view class="contact-inputs">
      <view class="input-group">
        <view class="input-label">
          <text class="label-icon">📱</text>
          <text class="label-text">手机号码</text>
        </view>
        <input class="contact-input" 
               type="number"
               placeholder="请输入您的手机号码"
               value="{{feedbackData.phone}}"
               bindinput="onPhoneInput"
               maxlength="11" />
      </view>
      <view class="input-group">
        <view class="input-label">
          <text class="label-icon">📧</text>
          <text class="label-text">邮箱地址</text>
        </view>
        <input class="contact-input" 
               placeholder="请输入您的邮箱地址"
               value="{{feedbackData.email}}"
               bindinput="onEmailInput" />
      </view>
    </view>
  </view>

  <!-- 上传图片 -->
  <view class="upload-section">
    <view class="section-header">
      <text class="section-title">上传图片</text>
      <text class="optional-mark">（选填，最多3张）</text>
    </view>
    <view class="image-upload">
      <view class="image-list">
        <view class="image-item" wx:for="{{feedbackData.images}}" wx:key="*this">
          <image class="uploaded-image" src="{{item}}" mode="aspectFill" bindtap="onPreviewImage" data-url="{{item}}" />
          <view class="delete-btn" bindtap="onDeleteImage" data-index="{{index}}">×</view>
        </view>
        <view class="upload-btn" wx:if="{{feedbackData.images.length < 3}}" bindtap="onChooseImage">
          <text class="upload-icon">+</text>
          <text class="upload-text">添加图片</text>
        </view>
      </view>
      <view class="upload-tips">
        <text class="tips-text">• 支持jpg、png格式，单张不超过5MB</text>
        <text class="tips-text">• 建议上传问题截图或相关照片</text>
      </view>
    </view>
  </view>

  <!-- 系统信息 -->
  <view class="system-info-section">
    <view class="section-header">
      <text class="section-title">系统信息</text>
      <switch class="info-switch" 
              checked="{{feedbackData.includeSystemInfo}}" 
              bindchange="onSystemInfoChange"
              color="#007AFF" />
    </view>
    <view class="system-info" wx:if="{{feedbackData.includeSystemInfo}}">
      <view class="info-item">
        <text class="info-label">设备型号：</text>
        <text class="info-value">{{systemInfo.model}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">系统版本：</text>
        <text class="info-value">{{systemInfo.system}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">应用版本：</text>
        <text class="info-value">{{systemInfo.appVersion}}</text>
      </view>
    </view>
    <view class="system-tips" wx:if="{{feedbackData.includeSystemInfo}}">
      <text class="tips-text">包含系统信息有助于我们更好地定位和解决问题</text>
    </view>
  </view>

  <!-- 历史反馈 -->
  <view class="history-section" wx:if="{{historyFeedbacks.length > 0}}">
    <view class="section-header">
      <text class="section-title">我的反馈</text>
      <text class="view-all" bindtap="onViewAllHistory">查看全部</text>
    </view>
    <view class="history-list">
      <view class="history-item" wx:for="{{historyFeedbacks.slice(0, 3)}}" wx:key="id" bindtap="onViewHistory" data-id="{{item.id}}">
        <view class="history-content">
          <text class="history-type">{{item.typeName}}</text>
          <text class="history-desc">{{item.description}}</text>
          <text class="history-time">{{item.createTime}}</text>
        </view>
        <view class="history-status status-{{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? '' : 'disabled'}}" 
            bindtap="onSubmitFeedback"
            disabled="{{!canSubmit}}">
      <text class="submit-text">{{submitting ? '提交中...' : '提交反馈'}}</text>
    </button>
    <view class="submit-tips">
      <text class="tips-text">我们会在24小时内处理您的反馈</text>
    </view>
  </view>
</view>

<!-- 成功提示弹窗 -->
<modal wx:if="{{showSuccessModal}}" class="success-modal">
  <view class="modal-overlay" bindtap="onCloseSuccessModal"></view>
  <view class="modal-content">
    <view class="success-icon">✅</view>
    <text class="success-title">反馈提交成功</text>
    <text class="success-desc">感谢您的反馈，我们会尽快处理并回复您</text>
    <view class="success-info">
      <text class="info-label">反馈编号：</text>
      <text class="info-value">{{feedbackId}}</text>
    </view>
    <button class="success-btn" bindtap="onCloseSuccessModal">确定</button>
  </view>
</modal>