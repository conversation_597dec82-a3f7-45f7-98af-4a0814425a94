<!-- pages/orders/orders.wxml - 重新设计的订单列表页面 -->
<view class="orders-page">
  <!-- 状态筛选标签 -->
  <view class="status-tabs">
    <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="tabs-container">
        <view wx:for="{{statusTabs}}" wx:key="status" 
              class="tab-item {{currentStatus === item.status ? 'active' : ''}}" 
              bindtap="onStatusChange" 
              data-status="{{item.status}}">
          <text class="tab-text">{{item.name}}</text>
          <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
        </view>
      </view>
    </scroll-view>
  </view>
  
  <!-- 订单列表 -->
  <scroll-view class="orders-list" scroll-y="true" enable-back-to-top="true">
    <block wx:for="{{filteredOrders}}" wx:key="id">
      <view class="order-card">
        <!-- 订单头部信息 -->
        <view class="order-header" bindtap="onOrderDetail" data-id="{{item.id}}">
          <view class="order-info">
            <text class="order-number">{{item.orderNumber}}</text>
            <text class="order-time">{{item.createTime}}</text>
          </view>
          <view class="order-status status-{{item.status}}">
            <text class="status-dot"></text>
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>
        
        <!-- 商品信息 -->
        <view class="goods-section" bindtap="onOrderDetail" data-id="{{item.id}}">
          <view class="shop-info">
            <text class="shop-icon">🏪</text>
            <text class="shop-name">智慧养鹅官方旗舰店</text>
          </view>
          
          <view class="goods-list">
            <block wx:for="{{item.goods}}" wx:key="id" wx:for-item="goods" wx:for-index="goodsIndex">
              <view class="goods-item" wx:if="{{goodsIndex < 2}}">
                <image class="goods-image" src="{{goods.image}}" mode="aspectFill" lazy-load="true"></image>
                <view class="goods-detail">
                  <text class="goods-name">{{goods.name}}</text>
                  <view class="goods-meta">
                    <text class="goods-price">¥{{goods.price}}</text>
                    <text class="goods-qty">×{{goods.quantity}}</text>
                  </view>
                </view>
              </view>
            </block>
            <view class="more-goods" wx:if="{{item.goods.length > 2}}">
              <text class="more-text">还有{{item.goods.length - 2}}件商品</text>
            </view>
          </view>
        </view>
        
        <!-- 订单金额 -->
        <view class="order-amount" bindtap="onOrderDetail" data-id="{{item.id}}">
          <text class="amount-label">共{{item.totalQuantity}}件商品，实付</text>
          <text class="amount-value">¥{{item.totalPrice}}</text>
        </view>
        
        <!-- 操作按钮 -->
        <view class="order-actions">
          <!-- 待付款 -->
          <block wx:if="{{item.status === 'pending'}}">
            <button class="action-btn secondary" bindtap="onCancelOrder" data-id="{{item.id}}">取消订单</button>
            <button class="action-btn primary" bindtap="onPayOrder" data-id="{{item.id}}">立即付款</button>
          </block>
          
          <!-- 待发货 -->
          <block wx:elif="{{item.status === 'paid'}}">
            <button class="action-btn secondary" bindtap="onContactService" data-id="{{item.id}}">联系客服</button>
            <button class="action-btn secondary" bindtap="onOrderDetail" data-id="{{item.id}}">查看详情</button>
          </block>
          
          <!-- 待收货 -->
          <block wx:elif="{{item.status === 'shipped'}}">
            <button class="action-btn secondary" bindtap="onTrackOrder" data-id="{{item.id}}">查看物流</button>
            <button class="action-btn primary" bindtap="onConfirmOrder" data-id="{{item.id}}">确认收货</button>
          </block>
          
          <!-- 已完成 -->
          <block wx:elif="{{item.status === 'completed'}}">
            <button class="action-btn secondary" bindtap="onReviewOrder" data-id="{{item.id}}">评价</button>
            <button class="action-btn primary" bindtap="onRebuyOrder" data-id="{{item.id}}">再次购买</button>
          </block>
          
          <!-- 已取消 -->
          <block wx:elif="{{item.status === 'cancelled'}}">
            <button class="action-btn secondary" bindtap="onDeleteOrder" data-id="{{item.id}}">删除订单</button>
            <button class="action-btn primary" bindtap="onRebuyOrder" data-id="{{item.id}}">再次购买</button>
          </block>
        </view>
      </view>
    </block>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{filteredOrders.length > 0}}">
      <text class="load-text">— 已显示全部订单 —</text>
    </view>
  </scroll-view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredOrders.length === 0}}">
    <view class="empty-icon">📦</view>
    <text class="empty-title">暂无相关订单</text>
    <text class="empty-desc">快去商城选购心仪的商品吧</text>
    <button class="go-shop-btn" bindtap="onGoShop">
      <text class="btn-text">去购物</text>
      <text class="btn-icon">🛍️</text>
    </button>
  </view>
</view>

<!-- 评价模态框 -->
<view class="rating-modal" wx:if="{{showRatingModal}}">
  <view class="modal-mask" bindtap="onCloseRatingModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">商品评价</text>
      <text class="modal-close" bindtap="onCloseRatingModal">×</text>
    </view>
    <view class="modal-body">
      <view class="rating-section">
        <text class="rating-label">评分</text>
        <view class="rating-stars">
          <text wx:for="{{5}}" wx:key="*this" 
                class="star {{index < ratingData.rating ? 'active' : ''}}" 
                bindtap="onRatingChange" 
                data-rating="{{index + 1}}">★</text>
        </view>
      </view>
      <view class="comment-section">
        <text class="comment-label">评价内容</text>
        <textarea class="comment-input" 
                  placeholder="请输入您的评价" 
                  value="{{ratingData.comment}}" 
                  bindinput="onCommentInput" 
                  maxlength="200"></textarea>
      </view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onCloseRatingModal">取消</button>
      <button class="submit-btn" bindtap="onSubmitRating">提交评价</button>
    </view>
  </view>
</view>