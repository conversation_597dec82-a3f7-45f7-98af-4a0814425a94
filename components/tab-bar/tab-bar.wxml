<!-- components/tab-bar/tab-bar.wxml -->
<view class="tab-bar {{containerClass}}">
  <view 
    wx:for="{{tabs}}" 
    wx:key="id" 
    class="tab-item {{tabClass}} {{current === index ? 'active ' + activeTabClass : ''}}"
    data-index="{{index}}"
    bindtap="onTabTap"
  >
    <text class="tab-text">{{item.name || item.label || item.title}}</text>
    <view 
      wx:if="{{showIndicator && current === index}}" 
      class="tab-indicator"
    ></view>
  </view>
</view>