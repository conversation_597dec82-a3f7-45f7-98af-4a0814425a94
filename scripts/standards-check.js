#!/usr/bin/env node

/**
 * 智慧养鹅SAAS平台开发标准检查工具
 * 验证项目是否遵循.project-standards.json中定义的开发规范
 */

const fs = require('fs');
const path = require('path');

class StandardsChecker {
  constructor() {
    this.projectRoot = process.cwd();
    this.issues = [];
    this.warnings = [];
    this.standards = null;
    
    console.log('📏 智慧养鹅SAAS平台开发标准检查开始...\n');
  }

  /**
   * 运行标准检查
   */
  async runCheck() {
    try {
      // 加载项目标准配置
      await this.loadStandards();
      
      // 检查项目结构
      await this.checkProjectStructure();
      
      // 检查命名规范
      await this.checkNamingConventions();
      
      // 检查必要文件
      await this.checkRequiredFiles();
      
      // 检查代码质量配置
      await this.checkCodeQualityConfig();
      
      // 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 标准检查过程中出现错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 加载项目标准配置
   */
  async loadStandards() {
    const standardsPath = path.join(this.projectRoot, '.project-standards.json');
    
    if (!fs.existsSync(standardsPath)) {
      this.issues.push({
        type: 'MISSING_STANDARDS_FILE',
        severity: 'ERROR',
        message: '缺少项目标准配置文件 .project-standards.json'
      });
      return;
    }
    
    try {
      const content = fs.readFileSync(standardsPath, 'utf8');
      this.standards = JSON.parse(content);
      console.log('✅ 项目标准配置加载成功');
    } catch (error) {
      this.issues.push({
        type: 'INVALID_STANDARDS_FILE',
        severity: 'ERROR',
        message: `项目标准配置文件格式错误: ${error.message}`
      });
    }
  }

  /**
   * 检查项目结构
   */
  async checkProjectStructure() {
    console.log('📁 检查项目结构...');
    
    const requiredDirectories = [
      'pages',
      'components', 
      'utils',
      'backend',
      'database',
      'docs',
      'scripts'
    ];
    
    for (const dir of requiredDirectories) {
      const dirPath = path.join(this.projectRoot, dir);
      if (!fs.existsSync(dirPath)) {
        this.warnings.push({
          type: 'MISSING_DIRECTORY',
          severity: 'WARNING',
          message: `建议的项目目录不存在: ${dir}`
        });
      }
    }
    
    console.log('   ✅ 项目结构检查完成');
  }

  /**
   * 检查命名规范
   */
  async checkNamingConventions() {
    console.log('📝 检查命名规范...');
    
    // 检查页面命名规范
    const pagesDir = path.join(this.projectRoot, 'pages');
    if (fs.existsSync(pagesDir)) {
      this.checkPageNaming(pagesDir);
    }
    
    // 检查组件命名规范
    const componentsDir = path.join(this.projectRoot, 'components');
    if (fs.existsSync(componentsDir)) {
      this.checkComponentNaming(componentsDir);
    }
    
    console.log('   ✅ 命名规范检查完成');
  }

  /**
   * 检查页面命名
   */
  checkPageNaming(pagesDir) {
    const items = fs.readdirSync(pagesDir);
    
    for (const item of items) {
      const itemPath = path.join(pagesDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // 检查页面目录命名是否为kebab-case
        if (!this.isKebabCase(item)) {
          this.warnings.push({
            type: 'PAGE_NAMING_CONVENTION',
            severity: 'WARNING',
            message: `页面目录应使用kebab-case命名: ${item}`
          });
        }
        
        // 递归检查子目录
        this.checkPageNaming(itemPath);
      }
    }
  }

  /**
   * 检查组件命名
   */
  checkComponentNaming(componentsDir) {
    const items = fs.readdirSync(componentsDir);
    
    for (const item of items) {
      const itemPath = path.join(componentsDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // 检查组件目录命名
        if (!this.isKebabCase(item) && !item.startsWith('c-')) {
          this.warnings.push({
            type: 'COMPONENT_NAMING_CONVENTION',
            severity: 'WARNING',
            message: `组件目录建议使用kebab-case或c-前缀命名: ${item}`
          });
        }
        
        // 递归检查子目录
        this.checkComponentNaming(itemPath);
      }
    }
  }

  /**
   * 检查必要文件
   */
  async checkRequiredFiles() {
    console.log('📄 检查必要文件...');
    
    const requiredFiles = [
      {
        path: 'README.md',
        type: '项目说明文档'
      },
      {
        path: 'app.json',
        type: '小程序配置文件'
      },
      {
        path: 'app.js',
        type: '小程序入口文件'
      },
      {
        path: 'project.config.json',
        type: '小程序项目配置'
      },
      {
        path: 'package.json',
        type: 'Node.js包配置'
      },
      {
        path: 'docs/development-standards.md',
        type: '开发规范文档'
      }
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(this.projectRoot, file.path);
      if (!fs.existsSync(filePath)) {
        this.warnings.push({
          type: 'MISSING_REQUIRED_FILE',
          severity: 'WARNING',
          message: `建议的${file.type}不存在: ${file.path}`
        });
      }
    }
    
    console.log('   ✅ 必要文件检查完成');
  }

  /**
   * 检查代码质量配置
   */
  async checkCodeQualityConfig() {
    console.log('🔧 检查代码质量配置...');
    
    // 检查ESLint配置
    const eslintConfigs = ['.eslintrc.js', '.eslintrc.json', '.eslintrc.yml'];
    const hasEslintConfig = eslintConfigs.some(config => 
      fs.existsSync(path.join(this.projectRoot, config))
    );
    
    if (!hasEslintConfig) {
      this.warnings.push({
        type: 'MISSING_ESLINT_CONFIG',
        severity: 'WARNING',
        message: '建议配置ESLint进行代码质量检查'
      });
    }
    
    // 检查Prettier配置
    const prettierConfigs = ['.prettierrc', '.prettierrc.json', '.prettierrc.yml'];
    const hasPrettierConfig = prettierConfigs.some(config => 
      fs.existsSync(path.join(this.projectRoot, config))
    );
    
    if (!hasPrettierConfig) {
      this.warnings.push({
        type: 'MISSING_PRETTIER_CONFIG',
        severity: 'WARNING',
        message: '建议配置Prettier进行代码格式化'
      });
    }
    
    // 检查VS Code配置
    const vscodeConfigPath = path.join(this.projectRoot, '.vscode/settings.json');
    if (!fs.existsSync(vscodeConfigPath)) {
      this.warnings.push({
        type: 'MISSING_VSCODE_CONFIG',
        severity: 'WARNING',
        message: '建议配置VS Code工作区设置'
      });
    }
    
    console.log('   ✅ 代码质量配置检查完成');
  }

  /**
   * 检查是否为kebab-case命名
   */
  isKebabCase(str) {
    return /^[a-z0-9]+(-[a-z0-9]+)*$/.test(str);
  }

  /**
   * 检查是否为camelCase命名
   */
  isCamelCase(str) {
    return /^[a-z][a-zA-Z0-9]*$/.test(str);
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('\n📊 开发标准检查报告');
    console.log('='.repeat(50));
    
    if (this.issues.length === 0 && this.warnings.length === 0) {
      console.log('🎉 恭喜！项目完全符合开发标准！');
      return;
    }
    
    if (this.issues.length > 0) {
      console.log(`\n❌ 发现 ${this.issues.length} 个问题:`);
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.type}] ${issue.message}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log(`\n⚠️  发现 ${this.warnings.length} 个建议:`);
      this.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. [${warning.type}] ${warning.message}`);
      });
    }
    
    console.log('\n💡 改进建议:');
    console.log('1. 参考 docs/development-standards.md 了解详细规范');
    console.log('2. 使用 npm run context7:query 查询最佳实践');
    console.log('3. 运行 npm run format 进行代码格式化');
    console.log('4. 运行 npm run lint 进行代码检查');
    
    if (this.standards && this.standards.standards) {
      console.log('\n📋 项目标准要求:');
      const standards = this.standards.standards;
      if (standards.enforceContext7Query) {
        console.log('   ✓ 强制要求查询Context7最佳实践');
      }
      if (standards.requireSequentialThinking) {
        console.log('   ✓ 复杂问题需要使用Sequential Thinking');
      }
      if (standards.enforceConsistency) {
        console.log('   ✓ 强制执行四端一致性检查');
      }
      if (standards.mandatoryBestPractices) {
        console.log('   ✓ 所有开发必须遵循最佳实践');
      }
    }
    
    // 如果有严重问题，返回错误码
    if (this.issues.length > 0) {
      console.log('\n❌ 检查失败，请修复上述问题');
      process.exit(1);
    } else {
      console.log('\n✅ 标准检查通过！');
    }
  }
}

// 运行检查器
if (require.main === module) {
  const checker = new StandardsChecker();
  checker.runCheck().catch(error => {
    console.error('检查过程中出现未处理的错误:', error);
    process.exit(1);
  });
}

module.exports = StandardsChecker;