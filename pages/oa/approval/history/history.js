// pages/oa/approval/history/history.js

const { PERMISSIONS, PermissionMixin } = require('../../../../utils/oa-permissions');
const request = require('../../../../utils/request.js');
const { API } = require('../../../../constants/index.js');

Page({
  // 混入权限检查功能
  ...PermissionMixin,

  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    showFilters: false,
    showDetailModal: false,

    // 筛选条件
    filters: {
      status: 'all',
      businessType: 'all',
      timeRange: 'all',
      keyword: ''
    },

    // 筛选选项
    statusOptions: [
      { value: 'all', label: '全部状态' },
      { value: 'approved', label: '已通过' },
      { value: 'rejected', label: '已拒绝' }
    ],

    businessTypeOptions: [
      { value: 'all', label: '全部类型' },
      { value: 'reimbursement', label: '报销申请' },
      { value: 'leave', label: '请假申请' },
      { value: 'purchase', label: '采购申请' },
      { value: 'other', label: '其他申请' }
    ],

    timeRangeOptions: [
      { value: 'all', label: '全部时间' },
      { value: 'today', label: '今天' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' }
    ],

    // 审批历史数据
    approvals: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      hasMore: true
    },

    // 统计数据
    statistics: {
      total: 0,
      approved: 0,
      rejected: 0,
      thisMonth: 0,
      approvalRate: 0
    },

    // 选中的审批详情
    selectedApproval: null
  },

  onLoad(options) {
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('审批历史页面加载', options); } catch(_) {}
    this.initPage();
  },

  onShow() {
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadMoreHistory();
    }
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查审批权限
    const hasApprovalPermission = this.hasAnyPermission([
      PERMISSIONS.APPROVE_PURCHASE,
      PERMISSIONS.APPROVE_REIMBURSEMENT,
      PERMISSIONS.APPROVE_LEAVE
    ]);
    
    if (!hasApprovalPermission) {
      wx.showModal({
        title: '权限不足',
        content: '您没有审批权限，请联系管理员',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限验证通过，加载数据
    this.loadInitialData();
  },

  /**
   * 初始化页面
   */
  initPage() {
    try { const logger = require('../../../../utils/logger.js'); logger.debug && logger.debug('初始化审批历史页面'); } catch(_) {}
    // 权限检查会自动触发 onPermissionReady
  },

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    await this.loadStatistics();
    await this.loadHistory(true);
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      await Promise.all([
        this.loadStatistics(),
        this.loadHistory(true)
      ]);
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('刷新数据失败', error); } catch(_) {}
    } finally {
      this.setData({ refreshing: false });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const res = await request.get(API.ENDPOINTS.OA.APPROVALS.HISTORY, { page: 1, limit: 50 });
      if (res && res.success !== false) {
        const payload = res.data || res;
        const list = Array.isArray(payload.list) ? payload.list : (Array.isArray(payload) ? payload : []);
        const total = list.length;
        const approved = list.filter(i => i.status === 'approved').length;
        const rejected = list.filter(i => i.status === 'rejected').length;
        const monthStart = new Date();
        monthStart.setDate(1);
        const thisMonth = list.filter(i => new Date(i.approvalTime || i.created_at) >= monthStart).length;
        const approvalRate = total ? Math.round((approved / total) * 100) : 0;
        this.setData({ statistics: { total, approved, rejected, thisMonth, approvalRate } });
      }
    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载统计数据失败', error); } catch(_) {}
    }
  },

  /**
   * 加载审批历史
   */
  async loadHistory(refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      if (refresh) {
        this.setData({
          'pagination.page': 1,
          'pagination.hasMore': true
        });
      }

      const { pagination, filters } = this.data;
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        status: filters.status,
        businessType: filters.businessType,
        timeRange: filters.timeRange,
        keyword: filters.keyword
      };

      const res = await request.get(API.ENDPOINTS.OA.APPROVALS.HISTORY, params);
      if (res && res.success !== false) {
        const payload = res.data || res;
        const list = Array.isArray(payload.list) ? payload.list : (Array.isArray(payload) ? payload : []);
        const filteredHistory = this.filterHistory(list);
        const newPagination = payload.pagination || { page: pagination.page, pages: list.length < pagination.limit ? pagination.page : pagination.page + 1 };

        this.setData({
          approvals: refresh ? filteredHistory : [...this.data.approvals, ...filteredHistory],
          'pagination.page': newPagination.page,
          'pagination.total': newPagination.total || filteredHistory.length,
          'pagination.hasMore': newPagination.page < (newPagination.pages || newPagination.page)
        });
      }

    } catch (error) {
      try { const logger = require('../../../../utils/logger.js'); logger.error && logger.error('加载审批历史失败', error); } catch(_) {}
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载更多历史记录
   */
  loadMoreHistory() {
    const currentPage = this.data.pagination.page;
    this.setData({
      'pagination.page': currentPage + 1
    });
    this.loadHistory(false);
  },

  /**
   * 过滤历史数据
   */
  filterHistory(history) {
    const { filters } = this.data;
    
    return history.filter(item => {
      // 状态筛选
      if (filters.status !== 'all' && item.status !== filters.status) {
        return false;
      }

      // 业务类型筛选
      if (filters.businessType !== 'all' && item.business_type !== filters.businessType) {
        return false;
      }

      // 时间范围筛选
      if (filters.timeRange !== 'all') {
        const approvalDate = new Date(item.approvalTime);
        const now = new Date();
        
        switch (filters.timeRange) {
          case 'today':
            if (approvalDate.toDateString() !== now.toDateString()) {
              return false;
            }
            break;
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            if (approvalDate < weekAgo) {
              return false;
            }
            break;
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            if (approvalDate < monthAgo) {
              return false;
            }
            break;
          case 'quarter':
            const quarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            if (approvalDate < quarterAgo) {
              return false;
            }
            break;
        }
      }

      // 关键词搜索
      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase();
        if (!item.title.toLowerCase().includes(keyword) &&
            !item.applicant_name.toLowerCase().includes(keyword)) {
          return false;
        }
      }

      return true;
    });
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const { status } = e.currentTarget.dataset;
    this.setData({
      'filters.status': status
    });
    this.loadHistory(true);
  },

  /**
   * 时间筛选
   */
  onTimeFilter(e) {
    const { time } = e.currentTarget.dataset;
    this.setData({
      'filters.timeRange': time
    });
    this.loadHistory(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      'filters.keyword': keyword
    });
    
    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadHistory(true);
    }, 500);
  },

  /**
   * 搜索确认
   */
  onSearchConfirm() {
    this.loadHistory(true);
  },

  /**
   * 清除搜索
   */
  clearSearch() {
    this.setData({
      'filters.keyword': ''
    });
    this.loadHistory(true);
  },

  /**
   * 切换筛选面板
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 状态变更
   */
  onStatusChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.status': value
    });
  },

  /**
   * 业务类型变更
   */
  onBusinessTypeChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.businessType': value
    });
  },

  /**
   * 时间范围变更
   */
  onTimeRangeChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.timeRange': value
    });
  },

  /**
   * 重置筛选
   */
  resetFilters() {
    this.setData({
      filters: {
        status: 'all',
        businessType: 'all',
        timeRange: 'all',
        keyword: ''
      }
    });
  },

  /**
   * 应用筛选
   */
  applyFilters() {
    this.setData({
      showFilters: false
    });
    this.loadHistory(true);
  },

  /**
   * 历史记录点击
   */
  onHistoryTap(e) {
    const { approval } = e.currentTarget.dataset;
    
    // 添加触摸反馈
    wx.vibrateShort();
    
    this.setData({
      selectedApproval: approval,
      showDetailModal: true
    });
  },

  /**
   * 隐藏详情弹窗
   */
  hideDetailModal() {
    this.setData({
      showDetailModal: false,
      selectedApproval: null
    });
  },

  /**
   * 查看完整详情
   */
  viewFullDetail() {
    const { selectedApproval } = this.data;
    
    if (!selectedApproval) return;
    
    // 跳转到详情页面
    let url = '';
    switch (selectedApproval.business_type) {
      case 'reimbursement':
        url = `/pages/oa/reimbursement/detail/detail?id=${selectedApproval.id}`;
        break;
      case 'leave':
        url = `/pages/oa/leave/detail/detail?id=${selectedApproval.id}`;
        break;
      case 'purchase':
        url = `/pages/oa/purchase/detail/detail?id=${selectedApproval.id}`;
        break;
      default:
        wx.showToast({
          title: '暂不支持查看此类型详情',
          icon: 'none'
        });
        return;
    }
    
    this.hideDetailModal();
    
    wx.navigateTo({
      url,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  }
});