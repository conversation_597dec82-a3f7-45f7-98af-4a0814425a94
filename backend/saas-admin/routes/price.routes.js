const express = require("express");
const router = express.Router();

// 价格趋势数据
router.get("/trends", async (req, res) => {
  try {
    const { breed_name, timeRange } = req.query;

    // 模拟数据 - 实际应该从数据库获取
    const trends = {
      breedName: breed_name || "白鹅",
      breedType: "adult",
      timeRange: timeRange || "30d",
      data: [
        { date: "2024-01-01", price: 18.5 },
        { date: "2024-01-05", price: 19.2 },
        { date: "2024-01-10", price: 18.8 },
        { date: "2024-01-15", price: 19.5 },
        { date: "2024-01-20", price: 20.1 },
      ],
    };

    res.json({
      success: true,
      message: "获取价格趋势数据成功",
      data: trends,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取价格趋势数据失败",
      error: error.message,
    });
  }
});

// 价格统计信息
router.get("/statistics", async (req, res) => {
  try {
    const { breed_name } = req.query;

    // 模拟数据 - 实际应该从数据库获取
    const statistics = {
      breedName: breed_name || "白鹅",
      currentPrice: 20.1,
      previousPrice: 19.5,
      change: 0.6,
      changePercent: 3.08,
      highPrice: 21.5,
      lowPrice: 18.2,
      averagePrice: 19.6,
    };

    res.json({
      success: true,
      message: "获取价格统计信息成功",
      data: statistics,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取价格统计信息失败",
      error: error.message,
    });
  }
});

// 相关品种价格
router.get("/related", async (req, res) => {
  try {
    const { breed_name } = req.query;

    // 模拟数据 - 实际应该从数据库获取
    const related = [
      { breedName: "灰鹅", currentPrice: 17.8, change: -0.2 },
      { breedName: "棕鹅", currentPrice: 22.5, change: 0.5 },
    ];

    res.json({
      success: true,
      message: "获取相关品种价格成功",
      data: related,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取相关品种价格失败",
      error: error.message,
    });
  }
});

// 价格提醒订阅
router.post("/subscribe", async (req, res) => {
  try {
    const { breedName, targetPrice, direction, contact } = req.body;

    // 模拟创建订阅 - 实际应该保存到数据库
    const subscription = {
      id: Math.floor(Math.random() * 1000) + 1,
      breedName,
      targetPrice,
      direction,
      contact,
      status: "active",
      createdAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "订阅价格提醒成功",
      data: subscription,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "订阅价格提醒失败",
      error: error.message,
    });
  }
});

module.exports = router;
