// pages/shop/reviews.js
Page({
  data: {
    goodsId: null,
    goods: {},
    reviews: [],
    currentFilter: 'all',
    filterOptions: [
      { key: 'all', label: '全部' },
      { key: '5', label: '5星' },
      { key: '4', label: '4星' },
      { key: '3', label: '3星' },
      { key: 'withImages', label: '有图' }
    ],
    ratingStats: [
      { star: 5, count: 89, percentage: 57 },
      { star: 4, count: 45, percentage: 29 },
      { star: 3, count: 15, percentage: 10 },
      { star: 2, count: 5, percentage: 3 },
      { star: 1, count: 2, percentage: 1 }
    ],
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    loadingMore: false
  },

  onLoad: function (options) {
    const goodsId = options.goodsId;
    if (goodsId) {
      this.setData({ goodsId });
      this.loadGoodsInfo();
      this.loadReviews();
    }
  },

  // 加载商品信息
  loadGoodsInfo: function() {
    // 模拟数据，实际应该从API获取
    const goods = {
      id: this.data.goodsId,
      name: '优质鹅饲料',
      rating: 4.8,
      reviewCount: 156
    };
    
    this.setData({ goods });
  },

  // 加载评价列表
  loadReviews: function(isLoadMore = false) {
    if (this.data.loading || (isLoadMore && this.data.loadingMore)) {
      return;
    }

    this.setData({
      loading: !isLoadMore,
      loadingMore: isLoadMore
    });

    // 模拟API请求
    setTimeout(() => {
      const mockReviews = this.generateMockReviews(this.data.page, this.data.pageSize);
      
      const newReviews = isLoadMore 
        ? [...this.data.reviews, ...mockReviews]
        : mockReviews;

      this.setData({
        reviews: newReviews,
        page: this.data.page + 1,
        hasMore: mockReviews.length >= this.data.pageSize,
        loading: false,
        loadingMore: false
      });
    }, 1000);
  },

  // 生成模拟评价数据
  generateMockReviews: function(page, pageSize) {
    const reviews = [];
    const startIndex = (page - 1) * pageSize;
    
    for (let i = 0; i < pageSize && startIndex + i < 50; i++) {
      const index = startIndex + i + 1;
      reviews.push({
        id: index,
        username: `用户${index}`,
        avatar: '/images/default_avatar.png',
        rating: Math.floor(Math.random() * 2) + 4, // 4-5星
        date: this.getRandomDate(),
        content: this.getRandomReviewContent(),
        images: Math.random() > 0.6 ? ['/images/icons/goods1.png', '/images/icons/goods2.png'] : [],
        sku: Math.random() > 0.5 ? '1公斤 普通型' : '5公斤 增强型',
        helpful: Math.floor(Math.random() * 20)
      });
    }
    
    return reviews;
  },

  // 获取随机日期
  getRandomDate: function() {
    const now = new Date();
    const randomDays = Math.floor(Math.random() * 30);
    const date = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  // 获取随机评价内容
  getRandomReviewContent: function() {
    const contents = [
      '非常好的饲料，我家的鹅吃了长得很快，营养丰富，价格也实惠。',
      '使用了一个月，效果明显，鹅类的食欲和活力都很好。',
      '质量不错，包装严实，没有破损，鹅很喜欢吃。',
      '老客户了，一直在用这个牌子，品质稳定，值得信赖。',
      '物流很快，客服态度也很好，产品质量符合预期。',
      '性价比很高，比其他品牌便宜不少，效果也不错。',
      '朋友推荐的，用了确实不错，鹅的毛色都变好了。',
      '专业的饲料，配方科学，我家鹅群整体健康状况有明显改善。'
    ];
    
    return contents[Math.floor(Math.random() * contents.length)];
  },

  // 筛选变更
  onFilterChange: function(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      currentFilter: filter,
      page: 1,
      reviews: [],
      hasMore: true
    });
    this.loadReviews();
  },

  // 加载更多
  onLoadMore: function() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadReviews(true);
    }
  },

  // 预览图片
  onPreviewImage: function(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // 点赞评价
  onHelpfulTap: function(e) {
    const reviewId = e.currentTarget.dataset.id;
    const reviews = this.data.reviews.map(review => {
      if (review.id === reviewId) {
        return {
          ...review,
          helpful: (review.helpful || 0) + 1
        };
      }
      return review;
    });
    
    this.setData({ reviews });
    
    wx.showToast({
      title: '谢谢您的支持',
      icon: 'success',
      duration: 1000
    });
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: `${this.data.goods.name} - 商品评价`,
      path: `/pages/shop/reviews?goodsId=${this.data.goodsId}`
    };
  }
});