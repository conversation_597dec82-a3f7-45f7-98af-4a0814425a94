<!-- components/trend-chart/trend-chart.wxml -->
<view class="trend-chart-container square-indicators-v2">
  <!-- 图表头部 -->
  <view class="chart-header">
    <view class="chart-title-section">
      <text class="chart-title">{{title || '数据趋势'}}</text>
      <text class="chart-subtitle" wx:if="{{subtitle}}">{{subtitle}}</text>
    </view>
    
    <!-- 时间筛选器 -->
    <view class="time-filter time-filter-fixed" wx:if="{{showTimeFilter}}">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-options filter-options-fixed">
          <block wx:for="{{timeRanges}}" wx:key="value">
            <text
              class="filter-option filter-option-fixed {{activeTimeRange === item.value ? 'active' : ''}}"
              data-value="{{item.value}}"
              bindtap="onTimeRangeChange"
            >
              {{item.label}}
            </text>
          </block>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 图例 -->
  <view class="chart-legend" wx:if="{{legendData && legendData.length > 0}}">
    <scroll-view class="legend-scroll" scroll-x>
      <view class="legend-list">
        <block wx:for="{{legendData}}" wx:key="key">
          <view class="legend-item">
            <view class="legend-color" style="background-color: {{item.color}};"></view>
            <text class="legend-text">{{item.label}}</text>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>

  <!-- 图表主体 -->
  <view class="chart-body">
    <view class="chart-content" wx:if="{{chartData && chartData.length > 0}}">
      <!-- 加载状态 -->
      <view class="loading-overlay" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- Canvas图表 -->
      <canvas 
        type="2d"
        class="trend-canvas" 
        id="trendChart-{{componentId}}"
        style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;"
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove"
        bindtouchend="onTouchEnd"
      ></canvas>

      <!-- 数据点提示 -->
      <view 
        class="tooltip {{showTooltip ? 'show' : ''}}" 
        style="left: {{tooltipX}}px; top: {{tooltipY}}px;"
        wx:if="{{tooltipData}}"
      >
        <view class="tooltip-content">
          <view class="tooltip-time">{{tooltipData.time}}</view>
          <view class="tooltip-values">
            <block wx:for="{{tooltipData.values}}" wx:key="key">
              <view class="tooltip-value-item">
                <view class="value-color" style="background-color: {{item.color}};"></view>
                <text class="value-label">{{item.label}}:</text>
                <text class="value-number">{{item.value}}{{item.unit || ''}}</text>
              </view>
            </block>
          </view>
        </view>
        <view class="tooltip-arrow"></view>
      </view>


    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <image class="empty-icon" src="/images/icon_chart_empty.png" mode="aspectFit"></image>
      <text class="empty-text">{{emptyText || '暂无数据'}}</text>
      <button class="refresh-btn" bindtap="onRefresh" wx:if="{{showRefresh}}">重新加载</button>
    </view>
  </view>

  <!-- 图表底部信息 -->
  <view class="chart-footer" wx:if="{{showFooter}}">
    <text class="update-time">更新时间: {{updateTime}}</text>
    <view class="chart-actions">
      <button class="action-btn" bindtap="onExport" wx:if="{{allowExport}}">
        <image class="btn-icon" src="/images/icon_export.png" mode="aspectFit"></image>
        <text>导出</text>
      </button>
      <button class="action-btn" bindtap="onFullscreen" wx:if="{{allowFullscreen}}">
        <image class="btn-icon" src="/images/icon_fullscreen.png" mode="aspectFit"></image>
        <text>全屏</text>
      </button>
    </view>
  </view>
</view>