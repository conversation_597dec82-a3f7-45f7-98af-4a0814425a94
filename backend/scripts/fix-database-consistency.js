#!/usr/bin/env node

/**
 * 智慧养鹅系统 - 数据库一致性修复工具
 * 修复Sequelize模型与数据库表结构的不匹配问题
 */

const mysql = require("mysql2/promise");
const fs = require("fs").promises;
const path = require("path");

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "zhihuiyange",
  password: process.env.DB_PASSWORD || "zhihuiyange123",
  database: process.env.DB_NAME || "zhihuiyange_local",
  multipleStatements: false,
};

async function executeSQL(connection, sql) {
  try {
    console.log(`⚡ 执行SQL: ${sql.slice(0, 100)}...`);
    const [result] = await connection.execute(sql);
    console.log(`✅ 执行成功`);
    return result;
  } catch (error) {
    console.log(`⚠️  执行失败: ${error.message}`);
    return null;
  }
}

async function fixDatabaseConsistency() {
  let connection;

  try {
    console.log("🔧 智慧养鹅系统 - 数据库一致性修复工具");
    console.log("==================================================");
    
    console.log("🔗 连接数据库...");
    connection = await mysql.createConnection(dbConfig);
    console.log("✅ 数据库连接成功");

    // 1. 检查并创建ai_configs表
    console.log("📊 处理AI配置表...");
    await executeSQL(connection, `
      CREATE TABLE IF NOT EXISTS ai_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        provider VARCHAR(50) COMMENT 'AI服务提供商',
        name VARCHAR(100) COMMENT '配置名称',
        api_key TEXT COMMENT 'API密钥',
        base_url VARCHAR(255) COMMENT '基础URL',
        models JSON COMMENT '模型配置',
        max_tokens INT DEFAULT 1000 COMMENT '最大令牌数',
        temperature DECIMAL(3,2) DEFAULT 0.7 COMMENT '温度参数',
        enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
        is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认',
        priority INT DEFAULT 0 COMMENT '优先级',
        config JSON COMMENT '其他配置',
        created_by INT COMMENT '创建者ID',
        updated_by INT COMMENT '更新者ID',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI配置表'
    `);

    // 2. 检查并创建ai_usage_stats表
    console.log("📊 处理AI使用统计表...");
    await executeSQL(connection, `
      CREATE TABLE IF NOT EXISTS ai_usage_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT COMMENT '用户ID',
        flock_id INT COMMENT '鹅群ID',
        provider VARCHAR(50) COMMENT '服务提供商',
        model VARCHAR(100) COMMENT '模型名称',
        scenario VARCHAR(50) COMMENT '使用场景',
        feature VARCHAR(50) COMMENT '使用功能',
        service_type VARCHAR(50) COMMENT '服务类型',
        usage_count INT DEFAULT 1 COMMENT '使用次数',
        request_tokens INT DEFAULT 0 COMMENT '请求令牌数',
        response_tokens INT DEFAULT 0 COMMENT '响应令牌数',
        total_tokens INT DEFAULT 0 COMMENT '总令牌数',
        cost DECIMAL(10,4) DEFAULT 0 COMMENT '成本',
        response_time INT COMMENT '响应时间',
        success TINYINT(1) DEFAULT 1 COMMENT '是否成功',
        error_message TEXT COMMENT '错误信息',
        request_data JSON COMMENT '请求数据',
        response_data JSON COMMENT '响应数据',
        ip_address VARCHAR(45) COMMENT 'IP地址',
        user_agent TEXT COMMENT '用户代理',
        session_id VARCHAR(255) COMMENT '会话ID',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用统计表'
    `);

    // 3. 检查并创建announcements表
    console.log("📊 处理公告表...");
    await executeSQL(connection, `
      CREATE TABLE IF NOT EXISTS announcements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT COMMENT '发布者ID',
        flock_id INT COMMENT '关联鹅群ID',
        title VARCHAR(200) NOT NULL COMMENT '标题',
        content TEXT NOT NULL COMMENT '内容',
        type ENUM('system', 'user', 'maintenance', 'warning') DEFAULT 'user' COMMENT '类型',
        priority INT DEFAULT 0 COMMENT '优先级',
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
        publish_time DATETIME COMMENT '发布时间',
        expire_time DATETIME COMMENT '过期时间',
        target_users JSON COMMENT '目标用户',
        attachments JSON COMMENT '附件',
        read_count INT DEFAULT 0 COMMENT '阅读次数',
        is_top TINYINT(1) DEFAULT 0 COMMENT '是否置顶',
        allow_comment TINYINT(1) DEFAULT 1 COMMENT '是否允许评论',
        created_by INT COMMENT '创建者',
        updated_by INT COMMENT '更新者',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表'
    `);

    // 4. 更新users表，添加缺失字段
    console.log("📊 更新用户表字段...");
    await executeSQL(connection, `
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS tenant_role VARCHAR(50) COMMENT '租户角色'
    `);
    
    await executeSQL(connection, `
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS last_login_at DATETIME COMMENT '最后登录时间'
    `);

    console.log("🎯 数据库一致性修复完成！");
    console.log("==================================================");
    console.log("📝 修复摘要:");
    console.log("   ✅ 创建/更新 ai_configs 表");
    console.log("   ✅ 创建/更新 ai_usage_stats 表");
    console.log("   ✅ 创建/更新 announcements 表");
    console.log("   ✅ 更新 users 表字段");
    console.log("   ✅ 数据库一致性修复完成");

  } catch (error) {
    console.error("❌ 修复过程中出现错误:", error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔒 数据库连接已关闭");
    }
  }
  
  return true;
}

async function main() {
  const success = await fixDatabaseConsistency();
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main();
}

module.exports = { fixDatabaseConsistency };