-- 创建商品表
CREATE TABLE IF NOT EXISTS products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(200) NOT NULL COMMENT '商品名称',
  description TEXT COMMENT '商品描述',
  price DECIMAL(10, 2) NOT NULL COMMENT '商品价格',
  category VARCHAR(50) NOT NULL COMMENT '商品分类',
  image VARCHAR(500) COMMENT '商品图片URL',
  stock INT DEFAULT 0 COMMENT '库存数量',
  status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active' COMMENT '商品状态',
  tags JSON COMMENT '商品标签',
  specifications JSON COMMENT '商品规格参数',
  sales_count INT DEFAULT 0 COMMENT '销售数量',
  created_by INT NOT NULL COMMENT '创建者ID',
  updated_by INT COMMENT '更新者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  
  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_name (name),
  INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商城商品表';