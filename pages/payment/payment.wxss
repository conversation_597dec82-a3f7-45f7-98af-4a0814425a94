/* pages/payment/payment.wxss */
.payment-container {
  min-height: 100vh;
  background-color: #f5f6f8;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 支付内容 */
.payment-content {
  padding: 20rpx;
}

/* 通用section样式 */
.section-header {
  padding: 30rpx 0 20rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

/* 订单信息 */
.order-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.order-info {
  margin-bottom: 30rpx;
}

.order-number,
.order-time {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.label {
  font-size: 28rpx;
  color: #666666;
}

.value {
  font-size: 28rpx;
  color: #333333;
}

/* 商品列表 */
.goods-list {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
  margin-bottom: 30rpx;
}

.goods-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.goods-item:last-child {
  margin-bottom: 0;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.goods-name {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff4d4f;
  font-weight: 600;
}

.goods-qty {
  font-size: 26rpx;
  color: #666666;
}

/* 收货地址 */
.address-info {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.address-header {
  margin-bottom: 16rpx;
}

.address-title {
  font-size: 28rpx;
  color: #666666;
}

.address-detail {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.address-name,
.address-phone {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.address-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 支付方式 */
.payment-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.payment-method-fixed {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.payment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.payment-item.selected {
  border-color: #0066CC;
  background-color: #f0f8ff;
}

.payment-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.payment-icon {
  width: 60rpx;
  height: 60rpx;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.payment-name {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.payment-desc {
  font-size: 24rpx;
  color: #666666;
}

.payment-radio {
  display: flex;
  align-items: center;
}

.radio-circle {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-circle.checked {
  border-color: #0066CC;
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  background-color: #0066CC;
  border-radius: 50%;
}

/* 支付提示 */
.payment-notice {
  background-color: #f0f8ff;
  border: 1rpx solid #e6f4ff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 10rpx;
}

.notice-text {
  font-size: 26rpx;
  color: #0066CC;
  line-height: 1.4;
}

/* 价格明细 */
.price-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.price-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-item.total {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
  margin-top: 10rpx;
}

.price-label {
  font-size: 28rpx;
  color: #666666;
}

.price-item.total .price-label {
  font-size: 30rpx;
  color: #333333;
  font-weight: 600;
}

.price-value {
  font-size: 28rpx;
  color: #333333;
}

.price-value.discount {
  color: #52c41a;
}

.price-item.total .price-value {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: 600;
}

/* 底部支付栏 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.total-label {
  font-size: 28rpx;
  color: #666666;
}

.total-amount {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: 600;
}

.pay-button {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: #ffffff;
  border: none;
  border-radius: var(--radius-xl);
  padding: 24rpx 60rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.3);
}

.pay-button.loading {
  opacity: 0.7;
}

.pay-button::after {
  border: none;
}

/* 返回按钮 */
.back-button {
  position: fixed;
  top: 120rpx;
  right: 30rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  z-index: 100;
}