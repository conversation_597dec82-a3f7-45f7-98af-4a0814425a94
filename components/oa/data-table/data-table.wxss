/* components/oa/data-table/data-table.wxss */

.data-table {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
}

.data-table.bordered {
  border: 1rpx solid #E5E5E7;
}

/* 加载状态 */
.table-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E5E7;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 表格容器 */
.table-container {
  width: 100%;
  overflow-x: auto;
}

/* 表格行 */
.table-row {
  display: flex;
  align-items: center;
  min-height: 88rpx;
}

.header-row {
  background-color: #F2F2F7;
  border-bottom: 1rpx solid #E5E5E7;
}

.data-row {
  border-bottom: 1rpx solid #F2F2F7;
  transition: background-color 0.2s ease;
}

.data-row:hover {
  background-color: #F8F8F8;
}

.data-row.selected {
  background-color: #E3F2FD;
}

.data-row:last-child {
  border-bottom: none;
}

/* 斑马纹样式 */
.data-table.striped .data-row:nth-child(even) {
  background-color: #FAFAFA;
}

.data-table.striped .data-row:nth-child(even):hover {
  background-color: #F0F0F0;
}

/* 表格单元格 */
.table-cell {
  padding: 24rpx 20rpx;
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

.select-cell {
  flex: 0 0 80rpx;
  justify-content: center;
}

.header-cell {
  font-weight: 600;
  color: #1D1D1F;
  font-size: 28rpx;
}

.header-cell.sortable {
  cursor: pointer;
  user-select: none;
}

.header-cell.sortable:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.data-cell {
  font-size: 28rpx;
  color: #1D1D1F;
}

/* 表头文本和排序图标 */
.header-text {
  flex: 1;
}

.sort-icon {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
  transition: color 0.2s ease;
}

.header-cell.sortable:hover .sort-icon {
  color: #007AFF;
}

/* 单元格内容样式 */
.cell-text {
  word-break: break-all;
  line-height: 1.4;
}

.cell-status {
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  background-color: #F2F2F7;
  font-size: 24rpx;
  font-weight: 500;
}

.cell-currency {
  font-weight: 600;
  color: #007AFF;
}

.cell-date {
  color: #8E8E93;
  font-size: 26rpx;
}

.cell-custom {
  width: 100%;
}

/* 选择框样式 */
.select-checkbox {
  transform: scale(1.2);
}

/* 空数据状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 分页器 */
.table-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #E5E5E7;
  background-color: #FAFAFA;
}

.pagination-info {
  font-size: 26rpx;
  color: #8E8E93;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

.pagination-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 6rpx;
  background-color: #FFFFFF;
  color: #007AFF;
  margin: 0 8rpx;
}

.pagination-btn[disabled] {
  color: #C7C7CC;
  background-color: #F2F2F7;
}

.page-info {
  font-size: 26rpx;
  color: #1D1D1F;
  margin: 0 16rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .table-cell {
    padding: 20rpx 16rpx;
    font-size: 26rpx;
  }
  
  .header-cell {
    font-size: 26rpx;
  }
  
  .data-cell {
    font-size: 26rpx;
  }
}
