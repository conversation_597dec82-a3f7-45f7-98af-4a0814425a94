# 智慧养鹅SAAS平台 - 环境变量配置模板
# 复制此文件为 .env 并填写实际值

# 应用配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=zhihuiyange
DB_PASSWORD=your_secure_database_password_here
DB_NAME=zhihuiyange_local

# JWT 密钥 (请使用强密钥，至少32个字符)
JWT_SECRET=your_jwt_secret_key_at_least_32_characters_long_here

# 会话密钥
SESSION_SECRET=your_session_secret_key_at_least_32_characters_long_here

# 微信小程序配置 (可选)
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 邮件配置 (可选)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_DIR=uploads

# API配置
API_RATE_LIMIT=1000
API_TIMEOUT=30000

# 日志配置
LOG_LEVEL=info
LOG_RETENTION_DAYS=30

# 安全配置
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# 开发配置
DEBUG=false
CORS_ORIGIN=http://localhost:3000

# 生产环境额外配置
# HTTPS_CERT_PATH=/path/to/cert.pem
# HTTPS_KEY_PATH=/path/to/key.pem
# TRUSTED_PROXIES=127.0.0.1
