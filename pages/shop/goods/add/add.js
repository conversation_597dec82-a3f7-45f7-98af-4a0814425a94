// pages/shop/goods/add/add.js
const logger = require('../../../../utils/logger.js');

Page({
  data: {
    // 商品数据
    goodsData: {
      name: '',
      price: '',
      category: '',
      description: '',
      image: '',
      stock: ''
    },
    
    // 分类数据
    categories: ['饲料', '药品', '设备', '用品', '其他'],
    categoryIndex: 0,
    
    // 表单验证
    formValid: false
  },

  onLoad: function (options) {
    // 页面加载
  },

  // 输入框内容变化
  onInputChange: function (e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`goodsData.${field}`]: value
    });
    
    this.validateForm();
  },

  // 分类选择变化
  onCategoryChange: function (e) {
    const categoryIndex = parseInt(e.detail.value);
    this.setData({
      categoryIndex: categoryIndex,
      'goodsData.category': this.data.categories[categoryIndex]
    });
    
    this.validateForm();
  },

  // 选择图片
  onChooseImage: function () {
    const that = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        const tempFilePath = res.tempFilePaths[0];
        that.setData({
          'goodsData.image': tempFilePath
        });
        
        // 模拟上传到服务器
        that.uploadImage(tempFilePath);
      },
      fail: function () {
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 上传图片
  uploadImage: function (filePath) {
    wx.showLoading({
      title: '上传中...'
    });
    
    // 模拟上传过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '图片上传成功',
        icon: 'success'
      });
      
      // 这里应该是实际的上传逻辑
      // wx.uploadFile({
      //   url: 'your-upload-url',
      //   filePath: filePath,
      //   name: 'file',
      //   success: (uploadRes) => {
      //     // 处理上传成功
      //   }
      // });
    }, 1500);
  },

  // 表单验证
  validateForm: function () {
    const { name, price, category, stock } = this.data.goodsData;
    const formValid = name && price && category && stock && 
                     parseFloat(price) > 0 && parseInt(stock) >= 0;
    
    this.setData({ formValid });
  },

  // 取消添加
  onCancel: function () {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消添加商品吗？未保存的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 提交表单
  onSubmit: function () {
    if (!this.data.formValid) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    const goodsData = this.data.goodsData;
    
    // 验证数据
    if (!this.validateGoodsData(goodsData)) {
      return;
    }

    wx.showLoading({
      title: '保存中...'
    });

    // 模拟API调用
    this.saveGoods(goodsData);
  },

  // 验证商品数据
  validateGoodsData: function (data) {
    if (!data.name || data.name.trim().length === 0) {
      wx.showToast({
        title: '请输入商品名称',
        icon: 'none'
      });
      return false;
    }

    if (!data.price || parseFloat(data.price) <= 0) {
      wx.showToast({
        title: '请输入有效价格',
        icon: 'none'
      });
      return false;
    }

    if (!data.category) {
      wx.showToast({
        title: '请选择商品分类',
        icon: 'none'
      });
      return false;
    }

    if (!data.stock || parseInt(data.stock) < 0) {
      wx.showToast({
        title: '请输入有效库存数量',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 保存商品
  saveGoods: function (goodsData) {
    // 模拟API保存过程
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟保存成功
      const success = Math.random() > 0.1; // 90%成功率
      
      if (success) {
        wx.showToast({
          title: '商品添加成功',
          icon: 'success'
        });

        // 记录商品数据到本地存储
        this.saveToStorage(goodsData);

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showModal({
          title: '添加失败',
          content: '网络错误，请稍后重试',
          showCancel: false
        });
      }
    }, 2000);
  },

  // 保存到本地存储
  saveToStorage: function (goodsData) {
    try {
      // 获取现有商品列表
      let goodsList = wx.getStorageSync('goodsList') || [];
      
      // 添加新商品
      const newGoods = {
        id: Date.now(), // 简单的ID生成
        ...goodsData,
        price: parseFloat(goodsData.price),
        stock: parseInt(goodsData.stock),
        createTime: new Date().toISOString(),
        status: 'active'
      };
      
      goodsList.unshift(newGoods);
      
      // 保存到本地存储
      wx.setStorageSync('goodsList', goodsList);
      
      logger.debug && logger.debug('商品保存到本地存储成功', newGoods);
    } catch (error) {
      logger.error && logger.error('保存到本地存储失败', error);
    }
  },

  // 重置表单
  onReset: function () {
    this.setData({
      goodsData: {
        name: '',
        price: '',
        category: '',
        description: '',
        image: '',
        stock: ''
      },
      categoryIndex: 0,
      formValid: false
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '添加商品 - 智慧养鹅商城',
      path: '/pages/shop/goods/add/add'
    };
  }
});