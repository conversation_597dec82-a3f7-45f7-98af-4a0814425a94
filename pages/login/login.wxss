/* 多租户登录页面样式 V2.0 */
@import '/styles/design-system.wxss';

.login-container {
  height: 100vh;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  display: flex;
  flex-direction: column;
  padding: 0 var(--space-2xl);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 错误提示样式 */
.error-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 40rpx;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 28rpx;
  color: #FFFFFF;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 20rpx;
}

.retry-btn,
.contact-btn {
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: 2rpx solid #FFFFFF;
  color: #FFFFFF;
  background: transparent;
}

.retry-btn:active,
.contact-btn:active {
  background: rgba(255, 255, 255, 0.1);
}

/* 登录内容区域 */
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 租户信息头部 */
.tenant-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.tenant-logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: var(--space-lg);
  border-radius: var(--radius-full);
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  background-color: var(--bg-primary);
  object-fit: cover;
  animation: slideInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s backwards;
}

.tenant-info {
  margin-bottom: 10rpx;
}

.tenant-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 10rpx;
}

.tenant-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 租户代码 */
.tenant-code {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 15rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.code-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.code-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-left: 10rpx;
}

/* 微信登录区域 */
.wechat-login-section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-3xl) var(--space-2xl);
  margin-bottom: var(--space-2xl);
  text-align: center;
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s backwards;
}

.login-title {
  margin-bottom: 15rpx;
}

.login-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.login-subtitle {
  margin-bottom: 40rpx;
}

.login-subtitle text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.wechat-login-btn {
  width: 100%;
  height: 120rpx;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-primary);
  transition: var(--transition-all);
}

.wechat-login-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-md);
}

.wechat-login-btn.disabled {
  background: #CCCCCC;
  box-shadow: none;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.login-error {
  margin-top: 20rpx;
  text-align: center;
}

.error-tip {
  display: block;
  font-size: 24rpx;
  color: #FF5722;
  margin-bottom: 10rpx;
}

.error-action {
  font-size: 24rpx;
  color: #0066CC;
  text-decoration: underline;
}

/* 演示模式区域 */
.demo-login-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  padding: 50rpx 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
}

.demo-title {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15rpx;
  gap: 20rpx;
}

.demo-title text:first-child {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.demo-info-btn {
  font-size: 22rpx;
  color: #0066CC;
  padding: 6rpx 16rpx;
  border: 1rpx solid #0066CC;
  border-radius: 20rpx;
  background: transparent;
}

.demo-description {
  margin-bottom: 40rpx;
}

.demo-description text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.demo-login-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(135deg, #FF9800 0%, #FF6F00 100%);
  color: #FFFFFF;
  border-radius: var(--radius-xl);
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(255, 152, 0, 0.3);
}

.demo-login-btn.disabled {
  background: #CCCCCC;
  box-shadow: none;
}

/* 角色选择器样式 */
.role-selector {
  margin-bottom: 30rpx;
}

.role-title {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.role-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.role-item {
  padding: 20rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.role-item:active {
  transform: scale(0.95);
}

.role-item.active {
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
  border-color: #0066CC;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.2);
}

.role-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.role-item.active .role-name {
  color: #0066CC;
}

.role-desc {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.3;
}

.role-item.active .role-desc {
  color: #4d88cc;
}

/* 模式切换 */
.mode-switch {
  text-align: center;
  margin-bottom: 30rpx;
}

.switch-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.switch-link {
  color: #FFFFFF;
  text-decoration: underline;
  font-weight: bold;
  margin-left: 10rpx;
}

/* 帮助区域 */
.help-section {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.help-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.help-item:active {
  background: rgba(255, 255, 255, 0.2);
}

.help-icon {
  width: 30rpx;
  height: 30rpx;
}

.help-item text {
  font-size: 26rpx;
  color: #FFFFFF;
}

/* 页面底部 */
.login-footer {
  text-align: center;
  margin-top: auto;
  padding-bottom: 40rpx;
}

.copyright {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 动画效果 */
.login-content {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-height: 600px) {
  .tenant-header {
    margin-bottom: 20rpx;
  }

  .tenant-logo {
    width: 80rpx;
    height: 80rpx;
  }

  .wechat-login-section,
  .demo-login-section {
    padding: 30rpx;
  }
}