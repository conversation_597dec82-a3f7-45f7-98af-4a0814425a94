@echo off
chcp 65001 >nul
title 智慧养鹅开发环境HTTPS代理服务器

echo.
echo ========================================
echo 🚀 智慧养鹅开发环境HTTPS代理服务器
echo ========================================
echo.

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 显示Node.js版本
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查依赖包
echo.
echo 📦 检查依赖包...
npm list express http-proxy-middleware cors >nul 2>&1
if errorlevel 1 (
    echo 📦 安装缺失的依赖包...
    npm install express http-proxy-middleware cors
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

:: 设置环境变量
set HTTPS_PORT=8443
set HTTP_PORT=8080
set TARGET_PORT=3000
set PROXY_DOMAIN=dev-api.zhihuiyange.local

echo.
echo ⚙️  配置信息:
echo    HTTPS端口: %HTTPS_PORT%
echo    HTTP端口: %HTTP_PORT%
echo    目标端口: %TARGET_PORT%
echo    代理域名: %PROXY_DOMAIN%

:: 检查端口占用
netstat -an | findstr :%HTTPS_PORT% >nul 2>&1
if not errorlevel 1 (
    echo.
    echo ❌ 端口 %HTTPS_PORT% 已被占用
    echo    请关闭占用端口的程序或修改配置
    pause
    exit /b 1
)

netstat -an | findstr :%HTTP_PORT% >nul 2>&1
if not errorlevel 1 (
    echo.
    echo ❌ 端口 %HTTP_PORT% 已被占用
    echo    请关闭占用端口的程序或修改配置
    pause
    exit /b 1
)

:: 检查后端服务器
echo.
echo 🔍 检查后端服务器 (localhost:%TARGET_PORT%)...
netstat -an | findstr :%TARGET_PORT% >nul 2>&1
if errorlevel 1 (
    echo ⚠️  后端服务器未运行在端口 %TARGET_PORT%
    echo    请确保后端服务器已启动
)

:: 检查hosts文件
findstr /c:"127.0.0.1 %PROXY_DOMAIN%" %WINDIR%\System32\drivers\etc\hosts >nul 2>&1
if errorlevel 1 (
    echo.
    echo 🌐 需要配置hosts文件
    echo    请以管理员身份运行以下命令:
    echo    echo 127.0.0.1 %PROXY_DOMAIN% ^>^> %WINDIR%\System32\drivers\etc\hosts
    echo.
    echo    或者手动编辑 %WINDIR%\System32\drivers\etc\hosts 文件
    echo    添加一行: 127.0.0.1 %PROXY_DOMAIN%
    echo.
)

echo.
echo 🚀 启动代理服务器...
echo.
echo ========================================
echo 📋 微信小程序配置指南
echo ========================================
echo 1. 在微信开发者工具中添加以下域名到request合法域名:
echo    https://%PROXY_DOMAIN%:%HTTPS_PORT%
echo.
echo 2. 或者在开发阶段临时关闭域名校验:
echo    详情 → 本地设置 → 勾选"不校验合法域名..."
echo.
echo 3. 在小程序中使用代理URL:
echo    const apiUrl = 'https://%PROXY_DOMAIN%:%HTTPS_PORT%/api/v1';
echo ========================================
echo.

:: 启动代理服务器
node dev-https-proxy.js

:: 脚本结束时暂停
echo.
echo 代理服务器已停止
pause