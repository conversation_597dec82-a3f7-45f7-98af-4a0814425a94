// backend/controllers/audit-log.controller.js
// 操作日志管理控制器

const AuditLog = require("../models/audit-log.model");
const { createError, asyncHandler } = require("../utils/error-handler");
const { Op } = require("sequelize");

/**
 * 获取操作日志列表
 * GET /api/admin/audit-logs
 */
exports.getLogs = asyncHandler(async (req, res) => {
  // 检查管理员权限
  if (!req.user || req.user.role !== "admin") {
    throw createError.insufficientPermissions("需要管理员权限");
  }

  const {
    page = 1,
    limit = 50,
    userId,
    action,
    resource,
    level,
    success,
    startDate,
    endDate,
    search,
  } = req.query;

  // 验证分页参数
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);

  if (pageNum < 1 || limitNum < 1 || limitNum > 1000) {
    throw createError.validationFailed("分页参数无效");
  }

  const offset = (pageNum - 1) * limitNum;
  const where = {};

  // 构建查询条件
  if (userId) {
    where.userId = parseInt(userId);
  }

  if (action) {
    where.action = action;
  }

  if (resource) {
    where.resource = resource;
  }

  if (level) {
    where.level = level;
  }

  if (success !== undefined) {
    where.success = success === "true";
  }

  // 日期范围查询
  if (startDate || endDate) {
    where.created_at = {};
    if (startDate) {
      where.created_at[Op.gte] = new Date(startDate);
    }
    if (endDate) {
      where.created_at[Op.lte] = new Date(endDate);
    }
  }

  // 搜索功能
  if (search) {
    where[Op.or] = [
      { username: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } },
      { url: { [Op.like]: `%${search}%` } },
      { ipAddress: { [Op.like]: `%${search}%` } },
    ];
  }

  const { count, rows } = await AuditLog.findAndCountAll({
    where,
    limit: limitNum,
    offset: offset,
    order: [["created_at", "DESC"]],
    attributes: [
      "id",
      "userId",
      "username",
      "action",
      "resource",
      "resourceId",
      "method",
      "url",
      "ipAddress",
      "responseStatus",
      "duration",
      "success",
      "level",
      "category",
      "description",
      "created_at",
    ],
  });

  res.json({
    success: true,
    message: "获取操作日志成功",
    data: {
      logs: rows,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: count,
        totalPages: Math.ceil(count / limitNum),
      },
    },
  });
});

/**
 * 获取操作日志详情
 * GET /api/admin/audit-logs/:id
 */
exports.getLogDetail = asyncHandler(async (req, res) => {
  // 检查管理员权限
  if (!req.user || req.user.role !== "admin") {
    throw createError.insufficientPermissions("需要管理员权限");
  }

  const { id } = req.params;

  if (!id || isNaN(parseInt(id))) {
    throw createError.validationFailed("无效的日志ID");
  }

  const log = await AuditLog.findByPk(parseInt(id));

  if (!log) {
    throw createError.recordNotFound("操作日志");
  }

  res.json({
    success: true,
    message: "获取操作日志详情成功",
    data: log,
  });
});

/**
 * 获取操作统计信息
 * GET /api/admin/audit-logs/stats
 */
exports.getStats = asyncHandler(async (req, res) => {
  // 检查管理员权限
  if (!req.user || req.user.role !== "admin") {
    throw createError.insufficientPermissions("需要管理员权限");
  }

  const { days = 7 } = req.query;
  const daysNum = parseInt(days);

  if (daysNum < 1 || daysNum > 365) {
    throw createError.validationFailed("天数参数无效");
  }

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - daysNum);

  // 总操作数
  const totalOperations = await AuditLog.count({
    where: {
      created_at: {
        [Op.gte]: startDate,
      },
    },
  });

  // 成功操作数
  const successfulOperations = await AuditLog.count({
    where: {
      created_at: {
        [Op.gte]: startDate,
      },
      success: true,
    },
  });

  // 失败操作数
  const failedOperations = totalOperations - successfulOperations;

  // 按操作类型统计
  const actionStats = await AuditLog.findAll({
    where: {
      created_at: {
        [Op.gte]: startDate,
      },
    },
    attributes: [
      "action",
      [AuditLog.sequelize.fn("COUNT", AuditLog.sequelize.col("id")), "count"],
    ],
    group: ["action"],
    order: [
      [AuditLog.sequelize.fn("COUNT", AuditLog.sequelize.col("id")), "DESC"],
    ],
  });

  // 按资源类型统计
  const resourceStats = await AuditLog.findAll({
    where: {
      created_at: {
        [Op.gte]: startDate,
      },
    },
    attributes: [
      "resource",
      [AuditLog.sequelize.fn("COUNT", AuditLog.sequelize.col("id")), "count"],
    ],
    group: ["resource"],
    order: [
      [AuditLog.sequelize.fn("COUNT", AuditLog.sequelize.col("id")), "DESC"],
    ],
  });

  // 按用户统计
  const userStats = await AuditLog.findAll({
    where: {
      created_at: {
        [Op.gte]: startDate,
      },
      userId: {
        [Op.not]: null,
      },
    },
    attributes: [
      "userId",
      "username",
      [AuditLog.sequelize.fn("COUNT", AuditLog.sequelize.col("id")), "count"],
    ],
    group: ["userId", "username"],
    order: [
      [AuditLog.sequelize.fn("COUNT", AuditLog.sequelize.col("id")), "DESC"],
    ],
    limit: 10,
  });

  // 按日期统计（最近7天）
  const dailyStats = await AuditLog.findAll({
    where: {
      created_at: {
        [Op.gte]: startDate,
      },
    },
    attributes: [
      [
        AuditLog.sequelize.fn("DATE", AuditLog.sequelize.col("created_at")),
        "date",
      ],
      [AuditLog.sequelize.fn("COUNT", AuditLog.sequelize.col("id")), "count"],
    ],
    group: [
      AuditLog.sequelize.fn("DATE", AuditLog.sequelize.col("created_at")),
    ],
    order: [
      [
        AuditLog.sequelize.fn("DATE", AuditLog.sequelize.col("created_at")),
        "ASC",
      ],
    ],
  });

  res.json({
    success: true,
    message: "获取操作统计成功",
    data: {
      summary: {
        totalOperations,
        successfulOperations,
        failedOperations,
        successRate:
          totalOperations > 0
            ? ((successfulOperations / totalOperations) * 100).toFixed(2)
            : 0,
        period: `${daysNum}天`,
      },
      actionStats: actionStats.map((item) => ({
        action: item.action,
        count: parseInt(item.dataValues.count),
      })),
      resourceStats: resourceStats.map((item) => ({
        resource: item.resource,
        count: parseInt(item.dataValues.count),
      })),
      userStats: userStats.map((item) => ({
        userId: item.userId,
        username: item.username,
        count: parseInt(item.dataValues.count),
      })),
      dailyStats: dailyStats.map((item) => ({
        date: item.dataValues.date,
        count: parseInt(item.dataValues.count),
      })),
    },
  });
});

/**
 * 导出操作日志
 * GET /api/admin/audit-logs/export
 */
exports.exportLogs = asyncHandler(async (req, res) => {
  // 检查管理员权限
  if (!req.user || req.user.role !== "admin") {
    throw createError.insufficientPermissions("需要管理员权限");
  }

  const {
    format = "csv",
    startDate,
    endDate,
    userId,
    action,
    resource,
  } = req.query;

  if (!["csv", "json"].includes(format)) {
    throw createError.validationFailed("不支持的导出格式");
  }

  const where = {};

  // 构建查询条件
  if (userId) where.userId = parseInt(userId);
  if (action) where.action = action;
  if (resource) where.resource = resource;

  if (startDate || endDate) {
    where.created_at = {};
    if (startDate) where.created_at[Op.gte] = new Date(startDate);
    if (endDate) where.created_at[Op.lte] = new Date(endDate);
  }

  const logs = await AuditLog.findAll({
    where,
    order: [["created_at", "DESC"]],
    limit: 10000, // 限制导出数量
    attributes: [
      "id",
      "userId",
      "username",
      "action",
      "resource",
      "resourceId",
      "method",
      "url",
      "ipAddress",
      "responseStatus",
      "duration",
      "success",
      "level",
      "category",
      "description",
      "created_at",
    ],
  });

  if (format === "csv") {
    // CSV格式导出
    const csvHeader = [
      "ID",
      "用户ID",
      "用户名",
      "操作",
      "资源",
      "资源ID",
      "HTTP方法",
      "URL",
      "IP地址",
      "响应状态",
      "耗时(ms)",
      "是否成功",
      "级别",
      "分类",
      "描述",
      "创建时间",
    ].join(",");

    const csvRows = logs.map((log) =>
      [
        log.id,
        log.userId || "",
        log.username || "",
        log.action,
        log.resource,
        log.resourceId || "",
        log.method,
        `"${log.url}"`,
        log.ipAddress || "",
        log.responseStatus || "",
        log.duration || "",
        log.success ? "是" : "否",
        log.level,
        log.category || "",
        `"${log.description || ""}"`,
        log.created_at,
      ].join(","),
    );

    const csvContent = [csvHeader, ...csvRows].join("\n");

    res.setHeader("Content-Type", "text/csv; charset=utf-8");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=audit-logs-${Date.now()}.csv`,
    );
    res.send("\ufeff" + csvContent); // 添加BOM以支持中文
  } else {
    // JSON格式导出
    res.setHeader("Content-Type", "application/json");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=audit-logs-${Date.now()}.json`,
    );
    res.json({
      exportTime: new Date().toISOString(),
      totalCount: logs.length,
      data: logs,
    });
  }
});

/**
 * 清理过期日志
 * DELETE /api/admin/audit-logs/cleanup
 */
exports.cleanupLogs = asyncHandler(async (req, res) => {
  // 检查管理员权限
  if (!req.user || req.user.role !== "admin") {
    throw createError.insufficientPermissions("需要管理员权限");
  }

  const { days = 90 } = req.body;
  const daysNum = parseInt(days);

  if (daysNum < 30 || daysNum > 365) {
    throw createError.validationFailed("保留天数必须在30-365天之间");
  }

  const deletedCount = await AuditLog.cleanupOldLogs(daysNum);

  res.json({
    success: true,
    message: `成功清理${deletedCount}条过期操作日志`,
    data: {
      deletedCount,
      retentionDays: daysNum,
    },
  });
});

module.exports = {
  getLogs: exports.getLogs,
  getLogDetail: exports.getLogDetail,
  getStats: exports.getStats,
  exportLogs: exports.exportLogs,
  cleanupLogs: exports.cleanupLogs,
};
