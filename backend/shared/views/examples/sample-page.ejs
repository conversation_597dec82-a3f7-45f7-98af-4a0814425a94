<%
// 示例页面配置
const pageConfig = {
    title: '示例页面',
    subtitle: '展示统一组件库的使用方法',
    icon: 'bi-star',
    platformType: 'saas', // 或 'admin'
    currentPage: 'example',
    breadcrumb: [
        { text: '首页', url: '/dashboard' },
        { text: '示例', url: '/examples' },
        { text: '示例页面' }
    ],
    actions: [
        {
            text: '新增项目',
            icon: 'bi-plus-lg',
            class: 'btn-primary',
            onclick: 'createItem()'
        },
        {
            text: '导出数据',
            icon: 'bi-download',
            class: 'btn-outline-secondary',
            onclick: 'exportData()'
        },
        {
            type: 'dropdown',
            text: '更多操作',
            icon: 'bi-three-dots',
            class: 'btn-outline-primary',
            items: [
                { text: '批量编辑', icon: 'bi-pencil-square', onclick: 'batchEdit()' },
                { text: '批量删除', icon: 'bi-trash', onclick: 'batchDelete()' },
                { divider: true },
                { text: '导入数据', icon: 'bi-upload', onclick: 'importData()' }
            ]
        }
    ],
    messages: {
        success: ['操作成功完成！'],
        info: ['这是一个信息提示'],
        warning: [{ 
            title: '注意事项', 
            text: '这是一个带标题的警告消息' 
        }]
    },
    currentUser: {
        name: '管理员',
        email: '<EMAIL>',
        avatar: null
    },
    notifications: {
        unreadCount: 3,
        items: [
            {
                title: '系统更新',
                content: '系统已更新至v2.6.0版本',
                icon: 'bi-arrow-up-circle',
                timeAgo: '5分钟前',
                read: false
            },
            {
                title: '新用户注册',
                content: '有新用户注册了平台',
                icon: 'bi-person-plus',
                timeAgo: '1小时前',
                read: true
            }
        ]
    }
};
%>

<!-- 使用统一布局 -->
<%- contentFor('body') %>
    <!-- 页面内容开始 -->
    <div class="row">
        <!-- 统计卡片 -->
        <div class="col-md-3 mb-4">
            <div class="modern-card">
                <div class="modern-card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="bi bi-people fs-1 text-primary"></i>
                    </div>
                    <h3 class="mb-1">1,234</h3>
                    <p class="text-muted mb-0">总用户数</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="modern-card">
                <div class="modern-card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="bi bi-graph-up fs-1 text-success"></i>
                    </div>
                    <h3 class="mb-1">89.5%</h3>
                    <p class="text-muted mb-0">系统性能</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="modern-card">
                <div class="modern-card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="bi bi-currency-dollar fs-1 text-warning"></i>
                    </div>
                    <h3 class="mb-1">¥56,789</h3>
                    <p class="text-muted mb-0">月收入</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="modern-card">
                <div class="modern-card-body text-center">
                    <div class="stat-icon mb-3">
                        <i class="bi bi-activity fs-1 text-info"></i>
                    </div>
                    <h3 class="mb-1">99.9%</h3>
                    <p class="text-muted mb-0">在线时间</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 表格示例 -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="mb-0">数据列表</h5>
                    <div class="card-header-actions">
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-funnel"></i> 筛选
                        </button>
                        <button class="btn btn-sm btn-primary">
                            <i class="bi bi-plus"></i> 新增
                        </button>
                    </div>
                </div>
                <div class="modern-table">
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>001</td>
                                <td>示例项目</td>
                                <td><span class="status-badge active">活跃</span></td>
                                <td>2024-01-15 10:30:00</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">编辑</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>002</td>
                                <td>测试项目</td>
                                <td><span class="status-badge pending">待审核</span></td>
                                <td>2024-01-14 16:45:00</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">编辑</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>003</td>
                                <td>已完成项目</td>
                                <td><span class="status-badge inactive">已完成</span></td>
                                <td>2024-01-13 09:15:00</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">编辑</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 表单示例 -->
    <div class="row">
        <div class="col-md-8 mb-4">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="mb-0">表单示例</h5>
                </div>
                <div class="modern-card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="itemName" class="form-label">项目名称</label>
                                <input type="text" class="form-control" id="itemName" placeholder="请输入项目名称">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="itemType" class="form-label">项目类型</label>
                                <select class="form-select" id="itemType">
                                    <option selected>选择类型</option>
                                    <option value="1">开发项目</option>
                                    <option value="2">测试项目</option>
                                    <option value="3">运维项目</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="itemDescription" class="form-label">项目描述</label>
                            <textarea class="form-control" id="itemDescription" rows="3" placeholder="请输入项目描述"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isPublic">
                                <label class="form-check-label" for="isPublic">
                                    公开项目
                                </label>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-modern btn-primary">保存项目</button>
                            <button type="reset" class="btn btn-outline-secondary">重置表单</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="mb-0">快捷操作</h5>
                </div>
                <div class="modern-card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-modern btn-success" onclick="showSuccess('操作成功!')">
                            <i class="bi bi-check-circle me-2"></i>成功提示
                        </button>
                        <button class="btn btn-modern btn-warning" onclick="showWarning('请注意检查数据!')">
                            <i class="bi bi-exclamation-triangle me-2"></i>警告提示
                        </button>
                        <button class="btn btn-modern btn-danger" onclick="showError('操作失败!')">
                            <i class="bi bi-x-circle me-2"></i>错误提示
                        </button>
                        <button class="btn btn-modern btn-info" onclick="showInfo('这是一条信息')">
                            <i class="bi bi-info-circle me-2"></i>信息提示
                        </button>
                    </div>
                    
                    <hr>
                    
                    <h6>系统功能</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="checkSystemHealth()">
                            <i class="bi bi-heart-pulse me-2"></i>健康检查
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="showSystemInfo()">
                            <i class="bi bi-info-circle me-2"></i>系统信息
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportSystemReport()">
                            <i class="bi bi-download me-2"></i>导出报告
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 页面内容结束 -->

<!-- 页面特定样式 -->
<% pageConfig.customStyles = `
    .stat-icon {
        opacity: 0.8;
    }
    
    .modern-card:hover .stat-icon {
        opacity: 1;
        transform: scale(1.05);
        transition: all 0.3s ease;
    }
    
    .card-header-actions {
        display: flex;
        gap: 8px;
    }
    
    .modern-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
`; %>

<!-- 页面特定脚本 -->
<% pageConfig.customScripts = `
    // 页面功能函数
    function createItem() {
        showInfo('打开创建项目对话框');
    }
    
    function exportData() {
        showSuccess('开始导出数据...');
    }
    
    function batchEdit() {
        showInfo('批量编辑功能');
    }
    
    function batchDelete() {
        if (confirm('确定要批量删除选中项目吗？')) {
            showSuccess('批量删除完成');
        }
    }
    
    function importData() {
        showInfo('打开数据导入对话框');
    }
    
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('示例页面加载完成');
        
        // 为表单添加验证
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                showSuccess('表单提交成功！');
            });
        }
    });
`; %>

<!-- 渲染页面布局 -->
<%- include('../layouts/base', {
    ...pageConfig,
    body: contentFor('body')
}) %>