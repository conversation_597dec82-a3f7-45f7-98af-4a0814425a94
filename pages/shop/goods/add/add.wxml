<!--pages/shop/goods/add/add.wxml-->
<view class="container">
  <view class="header">
    <text class="title">添加商品</text>
  </view>
  
  <view class="content">
    <form class="goods-form">
      <view class="form-group">
        <text class="form-label">商品名称</text>
        <input class="form-input" placeholder="请输入商品名称" bindinput="onInputChange" data-field="name" />
      </view>
      
      <view class="form-group">
        <text class="form-label">商品价格</text>
        <input class="form-input" type="digit" placeholder="请输入商品价格" bindinput="onInputChange" data-field="price" />
      </view>
      
      <view class="form-group">
        <text class="form-label">商品分类</text>
        <picker bindchange="onCategoryChange" value="{{categoryIndex}}" range="{{categories}}">
          <view class="picker-display">
            <text>{{categories[categoryIndex] || '请选择分类'}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
      
      <view class="form-group">
        <text class="form-label">商品描述</text>
        <textarea class="form-textarea" placeholder="请输入商品描述" bindinput="onInputChange" data-field="description"></textarea>
      </view>
      
      <view class="form-group">
        <text class="form-label">商品图片</text>
        <view class="image-upload" bindtap="onChooseImage">
          <image wx:if="{{goodsData.image}}" src="{{goodsData.image}}" class="preview-image" />
          <view wx:else class="upload-placeholder">
            <text class="upload-icon">+</text>
            <text class="upload-text">点击上传图片</text>
          </view>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">库存数量</text>
        <input class="form-input" type="number" placeholder="请输入库存数量" bindinput="onInputChange" data-field="stock" />
      </view>
      
      <view class="form-actions">
        <button class="btn btn-secondary" bindtap="onCancel">取消</button>
        <button class="btn btn-primary" bindtap="onSubmit">保存商品</button>
      </view>
    </form>
  </view>
</view>
