<!-- 通用卡片组件 -->
<view 
  class="c-card c-card--padding-{{padding}} {{margin !== 'none' ? 'c-card--margin-' + margin : ''}} {{shadow ? 'c-card--shadow-' + shadowSize : ''}} c-card--radius-{{radius}} {{border ? 'c-card--border' : ''}} {{clickable && !disabled ? 'c-card--clickable' : ''}} {{disabled ? 'c-card--disabled' : ''}} {{customClass}}"
  style="{{backgroundColor ? 'background-color: ' + backgroundColor + ';' : ''}} {{customStyle}}"
  aria-label="{{ariaLabel || title}}"
  aria-disabled="{{disabled}}"
  role="{{clickable ? 'button' : 'region'}}"
  bindtap="onTap"
  bindlongpress="onLongPress"
>
  <!-- 卡片头部 -->
  <view wx:if="{{title || subtitle}}" class="c-card__header" bindtap="onHeaderTap">
    <slot name="header">
      <view class="c-card__header-content">
        <!-- 标题 -->
        <view wx:if="{{title}}" class="c-card__title">
          <text>{{title}}</text>
        </view>
        
        <!-- 副标题 -->
        <view wx:if="{{subtitle}}" class="c-card__subtitle">
          <text>{{subtitle}}</text>
        </view>
      </view>
      
      <!-- 头部右侧内容 -->
      <view class="c-card__header-extra">
        <slot name="header-extra"></slot>
        
        <!-- 箭头图标 -->
        <view wx:if="{{clickable && arrow && !disabled}}" class="c-card__arrow">
          <text class="c-card__arrow-icon">›</text>
        </view>
      </view>
    </slot>
  </view>
  
  <!-- 卡片主体内容 -->
  <view class="c-card__body">
    <slot></slot>
  </view>
  
  <!-- 卡片底部 -->
  <view class="c-card__footer" bindtap="onFooterTap">
    <slot name="footer"></slot>
  </view>
</view>