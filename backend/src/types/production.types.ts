import { z } from "zod";

// 天气状况枚举
export const WeatherConditionSchema = z.enum([
  "sunny", // 晴天
  "cloudy", // 多云
  "overcast", // 阴天
  "rainy", // 雨天
  "foggy", // 雾天
  "windy", // 大风
  "stormy", // 暴风雨
]);

// 记录类型枚举
export const RecordTypeSchema = z.enum([
  "daily", // 日常记录
  "feeding", // 喂食记录
  "collection", // 收集记录
  "cleaning", // 清洁记录
  "maintenance", // 维护记录
]);

// 基础生产记录Schema
export const ProductionRecordSchema = z.object({
  id: z.number().int().positive(),
  userId: z.number().int().positive(),
  flockId: z.number().int().positive(),
  recordType: RecordTypeSchema.default("daily"),
  recordDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "记录日期格式无效",
  }),
  eggCount: z.number().int().nonnegative().default(0),
  eggWeight: z.number().positive().optional(), // 蛋重量(kg)
  feedConsumption: z.number().nonnegative().default(0), // 饲料消耗(kg)
  feedCost: z.number().nonnegative().optional(), // 饲料成本
  waterConsumption: z.number().nonnegative().optional(), // 饮水量(L)
  temperature: z.number().min(-20).max(50).optional(), // 环境温度(°C)
  humidity: z.number().min(0).max(100).optional(), // 湿度(%)
  weather: WeatherConditionSchema.optional(),
  mortality: z.number().int().nonnegative().default(0), // 死亡数量
  healthyCount: z.number().int().nonnegative().optional(), // 健康数量
  sickCount: z.number().int().nonnegative().default(0), // 生病数量
  notes: z.string().max(2000).optional(),
  recorderId: z.number().int().positive(),
  createdAt: z.string(),
  updatedAt: z.string(),
  // 关联字段
  flockName: z.string().optional(),
  batchNumber: z.string().optional(),
  recorderName: z.string().optional(),
});

// 请求参数Schema
export const ProductionRecordParamsSchema = z.object({
  id: z.coerce.number().int().positive(),
});

// 分页查询Schema
export const GetProductionRecordsQuerySchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  flockId: z.coerce.number().int().positive().optional(),
  recordType: RecordTypeSchema.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  weather: WeatherConditionSchema.optional(),
  search: z.string().max(100).optional(),
  sortBy: z
    .enum(["recordDate", "createdAt", "eggCount", "feedConsumption"])
    .default("recordDate"),
  sortOrder: z.enum(["ASC", "DESC"]).default("DESC"),
});

// 创建生产记录Schema
export const CreateProductionRecordSchema = z
  .object({
    flockId: z.number().int().positive(),
    recordType: RecordTypeSchema.default("daily"),
    recordDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: "记录日期格式无效",
    }),
    eggCount: z.number().int().nonnegative().default(0),
    eggWeight: z.number().positive().optional(),
    feedConsumption: z.number().nonnegative().default(0),
    feedCost: z.number().nonnegative().optional(),
    waterConsumption: z.number().nonnegative().optional(),
    temperature: z.number().min(-20).max(50).optional(),
    humidity: z.number().min(0).max(100).optional(),
    weather: WeatherConditionSchema.optional(),
    mortality: z.number().int().nonnegative().default(0),
    healthyCount: z.number().int().nonnegative().optional(),
    sickCount: z.number().int().nonnegative().default(0),
    notes: z.string().max(2000).optional(),
  })
  .refine(
    (data) => {
      // 验证日期不能是未来日期
      const recordDate = new Date(data.recordDate);
      const today = new Date();
      today.setHours(23, 59, 59, 999); // 设置为当天的最后一刻
      return recordDate <= today;
    },
    {
      message: "记录日期不能是未来日期",
      path: ["recordDate"],
    },
  );

// 更新生产记录Schema
export const UpdateProductionRecordSchema = z.object({
  recordType: RecordTypeSchema.optional(),
  recordDate: z
    .string()
    .refine((date) => !isNaN(Date.parse(date)), {
      message: "记录日期格式无效",
    })
    .optional(),
  eggCount: z.number().int().nonnegative().optional(),
  eggWeight: z.number().positive().optional(),
  feedConsumption: z.number().nonnegative().optional(),
  feedCost: z.number().nonnegative().optional(),
  waterConsumption: z.number().nonnegative().optional(),
  temperature: z.number().min(-20).max(50).optional(),
  humidity: z.number().min(0).max(100).optional(),
  weather: WeatherConditionSchema.optional(),
  mortality: z.number().int().nonnegative().optional(),
  healthyCount: z.number().int().nonnegative().optional(),
  sickCount: z.number().int().nonnegative().optional(),
  notes: z.string().max(2000).optional(),
});

// 生产统计Schema
export const ProductionStatsSchema = z.object({
  totalRecords: z.number().int().nonnegative(),
  totalEggs: z.number().int().nonnegative(),
  totalFeedConsumption: z.number().nonnegative(),
  totalFeedCost: z.number().nonnegative(),
  averageEggsPerDay: z.number().nonnegative(),
  averageFeedPerDay: z.number().nonnegative(),
  eggProductionRate: z.number().min(0).max(100), // 产蛋率%
  feedEfficiency: z.number().nonnegative(), // 饲料效率 (蛋重/饲料重量)
  mortality: z.number().int().nonnegative(),
  mortalityRate: z.number().min(0).max(100),
  period: z.object({
    startDate: z.string(),
    endDate: z.string(),
    days: z.number().int().positive(),
  }),
});

// 生产趋势Schema
export const ProductionTrendSchema = z.object({
  date: z.string(),
  eggCount: z.number().int().nonnegative(),
  eggWeight: z.number().nonnegative(),
  feedConsumption: z.number().nonnegative(),
  feedCost: z.number().nonnegative(),
  productionRate: z.number().min(0).max(100),
  mortality: z.number().int().nonnegative(),
});

// 批量操作Schema
export const BatchCreateProductionRecordsSchema = z.object({
  records: z.array(CreateProductionRecordSchema).min(1).max(50), // 最多50条
});

export const BatchUpdateProductionRecordsSchema = z.object({
  ids: z.array(z.number().int().positive()).min(1).max(50),
  updateData: UpdateProductionRecordSchema,
});

export const BatchDeleteProductionRecordsSchema = z.object({
  ids: z.array(z.number().int().positive()).min(1).max(50),
});

// API输入输出类型定义
export type GetProductionRecordsInput = z.infer<
  typeof GetProductionRecordsQuerySchema
>;
export type GetProductionRecordsOutput = {
  items: z.infer<typeof ProductionRecordSchema>[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
};

export type GetProductionRecordByIdInput = z.infer<
  typeof ProductionRecordParamsSchema
>;
export type GetProductionRecordByIdOutput = z.infer<
  typeof ProductionRecordSchema
>;

export type CreateProductionRecordInput = z.infer<
  typeof CreateProductionRecordSchema
>;
export type CreateProductionRecordOutput = z.infer<
  typeof ProductionRecordSchema
>;

export type UpdateProductionRecordInput = z.infer<
  typeof UpdateProductionRecordSchema
> &
  z.infer<typeof ProductionRecordParamsSchema>;
export type UpdateProductionRecordOutput = z.infer<
  typeof ProductionRecordSchema
>;

export type DeleteProductionRecordInput = z.infer<
  typeof ProductionRecordParamsSchema
>;
export type DeleteProductionRecordOutput = {
  success: true;
  message: string;
};

export type GetProductionStatsInput = {
  flockId?: number;
  startDate?: string;
  endDate?: string;
  recordType?: z.infer<typeof RecordTypeSchema>;
};
export type GetProductionStatsOutput = z.infer<typeof ProductionStatsSchema>;

export type GetProductionTrendsInput = {
  flockId?: number;
  startDate?: string;
  endDate?: string;
  period?: "day" | "week" | "month";
};
export type GetProductionTrendsOutput = z.infer<typeof ProductionTrendSchema>[];

// 批量操作类型
export type BatchCreateProductionRecordsInput = z.infer<
  typeof BatchCreateProductionRecordsSchema
>;
export type BatchCreateProductionRecordsOutput = {
  success: true;
  created: z.infer<typeof ProductionRecordSchema>[];
  errors?: string[];
};

export type BatchUpdateProductionRecordsInput = z.infer<
  typeof BatchUpdateProductionRecordsSchema
>;
export type BatchUpdateProductionRecordsOutput = {
  success: true;
  updated: z.infer<typeof ProductionRecordSchema>[];
  errors?: string[];
};

export type BatchDeleteProductionRecordsInput = z.infer<
  typeof BatchDeleteProductionRecordsSchema
>;
export type BatchDeleteProductionRecordsOutput = {
  success: true;
  deletedCount: number;
  errors?: string[];
};
