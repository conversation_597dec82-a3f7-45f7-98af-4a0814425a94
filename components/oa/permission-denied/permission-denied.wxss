/* OA无权限页面组件样式 */

.permission-denied-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl) var(--space-xl);
  text-align: center;
  min-height: 60vh;
  background-color: var(--bg-secondary);
}

.permission-denied-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--space-2xl);
  padding: var(--space-xl);
  background-color: var(--permission-denied-bg);
  border-radius: var(--radius-full);
  color: var(--permission-denied);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-4xl);
  animation: pulse 2s infinite;
}

.permission-denied-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-tight);
}

.permission-denied-description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-2xl);
  max-width: 400rpx;
}

/* 操作按钮组 */
.permission-denied-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  width: 100%;
  max-width: 300rpx;
}

.permission-request-btn {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  font-family: var(--font-family);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-primary);
  min-height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.permission-request-btn:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

.permission-request-btn::after {
  border: none;
}

/* 权限详情区域 */
.permission-details {
  margin-top: var(--space-2xl);
  padding: var(--space-xl);
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  border: 1rpx solid var(--border-light);
  width: 100%;
  max-width: 400rpx;
}

.permission-details-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  text-align: center;
}

.permission-details-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) 0;
  border-bottom: 1rpx solid var(--border-light);
}

.permission-details-item:last-child {
  border-bottom: none;
}

.permission-name {
  flex: 1;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-align: left;
}

/* 权限指示器在详情中的样式 */
.permission-details .permission-indicator {
  font-size: var(--text-xs);
  padding: var(--space-xs) var(--space-sm);
}

.permission-details .permission-icon {
  width: 12rpx;
  height: 12rpx;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* 响应式适配 */
@media (max-width: 600rpx) {
  .permission-denied-page {
    padding: var(--space-2xl) var(--space-lg);
  }
  
  .permission-denied-description {
    max-width: 100%;
  }
  
  .permission-denied-actions {
    max-width: 100%;
  }
}