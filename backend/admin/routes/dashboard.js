const express = require("express");
const router = express.Router();
const apiService = require("../utils/apiService");

/**
 * 仪表板首页
 */
router.get("/", async (req, res) => {
  try {
    // 检查用户是否已登录
    if (!req.session.user || !req.session.token) {
      return res.redirect("/login");
    }

    // 获取仪表板统计数据
    let dashboardData = {
      totalUsers: 0,
      totalProduction: 0,
      totalHealth: 0,
      recentActivities: [],
      productionTrend: [],
      healthStatus: {
        healthy: 0,
        warning: 0,
        critical: 0,
      },
    };

    try {
      // 并行获取各种统计数据
      const [statsResult, productionResult, healthResult] =
        await Promise.allSettled([
          apiService.getDashboardStats(req.session.token),
          apiService.getProductionRecords(req.session.token, { limit: 10 }),
          apiService.getHealthRecords(req.session.token, { limit: 10 }),
        ]);

      // 处理统计数据
      if (statsResult.status === "fulfilled") {
        dashboardData = { ...dashboardData, ...statsResult.value };
      }

      // 处理生产记录数据
      if (
        productionResult.status === "fulfilled" &&
        productionResult.value.success
      ) {
        const records = productionResult.value.data.records || [];
        dashboardData.totalProduction =
          productionResult.value.data.total || records.length;

        // 生成生产趋势数据（最近7天）
        dashboardData.productionTrend = generateProductionTrend(records);
      }

      // 处理健康记录数据
      if (healthResult.status === "fulfilled" && healthResult.value.success) {
        const records = healthResult.value.data || [];
        dashboardData.totalHealth = records.length;

        // 生成健康状态统计
        dashboardData.healthStatus = generateHealthStatus(records);
      }

      // 生成最近活动
      dashboardData.recentActivities = generateRecentActivities();
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取仪表板数据失败:", error); } catch(_) {}

      // 使用默认数据，不阻止页面渲染
    }

    res.render("dashboard/index", {
      title: "仪表板 - 智慧养鹅管理系统",
      data: dashboardData,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("仪表板路由错误:", error); } catch(_) {}

    res.status(500).render("error", {
      title: "服务器错误 - 智慧养鹅管理系统",
      error: {
        status: 500,
        message: "获取仪表板数据失败",
        stack: process.env.NODE_ENV === "development" ? error.stack : "",
      },
    });
  }
});

/**
 * 生成生产趋势数据
 */
function generateProductionTrend(records) {
  const trend = [];
  const today = new Date();

  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split("T")[0];

    // 查找该日期的记录
    const dayRecords = records.filter((record) => {
      const recordDate = new Date(record.createdAt || record.记录日期);
      return recordDate.toISOString().split("T")[0] === dateStr;
    });

    const totalEggs = dayRecords.reduce(
      (sum, record) => sum + (record.产蛋数量 || 0),
      0,
    );

    trend.push({
      date: dateStr,
      eggs: totalEggs,
      label: date.toLocaleDateString("zh-CN", {
        month: "short",
        day: "numeric",
      }),
    });
  }

  return trend;
}

/**
 * 生成健康状态统计
 */
function generateHealthStatus(records) {
  const status = {
    healthy: 0,
    warning: 0,
    critical: 0,
  };

  records.forEach((record) => {
    if (record.健康状态) {
      switch (record.健康状态.toLowerCase()) {
        case "健康":
        case "healthy":
          status.healthy++;
          break;
        case "警告":
        case "warning":
          status.warning++;
          break;
        case "严重":
        case "critical":
          status.critical++;
          break;
        default:
          status.healthy++;
      }
    } else {
      status.healthy++;
    }
  });

  return status;
}

/**
 * 生成最近活动数据
 */
function generateRecentActivities() {
  return [
    {
      type: "production",
      icon: "bi-clipboard-data",
      title: "新增生产记录",
      description: "记录了今日的产蛋数量",
      time: "2小时前",
      color: "success",
    },
    {
      type: "health",
      icon: "bi-heart-pulse",
      title: "健康检查完成",
      description: "完成了鹅群的健康状态检查",
      time: "4小时前",
      color: "info",
    },
    {
      type: "user",
      icon: "bi-person-plus",
      title: "新用户注册",
      description: "有新的用户加入系统",
      time: "6小时前",
      color: "primary",
    },
    {
      type: "system",
      icon: "bi-gear",
      title: "系统更新",
      description: "系统配置已更新",
      time: "1天前",
      color: "warning",
    },
  ];
}

module.exports = router;
