// 系统信息获取辅助工具
// 兼容新旧API，逐步迁移wx.getSystemInfoSync()

/**
 * 获取设备信息（兼容新旧API）
 * @returns {Object} 设备信息对象
 */
function getDeviceInfo() {
  try {
    // 优先使用新API
    if (wx.getDeviceInfo) {
      return wx.getDeviceInfo();
    }
    
    // 后备方案：返回默认值
    try { const logger = require('./logger.js'); logger.warn && logger.warn('wx.getDeviceInfo不可用，返回默认设备信息'); } catch(_) {}
    return {
      platform: 'unknown',
      brand: 'unknown',
      model: 'unknown',
      system: 'unknown',
      version: 'unknown',
      pixelRatio: 1
    };
  } catch (error) {
    try { const logger = require('./logger.js'); logger.warn && logger.warn('获取设备信息失败', error); } catch(_) {}
    return {
      platform: 'unknown',
      brand: 'unknown', 
      model: 'unknown',
      system: 'unknown',
      version: 'unknown',
      pixelRatio: 1
    };
  }
}

/**
 * 获取窗口信息（兼容新旧API）
 * @returns {Object} 窗口信息对象
 */
function getWindowInfo() {
  try {
    // 优先使用新API
    if (wx.getWindowInfo) {
      return wx.getWindowInfo();
    }
    
    // 后备方案：返回默认值
    try { const logger = require('./logger.js'); logger.warn && logger.warn('wx.getWindowInfo不可用，返回默认窗口信息'); } catch(_) {}
    return {
      windowWidth: 375,
      windowHeight: 667,
      screenWidth: 375,
      screenHeight: 667,
      statusBarHeight: 20,
      safeArea: { left: 0, right: 375, top: 20, bottom: 667, width: 375, height: 647 }
    };
  } catch (error) {
    try { const logger = require('./logger.js'); logger.warn && logger.warn('获取窗口信息失败', error); } catch(_) {}
    return {
      windowWidth: 375,
      windowHeight: 667,
      screenWidth: 375,
      screenHeight: 667,
      statusBarHeight: 20,
      safeArea: { top: 20, right: 375, bottom: 667, left: 0 }
    };
  }
}

/**
 * 获取应用基础信息（兼容新旧API）
 * @returns {Object} 应用信息对象
 */
function getAppBaseInfo() {
  try {
    // 优先使用新API
    if (wx.getAppBaseInfo) {
      return wx.getAppBaseInfo();
    }
    
    // 后备方案：返回默认值
    try { const logger = require('./logger.js'); logger.warn && logger.warn('wx.getAppBaseInfo不可用，返回默认应用信息'); } catch(_) {}
    return {
      SDKVersion: '2.0.0',
      language: 'zh_CN',
      theme: 'light',
      appId: ''
    };
  } catch (error) {
    try { const logger = require('./logger.js'); logger.warn && logger.warn('获取应用信息失败', error); } catch(_) {}
    return {
      SDKVersion: '2.0.0',
      language: 'zh_CN',
      theme: 'light',
      appId: ''
    };
  }
}

/**
 * 获取系统设置（兼容新旧API）
 * @returns {Object} 系统设置对象
 */
function getSystemSetting() {
  try {
    // 优先使用新API
    if (wx.getSystemSetting) {
      return wx.getSystemSetting();
    }
    
    // 后备方案：返回默认值
    try { const logger = require('./logger.js'); logger.warn && logger.warn('wx.getSystemSetting不可用，返回默认系统设置'); } catch(_) {}
    return {
      bluetoothEnabled: false,
      locationEnabled: false,
      wifiEnabled: false,
      deviceOrientation: 'portrait'
    };
  } catch (error) {
    try { const logger = require('./logger.js'); logger.warn && logger.warn('获取系统设置失败', error); } catch(_) {}
    return {
      bluetoothEnabled: false,
      locationEnabled: false,
      wifiEnabled: false,
      deviceOrientation: 'portrait'
    };
  }
}

/**
 * 获取完整的系统信息（推荐使用新API组合）
 * @returns {Object} 完整系统信息
 */
function getCompleteSystemInfo() {
  const deviceInfo = getDeviceInfo();
  const windowInfo = getWindowInfo();
  const appBaseInfo = getAppBaseInfo();
  const systemSetting = getSystemSetting();
  
  return {
    ...deviceInfo,
    ...windowInfo,
    ...appBaseInfo,
    ...systemSetting
  };
}

module.exports = {
  getDeviceInfo,
  getWindowInfo,
  getAppBaseInfo,
  getSystemSetting,
  getCompleteSystemInfo
};