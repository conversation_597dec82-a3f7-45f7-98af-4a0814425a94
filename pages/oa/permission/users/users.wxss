/* pages/oa/permission/users/users.wxss */
@import '/styles/oa-common.wxss';

.container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: 120rpx;
}

/* 统计卡片区域 */
.stats-section {
  display: flex;
  padding: 24rpx 32rpx;
  gap: 16rpx;
}

/* 搜索和筛选区域 */
.search-section {
  background-color: #FFFFFF;
  margin: 0 32rpx 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  padding: 0 20rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #F8F8F8;
}

.filter-btn {
  padding: 0 24rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 26rpx;
  color: #007AFF;
  background-color: transparent;
  border: 1rpx solid #007AFF;
  border-radius: 8rpx;
}

.filter-btn::after {
  border: none;
}

/* 筛选面板 */
.filter-panel {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #E5E5E7;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

.filter-picker {
  flex: 1;
  height: 64rpx;
  padding: 0 20rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #1D1D1F;
}

.arrow {
  color: #8E8E93;
  font-size: 24rpx;
}

.filter-actions {
  margin-top: 24rpx;
  text-align: right;
}

.clear-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #8E8E93;
  background-color: transparent;
  border: 1rpx solid #E5E5E7;
  border-radius: 6rpx;
}

.clear-btn::after {
  border: none;
}

/* 列表区域 */
.list-section {
  margin: 0 32rpx;
}

/* 加载和空状态 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 用户列表 */
.user-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.user-item {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-text {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.user-email {
  font-size: 24rpx;
  color: #8E8E93;
}

.user-status {
  margin-left: 16rpx;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
}

.info-label {
  width: 120rpx;
  font-size: 26rpx;
  color: #8E8E93;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #1D1D1F;
  line-height: 1.4;
}

/* 操作按钮 */
.item-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  line-height: 1;
  color: #FFFFFF;
}

.action-btn::after {
  border: none;
}

.action-btn.assign {
  background-color: #007AFF;
}

/* 加载更多和没有更多 */
.loading-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #8E8E93;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.role-modal {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5E7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 32rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.modal-close::after {
  border: none;
}

.modal-content {
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

/* 角色列表 */
.role-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.role-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.role-item:hover {
  background-color: #F8F8F8;
}

.role-info {
  flex: 1;
  margin-left: 16rpx;
}

.role-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}

.role-desc {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.role-tags {
  display: flex;
  gap: 8rpx;
}

.modal-actions {
  display: flex;
  padding: 24rpx 32rpx 32rpx;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.modal-btn::after {
  border: none;
}

.modal-btn.cancel {
  background-color: #F2F2F7;
  color: #8E8E93;
}

.modal-btn.confirm {
  background-color: #007AFF;
  color: #FFFFFF;
}

.modal-btn[disabled] {
  background-color: #C7C7CC;
  color: #FFFFFF;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-section {
    flex-wrap: wrap;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-label {
    width: auto;
    margin-bottom: 12rpx;
  }

  .filter-picker {
    width: 100%;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .user-status {
    margin-left: 0;
    margin-top: 12rpx;
  }

  .item-content {
    margin-top: 20rpx;
  }

  .info-row {
    flex-direction: column;
  }

  .info-label {
    width: auto;
    margin-bottom: 4rpx;
  }
}