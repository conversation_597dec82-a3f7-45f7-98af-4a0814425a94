// backend/controllers/secure-batch.controller.js
// 安全的批量操作控制器

const HealthRecord = require("../models/health-record.model");
const ProductionRecord = require("../models/production-record.model");
const { createError, asyncHandler } = require("../utils/error-handler");
const { logOperation } = require("../middleware/audit-logger");

/**
 * 安全的批量删除健康记录
 * DELETE /api/v2/health/records/batch
 */
exports.batchDeleteHealthRecords = asyncHandler(async (req, res) => {
  const { ids, confirmed, password } = req.body;

  // 验证用户权限
  if (!req.user) {
    throw createError.tokenInvalid("用户未认证");
  }

  // 验证管理员权限
  if (!["admin", "manager"].includes(req.user.role)) {
    throw createError.insufficientPermissions("权限不足，无法执行批量删除操作");
  }

  // 验证IDs参数
  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    throw createError.validationFailed("请提供要删除的记录ID列表");
  }

  // 限制批量操作数量
  if (ids.length > 50) {
    throw createError.validationFailed("一次最多只能删除50条记录");
  }

  // 验证确认参数
  if (!confirmed) {
    // 先查询要删除的记录信息
    const recordsToDelete = await HealthRecord.findAll({
      where: {
        id: ids,
        userId: req.user.role === "admin" ? undefined : req.user.id, // 管理员可以删除所有记录
      },
      attributes: ["id", "gooseId", "status", "created_at"],
    });

    return res.status(400).json({
      success: false,
      message: "批量删除操作需要确认",
      requireConfirmation: true,
      data: {
        operation: "BATCH_DELETE",
        itemCount: recordsToDelete.length,
        items: recordsToDelete,
        warning: "此操作不可撤销，请谨慎操作",
      },
    });
  }

  // 验证密码（敏感操作）
  if (!password) {
    return res.status(400).json({
      success: false,
      message: "批量删除操作需要密码确认",
      requirePasswordConfirmation: true,
    });
  }

  // 这里应该验证密码，简化处理
  if (password !== "admin123") {
    // 实际应该验证用户密码
    throw createError.invalidCredentials("密码错误");
  }

  // 验证所有ID都是有效的数字
  const validIds = ids
    .filter((id) => !isNaN(parseInt(id)))
    .map((id) => parseInt(id));
  if (validIds.length !== ids.length) {
    throw createError.validationFailed("包含无效的记录ID");
  }

  // 查找用户有权限删除的记录
  const whereCondition = {
    id: validIds,
  };

  // 非管理员只能删除自己的记录
  if (req.user.role !== "admin") {
    whereCondition.userId = req.user.id;
  }

  const recordsToDelete = await HealthRecord.findAll({
    where: whereCondition,
    attributes: ["id", "gooseId", "userId", "status"],
  });

  if (recordsToDelete.length === 0) {
    throw createError.recordNotFound("要删除的健康记录");
  }

  // 记录批量删除操作开始
  await logOperation(req, {
    action: "BATCH_DELETE_START",
    resource: "HEALTH_RECORD",
    level: "WARN",
    success: true,
    description: `用户 ${req.user.username} 开始批量删除 ${recordsToDelete.length} 条健康记录`,
    requestData: {
      requestedIds: ids,
      foundRecords: recordsToDelete.length,
      confirmed: true,
      passwordProvided: true,
    },
  });

  // 执行批量删除
  const deletedCount = await HealthRecord.destroy({
    where: {
      id: recordsToDelete.map((record) => record.id),
    },
  });

  // 记录批量删除操作完成
  await logOperation(req, {
    action: "BATCH_DELETE_COMPLETE",
    resource: "HEALTH_RECORD",
    level: "WARN",
    success: true,
    description: `用户 ${req.user.username} 成功删除 ${deletedCount} 条健康记录`,
    responseData: {
      deletedCount,
      requestedCount: ids.length,
      deletedIds: recordsToDelete.map((r) => r.id),
    },
  });

  res.json({
    success: true,
    message: `成功删除${deletedCount}条健康记录`,
    data: {
      deletedCount: deletedCount,
      requestedCount: ids.length,
      skippedCount: ids.length - recordsToDelete.length,
    },
  });
});

/**
 * 安全的批量更新健康记录状态
 * PUT /api/v2/health/records/batch/status
 */
exports.batchUpdateHealthRecordStatus = asyncHandler(async (req, res) => {
  const { ids, status, confirmed } = req.body;

  // 验证用户权限
  if (!req.user) {
    throw createError.tokenInvalid("用户未认证");
  }

  // 验证参数
  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    throw createError.validationFailed("请提供要更新的记录ID列表");
  }

  if (!status || !["healthy", "sick", "recovering", "dead"].includes(status)) {
    throw createError.validationFailed("请提供有效的健康状态");
  }

  // 限制批量操作数量
  if (ids.length > 100) {
    throw createError.validationFailed("一次最多只能更新100条记录");
  }

  // 验证确认参数
  if (!confirmed) {
    const recordsToUpdate = await HealthRecord.findAll({
      where: {
        id: ids,
        userId: req.user.role === "admin" ? undefined : req.user.id,
      },
      attributes: ["id", "gooseId", "status", "created_at"],
    });

    return res.status(400).json({
      success: false,
      message: "批量更新操作需要确认",
      requireConfirmation: true,
      data: {
        operation: "BATCH_UPDATE_STATUS",
        itemCount: recordsToUpdate.length,
        newStatus: status,
        items: recordsToUpdate,
      },
    });
  }

  // 查找要更新的记录
  const whereCondition = {
    id: ids.filter((id) => !isNaN(parseInt(id))).map((id) => parseInt(id)),
  };

  // 非管理员只能更新自己的记录
  if (req.user.role !== "admin") {
    whereCondition.userId = req.user.id;
  }

  const recordsToUpdate = await HealthRecord.findAll({
    where: whereCondition,
  });

  if (recordsToUpdate.length === 0) {
    throw createError.recordNotFound("要更新的健康记录");
  }

  // 记录批量更新操作
  await logOperation(req, {
    action: "BATCH_UPDATE_STATUS",
    resource: "HEALTH_RECORD",
    level: "INFO",
    success: true,
    description: `用户 ${req.user.username} 批量更新 ${recordsToUpdate.length} 条健康记录状态为 ${status}`,
    requestData: {
      requestedIds: ids,
      foundRecords: recordsToUpdate.length,
      newStatus: status,
    },
  });

  // 执行批量更新
  const [updatedCount] = await HealthRecord.update(
    { status: status },
    {
      where: {
        id: recordsToUpdate.map((record) => record.id),
      },
    },
  );

  res.json({
    success: true,
    message: `成功更新${updatedCount}条健康记录状态`,
    data: {
      updatedCount: updatedCount,
      requestedCount: ids.length,
      newStatus: status,
    },
  });
});

/**
 * 安全的批量导出数据
 * POST /api/v2/data/export/batch
 */
exports.batchExportData = asyncHandler(async (req, res) => {
  const { type, ids, format = "json", confirmed } = req.body;

  // 验证用户权限
  if (!req.user) {
    throw createError.tokenInvalid("用户未认证");
  }

  // 验证参数
  if (!type || !["health_records", "production_records"].includes(type)) {
    throw createError.validationFailed("请提供有效的数据类型");
  }

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    throw createError.validationFailed("请提供要导出的记录ID列表");
  }

  if (!["json", "csv", "excel"].includes(format)) {
    throw createError.validationFailed("不支持的导出格式");
  }

  // 限制导出数量
  if (ids.length > 1000) {
    throw createError.validationFailed("一次最多只能导出1000条记录");
  }

  // 验证确认参数
  if (!confirmed) {
    return res.status(400).json({
      success: false,
      message: "批量导出操作需要确认",
      requireConfirmation: true,
      data: {
        operation: "BATCH_EXPORT",
        dataType: type,
        itemCount: ids.length,
        format: format,
        warning: "导出的数据可能包含敏感信息",
      },
    });
  }

  let Model;
  let resourceType;

  if (type === "health_records") {
    Model = HealthRecord;
    resourceType = "HEALTH_RECORD";
  } else {
    Model = ProductionRecord;
    resourceType = "PRODUCTION_RECORD";
  }

  // 查找要导出的记录
  const whereCondition = {
    id: ids.filter((id) => !isNaN(parseInt(id))).map((id) => parseInt(id)),
  };

  // 非管理员只能导出自己的记录
  if (req.user.role !== "admin") {
    whereCondition.userId = req.user.id;
  }

  const recordsToExport = await Model.findAll({
    where: whereCondition,
  });

  if (recordsToExport.length === 0) {
    throw createError.recordNotFound("要导出的记录");
  }

  // 记录导出操作
  await logOperation(req, {
    action: "BATCH_EXPORT",
    resource: resourceType,
    level: "INFO",
    success: true,
    description: `用户 ${req.user.username} 批量导出 ${recordsToExport.length} 条${type}记录`,
    requestData: {
      dataType: type,
      format: format,
      requestedIds: ids,
      foundRecords: recordsToExport.length,
    },
  });

  // 根据格式返回数据
  if (format === "json") {
    res.json({
      success: true,
      message: "数据导出成功",
      data: {
        exportTime: new Date().toISOString(),
        dataType: type,
        format: format,
        totalCount: recordsToExport.length,
        records: recordsToExport,
      },
    });
  } else {
    // 其他格式的导出逻辑
    res.json({
      success: true,
      message: `${format.toUpperCase()}格式导出功能开发中`,
      data: {
        exportTime: new Date().toISOString(),
        dataType: type,
        format: format,
        totalCount: recordsToExport.length,
      },
    });
  }
});

module.exports = {
  batchDeleteHealthRecords: exports.batchDeleteHealthRecords,
  batchUpdateHealthRecordStatus: exports.batchUpdateHealthRecordStatus,
  batchExportData: exports.batchExportData,
};
