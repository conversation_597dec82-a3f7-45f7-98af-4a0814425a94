<!-- pages/management/dashboard/dashboard.wxml -->
<view class="dashboard-container">
  <!-- 加载状态 -->
  <c-loading wx:if="{{loading}}" message="加载中..." />
  
  <!-- 仪表板内容 -->
  <view wx:else class="dashboard-content">
    
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">管理中心</text>
      <text class="page-subtitle">数据概览与快捷管理</text>
    </view>
    
    <!-- 数据概览卡片 -->
    <c-card padding="lg" margin="md">
      <c-section-header title="数据概览" showMore="{{true}}" bind:moretap="onViewMoreOverview" />
      <view class="overview-grid">
        <view class="overview-item">
          <text class="overview-value">{{overview.totalStaff}}</text>
          <text class="overview-label">总员工数</text>
          <text class="overview-detail">活跃: {{overview.activeStaff}}</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{overview.totalFlocks}}</text>
          <text class="overview-label">鹅群数量</text>
          <text class="overview-detail">总数: {{overview.totalGeese}}</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{overview.todayProduction}}</text>
          <text class="overview-label">今日产蛋</text>
          <text class="overview-detail">个</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">¥{{overview.monthlyRevenue}}</text>
          <text class="overview-label">月度收入</text>
          <text class="overview-detail">预估</text>
        </view>
      </view>
    </c-card>

    <!-- 待处理事项 -->
    <c-card padding="lg" margin="md">
      <c-section-header title="待处理事项" />
      <view class="pending-tasks-grid">
        <view class="pending-task-item" bindtap="onPendingTaskTap" data-type="approvals">
          <view class="task-icon-wrapper approval">
            <image class="task-icon" src="/images/icons/approval.png" mode="aspectFit" />
            <view wx:if="{{pendingTasks.approvals > 0}}" class="task-badge">{{pendingTasks.approvals}}</view>
          </view>
          <text class="task-label">待审批</text>
        </view>
        <view class="pending-task-item" bindtap="onPendingTaskTap" data-type="alerts">
          <view class="task-icon-wrapper alert">
            <image class="task-icon" src="/images/icons/alert.png" mode="aspectFit" />
            <view wx:if="{{pendingTasks.alerts > 0}}" class="task-badge">{{pendingTasks.alerts}}</view>
          </view>
          <text class="task-label">告警信息</text>
        </view>
        <view class="pending-task-item" bindtap="onPendingTaskTap" data-type="reports">
          <view class="task-icon-wrapper report">
            <image class="task-icon" src="/images/icons/report.png" mode="aspectFit" />
            <view wx:if="{{pendingTasks.reports > 0}}" class="task-badge">{{pendingTasks.reports}}</view>
          </view>
          <text class="task-label">待处理报表</text>
        </view>
        <view class="pending-task-item" bindtap="onPendingTaskTap" data-type="maintenance">
          <view class="task-icon-wrapper maintenance">
            <image class="task-icon" src="/images/icons/maintenance.png" mode="aspectFit" />
            <view wx:if="{{pendingTasks.maintenance > 0}}" class="task-badge">{{pendingTasks.maintenance}}</view>
          </view>
          <text class="task-label">设备维护</text>
        </view>
      </view>
    </c-card>

    <!-- 快捷功能 -->
    <c-card padding="lg" margin="md">
      <c-section-header title="快捷功能" />
      <view class="quick-actions-grid">
        <view 
          wx:for="{{quickActions}}" 
          wx:key="id" 
          class="quick-action-item"
          bindtap="onQuickActionTap"
          data-url="{{item.url}}"
          data-id="{{item.id}}"
        >
          <view class="action-icon-wrapper">
            <image class="action-icon" src="{{item.icon}}" mode="aspectFit" lazy-load="{{true}}" />
          </view>
          <view class="action-content">
            <text class="action-title">{{item.title}}</text>
            <text class="action-desc">{{item.description}}</text>
          </view>
          <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit" />
        </view>
      </view>
    </c-card>

    <!-- 最近活动 -->
    <c-card wx:if="{{recentActivities && recentActivities.length > 0}}" padding="lg" margin="md">
      <c-section-header title="最近活动" />
      <view class="recent-activities">
        <view 
          wx:for="{{recentActivities}}" 
          wx:key="id" 
          class="activity-item"
        >
          <view class="activity-time">{{item.time}}</view>
          <view class="activity-content">
            <text class="activity-title">{{item.title}}</text>
            <text class="activity-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </c-card>

  </view>
</view>