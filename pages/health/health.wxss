/* pages/health/health.wxss - 健康页面样式 V3.0 优化版 */

/* 容器 - 统一设计系统 */
.health-container {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--bg-secondary) 0%, rgba(240, 255, 244, 0.3) 100%);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面进入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab导航系统 - 健康主题优化 */
.tabs {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(240, 255, 244, 0.8) 100%);
  padding: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(20rpx);
  box-shadow: var(--shadow-sm);
}

.tab-list {
  display: flex;
  background: linear-gradient(135deg, var(--success-bg) 0%, rgba(240, 255, 244, 0.6) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-xs);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--success-light);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
}

.tab-list::before {
  content: '';
  position: absolute;
  top: var(--space-xs);
  left: var(--space-xs);
  width: calc(25% - var(--space-xs));
  height: calc(100% - var(--space-sm));
  background: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
  border-radius: var(--radius-xl);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  box-shadow: var(--shadow-sm);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: var(--space-lg) var(--space-md);
  font-size: var(--text-base);
  color: var(--text-secondary);
  position: relative;
  border-radius: var(--radius-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: var(--font-medium);
  z-index: 2;
  cursor: pointer;
}

.tab-item.active {
  color: var(--text-inverse);
  font-weight: var(--font-semibold);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* Tab指示器位置调整 */
.tab-list[data-active-index="0"]::before {
  transform: translateX(0);
}

.tab-list[data-active-index="1"]::before {
  transform: translateX(calc(100% + var(--space-xs)));
}

.tab-list[data-active-index="2"]::before {
  transform: translateX(calc(200% + var(--space-sm)));
}

.tab-list[data-active-index="3"]::before {
  transform: translateX(calc(300% + var(--space-lg)));
}

/* 内容区域 */
.content {
  padding: var(--space-lg) var(--space-lg) var(--space-2xl);
  background-color: var(--bg-secondary);
  min-height: calc(100vh - 200rpx);
}

/* 添加记录按钮 - 记录列表下方样式 */
.add-record-btn {
  margin: 32rpx 0 16rpx 0;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.add-record-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.2);
}

.add-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
  filter: brightness(0) invert(1);
}

.add-record-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

/* 记录列表 - 简洁版 */
.records-list {
  padding: 32rpx 16rpx;
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.record-item {
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  background: #ffffff;
  border: 1rpx solid #e8e8e8;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.record-item:active {
  background: #f8f9fa;
  transform: scale(0.98);
  border-color: #d0d0d0;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.record-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}

/* 状态文字 - 纯文字样式 */
.record-status {
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;
  font-size: 24rpx;
  line-height: 1.3;
  transition: all 0.2s ease;
}

/* 死亡状态 - 红色文字 */
.status-death {
  color: #ff4d4f;
}

/* 已完成状态 - 蓝色文字 */
.status-completed {
  color: #1890ff;
}

/* 已治愈状态 - 橙色文字 */
.status-cured {
  color: #fa8c16;
}

/* 健康状态 - 绿色文字 */
.status-healthy {
  color: #52c41a;
}

/* 入栏记录状态 - 绿色文字 */
.status-entry {
  color: #52c41a;
}

/* 防疫记录状态 - 蓝色文字 */
.status-vaccination {
  color: #1890ff;
}

/* 生病记录状态 - 橙色文字 */
.status-sick {
  color: #fa8c16;
}

/* 治疗记录状态 - 紫色文字 */
.status-treatment {
  color: #eb2f96;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }

  50% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1.2);
  }
}

.record-date {
  font-size: 24rpx;
  color: #52c41a;
  margin-bottom: 12rpx;
  font-weight: 500;
  background-color: #f6ffed;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  border: 1rpx solid #b7eb8f;
  display: inline-block;
}

.record-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: var(--space-3xl) var(--space-xl);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-2xl);
  border: 2rpx dashed var(--border-medium);
  margin: var(--space-xl) 0;
}

.empty::before {
  content: '🏥';
  display: block;
  font-size: 80rpx;
  margin-bottom: var(--space-lg);
  opacity: 0.6;
}

.empty image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: var(--space-lg);
  opacity: 0.4;
  filter: grayscale(100%);
}

.empty text {
  font-size: var(--text-lg);
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
}

/* AI诊断页面样式 - 优化版 */
.ai-diagnosis-section {
  padding: var(--space-lg);
  background: linear-gradient(180deg, rgba(240, 255, 244, 0.3) 0%, var(--bg-secondary) 100%);
  min-height: calc(100vh - 200rpx);
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
  animation-delay: 0.3s;
}

.card {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10rpx);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--info) 0%, var(--info-light) 100%);
}

.card::after {
  content: '🤖';
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  font-size: var(--text-xl);
  opacity: 0.3;
}

.card:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: var(--shadow-lg);
  border-color: var(--info);
}

.card-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-2xl);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  position: relative;
  z-index: 1;
}

.card-title::before {
  content: '';
  width: 6rpx;
  height: 36rpx;
  background: linear-gradient(135deg, var(--info) 0%, var(--info-light) 100%);
  border-radius: var(--radius-full);
}

/* 表单输入 - 优化版 */
.symptom-input {
  width: 100%;
  min-height: 200rpx;
  padding: var(--space-lg);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: var(--text-base);
  font-family: var(--font-family);
  line-height: var(--leading-relaxed);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(240, 255, 244, 0.3) 100%);
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  resize: vertical;
  box-sizing: border-box;
  backdrop-filter: blur(4rpx);
}

.symptom-input:focus {
  border-color: var(--info);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--info-bg) 100%);
  box-shadow: 0 0 0 4rpx var(--info-bg);
  transform: scale(1.02);
}

.symptom-input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
}

/* 图片上传区域 - 优化版 */
.image-upload {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180rpx, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-lg);
  position: relative;
  z-index: 1;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  /* 创建1:1的宽高比 */
  border-radius: var(--radius-xl);
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(240, 255, 244, 0.3) 100%);
  border: 2rpx solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.uploaded-image image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-lg);
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: var(--error);
  color: var(--text-inverse);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  box-shadow: var(--shadow-md);
  border: 2rpx solid var(--bg-primary);
  transition: var(--transition-all);
}

.delete-btn:active {
  transform: scale(0.9);
  background-color: var(--error-dark);
}

.upload-btn {
  /* aspect-ratio: 1; */
  /* 小程序可能不支持，使用固定尺寸 */
  width: 180rpx;
  height: 180rpx;
  border: 2rpx dashed var(--border-medium);
  border-radius: var(--radius-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  transition: var(--transition-all);
  cursor: pointer;
}

.upload-btn:active {
  border-color: var(--primary);
  background-color: var(--primary-subtle);
  transform: scale(0.98);
}

.upload-btn image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: var(--space-sm);
  opacity: 0.6;
}

.upload-btn text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.upload-tip {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--space-sm);
  text-align: center;
}

/* 按钮系统 */
.btn {
  padding: var(--space-lg) var(--space-2xl);
  border-radius: var(--radius-xl);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  border: none;
  transition: var(--transition-all);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.btn-primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-md);
}

.btn-block {
  width: 100%;
}

/* 可访问性支持 */
@media (prefers-contrast: high) {

  .add-record-btn,
  .records-list .record-item,
  .ai-diagnosis-section,
  .symptom-input {
    border-width: 2rpx;
    border-color: var(--text-primary);
  }

  .record-item .record-title,
  .record-item .record-date,
  .diagnosis-title {
    font-weight: var(--font-bold);
  }

  .record-status {
    border-width: 2rpx;
  }
}

@media (prefers-reduced-motion: reduce) {

  .health-container,
  .add-record-btn,
  .records-list .record-item,
  .ai-diagnosis-section {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .record-status.healthy::before {
    animation: none !important;
  }
}

/* 知识库页面样式 */
.knowledge-section {
  padding: 20rpx;
  background-color: #f5f6f8;
  min-height: calc(100vh - 200rpx);
}

.search-bar {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 0 20rpx;
}

.search-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 60rpx;
  background-color: #0066cc;
  color: #ffffff;
  border: none;
  border-radius: 10rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

.category-scroll {
  margin-bottom: 20rpx;
}

.category-list {
  display: flex;
  gap: 20rpx;
  padding: 0 20rpx;
}

.category-item {
  padding: 16rpx 32rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666666;
  white-space: nowrap;
}

.category-item.active {
  background-color: #0066cc;
  color: #ffffff;
}

.articles-container {
  padding: 0;
}

.article-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 102, 204, 0.1);
  transition: all 0.3s ease;
}

.article-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 102, 204, 0.15);
}

.article-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20rpx;
  line-height: 1.5;
  position: relative;
  padding-left: 20rpx;
}

.article-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  border-radius: 4rpx;
}

.article-meta {
  display: flex;
  gap: 25rpx;
  margin-bottom: 20rpx;
  align-items: center;
}

.article-category {
  padding: 10rpx 20rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #D1EAFF 100%);
  color: #0066CC;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid rgba(0, 102, 204, 0.2);
}

.article-date,
.article-read-count {
  font-size: 26rpx;
  color: #0066CC;
  font-weight: 500;
}

.article-summary {
  font-size: 28rpx;
  color: #555555;
  line-height: 1.7;
  padding-left: 20rpx;
}

/* 健康报告页面样式 */
.report-section {
  padding: 20rpx;
  background-color: #f5f6f8;
  min-height: calc(100vh - 200rpx);
}

.report-types {
  margin-bottom: 30rpx;
}

.type-list {
  display: flex;
  gap: 20rpx;
  padding: 0 20rpx;
}

.type-item {
  padding: 16rpx 32rpx;
  background-color: #ffffff;
  border-radius: 25rpx;
  font-size: 28rpx;
  color: #666666;
  white-space: nowrap;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.type-item.active {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  font-weight: 600;
  border: 2rpx solid var(--primary-subtle);
  box-shadow: var(--shadow-primary);
}

.overview-card,
.disease-card,
.treatment-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 102, 204, 0.1);
}

.export-btn {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-2xl);
  padding: var(--space-md) var(--space-xl);
  font-size: var(--text-sm);
  font-weight: 500;
  box-shadow: var(--shadow-primary);
}

.overview-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: 30rpx 0;
}

.stat-item {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.stat-item.healthy {
  background: linear-gradient(135deg, #E6F9F8 0%, #D1F5F0 100%);
  border-color: rgba(0, 204, 153, 0.3);
}

.stat-item.sick {
  background: linear-gradient(135deg, #FFF3E6 0%, #FFE8CC 100%);
  border-color: rgba(255, 153, 0, 0.3);
}

.stat-item.death {
  background: linear-gradient(135deg, #FFE6E6 0%, #FFCCCC 100%);
  border-color: rgba(255, 102, 102, 0.3);
}

.stat-value {
  font-size: 52rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.stat-label {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
  line-height: 1.4;
}

.health-rate {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #D1EAFF 100%);
  border-radius: 16rpx;
  border: 2rpx solid rgba(0, 102, 204, 0.2);
}

.rate-label {
  font-size: 30rpx;
  color: #0066CC;
  font-weight: 600;
}

.rate-value {
  font-size: 36rpx;
  color: #0066CC;
  font-weight: 700;
}

.disease-stats {
  margin-top: 30rpx;
}

.disease-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid rgba(0, 102, 204, 0.1);
  transition: all 0.3s ease;
}

.disease-item:last-child {
  border-bottom: none;
}

.disease-item:active {
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.05) 0%, rgba(0, 153, 204, 0.05) 100%);
  transform: translateX(10rpx);
  border-radius: 12rpx;
  margin: 0 -20rpx;
  padding: 25rpx 20rpx;
}

.disease-name {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 500;
  position: relative;
  padding-left: 20rpx;
}

.disease-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #FF8800 0%, #FF6600 100%);
  border-radius: 3rpx;
}

.disease-rate {
  font-size: 28rpx;
  color: #FF8800;
  font-weight: 600;
  background: rgba(255, 136, 0, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.treatment-stats {
  margin-top: 30rpx;
}

.treatment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid rgba(0, 102, 204, 0.1);
  transition: all 0.3s ease;
}

.treatment-item:last-child {
  border-bottom: none;
}

.treatment-item:active {
  background: linear-gradient(135deg, rgba(0, 204, 153, 0.05) 0%, rgba(0, 170, 136, 0.05) 100%);
  transform: translateX(10rpx);
  border-radius: 12rpx;
  margin: 0 -20rpx;
  padding: 25rpx 20rpx;
}

.treatment-name {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 500;
  position: relative;
  padding-left: 20rpx;
}

.treatment-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #00CC99 0%, #00AA88 100%);
  border-radius: 3rpx;
}

.treatment-rate {
  font-size: 28rpx;
  color: #00AA88;
  font-weight: 600;
  background: rgba(0, 204, 153, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* ==================== 物料管理样式 ==================== */
.materials-section {
  padding: 20rpx;
}

/* 物料统计 */
.material-stats {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.stats-item {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.stats-value {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #007aff;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #8e8e93;
  display: block;
}

.stats-item.warning .stats-value {
  color: #ff9500;
}

/* 快捷操作按钮 */
.quick-actions {
  margin-bottom: 24rpx;
}

.add-material-btn {
  width: 100%;
  background-color: #007aff;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 物料标签页 */
.material-tabs {
  margin-bottom: 24rpx;
}

.tab-list {
  display: flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.tab-item {
  padding: 16rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666666;
  white-space: nowrap;
}

.tab-item.active {
  background-color: #007aff;
  color: #ffffff;
}

/* 物料列表 */
.material-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.material-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.material-info {
  flex: 1;
}

.material-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8rpx;
}

.material-spec {
  font-size: 24rpx;
  color: #8e8e93;
}

.material-stock {
  text-align: right;
}

.stock-count {
  display: block;
  font-size: 26rpx;
  color: #1d1d1f;
  margin-bottom: 4rpx;
}

.stock-status {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.stock-status.normal {
  background-color: #f6ffed;
  color: #52c41a;
}

.stock-status.warning {
  background-color: #fff7e6;
  color: #fa8c16;
}

.stock-status.danger {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* ==================== 生产管理样式 ==================== */
.production-section {
  padding: 20rpx;
}

/* 添加记录按钮 */
.add-record-section {
  margin-bottom: 24rpx;
}

.add-record-btn {
  width: 100%;
  background-color: #52c41a;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

/* 记录类型筛选 */
.record-types {
  margin-bottom: 24rpx;
}

.type-list {
  display: flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.type-item {
  padding: 16rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666666;
  white-space: nowrap;
}

.type-item.active {
  background-color: #52c41a;
  color: #ffffff;
}

/* 生产记录列表 */
.production-records {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.record-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-type {
  display: flex;
  align-items: center;
}

.type-label {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 500;
}

.type-entry {
  background-color: #52c41a;
}

.type-weight {
  background-color: #1890ff;
}

.type-sale {
  background-color: #fa8c16;
}

.record-date {
  font-size: 24rpx;
  color: #8e8e93;
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.batch-label {
  font-size: 24rpx;
  color: #8e8e93;
}

.batch-value {
  font-size: 26rpx;
  color: #1d1d1f;
  font-weight: 600;
}

.record-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.detail-item {
  font-size: 24rpx;
  color: #666666;
  padding: 4rpx 8rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.detail-item.income {
  background-color: #f6ffed;
  color: #52c41a;
  font-weight: 600;
}

/* 底部间距占位 */
.bottom-spacing {
  height: 40rpx;
}

/* ==================== 二级标签页样式 ==================== */
.health-sub-tabs {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e0e0e0;
  margin-bottom: 20rpx;
}

.sub-tab-list {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 24rpx;
}

.sub-tab-item {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #666666;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.sub-tab-item.active {
  background-color: #007aff;
  color: #ffffff;
  font-weight: 600;
}

/* 健康记录内容区 */
.health-records-content {
  padding: 0 20rpx;
}

/* 健康报告内容区 */
.health-report-content {
  padding: 0 20rpx;
}

/* AI诊断内容区 */
.ai-diagnosis-content {
  padding: 0 20rpx;
}

.diagnosis-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

