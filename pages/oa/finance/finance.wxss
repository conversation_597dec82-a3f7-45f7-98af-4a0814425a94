@import '/styles/oa-common.wxss';
/* pages/oa/finance/finance.wxss */

/* ==================== 财务管理页面特有样式 ==================== */

/* ==================== 统计详情模态框 ==================== */
.stat-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.stat-modal-content {
  background: white;
  border-radius: var(--radius-xl);
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.stat-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xl) var(--space-xl) var(--space-lg);
  border-bottom: 2rpx solid var(--border-light);
}

.stat-modal-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.stat-modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  color: var(--text-secondary);
}

.stat-modal-close:active {
  background: var(--bg-tertiary);
}

.stat-modal-main {
  padding: var(--space-xl);
}

.stat-modal-total {
  text-align: center;
  padding-bottom: var(--space-xl);
  border-bottom: 2rpx solid var(--border-light);
  margin-bottom: var(--space-xl);
}

.stat-modal-value {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-sm);
}

.stat-modal-trend {
  font-size: var(--text-sm);
  padding: 8rpx 16rpx;
  border-radius: var(--radius-md);
  background: var(--success-light);
  color: var(--success);
}

.breakdown-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.breakdown-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.breakdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.breakdown-info {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.breakdown-label {
  font-size: var(--text-base);
  color: var(--text-primary);
}

.breakdown-percent {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 4rpx 8rpx;
  border-radius: var(--radius-sm);
}

.breakdown-amount {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--primary);
}



/* 时间范围选择器 */
.time-range-selector {
  margin-bottom: var(--space-xl);
}

.picker-arrow {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  transition: transform 0.2s ease;
}

/* ==================== 快捷操作网格 ==================== */
.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: var(--space-xl);
  border-radius: var(--radius-xl);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
  overflow: hidden;
}

.quick-action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.quick-action-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 102, 204, 0.15);
}

.quick-action-item:active::before {
  left: 100%;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.action-icon image {
  width: 48rpx;
  height: 48rpx;
}

.icon-text {
  font-size: 40rpx;
  line-height: 1;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.action-title {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.action-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.action-arrow {
  color: var(--text-tertiary);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
}

/* ==================== 统计概览网格 ==================== */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-top: 24rpx;
}

.stat-item {
  background: linear-gradient(135deg, var(--bg-primary) 0%, #f8fafc 100%);
  border-radius: var(--radius-xl);
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 2rpx solid var(--border-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  min-height: 120rpx;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary), var(--primary-dark));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-item:active {
  transform: translateY(-6rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 102, 204, 0.12);
}

.stat-item:active::before {
  transform: scaleX(1);
}

/* 图标样式已移除 - 财务概览组件不再使用图标 */

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  width: 100%;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-trend {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 600;
  margin-top: 6rpx;
}

.stat-trend.positive {
  background: var(--success-bg);
  color: var(--success);
  border: 1rpx solid var(--success);
}

.stat-trend.negative {
  background: var(--error-bg);
  color: var(--error);
  border: 1rpx solid var(--error);
}

.stat-trend.stable {
  background: var(--info-bg);
  color: var(--info);
  border: 1rpx solid var(--info);
}

/* ==================== 最近活动样式 ==================== */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.view-all-btn {
  font-size: var(--text-sm);
  color: var(--primary);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  background: rgba(74, 144, 226, 0.1);
  border: 1rpx solid rgba(74, 144, 226, 0.2);
  transition: all 0.2s ease;
}

.view-all-btn:active {
  background: rgba(74, 144, 226, 0.2);
  transform: scale(0.95);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.activity-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.activity-icon.income {
  background: var(--success-bg);
  color: var(--success);
  border: 1rpx solid var(--success);
}

.activity-icon.expense {
  background: var(--warning-bg);
  color: var(--warning);
  border: 1rpx solid var(--warning);
}

.activity-icon.transfer {
  background: var(--info-bg);
  color: var(--info);
  border: 1rpx solid var(--info);
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  min-width: 0;
}

.activity-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.activity-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.activity-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.activity-amount {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  flex-shrink: 0;
}

.activity-amount.income {
  color: var(--success);
}

.activity-amount.expense {
  color: var(--warning);
}

.activity-amount.transfer {
  color: var(--info);
}

/* ==================== 提醒样式 ==================== */
.alerts-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.alert-text {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.alert-time {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.alert-action {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-lg);
  background: var(--primary);
  color: var(--text-inverse);
  border: none;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.alert-action:active {
  background: var(--primary-dark);
  transform: scale(0.95);
}

/* ==================== 空状态样式 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-3xl) var(--space-xl);
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.6;
}

.empty-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin-bottom: var(--space-xl);
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 600rpx) {
  .statistics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .quick-actions-grid {
    gap: var(--space-md);
  }

  .quick-action-item {
    padding: var(--space-lg);
  }

  .action-icon {
    width: 64rpx;
    height: 64rpx;
  }

  .activity-item {
    gap: var(--space-md);
  }
}