/* 优化后的生产记录项组件样式 */

.production-record-item {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 16rpx 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.production-record-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

/* 记录头部 */
.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.header-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.header-right {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
}

.header-right-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6rpx;
}

/* 记录类型徽章 */
.type-badge {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  width: fit-content;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.type-entry {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.type-weight {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.type-sale {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.type-icon {
  font-size: 20rpx;
}

.type-text {
  font-size: 22rpx;
  font-weight: 600;
}

/* 批次信息 */
.batch-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.batch-prefix {
  font-size: 22rpx;
  color: #8c8c8c;
}

.batch-number {
  font-size: 26rpx;
  font-weight: 600;
  color: #262626;
  background: #f6f6f6;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e8e8e8;
}

/* 日期信息 */
.date-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2rpx;
  margin-bottom: 4rpx;
}

.date-text {
  font-size: 22rpx;
  font-weight: 500;
  color: #262626;
}

.date-weekday {
  font-size: 18rpx;
  color: #8c8c8c;
}

/* 箭头指示器 */
.arrow-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  background: #f6f6f6;
  border-radius: 50%;
  border: 1rpx solid #e8e8e8;
  align-self: center;
}

.arrow {
  font-size: 18rpx;
  color: #8c8c8c;
  font-weight: bold;
}

/* 记录内容 */
.record-content {
  margin-top: 16rpx;
}

.content-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.content-row:last-child {
  margin-bottom: 0;
}

.content-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
}

.item-label {
  font-size: 20rpx;
  color: #8c8c8c;
  font-weight: 400;
}

.item-value {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.item-value.cost {
  color: #ff4d4f;
  font-weight: 600;
}

.item-value.income {
  color: #52c41a;
  font-weight: 600;
}

/* 财务关联徽章 */
.finance-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  border: 1rpx solid #91d5ff;
  border-radius: 16rpx;
  width: fit-content;
  margin-left: auto;
}

.finance-icon {
  font-size: 18rpx;
}

.finance-text {
  font-size: 20rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 状态指示器 - 优化后的小巧设计 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  width: fit-content;
  margin-bottom: 4rpx;
}

.status-dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-text {
  font-size: 18rpx;
  font-weight: 400;
  white-space: nowrap;
}

.status-pending {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
  border: 1rpx solid rgba(250, 140, 22, 0.2);
}

.status-pending .status-dot {
  background: #fa8c16;
}

.status-approved {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border: 1rpx solid rgba(82, 196, 26, 0.2);
}

.status-approved .status-dot {
  background: #52c41a;
}

.status-completed {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
  border: 1rpx solid rgba(140, 140, 140, 0.2);
}

.status-completed .status-dot {
  background: #8c8c8c;
}

/* 不同记录类型的特殊样式 */
.entry-content {
  border-left: 4rpx solid #52c41a;
  padding-left: 16rpx;
  background: linear-gradient(90deg, rgba(82, 196, 26, 0.02), transparent);
}

.sale-content {
  border-left: 4rpx solid #fa8c16;
  padding-left: 16rpx;
  background: linear-gradient(90deg, rgba(250, 140, 22, 0.02), transparent);
}

.weight-content {
  border-left: 4rpx solid #1890ff;
  padding-left: 16rpx;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.02), transparent);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .content-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
  
  .content-item {
    width: 100%;
  }
  
  .finance-badge {
    margin-left: 0;
    margin-top: 8rpx;
  }
}

/* 动画效果 */
.production-record-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 悬停效果（支持的设备） */
@media (hover: hover) {
  .production-record-item:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.15);
  }
  
  .production-record-item:hover .arrow {
    color: #1890ff;
  }
  
  .production-record-item:hover .arrow-indicator {
    background: #e6f7ff;
    border-color: #91d5ff;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .production-record-item {
    background: #1f1f1f;
    border-color: #303030;
    color: #ffffff;
  }
  
  .batch-number {
    background: #303030;
    color: #ffffff;
    border-color: #434343;
  }
  
  .arrow-indicator {
    background: #303030;
    border-color: #434343;
  }
  
  .date-text, .item-value {
    color: #ffffff;
  }
  
  .batch-prefix, .date-weekday, .item-label, .arrow {
    color: #a6a6a6;
  }
}
