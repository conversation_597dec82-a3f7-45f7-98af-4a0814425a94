// backend/middleware/audit-logger.js
// 操作日志记录中间件

const AuditLog = require("../models/audit-log.model");

/**
 * 数据脱敏函数
 * @param {Object} data - 要脱敏的数据
 * @returns {Object} 脱敏后的数据
 */
function sanitizeData(data) {
  if (!data || typeof data !== "object") {
    return data;
  }

  const sensitiveFields = [
    "password",
    "token",
    "apiKey",
    "secret",
    "key",
    "authorization",
    "cookie",
    "session",
  ];

  const sanitized = { ...data };

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = "***MASKED***";
    }
  }

  // 递归处理嵌套对象
  for (const key in sanitized) {
    if (typeof sanitized[key] === "object" && sanitized[key] !== null) {
      sanitized[key] = sanitizeData(sanitized[key]);
    }
  }

  return sanitized;
}

/**
 * 获取客户端IP地址
 * @param {Object} req - 请求对象
 * @returns {string} IP地址
 */
function getClientIP(req) {
  return (
    req.ip ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
    req.headers["x-forwarded-for"]?.split(",")[0] ||
    req.headers["x-real-ip"] ||
    "unknown"
  );
}

/**
 * 解析操作类型和资源
 * @param {string} method - HTTP方法
 * @param {string} url - 请求URL
 * @returns {Object} 操作类型和资源信息
 */
function parseActionAndResource(method, url) {
  const urlParts = url.split("/").filter((part) => part);

  let action = method.toUpperCase();
  let resource = "UNKNOWN";
  let resourceId = null;
  let category = "API";

  // 根据URL路径解析资源类型
  if (url.includes("/auth/")) {
    resource = AuditLog.RESOURCES.USER;
    category = "AUTH";

    if (url.includes("/login")) action = AuditLog.ACTIONS.LOGIN;
    else if (url.includes("/logout")) action = AuditLog.ACTIONS.LOGOUT;
    else if (url.includes("/register")) action = AuditLog.ACTIONS.REGISTER;
    else if (url.includes("/password"))
      action = AuditLog.ACTIONS.CHANGE_PASSWORD;
  } else if (url.includes("/health/")) {
    resource = AuditLog.RESOURCES.HEALTH_RECORD;
    category = "HEALTH";

    if (method === "POST") action = AuditLog.ACTIONS.CREATE;
    else if (method === "GET") action = AuditLog.ACTIONS.READ;
    else if (method === "PUT" || method === "PATCH")
      action = AuditLog.ACTIONS.UPDATE;
    else if (method === "DELETE") action = AuditLog.ACTIONS.DELETE;
  } else if (url.includes("/production/")) {
    resource = AuditLog.RESOURCES.PRODUCTION_RECORD;
    category = "PRODUCTION";

    if (method === "POST") action = AuditLog.ACTIONS.CREATE;
    else if (method === "GET") action = AuditLog.ACTIONS.READ;
    else if (method === "PUT" || method === "PATCH")
      action = AuditLog.ACTIONS.UPDATE;
    else if (method === "DELETE") action = AuditLog.ACTIONS.DELETE;
  } else if (url.includes("/inventory/")) {
    resource = AuditLog.RESOURCES.INVENTORY;
    category = "INVENTORY";
  } else if (url.includes("/announcement/")) {
    resource = AuditLog.RESOURCES.ANNOUNCEMENT;
    category = "ANNOUNCEMENT";
  } else if (url.includes("/admin/")) {
    category = "ADMIN";
  } else if (url.includes("/ai/")) {
    resource = AuditLog.RESOURCES.AI_CONFIG;
    category = "AI";

    if (url.includes("/diagnosis")) action = AuditLog.ACTIONS.AI_DIAGNOSIS;
  }

  // 提取资源ID
  const idMatch = url.match(/\/(\d+)(?:\/|$)/);
  if (idMatch) {
    resourceId = idMatch[1];
  }

  return { action, resource, resourceId, category };
}

/**
 * 操作日志记录中间件
 * @param {Object} options - 配置选项
 * @returns {Function} Express中间件函数
 */
function auditLogger(options = {}) {
  const {
    enabled = true,
    logLevel = AuditLog.LEVELS.INFO,
    excludePaths = ["/health", "/ping", "/favicon.ico"],
    excludeMethods = [],
    includeRequestBody = true,
    includeResponseBody = false,
    maxBodySize = 10000, // 最大记录的请求/响应体大小
  } = options;

  return (req, res, next) => {
    // 如果禁用日志记录，直接跳过
    if (!enabled) {
      return next();
    }

    // 检查是否需要排除此路径
    const shouldExclude = excludePaths.some((path) =>
      req.originalUrl.includes(path),
    );
    if (shouldExclude) {
      return next();
    }

    // 检查是否需要排除此HTTP方法
    if (excludeMethods.includes(req.method)) {
      return next();
    }

    const startTime = Date.now();

    // 解析操作信息
    const { action, resource, resourceId, category } = parseActionAndResource(
      req.method,
      req.originalUrl,
    );

    // 准备日志数据
    const logData = {
      userId: req.user?.id || null,
      username: req.user?.username || null,
      action: action,
      resource: resource,
      resourceId: resourceId,
      method: req.method,
      url: req.originalUrl,
      userAgent: req.get("User-Agent"),
      ipAddress: getClientIP(req),
      sessionId: req.sessionID || req.headers["x-session-id"] || null,
      level: logLevel,
      category: category,
    };

    // 记录请求数据
    if (includeRequestBody && req.body) {
      const bodyStr = JSON.stringify(req.body);
      if (bodyStr.length <= maxBodySize) {
        logData.requestData = sanitizeData(req.body);
      } else {
        logData.requestData = { _truncated: true, _size: bodyStr.length };
      }
    }

    // 拦截响应
    const originalSend = res.send;
    const originalJson = res.json;

    res.send = function (data) {
      res.send = originalSend;

      // 记录响应信息
      const duration = Date.now() - startTime;
      const success = res.statusCode < 400;

      logData.responseStatus = res.statusCode;
      logData.duration = duration;
      logData.success = success;

      if (!success) {
        logData.level =
          res.statusCode >= 500 ? AuditLog.LEVELS.ERROR : AuditLog.LEVELS.WARN;
      }

      // 记录响应数据
      if (includeResponseBody && data) {
        try {
          const responseData =
            typeof data === "string" ? JSON.parse(data) : data;
          const responseStr = JSON.stringify(responseData);

          if (responseStr.length <= maxBodySize) {
            logData.responseData = sanitizeData(responseData);
          } else {
            logData.responseData = {
              _truncated: true,
              _size: responseStr.length,
            };
          }
        } catch (error) {
          // 如果解析失败，记录原始数据的长度
          logData.responseData = {
            _parseError: true,
            _size: data?.length || 0,
          };
        }
      }

      // 异步记录日志（不阻塞响应）
      setImmediate(() => {
        AuditLog.createLog(logData).catch((error) => {
          try { const { Logger } = require('./errorHandler'); Logger.error('记录操作日志失败', { error: error.message, stack: error.stack }); } catch(_) {}
        });
      });

      return originalSend.call(this, data);
    };

    res.json = function (data) {
      res.json = originalJson;

      // 记录响应信息
      const duration = Date.now() - startTime;
      const success = res.statusCode < 400;

      logData.responseStatus = res.statusCode;
      logData.duration = duration;
      logData.success = success;

      if (!success) {
        logData.level =
          res.statusCode >= 500 ? AuditLog.LEVELS.ERROR : AuditLog.LEVELS.WARN;
      }

      // 记录响应数据
      if (includeResponseBody && data) {
        const responseStr = JSON.stringify(data);

        if (responseStr.length <= maxBodySize) {
          logData.responseData = sanitizeData(data);
        } else {
          logData.responseData = {
            _truncated: true,
            _size: responseStr.length,
          };
        }
      }

      // 异步记录日志（不阻塞响应）
      setImmediate(() => {
        AuditLog.createLog(logData).catch((error) => {
          try { const { Logger } = require('./errorHandler'); Logger.error('记录操作日志失败', { error: error.message, stack: error.stack }); } catch(_) {}
        });
      });

      return originalJson.call(this, data);
    };

    next();
  };
}

/**
 * 手动记录操作日志
 * @param {Object} req - 请求对象
 * @param {Object} logData - 日志数据
 */
async function logOperation(req, logData) {
  const baseData = {
    userId: req.user?.id || null,
    username: req.user?.username || null,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get("User-Agent"),
    ipAddress: getClientIP(req),
    sessionId: req.sessionID || req.headers["x-session-id"] || null,
    level: AuditLog.LEVELS.INFO,
    success: true,
  };

  const finalData = { ...baseData, ...logData };

  try {
    return await AuditLog.createLog(finalData);
  } catch (error) {
    try { const { Logger } = require('./errorHandler'); Logger.error('手动记录操作日志失败', { error: error.message, stack: error.stack }); } catch(_) {}
    return null;
  }
}

module.exports = {
  auditLogger,
  logOperation,
  sanitizeData,
  getClientIP,
};
