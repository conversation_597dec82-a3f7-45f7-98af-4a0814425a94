<!-- components/oa/approval-flow/approval-flow.wxml -->

<view class="approval-flow {{size}}">
  <!-- 流程标题 -->
  <view wx:if="{{approvals.length > 0}}" class="flow-header">
    <text class="flow-title">审批流程</text>
    <text class="flow-status">{{approvals.length}}个步骤</text>
  </view>

  <!-- 审批步骤列表 -->
  <view class="approval-steps">
    <view 
      wx:for="{{approvals}}" 
      wx:key="id"
      class="{{getStepClass(item, index)}}"
      data-index="{{index}}"
      bindtap="onStepTap"
    >
      <!-- 步骤图标和连接线 -->
      <view class="step-indicator">
        <view class="step-icon">
          <text class="icon-text">{{getStepConfig(item).icon}}</text>
        </view>
        
        <!-- 连接线（非最后一个步骤） -->
        <view wx:if="{{index < approvals.length - 1}}" class="{{getConnectorClass(item, index)}}"></view>
      </view>

      <!-- 步骤内容 -->
      <view class="step-content">
        <!-- 步骤基本信息 -->
        <view class="step-header">
          <text class="step-name">{{item.step_name}}</text>
          <oa-status-tag
            text="{{getStepConfig(item).label}}"
            color="{{getStepConfig(item).color}}"
            size="small"
          />
        </view>

        <!-- 审批人信息 -->
        <view class="step-info">
          <text class="info-label">审批人：</text>
          <text class="info-value">{{item.approver_name || '待指定'}}</text>
        </view>

        <!-- 处理时间 -->
        <view wx:if="{{item.processed_at}}" class="step-info">
          <text class="info-label">处理时间：</text>
          <text class="info-value">{{formatTime(item.processed_at)}}</text>
        </view>

        <!-- 截止时间 -->
        <view wx:if="{{item.deadline && item.status === 'pending'}}" class="step-info">
          <text class="info-label">截止时间：</text>
          <text class="info-value deadline">{{item.deadline}}</text>
        </view>

        <!-- 审批意见 -->
        <view wx:if="{{item.remarks && showDetails}}" class="step-remarks">
          <text class="remarks-label">审批意见：</text>
          <text class="remarks-content">{{item.remarks}}</text>
        </view>

        <!-- 操作按钮（当前用户可操作的步骤） -->
        <view wx:if="{{isCurrentUserStep(item)}}" class="step-actions">
          <button 
            class="action-btn approve"
            data-action="approve"
            data-step-id="{{item.id}}"
            bindtap="onApprovalAction"
          >
            通过
          </button>
          <button 
            class="action-btn reject"
            data-action="reject"
            data-step-id="{{item.id}}"
            bindtap="onApprovalAction"
          >
            拒绝
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{approvals.length === 0}}" class="empty-flow">
    <text class="empty-icon">📋</text>
    <text class="empty-text">暂无审批流程</text>
  </view>
</view>
