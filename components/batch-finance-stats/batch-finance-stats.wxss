/* 批次财务统计组件样式 */

.batch-finance-stats {
  width: 100%;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  color: #ff4757;
}

.error-message {
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background: #4CAF50;
  color: white;
  border-radius: 8rpx;
  font-size: 26rpx;
}

/* 卡片样式 */
.stats-card {
  background: white;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.batch-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.batch-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rpx;
  background: #f5f5f5;
}

.stats-item {
  background: white;
  padding: 30rpx;
  text-align: center;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 利润状态颜色 */
.profit-positive .stats-value {
  color: #4CAF50;
}

.profit-negative .stats-value {
  color: #ff4757;
}

.profit-zero .stats-value {
  color: #666;
}

/* 利润率特殊样式 */
.profit-margin {
  grid-column: span 2;
  background: #f8f9fa;
}

.profit-margin .stats-value {
  font-size: 42rpx;
}

/* 周期信息 */
.cycle-info {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
  background: #f8f9fa;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  background: white;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #4CAF50;
  border: 2rpx solid #4CAF50;
}

.action-btn:active {
  background: #f0f8f0;
}

/* 详细记录列表 */
.records-section {
  margin-top: 20rpx;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
}

.records-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.records-count {
  font-size: 24rpx;
  color: #666;
}

.records-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.record-item:active {
  background: #f8f9fa;
}

.record-left {
  flex: 1;
}

.record-description {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-meta {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.record-income .record-amount {
  color: #4CAF50;
}

.record-expense .record-amount {
  color: #ff4757;
}

.record-type {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: white;
}

.record-income .record-type {
  background: #4CAF50;
}

.record-expense .record-type {
  background: #ff4757;
}

/* 简单样式 */
.stats-simple {
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  margin: 10rpx;
}

.stats-simple .stats-grid {
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  background: transparent;
}

.stats-simple .stats-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.stats-simple .stats-value {
  font-size: 28rpx;
}

.stats-simple .stats-label {
  font-size: 22rpx;
}

/* 紧凑样式 */
.stats-compact {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: white;
  border-radius: 8rpx;
  margin: 8rpx;
}

.stats-compact .stats-item {
  padding: 0;
  text-align: center;
}

.stats-compact .stats-value {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.stats-compact .stats-label {
  font-size: 20rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .profit-margin {
    grid-column: span 1;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    margin-bottom: 10rpx;
  }
}

/* 动画效果 */
.stats-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.record-item {
  transition: background-color 0.2s ease;
}

.action-btn {
  transition: all 0.2s ease;
}

/* 空状态 */
.empty-records {
  text-align: center;
  padding: 60rpx 30rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}
