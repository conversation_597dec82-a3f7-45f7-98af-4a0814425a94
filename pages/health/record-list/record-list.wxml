<!-- pages/health/record-list/record-list.wxml -->
<view class="record-list-container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item {{statusFilter === 'all' ? 'active' : ''}}" data-status="all" bindtap="onFilterStatus">
      全部
    </view>
    <view class="filter-item {{statusFilter === 'healthy' ? 'active' : ''}}" data-status="healthy" bindtap="onFilterStatus">
      健康
    </view>
    <view class="filter-item {{statusFilter === 'sick' ? 'active' : ''}}" data-status="sick" bindtap="onFilterStatus">
      生病
    </view>
    <view class="filter-item {{statusFilter === 'treated' ? 'active' : ''}}" data-status="treated" bindtap="onFilterStatus">
      已治疗
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="records-container">
    <block wx:for="{{records}}" wx:key="id">
      <view class="record-item" bindtap="onViewDetail" data-id="{{item.id}}">
        <view class="record-header">
          <text class="record-title">{{item.title}}</text>
          <view class="record-status {{item.status === 'healthy' ? 'status-healthy' : item.status === 'sick' ? 'status-sick' : 'status-treated'}}">
            {{item.status === 'healthy' ? '健康' : item.status === 'sick' ? '生病' : '已治疗'}}
          </view>
        </view>
        <view class="record-meta">
          <text class="record-date">{{item.date}}</text>
          <text class="record-author">{{item.author}}</text>
        </view>
        <view class="record-summary">
          {{item.summary}}
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view wx:if="{{records.length === 0 && !loading}}" class="empty-state">
      <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无健康记录</text>
      <button class="empty-btn" bindtap="onAddRecord">添加记录</button>
    </view>
    
    <!-- 加载更多 -->
    <view wx:if="{{hasMore && records.length > 0}}" class="load-more">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多 -->
    <view wx:elif="{{!hasMore && records.length > 0}}" class="no-more">
      <text>没有更多数据了</text>
    </view>
  </view>
  
  <!-- 添加按钮 -->
  <view class="add-button" bindtap="onAddRecord">
    <image src="/images/icon_add.png" mode="aspectFit"></image>
  </view>
</view>