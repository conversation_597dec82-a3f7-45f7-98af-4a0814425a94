/* components/goose-price/goose-price.wxss - 价格管理优化版 */
.price-container {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 32rpx;
  border: 2rpx solid #f0c040;
  position: relative;
  overflow: hidden;
}

/* 移除无用的伪元素和动画 */

.price-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.update-time {
  font-size: 24rpx;
  color: #666666;
  background-color: #f0c040;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: normal;
}

.price-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 6rpx;
  margin-bottom: 32rpx;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.tab-item.active {
  background: #0066cc;
  color: #ffffff;
  font-weight: bold;
}

.tab-item .tab-text {
  position: relative;
  z-index: 1;
}

.price-content {
  /* 价格内容区域 */
}

.price-list {
  /* 价格列表 */
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.price-item:last-child {
  border-bottom: none;
}

.breed-info {
  flex: 1;
}

.breed-name {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.breed-desc {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.current-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #0066cc;
}

.price-unit {
  font-size: 24rpx;
  color: #666666;
}

.price-change {
  font-size: 24rpx;
  font-weight: bold;
}

.price-change.up {
  color: #52c41a;
}

.price-change.down {
  color: #ff4d4f;
}

.change-value {
  font-weight: bold;
}

/* 移除其他动画和复杂样式 */