/* 智慧养鹅小程序 - 性能优化系统 V1.0 */
/* 提供全面的CSS性能优化解决方案 */

/* ==================== GPU加速优化 ==================== */

/* 强制GPU加速的元素 */
.gpu-accelerated {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000rpx;
  -webkit-perspective: 1000rpx;
}

/* 动画元素GPU加速 */
.animate-gpu {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* 滚动容器GPU加速 */
.scroll-gpu {
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

/* ==================== 渲染性能优化 ==================== */

/* 避免重排重绘的元素 */
.no-reflow {
  position: relative;
  transform: translateZ(0);
  contain: layout style paint;
}

/* 小程序兼容版本 */
.no-reflow-mp {
  position: relative;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 固定布局元素 */
.layout-fixed {
  position: fixed;
  contain: layout;
}

/* 静态元素（不参与交互） */
.static-element {
  pointer-events: none;
  contain: style paint;
}

/* ==================== 动画性能优化 ==================== */

/* 高性能动画基类 */
.performance-animation {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backface-visibility: hidden;
  will-change: transform, opacity;
}

/* 只使用transform和opacity的动画 */
.efficient-animation {
  transition: transform 0.3s ease, opacity 0.3s ease;
  will-change: transform, opacity;
}

/* 避免触发layout的动画 */
.no-layout-animation {
  /* 使用transform代替改变位置 */
  transition: transform 0.3s ease;
}

.no-layout-animation.move-right {
  transform: translateX(100rpx);
}

.no-layout-animation.move-down {
  transform: translateY(100rpx);
}

.no-layout-animation.scale-up {
  transform: scale(1.1);
}

/* 批量动画优化 */
.batch-animation {
  animation-delay: calc(var(--animation-index, 0) * 0.1s);
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

/* ==================== 内存优化 ==================== */

/* 大图片容器优化 */
.image-container-optimized {
  position: relative;
  overflow: hidden;
  contain: layout style paint;
}

.image-container-optimized image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: translateZ(0);
}

/* 长列表优化 */
.virtual-list-item {
  contain: layout style paint;
  transform: translateZ(0);
  height: var(--item-height, 120rpx);
}

/* 内容区域优化 */
.content-area-optimized {
  contain: layout style;
  min-height: 100rpx;
}

/* ==================== 滚动性能优化 ==================== */

/* 平滑滚动优化 */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
}

/* 滚动容器优化 */
.scroll-container {
  position: relative;
  transform: translateZ(0);
  will-change: scroll-position;
}

/* 固定头部优化 */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  transform: translateZ(0);
  will-change: transform;
  backdrop-filter: none; /* 移除，小程序不支持 */
  background-color: rgba(255, 255, 255, 0.95);
}

/* 无限滚动优化 */
.infinite-scroll-item {
  contain: layout style paint;
  transform: translateZ(0);
  min-height: var(--min-item-height, 80rpx);
}

/* ==================== 字体性能优化 ==================== */

/* 字体渲染优化 */
.optimized-text {
  font-display: swap;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 大段文本优化 */
.large-text-block {
  font-display: swap;
  contain: style;
  word-break: break-word;
  hyphens: auto;
}

/* 动态文本优化 */
.dynamic-text {
  contain: style paint;
  will-change: contents;
}

/* ==================== 布局性能优化 ==================== */

/* Flexbox性能优化 */
.flex-optimized {
  display: flex;
  contain: layout style;
}

/* Grid性能优化 */
.grid-optimized {
  display: grid;
  contain: layout style;
  grid-template-rows: repeat(auto-fit, minmax(100rpx, 1fr));
}

/* 单列布局优化 */
.single-column {
  width: 100%;
  contain: layout;
}

/* 多列布局优化 */
.multi-column {
  column-count: 2;
  column-gap: var(--space-lg);
  contain: layout style;
}

/* ==================== 交互性能优化 ==================== */

/* 点击反馈优化 */
.tap-optimized {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
  transition: transform 0.1s ease;
}

.tap-optimized:active {
  transform: scale(0.98);
}

/* 悬停效果优化 */
.hover-optimized {
  transition: opacity 0.2s ease;
  will-change: opacity;
}

.hover-optimized:hover {
  opacity: 0.8;
}

/* 长按优化 */
.longpress-optimized {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* ==================== 资源加载优化 ==================== */

/* 延迟加载元素 */
.lazy-load {
  opacity: 0;
  transform: translateY(50rpx);
  transition: opacity 0.5s ease, transform 0.5s ease;
  will-change: opacity, transform;
}

.lazy-load.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* 预加载元素 */
.preload {
  content-visibility: auto;
  contain-intrinsic-size: 200rpx;
}

/* 小程序兼容版本 */
.preload-mp {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preload-mp.ready {
  opacity: 1;
}

/* 图片懒加载优化 */
.image-lazy {
  background-color: var(--bg-secondary);
  background-image: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ==================== 缓存优化 ==================== */

/* 静态内容缓存 */
.cache-static {
  contain: strict;
  content-visibility: auto;
}

/* 动态内容缓存 */
.cache-dynamic {
  contain: layout style paint;
  will-change: contents;
}

/* ==================== 响应式性能优化 ==================== */

/* 移动端优化 */
@media screen and (max-width: 750rpx) {
  .mobile-optimized {
    contain: layout style;
    transform: translateZ(0);
  }
  
  .mobile-optimized .complex-animation {
    animation: none; /* 移动端禁用复杂动画 */
  }
}

/* 大屏优化 */
@media screen and (min-width: 1024rpx) {
  .desktop-optimized {
    contain: layout style paint;
    will-change: auto;
  }
}

/* ==================== 内存泄露防护 ==================== */

/* 清理will-change */
.animation-complete {
  will-change: auto;
}

/* 移除无用的transform */
.transform-reset {
  transform: none;
}

/* 清理事件监听器 */
.event-cleanup {
  pointer-events: none;
}

/* ==================== 性能监控辅助 ==================== */

/* 性能标记元素 */
.perf-marker {
  /* 用于性能监控的标记类 */
}

.perf-critical {
  /* 关键路径元素标记 */
  contain: layout style paint;
  will-change: auto;
}

.perf-heavy {
  /* 重型元素标记 */
  contain: strict;
}

/* ==================== 特殊场景优化 ==================== */

/* 模态框性能优化 */
.modal-optimized {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  contain: layout style paint;
  will-change: opacity;
}

/* 选项卡切换优化 */
.tab-content-optimized {
  contain: layout style paint;
  transform: translateZ(0);
}

.tab-content-optimized.inactive {
  visibility: hidden;
  pointer-events: none;
}

/* 长列表优化 */
.long-list-optimized {
  contain: layout style;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.long-list-optimized .list-item {
  contain: layout style paint;
  min-height: var(--item-min-height, 100rpx);
}

/* ==================== 工具类 ==================== */

/* 性能调试 */
.perf-debug {
  outline: 2rpx solid red;
  background-color: rgba(255, 0, 0, 0.1);
}

/* 禁用动画（调试用） */
.no-animations view,
.no-animations text,
.no-animations button,
.no-animations input,
.no-animations image,
.no-animations scroll-view {
  animation: none !important;
  transition: none !important;
}

/* 强制重绘（调试用） */
.force-repaint {
  transform: translateZ(0) scale(1.0001);
}

/* 性能模式切换 */
.performance-mode .expensive-effect {
  display: none;
}

.performance-mode .complex-animation {
  animation: none;
}

.performance-mode .heavy-shadow {
  box-shadow: none;
}

/* ==================== 媒体查询性能优化 ==================== */

/* 减少动效模式 - 小程序通过类名控制 */
/* @media (prefers-reduced-motion: reduce) { */ /* 小程序不支持 */
.respect-reduce-motion {
  animation: none !important;
  transition: none !important;
}

.respect-reduce-motion view,
.respect-reduce-motion text,
.respect-reduce-motion button,
.respect-reduce-motion input,
.respect-reduce-motion image,
.respect-reduce-motion scroll-view {
  animation: none !important;
  transition: none !important;
}
/* } */

/* 低功耗模式 - 小程序通过类名控制 */
/* @media (prefers-reduced-data: reduce) { */ /* 小程序不支持 */
.data-saver .background-image {
  background-image: none;
}

.data-saver .animation {
  animation: none;
}
/* } */

/* ==================== 性能优化工具函数 ==================== */

/* CSS变量性能优化 */
:root {
  /* 缓存复杂计算值 */
  --cached-shadow: 0 4rpx 6rpx -1rpx rgba(0, 0, 0, 0.1);
  --cached-gradient: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  --cached-transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 复用常用样式 */
.common-card-shadow {
  box-shadow: var(--cached-shadow);
}

.common-gradient-bg {
  background: var(--cached-gradient);
}

.common-smooth-transition {
  transition: var(--cached-transition);
}