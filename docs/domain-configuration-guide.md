# 🌐 域名配置解决方案

## 📋 问题概述

智慧养鹅SAAS平台在小程序端遇到的网络域名配置问题：

```
❌ 错误信息: request 合法域名校验出错
❌ 问题域名: http://localhost:3000 不在合法域名列表中
❌ 影响: API请求全部失败
```

## 🔍 问题分析

### 根本原因
1. **开发环境配置**: 使用`localhost`域名进行开发，但微信小程序要求所有网络请求域名必须在白名单中
2. **环境切换复杂**: 开发、测试、生产环境域名不统一，缺乏自动切换机制
3. **配置分散**: 域名配置分散在多个文件中，难以统一管理
4. **HTTPS要求**: 生产环境必须使用HTTPS，但开发环境通常使用HTTP

### 影响范围
- API请求无法正常发送
- 文件上传下载失败
- 实时数据同步中断
- 用户登录认证失败

## 🛠️ 解决方案

### 方案一：统一域名配置管理

#### 1.1 配置文件结构

```javascript
// config/domains.js
const DOMAIN_CONFIG = {
  development: {
    api: 'https://dev-api.zhihuiyange.com',
    upload: 'https://dev-upload.zhihuiyange.com',
    websocket: 'wss://dev-ws.zhihuiyange.com'
  },
  testing: {
    api: 'https://test-api.zhihuiyange.com',
    upload: 'https://test-upload.zhihuiyange.com',
    websocket: 'wss://test-ws.zhihuiyange.com'
  },
  staging: {
    api: 'https://staging-api.zhihuiyange.com',
    upload: 'https://staging-upload.zhihuiyange.com',
    websocket: 'wss://staging-ws.zhihuiyange.com'
  },
  production: {
    api: 'https://api.zhihuiyange.com',
    upload: 'https://upload.zhihuiyange.com',
    websocket: 'wss://ws.zhihuiyange.com'
  }
};
```

#### 1.2 环境自动检测

```javascript
// utils/environment-detector.js
class EnvironmentDetector {
  static detectEnvironment() {
    // 1. 从编译时环境变量获取
    if (typeof __ENVIRONMENT__ !== 'undefined') {
      return __ENVIRONMENT__;
    }
    
    // 2. 从全局数据获取
    const app = getApp();
    if (app && app.globalData && app.globalData.environment) {
      return app.globalData.environment;
    }
    
    // 3. 从版本号判断
    const accountInfo = wx.getAccountInfoSync();
    const version = accountInfo.miniProgram.version;
    
    if (version.includes('dev') || version.includes('test')) {
      return 'development';
    } else if (version.includes('staging')) {
      return 'staging';
    } else {
      return 'production';
    }
  }
}
```

### 方案二：微信小程序域名配置

#### 2.1 开发者工具配置

在微信开发者工具中配置合法域名：

```
开发管理 → 开发设置 → 服务器域名

request合法域名：
- https://api.zhihuiyange.com
- https://dev-api.zhihuiyange.com  
- https://test-api.zhihuiyange.com
- https://staging-api.zhihuiyange.com

uploadFile合法域名：
- https://upload.zhihuiyange.com
- https://dev-upload.zhihuiyange.com
- https://test-upload.zhihuiyange.com

downloadFile合法域名：
- https://cdn.zhihuiyange.com
- https://dev-cdn.zhihuiyange.com

socket合法域名：
- wss://ws.zhihuiyange.com
- wss://dev-ws.zhihuiyange.com
```

#### 2.2 开发环境特殊处理

```javascript
// 开发环境跳过域名校验
if (configManager.isDevelopment()) {
  // 在开发者工具中：详情 → 本地设置 → 不校验合法域名
  console.log('开发环境：请在开发者工具中关闭域名校验');
}
```

### 方案三：动态域名切换

#### 3.1 智能域名选择器

```javascript
// utils/smart-domain-selector.js
class SmartDomainSelector {
  constructor() {
    this.cachedDomains = new Map();
    this.healthCheckResults = new Map();
  }

  /**
   * 智能选择最佳域名
   */
  async selectBestDomain(service, environment) {
    const candidates = this.getDomainCandidates(service, environment);
    
    // 并行健康检查
    const healthPromises = candidates.map(domain => 
      this.checkDomainHealth(domain)
    );
    
    const healthResults = await Promise.allSettled(healthPromises);
    
    // 选择最健康的域名
    for (let i = 0; i < candidates.length; i++) {
      if (healthResults[i].status === 'fulfilled' && healthResults[i].value) {
        return candidates[i];
      }
    }
    
    // 如果都不可用，返回默认域名
    return candidates[0];
  }

  /**
   * 域名健康检查
   */
  async checkDomainHealth(domain) {
    try {
      const response = await wx.request({
        url: `${domain}/health`,
        timeout: 3000,
        method: 'GET'
      });
      
      return response.statusCode === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取域名候选列表
   */
  getDomainCandidates(service, environment) {
    const envConfig = DOMAIN_CONFIG[environment];
    const primaryDomain = envConfig[service];
    
    // 构建候选域名列表（主域名 + 备用域名）
    const candidates = [primaryDomain];
    
    // 添加备用域名逻辑
    if (environment === 'production') {
      candidates.push(
        primaryDomain.replace('api.', 'api2.'),
        primaryDomain.replace('api.', 'backup-api.')
      );
    }
    
    return candidates;
  }
}
```

### 方案四：本地开发代理方案

#### 4.1 开发服务器代理配置

```javascript
// scripts/dev-proxy-server.js
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const https = require('https');
const fs = require('fs');

class DevProxyServer {
  constructor() {
    this.app = express();
    this.setupProxy();
  }

  setupProxy() {
    // API代理
    this.app.use('/api', createProxyMiddleware({
      target: 'http://localhost:3000',
      changeOrigin: true,
      secure: false
    }));

    // 文件上传代理
    this.app.use('/upload', createProxyMiddleware({
      target: 'http://localhost:3001',
      changeOrigin: true,
      secure: false
    }));
  }

  start() {
    // 开发环境使用HTTPS（使用自签名证书）
    const options = {
      key: fs.readFileSync('./certs/dev-key.pem'),
      cert: fs.readFileSync('./certs/dev-cert.pem')
    };

    https.createServer(options, this.app).listen(443, () => {
      console.log('🚀 开发代理服务器启动: https://dev-api.zhihuiyange.com');
    });
  }
}

// 启动代理服务器
if (require.main === module) {
  new DevProxyServer().start();
}
```

#### 4.2 自签名证书生成脚本

```bash
#!/bin/bash
# scripts/generate-dev-certs.sh

# 创建证书目录
mkdir -p certs

# 生成私钥
openssl genrsa -out certs/dev-key.pem 2048

# 生成证书签名请求
openssl req -new -key certs/dev-key.pem -out certs/dev-csr.pem -subj "/C=CN/ST=Zhejiang/L=Hangzhou/O=ZhiHuiYangE/CN=dev-api.zhihuiyange.com"

# 生成自签名证书
openssl x509 -req -in certs/dev-csr.pem -signkey certs/dev-key.pem -out certs/dev-cert.pem -days 365

echo "✅ 开发证书生成完成"
echo "请将 dev-cert.pem 添加到系统信任证书列表"
```

### 方案五：云端配置管理

#### 5.1 远程配置服务

```javascript
// services/remote-config.service.js
class RemoteConfigService {
  constructor() {
    this.configCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 获取远程域名配置
   */
  async getRemoteDomainConfig() {
    const cacheKey = 'domain_config';
    const cached = this.configCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }

    try {
      const response = await wx.request({
        url: 'https://config.zhihuiyange.com/api/v1/domain-config',
        method: 'GET',
        timeout: 10000
      });

      if (response.statusCode === 200) {
        this.configCache.set(cacheKey, {
          data: response.data,
          timestamp: Date.now()
        });
        
        return response.data;
      }
    } catch (error) {
      console.error('获取远程域名配置失败:', error);
    }

    // 返回本地默认配置
    return this.getLocalDomainConfig();
  }

  /**
   * 更新域名配置
   */
  async updateDomainConfig(environment, config) {
    try {
      await wx.request({
        url: 'https://config.zhihuiyange.com/api/v1/domain-config',
        method: 'PUT',
        data: {
          environment,
          config
        },
        header: {
          'Authorization': 'Bearer ' + this.getAdminToken()
        }
      });

      // 清除缓存
      this.configCache.clear();
      
      return true;
    } catch (error) {
      console.error('更新域名配置失败:', error);
      return false;
    }
  }
}
```

## 📋 实施步骤

### 第一阶段：本地开发环境配置 (1天)

1. **生成开发证书**
   ```bash
   chmod +x scripts/generate-dev-certs.sh
   ./scripts/generate-dev-certs.sh
   ```

2. **配置本地域名解析**
   ```bash
   # 编辑 /etc/hosts 文件
   127.0.0.1 dev-api.zhihuiyange.com
   127.0.0.1 dev-upload.zhihuiyange.com
   ```

3. **启动代理服务器**
   ```bash
   node scripts/dev-proxy-server.js
   ```

4. **配置开发者工具**
   - 添加 `https://dev-api.zhihuiyange.com` 到合法域名
   - 开发阶段可临时关闭域名校验

### 第二阶段：测试和生产环境域名 (2天)

1. **申请SSL证书**
   - 为所有子域名申请通配符证书
   - 配置CDN和负载均衡

2. **DNS配置**
   ```
   A记录: api.zhihuiyange.com → 服务器IP
   A记录: test-api.zhihuiyange.com → 测试服务器IP
   A记录: dev-api.zhihuiyange.com → 开发服务器IP
   ```

3. **Nginx配置示例**
   ```nginx
   server {
       listen 443 ssl;
       server_name api.zhihuiyange.com;
       
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       
       location /api/v1/ {
           proxy_pass http://backend:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### 第三阶段：统一配置管理 (1天)

1. **集成统一配置**
   ```javascript
   // app.js
   const { configManager } = require('./utils/unified-config.js');
   
   App({
     async onLaunch() {
       await configManager.initialize();
       console.log('当前环境:', configManager.getEnvironment());
       console.log('API域名:', configManager.getApiEndpointUrl('tenant'));
     }
   });
   ```

2. **更新API调用**
   ```javascript
   // 替换硬编码域名
   const apiUrl = configManager.getApiEndpointUrl('tenant');
   wx.request({
     url: `${apiUrl}/flocks`,
     // ...
   });
   ```

### 第四阶段：智能域名切换 (1天)

1. **实现域名健康检查**
2. **配置域名故障转移**
3. **监控域名可用性**

## 🔧 配置检查清单

### 开发环境
- [ ] 本地HTTPS代理服务器运行正常
- [ ] 开发者工具域名配置正确
- [ ] API请求能够正常发送和接收
- [ ] 文件上传下载功能正常

### 测试环境
- [ ] 测试域名DNS解析正确
- [ ] SSL证书配置有效
- [ ] 负载均衡配置正确
- [ ] API健康检查通过

### 生产环境
- [ ] 生产域名SSL证书有效
- [ ] CDN配置优化
- [ ] 故障转移机制生效
- [ ] 监控告警配置完成

## 🚨 常见问题解决

### 问题1：开发环境域名校验失败

**解决方案**：
```javascript
// 开发环境临时解决方案
if (configManager.isDevelopment()) {
  // 方法1: 使用开发者工具的"不校验合法域名"选项
  // 方法2: 配置本地HTTPS代理
  // 方法3: 使用测试域名进行开发
}
```

### 问题2：证书不受信任

**解决方案**：
```bash
# macOS系统
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain certs/dev-cert.pem

# Windows系统
# 双击证书文件，选择"安装证书" → "本地计算机" → "受信任的根证书颁发机构"
```

### 问题3：域名解析失败

**解决方案**：
```bash
# 检查DNS配置
nslookup api.zhihuiyange.com

# 检查本地hosts文件
cat /etc/hosts | grep zhihuiyange

# 清除DNS缓存
sudo dscacheutil -flushcache  # macOS
ipconfig /flushdns             # Windows
```

### 问题4：跨域请求失败

**解决方案**：
```javascript
// 服务端CORS配置
app.use(cors({
  origin: [
    'https://servicewechat.com',
    'https://*.zhihuiyange.com'
  ],
  credentials: true
}));
```

## 📊 监控和维护

### 域名可用性监控

```javascript
// scripts/domain-monitor.js
class DomainMonitor {
  async checkAllDomains() {
    const environments = ['development', 'testing', 'staging', 'production'];
    const services = ['api', 'upload', 'websocket'];
    
    for (const env of environments) {
      for (const service of services) {
        const domain = DOMAIN_CONFIG[env][service];
        const isHealthy = await this.checkDomainHealth(domain);
        
        if (!isHealthy) {
          await this.sendAlert(env, service, domain);
        }
      }
    }
  }
}
```

### 自动化部署脚本

```bash
#!/bin/bash
# scripts/deploy-domain-config.sh

echo "🚀 部署域名配置..."

# 1. 验证域名配置
node scripts/validate-domain-config.js

# 2. 更新CDN配置
./scripts/update-cdn-config.sh

# 3. 重启服务
docker-compose restart nginx

# 4. 验证部署
node scripts/post-deploy-check.js

echo "✅ 域名配置部署完成"
```

## 📚 相关文档

- [微信小程序网络使用说明](https://developers.weixin.qq.com/miniprogram/dev/framework/ability/network.html)
- [统一配置管理使用指南](./unified-config-guide.md)
- [HTTPS证书配置指南](./https-certificate-guide.md)
- [负载均衡配置指南](./load-balancer-guide.md)

---

🎯 **目标**：通过统一的域名配置管理，彻底解决网络请求域名校验问题，提供稳定可靠的API服务。