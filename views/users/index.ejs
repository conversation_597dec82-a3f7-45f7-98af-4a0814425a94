<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        <a href="/users/create" class="btn btn-primary">
            <i class="bi bi-person-plus me-2"></i>添加用户
        </a>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="搜索用户..." id="searchInput">
            <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- 错误提示 -->
<% if (error) { %>
<div class="alert alert-warning" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    <%= error %>
</div>
<% } %>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-people me-2"></i>用户列表
            <span class="badge bg-primary ms-2"><%= users.length %></span>
        </h5>
    </div>
    <div class="card-body">
        <% if (users.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <% users.forEach(function(user) { %>
                    <tr>
                        <td><%= user.id %></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <%= user.username.charAt(0).toUpperCase() %>
                                </div>
                                <%= user.username %>
                            </div>
                        </td>
                        <td><%= user.email || '-' %></td>
                        <td>
                            <% if (user.role === 'admin') { %>
                                <span class="badge bg-danger">管理员</span>
                            <% } else if (user.role === 'manager') { %>
                                <span class="badge bg-warning">经理</span>
                            <% } else { %>
                                <span class="badge bg-info">用户</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (user.status === 'active') { %>
                                <span class="badge bg-success">活跃</span>
                            <% } else { %>
                                <span class="badge bg-secondary">禁用</span>
                            <% } %>
                        </td>
                        <td>
                            <%= moment(user.createdAt).format('YYYY-MM-DD HH:mm') %>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="/users/edit/<%= user.id %>" class="btn btn-outline-primary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" title="删除" 
                                        onclick="deleteUser(<%= user.id %>, '<%= user.username %>')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
        <% } else { %>
        <div class="text-center py-5">
            <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">暂无用户数据</h5>
            <p class="text-muted">点击上方按钮添加第一个用户</p>
        </div>
        <% } %>
    </div>
</div>

<!-- 分页 -->
<% if (users.length > 0) { %>
<nav aria-label="用户列表分页" class="mt-3">
    <ul class="pagination justify-content-center">
        <li class="page-item disabled">
            <a class="page-link" href="#" tabindex="-1">上一页</a>
        </li>
        <li class="page-item active">
            <a class="page-link" href="#">1</a>
        </li>
        <li class="page-item disabled">
            <a class="page-link" href="#">下一页</a>
        </li>
    </ul>
</nav>
<% } %>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
    font-weight: 600;
}
</style>

<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// 删除用户
function deleteUser(userId, username) {
    if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
        fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage('用户删除成功', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showMessage(result.message || '删除失败', 'danger');
            }
        })
        .catch(error => {
            console.error('删除用户错误:', error);
            showMessage('删除用户失败', 'danger');
        });
    }
}
</script>
