/* components/chart/chart.wxss */
.chart-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 8rpx;
}

.chart-canvas {
  display: block;
}

/* 加载状态 */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #fafafa;
  border-radius: 8rpx;
}

/* 错误状态 */
.chart-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #fafafa;
  border-radius: 8rpx;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.error-text {
  font-size: 24rpx;
  color: #999;
}

/* 图例 */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16rpx;
  margin-top: 20rpx;
  padding: 0 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 2rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .chart-legend {
    margin-top: 16rpx;
  }
  
  .legend-item {
    gap: 6rpx;
  }
  
  .legend-text {
    font-size: 22rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .chart-container {
    background-color: #1f1f1f;
  }
  
  .chart-loading,
  .chart-error {
    background-color: #2a2a2a;
  }
  
  .error-text,
  .legend-text {
    color: #ccc;
  }
}