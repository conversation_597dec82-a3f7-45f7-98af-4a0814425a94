// pages/address/address.js - 重新设计的收货地址页面
Page({
  data: {
    addressList: [],
    showDeleteModal: false,
    deleteTarget: null,
    isSelectMode: false
  },

  onLoad(options) {
    // 检查是否为选择模式
    if (options.select === 'true') {
      this.setData({
        isSelectMode: true
      });
      wx.setNavigationBarTitle({
        title: '选择收货地址'
      });
    }
    
    this.loadAddressList();
  },

  // 页面显示时刷新数据
  onShow() {
    this.loadAddressList();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadAddressList();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 加载地址列表
  loadAddressList() {
    // 模拟地址数据
    const addressList = [
      {
        id: '1',
        name: '张三',
        phone: '138****8888',
        province: '北京市',
        city: '朝阳区',
        district: '建国路街道',
        detail: 'SOHO现代城A座1001室',
        tag: '家',
        isDefault: true
      },
      {
        id: '2',
        name: '李四',
        phone: '139****9999',
        province: '上海市',
        city: '浦东新区',
        district: '陆家嘴街道',
        detail: '世纪大道88号金茂大厦2001室',
        tag: '公司',
        isDefault: false
      }
    ];

    this.setData({
      addressList
    });
  },

  // 选择地址
  onSelectAddress(e) {
    const addressId = e.currentTarget.dataset.id;
    
    // 检查是否是选择模式
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    // 从URL参数或上一个页面判断是否为选择模式
    if (currentPage.options && currentPage.options.select === 'true') {
      const selectedAddress = this.data.addressList.find(addr => addr.id === addressId);
      
      // 获取上一个页面
      const prevPage = pages[pages.length - 2];
      if (prevPage) {
        // 将选中的地址传递给上一个页面
        if (typeof prevPage.onAddressSelected === 'function') {
          prevPage.onAddressSelected(selectedAddress);
        } else {
          prevPage.setData({
            selectedAddress
          });
        }
      }
      
      wx.showToast({
        title: '地址已选择',
        icon: 'success',
        duration: 1000
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1000);
    } else {
      // 非选择模式，直接跳转到编辑页面
      wx.navigateTo({
        url: `/pages/address/edit/edit?id=${addressId}`
      });
    }
  },

  // 添加地址
  onAddAddress() {
    wx.navigateTo({
      url: '/pages/address/edit/edit'
    });
  },

  // 编辑地址
  onEditAddress(e) {
    // 修复：检查事件对象是否存在stopPropagation方法
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    const addressId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/address/edit/edit?id=${addressId}`
    });
  },

  // 删除地址 - 显示自定义模态框
  onDeleteAddress(e) {
    // 修复：检查事件对象是否存在stopPropagation方法
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    const addressId = e.currentTarget.dataset.id;
    const deleteTarget = this.data.addressList.find(addr => addr.id === addressId);
    
    this.setData({
      showDeleteModal: true,
      deleteTarget
    });
  },

  // 取消删除
  onCancelDelete() {
    this.setData({
      showDeleteModal: false,
      deleteTarget: null
    });
  },

  // 确认删除
  onConfirmDelete() {
    const addressId = this.data.deleteTarget.id;
    const addressList = this.data.addressList.filter(addr => addr.id !== addressId);
    
    this.setData({
      addressList,
      showDeleteModal: false,
      deleteTarget: null
    });
    
    wx.showToast({
      title: '删除成功',
      icon: 'success',
      duration: 1500
    });
  },

  // 设置默认地址
  onSetDefault(e) {
    // 修复：检查事件对象是否存在stopPropagation方法
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    const addressId = e.currentTarget.dataset.id;
    
    // 检查是否已经是默认地址
    const currentAddress = this.data.addressList.find(addr => addr.id === addressId);
    if (currentAddress && currentAddress.isDefault) {
      wx.showToast({
        title: '已经是默认地址',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    const addressList = this.data.addressList.map(addr => ({
      ...addr,
      isDefault: addr.id === addressId
    }));
    
    this.setData({
      addressList
    });
    
    wx.showToast({
      title: '设置成功',
      icon: 'success',
      duration: 1500
    });
    
    // 震动反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '收货地址管理',
      path: '/pages/address/address',
      imageUrl: '/images/share/address.png'
    };
  }
});