/* pages/shop/shop.wxss */

/* 页面容器 */
.shop-container {
  min-height: 100vh;
  background-color: #f5f6fa;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 搜索区域 */
.search-header {
  background: linear-gradient(135deg, #4285f4 0%, #1565c0 100%);
  padding: calc(env(safe-area-inset-top) + 20rpx) 24rpx 20rpx;
}

.search-bar {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.search-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  min-width: 0;
}

.search-input::placeholder {
  color: #999;
  font-size: 22rpx;
}

.search-btn {
  background: linear-gradient(135deg, #4285f4, #1565c0);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  margin-left: 16rpx;
  box-shadow: 0 3rpx 12rpx rgba(66, 133, 244, 0.3);
  transition: all 0.3s ease;
}

.search-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(66, 133, 244, 0.4);
}

/* 分类筛选 */
.filter-section {
  background-color: #ffffff;
  padding: 18rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
  width: 100%;
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 20rpx;
  padding: 6rpx 0;
}

.category-item {
  flex-shrink: 0;
  padding: 10rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background: linear-gradient(135deg, #4285f4 0%, #1565c0 100%);
  transform: scale(1.05);
}

.category-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.category-item.active .category-text {
  color: white;
  font-weight: 600;
}



/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  padding: 24rpx;
}

/* 商品卡片 */
.product-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}

/* 商品图片容器 */
.product-image-container {
  position: relative;
  width: 100%;
  height: 320rpx;
  background-color: #f8f9fa;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.placeholder-icon {
  font-size: 60rpx;
  opacity: 0.3;
}



/* 商品信息 */
.product-info {
  padding: 24rpx 20rpx;
}

.product-title {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 500;
  min-height: 72rpx;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.current-price {
  font-size: 34rpx;
  color: #4285f4;
  font-weight: bold;
  line-height: 1;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  line-height: 1;
}

.sales-info {
  font-size: 22rpx;
  color: #999;
  line-height: 1;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #bdc3c7;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #d5dbdb;
}

/* 浮动购物车 */
.cart-float {
  position: fixed;
  right: 30rpx;
  bottom: calc(120rpx + env(safe-area-inset-bottom));
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #4285f4 0%, #1565c0 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(66, 133, 244, 0.4);
  z-index: 1000;
  transition: all 0.3s ease;
  transform: translate(var(--cart-x, 0), var(--cart-y, 0));
  will-change: transform;
}

.cart-float:active {
  transform: scale(0.9) translate(var(--cart-x, 0), var(--cart-y, 0));
}

.cart-float.dragging {
  box-shadow: 0 12rpx 32rpx rgba(66, 133, 244, 0.6);
  transform: scale(1.1) translate(var(--cart-x, 0), var(--cart-y, 0));
}

.cart-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-icon {
  font-size: 44rpx;
  filter: brightness(0) invert(1);
  animation: pulse 2s infinite;
}

.cart-badge {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  background-color: #ff4757;
  border-radius: 24rpx;
  min-width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

.badge-text {
  font-size: 18rpx;
  color: white;
  font-weight: bold;
  line-height: 1;
  padding: 0 6rpx;
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 480rpx) {
  .top-navigation {
    padding: calc(env(safe-area-inset-top) + 15rpx) 20rpx 15rpx;
    gap: 15rpx;
  }

  .user-avatar {
    width: 60rpx;
    height: 60rpx;
  }

  .search-container {
    padding: 12rpx 20rpx;
  }

  .products-grid {
    gap: 15rpx;
    padding: 15rpx;
  }

  .product-image-container {
    height: 280rpx;
  }

  .product-info {
    padding: 15rpx 12rpx;
  }

  .current-price {
    font-size: 28rpx;
  }
}