<!-- pages/management/staff/staff.wxml -->
<view class="staff-container">
  <!-- 加载状态 -->
  <c-loading wx:if="{{loading}}" message="加载中..." />
  
  <!-- 员工管理内容 -->
  <view wx:else class="staff-content">
    
    <!-- 统计卡片 -->
    <c-card padding="lg" margin="md">
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{stats.total}}</text>
          <text class="stat-label">总员工</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{stats.active}}</text>
          <text class="stat-label">在职</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{stats.inactive}}</text>
          <text class="stat-label">离职</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{stats.todayAttendance}}</text>
          <text class="stat-label">今日出勤</text>
        </view>
      </view>
    </c-card>

    <!-- 搜索和筛选 -->
    <c-card padding="md" margin="md">
      <view class="search-filter-bar">
        <view class="search-wrapper">
          <image class="search-icon" src="/images/icons/search.png" mode="aspectFit" />
          <input 
            class="search-input" 
            placeholder="搜索员工姓名、手机号、部门..."
            value="{{searchKeyword}}"
            bindinput="onSearchInput"
          />
        </view>
        <view class="filter-button" bindtap="showFilterModal">
          <image class="filter-icon" src="/images/icons/filter.png" mode="aspectFit" />
          <text>筛选</text>
        </view>
      </view>
    </c-card>

    <!-- 功能按钮 -->
    <view class="action-buttons">
      <view class="add-button" bindtap="showAddStaffModal" wx:if="{{canManageStaff}}">
        <image class="add-icon" src="/images/icons/plus.png" mode="aspectFit" />
        <text>添加员工</text>
      </view>
    </view>

    <!-- 员工列表 -->
    <c-card padding="md" margin="md">
      <c-section-header title="员工列表 ({{filteredStaffList.length}})" />
      
      <view wx:if="{{filteredStaffList.length === 0}}" class="empty-state">
        <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit" />
        <text class="empty-text">暂无员工数据</text>
      </view>
      
      <view wx:else class="staff-list">
        <view 
          wx:for="{{filteredStaffList}}" 
          wx:key="id" 
          class="staff-item"
          bindtap="viewStaffDetail"
          data-id="{{item.id}}"
        >
          <view class="staff-avatar">
            <image src="{{item.avatar}}" mode="aspectFill" />
            <view class="status-dot {{item.status}}"></view>
          </view>
          
          <view class="staff-info">
            <view class="staff-name">{{item.name}}</view>
            <view class="staff-meta">
              <text class="role-tag {{item.role}}">{{item.roleLabel}}</text>
              <text class="department">{{item.department}} - {{item.position}}</text>
            </view>
            <view class="staff-contact">
              <text class="phone">{{item.phone}}</text>
              <text class="last-active">最后活跃: {{item.lastActiveTime}}</text>
            </view>
          </view>
          
          <view class="staff-actions" wx:if="{{canManageStaff}}">
            <view 
              class="action-button edit"
              bindtap="editStaff"
              data-id="{{item.id}}"
              catchtap="stopPropagation"
            >
              <image src="/images/icons/edit.png" mode="aspectFit" />
            </view>
            <view 
              class="action-button delete"
              bindtap="deleteStaff"
              data-id="{{item.id}}"
              data-name="{{item.name}}"
              catchtap="stopPropagation"
            >
              <image src="/images/icons/delete.png" mode="aspectFit" />
            </view>
          </view>
        </view>
      </view>
    </c-card>

  </view>

  <!-- 筛选弹窗 -->
  <c-modal 
    wx:if="{{showFilterModal}}" 
    title="筛选条件"
    bind:close="hideFilterModal"
  >
    <view class="filter-content">
      <view class="filter-section">
        <text class="filter-label">角色</text>
        <picker 
          range="{{roleOptions}}" 
          range-key="label"
          value="{{filterRole}}"
          bindchange="onRoleFilterChange"
        >
          <view class="picker-display">
            {{roleOptions[filterRole].label || '请选择角色'}}
            <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit" />
          </view>
        </picker>
      </view>
      
      <view class="filter-section">
        <text class="filter-label">状态</text>
        <picker 
          range="{{statusOptions}}" 
          range-key="label"
          value="{{filterStatus}}"
          bindchange="onStatusFilterChange"
        >
          <view class="picker-display">
            {{statusOptions[filterStatus].label || '请选择状态'}}
            <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit" />
          </view>
        </picker>
      </view>
      
      <view class="filter-buttons">
        <button class="reset-button" bindtap="resetFilter">重置</button>
        <button class="confirm-button" bindtap="hideFilterModal">确定</button>
      </view>
    </view>
  </c-modal>

  <!-- 添加员工弹窗 -->
  <c-modal 
    wx:if="{{showAddModal}}" 
    title="添加员工"
    bind:close="hideAddStaffModal"
  >
    <form class="add-staff-form">
      <view class="form-section">
        <view class="form-field">
          <text class="field-label">姓名 *</text>
          <input 
            class="field-input {{formErrors.name ? 'error' : ''}}"
            placeholder="请输入员工姓名"
            value="{{newStaff.name}}"
            bindinput="onFormInput"
            data-field="name"
          />
          <text wx:if="{{formErrors.name}}" class="error-text">{{formErrors.name}}</text>
        </view>
        
        <view class="form-field">
          <text class="field-label">手机号码 *</text>
          <input 
            class="field-input {{formErrors.phone ? 'error' : ''}}"
            placeholder="请输入手机号码"
            type="number"
            value="{{newStaff.phone}}"
            bindinput="onFormInput"
            data-field="phone"
          />
          <text wx:if="{{formErrors.phone}}" class="error-text">{{formErrors.phone}}</text>
        </view>
        
        <view class="form-field">
          <text class="field-label">邮箱</text>
          <input 
            class="field-input {{formErrors.email ? 'error' : ''}}"
            placeholder="请输入邮箱地址"
            value="{{newStaff.email}}"
            bindinput="onFormInput"
            data-field="email"
          />
          <text wx:if="{{formErrors.email}}" class="error-text">{{formErrors.email}}</text>
        </view>
        
        <view class="form-field">
          <text class="field-label">角色 *</text>
          <picker 
            range="{{roleOptions.slice(1)}}" 
            range-key="label"
            value="{{newStaff.role}}"
            bindinput="onFormInput"
            data-field="role"
          >
            <view class="picker-display">
              {{newStaff.role === 'admin' ? '管理员' : newStaff.role === 'manager' ? '经理' : '普通员工'}}
              <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit" />
            </view>
          </picker>
        </view>
        
        <view class="form-field">
          <text class="field-label">部门 *</text>
          <input 
            class="field-input {{formErrors.department ? 'error' : ''}}"
            placeholder="请输入部门"
            value="{{newStaff.department}}"
            bindinput="onFormInput"
            data-field="department"
          />
          <text wx:if="{{formErrors.department}}" class="error-text">{{formErrors.department}}</text>
        </view>
        
        <view class="form-field">
          <text class="field-label">职位 *</text>
          <input 
            class="field-input {{formErrors.position ? 'error' : ''}}"
            placeholder="请输入职位"
            value="{{newStaff.position}}"
            bindinput="onFormInput"
            data-field="position"
          />
          <text wx:if="{{formErrors.position}}" class="error-text">{{formErrors.position}}</text>
        </view>
      </view>
      
      <view class="form-buttons">
        <button class="cancel-button" bindtap="hideAddStaffModal">取消</button>
        <button class="submit-button" bindtap="submitAddStaff">添加</button>
      </view>
    </form>
  </c-modal>

</view>