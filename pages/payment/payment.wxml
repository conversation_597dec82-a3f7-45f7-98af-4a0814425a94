<!-- pages/payment/payment.wxml -->
<view class="payment-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载订单信息中...</text>
  </view>

  <!-- 支付内容 -->
  <view wx:else class="payment-content">
    <!-- 订单信息 -->
    <view class="order-section">
      <view class="section-header">
        <text class="section-title">订单信息</text>
      </view>
      
      <view class="order-info">
        <view class="order-number">
          <text class="label">订单号：</text>
          <text class="value">{{orderInfo.orderNumber}}</text>
        </view>
        <view class="order-time">
          <text class="label">下单时间：</text>
          <text class="value">{{orderInfo.createTime}}</text>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="goods-list">
        <block wx:for="{{orderInfo.goods}}" wx:key="id">
          <view class="goods-item">
            <image class="goods-image" src="{{item.image}}" mode="aspectFill"></image>
            <view class="goods-info">
              <text class="goods-name">{{item.name}}</text>
              <view class="goods-price-qty">
                <text class="goods-price">¥{{item.price}}</text>
                <text class="goods-qty">x{{item.quantity}}</text>
              </view>
            </view>
          </view>
        </block>
      </view>

      <!-- 收货地址 -->
      <view class="address-info">
        <view class="address-header">
          <text class="address-title">收货地址</text>
        </view>
        <view class="address-detail">
          <text class="address-name">{{orderInfo.address.name}}</text>
          <text class="address-phone">{{orderInfo.address.phone}}</text>
          <text class="address-text">{{orderInfo.address.detail}}</text>
        </view>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-section">
      <view class="section-header">
        <text class="section-title">支付方式</text>
      </view>

      <view class="payment-method-fixed">
        <view class="payment-item selected">
          <view class="payment-left">
            <image class="payment-icon" src="/images/icons/wechat-pay.png" mode="aspectFit"></image>
            <view class="payment-info">
              <text class="payment-name">微信支付</text>
              <text class="payment-desc">安全便捷的微信支付</text>
            </view>
          </view>
          <view class="payment-radio">
            <view class="radio-circle checked">
              <view class="radio-dot"></view>
            </view>
          </view>
        </view>

        <view class="payment-notice">
          <text class="notice-text">💡 小程序内仅支持微信支付</text>
        </view>
      </view>
    </view>

    <!-- 价格明细 -->
    <view class="price-section">
      <view class="section-header">
        <text class="section-title">价格明细</text>
      </view>
      
      <view class="price-details">
        <view class="price-item">
          <text class="price-label">商品总价</text>
          <text class="price-value">¥{{orderInfo.totalPrice}}</text>
        </view>
        <view class="price-item">
          <text class="price-label">优惠金额</text>
          <text class="price-value discount">-¥{{orderInfo.discountAmount}}</text>
        </view>
        <view class="price-item">
          <text class="price-label">运费</text>
          <text class="price-value">¥{{orderInfo.shippingFee}}</text>
        </view>
        <view class="price-item total">
          <text class="price-label">实付金额</text>
          <text class="price-value">¥{{orderInfo.actualAmount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部支付栏 -->
  <view class="payment-footer" wx:if="{{!loading}}">
    <view class="footer-left">
      <text class="total-label">实付：</text>
      <text class="total-amount">¥{{orderInfo.actualAmount}}</text>
    </view>
    <button class="pay-button {{paymentLoading ? 'loading' : ''}}" 
            bindtap="onPay" 
            disabled="{{paymentLoading}}">
      <text wx:if="{{!paymentLoading}}">立即支付</text>
      <text wx:else>支付中...</text>
    </button>
  </view>

  <!-- 返回按钮 -->
  <view class="back-button" bindtap="onBackToOrders">
    <text>返回订单</text>
  </view>
</view>
