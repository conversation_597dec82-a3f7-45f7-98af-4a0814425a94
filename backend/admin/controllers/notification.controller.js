// backend/admin/controllers/notification.controller.js
// 通知管理控制器

const { Op } = require("sequelize");

// 模拟通知数据存储（实际项目中应使用数据库）
let notifications = [
  {
    id: 1,
    title: "系统维护通知",
    content: "系统将于今晚22:00-24:00进行维护升级，请提前保存数据。",
    type: "system",
    priority: "high",
    status: "active",
    targetUsers: "all",
    createdBy: 1,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
  },
  {
    id: 2,
    title: "新功能上线",
    content: "商城功能已正式上线，用户可以在小程序中购买养鹅用品。",
    type: "feature",
    priority: "medium",
    status: "active",
    targetUsers: "all",
    createdBy: 1,
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
  },
];

// 获取通知列表
exports.getNotifications = (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type = "",
      status = "",
      search = "",
    } = req.query;

    let filteredNotifications = notifications;

    // 按类型过滤
    if (type) {
      filteredNotifications = filteredNotifications.filter(
        (n) => n.type === type,
      );
    }

    // 按状态过滤
    if (status) {
      filteredNotifications = filteredNotifications.filter(
        (n) => n.status === status,
      );
    }

    // 按搜索关键词过滤
    if (search) {
      filteredNotifications = filteredNotifications.filter(
        (n) =>
          n.title.toLowerCase().includes(search.toLowerCase()) ||
          n.content.toLowerCase().includes(search.toLowerCase()),
      );
    }

    // 分页
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const paginatedNotifications = filteredNotifications.slice(
      offset,
      offset + parseInt(limit),
    );

    res.json({
      success: true,
      data: {
        notifications: paginatedNotifications,
        total: filteredNotifications.length,
        page: parseInt(page),
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取通知列表失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取通知列表失败",
    });
  }
};

// 创建通知
exports.createNotification = (req, res) => {
  try {
    const { title, content, type, priority, targetUsers } = req.body;

    // 验证必填字段
    if (!title || !content || !type) {
      return res.status(400).json({
        success: false,
        message: "标题、内容和类型为必填字段",
      });
    }

    const newNotification = {
      id: notifications.length + 1,
      title,
      content,
      type,
      priority: priority || "medium",
      status: "active",
      targetUsers: targetUsers || "all",
      createdBy: req.user?.id || 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    notifications.unshift(newNotification);

    // 发送通知到客户端（实际项目中可以使用WebSocket或推送服务）
    this.sendNotificationToUsers(newNotification);

    res.json({
      success: true,
      message: "通知创建成功",
      data: newNotification,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("创建通知失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "创建通知失败",
    });
  }
};

// 更新通知
exports.updateNotification = (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, type, priority, status, targetUsers } = req.body;

    const notificationIndex = notifications.findIndex(
      (n) => n.id === parseInt(id),
    );

    if (notificationIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "通知不存在",
      });
    }

    // 更新通知
    notifications[notificationIndex] = {
      ...notifications[notificationIndex],
      title: title || notifications[notificationIndex].title,
      content: content || notifications[notificationIndex].content,
      type: type || notifications[notificationIndex].type,
      priority: priority || notifications[notificationIndex].priority,
      status: status || notifications[notificationIndex].status,
      targetUsers: targetUsers || notifications[notificationIndex].targetUsers,
      updatedAt: new Date(),
    };

    res.json({
      success: true,
      message: "通知更新成功",
      data: notifications[notificationIndex],
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("更新通知失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "更新通知失败",
    });
  }
};

// 删除通知
exports.deleteNotification = (req, res) => {
  try {
    const { id } = req.params;

    const notificationIndex = notifications.findIndex(
      (n) => n.id === parseInt(id),
    );

    if (notificationIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "通知不存在",
      });
    }

    notifications.splice(notificationIndex, 1);

    res.json({
      success: true,
      message: "通知删除成功",
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("删除通知失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "删除通知失败",
    });
  }
};

// 批量发送通知
exports.sendBulkNotification = (req, res) => {
  try {
    const { title, content, type, priority, userIds } = req.body;

    if (!title || !content || !userIds || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "标题、内容和目标用户为必填字段",
      });
    }

    const bulkNotification = {
      id: notifications.length + 1,
      title,
      content,
      type: type || "general",
      priority: priority || "medium",
      status: "active",
      targetUsers: userIds,
      createdBy: req.user?.id || 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    notifications.unshift(bulkNotification);

    // 发送给指定用户
    this.sendNotificationToSpecificUsers(bulkNotification, userIds);

    res.json({
      success: true,
      message: `成功发送通知给 ${userIds.length} 个用户`,
      data: bulkNotification,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("批量发送通知失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "批量发送通知失败",
    });
  }
};

// 获取通知统计
exports.getNotificationStats = (req, res) => {
  try {
    const stats = {
      total: notifications.length,
      active: notifications.filter((n) => n.status === "active").length,
      inactive: notifications.filter((n) => n.status === "inactive").length,
      byType: {
        system: notifications.filter((n) => n.type === "system").length,
        feature: notifications.filter((n) => n.type === "feature").length,
        maintenance: notifications.filter((n) => n.type === "maintenance")
          .length,
        general: notifications.filter((n) => n.type === "general").length,
      },
      byPriority: {
        high: notifications.filter((n) => n.priority === "high").length,
        medium: notifications.filter((n) => n.priority === "medium").length,
        low: notifications.filter((n) => n.priority === "low").length,
      },
    };

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取通知统计失败:", error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: "获取通知统计失败",
    });
  }
};

// 发送通知给所有用户
exports.sendNotificationToUsers = (notification) => {
  // 实际项目中应该通过WebSocket、推送服务或消息队列发送
  try { const { Logger } = require('../middleware/errorHandler'); Logger.info("发送通知给所有用户:", notification.title); } catch(_) {}


  // 模拟发送逻辑
  setTimeout(() => {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.info(`通知 "${notification.title}" 已发送给所有用户`); } catch(_) {}

  }, 1000);
};

// 发送通知给指定用户
exports.sendNotificationToSpecificUsers = (notification, userIds) => {
  try { const { Logger } = require('../middleware/errorHandler'); Logger.info(`发送通知给用户 ${userIds.join(", ")}:`, notification.title); } catch(_) {}


  // 模拟发送逻辑
  setTimeout(() => {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.info( } catch(_) {}

      `通知 "${notification.title}" 已发送给 ${userIds.length} 个用户`,
    );
  }, 1000);
};
