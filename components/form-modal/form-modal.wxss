/* components/form-modal/form-modal.wxss */
.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.close-text {
  font-size: 40rpx;
  color: #666666;
  line-height: 1;
}

.modal-body {
  flex: 1;
  padding: 0 40rpx;
  max-height: 60vh;
}

.form-container {
  padding: 20rpx 0;
}

.form-item {
  margin-bottom: 40rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-label::after {
  content: ' *';
  color: #ff4757;
  font-weight: bold;
  display: none;
}

.form-item.required .form-label::after {
  display: inline;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  box-sizing: border-box;
  transition: all 0.3s ease;
  position: relative;
}

.form-input:focus {
  border-color: #0066CC;
  background-color: #f8fbff;
  box-shadow: 0 0 0 4rpx rgba(0, 102, 204, 0.1);
  transform: translateY(-2rpx);
}

.picker-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background-color: #ffffff;
  font-size: 28rpx;
  color: #333333;
  transition: all 0.3s ease;
  position: relative;
}

.picker-value:active {
  border-color: #0066CC;
  background-color: #f8fbff;
}

.picker-value .placeholder {
  color: #999999;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

/* 特殊的日期时间选择器样式 */
.picker-date .picker-value,
.picker-time .picker-value {
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  border-color: #d1e7ff;
}

.picker-date .picker-value::before {
  content: '📅';
  margin-right: 12rpx;
  font-size: 24rpx;
}

.picker-time .picker-value::before {
  content: '🕐';
  margin-right: 12rpx;
  font-size: 24rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #0066CC;
}

/* 图片上传样式 */
.image-upload {
  width: 100%;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #e8e8e8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  position: relative;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
  opacity: 0.6;
}

.upload-placeholder .upload-text {
  font-size: 24rpx;
  color: #999999;
}

/* 显示字段样式 */
.form-display {
  padding: 0;
}

.display-content {
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 24rpx 32rpx;
  min-height: 80rpx;
  display: flex;
  align-items: center;
}

.display-text {
  color: #495057;
  font-size: 28rpx;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666666;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn::after {
  border: none;
}

.confirm-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.3);
}

.confirm-btn::after {
  border: none;
}

.confirm-btn.disabled {
  background: #cccccc;
  box-shadow: none;
}