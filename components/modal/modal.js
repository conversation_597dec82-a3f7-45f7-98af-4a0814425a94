// components/modal/modal.js
const { debounce } = require('../../utils/performance.js');

/**
 * 模态弹窗组件
 * 支持多种弹窗类型，自定义内容，动画效果
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 弹窗类型
    type: {
      type: String,
      value: 'default' // default, confirm, alert, custom
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 内容
    content: {
      type: String,
      value: ''
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      value: '确定'
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      value: '取消'
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      value: true
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      value: true
    },
    // 点击遮罩是否关闭
    maskClosable: {
      type: Boolean,
      value: true
    },
    // 弹窗宽度
    width: {
      type: String,
      value: '80%'
    },
    // 最大宽度
    maxWidth: {
      type: String,
      value: '500rpx'
    },
    // 动画持续时间(ms)
    duration: {
      type: Number,
      value: 300
    },
    // z-index
    zIndex: {
      type: Number,
      value: 1000
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画状态
    animationData: {},
    maskAnimationData: {},
    // 内部状态
    innerVisible: false,
    // 动画标识
    animating: false
  },

  /**
   * 数据监听器
   */
  observers: {
    'visible': function(visible) {
      this.toggleModal(visible);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      try { const logger = require('../../utils/logger.js'); logger.debug && logger.debug('[Modal] 组件初始化'); } catch(_) {}
      this.initAnimation();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化动画
     */
    initAnimation() {
      // 弹窗动画
      this.animation = wx.createAnimation({
        duration: this.properties.duration,
        timingFunction: 'ease-out'
      });

      // 遮罩动画
      this.maskAnimation = wx.createAnimation({
        duration: this.properties.duration,
        timingFunction: 'ease-out'
      });
    },

    /**
     * 切换弹窗显示状态
     */
    toggleModal: debounce(function(visible) {
      if (this.data.animating) {
        return;
      }

      this.setData({ animating: true });

      if (visible) {
        this.showModal();
      } else {
        this.hideModal();
      }
    }, 50),

    /**
     * 显示弹窗
     */
    showModal() {
      try { const logger = require('../../utils/logger.js'); logger.debug && logger.debug('[Modal] 显示弹窗'); } catch(_) {}
      
      // 先设置为显示状态
      this.setData({
        innerVisible: true
      });

      // 触发显示前事件
      this.triggerEvent('beforeshow');

      // 延迟执行动画，确保DOM已渲染
      setTimeout(() => {
        // 遮罩淡入动画
        this.maskAnimation.opacity(1).step();
        
        // 弹窗弹入动画
        this.animation
          .scale(1)
          .opacity(1)
          .translateY(0)
          .step();

        this.setData({
          maskAnimationData: this.maskAnimation.export(),
          animationData: this.animation.export(),
          animating: false
        });

        // 触发显示后事件
        setTimeout(() => {
          this.triggerEvent('show');
        }, this.properties.duration);
      }, 50);
    },

    /**
     * 隐藏弹窗
     */
    hideModal() {
      try { const logger = require('../../utils/logger.js'); logger.debug && logger.debug('[Modal] 隐藏弹窗'); } catch(_) {}
      
      // 触发隐藏前事件
      this.triggerEvent('beforehide');

      // 遮罩淡出动画
      this.maskAnimation.opacity(0).step();
      
      // 弹窗弹出动画
      this.animation
        .scale(0.9)
        .opacity(0)
        .translateY(-50)
        .step();

      this.setData({
        maskAnimationData: this.maskAnimation.export(),
        animationData: this.animation.export()
      });

      // 动画结束后隐藏组件
      setTimeout(() => {
        this.setData({
          innerVisible: false,
          animating: false
        });
        
        // 触发隐藏后事件
        this.triggerEvent('hide');
      }, this.properties.duration);
    },

    /**
     * 确认按钮点击
     */
    onConfirm() {
      this.triggerEvent('confirm');
      
      // 默认行为：关闭弹窗
      if (this.properties.type !== 'custom') {
        this.close();
      }
    },

    /**
     * 取消按钮点击
     */
    onCancel() {
      this.triggerEvent('cancel');
      this.close();
    },

    /**
     * 关闭按钮点击
     */
    onClose() {
      this.triggerEvent('close');
      this.close();
    },

    /**
     * 遮罩点击
     */
    onMaskTap() {
      if (this.properties.maskClosable) {
        this.triggerEvent('maskclick');
        this.close();
      }
    },

    /**
     * 阻止事件冒泡
     */
    onModalTap() {
      // 阻止点击弹窗内容时关闭
    },

    /**
     * 关闭弹窗
     */
    close() {
      if (this.data.animating) {
        return;
      }
      
      this.triggerEvent('update:visible', { value: false });
      this.toggleModal(false);
    },

    /**
     * 显示弹窗（外部调用）
     */
    show() {
      this.triggerEvent('update:visible', { value: true });
      this.toggleModal(true);
    },

    /**
     * 获取弹窗状态
     */
    isVisible() {
      return this.data.innerVisible;
    },

    /**
     * 设置弹窗内容
     */
    setContent(content) {
      this.setData({
        content: content
      });
    },

    /**
     * 设置弹窗标题
     */
    setTitle(title) {
      this.setData({
        title: title
      });
    }
  }
});