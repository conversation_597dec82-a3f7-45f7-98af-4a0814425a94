<!-- 统一导航栏组件 -->
<nav class="navbar-modern">
    <div class="navbar-content">
        <!-- 左侧：移动端菜单按钮和标题 -->
        <div class="navbar-left">
            <!-- 移动端菜单按钮 -->
            <button class="mobile-menu-btn d-md-none" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
            
            <!-- 当前页面标题（移动端显示） -->
            <div class="current-page-title d-md-none">
                <%= locals.title || '管理平台' %>
            </div>
        </div>
        
        <!-- 中间：搜索框 -->
        <div class="navbar-center">
            <div class="search-container">
                <div class="search-box">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索功能、数据或文档..." 
                           id="globalSearch" autocomplete="off">
                    <div class="search-shortcut">
                        <kbd>Ctrl</kbd> + <kbd>K</kbd>
                    </div>
                </div>
                
                <!-- 搜索结果下拉 -->
                <div class="search-results" id="searchResults" style="display: none;">
                    <div class="search-loading">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">搜索中...</span>
                        </div>
                        <span class="ms-2">搜索中...</span>
                    </div>
                    <div class="search-empty" style="display: none;">
                        <i class="bi bi-search"></i>
                        <p>没有找到相关内容</p>
                    </div>
                    <div class="search-items"></div>
                </div>
            </div>
        </div>
        
        <!-- 右侧：工具栏 -->
        <div class="navbar-right">
            <!-- 通知铃铛 -->
            <div class="nav-item">
                <button class="nav-btn" onclick="toggleNotifications()" id="notificationBtn">
                    <i class="bi bi-bell"></i>
                    <% if (locals.notifications && notifications.unreadCount > 0) { %>
                        <span class="notification-badge"><%= notifications.unreadCount %></span>
                    <% } %>
                </button>
                
                <!-- 通知下拉菜单 -->
                <div class="dropdown-menu notification-dropdown" id="notificationDropdown">
                    <div class="dropdown-header">
                        <h6 class="mb-0">通知消息</h6>
                        <% if (locals.notifications && notifications.unreadCount > 0) { %>
                            <button class="btn btn-sm btn-link" onclick="markAllAsRead()">全部已读</button>
                        <% } %>
                    </div>
                    
                    <div class="notification-list">
                        <% if (locals.notifications && notifications.items && notifications.items.length > 0) { %>
                            <% notifications.items.forEach(function(notification) { %>
                                <div class="notification-item <%= notification.read ? '' : 'unread' %>">
                                    <div class="notification-icon">
                                        <i class="<%= notification.icon || 'bi-info-circle' %>"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title"><%= notification.title %></div>
                                        <div class="notification-text"><%= notification.content %></div>
                                        <div class="notification-time"><%= notification.timeAgo %></div>
                                    </div>
                                    <% if (!notification.read) { %>
                                        <div class="notification-dot"></div>
                                    <% } %>
                                </div>
                            <% }); %>
                        <% } else { %>
                            <div class="notification-empty">
                                <i class="bi bi-bell-slash"></i>
                                <p>暂无通知消息</p>
                            </div>
                        <% } %>
                    </div>
                    
                    <div class="dropdown-footer">
                        <a href="/notifications" class="btn btn-sm btn-outline-primary w-100">查看全部</a>
                    </div>
                </div>
            </div>
            
            <!-- 快捷操作 -->
            <div class="nav-item">
                <button class="nav-btn" onclick="toggleQuickActions()" title="快捷操作">
                    <i class="bi bi-grid-3x3-gap"></i>
                </button>
                
                <!-- 快捷操作下拉菜单 -->
                <div class="dropdown-menu quick-actions-dropdown" id="quickActionsDropdown">
                    <div class="dropdown-header">
                        <h6 class="mb-0">快捷操作</h6>
                    </div>
                    
                    <div class="quick-actions-grid">
                        <% if (platformType === 'saas') { %>
                            <a href="/tenants/create" class="quick-action-item">
                                <i class="bi bi-building-add"></i>
                                <span>新增租户</span>
                            </a>
                            <a href="/announcements/create" class="quick-action-item">
                                <i class="bi bi-megaphone"></i>
                                <span>发布公告</span>
                            </a>
                            <a href="/monitoring" class="quick-action-item">
                                <i class="bi bi-activity"></i>
                                <span>系统监控</span>
                            </a>
                            <a href="/ai-config" class="quick-action-item">
                                <i class="bi bi-cpu"></i>
                                <span>AI配置</span>
                            </a>
                        <% } else { %>
                            <a href="/flocks/create" class="quick-action-item">
                                <i class="bi bi-plus-circle"></i>
                                <span>新增鹅群</span>
                            </a>
                            <a href="/health/records/create" class="quick-action-item">
                                <i class="bi bi-heart-pulse"></i>
                                <span>健康记录</span>
                            </a>
                            <a href="/production/records/create" class="quick-action-item">
                                <i class="bi bi-clipboard-data"></i>
                                <span>生产记录</span>
                            </a>
                            <a href="/reports" class="quick-action-item">
                                <i class="bi bi-bar-chart"></i>
                                <span>数据报表</span>
                            </a>
                        <% } %>
                    </div>
                </div>
            </div>
            
            <!-- 用户菜单 -->
            <div class="nav-item">
                <button class="nav-btn user-btn" onclick="toggleUserMenu()">
                    <% if (locals.currentUser && currentUser.avatar) { %>
                        <img src="<%= currentUser.avatar %>" alt="用户头像" class="user-avatar">
                    <% } else { %>
                        <i class="bi bi-person-circle"></i>
                    <% } %>
                    <span class="user-name d-none d-lg-inline">
                        <%= locals.currentUser ? currentUser.name : '管理员' %>
                    </span>
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                </button>
                
                <!-- 用户下拉菜单 -->
                <div class="dropdown-menu user-dropdown" id="userDropdown">
                    <div class="dropdown-header">
                        <div class="user-info">
                            <div class="user-name"><%= locals.currentUser ? currentUser.name : '管理员' %></div>
                            <div class="user-email"><%= locals.currentUser ? currentUser.email : '<EMAIL>' %></div>
                        </div>
                    </div>
                    
                    <div class="dropdown-divider"></div>
                    
                    <a href="/profile" class="dropdown-item">
                        <i class="bi bi-person"></i>
                        个人资料
                    </a>
                    <a href="/settings" class="dropdown-item">
                        <i class="bi bi-gear"></i>
                        账户设置
                    </a>
                    <a href="/help" class="dropdown-item">
                        <i class="bi bi-question-circle"></i>
                        帮助中心
                    </a>
                    
                    <div class="dropdown-divider"></div>
                    
                    <button class="dropdown-item" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i>
                        退出登录
                    </button>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
    /* 导航栏样式 */
    .navbar-modern {
        background: white;
        border-bottom: 1px solid rgba(0,0,0,0.08);
        box-shadow: 0 2px 4px rgba(0,0,0,0.04);
        position: sticky;
        top: 0;
        z-index: 1020;
        height: 64px;
    }
    
    .navbar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 0 24px;
        max-width: 100%;
    }
    
    .navbar-left,
    .navbar-right {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .navbar-center {
        flex: 1;
        max-width: 500px;
        margin: 0 20px;
    }
    
    /* 移动端菜单按钮 */
    .mobile-menu-btn {
        background: none;
        border: none;
        font-size: 20px;
        color: var(--dark-color);
        padding: 8px;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }
    
    .mobile-menu-btn:hover {
        background-color: #f8f9fa;
    }
    
    .current-page-title {
        font-weight: 600;
        color: var(--dark-color);
        margin-left: 12px;
    }
    
    /* 搜索框 */
    .search-container {
        position: relative;
        width: 100%;
    }
    
    .search-box {
        position: relative;
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 24px;
        padding: 8px 16px;
        transition: all 0.3s ease;
    }
    
    .search-box:focus-within {
        background: white;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    }
    
    .search-icon {
        color: #6c757d;
        margin-right: 8px;
        font-size: 14px;
    }
    
    .search-input {
        flex: 1;
        border: none;
        background: none;
        outline: none;
        font-size: 14px;
        color: var(--dark-color);
    }
    
    .search-input::placeholder {
        color: #6c757d;
    }
    
    .search-shortcut {
        display: flex;
        align-items: center;
        gap: 2px;
        margin-left: 8px;
        color: #6c757d;
        font-size: 11px;
    }
    
    .search-shortcut kbd {
        background: #e9ecef;
        border: 1px solid #dee2e6;
        border-radius: 3px;
        padding: 2px 4px;
        font-size: 10px;
        font-family: inherit;
    }
    
    /* 搜索结果 */
    .search-results {
        position: absolute;
        top: calc(100% + 8px);
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.15);
        max-height: 400px;
        overflow-y: auto;
        z-index: 1030;
    }
    
    .search-loading,
    .search-empty {
        padding: 20px;
        text-align: center;
        color: #6c757d;
    }
    
    .search-items {
        padding: 8px 0;
    }
    
    .search-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        text-decoration: none;
        color: var(--dark-color);
        transition: background-color 0.2s ease;
    }
    
    .search-item:hover {
        background-color: #f8f9fa;
        color: var(--dark-color);
    }
    
    .search-item-icon {
        width: 24px;
        height: 24px;
        background: #f8f9fa;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 12px;
        color: var(--primary-color);
    }
    
    .search-item-content {
        flex: 1;
    }
    
    .search-item-title {
        font-weight: 500;
        margin-bottom: 2px;
    }
    
    .search-item-description {
        font-size: 12px;
        color: #6c757d;
    }
    
    /* 导航按钮 */
    .nav-item {
        position: relative;
    }
    
    .nav-btn {
        background: none;
        border: none;
        color: var(--dark-color);
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        position: relative;
    }
    
    .nav-btn:hover {
        background-color: #f8f9fa;
        color: var(--primary-color);
    }
    
    .nav-btn.active {
        background-color: rgba(40, 167, 69, 0.1);
        color: var(--primary-color);
    }
    
    .user-btn .user-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .user-name {
        font-weight: 500;
        font-size: 14px;
    }
    
    .dropdown-arrow {
        font-size: 12px;
        transition: transform 0.2s ease;
    }
    
    .nav-btn.active .dropdown-arrow {
        transform: rotate(180deg);
    }
    
    /* 通知徽章 */
    .notification-badge {
        position: absolute;
        top: 4px;
        right: 4px;
        background: var(--danger-color);
        color: white;
        font-size: 10px;
        font-weight: 600;
        padding: 2px 5px;
        border-radius: 8px;
        min-width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }
    
    /* 下拉菜单 */
    .dropdown-menu {
        position: absolute;
        top: calc(100% + 8px);
        right: 0;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.15);
        min-width: 280px;
        max-height: 400px;
        overflow-y: auto;
        z-index: 1030;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }
    
    .dropdown-menu.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .dropdown-header {
        padding: 16px 20px 12px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .dropdown-header h6 {
        margin: 0;
        font-weight: 600;
        color: var(--dark-color);
    }
    
    .dropdown-divider {
        margin: 8px 0;
        border-top: 1px solid #e9ecef;
    }
    
    .dropdown-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        text-decoration: none;
        color: var(--dark-color);
        transition: background-color 0.2s ease;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        font-size: 14px;
    }
    
    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: var(--dark-color);
    }
    
    .dropdown-item i {
        width: 16px;
        margin-right: 12px;
        color: #6c757d;
    }
    
    .dropdown-footer {
        padding: 12px 20px 16px;
        border-top: 1px solid #e9ecef;
    }
    
    /* 通知下拉菜单 */
    .notification-dropdown {
        min-width: 350px;
    }
    
    .notification-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .notification-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 20px;
        border-bottom: 1px solid #f8f9fa;
        position: relative;
        transition: background-color 0.2s ease;
    }
    
    .notification-item:last-child {
        border-bottom: none;
    }
    
    .notification-item:hover {
        background-color: #f8f9fa;
    }
    
    .notification-item.unread {
        background-color: rgba(40, 167, 69, 0.05);
    }
    
    .notification-icon {
        width: 32px;
        height: 32px;
        background: #f8f9fa;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
    }
    
    .notification-content {
        flex: 1;
        min-width: 0;
    }
    
    .notification-title {
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 4px;
        color: var(--dark-color);
    }
    
    .notification-text {
        font-size: 13px;
        color: #6c757d;
        line-height: 1.4;
        margin-bottom: 4px;
    }
    
    .notification-time {
        font-size: 11px;
        color: #6c757d;
    }
    
    .notification-dot {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 8px;
        height: 8px;
        background: var(--primary-color);
        border-radius: 50%;
    }
    
    .notification-empty {
        padding: 40px 20px;
        text-align: center;
        color: #6c757d;
    }
    
    .notification-empty i {
        font-size: 32px;
        margin-bottom: 12px;
        display: block;
    }
    
    /* 快捷操作网格 */
    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 12px 20px 16px;
    }
    
    .quick-action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px 12px;
        text-decoration: none;
        color: var(--dark-color);
        border-radius: 8px;
        transition: all 0.2s ease;
        text-align: center;
    }
    
    .quick-action-item:hover {
        background-color: #f8f9fa;
        color: var(--primary-color);
        transform: translateY(-2px);
    }
    
    .quick-action-item i {
        font-size: 20px;
        margin-bottom: 8px;
        color: var(--primary-color);
    }
    
    .quick-action-item span {
        font-size: 12px;
        font-weight: 500;
    }
    
    /* 用户下拉菜单 */
    .user-dropdown .user-info {
        text-align: left;
    }
    
    .user-dropdown .user-name {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 2px;
    }
    
    .user-dropdown .user-email {
        font-size: 12px;
        color: #6c757d;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .navbar-content {
            padding: 0 16px;
        }
        
        .navbar-center {
            margin: 0 12px;
            max-width: none;
        }
        
        .search-shortcut {
            display: none;
        }
        
        .dropdown-menu {
            min-width: 260px;
        }
        
        .notification-dropdown {
            min-width: 300px;
        }
    }
    
    @media (max-width: 576px) {
        .navbar-center {
            display: none;
        }
        
        .navbar-right {
            gap: 8px;
        }
        
        .nav-btn {
            padding: 6px 8px;
        }
        
        .dropdown-menu {
            left: auto;
            right: 0;
            min-width: 240px;
        }
    }
</style>

<script>
    // 全局搜索功能
    let searchTimeout;
    const searchInput = document.getElementById('globalSearch');
    const searchResults = document.getElementById('searchResults');
    
    if (searchInput) {
        // 搜索输入事件
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length < 2) {
                searchResults.style.display = 'none';
                return;
            }
            
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        });
        
        // 点击外部关闭搜索结果
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-container')) {
                searchResults.style.display = 'none';
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                searchInput.focus();
            }
            
            if (e.key === 'Escape') {
                searchInput.blur();
                searchResults.style.display = 'none';
            }
        });
    }
    
    // 执行搜索
    function performSearch(query) {
        searchResults.style.display = 'block';
        searchResults.querySelector('.search-loading').style.display = 'block';
        searchResults.querySelector('.search-empty').style.display = 'none';
        searchResults.querySelector('.search-items').innerHTML = '';
        
        // 发送搜索请求
        fetch(`/api/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                searchResults.querySelector('.search-loading').style.display = 'none';
                
                if (data.results && data.results.length > 0) {
                    displaySearchResults(data.results);
                } else {
                    searchResults.querySelector('.search-empty').style.display = 'block';
                }
            })
            .catch(error => {
                console.error('搜索失败:', error);
                searchResults.querySelector('.search-loading').style.display = 'none';
                searchResults.querySelector('.search-empty').style.display = 'block';
            });
    }
    
    // 显示搜索结果
    function displaySearchResults(results) {
        const itemsContainer = searchResults.querySelector('.search-items');
        itemsContainer.innerHTML = '';
        
        results.forEach(result => {
            const item = document.createElement('a');
            item.className = 'search-item';
            item.href = result.url;
            item.innerHTML = `
                <div class="search-item-icon">
                    <i class="${result.icon || 'bi-file-text'}"></i>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">${result.title}</div>
                    <div class="search-item-description">${result.description}</div>
                </div>
            `;
            itemsContainer.appendChild(item);
        });
    }
    
    // 切换下拉菜单
    function toggleDropdown(buttonId, dropdownId) {
        const button = document.getElementById(buttonId);
        const dropdown = document.getElementById(dropdownId);
        
        // 关闭其他下拉菜单
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            if (menu.id !== dropdownId) {
                menu.classList.remove('show');
                const relatedBtn = document.querySelector(`[onclick*="${menu.id}"]`);
                if (relatedBtn) relatedBtn.classList.remove('active');
            }
        });
        
        // 切换当前下拉菜单
        const isOpen = dropdown.classList.contains('show');
        dropdown.classList.toggle('show');
        button.classList.toggle('active');
        
        return !isOpen;
    }
    
    // 通知功能
    function toggleNotifications() {
        toggleDropdown('notificationBtn', 'notificationDropdown');
    }
    
    function markAllAsRead() {
        fetch('/api/notifications/mark-all-read', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => console.error('标记已读失败:', error));
    }
    
    // 快捷操作
    function toggleQuickActions() {
        toggleDropdown('quickActionsBtn', 'quickActionsDropdown');
    }
    
    // 用户菜单
    function toggleUserMenu() {
        toggleDropdown('userMenuBtn', 'userDropdown');
    }
    
    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.nav-item')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
            document.querySelectorAll('.nav-btn.active').forEach(btn => {
                btn.classList.remove('active');
            });
        }
    });
    
    // 退出登录
    function logout() {
        if (confirm('确定要退出登录吗？')) {
            window.location.href = '/logout';
        }
    }
</script>