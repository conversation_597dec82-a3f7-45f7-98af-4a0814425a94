const express = require("express");
const router = express.Router();
const platformController = require("../controllers/platform.controller");

// 平台仪表板路由
router.get("/dashboard", platformController.getDashboard);

// 租户管理路由
router.get("/tenants", platformController.getTenants);
router.get("/tenants/:id", platformController.getTenantDetail);
router.post("/tenants", platformController.createTenant);
router.put("/tenants/:id", platformController.updateTenant);
router.delete("/tenants/:id", platformController.deleteTenant);

// 租户批量操作
router.post("/tenants/batch/activate", async (req, res) => {
  try {
    const { tenantIds } = req.body;
    // 模拟批量激活租户
    res.json({
      success: true,
      message: `成功激活 ${tenantIds.length} 个租户`,
      data: { activatedCount: tenantIds.length },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "批量激活失败",
      error: error.message,
    });
  }
});

router.post("/tenants/batch/suspend", async (req, res) => {
  try {
    const { tenantIds } = req.body;
    // 模拟批量暂停租户
    res.json({
      success: true,
      message: `成功暂停 ${tenantIds.length} 个租户`,
      data: { suspendedCount: tenantIds.length },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "批量暂停失败",
      error: error.message,
    });
  }
});

// 平台用户管理
router.get("/users", async (req, res) => {
  try {
    // 模拟平台管理员用户数据 - 实际应该从数据库获取
    const users = [
      {
        id: 1,
        username: "admin",
        email: "<EMAIL>",
        role: "super_admin",
        status: "active",
        lastLoginAt: "2024-01-20T10:30:00Z",
        createdAt: "2024-01-01T00:00:00Z",
      },
      {
        id: 2,
        username: "support",
        email: "<EMAIL>",
        role: "support",
        status: "active",
        lastLoginAt: "2024-01-19T15:45:00Z",
        createdAt: "2024-01-05T08:30:00Z",
      },
    ];

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("users/index", {
        title: "平台用户管理",
        users: users,
      });
    } else {
      // 如果是API请求，返回JSON
      res.json({
        success: true,
        message: "获取平台用户列表成功",
        data: users,
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取平台用户列表失败",
      error: error.message,
    });
  }
});

// 获取平台用户详情
router.get("/users/:id", async (req, res) => {
  try {
    const { id } = req.params;
    // 模拟数据 - 实际应该从数据库获取
    const user = {
      id: parseInt(id),
      username: "admin",
      email: "<EMAIL>",
      role: "super_admin",
      status: "active",
      permissions: ["tenant_management", "user_management", "system_config"],
      lastLoginAt: "2024-01-20T10:30:00Z",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-15T14:20:00Z",
    };

    res.json({
      success: true,
      message: "获取平台用户详情成功",
      data: user,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取平台用户详情失败",
      error: error.message,
    });
  }
});

// 创建平台用户
router.post("/users", async (req, res) => {
  try {
    const { username, email, role, password } = req.body;
    // 模拟创建用户 - 实际应该保存到数据库
    const newUser = {
      id: Math.floor(Math.random() * 1000) + 3,
      username,
      email,
      role,
      status: "active",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "创建平台用户成功",
      data: newUser,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "创建平台用户失败",
      error: error.message,
    });
  }
});

// 更新平台用户
router.put("/users/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, role, status } = req.body;
    // 模拟更新用户 - 实际应该更新数据库记录
    const updatedUser = {
      id: parseInt(id),
      username,
      email,
      role,
      status,
      updatedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      message: "更新平台用户成功",
      data: updatedUser,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "更新平台用户失败",
      error: error.message,
    });
  }
});

// 删除平台用户
router.delete("/users/:id", async (req, res) => {
  try {
    const { id } = req.params;
    // 模拟删除用户 - 实际应该从数据库删除

    res.json({
      success: true,
      message: "删除平台用户成功",
      data: { id: parseInt(id) },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "删除平台用户失败",
      error: error.message,
    });
  }
});

// 订阅计划管理
router.get("/plans", async (req, res) => {
  try {
    const plans = [
      {
        id: 1,
        planCode: "trial",
        planName: "试用版",
        description: "免费试用30天，体验基础功能",
        monthlyPrice: 0,
        yearlyPrice: 0,
        maxUsers: 3,
        maxFlocks: 5,
        storageLimit: 1073741824,
        apiCallsLimit: 1000,
        features: ["basic_management", "data_export"],
        isActive: true,
      },
      {
        id: 2,
        planCode: "basic",
        planName: "基础版",
        description: "适合小型养殖场",
        monthlyPrice: 99,
        yearlyPrice: 999,
        maxUsers: 10,
        maxFlocks: 20,
        storageLimit: 5368709120,
        apiCallsLimit: 10000,
        features: ["basic_management", "data_export", "basic_analytics"],
        isActive: true,
      },
      {
        id: 3,
        planCode: "standard",
        planName: "标准版",
        description: "适合中型养殖场",
        monthlyPrice: 299,
        yearlyPrice: 2999,
        maxUsers: 50,
        maxFlocks: 100,
        storageLimit: 21474836480,
        apiCallsLimit: 50000,
        features: [
          "basic_management",
          "data_export",
          "advanced_analytics",
          "api_access",
        ],
        isActive: true,
      },
      {
        id: 4,
        planCode: "premium",
        planName: "高级版",
        description: "适合大型养殖场",
        monthlyPrice: 599,
        yearlyPrice: 5999,
        maxUsers: 200,
        maxFlocks: 500,
        storageLimit: 107374182400,
        apiCallsLimit: 200000,
        features: [
          "basic_management",
          "data_export",
          "advanced_analytics",
          "api_access",
          "custom_reports",
          "priority_support",
        ],
        isActive: true,
      },
      {
        id: 5,
        planCode: "enterprise",
        planName: "企业版",
        description: "适合大型企业，提供定制化服务",
        monthlyPrice: 1299,
        yearlyPrice: 12999,
        maxUsers: -1, // 无限制
        maxFlocks: -1, // 无限制
        storageLimit: -1, // 无限制
        apiCallsLimit: -1, // 无限制
        features: [
          "all_features",
          "custom_development",
          "24_7_support",
          "dedicated_support",
        ],
        isActive: true,
      },
    ];

    // 如果是HTML请求，渲染页面
    if (req.accepts("html")) {
      return res.render("plans/index", {
        title: "订阅计划管理",
        plans: plans,
      });
    } else {
      // 如果是API请求，返回JSON
      res.json({
        success: true,
        message: "获取订阅计划列表成功",
        data: plans,
      });
    }
  } catch (error) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error("获取订阅计划失败:", error); } catch(_) {}

    if (req.accepts("html")) {
      return res.render("error", {
        title: "错误",
        error: "获取订阅计划失败",
        message: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "获取订阅计划失败",
        error: error.message,
      });
    }
  }
});

// 知识库管理
router.get("/knowledge", (req, res) => {
  const articles = [
    { id: 1, name: "养鹅技术指南", type: "article" },
    { id: 2, name: "疾病防治手册", type: "manual" },
  ];

  if (req.accepts("html")) {
    res.render("knowledge/index", {
      title: "知识库",
      articles: articles,
    });
  } else {
    res.json({ success: true, data: articles });
  }
});

// 系统监控
router.get("/monitoring", (req, res) => {
  if (req.accepts("html")) {
    res.render("monitoring/index", {
      title: "性能监控",
      data: [],
    });
  } else {
    res.json({ success: true, message: "性能监控功能", data: [] });
  }
});

module.exports = router;
